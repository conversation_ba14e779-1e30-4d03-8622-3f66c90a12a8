/*
 UMCommonLog.strings
 testUMCommonLog
 
 Created by 张军华 on 2017/12/11.
 Copyright © 2017年 张军华. All rights reserved.
 */



////////////////////////////////////////////////
//init begin
//init  20000 - 20250
common_init_error1 = "[CIE10001]用户传入的appKey不合法，请到官网申请appkey，以免影响自己App的统计数据。网址如下：https://developer.umeng.com/docs/66632/detail/67191?um_channel=sdk";

common_init_warn1 = "当前传入的appkey和用户以前设置过的appkey不一样。";
common_init_warn2 = "当前传入的channel渠道为空。";
common_init_warn3 = "检测用户正在使用UI无埋点功能，检测到UMABTest.framework和UMAutoEventBinding.framework同时并存，在需要release发布需要删除UMABTest.framework。";

common_init_info1 = "正在使用国内Common组件化SDK版本。";
common_init_info2 = "正在使用国际化Common组件化SDK版本。";

common_init_debug1 = "UMCommon版本号：%@。";

common_init_verbose1 = "";

//init end
////////////////////////////////////////////////

////////////////////////////////////////////////
//integration_test begin
common_integrationtest_error1 = "experiment params invalid。";
common_integrationtest_error2 = "unknow experiment group key。";
common_integrationtest_error3 = "unknow experiment test key。";
common_integrationtest_error4 = "experiment params invalid。";

//client_test end
////////////////////////////////////////////////


////////////////////////////////////////////////
//deviceToken begin
common_deviceToken_error1 = "error,tokenStringWithData, token inValid! [%ld]。";

//deviceToken end
////////////////////////////////////////////////


////////////////////////////////////////////////
//Envelope begin

common_envelope_error1 = "信封json创建失败";
common_envelope_error2 = "信封raw size:(%d)超过最大限制,创建失败";
common_envelope_error3 = "信封压缩数据失败";
common_envelope_error4 = "信封打包失败";
common_envelope_error5 = "信封创建失败";
common_envelope_error6 = "信封size:(%d)超过最大限制，创建失败";
common_envelope_error7 = "发送信封(%@)失败";
common_envelope_error8 = "网络请求失败(Response Applog) {\"fail\": \"error\"}";
common_envelope_error9 = "网络请求失败(Response Applog) {\"fail\": \"statusCode\":%d}";
common_envelope_error10 = "网络请求失败(Error   Applog) %@";

common_envelope_warn1 = "信封数量超过最大限制%d,并且删除此文件名为:%@";

common_envelope_info1 = "准备发送信封";
common_envelope_info2 = "信封名字的前缀:(%@)";

common_envelope_debug1 = "当前正在发送网络请求，还不能打包信封(network running=YES)。";
common_envelope_debug2 = "当前网络状态不可用。";
common_envelope_debug3 = "当前本地有信封存在,还不能打包新的信封。";
common_envelope_debug4 = "生成信封(%@)成功";
common_envelope_debug5 = "准备发送信封(%@)...";
common_envelope_debug6 = "发送信封(%@)成功";
common_envelope_debug7 = "网络请求成功(Response Applog) {\"success\": \"ok\"}";
common_envelope_debug8 = "将要打包的有状态数据:%@";
common_envelope_debug9 = "信封SerialNum:%d";

common_envelope_verbose1 = "";

//Envelope end
////////////////////////////////////////////////

////////////////////////////////////////////////
//SLEnvelope begin

common_slenvelope_error1 = "无状态信封json创建失败";
common_slenvelope_error2 = "无状态信封raw size:(%d)超过最大限制,创建失败";
common_slenvelope_error3 = "无状态信封压缩数据失败";
common_slenvelope_error4 = "无状态信封打包失败";
common_slenvelope_error5 = "无状态信封创建失败";
common_slenvelope_error6 = "无状态信封size:(%d)超过最大限制，创建失败";
common_slenvelope_error7 = "发送无状态信封(%@)失败";
common_slenvelope_error8 = "无状态信封(%@)不存在";
common_slenvelope_error9 = "网络请求失败(SLResponse Applog) {\"fail\": \"statusCode\":%d}";

common_slenvelope_warn1 = "";

common_slenvelope_info1 = "";


common_slenvelope_debug1 = "生成无状态信封(%@)成功";
common_slenvelope_debug2 = "准备发送无状态信封(%@)...";
common_slenvelope_debug3 = "发送无状态信封(%@)成功";
common_slenvelope_debug4 = "网络请求成功(SLResponse Applog) {\"success\": \"ok\"}";
common_slenvelope_debug5 = "将要打包的无状态数据:%@";

common_slenvelope_verbose1 = "";


//SLEnvelope end
////////////////////////////////////////////////

analytics_init_error1 = "[AIE10012]Latitude或者longitude设置错误 https://developer.umeng.com/docs/66632/detail/66999?um_channel=sdk";
analytics_init_error2 = "[AIE10013]puid 为空 https://developer.umeng.com/docs/66632/detail/67000?um_channel=sdk";
analytics_init_error3 = "[AIE10013]puid 大于64字节 https://developer.umeng.com/docs/66632/detail/67000?um_channel=sdk";
analytics_init_error4 = "[AIE10013]provider 大于32字节 https://developer.umeng.com/docs/66632/detail/67000?um_channel=sdk";
analytics_init_error5 = "[AIE10005]请设置Dplus 场景 https://developer.umeng.com/docs/66632/detail/66990?um_channel=sdk";
analytics_init_error6 = "数据库连接失败";
analytics_init_error7 = "数据库已经打开";
analytics_init_error8 = "修改表失败,TABLE %@ with columnName(%@)";
analytics_init_error9 = "数据库运行失败,sql: %@, error: %s,errorCode:%d";
analytics_init_error10 = "%@ 表创建失败";
analytics_init_error11 = "%@ 表删除失败";
analytics_init_error12 = "sql执行失败 %s";
analytics_init_error13 = "%@ 表写入失败";
analytics_init_error14 = "%@ 表修改失败";
analytics_init_error15 = "[AIE10011]DeepLink url 不能大于 %d字节 https://developer.umeng.com/docs/66632/detail/66998?um_channel=sdk";
analytics_init_error16 = "[AIE10011]DeepLink eventId %@ 不能大于 %d字节 https://developer.umeng.com/docs/66632/detail/66998?um_channel=sdk";
analytics_init_error17 = "[AIE10006]%@是SDK保留字段，不能作为eventId使用 https://developer.umeng.com/docs/66632/detail/66991?um_channel=sdk";
analytics_init_error18 = "[AIE1006]attributes中value 不能为 NSNull https://developer.umeng.com/docs/66632/detail/67191?um_channel=sdk";
analytics_init_error19 = "[AIE10001]appkey 不能为空 https://developer.umeng.com/docs/66632/detail/66982?um_channel=sdk";
analytics_init_error20 = "[AIE10005]MobClickGameAnalytics 是游戏API，请先设置游戏场景 https://developer.umeng.com/docs/66632/detail/66990?um_channel=sdk";
analytics_init_error21 = "[AIE10014]MobClickGameAnalytics orderId 不能大于 1024字节 https://developer.umeng.com/docs/66632/detail/67001?um_channel=sdk";
analytics_init_error22 = "[AIE10014]MobClickGameAnalytics currencyType 不能大于 3字节 https://developer.umeng.com/docs/66632/detail/67001?um_channel=sdk";
analytics_init_error23 = "[AIE10005]DplusMobClick 是Dplus API，请先设置Dplus场景 https://developer.umeng.com/docs/66632/detail/66990?um_channel=sdk";

analytics_init_error24 = "[AIE1008]Dplus key：%@ 是预制字段，不能使用 https://developer.umeng.com/docs/66632/detail/67191?um_channel=sdk";
analytics_init_error25 = "[AIE10008]Dplus property value只能使用NSString,NSNumber,NSArray类型 NSArray只能是(NSString,NSNumber)类型且不能为空 https://developer.umeng.com/docs/66993/detail/id?um_channel=sdk";
analytics_init_error26 = "[AIE10008]Dplus property的key只能是NSString类型且不能为空 https://developer.umeng.com/docs/66632/detail/66993?um_channel=sdk";
analytics_init_error27 = "[AIE10008]Dplus property的value数组只能是NSString类型或NSNumber类型且不能为空 https://developer.umeng.com/docs/66632/detail/66993?um_channel=sdk";

analytics_init_error30 = "[AIE10010]Dplus eventList必须是NSArray类型 https://developer.umeng.com/docs/66632/detail/66996?um_channel=sdk";
analytics_init_error31 = "[AIE10010]Dplus eventList已存入5个，无法再添加 https://developer.umeng.com/docs/66632/detail/66996?um_channel=sdk";
analytics_init_error32 = "[AIE10008]Dplus property只能是NSDictionary类型 https://developer.umeng.com/docs/66632/detail/66993?um_channel=sdk";
analytics_init_error33 = "[AIE10008]Dplus propertyName只能是NSString类型并且不能为空 https://developer.umeng.com/docs/66632/detail/66993?um_channel=sdk";

analytics_init_error34 = "[AIE10006]关键字id, ts, du, dplus_st, %@是SDK保留字段，不能作为key使用。https://developer.umeng.com/docs/66632/detail/66991?um_channel=sdk";
analytics_init_error35 = "[AIE10006]事件的key和value 必须是string类型,key不能大于%ld字节，value不能大于%ld字节 https://developer.umeng.com/docs/66991/detail/id?um_channel=sdk";
analytics_init_error36 = "[AIE10008]Dplus eventName为空 https://developer.umeng.com/docs/66632/detail/66993?um_channel=sdk";
analytics_init_error37 = "[AIE10006]关键字id, ts, du, dplus_st是友盟SDK保留字段，不能作为key使用。https://developer.umeng.com/docs/66632/detail/66991?um_channel=sdk";
analytics_init_error38 = "make envelope failed code:%d";
analytics_init_error39 = "获取内容失败";
analytics_init_error40 = "[AIE10006]eventId %@ 不能大于 %d字节 https://developer.umeng.com/docs/66632/detail/66991?um_channel=sdk";
analytics_init_error41 = "[AIE10011]DeepLink 请检查参数是否正确 https://developer.umeng.com/docs/66632/detail/66998?um_channel=sdk";
analytics_init_error42 = "[AIE10010]Dplus eventList参数错误 https://developer.umeng.com/docs/66632/detail/66996?um_channel=sdk";

analytics_init_error43 = "[AIE10009]Dplus 请检查预置事件参数 https://developer.umeng.com/docs/66632/detail/66994?um_channel=sdk";
analytics_init_error44 = "[AIE10009]Dplus 预置事件的key和value 必须是string类型,key不能大于%d字节，value不能大于%d字节 https://developer.umeng.com/docs/66632/detail/66994?um_channel=sdk";
analytics_init_error45 = "[AIE10009]Dplus 预置事件property的value只能是NSString或者NSNumber类型且不能为空 https://developer.umeng.com/docs/66632/detail/66994?um_channel=sdk";
analytics_init_error46 = "[AIE10009]Dplus 预支事件property的key只能是NSString类型且不能为空 https://developer.umeng.com/docs/66632/detail/66994?um_channel=sdk";
analytics_init_error47 = "[AIE10009]Dplus 预置事件property只能是NSDictionary类型 https://developer.umeng.com/docs/66632/detail/66994?um_channel=sdk";
analytics_init_error48 = "[AIE10009]Dplus 预置事件property为空 https://developer.umeng.com/docs/66632/detail/66994?um_channel=sdk";
analytics_init_error49 = "[AIE10009]Dplus 请检查预置事件property中的key和value是否正确 https://developer.umeng.com/docs/66632/detail/66994?um_channel=sdk";
analytics_init_error50 = "[AIE10009]Dplus 预置事件propertyName只能是NSString类型并且不能为空 https://developer.umeng.com/docs/66632/detail/66994?um_channel=sdk";

analytics_init_error51 = "[AIE10007]请检查参数是否正确 https://developer.umeng.com/docs/66632/detail/66992?um_channel=sdk";
analytics_init_error52 = "[AIE10007]pageName不能为空 https://developer.umeng.com/docs/66632/detail/66992?um_channel=sdk";
analytics_init_error53 = "[AIE10008]Dplus property为空 https://developer.umeng.com/docs/66632/detail/66993?um_channel=sdk";

analytics_init_error54 = "[AIE10006]请检查参数是否正确 https://developer.umeng.com/docs/66632/detail/66991?um_channel=sdk";

analytics_init_error55 = "[AIE10014]MobClickGameAnalytics level不能为空 https://developer.umeng.com/docs/66632/detail/67000?um_channel=sdk";
analytics_init_error56 = "[AIE10014]MobClickGameAnalytics 请检查currencyAmount和virtualAmount是否正确 https://developer.umeng.com/docs/66632/detail/67001?um_channel=sdk";
analytics_init_error57 = "[AIE10014]请检查参数是否正确 https://developer.umeng.com/docs/66632/detail/67001?um_channel=sdk";

analytics_init_error58 = "[AIE1008]超级属性设置失败 key：%@ 只能是%@、%@、%@、%@、%@字段，其他不能使用 https://developer.umeng.com/docs/66632/detail/67191?um_channel=sdk";
analytics_init_error59 = "[AIE10006]自定义属性中事件的key %@ 必须是NSString类型,key不能大于%d字节 https://developer.umeng.com/docs/66991/detail/id?um_channel=sdk";
analytics_init_error60 = "[AIE10006]自定义属性中事件的key value 不能为空 https://developer.umeng.com/docs/66991/detail/id?um_channel=sdk";
analytics_init_error61 = "[AIE10006]自定义属性中事件的value %@ 只能是NSString,NSNumber,NSArray类型,value不能大于%d字节 https://developer.umeng.com/docs/66991/detail/id?um_channel=sdk";

analytics_init_error62 = "[AIE10006]自定义属性中事件的value 中的数组只能是NSString或NSNumber类型 https://developer.umeng.com/docs/66991/detail/id?um_channel=sdk";
analytics_init_error63 = "[AIE10006]自定义属性中事件value中array的值不能大于%d字节 https://developer.umeng.com/docs/66991/detail/id?um_channel=sdk";
analytics_init_error64 = "[AIE10006]自定义属性key：%@ 的value中的数组不能大于 %d 条 https://developer.umeng.com/docs/66991/detail/id?um_channel=sdk";
analytics_init_error65 = "[AIE10006]自定义属性不能大于 %d 条 https://developer.umeng.com/docs/66991/detail/id?um_channel=sdk";

analytics_init_error66 = "[AIE1008]设置失败 key：%@ 不能大于%d字节 https://developer.umeng.com/docs/66632/detail/67191?um_channel=sdk";

analytics_init_error67 = "[AIE1008]设置失败 value：%@ 不能大于%d字节 https://developer.umeng.com/docs/66632/detail/67191?um_channel=sdk";

analytics_init_error68 = "预置事件条数大于 %d ";
analytics_init_error69 = "propertyName字节大于 %d ";

analytics_init_error70 = "eventId:%@ 时长必须大于0小于 %d 毫秒 ";
analytics_init_error71 = "eventId不能为空 ";
analytics_init_error72 = "label %@ 不能大于 %d字节 ";
analytics_init_error73 = "label 必须是NSString类型 ";
analytics_init_error74 = "link 必须是NSUrl类型 ";
analytics_init_error75 = "link 不能为空 ";
analytics_init_error76 = "property的key只能是NSString类型且不能为空";

analytics_init_error77 = "ModuleTag 必须是NSString类型";
analytics_init_error78 = "ModuleTag key长度不能大于64，value长度不能大于128";
analytics_init_error79 = "ModuleTag 不能超过30个";


analytics_init_warn1 = "provider 为空";
analytics_init_warn3 = "错误信息表内容不是jsonString";
analytics_init_warn5 = "请检查property中的key和value值";
analytics_init_warn6 = "app已经启动";
analytics_init_warn7 = "attributes中value 不能为空";
analytics_init_warn8 = "setUserLevel 方法已下线，请使用setUserLevelId方法";
analytics_init_warn9 = "setUserID 方法已下线，请使用其他相关User的方法";

analytics_init_warn10 = "[AIE10010]Dplus eventList为空 https://developer.umeng.com/docs/66632/detail/66996?um_channel=sdk";


analytics_init_warn18 = "event label (%@) 不能大于%d字节";
analytics_init_warn19 = "event事件在8小时内最多发送%d";
analytics_init_warn20 = "track事件在8小时内最多发送%d";
analytics_init_warn21 = "sessionId为空";
analytics_init_warn22 = "不确定event 类型";

analytics_init_warn23 = "您已关闭使用统计功能";

analytics_init_warn24 = "key:%@ 超出限制字节，已被截取到%ld字节";
analytics_init_warn25 = "value:%@ 超出限制字节，已被截取到%ld字节";
analytics_init_warn26 = "预置事件条数大于 %ld ，已被截取只取前 %ld 条";

analytics_init_warn27 = "此方法不生效，如果使用请将setAutoPageEnabled方法设置为NO";

analytics_init_warn28 = "setAutoPageEnabled已调用过，此方法只能调用一次，建议在初始化之前使用";

analytics_init_warn29 = "建议不要在初始化之前使用事件，在初始化之前调用的事件有可能会丢失";







analytics_init_info1 = "超级属性注册成功";
analytics_init_info2 = "缓存数据存储完成";
analytics_init_info3 = "session 开始：%@";
analytics_init_info4 = "session 结束：%@";

analytics_init_info9 = "dladdr(%p, ...) failed";
analytics_init_info10 = "Invalid Mach-O header magic value: %x";
analytics_init_info11 = "LC_SEGMENT 0x%08x";
analytics_init_info12 = "数据过大，进行拆包";
analytics_init_info13 = "数据过大，进行Dplus拆包";
analytics_init_info14 = "数据过大，进行analytics拆包";

analytics_init_info15 = "已将是否自动获取页面设置为%@";










analytics_init_debug1 = "UMAnalytics sdk版本号：%@,app 版本号：%@";
analytics_init_debug2 = "UMAnalytics发送配置：sdk_version:%@, device_id:%@, model:%@, os_version:%@, app_version:%@";
analytics_init_debug3 = "UMAnalytics统计发送内容：active_user:%@, activate_msg:%@, sessions:%@, ekv:%@";
analytics_init_debug4 = "UMAnalytics游戏统计发送内容：gkv:%@";
analytics_init_debug5 = "UMAnalytics Dplus发送内容：Dplus:%@";
analytics_init_debug6 = "UMAnalytics error:%@";
analytics_init_debug7 = "UMAnalytics event:%@";
analytics_init_debug8 = "UMAnalytics session:%@";
analytics_init_debug9 = "UMAnalytics ekv:%@";
analytics_init_debug10 = "UMAnalytics gkv:%@";
analytics_init_debug11 = "UMAnalytics app被杀死";
analytics_init_debug12 = "UMAnalytics pageDuration:%@";
analytics_init_debug13 = "UMAnalytics pageBegin=%@, beginTime:%@";
analytics_init_debug14 = "UMAnalytics pageEnd=%@, duration:%lld";
analytics_init_debug15 = "UMAnalytics eventBegin eventId=%@, label=%@";
analytics_init_debug16 = "UMAnalytics ekvBegin ekvEvent.id=%@, ekvEvent.key=%@";
analytics_init_debug17 = "UMAnalytics统计发送内容：active_user:%@, activate_msg:%@, sessions:%@, ekv:%@ ,error:%@";

analytics_init_debug18 = "UMAnalytics 写入缓存:%@,config:%@,row:%d";
analytics_init_debug19 = "UMAnalytics 缓存读取:%@";
analytics_init_debug20 = "UMAnalytics 缓存读取失败:config:%@,isReadyBuild:%d";











analytics_init_verbose1 = "UMAnalytics 发送日志：%@";
analytics_init_verbose2 = "UIApplicationWillTerminateNotification arrived";
analytics_init_verbose3 = "app inactivate enter";
analytics_init_verbose4 = "viewDidAppear is: %@";
analytics_init_verbose5 = "viewDidDisappear is: %@";



