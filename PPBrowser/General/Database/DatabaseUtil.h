//
//  DatabaseUtil.h
//  Line
//
//  Created by qingbin on 2018/6/21.
//  Copyright © 2018年 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>
@class DatabaseUnit;

#ifndef _DatabaseUtil_
#define DB_EXEC(unit) [[DatabaseUtil shareInstance] executeUnit:unit];
#endif

@interface DatabaseUtil : NSObject

+ (instancetype) shareInstance;
- (void)executeUnit:(DatabaseUnit*) unit;

- (void)asyncOnQueue:(void(^)(void))executeBlock;

@end
