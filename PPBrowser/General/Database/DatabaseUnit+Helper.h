//
//  DatabaseUnit+Helper.h
//  Keyboard
//
//  Created by qingbin on 2022/1/21.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "DatabaseUnit.h"

#import "FMDatabaseQueue.h"
#import "FMDatabase.h"

@interface DatabaseUnit (Helper)

+ (DatabaseUnit*)createTabTable;

// 创建的时候就需要先保存, 否则等到saveTab时再保存会导致顺序不对
// 因为创建的时候就是首页, 所以必定存在internal://的url
// 插入url可以保证标签页的标题正常显示

//由于alltabs不是表示所有数据,因此采用以下策略:
//新开的页面order都是取最大值,已存在的页面由于只更新url不改变order
//所以不会影响到历史tab。
//因此同样是最大值的order,可以根据插入的时间来判断顺序
//然后打开标签页时,再重新修改成正确的order。
+ (DatabaseUnit*)insertIntoTabWithTabId:(NSString*)tabId
                                    url:(NSString*)url
                                  order:(int)order;

// 插入或者更新tab model
+ (DatabaseUnit*)insertIntoTabWithTitle:(NSString*)title
                                    url:(NSString*)url
                                  tabId:(NSString*)tabId
                     urlHistorySnapshot:(NSString*)urlHistorySnapshot
                 urlHistoryCurrentIndex:(int)urlHistoryCurrentIndex
                             isSelected:(BOOL)isSelected;

+ (DatabaseUnit*)selectAllTabs;

+ (DatabaseUnit*)deleteTabWithTabId:(NSString*)tabId;

+ (DatabaseUnit*)deleteTabArrayWithTabIds:(NSArray*)tabIds;

+ (DatabaseUnit*)deleteAllTabs;

+ (DatabaseUnit*)queryThatSelectedTab;

+ (DatabaseUnit*)updateSelectedTabWithTabId:(NSString*)tabId;

+ (DatabaseUnit*)updateTabTitleWithTabId:(NSString*)tabId title:(NSString*)title;

//批量更新tab的顺序
+ (DatabaseUnit*)updateAllTabsOrder:(NSArray*)allTabModels;

//批量更新urlHistorySnapshot
+ (DatabaseUnit*)updateTabWithTabIdArray:(NSArray<NSString *> *)tabIdArray
                 urlHistorySnapshotArray:(NSArray<NSString *> *)urlHistorySnapshotArray;

@end

