//
//  DatabaseUnit+Helper.m
//  Keyboard
//
//  Created by qing<PERSON> on 2022/1/21.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "DatabaseUnit+Helper.h"

#import "ReactiveCocoa.h"
#import "TabModel.h"
#import "PPEnums.h"

#import "CustomTagModel.h"
#import "BrowserUtils.h"
#import "UserAgentModel.h"
#import "AdBlockModel.h"
#import "MaizyHeader.h"
#import "ToolbarGestureModel.h"

@implementation DatabaseUnit (Helper)

+ (DatabaseUnit*)createTabTable
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = @"CREATE TABLE IF NOT EXISTS t_tab(tabId TEXT PRIMARY KEY,title TEXT, url TEXT, ppOrder INTEGER, urlHistorySnapshot TEXT, urlHistoryCurrentIndex INTEGER, isSelected INTEGER, ctime TEXT, update_time TEXT)";
        BOOL result = [db executeUpdate:command];
        
        /*
         searchType:
         1-url是搜索引擎+关键字
         2-是网址url
         */
        command = @"CREATE TABLE IF NOT EXISTS t_history(historyId TEXT PRIMARY KEY,keyword TEXT,title TEXT, url TEXT, searchType INTEGER, ctime TEXT)";
        result = [db executeUpdate:command];
        
        //书签
        command = @"CREATE TABLE IF NOT EXISTS t_bookmark(bookmarkId TEXT PRIMARY KEY,parentId TEXT,title TEXT, url TEXT, fileType INTEGER, ppOrder INTEGER, updateTime TEXT DEFAULT '1', ctime TEXT)";
        result = [db executeUpdate:command];
        
        //添加parentId索引
        //创建索引（在同一事务中），在"获取下一级目录的数量"操作中的耗时操作
        [db executeUpdate:@"CREATE INDEX IF NOT EXISTS idx_bookmark_parentId ON t_bookmark(parentId)"];
        
        //插入一条根节点
        command = @"INSERT INTO t_bookmark(bookmarkId, parentId, title) VALUES (?,?,?)\
        on CONFLICT(bookmarkId) DO UPDATE SET title=excluded.title WHERE excluded.bookmarkId=t_bookmark.bookmarkId;\
        ";
        result = [db executeUpdate:command, @"root", @"", NSLocalizedString(@"home.bookMark", nil)];
        
        command = @"SELECT sql FROM sqlite_master WHERE tbl_name='t_bookmark'";
        FMResultSet *reader = [db executeQuery:command];
        if ([reader next]) {
            id data = [reader objectForColumnIndex:0];
            if ([data isKindOfClass:[NSString class]]) {
                if ([data rangeOfString:@"ppOrder"].location == NSNotFound) {
                    @try {
                        command = @"ALTER TABLE t_bookmark ADD ppOrder INTEGER";
                        [db executeUpdate:command];
                    }
                    @catch (NSException *exception){}
                    @finally {}
                }
                
                if ([data rangeOfString:@"updateTime"].location == NSNotFound) {
                    @try {
                        command = @"ALTER TABLE t_bookmark ADD updateTime TEXT DEFAULT '1'";
                        [db executeUpdate:command];
                    }
                    @catch (NSException *exception){}
                    @finally {}
                }
            }
        }

        //2.6.0
        command = @"CREATE TABLE IF NOT EXISTS t_userscript(uuid TEXT PRIMARY KEY, name TEXT, author TEXT, namespace TEXT, version TEXT,  desc TEXT, iconUrl TEXT, runAt TEXT, selectFrame INTEGER, frameOption INTEGER, isAutoUpdate INTEGER, updateUrl TEXT, downloadUrl TEXT, content TEXT, isActive INTEGER, ppOrder INTEGER, sql_includes TEXT, sql_matches TEXT, sql_excludes TEXT, sql_grants TEXT, sql_requireUrls TEXT, sql_resourceUrls TEXT, whiteList TEXT, blackList TEXT, updateTime TEXT DEFAULT '1', ctime TEXT)";
        result = [db executeUpdate:command];
        
        command = @"SELECT sql FROM sqlite_master WHERE tbl_name='t_userscript'";
        reader = [db executeQuery:command];
        if ([reader next]) {
            //决定将脚本所有数据都保存到数据库中，所以需要添加其他字段
            // author namespace iconUrl  runAt  updateUrl downloadUrl content sql_includes sql_matches sql_excludes sql_grants sql_requireUrls sql_resourceUrls
            
            id data = [reader objectForColumnIndex:0];
            if ([data isKindOfClass:[NSString class]]) {
        
                if ([data rangeOfString:@"sql_resourceUrls"].location == NSNotFound) {
                    @try {
                        command = @"ALTER TABLE t_userscript ADD sql_resourceUrls TEXT DEFAULT ''";
                        [db executeUpdate:command];
                    }
                    @catch (NSException *exception){}
                    @finally {}
                }
                
                if ([data rangeOfString:@"sql_requireUrls"].location == NSNotFound) {
                    @try {
                        command = @"ALTER TABLE t_userscript ADD sql_requireUrls TEXT DEFAULT ''";
                        [db executeUpdate:command];
                    }
                    @catch (NSException *exception){}
                    @finally {}
                }
                
                if ([data rangeOfString:@"sql_grants"].location == NSNotFound) {
                    @try {
                        command = @"ALTER TABLE t_userscript ADD sql_grants TEXT DEFAULT ''";
                        [db executeUpdate:command];
                    }
                    @catch (NSException *exception){}
                    @finally {}
                }
                
                if ([data rangeOfString:@"sql_excludes"].location == NSNotFound) {
                    @try {
                        command = @"ALTER TABLE t_userscript ADD sql_excludes TEXT DEFAULT ''";
                        [db executeUpdate:command];
                    }
                    @catch (NSException *exception){}
                    @finally {}
                }
                
                if ([data rangeOfString:@"sql_matches"].location == NSNotFound) {
                    @try {
                        command = @"ALTER TABLE t_userscript ADD sql_matches TEXT DEFAULT ''";
                        [db executeUpdate:command];
                    }
                    @catch (NSException *exception){}
                    @finally {}
                }
                
                if ([data rangeOfString:@"sql_includes"].location == NSNotFound) {
                    @try {
                        command = @"ALTER TABLE t_userscript ADD sql_includes TEXT DEFAULT ''";
                        [db executeUpdate:command];
                    }
                    @catch (NSException *exception){}
                    @finally {}
                }
                
                if ([data rangeOfString:@"author"].location == NSNotFound) {
                    @try {
                        command = @"ALTER TABLE t_userscript ADD author TEXT DEFAULT ''";
                        [db executeUpdate:command];
                    }
                    @catch (NSException *exception){}
                    @finally {}
                }
                
                if ([data rangeOfString:@"namespace"].location == NSNotFound) {
                    @try {
                        command = @"ALTER TABLE t_userscript ADD namespace TEXT DEFAULT ''";
                        [db executeUpdate:command];
                    }
                    @catch (NSException *exception){}
                    @finally {}
                }
                
                if ([data rangeOfString:@"iconUrl"].location == NSNotFound) {
                    @try {
                        command = @"ALTER TABLE t_userscript ADD iconUrl TEXT DEFAULT ''";
                        [db executeUpdate:command];
                    }
                    @catch (NSException *exception){}
                    @finally {}
                }
                
                if ([data rangeOfString:@"runAt"].location == NSNotFound) {
                    @try {
                        command = @"ALTER TABLE t_userscript ADD runAt TEXT DEFAULT ''";
                        [db executeUpdate:command];
                    }
                    @catch (NSException *exception){}
                    @finally {}
                }
                
                if ([data rangeOfString:@"updateUrl"].location == NSNotFound) {
                    @try {
                        command = @"ALTER TABLE t_userscript ADD updateUrl TEXT DEFAULT ''";
                        [db executeUpdate:command];
                    }
                    @catch (NSException *exception){}
                    @finally {}
                }
                
                if ([data rangeOfString:@"downloadUrl"].location == NSNotFound) {
                    @try {
                        command = @"ALTER TABLE t_userscript ADD downloadUrl TEXT DEFAULT ''";
                        [db executeUpdate:command];
                    }
                    @catch (NSException *exception){}
                    @finally {}
                }
                
                if ([data rangeOfString:@"content"].location == NSNotFound) {
                    @try {
                        command = @"ALTER TABLE t_userscript ADD content TEXT DEFAULT ''";
                        [db executeUpdate:command];
                    }
                    @catch (NSException *exception){}
                    @finally {}
                }
                
                
                if ([data rangeOfString:@"selectFrame"].location == NSNotFound) {
                    @try {
                        command = @"ALTER TABLE t_userscript ADD COLUMN selectFrame INTEGER DEFAULT 0";
                        [db executeUpdate:command];
                    }
                    @catch (NSException *exception){}
                    @finally {}
                }
                
                if ([data rangeOfString:@"ppOrder"].location == NSNotFound) {
                    @try {
                        command = @"ALTER TABLE t_userscript ADD ppOrder INTEGER";
                        [db executeUpdate:command];
                    }
                    @catch (NSException *exception){}
                    @finally {}
                }
                
                if ([data rangeOfString:@"updateTime"].location == NSNotFound) {
                    @try {
                        command = @"ALTER TABLE t_userscript ADD updateTime TEXT DEFAULT '1'";
                        [db executeUpdate:command];
                    }
                    @catch (NSException *exception){}
                    @finally {}
                }
                
                if ([data rangeOfString:@"isAutoUpdate"].location == NSNotFound) {
                    @try {
                        command = @"ALTER TABLE t_userscript ADD COLUMN isAutoUpdate INTEGER DEFAULT 0";
                        [db executeUpdate:command];
                    }
                    @catch (NSException *exception){}
                    @finally {}
                }
                
                if ([data rangeOfString:@"frameOption"].location == NSNotFound) {
                    @try {
                        command = @"ALTER TABLE t_userscript ADD COLUMN frameOption INTEGER DEFAULT 0";
                        [db executeUpdate:command];
                    }
                    @catch (NSException *exception){}
                    @finally {}
                }
                
                if ([data rangeOfString:@"whiteList"].location == NSNotFound) {
                    @try {
                        command = @"ALTER TABLE t_userscript ADD whiteList TEXT DEFAULT ''";
                        [db executeUpdate:command];
                    }
                    @catch (NSException *exception){}
                    @finally {}
                }
                
                if ([data rangeOfString:@"blackList"].location == NSNotFound) {
                    @try {
                        command = @"ALTER TABLE t_userscript ADD blackList TEXT DEFAULT ''";
                        [db executeUpdate:command];
                    }
                    @catch (NSException *exception){}
                    @finally {}
                }
            }
        }
        
        //首页自定义标签页
        command = @"CREATE TABLE IF NOT EXISTS t_hometags(uuid TEXT PRIMARY KEY, title TEXT, targetUrl TEXT, iconUrl TEXT, type INTEGER, ppOrder INTEGER, updateTime TEXT DEFAULT '1', ctime TEXT)";
        result = [db executeUpdate:command];
        
        command = @"SELECT sql FROM sqlite_master WHERE tbl_name='t_hometags'";
        reader = [db executeQuery:command];
        if ([reader next]) {
            id data = [reader objectForColumnIndex:0];
            if ([data isKindOfClass:[NSString class]]) {
                if ([data rangeOfString:@"iconUrl"].location == NSNotFound) {
                    @try {
                        command = @"ALTER TABLE t_hometags ADD COLUMN iconUrl TEXT";
                        [db executeUpdate:command];
                    }
                    @catch (NSException *exception){}
                    @finally {}
                }
            }
            
            if ([data rangeOfString:@"updateTime"].location == NSNotFound) {
                @try {
                    command = @"ALTER TABLE t_hometags ADD updateTime TEXT DEFAULT '1'";
                    [db executeUpdate:command];
                }
                @catch (NSException *exception){}
                @finally {}
            }
        }
        
        NSUserDefaults* userDefault = [NSUserDefaults standardUserDefaults];
        NSNumber* val = [userDefault objectForKey:@"t_hometags"];
        LocalizableOption option = [BrowserUtils localizableOption];
        
        if(!val) {
            //只插入一次, 后面即使删除了也不再添加回来
            [userDefault setObject:@(true) forKey:@"t_hometags"];
            [userDefault synchronize];
            
            //插入"书签"两个标签
            NSMutableArray* items = [NSMutableArray array];
            
            CustomTagModel* item = [CustomTagModel bookMark];
            [items addObject:item];
            
            item = [CustomTagModel guideline];
            [items addObject:item];
        
            if(option != LocalizableOptionZh_Hans) {
                //非大陆地区,添加Youtube入口
                item = [CustomTagModel wikipedia];
                [items addObject:item];
                
                item = [CustomTagModel youtube];
                [items addObject:item];
                
                item = [CustomTagModel twitter];
                [items addObject:item];
                
                item = [CustomTagModel reddit];
                [items addObject:item];
                
                item = [CustomTagModel greasyfork];
                [items addObject:item];
            } else {
                //大陆地区
                //秘塔AI
//                item = [CustomTagModel metaso];
//                [items addObject:item];
                
                item = [CustomTagModel youtube];
                [items addObject:item];
                
                item = [CustomTagModel greasyfork];
                [items addObject:item];
                
                //增加其他油猴脚本网站
                //gfmirror
                item = [CustomTagModel gfmirror];
                [items addObject:item];
            }
            
            NSString* ctime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
            
            command = @"INSERT INTO t_hometags(uuid, title, targetUrl, iconUrl, type, ppOrder, ctime) VALUES(?,?,?,?,?,?,?);";
            for(int i=0;i<items.count;i++) {
                CustomTagModel* item = items[i];
                [db executeUpdate:command, item.uuid, item.title, item.targetUrl, item.iconUrl, @(item.type), @(item.ppOrder), ctime];
            }
            //删除旧版本的数据
            command = @"DELETE FROM t_hometags WHERE type=2";
            [db executeUpdate:command];
            
//            command = @"INSERT INTO t_hometags(uuid, title, targetUrl, type, ppOrder, ctime) VALUES(?,?,?,?,?,?);";
//            [db executeUpdate:command, item.uuid, item.title, item.targetUrl, @(item.type), @(item.ppOrder), ctime];
            
//            item = [CustomTagModel fileManager];
//
//            command = @"INSERT INTO t_hometags(uuid, title, targetUrl, type, ppOrder, ctime) VALUES(?,?,?,?,?,?);";
//            [db executeUpdate:command, item.uuid, item.title, item.targetUrl, @(item.type), @(item.ppOrder), ctime];
        }
        
        //新版和旧版，都插入一次秘塔AI
        if(option == LocalizableOptionZh_Hans || option == LocalizableOptionZh_Hant) {
            //简体或者繁体
            NSNumber* val = [userDefault objectForKey:@"t_hometags_metaso"];
            if(!val) {
                //只插入一次, 后面即使删除了也不再添加回来
                [userDefault setObject:@(true) forKey:@"t_hometags_metaso"];
                [userDefault synchronize];
                
                NSString* ctime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
                CustomTagModel* item = [CustomTagModel metaso];
                [db executeUpdate:@"INSERT INTO t_hometags(uuid, title, targetUrl, iconUrl, type, ppOrder, ctime) VALUES(?,?,?,?,?,?,?);", item.uuid, item.title, item.targetUrl, item.iconUrl, @(item.type), @(item.ppOrder), ctime];
            }
        }
        
        //标记模式
        command = @"CREATE TABLE IF NOT EXISTS t_tagit(tagitId TEXT PRIMARY KEY, host TEXT, xpath TEXT, updateTime TEXT DEFAULT '1', ctime TEXT)";
        result = [db executeUpdate:command];
                
        command = @"SELECT sql FROM sqlite_master WHERE tbl_name='t_tagit'";
        reader = [db executeQuery:command];
        if ([reader next]) {
            id data = [reader objectForColumnIndex:0];
            if ([data rangeOfString:@"updateTime"].location == NSNotFound) {
                @try {
                    command = @"ALTER TABLE t_tagit ADD updateTime TEXT DEFAULT '1'";
                    [db executeUpdate:command];
                }
                @catch (NSException *exception){}
                @finally {}
            }
        }
        
        //拦截APP跳转
        command = @"CREATE TABLE IF NOT EXISTS t_alertJump(uuid TEXT PRIMARY KEY, scheme TEXT, ctime TEXT)";
        result = [db executeUpdate:command];
        
        //UserAgent/浏览器标识
        command = @"CREATE TABLE IF NOT EXISTS t_useragent(uuid TEXT PRIMARY KEY, title TEXT, value TEXT, type INTEGER, ppOrder INTEGER, isSelected INTEGER, ctime TEXT)";
        result = [db executeUpdate:command];
        NSArray* items = @[[UserAgentModel iphone], [UserAgentModel ipad], [UserAgentModel android], [UserAgentModel safari], [UserAgentModel edge], [UserAgentModel chrome], [UserAgentModel firefox]];
        for(UserAgentModel* item in items) {
            //默认useragent的值不能写死，根据ppOrder来从代码中读取真正的值
            command = @"INSERT INTO t_useragent(uuid, title, value, type, isSelected, ppOrder, ctime) VALUES (?,?,?,?,?,?,?)\
            on CONFLICT(uuid) DO UPDATE SET title=excluded.title WHERE excluded.uuid=t_useragent.uuid;\
            ";
            result = [db executeUpdate:command, item.uuid, item.title, item.value, @(item.type), @(item.isSelected), @(item.ppOrder), item.ctime];
        }
        
        //网页黑名单
        command = @"CREATE TABLE IF NOT EXISTS t_webBlacklist(uuid TEXT PRIMARY KEY, url TEXT, updateTime TEXT DEFAULT '1', ctime TEXT)";
        result = [db executeUpdate:command];
        
        command = @"SELECT sql FROM sqlite_master WHERE tbl_name='t_webBlacklist'";
        reader = [db executeQuery:command];
        if ([reader next]) {
            id data = [reader objectForColumnIndex:0];
            if ([data rangeOfString:@"updateTime"].location == NSNotFound) {
                @try {
                    command = @"ALTER TABLE t_webBlacklist ADD updateTime TEXT DEFAULT '1'";
                    [db executeUpdate:command];
                }
                @catch (NSException *exception){}
                @finally {}
            }
        }
        
        //广告过滤
        command = @"CREATE TABLE IF NOT EXISTS t_adblock(uuid TEXT PRIMARY KEY, title TEXT, url TEXT, isActive INTEGER, updateTime TEXT, ctime TEXT)";
        result = [db executeUpdate:command];
        
        val = [userDefault objectForKey:@"t_adblock"];
        if(!val) {
            //只插入一次, 初始化内置规则
            [userDefault setObject:@(true) forKey:@"t_adblock"];
            [userDefault synchronize];
            
            NSArray* items = @[[AdBlockModel easylistModel], [AdBlockModel easyprivacyModel], [AdBlockModel antiadblockfiltersModel], [AdBlockModel jiekouADModel]];
            
            command = @"INSERT INTO t_adblock(uuid, title, url, isActive, updateTime, ctime) VALUES(?,?,?,?,?,?);";
            for(int i=0;i<items.count;i++) {
                AdBlockModel* item = items[i];
                [db executeUpdate:command, item.uuid, item.title, item.url, @(item.isActive), item.updateTime, item.ctime];
            }
        }
        
        //2.6.3 自动翻页,规则列表
        command = @"CREATE TABLE IF NOT EXISTS t_autopage_list ("
                        "uuid TEXT PRIMARY KEY,"
                        "sourceUrl TEXT,"
                        "patternUrl TEXT,"
                        "type INTEGER,"
                        "isActive INTEGER,"
                        "pageElementXPath TEXT,"
                        "nextXPath TEXT,"
                        "updateTime TEXT,"
                        "ctime TEXT"
                        ");";
        result = [db executeUpdate:command];
        
        // 创建自动翻页黑名单表
        [db executeUpdate:@"CREATE TABLE IF NOT EXISTS t_autopage_blacklist ("
         "uuid TEXT PRIMARY KEY,"           // 主键
         "sourceUrl TEXT,"                  // 原始URL
         "patternUrl TEXT,"                 // URL匹配模式(正则表达式)
         "updateTime TEXT,"                 // 更新时间
         "ctime TEXT"                       // 创建时间
         ");"];
        

        // v2.6.8, 工具栏长按手势触发事件
        command = @"CREATE TABLE IF NOT EXISTS t_toolbar_gesture(uuid TEXT PRIMARY KEY, type INTEGER, groupType INTEGER, position INTEGER)";
        result = [db executeUpdate:command];
        
        val = [userDefault objectForKey:@"t_toolbar_gesture"];
        if(!val) {
            //只插入一次, 初始化内置工具栏手势触发事件
            [userDefault setObject:@(true) forKey:@"t_toolbar_gesture"];
            [userDefault synchronize];
            
            NSArray* builtInList = [ToolbarGestureModel builtInList];
            
            command = @"INSERT INTO t_toolbar_gesture(uuid, type, groupType, position) VALUES(?,?,?,?);";
            for(int i=0;i<builtInList.count;i++) {
                ToolbarGestureModel* item = builtInList[i];
                [db executeUpdate:command, item.uuid, @(item.type), @(item.groupType), @(item.position)];
            }
        }
        
        // v2.7.1, 搜索列表
//        command = @"CREATE TABLE IF NOT EXISTS t_search(uuid TEXT PRIMARY KEY, type INTEGER, position INTEGER)";
//        result = [db executeUpdate:command];
//        // 插入内置搜索引擎
//        val = [userDefault objectForKey:@"t_search"];
//        if(!val) {
//            //只插入一次, 初始化内置搜索引擎
//            [userDefault setObject:@(true) forKey:@"t_search"];
//            [userDefault synchronize];
//        }
        
        // v2.7.8, 文本替换
        command = @"CREATE TABLE IF NOT EXISTS t_bookReplace(uuid TEXT PRIMARY KEY, replaceText TEXT, resultText TEXT, isActive INTEGER, ctime TEXT)";
        result = [db executeUpdate:command];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

+ (DatabaseUnit*)insertIntoTabWithTabId:(NSString*)tabId
                                    url:(NSString*)url
                                  order:(int)order
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        //on CONFLICT(tabId) ... 这个条件判断是为了去除冲突提醒
        NSString* command = @"INSERT INTO t_tab(tabId, url, ppOrder, ctime, update_time) VALUES (?,?,?,?,?) on CONFLICT(tabId) DO UPDATE SET url=excluded.url WHERE excluded.tabId=t_tab.tabId;";
        
        NSString* ctime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        NSString* update_time = ctime;

        BOOL result = [db executeUpdate:command, tabId, url, @(order), ctime, update_time];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

// 插入或者更新tab model
+ (DatabaseUnit*)insertIntoTabWithTitle:(NSString*)title
                                    url:(NSString*)url
                                  tabId:(NSString*)tabId
                     urlHistorySnapshot:(NSString*)urlHistorySnapshot
                 urlHistoryCurrentIndex:(int)urlHistoryCurrentIndex
                             isSelected:(BOOL)isSelected
{
    //https://stackoverflow.com/questions/3634984/insert-if-not-exists-else-update
    
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
                
        NSString* command = @"INSERT INTO t_tab(title, url, tabId, urlHistorySnapshot, urlHistoryCurrentIndex, isSelected, ctime, update_time) VALUES (?,?,?,?,?,?,?,?)\
        on CONFLICT(tabId) DO UPDATE SET title=excluded.title, url=excluded.url,\
        urlHistorySnapshot=excluded.urlHistorySnapshot, urlHistoryCurrentIndex=excluded.urlHistoryCurrentIndex, \
        isSelected=excluded.isSelected\
        WHERE excluded.tabId=t_tab.tabId;\
        ";
        
        NSString* ctime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        NSString* update_time = ctime;
        NSString* urlsHistory = urlHistorySnapshot?:@"";
        BOOL result = [db executeUpdate:command, title, url, tabId, urlsHistory, @(urlHistoryCurrentIndex), @(isSelected), ctime, update_time];
        
        NSNumber* insertId = nil;
        if(result) {
            NSString* command = @"SELECT last_insert_rowid() FROM t_tab;";
            FMResultSet* set = [db executeQuery:command];
            
            if([set next]) {
                insertId = [set resultDictionary][@"last_insert_rowid()"];
            }
        }

        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(insertId, result);
            }
        });
    };
    
    return unit;
}

//批量更新urlHistorySnapshot
+ (DatabaseUnit*)updateTabWithTabIdArray:(NSArray<NSString *> *)tabIdArray
                 urlHistorySnapshotArray:(NSArray<NSString *> *)urlHistorySnapshotArray
{
    DatabaseUnit* unit = [DatabaseUnit new];
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        BOOL result = YES;
        for(int i=0;i<tabIdArray.count;i++) {
            NSString* tabId = tabIdArray[i];
            NSString* urlHistorySnapshot = urlHistorySnapshotArray[i];
            NSString* command = @"UPDATE t_tab SET urlHistorySnapshot = ? WHERE tabId = ?";
            
            result = [db executeUpdate:command, urlHistorySnapshot?:@"", tabId] && result;
        }

        if(unit.completeBlock) {
            unit.completeBlock(nil, result);
        }
    };
    
    return unit;
}

+ (DatabaseUnit*)selectAllTabs
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = @"SELECT * FROM t_tab order by ppOrder asc, ctime asc";
        FMResultSet* set = [db executeQuery:command];
        
        NSMutableArray *array = [[NSMutableArray alloc] init];
        while([set next]) {
            TabModel* item = [[TabModel alloc]initWithDictionary:[set resultDictionary] error:nil];
            [array addObject:item];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(array,YES);
            }
        });
    };
    
    return unit;
}

+ (DatabaseUnit*)queryThatSelectedTab
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = @"SELECT * FROM t_tab WHERE isSelected = 1";
        FMResultSet* set = [db executeQuery:command];
        
        TabModel* item;
        if([set next]) {
            item = [[TabModel alloc]initWithDictionary:[set resultDictionary] error:nil];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(item,YES);
            }
        });
    };
    
    return unit;
}

+ (DatabaseUnit*)deleteTabWithTabId:(NSString*)tabId
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = @"DELETE FROM t_tab WHERE tabId = ?";
        
        BOOL result = [db executeUpdate:command,tabId];

        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

+ (DatabaseUnit*)deleteTabArrayWithTabIds:(NSArray*)tabIds
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = @"DELETE FROM t_tab WHERE tabId = ?";
         
        BOOL result = YES;
        for(NSString *tabId in tabIds) {
            [db executeUpdate:command,tabId];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

+ (DatabaseUnit*)deleteAllTabs
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = @"DELETE FROM t_tab;";
        
        BOOL result = [db executeUpdate:command];

        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

+ (DatabaseUnit*)updateSelectedTabWithTabId:(NSString*)tabId
{
    DatabaseUnit* unit = [DatabaseUnit new];
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = @"UPDATE t_tab SET isSelected = ?";
        BOOL result = [db executeUpdate:command,@(NO)];
        
        command = @"UPDATE t_tab SET isSelected=? WHERE tabId = ?";
        result = [db executeUpdate:command,@(YES),tabId];
        if(unit.completeBlock) {
            unit.completeBlock(nil, result);
        }
    };
    
    return unit;
}

+ (DatabaseUnit*)updateTabTitleWithTabId:(NSString*)tabId title:(NSString*)title
{
    DatabaseUnit* unit = [DatabaseUnit new];
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = @"UPDATE t_tab SET title = ? WHERE tabId = ?";
        BOOL result = [db executeUpdate:command,title,tabId];
        if(unit.completeBlock) {
            unit.completeBlock(nil, result);
        }
    };
    
    return unit;
}

//批量更新tab的顺序
+ (DatabaseUnit*)updateAllTabsOrder:(NSArray*)allTabModels
{
    DatabaseUnit* unit = [DatabaseUnit new];
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSMutableString* command = [NSMutableString new];
        [command appendString:@"UPDATE t_tab SET ppOrder = CASE tabId \n"];
        
        for(int i=0;i<allTabModels.count;i++) {
            TabModel* item = allTabModels[i];
            item.ppOrder = i;
            NSString* str = [NSString stringWithFormat:@"WHEN '%@' THEN %@ \n",item.tabId,@(i)];
            [command appendString:str];
            
            if(i==allTabModels.count-1) {
                [command appendString:@"END;"];
            }
        }
        
        BOOL result = [db executeUpdate:command];
        if(unit.completeBlock) {
            unit.completeBlock(nil, result);
        }
    };
    
    return unit;
}

@end
