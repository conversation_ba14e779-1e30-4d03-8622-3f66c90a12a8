//
//  NSString+Helper.m
//  QRCode
//
//  Created by qingbin on 2021/11/9.
//  Copyright © 2021 qingbin. All rights reserved.
//

#import "NSString+Helper.h"

@implementation NSString (Helper)

+ (NSString *)convertToJsonString:(id)source options:(BOOL)plain
{
    if ([NSJSONSerialization isValidJSONObject:source]) {
        NSError *error;
        NSData *data = [NSJSONSerialization dataWithJSONObject:source options:plain?kNilOptions:NSJSONWritingPrettyPrinted error:&error];
        if (!error) {
            return [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
        }
    }
    return nil;
}

+ (id)jsonConvertToObject:(NSString *)source
{
    NSData *data = [source dataUsingEncoding:NSUTF8StringEncoding];
    if(data == nil) return nil;
    
    NSError *error;
    id object = [NSJSONSerialization JSONObjectWithData:data options:kNilOptions error:&error];
    if (!error) return object;
    
    return nil;
}

+ (NSMutableAttributedString *)attributedStringWithText:(NSString *)text
                                                   font:(UIFont *)font
                                                  color:(UIColor *)color
                                                  title:(NSString *)title
                                              titleFont:(UIFont *)titleFont
                                             titleColor:(UIColor *)titleColor
{
    NSMutableAttributedString *attriString = [[NSMutableAttributedString alloc] initWithString:text];
    [attriString addAttribute:NSFontAttributeName
                        value:font
                        range:NSMakeRange(0, text.length)];
    
    [attriString addAttribute:NSForegroundColorAttributeName
                        value:color
                        range:NSMakeRange(0, text.length)];
    
    [attriString addAttribute:NSFontAttributeName
                        value:titleFont
                        range:[text rangeOfString:title?:@""]];
    
    [attriString addAttribute:NSForegroundColorAttributeName
                        value:titleColor
                        range:[text rangeOfString:title?:@""]];
    
    return attriString;
}


+ (CGSize)sizeWithText:(NSString *)text fontSize:(CGFloat)fontSize width:(CGFloat)width
{
    NSDictionary * tdic = [NSDictionary dictionaryWithObjectsAndKeys:[UIFont systemFontOfSize:fontSize], NSFontAttributeName,nil];
    CGSize size = CGSizeMake(width, 20000.0f);
    size =[text boundingRectWithSize:size options:NSStringDrawingUsesLineFragmentOrigin | NSStringDrawingUsesFontLeading attributes:tdic context:nil].size;

    return CGSizeMake(size.width, ceil(size.height));
}

+ (CGSize)sizeWithAttributedText:(NSAttributedString *)text width:(CGFloat)width
{
    UITextView *view = [[UITextView alloc] initWithFrame:CGRectMake(0, 0, width, 10)];
    view.attributedText=text;
    CGSize size=[view sizeThatFits:CGSizeMake(width, CGFLOAT_MAX)];
    return size;
}

- (BOOL)isNumberValue
{
    return [self isDoubleValue] || [self isFloatValue] || [self isIntValue];
}

- (BOOL)isDoubleValue
{
    NSScanner* scanner = [NSScanner scannerWithString:self];
    double val;
    return [scanner scanDouble:&val] && [scanner isAtEnd];
}

- (BOOL)isFloatValue
{
    NSScanner* scanner = [NSScanner scannerWithString:self];
    float val;
    return [scanner scanFloat:&val] && [scanner isAtEnd];
}

- (BOOL)isIntValue
{
    NSScanner* scanner = [NSScanner scannerWithString:self];
    int val;
    return [scanner scanInt:&val] && [scanner isAtEnd];
}

- (NSString*)trim:(NSString *)charactersInString
{
    return [self stringByTrimmingCharactersInSet:[NSCharacterSet characterSetWithCharactersInString:charactersInString]];
}

- (NSString *)encodeURIComponent
{
    NSCharacterSet *allowedCharacters = [NSCharacterSet characterSetWithCharactersInString:@"abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-._~"];
    
    return [self stringByAddingPercentEncodingWithAllowedCharacters:allowedCharacters];
}

//base64编码
+ (NSString *)base64EncodeWithDictionary:(NSDictionary *)dict
{
    NSError *error;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:dict
                                                      options:0
                                                        error:&error];
    if (error) {
        return nil;
    }
    
    //转换为 Base64 字符串
    NSString *base64String = [jsonData base64EncodedStringWithOptions:0];
    
    return base64String;
}

// 移除脚本中的//# sourceMappingURL=xxx
// sourceMappingURL 可能以 //@ 或 //# 开头
+ (NSString *)removeSourceMappingURL:(NSString *)scriptContent
{
    if (!scriptContent || scriptContent.length == 0) {
        return scriptContent;
    }
    
    // 先检查是否包含sourceMappingURL
    if ([scriptContent rangeOfString:@"sourceMappingURL"].location == NSNotFound) {
        return scriptContent;
    }
    
    NSError *error = nil;
    NSRegularExpression *regex = [NSRegularExpression regularExpressionWithPattern:@"\\s*//[@#]\\s*sourceMappingURL=.*$"
                                                                          options:NSRegularExpressionAnchorsMatchLines
                                                                            error:&error];
    if (error) {
        NSLog(@"正则表达式创建失败: %@", error);
        return scriptContent;
    }
    
    NSString *cleanedScript = [regex stringByReplacingMatchesInString:scriptContent
                                                            options:0
                                                              range:NSMakeRange(0, scriptContent.length)
                                                       withTemplate:@""];
    return cleanedScript;
}

// 编码为Base64字符串
- (NSString *)convertToBase64
{
    if(self.length == 0) return @"";

    // 将原始字符串转换为NSData对象
    NSData *data = [self dataUsingEncoding:NSUTF8StringEncoding];
    if (!data) {
        return @"";
    }

    // 进行Base64编码
    NSString *base64EncodedString = [data base64EncodedStringWithOptions:0];
    if (!base64EncodedString) {
        return @"";
    }

    return base64EncodedString;
}

// 从Base64字符串解码
- (NSString *)convertFromBase64
{
    if(self.length == 0) return @"";

    // 将Base64编码后的字符串解码为NSData对象
    NSData *decodedData = [[NSData alloc] initWithBase64EncodedString:self options:NSDataBase64DecodingIgnoreUnknownCharacters];
    if (!decodedData) {
        return nil; // 返回nil表示解码失败，调用方可以据此判断
    }

    // 将解码后的NSData对象转换为NSString
    NSString *decodedString = [[NSString alloc] initWithData:decodedData encoding:NSUTF8StringEncoding];
    if (!decodedString) {
        return nil;
    }

    return decodedString;
}

@end
