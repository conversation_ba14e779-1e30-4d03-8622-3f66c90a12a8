//
//  UIView+Helper.h
//  QRCode
//
//  Created by qingbin on 2021/11/7.
//  Copyright © 2021 qingbin. All rights reserved.
//

#import <UIKit/UIKit.h>

#import "PPBrowser-Swift.h"

@interface UIView (Helper)

+ (UILabel *)createLabelWithTitle:(NSString *)title
                        textColor:(UIColor *)textColor
                          bgColor:(UIColor *)bgColor
                         fontSize:(CGFloat)fontSize
                    textAlignment:(NSTextAlignment)textAlignment
                            bBold:(BOOL)bBold;;

+ (UIButton *)createButtonWithImage:(UIImage *)image
                              click:(void (^)(id x))nextBlock;

+ (UIButton *)createButtonWithTitle:(NSString *)title
                          textColor:(UIColor *)textColor
                            bgColor:(UIColor *)bgColor
                           fontSize:(CGFloat)fontSize
                              click:(void (^)(id x))nextBlock;

///MBPorgressHUD
//+ (void)showLoading:(UIView *)view animated:(BOOL)animated;
//+ (void)hideLoading:(UIView *)view animated:(BOOL)animated;
//+ (void)showMessage:(UIView *)view message:(NSString *)msg;
//+ (void)showLongMessage:(UIView *)view message:(NSString *)msg;

///ProgressHUD
/// 显示toast
+ (void)showToast:(NSString *)msg;
/// 显示toast
+ (void)showToast:(NSString *)msg delay:(double)delay;
//
+ (void)showToast:(NSString *)msg endEditing:(BOOL)endEditing;
/// 进度显示toast
+ (void)showLoading:(NSString *)msg;
/// 加载中时，需要更新文案（避免闪烁）
+ (void)updateLoadingText:(NSString *)msg;
/// 隐藏toast
+ (void)hideHud:(BOOL)animated;
/// 成功
+ (void)showSucceed:(NSString *)msg;
/// 失败
+ (void)showFailed:(NSString *)msg;
/// 警告
+ (void)showWarning:(NSString *)msg;


+ (void)showCustomMessage:(UIView *)view
                  message:(NSString *)msg
                actionMsg:(NSString *)actionMsg
                   action:(void(^)(void))actionBlock;

- (UIImage*)screenshotWithSize:(CGSize)size offset:(CGPoint)offset quality:(float)quality;
- (UIImage*)screenshotWithAspectRatio:(float)aspectRatio offset:(CGPoint)offset quality:(float)quality;

- (UIImage*)screenshot;

@end
