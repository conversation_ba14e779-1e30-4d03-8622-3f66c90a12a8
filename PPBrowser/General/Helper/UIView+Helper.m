//
//  UIView+Helper.m
//  QRCode
//
//  Created by qingbin on 2021/11/7.
//  Copyright © 2021 qingbin. All rights reserved.
//

#import "UIView+Helper.h"
#import "ReactiveCocoa.h"
#import "MBCustomView.h"
#import "AppDelegate.h"

#import "NSObject+Helper.h"
#import "MBProgressHUD.h"

#import "PPBrowser-Swift.h"

@implementation UIView (Helper)

+ (UILabel *)createLabelWithTitle:(NSString *)title
                        textColor:(UIColor *)textColor
                          bgColor:(UIColor *)bgColor
                         fontSize:(CGFloat)fontSize
                    textAlignment:(NSTextAlignment)textAlignment
                            bBold:(BOOL)bBold
{
    UILabel *label = [[UILabel alloc] init];
    label.backgroundColor = bgColor;
    label.textColor = textColor;
    if (bBold) {
        label.font = [UIFont boldSystemFontOfSize:fontSize];
    } else {
        label.font = [UIFont systemFontOfSize:fontSize];
    }
    
    label.text = title;
    label.textAlignment = textAlignment;
    
    return label;
}

+ (UIButton *)createButtonWithImage:(UIImage *)image
                              click:(void (^)(id x))nextBlock
{
    UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
    [btn setImage:image forState:UIControlStateNormal];
    if (nextBlock != nil) {
        [[btn rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:nextBlock];
    }
    
    return btn;
}

+ (UIButton *)createButtonWithTitle:(NSString *)title
                          textColor:(UIColor *)textColor
                            bgColor:(UIColor *)bgColor
                           fontSize:(CGFloat)fontSize
                              click:(void (^)(id x))nextBlock
{
    UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
    
    [btn setTitle:title forState:UIControlStateNormal];
    btn.backgroundColor = bgColor;
    [btn setTitleColor:textColor forState:UIControlStateNormal];
    btn.titleLabel.font = [UIFont systemFontOfSize:fontSize];
    if (nextBlock != nil) {
        [[btn rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:nextBlock];
    }

    return btn;
}

- (UIImage*)screenshotWithAspectRatio:(float)aspectRatio offset:(CGPoint)offset quality:(float)quality
{
    if(aspectRatio < 0) return nil;
    
    CGSize size;
    if(aspectRatio > 0) {
        float viewAspectRatio = self.frame.size.width / self.frame.size.height;
        if (viewAspectRatio > aspectRatio) {
            size.height = self.frame.size.height;
            size.width = size.height * aspectRatio;
        } else {
            size.width = self.frame.size.width;
            size.height = size.width / aspectRatio;
        }
    } else {
        size = self.frame.size;
    }
    
    return [self screenshotWithSize:size offset:offset quality:quality];
}

- (UIImage*)screenshotWithSize:(CGSize)size offset:(CGPoint)offset quality:(float)quality
{
    if(quality < 0 || quality > 1) return nil;
    
    /**
     [self drawViewHierarchyInRect:afterScreenUpdates:]方法是UIView的一个方法，它使用底层的图形渲染引擎来将指定矩形区域内的内容绘制到图形上下文中。该方法的参数afterScreenUpdates指定是否在更新后立即绘制，如果设置为NO，则在当前屏幕更新周期之前捕获屏幕内容。

     相比之下，使用[self.layer renderInContext:]方法来渲染UIView的层级，它会直接绘制视图的图层到图形上下文中，不需要考虑屏幕更新周期。这种方式更加直接和高效，适用于大多数情况下的UIView截图需求。

     然而，[self drawViewHierarchyInRect:afterScreenUpdates:]方法也有一些优点。它可以捕获到包括CALayer中的一些额外内容，比如Core Animation效果、视频播放器等。此外，它还可以在动画过渡完成后立即捕获当前视图的状态。

     因此，选择使用哪种方法取决于你的具体需求。如果你只需要截图UIView的内容，直接使用[self.layer renderInContext:]方法是简单和高效的选择。如果你希望包括额外的内容或需要在动画完成后截图，可以考虑使用[self drawViewHierarchyInRect:afterScreenUpdates:]方法。
     */
    //这句代码会有问题，如果是从书签直接在新标签页打开，那么会导致首页截图失败。
//    [self drawViewHierarchyInRect:CGRectMake(offset.x, offset.y, size.width, size.height) afterScreenUpdates:NO];
    
    //版本1.3 iOS17.1.1崩溃了
//    UIGraphicsBeginImageContextWithOptions(size, false, [UIScreen mainScreen].scale*quality);
//    CGContextRef context = UIGraphicsGetCurrentContext();
//    [self.layer renderInContext:context];
//
//    UIImage* image = UIGraphicsGetImageFromCurrentImageContext();
//    UIGraphicsEndImageContext();
    
    //版本2.4.6
    UIGraphicsImageRenderer *renderer = [[UIGraphicsImageRenderer alloc] initWithSize:self.bounds.size];
    UIImage *image = [renderer imageWithActions:^(UIGraphicsImageRendererContext * _Nonnull rendererContext) {
        [self.layer renderInContext:rendererContext.CGContext];
    }];
    
    return image;
}

//snapshot会报[_UIReplicantView _isSymbolImage]的错误
- (UIImage*)screenshot
{
    UIGraphicsImageRenderer* render = [[UIGraphicsImageRenderer alloc]initWithSize:self.bounds.size];
    @weakify(self)
    UIImage* image = [render imageWithActions:^(UIGraphicsImageRendererContext * _Nonnull rendererContext) {
        @strongify(self)
        [self drawViewHierarchyInRect:self.frame afterScreenUpdates:YES];
    }];
    
    return image;
}

//+ (void)showLoading:(UIView *)view animated:(BOOL)animated
//{
//    UIView* parentView = [self _hudsParentView:view];
//    [MBProgressHUD showHUDAddedTo:parentView animated:animated];
//}
//
//+ (void)hideLoading:(UIView *)view animated:(BOOL)animated
//{
//    UIView* parentView = [self _hudsParentView:view];
//    [MBProgressHUD hideHUDForView:parentView animated:animated];
//}
//
//+ (void)showLongMessage:(UIView *)view message:(NSString *)msg
//{
//    [self _showMessage:view message:msg time:2.0f];
//}
//
//+ (void)showMessage:(UIView *)view message:(NSString *)msg
//{
//    [self _showMessage:view message:msg time:1.0f];
//}

+ (void)showCustomMessage:(UIView *)view
                  message:(NSString *)msg
                actionMsg:(NSString *)actionMsg
                   action:(void(^)(void))actionBlock
{
    MBCustomView* hud = [MBCustomView showCustomMessage:view
                                                message:msg
                                              actionMsg:actionMsg
                                                 action:actionBlock];
    
    [hud hideAfterDelay:5];
}

//+ (UIView*)_hudsParentView:(UIView*)view
//{
//    UIWindow *_keyWindow = [[UIApplication sharedApplication] keyWindow];
//    [MBProgressHUD hideAllHUDsForView:_keyWindow animated:NO];
//    [MBProgressHUD hideAllHUDsForView:view animated:NO];
//    UIView *_parentView = view;
//    if (!_parentView) {
//        _parentView = _keyWindow;
//    }
//
//    return _parentView;
//}
//
//+ (void)_showMessage:(UIView *)view message:(NSString *)str time:(NSInteger)time
//{
//    if (str.length <= 0) return;
//    // 收键盘
//    [view.window endEditing:YES];
//
//    UIWindow *_keyWindow = [[UIApplication sharedApplication] keyWindow];
//    [MBProgressHUD hideAllHUDsForView:_keyWindow animated:NO];
//    if(view) {
//        [MBProgressHUD hideAllHUDsForView:view animated:NO];
//    }
//    UIView *_parentView = view;
//    if (!_parentView) {
//        _parentView = _keyWindow;
//    }
//
//    MBProgressHUD *hud = [MBProgressHUD showHUDAddedTo:_parentView animated:YES];
//    hud.mode = MBProgressHUDModeText;
//    hud.margin = 10.f;
//    hud.labelText = str;
//    hud.removeFromSuperViewOnHide = YES;
//    [hud hide:YES afterDelay:time];
//}

/// 显示toast
+ (void)showToast:(NSString *)msg
{
    [self showToast:msg delay:1.0f];
}

/// 显示toast
+ (void)showToast:(NSString *)msg delay:(double)delay
{
    [self _showToast:msg delay:delay endEditing:YES];
}

+ (void)showToast:(NSString *)msg endEditing:(BOOL)endEditing
{
    [self _showToast:msg delay:1.0f endEditing:endEditing];
}

/// 显示toast
+ (void)_showToast:(NSString *)msg delay:(double)delay endEditing:(BOOL)endEditing
{
    [self hideHud:NO];
    
    if (msg.length <= 0) return;
    
    UIWindow *_keyWindow = [NSObject normalWindow];
    if (endEditing) {
        [_keyWindow endEditing:YES];
    }
    [MBProgressHUD hideAllHUDsForView:_keyWindow animated:NO];

    MBProgressHUD *hud = [MBProgressHUD showHUDAddedTo:_keyWindow animated:YES];
    hud.mode = MBProgressHUDModeText;
    hud.margin = 10.f;
    hud.labelText = msg;
    hud.removeFromSuperViewOnHide = YES;
    [hud hide:YES afterDelay:delay];
}

/// 进度显示toast
+ (void)showLoading:(NSString *)msg
{
    [self hideHud:NO];
    [ProgressHUD setCustomAnimationType:1];
    [ProgressHUD show:msg interaction:NO];
}

/// 加载中时，需要更新文案（避免闪烁）
+ (void)updateLoadingText:(NSString *)msg
{
    [ProgressHUD updateStatusWithText:msg];
}

/// 隐藏toast
+ (void)hideHud:(BOOL)animated
{
    if(animated) {
        [ProgressHUD dismiss];
    } else {
        [ProgressHUD remove];
    }
}

/// 成功
+ (void)showSucceed:(NSString *)msg
{
    [self hideHud:NO];
    [ProgressHUD showSucceed:msg];
}

/// 失败
+ (void)showFailed:(NSString *)msg
{
    [self hideHud:NO];
    [ProgressHUD showFailed:msg];
}

/// 警告
+ (void)showWarning:(NSString *)msg
{
    [self hideHud:NO];
    [ProgressHUD showWarning:msg];
}


@end
