//
//  UIColor+Helper.h
//  QRCode
//
//  Created by qingbin on 2021/11/7.
//  Copyright © 2021 qingbin. All rights reserved.
//
#import <UIKit/UIKit.h>

@interface UIColor (Helper)

/**
 *  从色值中生成颜色
 *
 *  @param hexString 色值 #909090 或者 0X909090
 *
 *  @return UIColor对象
 */
+ (UIColor *)colorWithHexString:(NSString *)hexString;

+ (UIImage *)imageWithColor:(UIColor *)color size:(CGSize)size;

// 2.6.4 根据给定url，返回一个指定的颜色值
+ (UIColor *)colorWithURL:(NSString *)url;
// 2.6.4 根据给定url，返回一个指定的渐变颜色值
+ (NSArray<NSString *> *)gradientColorWithURL:(NSString *)url;

@end
