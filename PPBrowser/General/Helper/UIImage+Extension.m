//
//  UIImage+Extension.m
//  Saber
//
//  Created by q<PERSON><PERSON> on 2023/2/15.
//

#import "UIImage+Extension.h"

@implementation UIImage (Extension)

+ (UIImage *)ext_systemImageNamed:(NSString *)imageName
                        pointSize:(int)pointSize
{
    return [self ext_systemImageNamed:imageName
                     pointSize:pointSize
                    renderMode:UIImageRenderingModeAutomatic];
}

+ (UIImage *)ext_systemImageNamed:(NSString *)imageName
                        pointSize:(int)pointSize
                       renderMode:(UIImageRenderingMode)renderMode
{
    UIImageSymbolConfiguration* config = [UIImageSymbolConfiguration configurationWithPointSize:pointSize];
    UIImage* image = [UIImage systemImageNamed:imageName withConfiguration:config];
    //UIImageRenderingModeAlwaysTemplate
    image = [image imageWithRenderingMode:renderMode];
    
    return image;
}

@end
