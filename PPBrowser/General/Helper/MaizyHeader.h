//
//  <PERSON><PERSON><PERSON>eader.h
//  <PERSON><PERSON><PERSON><PERSON>
//
//  Created by qing<PERSON> on 2022/1/24.
//

#ifndef <PERSON>eader_h
#define <PERSON><PERSON><PERSON><PERSON>er_h

#define FOCUS_UMENG_TF 1
//油猴脚本的单元测试
#define FOCUS_UNIT_TEST 0

#define kUMengA<PERSON>Key @"62b9067a05844627b5c87a26"
#define sChannelId @"appStore"
//#define kPrivacyLink @"https://dztx2022.github.io/focus_privacy.html"

//App id
#define kAppId @"1631941898"
//永久会员
#define kVipProductId @"com.qingbin.focusbrowser.vipmember"
#define kSharedSecret @"b4e2bc89fcc8421f9016c4cd41cc1e8d"

#define kScreenWidth ([UIScreen mainScreen].bounds.size.width)
#define kScreenHeight ([UIScreen mainScreen].bounds.size.height)

#ifdef DEBUG
#define bCatchCrash NO
#define bLogSwitch YES
#define kSendLog REALTIME
#else
#define bCatchCrash YES
#define bLogSwitch NO
#define kSendLog BATCH
#define NSLog(...) {}
#endif

#if DEBUG
#define __FILENAME__ (strrchr(__FILE__,'/')+1)
#define LOG_ERROR(format, ...) FFLog_(__FILENAME__, __LINE__, __FUNCTION__, @"Error:", format, ##__VA_ARGS__)
#define LOG_WARNING(format, ...) FFLog_(__FILENAME__, __LINE__, __FUNCTION__, @"Warning:", format, ##__VA_ARGS__)
#define LOG_INFO(format, ...) FFLog_(__FILENAME__, __LINE__, __FUNCTION__, @"Info:", format, ##__VA_ARGS__)
#define LOG_DEBUG(format, ...) FFLog_(__FILENAME__, __LINE__, __FUNCTION__, @"Debug:", format, ##__VA_ARGS__)

#define FFLog_(file, line, func, prefix, format, ...) {    \
NSString *aMessage = [NSString stringWithFormat:@"%@ %@",prefix, [NSString stringWithFormat:format, ##__VA_ARGS__, nil]]; \
NSLog(@"%@",aMessage);    \
}
#else
#define LOG_ERROR(format, ...) {}
#define LOG_WARNING(format, ...) {}
#define LOG_INFO(format, ...) {}
#define LOG_DEBUG(format, ...) {}
#endif

#define kWebViewEstimatedProgress @"estimatedProgress"
#define kWebViewURL @"URL"
#define kWebViewTitle @"title"
#define kWebViewCanGoBack @"canGoBack"
#define kWebViewCanGoForward @"canGoForward"
#define kWebViewLoading @"loading"
#define kWebViewHasOnlySecureContent @"hasOnlySecureContent"
#define kWebViewServerTrust @"serverTrust"

#define kDownloadIdentifierV2 @"com.browser.downloadmodule"
#define kDownloadIdentifierV3 @"v3.com.browser.downloadmodule"

//必须是16个字母
#define kPassword @"_qingbin_&*^1024"

#endif /* MaizyHeader_h */
