//
//  BrowserHelper.m
//  PPBrowser
//
//  Created by qingbin on 2023/1/30.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import "BrowserHelper.h"
#import <UIKit/UIKit.h>

@implementation BrowserHelper

+ (BOOL)joinQQGroup
{
    //https://www.cnblogs.com/wangkejia/p/9004412.html  
    //key参考: https://qun.qq.com/join.html
    NSString *urlStr = [NSString stringWithFormat:@"mqqapi://card/show_pslcard?src_type=internal&version=1&uin=%@&key=%@&card_type=group&source=external&jump_from=webapi", @"945578760",@"7116f0524c7c4870ab7ac4af04ff3ce3f8ac7f05e70bdf6d7cb8b0d1f306d367"];
    
    NSURL *url = [NSURL URLWithString:urlStr];
    if([[UIApplication sharedApplication] canOpenURL:url]){
        [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
        return YES;
    }
    
    return NO;
}

// 设备支持的生物识别类型
+ (BiometricsType)bioTypeForDevice
{
    LAContext *context = [LAContext new];
    NSError *error = nil;
    BOOL result = [context canEvaluatePolicy:(LAPolicyDeviceOwnerAuthenticationWithBiometrics) error:&error];
    if (@available(iOS 11.0, *)) {
        // 实测调用了canEvaluatePolicy，无论成功失败，都能获取到biometryType的值
        if (context.biometryType == LABiometryTypeFaceID) {
            return BiometricsTypeFaceID;
        } else if (context.biometryType == LABiometryTypeTouchID) {
            return BiometricsTypeTouchID;
        } else {
            return BiometricsTypeNone;
        }
    } else {
        // iOS 11以下，如果canEvaluatePolicy成功了，就认为支持指纹
        if (result) {
            return BiometricsTypeTouchID;
        } else {
            return BiometricsTypeNone;
        }
    }
}

// 设备支持生物识别的基础上，是否准备好了使用生物识别
+ (BOOL)isBiometricVerifyValid
{
    LAContext *context = [LAContext new];
    NSError *error = nil;
    if ([context canEvaluatePolicy:(LAPolicyDeviceOwnerAuthentication) error:&error]){
        return YES;
    } else {
        return NO;
    }
}

// 触发生物识别 success:生物识别是否成功
+ (void)biometricVerify:(void(^)(BOOL success))completion
{
    LAContext *context = [LAContext new];
    NSError *error = nil;
    
    NSString *reason = NSLocalizedString(@"generalSetting.identity.verification", nil);
    if ([context canEvaluatePolicy:(LAPolicyDeviceOwnerAuthentication) error:&error]){
        [context evaluatePolicy:LAPolicyDeviceOwnerAuthentication localizedReason:reason reply:^(BOOL success, NSError * _Nullable error) {
            dispatch_async(dispatch_get_main_queue(), ^{
                if (success) {
                    completion(YES);
                } else {
                    if (error.code == kLAErrorUserFallback){
                        completion(NO);
                    } else {
                        completion(NO);
                    }
                }
            });
        }];
    } else {
        completion(NO);
    }
}

// 重签名检测
+ (void)checkResignApp
{
    //92R56D3745.com.qingbin.Addons
    if(false == [self _checkCodeSignWithProvisionID:@"92R56D3745"]) {
        //被重签名了,直接退出
        exit(0);
    }
}

// 重签名检测
+ (BOOL)_checkCodeSignWithProvisionID:(NSString *)provisionID
{
    //https://zhuanlan.zhihu.com/p/482822751
    //https://www.jianshu.com/p/7fdd3f4a1261
    //https://juejin.cn/post/6917590228609957902
    BOOL hasResult = NO;
    BOOL result = YES;
    @try {
        // 描述文件路径
        NSString *embeddedPath = [[NSBundle mainBundle] pathForResource:@"embedded" ofType:@"mobileprovision"];
        if ([[NSFileManager defaultManager] fileExistsAtPath:embeddedPath]) {
            // 读取application-identifier
            NSString *embeddedProvisioning = [NSString stringWithContentsOfFile:embeddedPath encoding:NSASCIIStringEncoding error:nil];
            NSArray *embeddedProvisioningLines = [embeddedProvisioning componentsSeparatedByCharactersInSet:[NSCharacterSet newlineCharacterSet]];
            for (int i = 0; i < [embeddedProvisioningLines count]; i++) {
                if ([[embeddedProvisioningLines objectAtIndex:i] rangeOfString:@"application-identifier"].location != NSNotFound) {
                    NSInteger fromPosition = [[embeddedProvisioningLines objectAtIndex:i+1] rangeOfString:@"<string>"].location+8;
                    NSInteger toPosition = [[embeddedProvisioningLines objectAtIndex:i+1] rangeOfString:@"</string>"].location;
                    NSRange range;
                    range.location = fromPosition;
                    range.length = toPosition - fromPosition;
                    NSString *fullIdentifier = [[embeddedProvisioningLines objectAtIndex:i+1] substringWithRange:range];
//                    NSLog(@"%@", fullIdentifier); //92R56D3745.com.qingbin.Addons
                    NSArray *identifierComponents = [fullIdentifier componentsSeparatedByString:@"."];
                    NSString *appIdentifier = [identifierComponents firstObject]; //92R56D3745
                    // 对比签名ID
                    if (![appIdentifier isEqual:provisionID]) {
                        result = NO;
                    } else {
                        result = YES;
                    }
                    hasResult = YES;
                }
            }
        } else {
            // appstore上的包是没有embedded文件的
            result = YES;
            hasResult = YES;
        }
    } @catch (NSException *exception) {
        
    } @finally {
        // 有结果用检测的结果，没结果崩溃了，就当yes
        if (hasResult == NO) {
            result = YES;
        }
    }
    return result;
}

@end
