//
//  UIView+LoadingView.m
//  Reader
//
//  Created by qingbin on 2023/8/25.
//

#import "UIView+LoadingView.h"

#import <objc/runtime.h>
#import "UIColor+Helper.h"
#import "UIView+FrameHelper.h"
#import "UIView+Helper.h"
#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "Masonry.h"

#import "Lottie.h"

#import "ThemeProtocol.h"

static const void* kUIViewLoadingViewKey = "kUIViewLoadingViewKey";

@implementation UIView (LoadingView)

// qingbin
// 显示加载动画
- (void)showLoading
{    
    UIView *loadingView = objc_getAssociatedObject(self, kUIViewLoadingViewKey);
    if (!loadingView) {
        loadingView = [[UIView alloc] init];
        
        BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
        if(isDarkTheme) {
            loadingView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        } else {
            loadingView.backgroundColor = UIColor.whiteColor;
        }
        
        [self addSubview:loadingView];
        
        objc_setAssociatedObject(self, kUIViewLoadingViewKey, loadingView, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
        
        [loadingView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.top.equalTo(self);
            make.width.equalTo(self);
            make.height.equalTo(self);
        }];

        LOTAnimationView *lottieLogo = [LOTAnimationView animationNamed:@"animation_loading1"];
        lottieLogo.loopAnimation = YES;
        lottieLogo.contentMode = UIViewContentModeScaleAspectFill;
        
        [loadingView addSubview:lottieLogo];
        [lottieLogo mas_makeConstraints:^(MASConstraintMaker *make) {
            make.center.mas_offset(0);
            make.width.mas_equalTo(55);
        }];
        [lottieLogo play];
    }
}

// 隐藏加载动画
- (void)hideLoading
{
    __block UIView *loadingView = objc_getAssociatedObject(self, kUIViewLoadingViewKey);
    [UIView animateWithDuration:0.25 animations:^{
        loadingView.alpha = 0;
    } completion:^(BOOL finished) {
        [loadingView removeFromSuperview];
        loadingView = nil;
        
        objc_setAssociatedObject(self, kUIViewLoadingViewKey, nil, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    }];
}

@end
