//
//  DateHelper.h
//  YSBBusiness
//
//  Created by lu luca<PERSON> on 20/12/14.
//  Copyright (c) 2014 lu lucas. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <CoreGraphics/CoreGraphics.h>
#import <UIKit/UIKit.h>

static NSString * const NormalDateFormatter = @"yyyy-MM-dd HH:mm:ss";
static NSString * const CouponDateFormatter = @"yyyy.MM.dd";
static NSString * const MessageOnlyMDHMDateFormatter = @"MM月dd日 HH:mm";
static NSString * const MessageOnlyMDDateFormatter = @"MM/dd";
static NSString * const MessageOnlyHMDateFormatter = @"HH:mm";
static NSString * const MessageDateTimeFormatter = @"MM/dd HH:mm:ss";
static NSString * const VideoDateTimeFormatter = @"yyyy/MM/dd HH:mm";
static NSString * const VideoListDateTimeFormatter = @"yyyy/MM/dd";
static NSString * const CreditpayBillTimeFormatter = @"MM/yyyy";
static NSString * const CreditpayCheckBillTimeFormatter = @"yyyy-MM-dd";

typedef enum : NSUInteger {
    TheSameTypeDay,
    TheSameTypeMonth,
    TheSameTypeYear,
    TheSameTypeNotSame
} TheSameType;

@interface DateHelper : NSObject

// 根据日期和格式，获取日期描述
+ (NSString *)timeWithDate:(NSTimeInterval)timeStamp formatter:(NSString *)formatter;
// 根据日期字符串获取时间戳
+ (NSTimeInterval)timeIntervalFromString:(NSString *)timeString formatter:(NSString *)formatter;

+ (NSString *)yearWithDate:(NSDate *)date;
+ (NSString *)monthWithDate:(NSDate *)date;
+ (NSString *)weekWithDate:(NSString *)date;
+ (NSString *)dayWithDate:(NSDate *)date;
+ (NSString *)getDateYearMonthDayString:(NSDate *)date;

/**
 *  将秒数转换为时：分：秒
 *
 *  @param duration 时长
 *
 *  @return 字符串
 */
+ (NSString *)changeDurationToTime:(CGFloat)duration;



// 日历操作
+ (NSInteger)firstWeekdayInThisMonth:(NSDate *)date;

+ (NSInteger)totaldaysInMonth:(NSDate *)date;

+ (NSDate *)lastMonth:(NSDate *)date;

+ (NSDate*)nextMonth:(NSDate *)date;

// 用于药消息的时间显示
+ (NSString *)getMessageDateString:(NSTimeInterval)timeInterval;

// date是否是今天
+ (BOOL)isToday:(NSDate *)date;
// 是否是昨天
+ (BOOL)isYesterday:(NSDate *)date;
// 获取星期
+ (NSString*)getWeekDayFromDate:(NSDate *)date;

// 获取两个时间戳之间的月份
+ (NSArray*)getMonthsBetween:(NSDate *)min Max:(NSDate *)max formatter:(NSString*)formatter;

// 获取月份对应的第一天
+ (NSString*)getMonthFirstDay:(NSString*)dateString formatter:(NSString*)formatter;

// 获取月份对应的最后一天
+ (NSString*)getMonthLastDay:(NSString*)dateString formatter:(NSString*)formatter;
@end
