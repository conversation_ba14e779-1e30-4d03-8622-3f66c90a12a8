//
//  NSFileManager+Helper.h
//  PPBrowser
//
//  Created by qing<PERSON> on 2022/11/19.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>

@interface NSFileManager (Helper)

// 文件下载目录
+ (NSURL *)downloadsPath;

/// 打开Files - Focus - Downloads目录
+ (void)openFocusDownloadsFolder;

/// https://www.jianshu.com/p/382b3ea37106
/// https://github.com/quoid/userscripts
/// 保存链接
+ (void)saveBookMark:(NSURL *)url;
/// 读取链接
+ (NSURL *)readBookMark;

/// 脚本保存的位置
+ (NSString *)userScriptPath;

/// CloudKit中CKAsset的保存目录
/// Documents/CloudKit
+ (NSURL *)cloudKitPath;

@end

