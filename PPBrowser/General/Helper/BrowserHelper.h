//
//  BrowserHelper.h
//  PPBrowser
//
//  Created by qingbin on 2023/1/30.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <LocalAuthentication/LocalAuthentication.h>

#pragma mark - 生物识别
typedef NS_ENUM(NSUInteger, BiometricsType) {
    BiometricsTypeNone = 0,     //无
    BiometricsTypeTouchID = 1,  //指纹
    BiometricsTypeFaceID = 2,   //FaceID
};

@interface BrowserHelper : NSObject

+ (BOOL)joinQQGroup;

// 指纹或者面容识别

// 设备支持的生物识别类型
+ (BiometricsType)bioTypeForDevice;

// 设备支持生物识别的基础上，是否准备好了使用生物识别
+ (BOOL)isBiometricVerifyValid;

// 触发生物识别 success:生物识别是否成功
+ (void)biometricVerify:(void(^)(BOOL success))completion;

// 重签名检测
+ (void)checkResignApp;

@end

