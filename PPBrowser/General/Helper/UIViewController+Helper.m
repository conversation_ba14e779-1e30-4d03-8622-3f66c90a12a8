//
//  UIViewController+Helper.m
//  <PERSON><PERSON><PERSON><PERSON>
//
//  Created by q<PERSON><PERSON> on 2022/1/25.
//

#import "UIViewController+Helper.h"
#import "UIColor+Helper.h"

#import "BaseNavigationController.h"
#import "BrowserUtils.h"

@implementation UIViewController (Helper)

// v2.6.8,统一处理viewcontroller present操作，以适配iPad
- (void)presentCustomToViewController:(UIViewController *)viewController
{
    if (!viewController) {
        return;
    }

    if([BrowserUtils isiPad]) {
        //iPad
        viewController.modalPresentationStyle = UIModalPresentationFormSheet;
        float scale = 0.9;
        viewController.preferredContentSize = CGSizeMake(kScreenWidth*scale, kScreenHeight*scale);
        
        [self presentViewController:viewController animated:YES completion:nil];
    } else {
        //iPhone
        [self presentViewController:viewController animated:YES completion:nil];
    }
}

@end
