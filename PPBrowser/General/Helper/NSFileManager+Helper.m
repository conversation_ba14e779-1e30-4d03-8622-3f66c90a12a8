//
//  NSFileManager+Helper.m
//  PPBrowser
//
//  Created by qingbin on 2022/11/19.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "NSFileManager+Helper.h"

#import <UIKit/UIKit.h>
#import "ReactiveCocoa.h"

static NSString* kBookMarkKey = @"files_save_paths";

@implementation NSFileManager (Helper)

+ (void)saveBookMark:(NSURL *)url
{
    if(!url) return;
    NSError* error = nil;
    NSData* bookmark = [url bookmarkDataWithOptions:NSURLBookmarkCreationSuitableForBookmarkFile includingResourceValuesForKeys:nil relativeToURL:nil error:&error];
    if(!error) {
        NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
        [defaults setObject:bookmark forKey:kBookMarkKey];
        [defaults synchronize];
    }
}

/// 读取链接
+ (NSURL *)readBookMark
{
    NSURL *url = nil;
    BOOL bookmarkIsStale = false;
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    NSData *data = [defaults objectForKey:kBookMarkKey];
    if(data) {
        url = [NSURL URLByResolvingBookmarkData:data options:0 relativeToURL:nil bookmarkDataIsStale:&bookmarkIsStale error:nil];
        
        if(bookmarkIsStale) {
            [self saveBookMark:url];
        }
    }
    
    return url;
}

+ (NSURL *)getOrCreateFolder:(NSString *)name
          excludeFromBackups:(BOOL)excludeFromBackups
                    location:(NSSearchPathDirectory )location
{
    NSURL* documentsDir = [[NSFileManager defaultManager] URLsForDirectory:location inDomains:NSUserDomainMask].firstObject;
    NSURL* folderDir = [documentsDir URLByAppendingPathComponent:name];
    
    if([[NSFileManager defaultManager] fileExistsAtPath:folderDir.path]) return folderDir;
    
    @try {
        [[NSFileManager defaultManager] createDirectoryAtURL:folderDir withIntermediateDirectories:YES attributes:nil error:nil];
        
        if(excludeFromBackups) {
            [folderDir setResourceValues:@{NSURLIsExcludedFromBackupKey:@(YES)} error:nil];
        }
        
        return folderDir;
    } @catch (NSException *exception) {
    } @finally {
    }
    
    return nil;
}

+ (NSURL *)downloadsPath
{
    [self getOrCreateFolder:@"Downloads" excludeFromBackups:YES location:NSDocumentDirectory];
    
    return [[[NSFileManager defaultManager] URLForDirectory:NSDocumentDirectory inDomain:NSUserDomainMask appropriateForURL:nil create:false error:nil] URLByAppendingPathComponent:@"Downloads"];
}

+ (void)openFocusDownloadsFolder
{
    NSURLComponents* downloadsPathComponents = [NSURLComponents componentsWithURL:[self downloadsPath]
                                                          resolvingAgainstBaseURL:false];
    
    downloadsPathComponents.scheme = @"shareddocuments";
    
    NSURL* url = downloadsPathComponents.URL;
    @try {
        [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:^(BOOL success) {
        }];
    } @catch (NSException *exception) {
    } @finally {
    }
}

/// 脚本保存的位置
+ (NSString *)userScriptPath
{
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
    NSString *directory = [paths objectAtIndex:0];
    NSString* filePath = [directory stringByAppendingPathComponent:@"UserScript"];
    
    NSFileManager* fileManager = [NSFileManager defaultManager];
    BOOL isDirectory;
    if(![fileManager fileExistsAtPath:filePath isDirectory:&isDirectory]) {
        [fileManager createDirectoryAtPath:filePath withIntermediateDirectories:YES attributes:nil error:nil];
    }
    
    [[NSURL fileURLWithPath:filePath] setResourceValues:@{NSURLIsExcludedFromBackupKey:@(YES)} error:nil];
    
//    NSURL* filePath = [self getOrCreateFolder:@"UserScript" excludeFromBackups:YES location:NSDocumentDirectory];
//    NSLog(@"当前用户脚本地址: %@",filePath);
    
    return filePath;
}

/// CloudKit中CKAsset的保存目录
/// Documents/CloudKit
+ (NSURL *)cloudKitPath
{
    return [self getOrCreateFolder:@"CloudKit" excludeFromBackups:YES location:NSDocumentDirectory];
}


@end
