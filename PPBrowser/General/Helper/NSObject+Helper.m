//
//  NSObject+Helper.m
//  QRCode
//
//  Created by qingbin on 2021/11/25.
//  Copyright © 2021 qingbin. All rights reserved.
//

#import "NSObject+Helper.h"
#import "MaizyHeader.h"
#import <MobileCoreServices/MobileCoreServices.h>
#import "PPEnums.h"

#import "PPBrowser-Swift.h"

#import "AFNetworking.h"
#import "AFNetworkReachabilityManager.h"

#import "UIView+Helper.h"

//#import <UniformTypeIdentifiers.h>

@implementation NSObject (Helper)

UIWindow *YBIBNormalWindow(void)
{
    UIWindow *window;
    if (window.windowLevel != UIWindowLevelNormal) {
        NSArray *windows = [[UIApplication sharedApplication] windows];
        for(UIWindow *temp in windows) {
            if (temp.windowLevel == UIWindowLevelNormal) {
                return temp;
            }
        }
    }
    
    if(!window) {
        window = [UIApplication sharedApplication].delegate.window;
    }
    
    if(!window) {
        window = [[UIApplication sharedApplication] keyWindow];
    }
    
    return window;
}

+ (UIWindow*)normalWindow
{
    UIWindow *window;
    if (window.windowLevel != UIWindowLevelNormal) {
        NSArray *windows = [[UIApplication sharedApplication] windows];
        for(UIWindow *temp in windows) {
            if (temp.windowLevel == UIWindowLevelNormal) {
                return temp;
            }
        }
    }
    
    if(!window) {
        window = [UIApplication sharedApplication].delegate.window;
    }
    
    if(!window) {
        window = [[UIApplication sharedApplication] keyWindow];
    }
    
    return window;
}

// 获取可以present的viewcontroller(这里针对的是UINavigationController为基类的情况，UITabViewController需要验证)
+ (UIViewController *)validPresentViewController
{
    UINavigationController* navc = (UINavigationController *)[NSObject normalWindow].rootViewController;
    UIViewController* controller = navc.topViewController;
    
    while (controller.presentedViewController) {
        controller = controller.presentedViewController;
    }
    
    return controller;
}

//https://developer.apple.com/library/archive/documentation/Miscellaneous/Reference/UTIRef/Articles/System-DeclaredUniformTypeIdentifiers.html
//https://www.jianshu.com/p/f10441aa2fa7

+ (void)requestAuthorizationWithAuthorizedBlock:(void(^)(void))authorizedBlock
                                    rejectBlock:(void(^)(void))rejectBlock
{
    if(@available(iOS 14, *)) {
#ifdef __IPHONE_14_0
        [PHPhotoLibrary requestAuthorizationForAccessLevel:PHAccessLevelReadWrite handler:^(PHAuthorizationStatus status) {
            dispatch_async(dispatch_get_main_queue(), ^{
                if (status == PHAuthorizationStatusAuthorized ||
                    status == PHAuthorizationStatusLimited) {
                    if(authorizedBlock) {
                        authorizedBlock();
                    }
                }else {
                    [self showNoAuthorizedAlertView];
                    if (rejectBlock) {
                        rejectBlock();
                    }
                }
            });
        }];
#endif
    } else {
        [PHPhotoLibrary requestAuthorization:^(PHAuthorizationStatus status) {
            dispatch_async(dispatch_get_main_queue(), ^{
                if (status != PHAuthorizationStatusAuthorized) {
                    [self showNoAuthorizedAlertView];
                    if (rejectBlock) {
                        rejectBlock();
                    }
                }else {
                    if(authorizedBlock) {
                        authorizedBlock();
                    }
                }
            });
        }];
    }
}

+ (void)showNoAuthorizedAlertView
{
#warning -- 去除这个提示,被苹果拒绝了
    [UIView showFailed:@"没有权限访问相册"];
//    UIAlertController* alertController = [UIAlertController alertControllerWithTitle:@"无法访问所有照片，请在设置-隐私-相册中允许访问所有照片" message:nil preferredStyle:UIAlertControllerStyleAlert];
//    UIAlertAction* action = [UIAlertAction actionWithTitle:@"我知道了" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
//        [alertController dismissViewControllerAnimated:YES completion:nil];
//        NSURL *url = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
//        if (@available(iOS 10.0, *)) {
//            [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
//        }else {
//            [[UIApplication sharedApplication] openURL:url];
//        }
//    }];
//
//    [alertController addAction:action];
//
//    UIWindow* window = YBIBNormalWindow();
//    UIViewController* top = window.rootViewController;
//    [top presentViewController:alertController animated:YES completion:nil];
}

#pragma mark -- 智能检测是否需要带请求头
+ (void)requestForHeader:(NSDictionary*)requestHeader
                     url:(NSString*)url
              completion:(void(^)(BOOL succ, NSDictionary* header, id data))completion
{
    NSInteger timeout = 10;
    AFHTTPSessionManager *manager = [AFHTTPSessionManager manager];
    [manager.requestSerializer setTimeoutInterval:timeout];
    
    //不验证证书
    //https://www.jianshu.com/p/e1062737d7d2
    AFSecurityPolicy* securityPolicy = [AFSecurityPolicy policyWithPinningMode:AFSSLPinningModeNone];
    [securityPolicy setValidatesDomainName:NO];
    manager.securityPolicy = securityPolicy;
    
    // 创建数据接收计时器
    NSDate *startTime = [NSDate date];
    
    //https://pcc.mmiyue.com/hls/play/1f5ebd5be102f0aa0cac9d74966c4d0e695739b2ef5e8af803808769293bc0ea820cba1e1efa9c5ef48b341a25247319d5e2f47610dd4af210190fe4768050b569ac4d51373d42daef3ef07fe5301f87e69f|1672189926|c712b4886c943479cbc14d86848cc7ca5d8d4d05849c959d|c159d54e8b613b2472f5bbd8782b6123cb1165ce542871c198a8a7381f8ec9ce7cd0ecb23737d440032bef89d9d37f40550e74a1e9378434|780361253|6531.m3u8
    //ikandy.fun, 不规则的URL
    
    /*
     https://fsrkrylchds8.jztyxxjc.com/12-4143.m3u8
     该链接的索引文件必须是GET请求
     */
    //指定响应类型
    manager.responseSerializer = [AFHTTPResponseSerializer serializer];
    NSURLSessionDataTask *dataTask = nil;
    dataTask = [manager GET:url parameters:nil headers:requestHeader
                   progress:^(NSProgress * _Nonnull downloadProgress) {
        // 检查数据接收是否超时
        NSTimeInterval elapsed = [[NSDate date] timeIntervalSinceDate:startTime];
        if (elapsed > timeout) {
            [dataTask cancel];
            if(completion) {
                completion(NO, nil, nil);
            }
        }
    } success:^(NSURLSessionDataTask * _Nonnull task, id  _Nullable responseObject) {
        //responseObject返回NSZeroData,也是空值,例如抖音
        //https://www.anycodings.com/1questions/2566953/how-to-check-if-id-object-is-null-in-ios
        
        // 检查总耗时是否超时
        NSTimeInterval elapsed = [[NSDate date] timeIntervalSinceDate:startTime];
        if (elapsed > timeout) {
            [dataTask cancel];
            if(completion) {
                completion(NO, nil, nil);
            }
            return;
        }
        
        NSData* data = (NSData*)responseObject;
        NSLog(@"data = %@, length = %lu",data, (unsigned long)data.length);
        
        BOOL isValid = YES;
        if(data.length == 0) {
            isValid = NO;
        }
        
        if(isValid) {
            if(responseObject == (id)[NSNull null]) {
                isValid = NO;
            }
        }
        
        if(isValid) {
            if(completion) {
                completion(YES, requestHeader, data);
            }
        } else {
            if(completion) {
                completion(NO, nil, nil);
            }
        }
    } failure:^(NSURLSessionDataTask * _Nullable task, NSError * _Nonnull error) {
        if(completion) {
            completion(NO, nil, nil);
        }
    }];
}

#pragma mark - 阿拉伯语布局适配

+ (BOOL)isRTLLayout
{
    return [UIView userInterfaceLayoutDirectionForSemanticContentAttribute:UISemanticContentAttributeUnspecified] == UIUserInterfaceLayoutDirectionRightToLeft;
}

+ (void)rtlLayoutSupport
{
//    if ([self isRTLLayout]) {
//        // 导航栏返回按钮位置
//        [[UINavigationBar appearance] setSemanticContentAttribute:UISemanticContentAttributeForceLeftToRight];
//        // 其他需要全局固定方向的UI元素
//        [[UITabBar appearance] setSemanticContentAttribute:UISemanticContentAttributeForceLeftToRight];
//    }
}

+ (void)rtlNavigationLayoutSupport:(UINavigationController *)navc
{
//    if ([self isRTLLayout]) {
//        // 阿拉伯语环境 - 强制返回按钮在左侧
//        navc.navigationBar.semanticContentAttribute = UISemanticContentAttributeForceLeftToRight;
//    }
}

+ (void)rtlLayoutSupportWithViews:(NSArray<UIView *> *)views
{
//    if ([self isRTLLayout]) {
//        for (UIView* view in views) {
//            // 特定视图可能需要强制LTR
//            view.semanticContentAttribute = UISemanticContentAttributeForceLeftToRight;
//        }
//    }
}

@end
