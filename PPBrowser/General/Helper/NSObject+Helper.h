//
//  NSObject+Helper.h
//  QRCode
//
//  Created by qingbin on 2021/11/25.
//  Copyright © 2021 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import <sys/utsname.h>

#import "PPEnums.h"
#import <Photos/Photos.h>

@interface NSObject (Helper)

UIWindow *YBIBNormalWindow(void);

+ (UIWindow *)normalWindow;

// 获取可以present的viewcontroller(这里针对的是UINavigationController为基类的情况，UITabViewController需要验证)
+ (UIViewController *)validPresentViewController;

/// 相册权限检测
+ (void)requestAuthorizationWithAuthorizedBlock:(void(^)(void))authorizedBlock
                                    rejectBlock:(void(^)(void))rejectBlock;

#pragma mark -- 智能检测是否需要带请求头
+ (void)requestForHeader:(NSDictionary*)requestHeader
                     url:(NSString*)url
              completion:(void(^)(BOOL succ, NSDictionary* header, id data))completion;

// 2.6.8, 阿拉伯语布局适配
+ (BOOL)isRTLLayout;
+ (void)rtlLayoutSupport;
+ (void)rtlNavigationLayoutSupport:(UINavigationController *)navc;
+ (void)rtlLayoutSupportWithViews:(NSArray<UIView *> *)views;

@end

