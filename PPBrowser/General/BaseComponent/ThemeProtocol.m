//
//  ThemeProtocol.m
//  PPBrowser
//
//  Created by qingbin on 2022/9/4.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "ThemeProtocol.h"
#import "NSObject+Helper.h"

@implementation ThemeProtocol

//更新当前暗黑模式状态
+ (void)updateDarkModeStatus:(DarkModeStatus)status
{
    [PreferenceManager shareInstance].items.darkModeStatus = @(status);
    
    BOOL isDarkTheme;
    UIWindow* window = YBIBNormalWindow();
    if(status == DarkModeStatusAuto) {
        //跟随系统
        if(window.traitCollection.userInterfaceStyle == UIUserInterfaceStyleDark) {
            isDarkTheme = YES;
        } else {
            isDarkTheme = NO;
        }
    } else {
        //不跟随系统
        isDarkTheme = status==DarkModeStatusDark;
    }
    
    [PreferenceManager shareInstance].items.isDarkTheme = @(isDarkTheme);
    [[PreferenceManager shareInstance] encode];
    
    [self updateSystemUserInterfaceStyle];
}

//获取当前暗黑模式状态
+ (DarkModeStatus)getDarkModeStatus
{
    DarkModeStatus status = [[PreferenceManager shareInstance].items.darkModeStatus intValue];
    
    return status;
}

//强制点击设置暗黑模式(首页设置中调用)
+ (void)toggleDarkThemeCustom
{
    DarkModeStatus status = [[PreferenceManager shareInstance].items.darkModeStatus intValue];
    BOOL isFollowSystem = status==DarkModeStatusAuto;
    BOOL isDarkTheme;
    UIWindow* window = YBIBNormalWindow();
    
    if(isFollowSystem) {
        //如果之前是跟随系统,获取之前的模式
        if(window.traitCollection.userInterfaceStyle == UIUserInterfaceStyleDark) {
            isDarkTheme = YES;
        } else {
            isDarkTheme = NO;
        }
    } else {
        //如果之前是不跟随系统,获取之前的模式
        isDarkTheme = [[PreferenceManager shareInstance].items.isDarkTheme boolValue];
    }
    
    isDarkTheme = !isDarkTheme;
    
    //解除跟随系统
    if(isDarkTheme) {
        [PreferenceManager shareInstance].items.darkModeStatus = @(DarkModeStatusDark);
    } else {
        [PreferenceManager shareInstance].items.darkModeStatus = @(DarkModeStatusLight);
    }
    
    //设置系统的暗黑模式
    if(isDarkTheme) {
        [window setOverrideUserInterfaceStyle:UIUserInterfaceStyleDark];
    } else {
        [window setOverrideUserInterfaceStyle:UIUserInterfaceStyleLight];
    }
    
    [PreferenceManager shareInstance].items.isDarkTheme = @(isDarkTheme);
    [[PreferenceManager shareInstance] encode];
}

//不管什么状态,获取当前是否是暗黑模式
+ (BOOL)isDarkTheme
{
    DarkModeStatus status = [[PreferenceManager shareInstance].items.darkModeStatus intValue];
    BOOL isDarkTheme;
    UIWindow* window = YBIBNormalWindow();
    if(!window) {
        window = [UIApplication sharedApplication].delegate.window;
    }

    if(status == DarkModeStatusAuto) {
        //跟随系统
        if(window.traitCollection.userInterfaceStyle == UIUserInterfaceStyleDark) {
            isDarkTheme = YES;
        } else {
            isDarkTheme = NO;
        }
    } else {
        //不跟随系统
        isDarkTheme = [[PreferenceManager shareInstance].items.isDarkTheme boolValue];
    }

    return isDarkTheme;
}

//更新系统的模式
+ (void)updateSystemUserInterfaceStyle
{
    DarkModeStatus status = [[PreferenceManager shareInstance].items.darkModeStatus intValue];
    UIWindow* window = YBIBNormalWindow();
    if(!window) {
        window = [UIApplication sharedApplication].delegate.window;
    }
    
    if(status == DarkModeStatusAuto) {
        //跟随系统
        [window setOverrideUserInterfaceStyle:UIUserInterfaceStyleUnspecified];
    } else {
        //不跟随系统
        if(status == DarkModeStatusDark) {
            [window setOverrideUserInterfaceStyle:UIUserInterfaceStyleDark];
        } else {
            [window setOverrideUserInterfaceStyle:UIUserInterfaceStyleLight];
        }
    }
}

@end
