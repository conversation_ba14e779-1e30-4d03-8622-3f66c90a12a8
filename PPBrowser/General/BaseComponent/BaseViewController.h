//
//  BaseViewController.h
//  MaizyClock
//
//  Created by qing<PERSON> on 2022/1/25.
//

#import <UIKit/UIKit.h>

#import "ThemeProtocol.h"
#import "PreferenceManager.h"
#import "UIViewController+Helper.h"

typedef enum : NSUInteger {
    BaseNavigationBarStyleUnknown = 0,// 未知，默认值
    BaseNavigationBarStyleDefault,// 白底 黑字 黑按钮，没有分割线
    BaseNavigationBarStyleNoneWithLightContent,// 隐藏导航栏, 状态栏白色文字
    BaseNavigationBarStyleNoneWithDefaultContent,// 隐藏导航栏, 状态栏黑色文字
} BaseNavigationBarStyle;

@interface BaseViewController : UIViewController<ThemeProtocol>

/// 左上角返回按钮交给BaseViewController处理
@property (nonatomic, strong) UIButton* leftButton;

// 导航栏样式
- (BaseNavigationBarStyle)preferredNavigationBarStyle;

// 设置导航栏样式
- (void)configNavigationBarStype;

- (void)applyTheme;

// 暗黑模式(跟随系统相关逻辑处理)
- (void)themeDidChangeHandler:(BOOL)needReload;

//非最前面的controller/view收到暗黑模式自动切换的通知
- (void)darkThemeDidChangeNotification:(NSNotification *)notification;

// 设置左上角返回按钮
- (void)createCustomLeftBarButtonItem;

- (void)leftBarbuttonClick;

@end


