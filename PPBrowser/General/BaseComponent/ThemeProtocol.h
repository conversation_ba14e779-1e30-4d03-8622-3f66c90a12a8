//
//  ThemeProtocol.h
//  PPBrowser
//
//  Created by qingbin on 2022/8/22.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "PreferenceManager.h"

#define kDarkThemeColor222 @"#222222"
#define kDarkThemeColor030 @"#303030"
#define kDarkThemeColorText @"#ffffff"
#define kDarkThemeColorLine @"#555555"
#define kDarkThemeColorTextFiledBackgroundColor @"#404145"
#define kDarkThemeColorTextFiledContent @"#838488"

/// 暗黑模式
@protocol ThemeProtocol <NSObject>

- (void)applyTheme;

@end

//参考 https://developer.aliyun.com/article/932479
@interface ThemeProtocol : NSObject

//更新当前暗黑模式状态
+ (void)updateDarkModeStatus:(DarkModeStatus)status;
//获取当前暗黑模式状态
+ (DarkModeStatus)getDarkModeStatus;
//不管什么状态,获取当前是否是暗黑模式
+ (BOOL)isDarkTheme;

//强制点击设置暗黑模式(首页设置中调用)
+ (void)toggleDarkThemeCustom;
//更新系统的模式
+ (void)updateSystemUserInterfaceStyle;

@end
