//
//  PopupBottomViewDefaultHandler.m
//  YSBPro
//
//  Created by qingbin on 2021/11/11.
//  Copyright © 2021 lu lucas. All rights reserved.
//
#import "PopupBottomViewDefaultHandler.h"
#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "UIView+FrameHelper.h"
#import "NSObject+Helper.h"

@interface PopupBottomViewDefaultHandler ()

@end

@implementation PopupBottomViewDefaultHandler

@synthesize yb_height = _yb_height;
@synthesize yb_cornerRadius = _yb_cornerRadius;
@synthesize yb_contentView = _yb_contentView;
@synthesize yb_updateBlock = _yb_updateBlock;
@synthesize yb_supportTapHide = _yb_supportTapHide;
@synthesize yb_supportKeyboardEvent = _yb_supportKeyboardEvent;
@synthesize yb_willClose = _yb_willClose;
@synthesize yb_didClose = _yb_didClose;
@synthesize yb_manualClose = _yb_manualClose;
@synthesize yb_showAtWindow = _yb_showAtWindow;
@synthesize yb_isInitial = _yb_isInitial;
@synthesize yb_updateContainerColor = _yb_updateContainerColor;

- (instancetype)init
{
    self = [super init];
    if(self) {
        self.yb_supportTapHide = YES;
        self.yb_cornerRadius = 5;
        self.yb_height = kScreenHeight*0.6;
        self.yb_showAtWindow = YES;
        self.yb_isInitial = NO;
    }
    
    return self;
}

@end
