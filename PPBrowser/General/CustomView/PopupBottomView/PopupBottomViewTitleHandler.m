//
//  PopupBottomViewTitleHandler.m
//  YSBPro
//
//  Created by qingbin on 2021/11/11.
//  Copyright © 2021 lu lucas. All rights reserved.
//

#import "PopupBottomViewTitleHandler.h"
#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "UIView+FrameHelper.h"
#import "NSObject+Helper.h"

#import <UIKit/UIKit.h>

@interface PopupBottomViewTitleHandler()
@property(nonatomic,strong) UIView* titleView;
@property(nonatomic,strong) UILabel* titleLabel;
@property(nonatomic,strong) UIButton* closeBtn;
@end

@implementation PopupBottomViewTitleHandler

@synthesize yb_title = _yb_title;
@synthesize yb_titleFont = _yb_titleFont;

- (void)beforeUpdateUI
{
    [self.yb_contentView addSubview:self.titleView];
    [self.titleView addSubview:self.titleLabel];
    [self.titleView addSubview:self.closeBtn];
    
    [self.titleView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.yb_contentView);
        make.height.mas_equalTo(50).priorityHigh();
        make.top.equalTo(self.yb_contentView);
    }];

    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.titleView);
    }];

    [self.closeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.titleView);
        make.right.mas_offset(-5);
        make.size.mas_equalTo(CGSizeMake(44, 44));
    }];
    
    @weakify(self)
    [[self.closeBtn rac_signalForControlEvents:UIControlEventTouchUpInside]
    subscribeNext:^(id x) {
        @strongify(self)
        if(self.yb_manualClose) {
            self.yb_manualClose();
        }
    }];
}

- (void)setYb_title:(NSString *)yb_title
{
    _yb_title = yb_title;
    self.titleLabel.text = yb_title;
}

- (void)setYb_titleFont:(UIFont *)yb_titleFont
{
    _yb_titleFont = yb_titleFont;
    self.titleLabel.font = yb_titleFont;
}

- (UIView *)titleView
{
    if (!_titleView)
    {
        _titleView = [UIView new];
        _titleView.backgroundColor = [UIColor whiteColor];
    }
    return _titleView;
}

- (UILabel *)titleLabel
{
    if (!_titleLabel)
    {
        _titleLabel = [UIView createLabelWithTitle:@""
                                             textColor:[UIColor colorWithHexString:@"#333333"]
                                               bgColor:[UIColor clearColor]
                                              fontSize:20.0f
                                         textAlignment:NSTextAlignmentCenter
                                                 bBold:NO];
    }
    return _titleLabel;
}

- (UIButton *)closeBtn
{
    if (!_closeBtn)
    {
        _closeBtn = [UIView createButtonWithImage:[UIImage imageNamed:@"dl_close"]
                                                 click:nil];
    }
    return _closeBtn;
}

@end
