//
//  PopupBottomView.h
//  YSBPro
//
//  Created by qing<PERSON> on 2021/11/11.
//  Copyright © 2021 lu lucas. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "PopupBottomViewDefaultHandler.h"

/**
 采用适配器模式，PopupBottomView作为适配器的调用者Adapter；
 而CustomPopupDefaultHandler和CustomPopupTitleHandler都是作为各自实现的Adaptee;
 CustomPopupDefaultHandler表示头部没有标题+关闭按钮的空白样式；
 CustomPopupTitleHandler表示头部有标题+关闭按钮的样式；
 如果需要其它样式，那么需要实现PopupBottomViewProtocol协议，协议表示拥有的功能。
 */
@interface PopupBottomView : UIView
- (void)showWithHanlder:(id<PopupBottomViewProtocol>)handler;
- (void)hide;
@end
