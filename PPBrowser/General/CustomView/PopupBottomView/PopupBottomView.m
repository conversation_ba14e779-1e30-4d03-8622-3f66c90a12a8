//
//  PopupBottomView.m
//  YSBPro
//
//  Created by qing<PERSON> on 2021/11/11.
//  Copyright © 2021 lu lucas. All rights reserved.
//

#import "PopupBottomView.h"
#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "UIView+FrameHelper.h"
#import "NSObject+Helper.h"
#import "UIScrollView+TPKeyboardAvoidingAdditions.h"

@interface PopupBottomView()
@property(nonatomic,strong) UIScrollView* scrollview;
@property(nonatomic,strong) UIView* contentView;

@property(nonatomic,strong) UIView* customMaskView;
@property(nonatomic,strong) UIView* containerView;
//白色背景图
@property(nonatomic,strong) UIView* whiteBackgroupView;

@property(nonatomic,strong) id<PopupBottomViewProtocol> handler;
@end

@implementation PopupBottomView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if(self)
    {
        self.layer.opacity = 0;
        self.customMaskView.layer.opacity = 0;
        
        [self addSubiews];
        [self defineLayout];
    }

    return self;
}

- (void)addSubiews
{
    [self addSubview:self.whiteBackgroupView];
    [self addSubview:self.containerView];
    [self addSubview:self.customMaskView];

    [self addSubview:self.scrollview];
    [self.scrollview addSubview:self.contentView];
}

- (void)defineLayout
{
    float bottomOffset = 0;
    if(@available(iOS 11.0, *)) {
        bottomOffset = YBIBNormalWindow().safeAreaInsets.bottom;
    }
    [self.whiteBackgroupView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.bottom.equalTo(self);
        make.height.mas_equalTo(bottomOffset);
    }];
    
    [self.containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self);
        make.height.mas_equalTo([self getYbHeight]).priorityHigh();
    }];

    [self.customMaskView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.top.equalTo(self);
        make.bottom.equalTo(self.containerView.mas_top).priorityHigh();
    }];

    [self.scrollview mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.containerView);
    }];

    [self.contentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.scrollview);
        make.width.equalTo(self);
        make.bottom.equalTo(self.containerView);
    }];
}

- (void)setupObservers
{
    BOOL yb_supportTapHide = NO;
    if([self.handler respondsToSelector:@selector(yb_supportTapHide)]) {
        yb_supportTapHide = self.handler.yb_supportTapHide;
    }
    
    @weakify(self)
    if(yb_supportTapHide) {
        UITapGestureRecognizer* tap = [[UITapGestureRecognizer alloc]init];
        [self.customMaskView addGestureRecognizer:tap];
        [tap.rac_gestureSignal subscribeNext:^(id x) {
            @strongify(self)
            [self hide];
        }];
    }
    
    BOOL yb_supportKeyboardEvent = NO;
    if([self.handler respondsToSelector:@selector(yb_supportKeyboardEvent)]) {
        yb_supportKeyboardEvent = self.handler.yb_supportKeyboardEvent;
    }
    
    if(yb_supportKeyboardEvent) {
        [[NSNotificationCenter defaultCenter] addObserver:self
                                                 selector:@selector(_keyboardWillShown:)
                                                     name:UIKeyboardWillShowNotification
                                                   object:nil];

        [[NSNotificationCenter defaultCenter] addObserver:self
                                                 selector:@selector(_keyboardWillHidden:)
                                                     name:UIKeyboardWillHideNotification
                                                   object:nil];
    }
}

- (void)showWithHanlder:(id<PopupBottomViewProtocol>)handler
{
    self.handler = handler;
    
    BOOL yb_isInitial = NO;
    if([handler respondsToSelector:@selector(yb_isInitial)]) {
        yb_isInitial = handler.yb_isInitial;
    }
    
    if(!yb_isInitial) {
        //将是否已经初始化的标志放到弹窗内部中,不在业务代码中暴露细节
        handler.yb_isInitial = YES;
        
        if([handler respondsToSelector:@selector(setYb_contentView:)]) {
            handler.yb_contentView = self.contentView;
        }
        
        [self setupObservers];
        
        if([handler respondsToSelector:@selector(beforeUpdateUI)]) {
            [handler beforeUpdateUI];
        }
        
        if([handler respondsToSelector:@selector(setYb_updateContainerColor:)]) {
            @weakify(self)
            handler.yb_updateContainerColor = ^(UIColor *color) {
                @strongify(self)
//                self.containerView.backgroundColor = color;
                self.scrollview.backgroundColor = color;
                self.contentView.backgroundColor = color;
            };
        }
        
        if([handler respondsToSelector:@selector(yb_updateBlock)] && handler.yb_updateBlock){
            if(handler.yb_updateBlock) {
                handler.yb_updateBlock();
            }
        }
        
        if([handler respondsToSelector:@selector(afterUpdateUI)]) {
            [handler afterUpdateUI];
        }
        
        if([handler respondsToSelector:@selector(yb_supportTapHide)]) {
            if(handler.yb_cornerRadius > 0) {
                [self setTopRoundingCorners:handler.yb_cornerRadius];
            }
        }
        
        if([handler respondsToSelector:@selector(setYb_manualClose:)]) {
            @weakify(self)
            handler.yb_manualClose = ^{
                @strongify(self)
                [self hide];
            };
        }
    }
    
    [self popup];
}

- (void)hide
{    
    if([self.handler respondsToSelector:@selector(yb_willClose)] && self.handler.yb_willClose) {
        self.handler.yb_willClose();
    }
    
    float height = [self getYbHeight];
    
    [self endEditing:YES];
    
    @weakify(self)
    [UIView animateWithDuration:0.25f animations:^{
        @strongify(self)
        
        self.layer.opacity = 0;
        self.customMaskView.layer.opacity = 0;
        
        [self.containerView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(self);
            make.height.mas_equalTo(height);
            if (@available(iOS 11.0, *)){
                make.bottom.equalTo(self.mas_safeAreaLayoutGuideBottom).offset(height);
            }
            else
            {
                make.bottom.equalTo(self);
            }
        }];
        [self layoutIfNeeded];
    } completion:^(BOOL finished) {
        @strongify(self)
        self.hidden = YES;
        [self removeFromSuperview];
        
        if([self.handler respondsToSelector:@selector(yb_didClose)] && self.handler.yb_didClose) {
            self.handler.yb_didClose();
        }
    }];
}

- (void)popup
{
    float height = [self getYbHeight];
    
    BOOL showAtWindow = YES;
    if([self.handler respondsToSelector:@selector(yb_showAtWindow)]) {
        showAtWindow = self.handler.yb_showAtWindow;
    }
    
    UIView* view = nil;
    if(showAtWindow) {
        view = YBIBNormalWindow();
    } else {
        UINavigationController* navigationController = (UINavigationController*)YBIBNormalWindow().rootViewController;
        view = navigationController.topViewController.view;
    }
    
    [view addSubview:self];
    [view bringSubviewToFront:self];
    
    [self mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(view);
    }];
    
    self.hidden = NO;
    [self.containerView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self);
        make.height.mas_equalTo(height);
        if (@available(iOS 11.0, *)){
            make.bottom.equalTo(self.mas_safeAreaLayoutGuideBottom).offset(height);
        }
        else
        {
            make.bottom.equalTo(self).offset(height);
        }
    }];

    [self layoutIfNeeded];
    self.backgroundColor = [UIColor.blackColor colorWithAlphaComponent:0.6];

    @weakify(self)
    [UIView animateWithDuration:0.25 animations:^{
        @strongify(self)
        
        self.layer.opacity = 1;
        self.customMaskView.layer.opacity = 1;
        
        [self.containerView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(self);
            make.height.mas_equalTo(height);
            if (@available(iOS 11.0, *)){
                make.bottom.equalTo(self.mas_safeAreaLayoutGuideBottom);
            }
            else
            {
                make.bottom.equalTo(self);
            }
        }];
        [self layoutIfNeeded];
    }];
}

- (void)setTopRoundingCorners:(float)radius
{
    float height = [self getYbHeight];
    
    UIWindow *keyWindow = [NSObject normalWindow];
    CGFloat bottomOffset = 0;
    if (@available(iOS 11, *)) {
        bottomOffset = keyWindow.safeAreaInsets.bottom;
    }
    
    self.scrollview.layer.mask = [self createMaskWith:CGRectMake(0, 0, kScreenWidth, height) radius:radius];
}

- (CAShapeLayer*)createMaskWith:(CGRect)boundRect radius:(float)radius
{
    UIBezierPath *path = [UIBezierPath bezierPathWithRoundedRect:boundRect
                                               byRoundingCorners:UIRectCornerTopLeft|UIRectCornerTopRight
                                                     cornerRadii:CGSizeMake(radius,radius)];
    CAShapeLayer *mask = [CAShapeLayer layer];
    mask.path = path.CGPath;
    
    return mask;
}

#pragma mark -- 键盘弹起事件
- (void)_keyboardWillShown:(NSNotification *)notification
{
    NSDictionary *info = [notification userInfo];
    CGRect keyboardRect = [[info objectForKey:UIKeyboardFrameEndUserInfoKey] CGRectValue];
    if (CGRectIsEmpty(keyboardRect)) {
        return;
    }
    
    float height = [self getYbHeight];
    
    /*
     自适应键盘高度的逻辑:
     1、找到触发键盘的输入框对象A
     2、将输入框A的frame转换到window坐标系，然后和键盘的frame进行比较
     3、通过比较Y值, 如果输入框A没有被键盘遮挡, 那么无需偏移
     4、如果输入框A被键盘遮挡, 那么计算出被遮挡的高度offset, 然后偏移offset即可;
     */
    float offset = 0;
    //获取触发键盘的对象firstResponder
    UIView *firstResponder = [self.scrollview TPKeyboardAvoiding_findFirstResponderBeneathView:self.scrollview];
    if(firstResponder) {
        //如果添加了inputAccessoryView,那么需要把inputAccessoryView当成键盘的一部分,相应处理y和height;
        if ([firstResponder conformsToProtocol:@protocol(UITextInput)]) {
            if(firstResponder.inputAccessoryView) {
                CGRect frame = keyboardRect;
                frame.origin.y -= firstResponder.inputAccessoryView.frame.size.height;
                frame.size.height += firstResponder.inputAccessoryView.frame.size.height;
                keyboardRect = frame;
            }
        }
        
        UIWindow* window = [NSObject normalWindow];
        CGRect frame = [firstResponder convertRect:firstResponder.bounds toView:window];
        
        //判断键盘有没有遮挡输入框, 10是添加到键盘上的, 相当于留10的缓冲, 防止键盘刚好贴边输入框底部
        float bufferHeight = 10;
        if(CGRectGetMaxY(frame) + bufferHeight <= CGRectGetMinY(keyboardRect)) {
            offset = 0;
        } else {
            offset = MAX(CGRectGetMaxY(frame) - CGRectGetMinY(keyboardRect), 0) + bufferHeight;
        }
        
        //最大值判断存疑
//        offset = MIN(CGRectGetHeight(window.frame)-CGRectGetHeight(self.backView.frame), offset);
        offset = MAX(0, offset); //防止offset溢出, 最小值判断
    }
    
    if(offset > 0) {
        // iOS14 bug:点击TextFiled或者TextView后，再点击UIScrollView，UIKeyboardWillShowNotification的回调会被触发
        // 如果offset = 0, 那么则不需要重新修改约束, 表示位置是不会被键盘遮挡的
        [UIView beginAnimations:nil context:NULL];
        [UIView setAnimationCurve:[[[notification userInfo] objectForKey:UIKeyboardAnimationCurveUserInfoKey] intValue]];
        [UIView setAnimationDuration:[[[notification userInfo] objectForKey:UIKeyboardAnimationDurationUserInfoKey] floatValue]];
        
        [self.containerView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(self);
            make.height.mas_equalTo(height);
            if (@available(iOS 11.0, *)){
                make.bottom.equalTo(self.mas_safeAreaLayoutGuideBottom).offset(-offset);
            }
            else
            {
                make.bottom.equalTo(self).offset(-offset);
            }
        }];
        
        [self layoutIfNeeded];
        [UIView commitAnimations];
    }
}

#pragma mark -- 键盘收起事件
- (void)_keyboardWillHidden:(NSNotification *)notification
{
    float height = [self getYbHeight];
    
    [UIView beginAnimations:nil context:NULL];
    [UIView setAnimationCurve:[[[notification userInfo] objectForKey:UIKeyboardAnimationCurveUserInfoKey] intValue]];
    [UIView setAnimationDuration:[[[notification userInfo] objectForKey:UIKeyboardAnimationDurationUserInfoKey] floatValue]];
    
    [self.containerView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self);
        make.height.mas_equalTo(height);
        if (@available(iOS 11.0, *)){
            make.bottom.equalTo(self.mas_safeAreaLayoutGuideBottom);
        }
        else
        {
            make.bottom.equalTo(self);
        }
    }];
    
    [self layoutIfNeeded];
    [UIView commitAnimations];
}

- (float)getYbHeight
{
    float height = kScreenHeight*0.6;
    if([self.handler respondsToSelector:@selector(yb_height)]) {
        height = self.handler.yb_height;
    }
    
    return height;
}

- (UIScrollView *)scrollview
{
    if(!_scrollview)
    {
        _scrollview = [[UIScrollView alloc]init];
        _scrollview.showsVerticalScrollIndicator = NO;
        _scrollview.backgroundColor = [UIColor colorWithHexString:@"ffffff"];
    }
    return _scrollview;
}

- (UIView *)contentView
{
    if(!_contentView)
    {
        _contentView = [[UIView alloc]init];
        _contentView.backgroundColor = [UIColor colorWithHexString:@"f5f6f5"];
    }
    return _contentView;
}

- (UIView *)containerView
{
    if(!_containerView)
    {
        _containerView = [UIView new];
        _containerView.backgroundColor = UIColor.clearColor;
    }
    return _containerView;
}

- (UIView *)customMaskView
{
    if(!_customMaskView)
    {
        _customMaskView = [[UIView alloc]init];
        _customMaskView.backgroundColor = UIColor.clearColor;
    }
    return _customMaskView;
}

- (UIView *)whiteBackgroupView
{
    if(!_whiteBackgroupView)
    {
        _whiteBackgroupView = [[UIView alloc]init];
        _whiteBackgroupView.backgroundColor = UIColor.whiteColor;
    }

    return _whiteBackgroupView;
}

@end
