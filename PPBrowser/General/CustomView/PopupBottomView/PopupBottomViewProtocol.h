//
//  PopupBottomViewProtocol.h
//  YSBPro
//
//  Created by qingbin on 2021/11/11.
//  Copyright © 2021 lu lucas. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

@protocol PopupBottomViewProtocol <NSObject>
@required
/// 弹窗内容的高度
@property(nonatomic,assign) int yb_height;

@optional
/// 弹窗的圆角
@property(nonatomic,assign) int yb_cornerRadius;
/// 容器视图 (可在上面添加子视图)
@property (nonatomic, weak) UIView *yb_contentView;
/// 更新UI的执行block
@property (nonatomic, copy) void(^yb_updateBlock)(void);
/// 手动关闭弹窗
@property (nonatomic, copy) void(^yb_manualClose)(void);
/// 即将关闭弹窗
@property (nonatomic, copy) void(^yb_willClose)(void);
/// 已经关闭弹窗
@property (nonatomic, copy) void(^yb_didClose)(void);
/// 更新contentView的背景色
@property (nonatomic, copy) void(^yb_updateContainerColor)(UIColor* color);
/// 是否支持点击mask收起弹窗
@property (nonatomic,assign) BOOL yb_supportTapHide;
/// 是否支持监听键盘事件
@property (nonatomic,assign) BOOL yb_supportKeyboardEvent;
/// 标题
@property (nonatomic,strong) NSString* yb_title;
/// 标题字体
@property (nonatomic,strong) UIFont* yb_titleFont;
/// YES:显示在Window上。NO:显示在当前controller上
@property (nonatomic,assign) BOOL yb_showAtWindow;
/// 标志是否已经初始化，适用于关闭弹窗后，再次打开保持上下文的场合
@property (nonatomic,assign) BOOL yb_isInitial;
/// 自定义扩展UI，执行updateBlock前
- (void)beforeUpdateUI;
/// 自定义扩展UI，执行updateBlock后
- (void)afterUpdateUI;

@end

