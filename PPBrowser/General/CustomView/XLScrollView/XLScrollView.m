//
//  XLScrollView.m
//  MaizyClock
//
//  Created by q<PERSON><PERSON> on 2022/1/26.
//

#import "XLScrollView.h"

@implementation XLScrollView

- (nullable UIView *)hitTest:(CGPoint)point withEvent:(nullable UIEvent *)event {
    //重写父类方法，如果事件响应是直接作用在底层scrollView上的话，不响应此事件
    //这样做是为了，只有滑动上层子视图tableView才会正常滑动，否则就不响应，事件就会往下面传递，不会造成遮挡视图事件。
    UIView* hitView = [super hitTest:point withEvent:event];
    if(hitView == self) {
        return nil;
    }
    
    return hitView;
}

@end
