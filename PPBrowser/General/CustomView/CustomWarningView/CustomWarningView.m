//
//  CustomWarningView.m
//  PPBrowser
//
//  Created by qingbin on 2024/11/9.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import "CustomWarningView.h"

#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "UIView+FrameHelper.h"
#import "NSObject+Helper.h"

#import "BrowserUtils.h"
#import "NSString+Helper.h"

@interface CustomWarningView ()

@property (nonatomic, strong) UIView *backView;
@property (nonatomic, strong) UIImageView *triangleView;
@property (nonatomic, strong) UILabel *label;

@end

@implementation CustomWarningView

- (instancetype)init
{
    self = [super init];
    if (self) {
        [self addSubviews];
        [self defineLayout];
        
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] init];
        @weakify(self)
        [tap.rac_gestureSignal subscribeNext:^(id x) {
            @strongify(self)
            if (self.clickAction) {
                self.clickAction();
            }
        }];
        [self.triangleView addGestureRecognizer:tap];
    }
    return self;
}

- (void)addSubviews
{
    [self addSubview:self.backView];
    [self.backView addSubview:self.triangleView];
    [self.backView addSubview:self.label];
}

- (void)defineLayout
{
    [self.backView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
    [self.triangleView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.backView).offset(71.0f);
        make.top.equalTo(self.backView).offset(38.0f);
        make.width.equalTo(@167.0f);
        make.height.equalTo(@38.0f);
    }];
    [self.label mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.triangleView);
        make.bottom.equalTo(self.triangleView).offset(-7.0f);
    }];
}

- (UIView *)backView
{
    if (!_backView) {
        _backView = [[UIView alloc] init];
    }
    return _backView;
}

- (UIImageView *)triangleView
{
    if (!_triangleView) {
        UIImage *image = [[UIImage imageNamed:@"custom_warning_triangle"] stretchableImageWithLeftCapWidth:56 topCapHeight:0];
        _triangleView = [[UIImageView alloc] initWithImage:image];
        _triangleView.userInteractionEnabled = YES;
    }
    return _triangleView;
}

- (UILabel *)label
{
    if (!_label) {
        _label = [UIView createLabelWithTitle:@"" textColor:UIColor.whiteColor bgColor:UIColor.clearColor fontSize:15 textAlignment:NSTextAlignmentCenter bBold:YES];
    }
    return _label;
}

- (void)showAt:(UIView*)view
   warningText:(NSString *)warningText
{
    self.label.text = warningText;
    
    [self removeFromSuperview];
    [view addSubview:self];
    
    [self startAnimate:self.backView];
}

- (void)hide
{
    if (self.superview) {
        [self removeFromSuperview];
    }
}

- (void)startAnimate:(UIView *)view
{
    // 先收起
    view.transform = CGAffineTransformMakeScale(0.0, 0.0); // 缩小到原来的0.0倍
    view.alpha = 0;
    // 创建展开动画
    [UIView animateWithDuration:0.68 delay:0.0 options:UIViewAnimationOptionCurveEaseOut | UIViewAnimationOptionAllowUserInteraction animations:^{
        view.transform = CGAffineTransformMakeScale(1, 1);
        view.alpha = 0.99;
    } completion:^(BOOL finished) {
        // 执行delay动画，注：之所以这么写，是因为尝试下来，用delay参数的时候，点击不响应
        [UIView animateWithDuration:0.16 delay:0.0 options:UIViewAnimationOptionAllowUserInteraction animations:^{
            view.alpha = 1;
        } completion:^(BOOL finished) {
            // 执行旋转动画
            [UIView animateWithDuration:0.2 delay:0 options:UIViewAnimationOptionBeginFromCurrentState | UIViewAnimationOptionAllowUserInteraction animations:^{
                view.transform = CGAffineTransformMakeRotation(-4 * M_PI / 180.0);
            } completion:^(BOOL finished) {
                // 执行旋转动画
                [UIView animateWithDuration:0.4 delay:0.0 options:UIViewAnimationOptionBeginFromCurrentState | UIViewAnimationOptionAllowUserInteraction animations:^{
                    view.transform = CGAffineTransformMakeRotation(5 * M_PI / 180.0);
                } completion:^(BOOL finished) {
                    // 执行旋转动画
                    [UIView animateWithDuration:0.2 delay:0.0 options:UIViewAnimationOptionBeginFromCurrentState | UIViewAnimationOptionAllowUserInteraction animations:^{
                        view.transform = CGAffineTransformMakeRotation(-2 * M_PI / 180.0);
                    } completion:^(BOOL finished) {
                        // 执行旋转动画
                        [UIView animateWithDuration:0.12 delay:0.0 options:UIViewAnimationOptionBeginFromCurrentState | UIViewAnimationOptionAllowUserInteraction animations:^{
                            view.transform = CGAffineTransformMakeRotation(0);
                        } completion:^(BOOL finished) {
                            // 执行delay动画，注：之所以这么写，是因为尝试下来，用delay参数的时候，点击不响应
                            [UIView animateWithDuration:4 delay:0.0 options:UIViewAnimationOptionAllowUserInteraction animations:^{
                                view.alpha = 0.99;
                            } completion:^(BOOL finished) {
                                // 执行收缩动画
                                @weakify(self)
                                [UIView animateWithDuration:0.68 delay:0.0 options:UIViewAnimationOptionCurveEaseIn | UIViewAnimationOptionAllowUserInteraction animations:^{
                                    view.transform = CGAffineTransformMakeScale(0.1, 0.1);
                                    view.alpha = 0;
                                } completion:^(BOOL finished) {
                                }];
                            }];
                        }];
                    }];
                }];
            }];
        }];
    }];
}

- (BOOL)pointInside:(CGPoint)point withEvent:(UIEvent *)event {
    // 其实self是非常大的，所以限定只有文案部分能响应点击，配合动画中的UIViewAnimationOptionAllowUserInteraction参数实现点击效果
    if (CGRectContainsPoint(self.triangleView.frame, point)) {
        return YES;
    } else {
        return NO;
    }
}


@end
