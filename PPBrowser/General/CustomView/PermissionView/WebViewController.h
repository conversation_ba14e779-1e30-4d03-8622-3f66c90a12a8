//
//  WebViewController.h
//  MaizyClock
//
//  Created by q<PERSON><PERSON> on 2022/2/25.
//

#import <UIKit/UIKit.h>

#import <WebKit/WebKit.h>
#import "BaseViewController.h"

typedef enum WebViewType {
    WebViewTypePush,
    WebViewTypePresent
} WebViewType;

@interface WebViewController : BaseViewController

- (instancetype)initWithWebviewType:(WebViewType)webviewType
                              Title:(NSString*)title;

- (instancetype)initWithWebviewType:(WebViewType)webviewType
                              Title:(NSString*)title
                   rightButtonTitle:(NSString *)rightButtonTitle
             rightButtonClickAction:(void (^)(UIView* view))rightButtonClickAction;

//加载本地文件
- (void)loadFileName:(NSString*)file;
//加载URL
- (void)loadRequest:(NSURLRequest*)request;
//加载字符串
-  (void)loadHTMLString:(NSString *)string baseURL:(NSURL *)baseURL;
//根据MIMEType加载二进制文件
- (void)loadData:(NSData *)data MIMEType:(NSString *)type;

@property(nonatomic,copy) void (^didBackBlock)(void);

@end

