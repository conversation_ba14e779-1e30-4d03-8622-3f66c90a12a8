//
//  PermissionView.m
//  YSBPro
//
//  Created by lucas on 2020/4/10.
//  Copyright © 2020 lu lucas. All rights reserved.
//

#import "PermissionView.h"

#import "MaizyHeader.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"
#import "UIView+FrameHelper.h"

#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "NSString+Helper.h"
#import "NSObject+Helper.h"
#import "ReactiveCocoa.h"
#import <WebKit/WebKit.h>

#import "WebViewController.h"
#import "PreferenceModel.h"
#import "PreferenceManager.h"

@interface PermissionView ()

@property (nonatomic, strong) UIView *backView;

@property (nonatomic, strong) UIImageView *topImageView;
@property (nonatomic, strong) UILabel *titleLabel;

@property (nonatomic, strong) UIScrollView *scrollView;
@property (nonatomic, strong) UIView *contentView;
@property (nonatomic, strong) UILabel *contentLabel;

@property (nonatomic, strong) UILabel *checkLabel;

@property (nonatomic, strong) UIButton *agreeBtn;
@property (nonatomic, strong) UIButton *rejectBtn;

@property (nonatomic, copy) void(^completion)(void);

@end

@implementation PermissionView

+ (void)showPermissionView:(void(^)())completion
{
    PermissionView *view = [[PermissionView alloc] init];
    view.completion = completion;
    UIWindow* window = YBIBNormalWindow();
    [window addSubview:view];
    [view show];
}

- (instancetype)init
{
    if (self = [super init]) {
        self.frame = [UIScreen mainScreen].bounds;
        self.backgroundColor = [UIColor colorWithWhite:0 alpha:0.2f];
        [self addSubViews];
        [self defineLayout];
    }
    return self;
}

- (void)addSubViews
{
    [self addSubview:self.backView];
    
    [self.backView addSubview:self.topImageView];
    [self.topImageView addSubview:self.titleLabel];
    
    [self.backView addSubview:self.scrollView];
    [self.scrollView addSubview:self.contentView];
    [self.contentView addSubview:self.contentLabel];
    
    [self.backView addSubview:self.checkLabel];
    [self.backView addSubview:self.agreeBtn];
    [self.backView addSubview:self.rejectBtn];
}

- (void)defineLayout
{
    [self.backView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self);
        make.width.equalTo(@290.0f);
        make.height.equalTo(@335.0f);
    }];
    
    [self.topImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.top.equalTo(self.backView);
        make.height.equalTo(@44.0f);
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.topImageView);
    }];
    
    [self.scrollView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.backView).offset(20.0f);
        make.right.equalTo(self.backView).offset(-20.0f);
        make.top.equalTo(self.topImageView.mas_bottom).offset(20.0f);
        make.height.equalTo(@170.0f);
    }];
    
    [self.contentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.scrollView);
        make.width.equalTo(self.scrollView);
    }];
    
    [self.contentLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.top.bottom.equalTo(self.contentView);
    }];
    
    [self.checkLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.backView).offset(20.0f);
        make.top.equalTo(self.scrollView.mas_bottom);
        make.height.equalTo(@42.0f);
    }];
    
    UIView* line = [UIView new];
    line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    [self.backView addSubview:line];
    [line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.backView);
        make.top.equalTo(self.checkLabel.mas_bottom).offset(10.0f);
        make.height.mas_equalTo(0.5);
    }];
    
    [self.agreeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.backView);
        make.top.equalTo(line.mas_bottom);
        make.height.mas_equalTo(40);
        make.left.equalTo(self.rejectBtn.mas_right);
    }];
    
    [self.rejectBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.backView);
        make.top.equalTo(self.agreeBtn);
        make.height.mas_equalTo(40);
        make.width.equalTo(self.agreeBtn);
    }];
    
    line = [UIView new];
    line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    [self.backView addSubview:line];
    [line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.bottom.equalTo(self.agreeBtn);
        make.centerX.equalTo(self.backView);
        make.width.mas_equalTo(0.5);
    }];
}

- (UIView *)backView
{
    if (!_backView) {
        _backView = [[UIView alloc] init];
        _backView.backgroundColor = [UIColor whiteColor];
        _backView.layer.cornerRadius = 5.0f;
        _backView.layer.masksToBounds = YES;
    }
    return _backView;
}

- (UIImageView *)topImageView
{
    if (!_topImageView) {
        _topImageView = [[UIImageView alloc] init];
        _topImageView.backgroundColor = [[UIColor colorWithHexString:@"#0080fe"] colorWithAlphaComponent:0.8];
//        UIImage* image = [[UIImage imageNamed:@"permission_top_back"] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
//        _topImageView.image = image;
//        _topImageView.tintColor = [[UIColor colorWithHexString:@"#0080fe"] colorWithAlphaComponent:0.8];
    }
    return _topImageView;
}

- (UILabel *)titleLabel
{
    if (!_titleLabel) {
        _titleLabel = [UIView createLabelWithTitle:@""
                                              textColor:[UIColor whiteColor]
                                                bgColor:[UIColor clearColor]
                                               fontSize:16.0f
                                          textAlignment:NSTextAlignmentCenter
                                                  bBold:YES];
    }
    return _titleLabel;
}

- (UIScrollView *)scrollView
{
    if (!_scrollView) {
        _scrollView = [[UIScrollView alloc] init];
        _scrollView.showsVerticalScrollIndicator = NO;
    }
    return _scrollView;
}

- (UIView *)contentView
{
    if (!_contentView) {
        _contentView = [[UIView alloc] init];
    }
    return _contentView;
}

- (UILabel *)contentLabel
{
    if (!_contentLabel) {
        _contentLabel = [UIView createLabelWithTitle:@""
                                              textColor:[UIColor colorWithHexString:@"#666666"]
                                                bgColor:[UIColor clearColor]
                                               fontSize:15.0f
                                          textAlignment:NSTextAlignmentLeft
                                                  bBold:NO];
        _contentLabel.numberOfLines = 0;
    }
    return _contentLabel;
}

- (UILabel *)checkLabel
{
    if (!_checkLabel) {
        _checkLabel = [[UILabel alloc] init];
        _checkLabel.attributedText = [NSString attributedStringWithText:@"点击查看《隐私政策》"
                                                                      font:[UIFont systemFontOfSize:12.0f]
                                                                  color:[[UIColor colorWithHexString:@"#0080fe"] colorWithAlphaComponent:0.6]
                                                                     title:@"点击查看"
                                                                 titleFont:[UIFont systemFontOfSize:12.0f]
                                                                titleColor:[UIColor colorWithHexString:@"#333333"]];
        _checkLabel.userInteractionEnabled = YES;
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] init];
        @weakify(self)
        [tap.rac_gestureSignal subscribeNext:^(id x) {
            @strongify(self)
            [self showXieyi];
        }];
        [_checkLabel addGestureRecognizer:tap];
    }
    return _checkLabel;
}

- (void)showXieyi
{
    WebViewController* vc = [[WebViewController alloc]initWithWebviewType:WebViewTypePresent Title:nil];
    
    NSURL* url = [NSURL URLWithString:NSLocalizedString(@"privacy.policy.fileUrl", nil)];
    NSURLRequest* request = [NSURLRequest requestWithURL:url];
    [vc loadRequest:request];
    
    vc.didBackBlock = ^{
        UIWindow* window = YBIBNormalWindow();
        [window addSubview:self];
        [self show];
    };
    vc.hidesBottomBarWhenPushed = YES;
    
    UIWindow* window = YBIBNormalWindow();
    UITabBarController* tabvc = (UITabBarController*)window.rootViewController;
    
    UINavigationController* navc = [[UINavigationController alloc]initWithRootViewController:vc];
    [tabvc presentViewController:navc animated:YES completion:nil];
    
    [self removeFromSuperview];
}

- (UIButton *)agreeBtn
{
    if (!_agreeBtn) {
        _agreeBtn = [UIButton new];
        [_agreeBtn setTitle:@"同意" forState:UIControlStateNormal];
        _agreeBtn.titleLabel.font = [UIFont systemFontOfSize:16];
        [_agreeBtn setTitleColor:[UIColor colorWithHexString:@"#0080fe"] forState:UIControlStateNormal];
        @weakify(self)
        [[_agreeBtn rac_signalForControlEvents:UIControlEventTouchUpInside]
            subscribeNext:^(id x) {
            @strongify(self)
            
            // 更新状态
            [PreferenceManager shareInstance].items.isAgreedUMengPermission = @(YES);
            [[PreferenceManager shareInstance] encode];
            
            if (self.completion) {
                self.completion();
            }
            [self removeFromSuperview];
        }];

        _agreeBtn.layer.cornerRadius = 5.0f;
        _agreeBtn.layer.masksToBounds = YES;
    }
    return _agreeBtn;
}

- (UIButton *)rejectBtn
{
    if (!_rejectBtn) {
        _rejectBtn = [UIButton new];
        [_rejectBtn setTitle:@"取消" forState:UIControlStateNormal];
//        _rejectBtn.backgroundColor = [[UIColor colorWithHexString:kMainColor] colorWithAlphaComponent:0.8];
        _rejectBtn.titleLabel.font = [UIFont systemFontOfSize:16];
        [_rejectBtn setTitleColor:[UIColor colorWithHexString:@"#999999"] forState:UIControlStateNormal];
        [[_rejectBtn rac_signalForControlEvents:UIControlEventTouchUpInside]
            subscribeNext:^(id x) {
            // 更新状态
            [PreferenceManager shareInstance].items.isAgreedUMengPermission = @(NO);
            [[PreferenceManager shareInstance] encode];
            
            exit(0);
        }];
        
        _rejectBtn.layer.cornerRadius = 5.0f;
//        _rejectBtn.layer.borderColor = [UIColor colorWithHexString:@"#fc950f"].CGColor;
//        _rejectBtn.layer.borderWidth = 1.0f;
    }
    return _rejectBtn;
}

- (void)show
{
    // 4.37.0 改为本地，否则国行第一次打开会显示不出来
    self.titleLabel.text = @"个人信息保护指引";
    self.contentLabel.text = @"\
Focus浏览器隐私政策指引:\n\
为了让您更好地使用Focus浏览器，请充分阅读并理解《隐私政策》\n\
我们会遵循隐私政策收集、使用信息，但不会因同意了隐私政策而进行强制捆绑式的信息收集。\n\
如果您同意，请点击下面的按钮以接受我们的服务。\n\
您可以查看完整版《隐私政策》";
}


@end
