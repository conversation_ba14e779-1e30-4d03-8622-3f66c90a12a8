//
//  WebViewController.m
//  Maizy<PERSON><PERSON>
//
//  Created by q<PERSON><PERSON> on 2022/2/25.
//

#import "WebViewController.h"

#import "MaizyHeader.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"
#import "UIView+FrameHelper.h"

#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "NSString+Helper.h"
#import "NSObject+Helper.h"
#import "ReactiveCocoa.h"

#import "ThemeProtocol.h"
#import "PreferenceManager.h"

#import "UIView+LoadingView.h"
#import "BrowserUtils.h"

@interface WebViewController ()<WKNavigationDelegate, WKUIDelegate, WKScriptMessageHandler, WKURLSchemeHandler, ThemeProtocol>

@property (nonatomic, strong) WKWebView *webView;
@property (nonatomic, strong) WKUserContentController *userContentController;

@property (nonatomic, assign) WebViewType webviewType;

@property (nonatomic, strong) UIButton* rightButton;

@property (nonatomic, strong) NSString *rightButtonTitle;

@property (nonatomic, copy) void (^rightButtonClickAction)(UIView*);

@end

@implementation WebViewController

- (instancetype)initWithWebviewType:(WebViewType)webviewType
                              Title:(NSString*)title
{
    self = [super init];
    if(self) {
        self.title = title;
        self.webviewType = webviewType;
    }
    
    return self;
}

- (instancetype)initWithWebviewType:(WebViewType)webviewType
                              Title:(NSString*)title
                   rightButtonTitle:(NSString *)rightButtonTitle
             rightButtonClickAction:(void (^)(UIView* view))rightButtonClickAction
{
    self = [self initWithWebviewType:webviewType Title:title];
    if(self) {
        self.rightButtonTitle = rightButtonTitle;
        self.rightButtonClickAction = rightButtonClickAction;
        
        [self _createCustomRightBarButtonItem];
    }
    
    return self;
}

- (void)viewDidLoad
{
    [super viewDidLoad];
    
    self.view.backgroundColor = UIColor.whiteColor;
    
    [self addSubviews];
    [self defineLayout];
    [self createCustomLeftBarButtonItem];

    [self applyTheme];
}

- (void)viewDidDisappear:(BOOL)animated
{
    [super viewDidDisappear:animated];
    
    if(self.didBackBlock) {
        self.didBackBlock();
    }
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
}

- (void)loadFileName:(NSString*)file
{
    NSString* url = [[NSBundle mainBundle] pathForResource:file ofType:@"html"];
    NSURL* fileUrl = [NSURL fileURLWithPath:url];
    [self.webView loadRequest:[NSURLRequest requestWithURL:fileUrl]];
}

- (void)loadRequest:(NSURLRequest*)request
{
    [self.webView loadRequest:request];
}

-  (void)loadHTMLString:(NSString *)string baseURL:(NSURL *)baseURL
{
    //适配wkwebview字体显示异常的问题, 本地手动添加html<header>标签
    NSString* header = @"<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no\"><style>*{padding: 0;margin: 0;box-sizing: border-box;}html,body {width: 100vw;overflow-x: hidden;}body{padding:8px 8px;}</style>";
    NSString* content = [NSString stringWithFormat:@"%@%@",header,string];
    [self.webView loadHTMLString:content baseURL:baseURL];
}

- (void)loadData:(NSData *)data MIMEType:(NSString *)type
{
    [self.webView loadData:data MIMEType:type characterEncodingName:@"" baseURL:[NSURL URLWithString:@""]];
}

//- (void)configureNavigaitonBar
//{
//    [self.navigationController.navigationBar setTitleTextAttributes:@{ NSForegroundColorAttributeName:[UIColor colorWithHexString:@"#333333"],
//                                                                       NSFontAttributeName:[UIFont systemFontOfSize:18.0f],
//                                                                       NSShadowAttributeName:[[NSShadow alloc] init] }];
//    // 4.44.0 去掉导航栏下方默认的一像素shadow
//    [self.navigationController.navigationBar setBackgroundImage:[[UIImage alloc] init] forBarMetrics:UIBarMetricsDefault];
//    [self.navigationController.navigationBar setShadowImage:[[UIImage alloc] init]];
//    
//    if (@available(iOS 15.0, *)) {
//        //5.8.5 iOS 15兼容
//        UINavigationBarAppearance *navigationBarAppearance = [UINavigationBarAppearance new];
//        navigationBarAppearance.backgroundColor = [UIColor colorWithHexString:@"#ffffff"];
//        navigationBarAppearance.shadowColor = [UIColor clearColor];
//        // 5.8.5 虽然前面设置过导航栏文字样式了，但是如果这里不设置，会被重置回黑色的默认样式
//        [navigationBarAppearance setTitleTextAttributes:@{ NSForegroundColorAttributeName:[UIColor colorWithHexString:@"#333333"],
//                                                           NSFontAttributeName:[UIFont systemFontOfSize:18.0f],
//                                                           NSShadowAttributeName:[[NSShadow alloc] init] }];
//        self.navigationController.navigationBar.scrollEdgeAppearance = navigationBarAppearance;
//        self.navigationController.navigationBar.standardAppearance = navigationBarAppearance;
//    } else {
//        // 5.2.5 改为白色
//        self.navigationController.navigationBar.barTintColor = [UIColor colorWithHexString:@"#ffffff"];
//    }
//    
//    // 设置导航栏和状态栏样式
//    if (@available(iOS 13.0, *)) {
//        [UIApplication sharedApplication].statusBarStyle = UIStatusBarStyleDarkContent;
//    } else {
//        [UIApplication sharedApplication].statusBarStyle = UIStatusBarStyleDefault;
//    }
//    
//    [[UIApplication sharedApplication] setStatusBarHidden:NO];
//    
//    [self.navigationController setNavigationBarHidden:NO];
//}
//
//- (void)createLeftButton
//{
//    UIButton* leftButton = [[UIButton alloc] initWithFrame:CGRectMake(0.0, 0.0f, 44.0f, 44.0f)];
//    leftButton.tintColor = [UIColor blackColor];
//    [leftButton setBackgroundColor:[UIColor clearColor]];
//    [leftButton setImage:[[UIImage imageNamed:@"navi_back_normal_gray"] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate] forState:UIControlStateNormal];
//    [leftButton addTarget:self action:@selector(leftBarbuttonClick:) forControlEvents:UIControlEventTouchUpInside];
//    self.leftButton = leftButton;
//    
//    UIBarButtonItem *barItem = [[UIBarButtonItem alloc] initWithCustomView:leftButton];
//    self.navigationItem.leftBarButtonItems = @[barItem];
//}

#pragma mark -- 导航栏

- (void)_createCustomRightBarButtonItem
{
    UIButton* rightButton = [[UIButton alloc] initWithFrame:CGRectMake(0.0, 0.0f, 44.0f, 44.0)];
    [rightButton setBackgroundColor:[UIColor clearColor]];

    float font = iPadValue(20, 16);
    rightButton.titleLabel.font = [UIFont systemFontOfSize:font];
    [rightButton setTitle:self.rightButtonTitle forState:UIControlStateNormal];
    [rightButton setTitleColor:[UIColor colorWithHexString:@"#2D7AFE"] forState:UIControlStateNormal];
    [rightButton addTarget:self action:@selector(rightBarbuttonClick) forControlEvents:UIControlEventTouchUpInside];
    UIBarButtonItem* barItem = [[UIBarButtonItem alloc] initWithCustomView:rightButton];
    UIBarButtonItem *spacer = [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemFixedSpace target:nil action:nil];
    spacer.width = -16.0f; // for example shift right bar button to the right

    self.rightButton = rightButton;
    
    self.navigationItem.rightBarButtonItems = @[spacer, barItem];
}

- (void)rightBarbuttonClick
{
    if(self.rightButtonClickAction) {
        self.rightButtonClickAction(self.view);
    }
}

- (void)leftBarbuttonClick:(UIButton *)button
{
    if([self.webView canGoBack]) {
        [self.webView goBack];
    } else {
        if(self.webviewType == WebViewTypePush) {
            [self.navigationController popViewControllerAnimated:YES];
        } else {
            [self dismissViewControllerAnimated:YES completion:^{
            }];
        }
    }
}

- (void)addSubviews
{
    [self.view addSubview:self.webView];
}

- (void)defineLayout
{
    [self.webView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view);
        make.left.and.right.equalTo(self.view);
        make.bottom.equalTo(self.view);
    }];
}

#pragma mark -- WKScriptMessageHandler
- (void)userContentController:(WKUserContentController *)userContentController didReceiveScriptMessage:(WKScriptMessage *)message
{
    if ([message.name isEqualToString:@"nativeApp"]) {
        NSDictionary *messageBody = message.body;
        NSString *messageType = messageBody[@"type"];
        
        if ([messageType isEqualToString:@"playVideo"]) {
            //播放视频
        }
    }
}

#pragma mark -- WKURLSchemeHandler
- (void)webView:(WKWebView *)webView startURLSchemeTask:(id <WKURLSchemeTask>)urlSchemeTask
{
    NSURL* URL = urlSchemeTask.request.URL;
    if(!URL || ![URL.scheme isEqualToString:@"focus"]) {
        return;
    }
    
    NSString* host = URL.host;
    
    NSArray* items = [host componentsSeparatedByString:@"."];
    NSString* name = items[0];
    NSString* type = items[1];
    
    NSString *bundle = [[NSBundle mainBundle] pathForResource:@"Help" ofType:@"bundle"];
    NSString *fileUrl = [[NSBundle bundleWithPath:bundle] pathForResource:name ofType:type];

    //这里涉及到png/gif的加载, 不同的类型转换为data方式不一样
    //所以这里采用本地请求的方式, 交给系统来处理格式转换的问题
    NSURL* fileURL = [NSURL fileURLWithPath:fileUrl];
    NSURLRequest *request = [[NSURLRequest alloc]initWithURL:fileURL];
    NSURLSession *session = [NSURLSession sharedSession];
    NSURLSessionDataTask *task = [session dataTaskWithRequest:request completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
        NSURLResponse* urlResponse = [[NSURLResponse alloc]initWithURL:URL MIMEType:response.MIMEType expectedContentLength:data.length textEncodingName:nil];
        
        [urlSchemeTask didReceiveResponse:urlResponse];
        [urlSchemeTask didReceiveData:data];
        [urlSchemeTask didFinish];
    }];
    
    [task resume];
}

- (void)webView:(nonnull WKWebView *)webView stopURLSchemeTask:(nonnull id<WKURLSchemeTask>)urlSchemeTask {
}

- (WKUserContentController *)userContentController
{
    if (!_userContentController) {
        _userContentController = [[WKUserContentController alloc] init];
    }
    return _userContentController;
}

- (WKWebView *)webView
{
    if (!_webView) {
        //! 使用添加了ScriptMessageHandler的userContentController配置configuration
        WKWebViewConfiguration *configuration = [[WKWebViewConfiguration alloc] init];
        configuration.userContentController = self.userContentController;
        [configuration setURLSchemeHandler:self forURLScheme:@"focus"];
        
        // 注册消息处理
        [configuration.userContentController addScriptMessageHandler:self name:@"nativeApp"];
            
        
        _webView = [[WKWebView alloc] initWithFrame:CGRectZero configuration:configuration];
        _webView.navigationDelegate = self;
        _webView.UIDelegate = self;
        
        _webView.scrollView.showsVerticalScrollIndicator = NO;
    }
    return _webView;
}

- (void)dealloc {
    // 移除消息处理器
    [self.webView.configuration.userContentController removeScriptMessageHandlerForName:@"nativeApp"];
}

@end
