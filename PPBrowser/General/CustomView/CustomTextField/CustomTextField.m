//
//  CustomTextField.m
//  YSBBusiness
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 15/1/7.
//  Copyright (c) 2015年 lu lucas. All rights reserved.
//

#import "CustomTextField.h"

@implementation CustomTextField

- (CGRect)leftViewRectForBounds:(CGRect)bounds{
    
    return _leftViewRect;
    
}

- (CGRect)rightViewRectForBounds:(CGRect)bounds{
    
    return _rightViewRect;
}

- (void)updatePlaceHolder:(NSString *)holder color:(UIColor *)color
{
    NSMutableAttributedString *placeholder = [[NSMutableAttributedString alloc]initWithString:holder];
    [placeholder addAttribute:NSForegroundColorAttributeName
                       value:color
                       range:NSMakeRange(0, holder.length)];
    self.attributedPlaceholder = placeholder;
}

@end
