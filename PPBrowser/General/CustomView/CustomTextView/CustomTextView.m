//
//  CustomTextView.m
//  PPBrowser
//
//  Created by qingbin on 2024/5/8.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import "CustomTextView.h"

#import "ReactiveCocoa.h"
#import "Masonry.h"

@interface CustomTextView ()
/// 最小高度
@property (nonatomic, assign) float minHeight;
/// 最大高度
@property (nonatomic, assign) float maxHeight;
/// 沉底提示
@property (nonatomic, strong) NSAttributedString *customPlaceHolder;

@end

@implementation CustomTextView

- (instancetype)init
{
    self = [super init];
    if (self) {
        [self commonInit];
    }
    
    return self;
}

#pragma mark - private method

- (void)commonInit
{
    // 使用通知监听文字改变
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(textDidChange:) name:UITextViewTextDidChangeNotification object:self];
}

- (void)textDidChange:(NSNotification *)note
{
    // 会重新调用drawRect:方法
    [self setNeedsDisplay];
}

- (void)dealloc
{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

//每次调用drawRect:方法，都会将以前画的东西清除掉
- (void)drawRect:(CGRect)rect
{
    // 本身textview的内边距， 上下是textView.textContainerInset.top，为8，左右是textView.textContainer.lineFragmentPadding，为5
    // 如果有文字，就直接返回，不需要画占位文字
    if (self.hasText) return;
    
    rect.origin.x = self.textContainer.lineFragmentPadding + self.textContainerInset.left;
    rect.origin.y = self.textContainerInset.top;
    rect.size.width -= 2 * rect.origin.x;
    
    if (self.customPlaceHolder != nil) {
        [self.customPlaceHolder drawInRect:rect];
    }
}

- (void)layoutSubviews
{
    [super layoutSubviews];
    
    [self setNeedsDisplay];
}

#pragma mark - public method

//更新placeholder
- (void)updateWithPlaceholder:(NSAttributedString *)placeholder
{
    self.customPlaceHolder = placeholder;
    
    [self setNeedsDisplay];
}

- (void)updateWithMinHeight:(float)minHeight
                  maxHeight:(float)maxHeight
{
    self.minHeight = minHeight;
    self.maxHeight = maxHeight;
}

- (void)updateWithMaxHeight:(float)maxHeight
{
    self.maxHeight = maxHeight;
}

- (void)updateWithMinHeight:(float)minHeight
{
    self.minHeight = minHeight;
}

#pragma mark -- override

- (void)setContentSize:(CGSize)contentSize
{
    [super setContentSize:contentSize];
    
    [self invalidateIntrinsicContentSize];
}

- (CGSize)intrinsicContentSize
{
    float height = self.contentSize.height;
    height = MAX(height, self.minHeight);
    height = MIN(height, self.maxHeight);
    
    return CGSizeMake(self.contentSize.width, height);
}

@end
