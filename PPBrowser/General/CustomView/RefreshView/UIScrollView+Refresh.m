//
//  UIScrollView+Refresh.m
//  TableViewPull
//
//  Created by lu luca<PERSON> on 6/11/14.
//
//

#import "UIScrollView+Refresh.h"
#import "EGORefreshTableHeaderView.h"
#import <objc/runtime.h>
#import "LZZImmediateRefreshTableFooterView.h"
#import "MaizyHeader.h"

@interface UIScrollView ()

@end

@implementation UIScrollView (Refresh)

static char LZZRefreshHeaderViewKey;
static char LZZImmediateRefreshFooterViewKey;

- (void)setHeader:(EGORefreshTableHeaderView *)header
{
    [self willChangeValueForKey:@"LZZRefreshHeaderViewKey"];
    objc_setAssociatedObject(self, &LZZRefreshHeaderViewKey,
                             header,
                             OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    [self didChangeValueForKey:@"LZZRefreshHeaderViewKey"];
}

- (void)setImmediateFooter:(LZZImmediateRefreshTableFooterView *)footer
{
    [self willChangeValueForKey:@"LZZImmediateRefreshFooterViewKey"];
    objc_setAssociatedObject(self, &LZZImmediateRefreshFooterViewKey,
                             footer,
                             OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    [self didChangeValueForKey:@"LZZImmediateRefreshFooterViewKey"];
}

- (EGORefreshTableHeaderView *)header
{
    return objc_getAssociatedObject(self, &LZZRefreshHeaderViewKey);
}

- (LZZImmediateRefreshTableFooterView *)immediateFooter
{
    return objc_getAssociatedObject(self, &LZZImmediateRefreshFooterViewKey);
}

- (void)addHeaderWithCallback:(void (^)(void))callback
{
    [self addHeaderWithCallback:callback attributes:nil];
}

- (void)addHeaderWithCallback:(void (^)(void))callback
                   attributes:(NSDictionary *)attrs
{
    // 1.创建新的header
    if (!self.header) {
        EGORefreshTableHeaderView *view = [[EGORefreshTableHeaderView alloc] initWithFrame:CGRectMake(0.0f, -kScreenHeight, kScreenWidth, kScreenHeight)];
        // 5.20.0 新增
        view.backgroundColor = attrs[@"backgroundColor"]?:[UIColor colorWithRed:0xf5 / 255.0f green:0xf6 / 255.0f blue:0xf5 / 255.0f alpha:1.0f];
        [view setTextColor:attrs[@"textColor"]?:[UIColor colorWithRed:0x56 / 255.0f green:0x5a / 255.0f blue:0x5c / 255.0f alpha:1.0f]];
        
        [self addSubview:view];
        [self setHeader:view];
    }
    
    // 2.设置block回调
    self.header.beginRefreshingCallback = callback;
}

- (void)endHeaderRefresh
{
    [self.header egoRefreshScrollViewDataSourceDidFinishedLoading:self];
}

- (void)removeHeaderView
{
    [self.header removeFromSuperview];
    self.header = nil;
    self.contentInset = UIEdgeInsetsMake(0, 0, 0, 0);
    
}

- (void)addImmediateFooterWithCallback:(void (^)(void))callback pageSize:(NSInteger)count
{
    [self addImmediateFooterWithCallback:callback pageSize:count attributes:nil];
}

- (void)addImmediateFooterWithCallback:(void (^)(void))callback pageSize:(NSInteger)count attributes:(NSDictionary *)attrs
{
    if (!self.immediateFooter) {
        // 去掉ios 11 默认tableview 为Self-Sizing，导致estimatedRowHeight为UITableViewAutomaticDimension，从而导致contentsize计算不为真实值
        if ([self isKindOfClass:[UITableView class]]) {
            if (@available(iOS 11.0, *)) {
                UITableView *tableView = (UITableView *)self;
                if (tableView.estimatedRowHeight == UITableViewAutomaticDimension) {
                    tableView.estimatedRowHeight = 0;
                }
            }
        }
        LZZImmediateRefreshTableFooterView *view = [[LZZImmediateRefreshTableFooterView alloc] initWithFrame:CGRectMake(0.0f, kScreenHeight , kScreenWidth, kScreenHeight)];
        view.text = attrs[@"text"]?:NSLocalizedString(@"refresh.noMore.title", nil);
        view.backgroundColorForNormal = attrs[@"backgroundColor"]?:[UIColor colorWithRed:0xf5 / 255.0f green:0xf6 / 255.0f blue:0xf5 / 255.0f alpha:1.0f];
        view.backgroundColorForNoMore = attrs[@"backgroundColorForNoMore"];
        [view setTextColor:attrs[@"textColor"]?:[UIColor colorWithRed:0x56 / 255.0f green:0x5a / 255.0f blue:0x5c / 255.0f alpha:1.0f]];
        
        [self addSubview:view];
        [self setImmediateFooter:view];
        view.pageSize = count;
    }
    
    // 2.设置block回调
    self.immediateFooter.beginRefreshingCallback = callback;
}
- (void)setImmediateFooterManualDetectLoad:(BOOL(^)())manualDetectLoad
{
    self.immediateFooter.manualDetectLoad = manualDetectLoad;
}

- (void)removeImmediateFooterViewAndNoMoreCallBack
{
    [self.immediateFooter removeFromSuperview];
    self.immediateFooter = nil;
    self.contentInset = UIEdgeInsetsMake(0, 0, 0, 0);
}

- (void)endImmediateFooterRefresh:(NSArray *)array
{
    if ([array respondsToSelector:@selector(count)]) {
        [self.immediateFooter lzzRefreshScrollViewDataSourceDidFinishedLoading:self count:array.count];
    } else {
        [self.immediateFooter lzzRefreshScrollViewDataSourceDidFinishedLoading:self count:0];
    }
}

- (void)endHeaderRefreshWithRequestCount:(NSArray *)array
{
    [self.header egoRefreshScrollViewDataSourceDidFinishedLoading:self];
    if ([array respondsToSelector:@selector(count)]) {
        [self.immediateFooter configNoMore:array.count];
    } else {
        [self.immediateFooter configNoMore:0];
    }
}

- (void)resetImmediateFooterRefreshBMore
{
    [self.immediateFooter configNoMore:self.immediateFooter.pageSize];
}

@end
