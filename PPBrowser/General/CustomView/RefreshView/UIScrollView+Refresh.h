//
//  UIScrollView+Refresh.h
//  TableViewPull
//
//  Created by lu l<PERSON><PERSON> on 6/11/14.
//
//

#import <UIKit/UIKit.h>


@interface UIScrollView (Refresh)
/**
 *  添加下拉刷新的回调方法，在回调中写网络请求
 *
 *  @param callback 回调方法
 */
- (void)addHeaderWithCallback:(void (^)(void))callback;
/*
 5.20.0 新增
 backgroundColor:背景色uicolor
 textColor:文案的颜色uicolor
 */
- (void)addHeaderWithCallback:(void (^)(void))callback
                   attributes:(NSDictionary *)attrs;
/**
 *  调用此方法完成下拉刷新(聊天页加载更多历史聊天 / 下拉刷新失败的时候)
 */
- (void)endHeaderRefresh;
// 移除下拉刷新
- (void)removeHeaderView;
/**
 *  调用此方法完成下拉刷新
 *  写在reloaddata 方法之后
 *  注：下拉刷新失败的时候调用这个方法（传nil或者@[]）感觉不是很合理，会导致被认为不能上拉加载更多了
 */
- (void)endHeaderRefreshWithRequestCount:(NSArray *)array;
/**
 *  添加上拉加载更多
 *
 *  @param callback 回调方法
 */
- (void)addImmediateFooterWithCallback:(void (^)(void))callback pageSize:(NSInteger)count;
/*
 text:没有更多的文案nsstring
 textColor:文案的颜色uicolor
 backgroundColor:背景色uicolor
 backgroundColorForNoMore:没有更多时的背景色uicolor
 */
- (void)addImmediateFooterWithCallback:(void (^)(void))callback pageSize:(NSInteger)count attributes:(NSDictionary *)attrs;
///4.42.0 列表预加载，设置这个方法后，加载下一页的时机由block返回的bool控制
- (void)setImmediateFooterManualDetectLoad:(BOOL(^)(void))manualDetectLoad;
// 移除上拉加载更多
- (void)removeImmediateFooterViewAndNoMoreCallBack;
/**
 *  调用此方法完成上拉加载
 *  写在reloaddata 方法之后
 */
- (void)endImmediateFooterRefresh:(NSArray *)array;
// 重置没有更多的状态，用于单列表，切换分类时重置状态
- (void)resetImmediateFooterRefreshBMore;

@end
