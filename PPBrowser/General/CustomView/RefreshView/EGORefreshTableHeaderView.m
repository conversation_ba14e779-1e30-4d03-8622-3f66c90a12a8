//
//  EGORefreshTableHeaderView.m
//  Demo
//
//  Created by <PERSON> on 10/14/09October14.
//  Copyright 2009 enormego. All rights reserved.
//
//  Permission is hereby granted, free of charge, to any person obtaining a copy
//  of this software and associated documentation files (the "Software"), to deal
//  in the Software without restriction, including without limitation the rights
//  to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
//  copies of the Software, and to permit persons to whom the Software is
//  furnished to do so, subject to the following conditions:
//
//  The above copyright notice and this permission notice shall be included in
//  all copies or substantial portions of the Software.
//
//  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
//  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
//  FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
//  AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
//  LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
//  OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
//  THE SOFTWARE.
//

#import "EGORefreshTableHeaderView.h"
#import <QuartzCore/QuartzCore.h>
#import "Masonry.h"

@interface EGORefreshTableHeaderView ()

{
    BOOL _reloading;
    EGOPullRefreshState _state;
}

@property (nonatomic, strong) UIStackView *centerView;
@property (nonatomic, strong) UILabel *statusLabel;
@property (nonatomic, strong) UIActivityIndicatorView *activityView;

@end

@implementation EGORefreshTableHeaderView

- (id)initWithFrame:(CGRect)frame
{
    if (self = [super initWithFrame:frame]) {
        [self addSubviews];
        [self defineLayout];
        
		[self setState:EGOOPullRefreshNormal];
    }
    return self;
}

- (void)addSubviews
{
    [self addSubview:self.centerView];
}

- (void)defineLayout
{
    [self.centerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self).offset(-27.0f);
        make.centerX.equalTo(self);
    }];
}

- (UIStackView *)centerView
{
    if (!_centerView) {
        _centerView = [[UIStackView alloc] initWithArrangedSubviews:@[
            self.activityView,
            self.statusLabel
        ]];
        _centerView.axis = UILayoutConstraintAxisHorizontal;
        _centerView.spacing = 4.0f;
        _centerView.distribution = UIStackViewDistributionFill;
        _centerView.alignment = UIStackViewAlignmentFill;
    }
    return _centerView;
}

- (UILabel *)statusLabel
{
    if (!_statusLabel) {
        _statusLabel = [[UILabel alloc] init];
        _statusLabel.font = [UIFont systemFontOfSize:13.0f];

        _statusLabel.backgroundColor = [UIColor clearColor];
        _statusLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _statusLabel;
}

- (UIActivityIndicatorView *)activityView
{
    if (!_activityView) {
        _activityView = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleGray];
    }
    return _activityView;
}

#pragma mark -
#pragma mark Setters

- (void)setState:(EGOPullRefreshState)aState{
	
	switch (aState) {
		case EGOOPullRefreshPulling:
        {
            self.statusLabel.text = @"松手刷新";
        }
			break;
		case EGOOPullRefreshNormal:
        {
            self.statusLabel.text = @"下拉刷新";
            [self.activityView stopAnimating];
            self.activityView.hidden = YES;
        }
			break;
		case EGOOPullRefreshLoading:
        {
            self.statusLabel.text = @"努力刷新中";
            [self.activityView startAnimating];
            self.activityView.hidden = NO;
        }
			break;
		default:
			break;
	}
	
	_state = aState;
}

- (void)setTextColor:(UIColor *)textColor
{
    self.statusLabel.textColor = textColor;
    // 如果是白色字，用白色的loading，其余用gray的loading
    // 箭头颜色也是同理
    if (CGColorEqualToColor(UIColor.whiteColor.CGColor, textColor.CGColor)) {
        self.activityView.activityIndicatorViewStyle = UIActivityIndicatorViewStyleWhite;
    } else {
        self.activityView.activityIndicatorViewStyle = UIActivityIndicatorViewStyleGray;
    }
}

#pragma mark -
#pragma mark ScrollView Methods

- (void)egoRefreshScrollViewDidScroll:(UIScrollView *)scrollView {	
	
	if (_state == EGOOPullRefreshLoading) {
        if (UIEdgeInsetsEqualToEdgeInsets(UIEdgeInsetsZero, scrollView.contentInset)) {
            CGFloat offset = MAX(scrollView.contentOffset.y * -1, 0);
            offset = MIN(offset, 60);
            scrollView.contentInset = UIEdgeInsetsMake(offset, 0.0f, scrollView.contentInset.bottom, 0.0f);
        }
	} else if (scrollView.isDragging) {
		
		BOOL _loading = NO;
        // fix
        _loading = _reloading;
		
		if (_state == EGOOPullRefreshPulling && scrollView.contentOffset.y > -65.0f && scrollView.contentOffset.y < 0.0f && !_loading) {
			[self setState:EGOOPullRefreshNormal];
		} else if (_state == EGOOPullRefreshNormal && scrollView.contentOffset.y < -65.0f && !_loading) {
			[self setState:EGOOPullRefreshPulling];
		}
		
		if (scrollView.contentInset.top != 0) {
			scrollView.contentInset = UIEdgeInsetsZero;
		}
	}
}

- (void)egoRefreshScrollViewDidEndDragging:(UIScrollView *)scrollView {
	
	BOOL _loading = NO;
    // fix
    _loading = _reloading;
	
    // 加入loading状态判断
    // 4.42.0 添加注释：uiview动画会导致_reloading变量的赋值滞后，如果改65.0f这个值的话，要验证是否会导致触发多次beginRefreshingCallback
	if (scrollView.contentOffset.y <= - 65.0f && !_loading) {
        
        [UIView beginAnimations:nil context:NULL];
        [UIView setAnimationDuration:0.2];
        scrollView.contentInset = UIEdgeInsetsMake(60.0f, 0.0f, scrollView.contentInset.bottom, 0.0f);
        [UIView commitAnimations];
        
        [self setState:EGOOPullRefreshLoading];
        _reloading = YES;
        if (self.beginRefreshingCallback) {
            self.beginRefreshingCallback();
        }
	}
}

- (void)egoRefreshScrollViewDataSourceDidFinishedLoading:(UIScrollView *)scrollView {	
	
    //fix, 使用缓存的时候，瞬间就返回了（如聊天），导致不能缩回去(4.5.0 更新 取消了延时，修改了上面方法的执行顺序，这个延时会导致请求cancel的时候，状态不对劲)
    if (!self) {
        return ;
    }
    
    [UIView animateWithDuration:0.3f delay:0.3f options:UIViewAnimationOptionCurveEaseOut animations:^{
        [scrollView setContentInset:UIEdgeInsetsMake(0.0f, 0.0f, scrollView.contentInset.bottom, 0.0f)];
    } completion:^(BOOL finished) {
        _reloading = NO;
        [self setState:EGOOPullRefreshNormal];
    }];
}

// fix
- (void)observeValueForKeyPath:(NSString *)keyPath ofObject:(id)object change:(NSDictionary *)change context:(void *)context
{
    // 不能跟用户交互就直接返回
    if (!self.userInteractionEnabled || self.alpha <= 0.01 || self.hidden) return;
    
    if ([@"contentOffset" isEqualToString:keyPath]) {
        UIScrollView *scrollView = (UIScrollView *)object;
        if (scrollView.isDragging) {
            [self egoRefreshScrollViewDidScroll:scrollView];
        } else {
            [self egoRefreshScrollViewDidEndDragging:scrollView];
        }
    }
}

// 加了这个才使得remove的时候调用willmove，这样就能移除观察者
- (void)removeFromSuperview
{
    [super removeFromSuperview];
}

- (void)willMoveToSuperview:(UIView *)newSuperview
{
    [super willMoveToSuperview:newSuperview];
    
    // 旧的父控件
    [self.superview removeObserver:self forKeyPath:@"contentOffset" context:nil];
    
    if (newSuperview) { // 新的父控件
        [newSuperview addObserver:self forKeyPath:@"contentOffset" options:NSKeyValueObservingOptionNew context:nil];
    }
}

#pragma mark -
#pragma mark Dealloc
- (void)dealloc
{
	
}

@end
