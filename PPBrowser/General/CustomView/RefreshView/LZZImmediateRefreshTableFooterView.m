//
//  LZZImmediateRefreshTableFooterView.m
//  YSBBusiness
//
//  Created by lu luca<PERSON> on 17/4/15.
//  Copyright (c) 2015 lu lucas. All rights reserved.
//

#import "LZZImmediateRefreshTableFooterView.h"
#import "UIView+FrameHelper.h"
#import "Masonry.h"

#define TEXT_COLOR	 [UIColor colorWithRed:31.0/255.0 green:31.0/255.0 blue:31.0/255.0 alpha:1.0]

#define kContentOffsetY (scrollView.contentOffset.y + scrollView.frame.size.height - scrollView.contentSize.height)


@interface LZZImmediateRefreshTableFooterView ()

{
    BOOL _reloading;// 如果是请求中，不再次触发加载更多
    
    BOOL _bNoMore;// yes：没有更多了；no：还能加载
    
    RefreshImmediateFooterType _type;
    
    LZZImmediatePullRefreshState _state;// 当前状态
    
    __weak UIScrollView *_scrollView;
}

@property (nonatomic, strong) UIStackView *stackView;
@property (nonatomic, strong) UIActivityIndicatorView *activityView;// 转圈view
@property (nonatomic, strong) UILabel *statusLabel;// 文字

@end

@implementation LZZImmediateRefreshTableFooterView


- (id)initWithFrame:(CGRect)frame
{
    if (self = [super initWithFrame:frame]) {
        
        [self addSubviews];
        [self defineLayout];
        
        _bNoMore = NO;
        _reloading = NO;
        [self setState:LZZImmediatePullRefreshHide];
    }
    
    return self;
    
}

- (void)addSubviews
{
    [self addSubview:self.stackView];
}

- (void)defineLayout
{
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self);
        make.height.equalTo(@48.0f);
        make.centerX.equalTo(self);
    }];
}

- (UIStackView *)stackView
{
    if(!_stackView)
    {
        _stackView = [[UIStackView alloc]initWithArrangedSubviews:@[
            self.activityView,
            self.statusLabel
        ]];
        
        _stackView.axis = UILayoutConstraintAxisHorizontal;
        _stackView.alignment = UIStackViewAlignmentCenter;
        _stackView.distribution = UIStackViewDistributionFill;
        _stackView.spacing = 2.0f;
    }
    
    return _stackView;
}

- (UIActivityIndicatorView *)activityView
{
    if (!_activityView) {
        _activityView = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleGray];
    }
    return _activityView;
}

- (UILabel *)statusLabel
{
    if (!_statusLabel) {
        _statusLabel = [[UILabel alloc] init];
        _statusLabel.font = [UIFont systemFontOfSize:13.0f];
        _statusLabel.backgroundColor = [UIColor clearColor];
    }
    return _statusLabel;
}

#pragma mark - Setters

- (void)setTextColor:(UIColor *)textColor
{
    self.statusLabel.textColor = textColor;
}

// state仅处理显示
- (void)setState:(LZZImmediatePullRefreshState)aState{
    
    if (aState == _state) {
        // 相同的话就不修改了
        return;
    }
    [self.activityView stopAnimating];// bug 35424 需要先搜索有很多结果的，再通过搜索框搜索只有少数结果的，怀疑是startAnimating使得hidden无效，所以先stop
    self.activityView.hidden = YES;
    self.statusLabel.hidden = YES;
    switch (aState) {
        case LZZImmediatePullRefreshHide:
        {
            self.backgroundColor = self.backgroundColorForNormal;
        }
            break;
        case LZZImmediatePullRefreshNoMore:
        {
            self.statusLabel.hidden = NO;
            self.statusLabel.text = self.text;
            self.backgroundColor = self.backgroundColorForNoMore?:self.backgroundColorForNormal;
        }
            break;
        case LZZImmediatePullRefreshLoading:
        {
            self.activityView.hidden = NO;
            self.statusLabel.hidden = NO;
            
            self.statusLabel.text = NSLocalizedString(@"refresh.loading.title", nil);

            
            [self.activityView startAnimating];
            self.backgroundColor = self.backgroundColorForNormal;
        }
            
            break;
        default:
            break;
    }
    
    _state = aState;
}

- (void)configNoMore:(NSInteger)count
{
    if (count == 0 || count < _pageSize) {
        // 没有更多了
        _bNoMore = YES;
    } else {
        // 还有下一页
        _bNoMore = NO;
    }
}

#pragma mark -
#pragma mark ScrollView Methods

- (void)lzzRefreshScrollViewDidScroll:(UIScrollView *)scrollView {
    
    if (scrollView.contentSize.height < scrollView.frame.size.height) {
        return;
    }
    
    if (_reloading == YES
        || _bNoMore == YES) {
        
        return;
    }
    // 4.21.0 添加注释，因为这里kContentOffsetY是double的相减，加上底部48的contentInset（此处的inset是为了滚动到底部时显示加载中的文案）,使得存在出现48.00000000237这样的误差，此时就会触发callback，然后并不希望触发callback，所以这里使用了向下取整。
    // 举例就是bug 26467，点击有促销，促使会remove数据源，reload tableview，这样就会触发上面的问题。事实上调试时发现，此时选择排序，也会触发多次请求，也是不对的，也是这个引起的。
    if (self.manualDetectLoad) {// 4.42.0 手动控制加载下一页的时机
        if (self.manualDetectLoad()) {
            if (self.beginRefreshingCallback) {
                _reloading = YES;
                self.beginRefreshingCallback();
            }
        }
    } else {// 默认的时机
        if (floor(kContentOffsetY) > 48.0f) {
            if (self.beginRefreshingCallback) {
                _reloading = YES;
                self.beginRefreshingCallback();
            }
        }
    }
}

// 在这里处理显示的话，主要问题是此时无法获得正常的contentsize
- (void)lzzRefreshScrollViewDataSourceDidFinishedLoading:(UIScrollView *)scrollView count:(NSInteger)count {
    /*
     4.18.0 补充注释
     1.一开始的时候是没有dispatch_after的，上面的那个方法是beginRefreshingCallback之后再setState:LZZImmediatePullRefreshLoading的
     2.当时还有自行做的一套缓存机制（或者聊天的下拉加载更多聊天记录），然后发现，当使用缓存时，beginRefreshingCallback立即就返回数据了，此时lzzRefreshScrollViewDataSourceDidFinishedLoading改变好的状态，被1中的setState:LZZImmediatePullRefreshLoading又改回去了，导致状态失效，所以在这里增加了dispatch_after，使得状态正常。
     3.v4.5.0版本的时候，通过调整beginRefreshingCallback和setState:LZZImmediatePullRefreshLoading的顺序，解决了状态失效问题，此时也发现dispatch_after会导致请求cancel的时候，状态不对劲（想不起来是什么不对劲。后来想起来了，详见第6点），所以把dispatch_after去掉了。
     4.v4.10.0版本的时候，发现存在这样的问题：header refresh，返回一页，而且小于pagesize，此时如果先调用lzzRefreshScrollViewDataSourceDidFinishedLoading，再调用tableview 的reloaddata，在这个判断上就会进入LZZImmediatePullRefreshHide，然而理应进入LZZImmediatePullRefreshNoMore（如4.10.0及以前版本的药学习首页-跟我学列表）。此时建议大伙使用的时候，先reloaddata，再调用endrefresh方法，使得endrefresh时tableview的contentsize的高度是正确的。
     5.v4.18.0发现，就算按照4的方式去写，也存在一个问题：tableview.rowheight如果是UITableViewAutomaticDimension的时候，reloaddata之后，获取到的tableview.contentsize.height并不正确，导致之后调用endrefresh方法时，判断还是出错，此时无解。。。
     6.4.19.0 这个延时会导致请求cancel的时候，状态不对劲？这句的意思是这样的，在4.19.0以前的我的余额首页（以此为例），切换全部/收入/支出的时候会先cancel掉旧请求，此时会返回success为no的回调，使得state会被置为nomore，然后reset state，置为非nomore。但是由于使得state会被置为nomore加了延时，使得reset之后的非nomore状态，还是被置为了nomore，使得切换过去的tab无法请求下一页。
     ******** 承接第5点，在4.19.0以前的药赚钱首页，在iPhoneX ios11/12，第一页返回5项，这样的条件下（在6s，iOS12也同样有contentsize不对的问题，但是因为屏幕高度的原因，恰好没问题），虽然tableview.rowheight不是UITableViewAutomaticDimension，contentsize在reloaddata之后也还是不对(4.28.0 因为这一页加了section和tableheader)，得延时之后才对，导致判断还是出错。
     ******** 为了应对之前的问题，这个方法仅处理标志位，不处理显示
     */
    //fix, 使用缓存的时候，瞬间就返回了，导致不能缩回去(4.5.0 更新 目前不用缓存了，这个延时会导致请求cancel的时候，状态不对劲)
//    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
    if (!self) {
        return;
    }
    _reloading = NO;// 请求结束
    if (count < _pageSize) {
        // 没有更多了
        _bNoMore = YES;
    } else {
        // 还有下一页
        _bNoMore = NO;
    }
//    });
    /* 4.28.0 endrefresh和tableview reloaddata外面使用使用的时候有可能有先后之分
    reloaddata前，endrefresh后，reloaddata后因为状态不对，显示不正常，endrefresh状态正常之后，再_adjustFrameWithContentSize，使得显示正常
    endrefresh前，reloaddata后，endrefresh状态正常之后，reloaddata使得显示正常*/
    [self _adjustFrameWithContentSize:scrollView];
    
}

// fix
- (void)observeValueForKeyPath:(NSString *)keyPath ofObject:(id)object change:(NSDictionary *)change context:(void *)context
{
    // 不能跟用户交互就直接返回
    if (!self.userInteractionEnabled || self.alpha <= 0.01 || self.hidden) return;
    
    UIScrollView *scrollView = (UIScrollView *)object;
    if ([@"contentOffset" isEqualToString:keyPath]) {
        [self lzzRefreshScrollViewDidScroll:scrollView];
        
    } else if ([@"contentSize" isEqualToString:keyPath]) {
        [self _adjustFrameWithContentSize:scrollView];
        
    }
}

// 加了这个才使得remove的时候调用willmove，这样就能移除观察者
- (void)removeFromSuperview
{
    [super removeFromSuperview];
}

- (void)willMoveToSuperview:(UIView *)newSuperview
{
    [super willMoveToSuperview:newSuperview];
    
    // 旧的父控件
    [self.superview removeObserver:self forKeyPath:@"contentOffset" context:nil];
    [self.superview removeObserver:self forKeyPath:@"contentSize" context:nil];
    
    if (newSuperview) { // 新的父控件
        [newSuperview addObserver:self forKeyPath:@"contentOffset" options:NSKeyValueObservingOptionNew context:nil];
        [newSuperview addObserver:self forKeyPath:@"contentSize" options:NSKeyValueObservingOptionNew context:nil];
        
        [self _adjustFrameWithContentSize:newSuperview];
    }
}

// 4.28.0 改为在这里统一处理显示的问题，因为这里的话，就算autolayout，也是有真实的contentsize了
- (void)_adjustFrameWithContentSize:(UIView *)newSuperview
{
    _scrollView = (UIScrollView *)newSuperview;
    
    // 3.11.0 允许同时存在下拉刷新和上拉加载的UI状态
    
    // 4.28.0 bug34305 改为无论下方这个判断结果如何，都走一下最下方的frame设置，否则autolayout下，列表不满一屏会导致被拦截不再设置frame，此时frame的top为0
    if (_scrollView.contentSize.height < _scrollView.frame.size.height) {
        // 3.9.0 如果不够一屏，重置contentinset, 出现的问题：不够一屏时，tableview也能上拉一段距离
        _scrollView.contentInset = UIEdgeInsetsMake(_scrollView.contentInset.top, 0.0f, 0.0f, 0.0f);
        // 不足一屏的话，隐藏
        [self setState:LZZImmediatePullRefreshHide];
    } else {
        _scrollView.contentInset = UIEdgeInsetsMake(_scrollView.contentInset.top, 0.0f, 48.0f, 0.0f);
        // 大于一屏的话，存在几个可能性
        if (_bNoMore) {
            // 没有更多了
            [self setState:LZZImmediatePullRefreshNoMore];
        } else {
            // 还有下一页
            [self setState:LZZImmediatePullRefreshLoading];
        }
    }
    
    UIScrollView *view = (UIScrollView *)newSuperview;
    // 内容的高度
    CGFloat contentHeight = view.contentSize.height;
    // 表格的高度
    CGFloat scrollHeight = view.frame.size.height;
    // 设置位置和尺寸
    CGFloat y = MAX(contentHeight, scrollHeight);
    CGRect rect = self.frame;
    rect.origin.y = y;
    rect.size.width = newSuperview.size.width;// 4.29.0 拼团首页的listview的宽度变窄了
    self.frame = rect;
}

@end
