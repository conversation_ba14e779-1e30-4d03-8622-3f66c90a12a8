//
//  CustomTitleView.m
//  PPBrowser
//
//  Created by qingbin on 2023/6/10.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import "CustomTitleView.h"

#import "PaddingNewLabel.h"

#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "UIView+FrameHelper.h"
#import "NSObject+Helper.h"

#import "BrowserUtils.h"
#import "NSString+Helper.h"

#import "PPEnums.h"

@interface CustomTitleView ()

@property (nonatomic, strong) UIStackView* stackView;
//正在运行的状态
@property (nonatomic, strong) PaddingNewLabel* runningLabel;
//更新的状态
//@property (nonatomic, strong) PaddingNewLabel* updateLabel;
//标题
@property (nonatomic, strong) UILabel* titleLabel;

@end

@implementation CustomTitleView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if(self) {
        [self addSubviews];
        [self defineLayout];
    }
    
    return self;
}

- (void)updateWithTitle:(NSString *)title
                 status:(CustomTitleViewStatus)status
           updateStatus:(UserScriptUpdateStatus)updateStatus
{
    self.titleLabel.text = title;
    
    //#1CD8D2 绿色
    //#1FA2FF 蓝色
    //#e52d27 红色
    self.runningLabel.hidden = status==CustomTitleViewStatusDefault;
    if(status == CustomTitleViewStatusWorking) {
        self.runningLabel.text = NSLocalizedString(@"tips.userscript.working", nil);
        self.runningLabel.backgroundColor = [UIColor colorWithHexString:@"#1FA2FF"];
    }
    
//    if(updateStatus == UserScriptUpdateStatusDefault
//        || updateStatus == UserScriptUpdateStatusFinishRequest) {
//        self.updateLabel.hidden = YES;
//    } else {
//        self.updateLabel.hidden = NO;
//        
//        if(updateStatus == UserScriptUpdateStatusLoading) {
//            self.updateLabel.backgroundColor = [UIColor colorWithHexString:@"#1FA2FF"];
//            self.updateLabel.text = NSLocalizedString(@"script.update.running", nil);
//        } else if(updateStatus == UserScriptUpdateStatusFailed) {
//            self.updateLabel.backgroundColor = [UIColor colorWithHexString:@"#e52d27"];
//            self.updateLabel.text = NSLocalizedString(@"script.update.fail", nil);
//        }
//    }
}

#pragma mark -- 暗黑模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.titleLabel.textColor = UIColor.whiteColor;
    } else {
        self.titleLabel.textColor = [UIColor colorWithHexString:@"#1A1A1A"];
    }
}

- (void)addSubviews
{
    [self addSubview:self.stackView];
}

- (void)defineLayout
{
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
    
    [self.runningLabel setContentCompressionResistancePriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    [self.runningLabel setContentHuggingPriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    
//    [self.updateLabel setContentCompressionResistancePriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
//    [self.updateLabel setContentHuggingPriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    
    [self.titleLabel setContentCompressionResistancePriority:UILayoutPriorityDefaultHigh forAxis:UILayoutConstraintAxisHorizontal];
    [self.titleLabel setContentHuggingPriority:UILayoutPriorityDefaultHigh forAxis:UILayoutConstraintAxisHorizontal];
}

- (PaddingNewLabel *)runningLabel
{
    if(!_runningLabel) {
        _runningLabel = [PaddingNewLabel new];
        float left = iPadValue(4, 2);
        float top = iPadValue(5, 3);
        _runningLabel.edgeInsets = UIEdgeInsetsMake(left, top, left, top);
        
        _runningLabel.font = [UIFont systemFontOfSize:iPadValue(15, 12)];
        _runningLabel.textColor = UIColor.whiteColor;
        _runningLabel.textAlignment = NSTextAlignmentCenter;
        
        
        _runningLabel.layer.cornerRadius = 5;
        _runningLabel.layer.masksToBounds = YES;
        _runningLabel.hidden = YES;
    }
    
    return _runningLabel;
}

//- (PaddingNewLabel *)updateLabel
//{
//    if(!_updateLabel) {
//        _updateLabel = [PaddingNewLabel new];
//        float left = iPadValue(4, 2);
//        float top = iPadValue(5, 3);
//        _runningLabel.edgeInsets = UIEdgeInsetsMake(left, top, left, top);
//        
//        _updateLabel.font = [UIFont systemFontOfSize:iPadValue(15, 12)];
//        _updateLabel.textColor = UIColor.whiteColor;
//        _updateLabel.textAlignment = NSTextAlignmentCenter;
//        
//        _updateLabel.layer.cornerRadius = 5;
//        _updateLabel.layer.masksToBounds = YES;
//        _updateLabel.hidden = YES;
//    }
//    
//    return _updateLabel;
//}


- (UILabel *)titleLabel
{
    if(!_titleLabel) {
        float font = iPadValue(20, 16);
        _titleLabel = [UIView createLabelWithTitle:@""
                                         textColor:[UIColor colorWithHexString:@"#1A1A1A"]
                                           bgColor:UIColor.clearColor
                                          fontSize:font
                                     textAlignment:NSTextAlignmentLeft
                                             bBold:YES];
    }
    
    return _titleLabel;
}

- (UIStackView *)stackView
{
    if(!_stackView) {
        _stackView = [[UIStackView alloc]initWithArrangedSubviews:@[
            self.runningLabel,
//            self.updateLabel,
            self.titleLabel
        ]];
        
        _stackView.spacing = 5;
        _stackView.axis = UILayoutConstraintAxisHorizontal;
    }
    
    return _stackView;
}

@end
