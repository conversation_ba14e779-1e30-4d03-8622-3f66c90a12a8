//
//  CustomTitleAndImageView.h
//  YSBPro
//
//  Created by qing<PERSON> on 2024/1/5.
//  Copyright © 2024 lu lucas. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN
/// 可自定义autolayout的图片+文字view
@interface CustomTitleAndImageView : UIView

- (instancetype)initWithLayout:(void (^)(CustomTitleAndImageView* view, UIImageView* imageView, UILabel* titleLabel))block;

// 更新事件
- (void)updateWith:(void(^)(CustomTitleAndImageView* view, UIImageView* imageView, UILabel* titleLabel))block;

@property (nonatomic, copy) void (^tapAction)(void);

//图片
@property (readonly) UIImageView *imageView;
//标题
@property (readonly) UILabel *titleLabel;
//
@property (nonatomic, strong) UITapGestureRecognizer *tapGesture;

@end

NS_ASSUME_NONNULL_END
