//
//  CommonTextAndArrowView.h
//  PPBrowser
//
//  Created by qing<PERSON> on 2022/6/6.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "ThemeProtocol.h"

NS_ASSUME_NONNULL_BEGIN
//图标+标题+内容+箭头
@interface CommonTextAndArrowView : UIView<ThemeProtocol>

- (void)updateWithTitle:(NSString *)title
                content:(NSString *)content
                   icon:(NSString *)icon
    iconBackgroundColor:(NSString *)iconBackgroundColor
               showLine:(BOOL)showLine;

- (void)updateWithTitle:(NSString *)title;
- (void)updateWithContent:(NSString *)content;

+ (float)height;

@property (nonatomic, copy) void (^didAction)(void);

@end

NS_ASSUME_NONNULL_END
