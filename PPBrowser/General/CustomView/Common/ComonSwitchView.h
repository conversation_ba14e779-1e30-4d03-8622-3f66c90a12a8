//
//  ComonSwitchView.h
//  PPBrowser
//
//  Created by qing<PERSON> on 2022/6/6.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "ThemeProtocol.h"

NS_ASSUME_NONNULL_BEGIN
//图标+标题+开关控件
@interface ComonSwitchView : UIView<ThemeProtocol>

- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)new NS_UNAVAILABLE;

- (instancetype)initWithTitle:(NSString *)title
                         icon:(NSString *)icon
          iconBackgroundColor:(NSString *)iconBackgroundColor
                     showLine:(BOOL)showLine
                      showVip:(BOOL)showVip;

+ (float)height;

- (void)updateWithTitle:(NSString *)title;
- (void)updateWithIsOn:(BOOL)isOn;

@property (nonatomic, copy) void (^didSwithAction)(BOOL isOn);

@end

NS_ASSUME_NONNULL_END
