//
//  ComonSwitchView.m
//  PPBrowser
//
//  Created by qingbin on 2022/6/6.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "ComonSwitchView.h"

#import "Masonry.h"
#import "MaizyHeader.h"
#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "ReactiveCocoa.h"
#import "UIView+FrameHelper.h"
#import "UIViewController+Helper.h"
#import "NSObject+Helper.h"
#import "BrowserUtils.h"

#import "PPNotifications.h"
#import "PaddingNewLabel.h"

@interface ComonSwitchView ()
//图标背景
@property (nonatomic, strong) UIView *iconBackView;
//图标
@property (nonatomic, strong) UIImageView *icon;
//标题
@property (nonatomic, strong) UILabel* titleLabel;
//开关控件
@property (nonatomic, strong) UISwitch *switchView;
//vip
@property (nonatomic, strong) PaddingNewLabel *vipLabel;
//箭头
@property (nonatomic, strong) UIImageView* arrow;
//分割线
@property (nonatomic, strong) UIView* line;

@end

@implementation ComonSwitchView

- (instancetype)initWithTitle:(NSString *)title
                         icon:(NSString *)icon
          iconBackgroundColor:(NSString *)iconBackgroundColor
                     showLine:(BOOL)showLine
                      showVip:(BOOL)showVip
{
    self = [super init];
    
    if(self) {
        self.titleLabel.text = title;
        self.icon.image = [UIImage imageNamed:icon];
        self.iconBackView.backgroundColor = [UIColor colorWithHexString:iconBackgroundColor];
        self.line.hidden = !showLine;
        self.vipLabel.hidden = !showVip;
        
        [self addSubviews];
        [self defineLayout];
        [self setupObservers];
        
        [self applyTheme];
    }
    
    return self;
}

- (void)layoutSubviews
{
    [super layoutSubviews];
    
    self.vipLabel.layer.cornerRadius = CGRectGetHeight(self.vipLabel.frame) / 2.0;
}

- (void)dealloc
{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        self.titleLabel.textColor = [UIColor colorWithHexString:kDarkThemeColorText];
        self.line.backgroundColor = [UIColor colorWithHexString:kDarkThemeColorLine];
    } else {
        self.backgroundColor = UIColor.whiteColor;
        self.titleLabel.textColor = [UIColor colorWithHexString:@"#333333"];
        self.line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
}

//修改暗黑模式
- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}

- (void)updateWithTitle:(NSString *)title
{
    self.titleLabel.text = title;
}

- (void)updateWithIsOn:(BOOL)isOn
{
    self.switchView.on = isOn;
}

+ (float)height
{
    if([BrowserUtils isiPad]) {
        return 88;
    } else {
        return 60;
    }
}

- (void)setupObservers
{
    @weakify(self)
    [[self.switchView rac_newOnChannel] subscribeNext:^(id x) {
        @strongify(self)
        if(self.didSwithAction) {
            self.didSwithAction(self.switchView.isOn);
        }
    }];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(darkThemeDidChangeNotification:)
                                                 name:kDarkThemeDidChangeNotification
                                               object:nil];
}

#pragma mark - layout

- (void)addSubviews
{
    [self addSubview:self.iconBackView];
    [self.iconBackView addSubview:self.icon];
    [self addSubview:self.titleLabel];
    [self addSubview:self.switchView];
    [self addSubview:self.vipLabel];
}

- (void)defineLayout
{
    float offset = iPadValue(30, 15);
    [self.iconBackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(offset);
        make.centerY.equalTo(self);
        make.size.mas_equalTo(iPadValue(48, 32));
    }];
    
    [self.icon mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.iconBackView);
        make.size.mas_equalTo(iPadValue(24, 16));
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.iconBackView.mas_right).offset(offset);
        make.centerY.equalTo(self);
    }];
    
    [self.switchView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_offset(-offset);
        make.centerY.equalTo(self);
    }];
    
    [self.vipLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self);
        make.left.equalTo(self.switchView.mas_right).offset(iPadValue(10, 8));
    }];
    
    if([BrowserUtils isiPad]) {
        //iPad
        self.switchView.transform = CGAffineTransformMakeScale(1.2, 1.2);
    }
    
    UIView* line = [UIView new];
    line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    [self addSubview:line];
    [line mas_makeConstraints:^(MASConstraintMaker *make) {
       make.left.mas_offset(0);
       make.right.mas_offset(-0);
       make.height.mas_equalTo(0.5);
       make.bottom.mas_offset(0);
    }];
    self.line = line;
}

#pragma mark - getters

- (UIView *)iconBackView
{
    if (!_iconBackView) {
        _iconBackView = [UIView new];
    }
    return _iconBackView;
}

- (UIImageView *)icon
{
    if(!_icon) {
        _icon = [UIImageView new];
        _icon.contentMode = UIViewContentModeScaleAspectFit;
    }
    return _icon;
}

- (UISwitch *)switchView
{
    if(!_switchView) {
        _switchView = [UISwitch new];
        _switchView.on = YES;
    }
    
    return _switchView;
}

- (PaddingNewLabel *)vipLabel
{
    if (!_vipLabel) {
        _vipLabel = [PaddingNewLabel new];
        _vipLabel.text = @"Pro";
        _vipLabel.textColor = UIColor.whiteColor;
        _vipLabel.font = [UIFont systemFontOfSize:iPadValue(16, 12)];
        _vipLabel.backgroundColor = [UIColor colorWithHexString:@"#007aff"];
        _vipLabel.edgeInsets = UIEdgeInsetsMake(2, 8, 2, 8);
        _vipLabel.layer.masksToBounds = YES;
        _vipLabel.hidden = YES;
    }
    
    return _vipLabel;
}

- (UILabel *)titleLabel
{
    if(!_titleLabel) {
        float font = iPadValue(20, 16);
        _titleLabel = [UIView createLabelWithTitle:nil
                                        textColor:[UIColor colorWithHexString:@"#333333"]
                                          bgColor:UIColor.clearColor
                                         fontSize:font
                                    textAlignment:NSTextAlignmentLeft
                                            bBold:NO];
    }
    
    return _titleLabel;
}

@end


