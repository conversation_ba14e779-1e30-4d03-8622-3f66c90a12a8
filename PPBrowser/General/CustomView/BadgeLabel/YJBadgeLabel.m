//
//  YJBadgeLabel.m
//  iAround
//
//  Created by liyy on 12-12-18.
//
//

#import "YJBadgeLabel.h"
#import "UIColor+Helper.h"
#import "Masonry.h"
#import "UIView+FrameHelper.h"

// 外框与内框的差值
#define kPadding 2.0f
@interface BadgePaddingLabel : UILabel
@property (nonatomic, assign) UIEdgeInsets edgeInsets;
@end
@implementation BadgePaddingLabel
- (id)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        self.edgeInsets = UIEdgeInsetsMake(0, 0, 0, 0);
        self.textAlignment = NSTextAlignmentCenter;
    }
    return self;
}
- (instancetype)init{
    if (self = [super init]) {
        self.edgeInsets = UIEdgeInsetsMake(0, 0, 0, 0);
        self.textAlignment = NSTextAlignmentCenter;
    }
    return self;
}
- (void)drawRect:(CGRect)rect
{
    [super drawTextInRect:UIEdgeInsetsInsetRect(rect, self.edgeInsets)];
}

- (CGSize)intrinsicContentSize
{
    CGSize size = [super intrinsicContentSize];
    size.width += self.edgeInsets.left + self.edgeInsets.right;
    size.height += self.edgeInsets.top + self.edgeInsets.bottom;
    return size;
}
@end

@interface YJBadgeLabel ()

{
    UIFont *_fontSize;
    BadgeType _type;
}
@property (nonatomic, strong) UIView *backView;
@property (nonatomic, strong) BadgePaddingLabel *valueLabel;

@end

@implementation YJBadgeLabel

- (instancetype)initWithFontSize:(UIFont *)fontSize type:(BadgeType)type
{
    self = [super init];
    if (self) {
        _fontSize = fontSize;
        _type = type;
        self.userInteractionEnabled = NO;
        [self _addSubviews];
        [self _defineLayout];
        
        switch (_type) {
            case BadgeTypeRedBackground:
            {
                self.backView.backgroundColor = [UIColor colorWithHexString:@"#f73226"];
                self.valueLabel.textColor = [UIColor whiteColor];
            }
                break;
            case BadgeTypeLightRedBackground:
            {
                self.backView.backgroundColor = [UIColor colorWithHexString:@"#f9544f"];
                self.valueLabel.textColor = [UIColor whiteColor];
            }
                break;
            case BadgeTypeRedBackgroundWhiteBorder:
            {
                self.valueLabel.backgroundColor = [UIColor colorWithHexString:@"#f73226"];
                self.valueLabel.textColor = [UIColor whiteColor];
                self.backView.backgroundColor = [UIColor whiteColor];
            }
                break;
            case BadgeTypeWhiteBackground:
            {
                self.valueLabel.backgroundColor = [UIColor whiteColor];
                self.valueLabel.textColor = [UIColor colorWithHexString:@"#999999"];
                self.backView.backgroundColor = [UIColor colorWithHexString:@"#999999"];
            }
                break;
            case BadgeTypeWhiteBackgroundOrangeBorder:
            {
                self.valueLabel.backgroundColor = [UIColor whiteColor];
                self.valueLabel.textColor = [UIColor colorWithRed:0xFE / 255.0f green:0x5C / 255.0f blue:0x02 / 255.0f alpha:1.0f];
                self.backView.backgroundColor = [UIColor colorWithRed:0xFE / 255.0f green:0x5C / 255.0f blue:0x02 / 255.0f alpha:1.0f];
            }
                break;
            case BadgeTypeWhiteBackgroundRedBorder:
            {
                self.valueLabel.backgroundColor = [UIColor whiteColor];
                self.valueLabel.textColor = [UIColor colorWithRed:255.0f/255.0f green:32.0f/255.0f blue:32.0f/255.0f alpha:1.0f];
                self.backView.backgroundColor = [UIColor colorWithRed:255.0f/255.0f green:32.0f/255.0f blue:32.0f/255.0f alpha:1.0f];
            }
                break;
            case BadgeTypeOrangeBackgroundWhiteBorder:
            {
                self.valueLabel.backgroundColor = [UIColor colorWithHexString:@"#fd5c02"];
                self.valueLabel.textColor = [UIColor colorWithHexString:@"#ffffff"];
                self.backView.backgroundColor = [UIColor colorWithHexString:@"#ffffff"];
            }
                break;
            default:
                break;
        }
    }
    return self;
}

- (void)_addSubviews
{
    [self addSubview:self.backView];
    [self addSubview:self.valueLabel];
}

- (void)_defineLayout
{
    [self.backView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.top.bottom.equalTo(self);
        make.height.equalTo(@(_fontSize.lineHeight + kPadding));
    }];
    [self.valueLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.backView).offset(kPadding/2);
        make.bottom.equalTo(self.backView).offset(-kPadding/2);
        make.left.equalTo(self.backView).offset(kPadding/2);
        make.right.equalTo(self.backView).offset(-kPadding/2);
    }];
}

- (UIView *)backView
{
    if (!_backView) {
        _backView = [[UIView alloc] init];
        
        _backView.layer.cornerRadius = (_fontSize.lineHeight + kPadding)/2;
        _backView.layer.masksToBounds = YES;
    }
    return _backView;
}

- (BadgePaddingLabel *)valueLabel
{
    if (!_valueLabel) {
        _valueLabel = [[BadgePaddingLabel alloc] init];
        
        _valueLabel.layer.cornerRadius = _fontSize.lineHeight/2;
        _valueLabel.layer.masksToBounds = YES;
        _valueLabel.font = _fontSize;
        _valueLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _valueLabel;
}

- (void)setValue:(NSInteger)value
{
    if (value == 0) {
        self.hidden = YES;
    } else {
        self.hidden = NO;
    }
    
    NSInteger max = self.maxAmount;
    if (max == 0) {
        max = 99;
    }
    
    if (value <= max) {
        self.valueLabel.text = @(value).stringValue;
    } else {
        self.valueLabel.text = [NSString stringWithFormat:@"%@+", @(max)];
    }
    [self _updateWidth];
}

- (void)setStringValue:(NSString *)value
{
    self.valueLabel.text = value;
    [self _updateWidth];
}

- (void)_updateWidth
{
    
    // 如果是一位数字就用lineHeight，超过一位的都弄宽一点
    if (self.valueLabel.text.length < 2) {
        [self.valueLabel mas_updateConstraints:^(MASConstraintMaker *make) {
            make.width.equalTo(@(_fontSize.lineHeight));
        }];
    } else {
        // 计算宽度
        NSDictionary * tdic = [NSDictionary dictionaryWithObjectsAndKeys:_fontSize, NSFontAttributeName,nil];
        CGSize size = CGSizeMake(1000.0f, 20000.0f);
        CGFloat width = [self.valueLabel.text boundingRectWithSize:size options:NSStringDrawingUsesLineFragmentOrigin | NSStringDrawingUsesFontLeading attributes:tdic context:nil].size.width;
        [self.valueLabel mas_updateConstraints:^(MASConstraintMaker *make) {
            make.width.equalTo(@(width + 8.0f/*左右加点间距，否则太挤了*/));
        }];
    }
}

@end

