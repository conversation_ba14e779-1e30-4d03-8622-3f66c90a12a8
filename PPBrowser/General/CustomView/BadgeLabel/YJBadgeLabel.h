//
//  YJBadgeLabel.h
//  iAround
//
//  Created by liyy on 12-12-18.
//
//

#import <UIKit/UIKit.h>
#import <Foundation/Foundation.h>

typedef enum : NSUInteger {
    BadgeTypeRedBackground = 0, // 红底白字无边
    BadgeTypeRedBackgroundWhiteBorder = 1, // 红底白字白边
    BadgeTypeWhiteBackground = 2, // 白底黑字无边
    BadgeTypeWhiteBackgroundOrangeBorder = 3, // 白底橙字橙边
    BadgeTypeLightRedBackground = 4, // 浅红底白字无边
	BadgeTypeWhiteBackgroundRedBorder = 5, // 白底红字红边
    BadgeTypeOrangeBackgroundWhiteBorder = 6, //橙底白字白边
} BadgeType;

@interface YJBadgeLabel : UIView

// 不要用frame或者autolayout的方式去初始化，通过设置center控制位置，控件的大小由内部控制
// 店员版4.28.0 修改为使用autolayout控制显示
- (instancetype)initWithFontSize:(UIFont *)fontSize type:(BadgeType)type;

- (void)setValue:(NSInteger)value;

- (void)setStringValue:(NSString *)value;

@property (nonatomic, assign) NSInteger maxAmount;

@end
