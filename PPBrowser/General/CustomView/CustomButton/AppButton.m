//
//  AppButton.m
//  Reader
//
//  Created by qing<PERSON> on 2023/9/14.
//

#import "AppButton.h"

#import "Masonry.h"
#import "ReactiveCocoa.h"

#import "UIView+Helper.h"
#import "UIColor+Helper.h"

@interface AppButton ()

@property (nonatomic, strong) UIStackView *stackView;

@property (nonatomic, strong) UILabel *titleLabel;

@property (nonatomic, strong) UIImageView *imageView;

@end

@implementation AppButton

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if(self) {
        [self addSubviews];
        [self defineLayout];
        [self handleEvents];
    }
    
    return self;
}

- (void)setTitle:(NSString *)title
{
    self.titleLabel.text = title;
}

- (void)setImage:(UIImage *)image
{
    self.imageView.image = image;
}

- (void)setTitleColor:(UIColor *)color
{
    self.titleLabel.textColor = color;
}

#pragma mark -- handle events

- (void)handleEvents
{
    UITapGestureRecognizer* tap = [UITapGestureRecognizer new];
    [self addGestureRecognizer:tap];
    @weakify(self)
    [tap.rac_gestureSignal subscribeNext:^(id x) {
        @strongify(self)
        if(self.tapAction) {
            self.tapAction();
        }
    }];
}

#pragma mark -- layout

- (void)addSubviews
{
    [self addSubview:self.stackView];
}

- (void)defineLayout
{
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self);
    }];
}

#pragma mark -- Getter

- (UILabel *)titleLabel
{
    if(!_titleLabel) {
        _titleLabel = [UIView createLabelWithTitle:@""
                                              textColor:[UIColor colorWithHexString:@"#333333"]
                                                bgColor:UIColor.clearColor
                                               fontSize:15
                                          textAlignment:NSTextAlignmentLeft
                                                  bBold:NO];
    }
    
    return _titleLabel;
}

- (UIImageView *)imageView
{
    if(!_imageView) {
        _imageView = [UIImageView new];
        _imageView.contentMode = UIViewContentModeScaleAspectFit;
    }
    
    return _imageView;
}

- (UIStackView *)stackView
{
    if(!_stackView) {
        _stackView = [[UIStackView alloc]init];
    }
    
    return _stackView;
}

@end
