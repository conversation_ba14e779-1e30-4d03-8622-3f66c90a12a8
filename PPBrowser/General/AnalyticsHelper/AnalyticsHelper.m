//
//  AnalyticsHelper.m
//  MaizyClock
//
//  Created by qing<PERSON> on 2022/2/15.
//

#import "AnalyticsHelper.h"
#import "MaizyHeader.h"

#if FOCUS_UMENG_TF

#import <UMCommon/MobClick.h>
#import <UMCommon/UMCommon.h>
#import <UMCommonLog/UMCommonLogHeaders.h>

#endif

/// https://developer.umeng.com/docs/119267/detail/119517
@interface AnalyticsHelper ()

@property (nonatomic, strong) NSMutableDictionary *failMap;

@end

@implementation AnalyticsHelper

+ (instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    static AnalyticsHelper* obj;
    dispatch_once(&onceToken, ^{
        obj = [AnalyticsHelper new];
    });
    
    return obj;
}

- (instancetype)init
{
    self = [super init];
    if(self) {
        self.failMap = [NSMutableDictionary dictionary];
    }
    
    return self;
}


+ (void)startUMengService
{
    //开发者需要显式的调用此函数，日志系统才能工作
#if FOCUS_UMENG_TF
    [UMCommonLogManager setUpUMCommonLogManager];
    [UMConfigure setLogEnabled:bLogSwitch];
    [UMConfigure initWithAppkey:kUMengAppKey channel:sChannelId];

    //设置为自动采集页面
    [MobClick setAutoPageEnabled:YES];
#endif
}

/// 事件统计
+ (void)event:(NSString *)event
   attributes:(NSDictionary *)attributes
{
#if FOCUS_UMENG_TF
    if(attributes == nil) {
        attributes = @{};
    }
    
    [MobClick event:event attributes:attributes];
#endif
}

@end
