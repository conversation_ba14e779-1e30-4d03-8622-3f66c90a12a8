// swift-tools-version: 5.9
// The swift-tools-version declares the minimum version of Swift required to build this package.

import PackageDescription

let package = Package(
    name: "ContentBlockerConverter",
    products: [
        // Products define the executables and libraries a package produces, making them visible to other packages.
        .library(
            name: "ContentBlockerConverter",
            type: .static,
            targets: ["ContentBlockerConverter", "ContentBlockerEngine"]),
    ],
    dependencies: [
        // Dependencies declare other packages that this package depends on.
        // .package(url: /* package url */, from: "1.0.0"),
        .package(path: "../PunycodeSwift"),
    ],
    targets: [
        // Targets are the basic building blocks of a package, defining a module or a test suite.
        // Targets can depend on other targets in this package and products from dependencies.
        .target(
            name: "ContentBlockerConverter",
            dependencies: ["Shared"]),
        .target(
            name: "ContentBlockerEngine",
            dependencies: ["ContentBlockerConverter", "Shared"]),
        .target(
            name: "Shared")
    ]
)
