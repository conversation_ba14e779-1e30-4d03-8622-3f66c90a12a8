language: objective-c
os: osx
osx_image: xcode11.5

branches:
  only:
    - master
    - develop
#    - "/^v?[0-9\\.]+/"

cache:
  bundler: true
  cocoapods: true
  directories:
    - Carthage

env:
  global:
    - XCODE_PROJECT="Punycode.xcodeproj"
    - IOS_SCHEME="Punycode-iOS"
    - TVOS_SCHEME="Punycode-tvOS"
    - MACOS_SCHEME="Punycode-macOS"

matrix:
  include:
    # iOS Tests
    - env: SCHEME="$IOS_SCHEME"   DESTINATION="OS=13.5,name=iPhone 11 Pro"  RUN_TESTS="YES" LINT="NO"  COVERAGE="YES" CODECOV_FLAG="ios13"
    - env: SCHEME="$IOS_SCHEME"   DESTINATION="OS=12.4,name=iPhone X"       RUN_TESTS="YES" LINT="NO"  COVERAGE="YES" CODECOV_FLAG="ios12"
    - env: SCHEME="$IOS_SCHEME"   DESTINATION="OS=11.4,name=iPhone 7"       RUN_TESTS="YES" LINT="YES" COVERAGE="NO"  CODECOV_FLAG="ios11"
    - env: SCHEME="$IOS_SCHEME"   DESTINATION="OS=10.3.1,name=iPhone 6"     RUN_TESTS="YES" LINT="NO"  COVERAGE="NO"  CODECOV_FLAG="ios10"
    # tvOS Tests
    - env: SCHEME="$TVOS_SCHEME"  DESTINATION="OS=13.4,name=Apple TV 4K"    RUN_TESTS="YES" LINT="NO"  COVERAGE="NO"  CODECOV_FLAG="tvos13"
    - env: SCHEME="$TVOS_SCHEME"  DESTINATION="OS=12.4,name=Apple TV 4K"    RUN_TESTS="YES" LINT="NO"  COVERAGE="NO"  CODECOV_FLAG="tvos12"
    - env: SCHEME="$TVOS_SCHEME"  DESTINATION="OS=11.4,name=Apple TV"       RUN_TESTS="YES" LINT="NO"  COVERAGE="NO"  CODECOV_FLAG="tvos11"
    # macOS Tests
    - env: SCHEME="$MACOS_SCHEME" DESTINATION="arch=x86_64"                 RUN_TESTS="YES" LINT="NO"  COVERAGE="NO"  CODECOV_FLAG="macos"

before_install:
  - if [ "$TRAVIS_OS_NAME" == "osx" ]; then
    gem install cocoapods --no-document --quiet;
    gem install xcpretty --no-document --quiet;
    pod repo update;
    brew update;
    brew outdated carthage || brew upgrade carthage;
    fi

install:
  - ./install-swiftlint.sh;

script:
  - set -o pipefail;
  - swift -version;

  - if [ "$TRAVIS_OS_NAME" == "osx" ]; then
    xcrun simctl list;
    xcodebuild -version;
    xcodebuild -showsdks;
    xcodebuild -list;
    fi

  - if [ "$RUN_TESTS" == "YES" ]; then
    xcodebuild clean test -project "$XCODE_PROJECT" -scheme "$SCHEME" -destination "$DESTINATION" -enableCodeCoverage $COVERAGE | xcpretty -c;
    elif [ "$RUN_TESTS" == "NO" ]; then
    xcodebuild clean build -project "$XCODE_PROJECT" -scheme "$SCHEME" -destination "$DESTINATION" -configuration Release | xcpretty -c;
    fi

  - if [ "$LINT" == "YES" ]; then
    swiftlint;
    carthage build --no-skip-current;
    fi

after_success:
  - if [ "$COVERAGE" == "YES" ]; then
    bash <(curl -s https://codecov.io/bash) -J 'Punycode' -cF "$CODECOV_FLAG";
    fi
