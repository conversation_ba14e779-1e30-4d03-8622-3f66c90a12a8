/*
 Copyright (c) 2012-2019, <PERSON><PERSON><PERSON>
 All rights reserved.
 
 Redistribution and use in source and binary forms, with or without
 modification, are permitted provided that the following conditions are met:
 * Redistributions of source code must retain the above copyright
 notice, this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 notice, this list of conditions and the following disclaimer in the
 documentation and/or other materials provided with the distribution.
 * The name of <PERSON><PERSON><PERSON> may not be used to endorse
 or promote products derived from this software without specific
 prior written permission.
 
 THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 DISCLAIMED. IN NO EVENT SHALL PIERRE-OLIVIER LATOUR BE LIABLE FOR ANY
 DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CO<PERSON>EQUENTIAL DAMAGES
 (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

.row-file {
  height: 40px;
}

.column-icon {
  width: 40px;
  text-align: center;
}

.column-name {
}

.column-size {
  width: 100px;
  text-align: right;
}

.column-move {
  width: 40px;
  text-align: center;
}

.column-delete {
  width: 40px;
  text-align: center;
}

.column-path {  
}

.column-progress {
  width: 200px;
}

.footer {
  color: #999;
  text-align: center;
  font-size: 0.9em;
}

#reload {
  float: right;
}

#create-input {
  width: 50%;
  height: 20px;
}

#move-input {
  width: 80%;
  height: 20px;
}

/* Bootstrap overrides */

.btn:focus {
  outline: none;  /* FIXME: Work around for Chrome only but still draws focus ring while button pressed */
}

.btn-toolbar {
  margin-top: 30px;
  margin-bottom: 20px;
}

.table .progress {
  margin-top: 0px;
  margin-bottom: 0px;
  height: 16px;
}

.panel-default > .panel-heading {
  color: #555;
}

.breadcrumb {
  background-color: transparent;
  border-radius: 0px;
  margin-bottom: 0px;
  padding: 0px;
}

.breadcrumb > .active {
  color: #555;
}

.breadcrumb > li + li:before {
  color: #999;
}

.table > tbody > tr > td {
  vertical-align: middle;
}

.table > tbody > tr > td > p {
  margin: 0px;
}

/* Initial state */

.uploading {
  display: none;
}
