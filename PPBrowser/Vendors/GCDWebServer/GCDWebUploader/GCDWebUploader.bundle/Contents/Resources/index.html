<!--
 Copyright (c) 2012-2019, <PERSON><PERSON><PERSON>
 All rights reserved.
 
 Redistribution and use in source and binary forms, with or without
 modification, are permitted provided that the following conditions are met:
 * Redistributions of source code must retain the above copyright
 notice, this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 notice, this list of conditions and the following disclaimer in the
 documentation and/or other materials provided with the distribution.
 * The name of <PERSON><PERSON><PERSON> may not be used to endorse
 or promote products derived from this software without specific
 prior written permission.
 
 THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 DISCLAIMED. IN NO EVENT SHALL PIERRE-OLIVIER LATOUR BE LIABLE FOR ANY
 DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF <PERSON>UBSTITUTE GOODS OR SERVICES;
 LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
-->

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>%title%</title>
    <link rel="stylesheet" href="css/bootstrap.css">
    <link rel="stylesheet" href="css/bootstrap-theme.css">
    <link rel="stylesheet" href="css/jquery.fileupload.css">
    <link rel="stylesheet" href="css/index.css">
    <!--[if lt IE 9]>
      <script type="text/javascript" src="js/html5shiv.min.js"></script>
      <script type="text/javascript" src="js/respond.min.js"></script>
    <![endif]-->
    <script type="text/javascript" src="js/jquery.min.js"></script>
    <script type="text/javascript" src="js/jquery.ui.widget.js"></script>
    <script type="text/javascript" src="js/jquery.jeditable.js"></script>
    <script type="text/javascript" src="js/jquery.fileupload.js"></script>
    <script type="text/javascript" src="js/jquery.iframe-transport.js"></script>
    <script type="text/javascript" src="js/bootstrap.min.js"></script>
    <script type="text/javascript" src="js/tmpl.min.js"></script>
    <script type="text/javascript">
      var _device = "%device%";
    </script>
    <script type="text/javascript" src="js/index.js"></script>
  </head>
  <body>
    
    <div class="container">
      
      <div class="page-header">
        <h1>%header%</h1>
      </div>
      
      %prologue%
      
      <div id="alerts"></div>
      
      <div class="btn-toolbar">
        <button type="button" class="btn btn-primary fileinput-button" id="upload-file">
          <span class="glyphicon glyphicon-upload"></span> Upload Files&hellip;
          <input id="fileupload" type="file" name="files[]" multiple>
        </button>
        <button type="button" class="btn btn-success" id="create-folder">
          <span class="glyphicon glyphicon-folder-close"></span> Create Folder&hellip;
        </button>
        <button type="button" class="btn btn-default" id="reload">
          <span class="glyphicon glyphicon-refresh"></span> Refresh
        </button>
      </div>
      
      <div class="panel panel-default uploading">
        <div class="panel-heading">File Uploads in Progress</div>
        <table class="table table-striped"><tbody id="uploads"></tbody></table>
      </div>
      
      <div class="panel panel-default">
        <div class="panel-heading">
          <ol class="breadcrumb" id="path"></ol>
        </div>
        <table class="table table-striped"><tbody id="listing"></tbody></table>
      </div>
      
      %epilogue%
      
      <p class="footer">%footer%</p>
      
    </div>
    
    <div class="modal fade" id="create-modal" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal">&times;</button>
            <h4 class="modal-title">Create Folder</h4>
          </div>
          <div class="modal-body">
            <p>Please enter the name of the folder to be created:</p>
            <form onsubmit="return false">
              <input type="text" autocomplete="off" id="create-input">
            </form>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
            <button type="button" class="btn btn-primary" id="create-confirm">Create Folder</button>
          </div>
        </div>
      </div>
    </div>
    
    <div class="modal fade" id="move-modal" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal">&times;</button>
            <h4 class="modal-title">Move Item</h4>
          </div>
          <div class="modal-body">
            <p>Please enter the new location for this item:</p>
            <form onsubmit="return false">
              <input type="text" autocomplete="off" id="move-input">
            </form>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
            <button type="button" class="btn btn-primary" id="move-confirm">Move Item</button>
          </div>
        </div>
      </div>
    </div>
    
    <script type="text/x-tmpl" id="template-listing">
      <tr class="row-file">
        <td class="column-icon">
          {% if (o.size != null) { %}
            <button type="button" class="btn btn-default btn-xs button-download">
              <span class="glyphicon glyphicon-download-alt"></span>
            </button>
          {% } else { %}
            <button type="button" class="btn btn-default btn-xs button-open">
              <span class="glyphicon glyphicon-folder-open"></span>
            </button>
          {% } %}
        </td>
        <td class="column-name"><p class="edit">{%=o.name%}</p></td>
        <td class="column-size">
          {% if (o.size != null) { %}
            <p>{%=formatFileSize(o.size)%}</p>
          {% } %}
        </td>
        <td class="column-move">
          <button type="button" class="btn btn-default btn-xs button-move">
            <span class="glyphicon glyphicon glyphicon-share-alt"></span>
          </button>
        </td>
        <td class="column-delete">
          <button type="button" class="btn btn-danger btn-xs button-delete">
            <span class="glyphicon glyphicon-trash"></span>
          </button>
        </td>
      </tr>
    </script>
    
    <script type="text/x-tmpl" id="template-uploads">
      <tr class="row-file">
        <td class="column-icon">
          <button type="button" class="btn btn-warning btn-xs button-cancel">
            <span class="glyphicon glyphicon-ban-circle"></span>
          </button>
        </td>
        <td class="column-path"><p>{%=o.path%}</p></td>
        <td class="column-progress">
          <div class="progress">
            <div class="progress-bar" id="progress-bar"></div>
          </div>
        </ts>
      </tr>
    </script>
    
    <script type="text/x-tmpl" id="template-alert">
      <div class="alert alert-{%=o.level%} alert-dismissable">
        <button type="button" class="close" data-dismiss="alert">&times;</button>
        <strong>{%=o.title%}</strong>{%=o.description%}
      </div>
    </script>
    
  </body>
</html>
