//
//  AMSlidingAnimationType.swift
//  AMSlideMenu
//
// The MIT License (MIT)
//
// Created by : arturdev
// Copyright (c) 2020 arturdev. All rights reserved.
//
// Permission is hereby granted, free of charge, to any person obtaining a copy of
// this software and associated documentation files (the "Software"), to deal in
// the Software without restriction, including without limitation the rights to
// use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
// the Software, and to permit persons to whom the Software is furnished to do so,
// subject to the following conditions:
//
// The above copyright notice and this permission notice shall be included in all
// copies or substantial portions of the Software.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
// FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
// COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
// IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
// CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE

import UIKit

public struct AMSlidingAnimationOptions: OptionSet {

    public let rawValue: Int
    public var animator: AMSlidingAnimatorProtocol?
	public var tag: Int?

    public static let slidingMenu = AMSlidingAnimationOptions(rawValue: 1 << 1)
	public static let fixedMenu = AMSlidingAnimationOptions(rawValue: 1 << 2)
	public static let content = AMSlidingAnimationOptions(rawValue: 1 << 3)
    public static let blurBackground = AMSlidingAnimationOptions(rawValue: 1 << 4)
    public static let menuShadow = AMSlidingAnimationOptions(rawValue: 1 << 5)
	public static let contentShadow = AMSlidingAnimationOptions(rawValue: 1 << 6)
    public static let dimmedBackground = AMSlidingAnimationOptions(rawValue: 1 << 7)

	internal static var customOptions = [AMSlidingAnimationOptions]()

	public static func custom(_ animator: AMSlidingAnimatorProtocol, tag: Int) -> AMSlidingAnimationOptions {
		if let index = customOptions.firstIndex(where: {$0.tag == tag}) {
			customOptions.remove(at: index)
		}
		var custom = AMSlidingAnimationOptions(rawValue: 1 << (7 + customOptions.count))
        custom.animator = animator
		custom.tag = tag
		customOptions.append(custom)
        return custom
    }
    
    public init(rawValue: Int) {
        self.rawValue = rawValue
    }
}
