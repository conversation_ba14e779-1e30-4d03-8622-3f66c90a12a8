//
//  MBCustomView.m
//  PPBrowser
//
//  Created by qingbin on 2022/6/2.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "MBCustomView.h"

#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "NSString+Helper.h"
#import "UIView+FrameHelper.h"
#import "NSObject+Helper.h"

#import "ThemeProtocol.h"
#import "PreferenceManager.h"

#define kActionHeight 24

@interface MBCustomView ()<ThemeProtocol>

@property (nonatomic, strong) UILabel *msgLabel;

@property (nonatomic, strong) UIButton *actionBtn;

@property (nonatomic, strong) NSString* msg;

@property (nonatomic, strong) NSString* actionMsg;

@property (nonatomic, copy) void (^actionBlock)(void);

@end

@implementation MBCustomView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if(self) {
        [self addSubviews];
        [self setupObservers];
        
//        self.backgroundColor = [UIColor.blackColor colorWithAlphaComponent:0.7];
        self.backgroundColor = UIColor.blackColor;
        self.alpha = 0;
        self.transform = CGAffineTransformConcat(CGAffineTransformIdentity, CGAffineTransformMakeScale(0.5f, 0.5f));
        
        [self applyTheme];
    }
    
    return self;
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.backgroundColor = UIColor.whiteColor;
        self.msgLabel.textColor = UIColor.blackColor;
    } else {
        self.backgroundColor = UIColor.blackColor;
        self.msgLabel.textColor = UIColor.whiteColor;
    }
}

+ (instancetype)showCustomMessage:(UIView *)view
                          message:(NSString *)msg
                        actionMsg:(NSString *)actionMsg
                           action:(void(^)(void))actionBlock
{
    UIView* superView = view;
    if(!superView) {
        superView = YBIBNormalWindow();
    }
    
    MBCustomView* hud = [MBCustomView new];
    [superView addSubview:hud];
    
    hud.msg = msg;
    hud.actionMsg = actionMsg;
    hud.actionBlock = actionBlock;
    
    hud.msgLabel.text = msg;
    [hud.actionBtn setTitle:actionMsg forState:UIControlStateNormal];
    
    [hud defineLayout];
    
    CGSize actionSize = [hud sizeWithActionMsg:actionMsg];
    
    float textW = [NSString sizeWithText:msg fontSize:13 width:kScreenWidth].width;
    float width = actionSize.height-3 + textW + 8 + actionSize.width + 10;
    float height = actionSize.height + 8 + 8;
    hud.layer.cornerRadius = height / 2.0;
    hud.layer.masksToBounds = YES;
    
    [hud mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(width, height));
        make.centerX.equalTo(superView);
        make.bottom.equalTo(superView.mas_safeAreaLayoutGuideBottom).offset(-75);
    }];

    [UIView beginAnimations:nil context:NULL];
    [UIView setAnimationDuration:0.30];
    hud.alpha = 1.0f;
    hud.transform = CGAffineTransformIdentity;
    [UIView commitAnimations];
    
    return hud;
}

- (CGSize)sizeWithActionMsg:(NSString *)actionMsg
{
    float width = [NSString sizeWithText:actionMsg fontSize:13 width:kScreenWidth].width;
    width = 10 + width + 10;
    
    return CGSizeMake(width, kActionHeight);
}

- (void)hideAfterDelay:(NSTimeInterval)delay
{
    [self performSelector:@selector(hideDelayed) withObject:nil afterDelay:delay];
}

- (void)hideDelayed
{
    //已经隐藏
    if(self.alpha <= 0.01) return;
    
    [UIView beginAnimations:nil context:NULL];
    [UIView setAnimationDuration:0.30];
    [UIView setAnimationDelegate:self];
    // 0.02 prevents the hud from passing through touches during the animation the hud will get completely hidden
    // in the done method
    self.transform = CGAffineTransformConcat(CGAffineTransformIdentity, CGAffineTransformMakeScale(0.5f, 0.5f));

    self.alpha = 0.0f;
    [UIView commitAnimations];
}

- (void)addSubviews
{
    [self addSubview:self.msgLabel];
    [self addSubview:self.actionBtn];
}

- (void)defineLayout
{
    CGSize actionSize = [self sizeWithActionMsg:self.actionMsg];

    [self.msgLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(actionSize.height-3);
        make.centerY.equalTo(self);
    }];

    [self.actionBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self);
        make.size.mas_equalTo(actionSize);
        make.right.mas_offset(-10);
    }];
}

- (void)setupObservers
{
    @weakify(self)
    [[self.actionBtn rac_signalForControlEvents:UIControlEventTouchUpInside]
    subscribeNext:^(id x) {
        @strongify(self)
        if(self.actionBlock) {
            self.actionBlock();
        }
        
        [self hideDelayed];
    }];
}

- (UILabel *)msgLabel
{
    if(!_msgLabel) {
        _msgLabel = [UIView createLabelWithTitle:@""
                                       textColor:[UIColor colorWithHexString:@"#ffffff"]
                                         bgColor:UIColor.clearColor
                                        fontSize:13
                                   textAlignment:NSTextAlignmentLeft
                                           bBold:NO];
    }
    
    return _msgLabel;
}

- (UIButton *)actionBtn
{
    if(!_actionBtn) {
        _actionBtn = [UIButton new];
        [_actionBtn setTitleColor:[UIColor colorWithHexString:@"#ffffff"] forState:UIControlStateNormal];
        _actionBtn.backgroundColor = [UIColor colorWithHexString:@"#2D7AFE"];
        
        _actionBtn.layer.cornerRadius = kActionHeight/2.0;
        _actionBtn.layer.masksToBounds = YES;
        _actionBtn.titleLabel.font = [UIFont systemFontOfSize:13];
    }
    
    return _actionBtn;
}

@end
