//
//  LoadingView.m
//  QRCode
//
//  Created by qingbin on 2021/11/7.
//  Copyright © 2021 qingbin. All rights reserved.
//

#import "LoadingView.h"
#import "Masonry.h"

#define kAnimationWidth 65

@interface LoadingView ()

//@property (nonatomic, strong) UIImageView *maskImageView;

@property (nonatomic, strong) UIView* animationView;

@end

@implementation LoadingView

- (id)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self addSubviews];
        [self defineLayout];
        
        [self animationCircleSpinFade];
    }
    return self;
}

- (void)dealloc
{
    [self.animationView.layer removeAllAnimations];
}

- (void)animationCircleSpinFade
{
    float width = kAnimationWidth;
    
    float spacing = 3;
    float radius = (width - 4 * spacing) / 3.5;
    float radiusX = (width - radius) / 2.0;

    float duration = 1.0;
    double beginTime = CACurrentMediaTime();
    NSArray* beginTimes = @[@0.84, @0.72, @0.6, @0.48, @0.36, @0.24, @0.12, @0];
    
    CAKeyframeAnimation* animationScale = [CAKeyframeAnimation animationWithKeyPath:@"transform.scale"];
    animationScale.keyTimes = @[@0, @0.5, @1];
    animationScale.values = @[@1, @0.4, @1];
    animationScale.duration = duration;
    
    CAKeyframeAnimation* animationOpacity = [CAKeyframeAnimation animationWithKeyPath:@"opacity"];
    animationOpacity.keyTimes = @[@0, @0.5, @1];
    animationOpacity.values = @[@1, @0.3, @1];
    animationOpacity.duration = duration;
    
    CAAnimationGroup* animation = [CAAnimationGroup animation];
    animation.animations = @[animationScale, animationOpacity];
    CAMediaTimingFunction* timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionLinear];
    animation.timingFunction = timingFunction;
    animation.duration = duration;
    animation.repeatCount = INT_MAX;
    animation.removedOnCompletion = false;
    
    UIBezierPath* path = [UIBezierPath bezierPathWithArcCenter:CGPointMake(radius/2.0, radius/2.0) radius:radius/2.0 startAngle:0 endAngle:2*M_PI clockwise:false];
    
    for(int i=0;i<8;i++) {
        float angle = M_PI/4.0 * i;
        
        CAShapeLayer* layer = [CAShapeLayer layer];
        layer.frame = CGRectMake(radiusX * (cos(angle) + 1), radiusX * (sin(angle) + 1), radius, radius);
        layer.path = path.CGPath;
        layer.backgroundColor = UIColor.clearColor.CGColor;
        layer.fillColor = UIColor.whiteColor.CGColor;
        
        animation.beginTime = beginTime - [beginTimes[i] doubleValue];
        
        [layer addAnimation:animation forKey:@"animation"];
        [self.animationView.layer addSublayer:layer];
    }
}

- (void)addSubviews
{
    [self addSubview:self.animationView];
}

- (void)defineLayout
{
    [self.animationView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self);
        make.size.mas_equalTo(kAnimationWidth);
    }];
}

- (UIView *)animationView
{
    if(!_animationView) {
        _animationView = [UIView new];
    }
    
    return _animationView;
}

//- (UIImageView *)maskImageView
//{
//    if (!_maskImageView) {
//        _maskImageView = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"loading_90deg"]];
//
//        CABasicAnimation* rotationAnimation;
//        rotationAnimation = [CABasicAnimation animationWithKeyPath:@"transform.rotation.z"];
//        rotationAnimation.toValue = [NSNumber numberWithFloat: M_PI * 2.0 ];
//        rotationAnimation.duration = 0.6f;
//        rotationAnimation.cumulative = YES;
//        rotationAnimation.repeatCount = MAXFLOAT;
//        rotationAnimation.removedOnCompletion = NO;
//
//        [_maskImageView.layer addAnimation:rotationAnimation forKey:@"rotationAnimation"];
//    }
//
//    return _maskImageView;
//}

@end
