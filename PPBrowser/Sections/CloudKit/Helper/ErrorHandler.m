//
//  ErrorHandler.m
//  Saber
//
//  Created by q<PERSON><PERSON> on 2023/12/26.
//

#import "ErrorHandler.h"

@implementation ErrorHandler

+ (CKOperationResultType)resultTypeWithError:(NSError *)error
                                       retry:(double*)retryValue
{
    if (!error) {
        return CKOperationResultTypeSuccess;
    }
    
    NSString *message = [self returnErrorMessageForCode:error.code];
    
    switch (error.code) {
        case CKErrorServiceUnavailable:
        case CKErrorRequestRateLimited:
        case CKErrorZoneBusy: {
            // If there is a retry delay specified in the error, then use that.
            NSDictionary *userInfo = error.userInfo;
            double retry = [userInfo[CKErrorRetryAfterKey] doubleValue];
            *retryValue = retry;
            if (retry > 0) {
                NSLog(@"ErrorHandler - %@. Should retry in %.2f seconds.", message, retry);
                return CKOperationResultTypeRetry;
            } else {
                return CKOperationResultTypeFail;
            }
        }
            
        // RECOVERABLE ERROR
        case CKErrorChangeTokenExpired: {
            NSLog(@"ErrorHandler.tokenExpired: %@", message);
            return CKOperationResultTypeTokenExpired;
        }
            
        // SHOULD CHUNK IT UP
        case CKErrorLimitExceeded:
            NSLog(@"ErrorHandler.Chunk: %@", message);
            return CKOperationResultTypeChunk;
            
        // SHARE DATABASE RELATED
        case CKErrorNetworkUnavailable:
        case CKErrorNetworkFailure:
        case CKErrorServerRecordChanged:
        case CKErrorPartialFailure:
        case CKErrorAlreadyShared:
        case CKErrorParticipantMayNeedVerification:
        case CKErrorReferenceViolation:
        case CKErrorTooManyParticipants:
        // quota exceeded is sort of a special case where the user has to take action(like spare more room in iCloud) before retry
        case CKErrorQuotaExceeded:
        default: {
            NSLog(@"ErrorHandler.Fail: %@", message);
            return CKOperationResultTypeFail;
        }
    }
}

+ (NSString *)returnErrorMessageForCode:(CKErrorCode)code
{
    NSString *returnMessage = @"";
    
    switch (code) {
        case CKErrorAlreadyShared:
            returnMessage = @"Already Shared: a record or share cannot be saved because doing so would cause the same hierarchy of records to exist in multiple shares.";
            break;
        case CKErrorAssetFileModified:
            returnMessage = @"Asset File Modified: the content of the specified asset file was modified while being saved.";
            break;
        case CKErrorAssetFileNotFound:
            returnMessage = @"Asset File Not Found: the specified asset file is not found.";
            break;
        case CKErrorBadContainer:
            returnMessage = @"Bad Container: the specified container is unknown or unauthorized.";
            break;
        case CKErrorBadDatabase:
            returnMessage = @"Bad Database: the operation could not be completed on the given database.";
            break;
        case CKErrorBatchRequestFailed:
            returnMessage = @"Batch Request Failed: the entire batch was rejected.";
            break;
        case CKErrorChangeTokenExpired:
            returnMessage = @"Change Token Expired: the previous server change token is too old.";
            break;
        case CKErrorConstraintViolation:
            returnMessage = @"Constraint Violation: the server rejected the request because of a conflict with a unique field.";
            break;
        case CKErrorIncompatibleVersion:
            returnMessage = @"Incompatible Version: your app version is older than the oldest version allowed.";
            break;
        case CKErrorInternalError:
            returnMessage = @"Internal Error: a nonrecoverable error was encountered by CloudKit.";
            break;
        case CKErrorInvalidArguments:
            returnMessage = @"Invalid Arguments: the specified request contains bad information.";
            break;
        case CKErrorLimitExceeded:
            returnMessage = @"Limit Exceeded: the request to the server is too large.";
            break;
        case CKErrorManagedAccountRestricted:
            returnMessage = @"Managed Account Restricted: the request was rejected due to a managed-account restriction.";
            break;
        case CKErrorMissingEntitlement:
            returnMessage = @"Missing Entitlement: the app is missing a required entitlement.";
            break;
        case CKErrorNetworkUnavailable:
        case CKErrorNetworkFailure:
            returnMessage = @"Network Unavailable: the internet connection appears to be offline.";
            break;
        case CKErrorNotAuthenticated:
            returnMessage = @"Not Authenticated: to use this app, you must enable iCloud syncing. Go to device Settings, sign in to iCloud, then in the app settings, be sure the iCloud feature is enabled.";
            break;
        case CKErrorOperationCancelled:
            returnMessage = @"Operation Cancelled: the operation was explicitly canceled.";
            break;
        case CKErrorPartialFailure:
            returnMessage = @"Partial Failure: some items failed, but the operation succeeded overall.";
            break;
        case CKErrorParticipantMayNeedVerification:
            returnMessage = @"Participant May Need Verification: you are not a member of the share.";
            break;
        case CKErrorPermissionFailure:
            returnMessage = @"Permission Failure: to use this app, you must enable iCloud syncing. Go to device Settings, sign in to iCloud, then in the app settings, be sure the iCloud feature is enabled.";
            break;
        case CKErrorQuotaExceeded:
            returnMessage = @"Quota Exceeded: saving would exceed your current iCloud storage quota.";
            break;
        case CKErrorReferenceViolation:
            returnMessage = @"Reference Violation: the target of a record's parent or share reference was not found.";
            break;
        case CKErrorRequestRateLimited:
            returnMessage = @"Request Rate Limited: transfers to and from the server are being rate limited at this time.";
            break;
        case CKErrorServerRecordChanged:
            returnMessage = @"Server Record Changed: the record was rejected because the version on the server is different.";
            break;
        case CKErrorServerRejectedRequest:
            returnMessage = @"Server Rejected Request";
            break;
        case CKErrorServerResponseLost:
            returnMessage = @"Server Response Lost";
            break;
        case CKErrorServiceUnavailable:
            returnMessage = @"Service Unavailable: Please try again.";
            break;
        case CKErrorTooManyParticipants:
            returnMessage = @"Too Many Participants: a share cannot be saved because too many participants are attached to the share.";
            break;
        case CKErrorUnknownItem:
            returnMessage = @"Unknown Item: the specified record does not exist.";
            break;
        case CKErrorUserDeletedZone:
            returnMessage = @"User Deleted Zone: the user has deleted this zone from the settings UI.";
            break;
        case CKErrorZoneBusy:
            returnMessage = @"Zone Busy: the server is too busy to handle the zone operation.";
            break;
        case CKErrorZoneNotFound:
            returnMessage = @"Zone Not Found: the specified record zone does not exist on the server.";
            break;
        default:
            returnMessage = @"Unhandled Error.";
            break;
    }

    return [returnMessage stringByAppendingFormat:@" CKError.Code: %ld", (long)code];
}


@end
