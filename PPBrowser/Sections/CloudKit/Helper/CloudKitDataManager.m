//
//  CloudKitDataManager.m
//  Saber
//
//  Created by qing<PERSON> on 2024/1/1.
//

#import "CloudKitDataManager.h"

#import "Tampermonkey.h"

#import "PPEnums.h"
#import "PPNotifications.h"

@implementation CloudKitDataManager

+ (instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    static CloudKitDataManager* obj;
    dispatch_once(&onceToken, ^{
        obj = [[CloudKitDataManager alloc]init];
    });
    
    return obj;
}

- (instancetype)init
{
    self = [super init];
    if(self) {
        [self setupObservers];
    }
    
    return self;
}

- (void)setupObservers
{
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(handleNotification:)
                                                 name:kCloudKitDataDidChangeNotification
                                               object:nil];
}

- (void)handleNotification:(NSNotification *)notification
{    
    NSDictionary* infos = notification.userInfo;
    
    //处理文件逻辑
    //脚本处理@require/@resource逻辑
    NSArray* needDeleteScriptIds = infos[@"needDeleteScriptIds"];
    if(needDeleteScriptIds.count > 0) {
        [[Tampermonkey shareInstance] removeRequireAndResourceWithScriptIdArray:needDeleteScriptIds];
    }
    
    NSArray* needNewScripts = infos[@"needNewScripts"];
    NSArray* needUpdateScripts = infos[@"needUpdateScripts"];
    NSMutableArray* scripts = [NSMutableArray array];
    [scripts addObjectsFromArray:needNewScripts];
    [scripts addObjectsFromArray:needUpdateScripts];
    
    for(UserScript* script in scripts) {
        [[Tampermonkey shareInstance] loadRequiredWithUserScript:script forceReload:NO completion:nil];
        [[Tampermonkey shareInstance] loadResourceWithUserScript:script completion:nil];
    }
}

@end
