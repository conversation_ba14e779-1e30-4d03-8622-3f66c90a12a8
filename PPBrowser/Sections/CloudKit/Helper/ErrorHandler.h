//
//  ErrorHandler.h
//  Saber
//
//  Created by q<PERSON><PERSON> on 2023/12/26.
//

#import <Foundation/Foundation.h>

#import <CloudKit/CloudKit.h>

// Define CKOperationResultType enum
typedef NS_ENUM(NSInteger, CKOperationResultType) {
    CKOperationResultTypeSuccess,
    CKOperationResultTypeRetry,
    CKOperationResultTypeChunk,
    CKOperationResultTypeRecoverableError,
    CKOperationResultTypeTokenExpired,
    CKOperationResultTypeFail
};

NS_ASSUME_NONNULL_BEGIN

@interface ErrorHandler : NSObject

+ (CKOperationResultType)resultTypeWithError:(NSError *)error
                                       retry:(double*)retryValue;

@end

NS_ASSUME_NONNULL_END
