//
//  CloudKitHelper.h
//  Saber
//
//  Created by q<PERSON><PERSON> on 2023/12/28.
//

#import <Foundation/Foundation.h>

#import <CloudKit/CloudKit.h>

#import "UserScript.h"

@interface CloudKitHelper : NSObject

/// Focus都保存在自定义的Zone中
+ (CKRecordZoneID *)focusZoneID;

/// 更新iCloud同步时间
+ (void)updateSyncTimestamp;

/// 获取iCloud同步时间
+ (NSString *)iCloudSyncTimestamp;

// 检查iCloud是否可用
+ (BOOL)checkCloudAvailability;

// 是否打开了iCloud
+ (BOOL)enablediCloud;

@end

