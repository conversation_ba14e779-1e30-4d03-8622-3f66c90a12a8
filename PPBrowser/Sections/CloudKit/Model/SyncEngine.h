//
//  SyncEngine.h
//  Saber
//
//  Created by qing<PERSON> on 2023/12/27.
//

#import <Foundation/Foundation.h>

#import <CloudKit/CloudKit.h>
#import "ErrorHandler.h"

/**
 参考项目:
 https://github.com/jayhickey/Cirrus
 https://github.com/caiyue1993/IceCream
 
 CloudKit dashboard
 https://icloud.developer.apple.com/dashboard/home/<USER>/92R56D3745
 
 同步方案：
 1）当privateChangeToken为空时（此时从CloudKit中拉到的数据是全部数据），才对比本地和CloudKit的数据集，相互补充
 2）如果privateChangeToken不为空时，从CloudKit拉完数据之后，只更新到本地数据
 3）正常的增删改都同步到CloudKit
 4）如果关闭了iCloud开关，那么则清空privateChangeToken，下次再打开时则继续全量对比
 5）总结：初始化、开关或者手动同步时做全量对比，其他情况做增量更新即可。
 */

@interface SyncEngine : NSObject

+ (instancetype)shareInstance;

@property (readonly) BOOL isSyncing;

/// 同步数据到CloudKit
- (void)syncRecordsToCloudKit:(NSArray<CKRecord *> *)recordsToStore
            recordIDsToDelete:(NSArray<CKRecordID*> *)recordIDsToDelete
                   completion:(void(^)(BOOL succ))completion;

/// 立即同步(手动)，先拉取CloudKit数据到本地Pull，然后对比Merge，再将本地不同数据推送Push到CloudKit
- (void)syncNowWithCompletion:(void(^)(void))completion;

/// 自动拉取数据同步
- (void)fetchChangesInDatabase;

/// 更新数据token
- (void)updateWithPrivateChangeToken:(CKServerChangeToken *)privateChangeToken;

/// private container订阅ID
+ (NSString *)privateSubscriptionID;

@end

