//
//  SyncEngine.m
//  Saber
//
//  Created by q<PERSON><PERSON> on 2023/12/27.
//

#import "SyncEngine.h"

#import "ReactiveCocoa.h"
#import "PPNotifications.h"
#import "PPEnums.h"
#import "CloudKitHelper.h"

#import "DatabaseUnit+UserScript.h"
#import "DatabaseUnit+BookMark.h"
#import "DatabaseUnit+CustomTag.h"
#import "DatabaseUnit+Tagit.h"
#import "DatabaseUnit+WebBlacklist.h"

#import "PreferenceManager.h"
#import "SyncProtocol.h"

static NSString* keyForDatabaseChangeToken = @"keyForDatabaseChangeToken";
static NSString* keyForPrivateChangeToken = @"keyForPrivateChangeToken";
static NSString* keyForSubscriptionIsLocallyCached = @"keyForSubscriptionIsLocallyCached";

@interface SyncEngine ()

@property (nonatomic, strong) CKContainer *container;

@property (nonatomic, strong) CKDatabase *database;

@property (nonatomic, strong) dispatch_queue_t queue;

@property (nonatomic, assign) BOOL isSyncing;

@end

@implementation SyncEngine

+ (instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    static SyncEngine* obj;
    dispatch_once(&onceToken, ^{
        obj = [[SyncEngine alloc]init];
    });
    
    return obj;
}

- (instancetype)init
{
    self = [super init];
    if(self) {
        [self commonInit];
    }
    
    return self;
}

- (void)commonInit
{
    self.queue = dispatch_queue_create("com.focus.cloudkit.queue", DISPATCH_QUEUE_SERIAL);
    
    //指定容器
    self.container = [CKContainer containerWithIdentifier:@"iCloud.com.qingbin.focusbrowser"];
    self.database = self.container.privateCloudDatabase;
    
    self.isSyncing = YES;
    
    @weakify(self)
    [self.container accountStatusWithCompletionHandler:^(CKAccountStatus accountStatus, NSError * _Nullable error) {
        @strongify(self)
        if(!error) {
            switch (accountStatus) {
                case CKAccountStatusAvailable: {
                    //创建自定义Zone
                    @weakify(self)
                    [self createCustomZoneIfNeededWithCompletion:^(BOOL succ) {
                        @strongify(self)
                        ///等检查自定义zone是否存在再进行下一步动作
                        if(succ) {
                            //长连接保活
//                            [self resumeLongLivedOperationIfPossible];
                            //订阅更新
                            [self startObservingRemoteChanges];
                            //创建订阅
                            [self createPrivateSubscriptionsIfNeeded];
                            
                            //没有启动iCloud(包含会员检测)
                            BOOL enablediCloud = [[PreferenceManager shareInstance].items.enablediCloud2 boolValue];
                            if(!enablediCloud) {
                                //没有启动iCloud
                                self.isSyncing = NO;
                                return;
                            } else {
                                //启动了iCloud
                                //拉取数据
                                [self fetchChangesInDatabase];
                            }
                        }
                    }];
                }
                    break;
                    
                case CKAccountStatusNoAccount:
                case CKAccountStatusRestricted:
                default:
                    break;
            }
        } else {
            NSLog(@"获取CloudKit状态报错: %@", error);
        }
    }];
}

- (BOOL)isSubscriptionIsLocallyCached
{
    NSUserDefaults* userDefaults = [NSUserDefaults standardUserDefaults];
    return [userDefaults boolForKey:keyForSubscriptionIsLocallyCached];;
}

- (void)updateSubscriptionIsLocallyCached:(BOOL)subscriptionIsLocallyCached
{
    NSUserDefaults* userDefaults = [NSUserDefaults standardUserDefaults];
    [userDefaults setBool:subscriptionIsLocallyCached forKey:keyForSubscriptionIsLocallyCached];
}

#pragma mark -- 获取更新数据token
- (CKServerChangeToken *)privateChangeToken
{
    NSUserDefaults* userDefaults = [NSUserDefaults standardUserDefaults];
    NSData* tokenData = [userDefaults dataForKey:keyForPrivateChangeToken];
    if(tokenData) {
        return [NSKeyedUnarchiver unarchiveObjectWithData:tokenData];
    }
    
    return nil;
}

#pragma mark -- 更新数据token
- (void)updateWithPrivateChangeToken:(CKServerChangeToken *)privateChangeToken
{
    NSUserDefaults* userDefaults = [NSUserDefaults standardUserDefaults];
    
    if(privateChangeToken == nil) {
        [userDefaults removeObjectForKey:keyForPrivateChangeToken];
        return;
    }
    
    NSData* tokenData = [NSKeyedArchiver archivedDataWithRootObject:privateChangeToken];
    [userDefaults setObject:tokenData forKey:keyForPrivateChangeToken];
}

/// private container订阅ID
+ (NSString *)privateSubscriptionID
{
    return @"addons_private_changes_subscriptionID";
}

- (void)startObservingRemoteChanges
{
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(fetchChangesInDatabase)
                                                 name:kCloudKitDataSubscriptionNotification
                                               object:nil];
}

#pragma mark -- 自动拉取数据同步
- (void)fetchChangesInDatabase
{
    [self _syncNowWithCompletion:nil];
}

#pragma mark -- 创建订阅更新
- (void)createPrivateSubscriptionsIfNeeded
{
    //已经订阅过则不需要重复订阅，参考苹果官方文档说明
    if([self isSubscriptionIsLocallyCached]) {
        [self checkSubscription];
        return;
    }
    
    CKDatabaseSubscription* subscription = [[CKDatabaseSubscription alloc]initWithSubscriptionID:[SyncEngine privateSubscriptionID]];
    
    // Configure the notification so that the system delivers it silently
    // and, therefore, doesn't require permission from the user.
    CKNotificationInfo *notificationInfo = [CKNotificationInfo new];
    notificationInfo.shouldSendContentAvailable = YES;
    subscription.notificationInfo = notificationInfo;
        
    // Create an operation that saves the subscription to the server.
    CKModifySubscriptionsOperation *operation =
        [[CKModifySubscriptionsOperation alloc]
         initWithSubscriptionsToSave:@[subscription]
         subscriptionIDsToDelete:NULL];
    
    operation.modifySubscriptionsCompletionBlock =
        ^(NSArray *subscriptions, NSArray *deleted, NSError *error) {
        if (error) {
            // Handle the error.
        } else {
            // Record that the system successfully creates the subscription
            // to prevent unnecessary trips to the server in later launches.
            [self updateSubscriptionIsLocallyCached:YES];
        }
    };
        
    // Set an appropriate QoS and add the operation to the private
    // database's operation queue to execute it.
    operation.qualityOfService = NSQualityOfServiceUtility;
    [self.database addOperation:operation];
}

///检查是否创建了订阅
- (void)checkSubscription
{
    CKFetchSubscriptionsOperation* operation = [[CKFetchSubscriptionsOperation alloc]initWithSubscriptionIDs:@[[SyncEngine privateSubscriptionID]]];
    [operation setFetchSubscriptionCompletionBlock:^(NSDictionary<CKSubscriptionID,CKSubscription *> * _Nullable subscriptionsBySubscriptionID, NSError * _Nullable operationError) {
        double retry = 0;
        CKOperationResultType resutType = [ErrorHandler resultTypeWithError:operationError retry:&retry];
        if(resutType == CKOperationResultTypeSuccess) {
            //success
            if(subscriptionsBySubscriptionID.count > 0) {
                //success
            } else {
                //需要重新生成订阅
                [self updateSubscriptionIsLocallyCached:NO];
                [self createPrivateSubscriptionsIfNeeded];
            }
        } else if(resutType == CKOperationResultTypeRetry) {
            @weakify(self)
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(retry * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                @strongify(self)
                [self checkSubscription];
            });
        } else {
            //failed
        }
    }];
    
    [self.database addOperation:operation];
}

#pragma mark -- 创建自定义zone
- (BOOL)isCreatedCustomZone
{
    NSUserDefaults* userDefault = [NSUserDefaults standardUserDefaults];
    return [userDefault boolForKey:@"KeyForFocusCloudKitZone"];
}

- (void)updateWithIsCreatedCustomZone:(BOOL)isCreatedCustomZone
{
    NSUserDefaults* userDefault = [NSUserDefaults standardUserDefaults];
    [userDefault setBool:isCreatedCustomZone forKey:@"KeyForFocusCloudKitZone"];
}

- (void)createCustomZoneIfNeededWithCompletion:(void(^)(BOOL succ))completion
{
    if([self isCreatedCustomZone]) {
        //检查是否有zone
        [self checkCustomZoneWithCompletion:completion];
        return;
    } else {
        //直接创建zone
        CKRecordZoneID* zoneID = [CloudKitHelper focusZoneID];
        CKRecordZone* zone = [[CKRecordZone alloc]initWithZoneID:zoneID];

        CKModifyRecordZonesOperation* operation = [[CKModifyRecordZonesOperation alloc]initWithRecordZonesToSave:@[zone] recordZoneIDsToDelete:nil];

        [operation setModifyRecordZonesCompletionBlock:^(NSArray<CKRecordZone *> * _Nullable savedRecordZones, NSArray<CKRecordZoneID *> * _Nullable deletedRecordZoneIDs, NSError * _Nullable operationError) {
            double retry = 0.0f;
            CKOperationResultType resultType = [ErrorHandler resultTypeWithError:operationError retry:&retry];

            if(resultType == CKOperationResultTypeSuccess) {
                //success
                //更新状态
                [self updateWithIsCreatedCustomZone:YES];
                
                if(completion) {
                    completion(YES);
                }
            } else if(resultType == CKOperationResultTypeRetry) {
                @weakify(self)
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(retry * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    @strongify(self)
                    [self createCustomZoneIfNeededWithCompletion:completion];
                });
            } else {
                //failed
                if(completion) {
                    completion(NO);
                }
            }
        }];

        operation.qualityOfService = NSQualityOfServiceUserInitiated;

        [self.database addOperation:operation];
    }
}

- (void)checkCustomZoneWithCompletion:(void(^)(BOOL succ))completion
{
    CKRecordZoneID* zoneID = [CloudKitHelper focusZoneID];
    CKFetchRecordZonesOperation* operation = [[CKFetchRecordZonesOperation alloc]initWithRecordZoneIDs:@[zoneID]];

    [operation setFetchRecordZonesCompletionBlock:^(NSDictionary<CKRecordZoneID *,CKRecordZone *> * _Nullable recordZonesByZoneID, NSError * _Nullable operationError) {
        double retry = 0.0f;
        CKOperationResultType resultType = [ErrorHandler resultTypeWithError:operationError retry:&retry];

        if(resultType == CKOperationResultTypeSuccess) {
            //success
            //更新状态
            if(recordZonesByZoneID.count > 0) {
                //已经存在，更新状态
                [self updateWithIsCreatedCustomZone:YES];
                
                if(completion) {
                    completion(YES);
                }
            } else {
                //没有zone，创建
                [self updateWithIsCreatedCustomZone:NO];
                [self createCustomZoneIfNeededWithCompletion:completion];
            }
        } else if(resultType == CKOperationResultTypeRetry) {
            @weakify(self)
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(retry * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                @strongify(self)
                [self checkCustomZoneWithCompletion:completion];
            });
        } else {
            //failed
            
            if(completion) {
                completion(NO);
            }
        }
    }];

    operation.qualityOfService = NSQualityOfServiceUserInitiated;

    [self.database addOperation:operation];
}

#pragma mark -- 同步数据到CloudKit
- (void)syncRecordsToCloudKit:(NSArray<CKRecord *> *)recordsToStore
            recordIDsToDelete:(NSArray<CKRecordID*> *)recordIDsToDelete
                   completion:(void(^)(BOOL succ))completion
{
    self.isSyncing = YES;
    
    //没有开启iCloud同步，直接返回(enablediCloud2包含了会员检测)
    BOOL enablediCloud = [[PreferenceManager shareInstance].items.enablediCloud2 boolValue];
    if(!enablediCloud) {
        //没有启动iCloud
        self.isSyncing = NO;
        return;
    }
    
    @weakify(self)
    dispatch_async(self.queue, ^{
        @strongify(self)
        @weakify(self)
        [self _syncRecordsToCloudKit:recordsToStore recordIDsToDelete:recordIDsToDelete completion:^(BOOL succ) {
            @strongify(self)
            if(completion) {
                completion(succ);
            }
            
            self.isSyncing = NO;
        }];
    });
}

- (void)_syncRecordsToCloudKit:(NSArray<CKRecord *> *)recordsToStore
             recordIDsToDelete:(NSArray<CKRecordID*> *)recordIDsToDelete
                    completion:(void(^)(BOOL succ))completion
{
    CKModifyRecordsOperation* modifyOpe = [[CKModifyRecordsOperation alloc]initWithRecordsToSave:recordsToStore recordIDsToDelete:recordIDsToDelete];
    
//    CKOperationConfiguration* config = [CKOperationConfiguration new];
//    config.longLived = YES;
//    modifyOpe.configuration = config;
    
    // We use .changedKeys savePolicy to do unlocked changes here cause my app is contentious and off-line first
    // Apple suggests using .ifServerRecordUnchanged save policy
    // For more, see Advanced CloudKit(https://developer.apple.com/videos/play/wwdc2014/231/)
    // 在你的CKModifyRecordsOperation中，
    // 你可以设置savePolicy属性为.changedKeys，这样CloudKit会尝试仅保存已更改的字段，而不是整个记录。这可以帮助你避免由于重复记录ID而导致的错误。
    modifyOpe.savePolicy = CKRecordSaveChangedKeys;
    
    // To avoid CKError.partialFailure, make the operation atomic (if one record fails to get modified, they all fail)
    // If you want to handle partial failures, set .isAtomic to false and implement CKOperationResultType .fail(reason: .partialFailure) where appropriate
    modifyOpe.atomic = YES;
    
    @weakify(self)
    [modifyOpe setModifyRecordsCompletionBlock:^(NSArray<CKRecord *> * _Nullable savedRecords, NSArray<CKRecordID *> * _Nullable deletedRecordIDs, NSError * _Nullable operationError) {
        @strongify(self)
        double retry = 0;
        CKOperationResultType resutType = [ErrorHandler resultTypeWithError:operationError retry:&retry];
        if(resutType == CKOperationResultTypeSuccess) {
            NSLog(@"------------ 同步数据到CloudKit成功 ------------");
            
            /// 更新同步时间
            [CloudKitHelper updateSyncTimestamp];
            
            dispatch_async(dispatch_get_main_queue(), ^{
                if(completion) {
                    completion(YES);
                }
            });
        } else if(resutType == CKOperationResultTypeRetry) {
            @weakify(self)
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(retry * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                @strongify(self)
                [self syncRecordsToCloudKit:recordsToStore recordIDsToDelete:recordIDsToDelete completion:completion];
            });
        } else if(resutType == CKOperationResultTypeChunk) {
            /// CloudKit says maximum number of items in a single request is 400.
            /// So I think 300 should be fine by them.
            NSArray* chunkedRecords = [self chunkItUpWithSize:300 array:recordsToStore];
            for(NSArray* chunk in chunkedRecords) {
                [self syncRecordsToCloudKit:chunk recordIDsToDelete:recordIDsToDelete completion:completion];
            }
        } else {
            //failed
            if(completion) {
                completion(NO);
            }
        }
    }];
    
    [self.database addOperation:modifyOpe];
}

- (NSArray<NSArray *> *)chunkItUpWithSize:(NSInteger)chunkSize array:(NSArray *)array
{
    NSMutableArray<NSArray *> *result = [NSMutableArray array];

    for (NSInteger startIndex = 0; startIndex < array.count; startIndex += chunkSize) {
        NSInteger endIndex = (startIndex + chunkSize > array.count) ? array.count : (startIndex + chunkSize);
        NSRange range = NSMakeRange(startIndex, endIndex - startIndex);
        NSArray *chunk = [array subarrayWithRange:range];
        [result addObject:chunk];
    }

    return result;
}

#pragma mark -- 长链接保活
/// The CloudKit Best Practice is out of date, now use this:
/// https://developer.apple.com/documentation/cloudkit/ckoperation
/// Which problem does this func solve? E.g.:
/// 1.(Offline) You make a local change, involve a operation
/// 2. App exits or ejected by user
/// 3. Back to app again
/// The operation resumes! All works like a magic!
/// 崩溃非常严重，去掉该逻辑
//- (void)resumeLongLivedOperationIfPossible
//{
//    @weakify(self)
//    [self.container fetchAllLongLivedOperationIDsWithCompletionHandler:^(NSArray<CKOperationID> * _Nullable outstandingOperationIDs, NSError * _Nullable error) {
//        @strongify(self)
//        if(!error) {
//            @weakify(self)
//            for(CKOperationID operationId in outstandingOperationIDs) {
//                [self.container fetchLongLivedOperationWithID:operationId completionHandler:^(CKOperation * _Nullable_result outstandingOperation, NSError * _Nullable error) {
//                    @strongify(self)
//                    if(!error && outstandingOperation) {
//                        CKModifyRecordsOperation* modifyOp = (CKModifyRecordsOperation*)outstandingOperation;
//                        
//                        [modifyOp setModifyRecordsCompletionBlock:^(NSArray<CKRecord *> * _Nullable savedRecords, NSArray<CKRecordID *> * _Nullable deletedRecordIDs, NSError * _Nullable operationError) {
//                            NSLog(@"Resume modify records success!");
//                        }];
//                        
//                        // The Apple's example code in doc(https://developer.apple.com/documentation/cloudkit/ckoperation/#1666033)
//                        // tells we add operation in container. But however it crashes on iOS 15 beta versions.
//                        // And the crash log tells us to "CKDatabaseOperations must be submitted to a CKDatabase".
//                        // So I guess there must be something changed in the daemon. We temperorily add this availabilty check.
//                        
//                        //在iOS15崩溃了
//                        @try {
//                            if(@available(iOS 15, *)) {
//                                [self.database addOperation:modifyOp];
//                            } else {
//                                [self.container addOperation:modifyOp];
//                            }
//                        } @catch (NSException *exception) {
//                        } @finally {
//                        }
//                    }
//                }];
//            }
//        }
//    }];
//}


#pragma mark -- 获取CloudKit变更的数据
- (void)fetchRemoteChangesWithCompletion:(void(^)(BOOL succ, NSArray* changedRecords, NSArray* deletedRecords))completion
{
    /// 必须要在自定义的zone中，否则没法用CKFetchRecordZoneChangesOperation
    /// 会报错：AppDefaultZone does not support getChanges call
    
    __block NSMutableArray* changedRecords = [NSMutableArray array];
    __block NSMutableArray* deletedRecords = [NSMutableArray array];
    
    CKFetchRecordZoneChangesConfiguration* config = [[CKFetchRecordZoneChangesConfiguration alloc]init];
    config.previousServerChangeToken = [self privateChangeToken];
    
    CKRecordZoneID* zoneID = [CloudKitHelper focusZoneID];
    CKFetchRecordZoneChangesOperation* operation = [[CKFetchRecordZoneChangesOperation alloc]initWithRecordZoneIDs:@[zoneID] configurationsByRecordZoneID:@{
        zoneID : config,
    }];
    operation.fetchAllChanges = YES;
    
    @weakify(self)
    [operation setRecordZoneChangeTokensUpdatedBlock:^(CKRecordZoneID * _Nonnull recordZoneID, CKServerChangeToken * _Nullable serverChangeToken, NSData * _Nullable clientChangeTokenData) {
        @strongify(self)
        [self updateWithPrivateChangeToken:serverChangeToken];
    }];
    
    [operation setRecordChangedBlock:^(CKRecord * _Nonnull record) {
        @strongify(self)
        //更改Record
        NSLog(@"Record Change = %@", record);
        
        dispatch_async(self.queue, ^{
            [changedRecords addObject:record];
        });
    }];
    
    [operation setRecordWithIDWasDeletedBlock:^(CKRecordID * _Nonnull recordID, CKRecordType  _Nonnull recordType) {
        @strongify(self)
        //删除Record
        NSLog(@"recordType = %@, recordID = %@", recordType, recordID);
        
        dispatch_async(self.queue, ^{
            RACTuple* tuple = [RACTuple tupleWithObjects:recordID, recordType, nil];
            [deletedRecords addObject:tuple];
        });
    }];
    
    [operation setRecordZoneFetchCompletionBlock:^(CKRecordZoneID * _Nonnull recordZoneID, CKServerChangeToken * _Nullable serverChangeToken, NSData * _Nullable clientChangeTokenData, BOOL moreComing, NSError * _Nullable recordZoneError) {
        @strongify(self)
        //可能会多次调用，直到moreComing为False
        double retry = 0.0f;
        CKOperationResultType resultType = [ErrorHandler resultTypeWithError:recordZoneError retry:&retry];

        if(resultType == CKOperationResultTypeSuccess) {
            //success
            [self updateWithPrivateChangeToken:serverChangeToken];
        } else if(resultType == CKOperationResultTypeRetry) {
            @weakify(self)
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(retry * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                @strongify(self)
                [self fetchRemoteChangesWithCompletion:completion];
            });
        } else if(resultType == CKOperationResultTypeTokenExpired) {
            /// The previousServerChangeToken value is too old and the client must re-sync from scratch
            [self updateWithPrivateChangeToken:nil];
            [self fetchRemoteChangesWithCompletion:completion];
        } else {
            //failed
        }
    }];
    
    [operation setFetchRecordZoneChangesCompletionBlock:^(NSError * _Nullable operationError) {
        //在操作完成时调用，用于处理获取的变化记录。
        double retry = 0.0f;
        CKOperationResultType resultType = [ErrorHandler resultTypeWithError:operationError retry:&retry];
        if(resultType == CKOperationResultTypeSuccess) {
            dispatch_async(self.queue, ^{
                if(completion) {
                    completion(YES, changedRecords, deletedRecords);
                }
            });
        } else {
            dispatch_async(self.queue, ^{
                if(completion) {
                    completion(NO, changedRecords, deletedRecords);
                }
            });
        }
    }];
    
    [self.database addOperation:operation];
}

#pragma mark -- 立即同步
/// 立即同步，先拉取CloudKit数据到本地Pull，然后对比Merge，再将本地不同数据推送Push到CloudKit
- (void)syncNowWithCompletion:(void(^)(void))completion
{
    //手动点击同步，那么清空token，做一次全量的更新
    [self updateWithPrivateChangeToken:nil];
    
    dispatch_async(self.queue, ^{
        [self _syncNowWithCompletion:completion];
    });
}

- (void)_syncNowWithCompletion:(void(^)(void))completion
{
    BOOL hasPrivateChangeToken = [self privateChangeToken];
    
    self.isSyncing = YES;
    
    @weakify(self)
    [self fetchRemoteChangesWithCompletion:^(BOOL succ, NSArray *changedRecords, NSArray *deletedRecords) {
        @strongify(self)
        @weakify(self)
        if(succ) {
            //进行merge操作
            [self _handleMergeDataWithChangedRecords:changedRecords 
                                      deletedRecords:deletedRecords
                               hasPrivateChangeToken:hasPrivateChangeToken
                                          completion:^{
                @strongify(self)
                if(completion) {
                    completion();
                }
                
                self.isSyncing = NO;
            }];
        } else {
            if(completion) {
                completion();
            }
            
            self.isSyncing = NO;
        }
    }];
}

- (void)_handleMergeDataWithChangedRecords:(NSArray *)changedRecords
                            deletedRecords:(NSArray *)deletedRecords
                     hasPrivateChangeToken:(BOOL)hasPrivateChangeToken
                                completion:(void(^)(void))completion
{
    dispatch_group_t group = dispatch_group_create();
    dispatch_queue_t queue = dispatch_queue_create("com.cloudkit.merge.queue", DISPATCH_QUEUE_CONCURRENT);
    
    __block NSArray* allUserScripts = nil;
    __block NSArray* allBookMarks = nil;
    __block NSArray* allTagits = nil;
    __block NSArray* allCustomTags = nil;
    __block NSArray* allWebBlacklists = nil;
    
    //查询所有脚本
    DatabaseUnit* unit = [DatabaseUnit queryAllUserScripts];
    dispatch_group_enter(group);
    [unit setCompleteBlock:^(id result, BOOL success) {
        allUserScripts = result;
        dispatch_group_leave(group);
    }];
    DB_EXEC(unit);
    
    //查询所有书签
    unit = [DatabaseUnit queryAllBookMarks];
    dispatch_group_enter(group);
    [unit setCompleteBlock:^(id result, BOOL success) {
        allBookMarks = result;
        dispatch_group_leave(group);
    }];
    DB_EXEC(unit);
    
    //查询所有标记模式
    unit = [DatabaseUnit queryAllTagit];
    dispatch_group_enter(group);
    [unit setCompleteBlock:^(id result, BOOL success) {
        allTagits = result;
        dispatch_group_leave(group);
    }];
    DB_EXEC(unit);
    
    //查询所有首页标签
    unit = [DatabaseUnit queryAllCustomTags];
    dispatch_group_enter(group);
    [unit setCompleteBlock:^(id result, BOOL success) {
        allCustomTags = result;
        dispatch_group_leave(group);
    }];
    DB_EXEC(unit);
    
    //查询所有网页黑名单
    unit = [DatabaseUnit queryAllWebBlacklist];
    dispatch_group_enter(group);
    [unit setCompleteBlock:^(id result, BOOL success) {
        allWebBlacklists = result;
        dispatch_group_leave(group);
    }];
    DB_EXEC(unit);
    
    @weakify(self)
    dispatch_group_notify(group, queue, ^{
        @strongify(self)
        [self _handleMergeDataWithChangedRecords:changedRecords
                                  deletedRecords:deletedRecords
                                  allUserScripts:allUserScripts
                                    allBookMarks:allBookMarks
                                   allCustomTags:allCustomTags
                                allWebBlacklists:allWebBlacklists
                                       allTagits:allTagits
                           hasPrivateChangeToken:hasPrivateChangeToken
                                      completion:completion];
    });
}

- (void)_handleMergeDataWithChangedRecords:(NSArray *)changedRecords 
                            deletedRecords:(NSArray *)deletedRecords
                            allUserScripts:(NSArray *)allUserScripts
                              allBookMarks:(NSArray *)allBookMarks
                             allCustomTags:(NSArray *)allCustomTags
                          allWebBlacklists:(NSArray *)allWebBlacklists
                                 allTagits:(NSArray *)allTagits
                     hasPrivateChangeToken:(BOOL)hasPrivateChangeToken
                                completion:(void(^)(void))completion
{
    //进行merge操作
    //脚本
    NSArray* types = @[@(CloudModelTypeUserScript), @(CloudModelTypeBookMark), @(CloudModelTypeCustomTag), @(CloudModelTypeTagit), @(CloudModelTypeWebBlacklist)];
    NSArray* models = @[allUserScripts?:@[], allBookMarks?:@[], allCustomTags?:@[], allTagits?:@[], allWebBlacklists?:@[]];
    
    for(int i=0;i<types.count;i++) {
        //增量更新/全量更新
        [self _handleMergeDataWithChangedRecords:changedRecords
                                  deletedRecords:deletedRecords
                                       cloudType:[types[i] intValue]
                                       dataArray:models[i]
                           hasPrivateChangeToken:hasPrivateChangeToken];
    }
    
    //更新iCloud同步时间
    [CloudKitHelper updateSyncTimestamp];
    
    if(completion) {
        completion();
    }
}

/// 处理脚本/书签/首页标签/网页黑名单/标记模式相关逻辑
/// 如果是首次或者手动刷新的，那么remoteMap只能从iCloud获取
#pragma mark -- 全量更新
- (void)_handleMergeDataWithChangedRecords:(NSArray *)changedRecords
                            deletedRecords:(NSArray *)deletedRecords
                                 cloudType:(CloudModelType)cloudType
                                 dataArray:(NSArray *)dataArray
                     hasPrivateChangeToken:(BOOL)hasPrivateChangeToken
{
    //hasPrivateChangeToken==true -- 增量更新(基于本地此时此刻的时间戳，那么此时只考虑对本地数据进行增删改操作，而不用考虑将数据同步到iCloud(因为同步这个操作每次手动变更的时候已经做了))
    //hasPrivateChangeToken==false -- 全量更新
    
    //uuid/url -> tagit/userscript/xx
    NSMutableDictionary* localDataMapper = [NSMutableDictionary dictionary];
    
    for(id<SyncProtocol> obj in dataArray) {
        localDataMapper[[obj getUuid]] = obj;
    }
        
    //删除
    NSMutableDictionary* needDeletedIdsMapper = [NSMutableDictionary dictionary];
    NSMutableArray* needDeletedIds = [NSMutableArray array];
    for(RACTuple* tuple in deletedRecords) {
        CKRecordID *recordID = tuple.first;
        
        //本地数据不存在，则不需要删除
        id<SyncProtocol> obj = localDataMapper[recordID.recordName];
        if(!obj) continue;
        
        [needDeletedIds addObject:[obj getUuid]];
        needDeletedIdsMapper[[obj getUuid]] = obj;
    }
    
    //创建到本地
    NSMutableArray* needNewArray = [NSMutableArray array];
    //更新到本地
    NSMutableArray* needUpdateArray = [NSMutableArray array];
    
    //CloudKit数据
    NSMutableArray* remoteDataArray = [NSMutableArray array];
    
    //找出需要新建/更新到CloudKit的数据
    NSMutableArray* needUploadRecords = [NSMutableArray array];
    
    for(CKRecord* record in changedRecords) {
        CloudModelType appType = [record[@"appType"] intValue];
        if(appType != cloudType) continue;
        
        id<SyncProtocol> remoteObj = nil;
        if(cloudType == CloudModelTypeUserScript) {
            //脚本
            remoteObj = [[UserScript alloc]initWithCKRecord:record];
        } else if(cloudType == CloudModelTypeTagit) {
            //标记模式
            remoteObj = [[TagitModel alloc]initWithCKRecord:record];
        } else if(cloudType == CloudModelTypeBookMark) {
            //书签
            remoteObj = [[BookMarkModel alloc]initWithCKRecord:record];
        } else if(cloudType == CloudModelTypeCustomTag) {
            //首页标签
            remoteObj = [[CustomTagModel alloc]initWithCKRecord:record];
        } else if(cloudType == CloudModelTypeWebBlacklist) {
            //网页黑名单
            remoteObj = [[WebBlacklistModel alloc]initWithCKRecord:record];
        }
        
        //记录CloudKit数据
        [remoteDataArray addObject:remoteObj];
        
        //已经被删除了，不需要添加或者更新
        if(needDeletedIdsMapper[[remoteObj getUuid]]) continue;
        
        BOOL isExist = NO;
        id<SyncProtocol> localObj = nil;
        for(id<SyncProtocol> obj in dataArray) {
            if([obj objectIsEqualTo:remoteObj]) {
                isExist = YES;
                localObj = obj;
                break;
            }
        }
        
        //判断本地和iCloud两者之间的更新时间
        if(isExist) {
            if([localObj getUpdateTime].integerValue > [remoteObj getUpdateTime].integerValue) {
                //本地的比较新，需要更新iCloud
                [needUploadRecords addObject:[localObj toCKRecord]];
            } else if([localObj getUpdateTime].integerValue < [remoteObj getUpdateTime].integerValue) {
                //iCloud的比较新，需要更新本地
                [needUpdateArray addObject:remoteObj];
            } else {
                //更新时间一致，不需要更新本地和iCloud
            }
        } else {
            //不存在，需要创建
            [needNewArray addObject:remoteObj];
        }
    }
    
    //找出需要在iCloud新建的数据
    for(id obj in dataArray) {
        BOOL isExist = NO;
        for(id item in remoteDataArray) {
            if([obj objectIsEqualTo:item]) {
                isExist = YES;
                break;
            }
        }
        
        if(!isExist) {
            //CloudKit不存在，需要新建或者更新到CloudKit
            [needUploadRecords addObject:[obj toCKRecord]];
        }
    }
    
    if(cloudType == CloudModelTypeUserScript) {
        //脚本
        if(needDeletedIds.count > 0) {
            //删除
            DatabaseUnit* unit = [DatabaseUnit removeUserScriptArray:needDeletedIds];
            DB_EXEC(unit);
        }
        
        if(needNewArray.count > 0) {
            //新建
            DatabaseUnit* unit = [DatabaseUnit addUserScriptArray:needNewArray];
            DB_EXEC(unit);
            
            //需要处理@require/@resource
        }
        
        if(needUpdateArray.count > 0) {
            //更新
            DatabaseUnit* unit = [DatabaseUnit updateUserScriptArray:needUpdateArray];
            DB_EXEC(unit);
            
            //需要处理@require/@resource
        }
    } else if(cloudType == CloudModelTypeTagit) {
        //标记模式
        if(needDeletedIds.count > 0) {
            //删除
            DatabaseUnit* unit = [DatabaseUnit removeTagitArray:needDeletedIds];
            DB_EXEC(unit);
            
            //需要删除广告规则
        }
        
        if(needNewArray.count > 0) {
            //新建
            DatabaseUnit* unit = [DatabaseUnit addTagitArray:needNewArray];
            DB_EXEC(unit);
        }
        
        if(needUpdateArray.count > 0) {
            //更新
            DatabaseUnit* unit = [DatabaseUnit updateTagitArray:needUpdateArray];
            DB_EXEC(unit);
        }
    } else if(cloudType == CloudModelTypeBookMark) {
        //书签
        if(needDeletedIds.count > 0) {
            //删除
            DatabaseUnit* unit = [DatabaseUnit removeBookMarkArray:needDeletedIds];
            DB_EXEC(unit);
        }
        
        if(needNewArray.count > 0) {
            //新建
            DatabaseUnit* unit = [DatabaseUnit addBookMarkArray:needNewArray];
            DB_EXEC(unit);
        }
        
        if(needUpdateArray.count > 0) {
            //更新
            DatabaseUnit* unit = [DatabaseUnit updateBookMarkArray:needUpdateArray];
            DB_EXEC(unit);
        }
    } else if(cloudType == CloudModelTypeCustomTag) {
        //首页标签
        if(needDeletedIds.count > 0) {
            //删除
            DatabaseUnit* unit = [DatabaseUnit removeCustomtagArray:needDeletedIds];
            DB_EXEC(unit);
        }
        
        if(needNewArray.count > 0) {
            //新建
            DatabaseUnit* unit = [DatabaseUnit addCustomtagArray:needNewArray];
            DB_EXEC(unit);
        }
        
        if(needUpdateArray.count > 0) {
            //更新
            DatabaseUnit* unit = [DatabaseUnit updateCustomtagArray:needUpdateArray];
            DB_EXEC(unit);
        }
    } else if(cloudType == CloudModelTypeWebBlacklist) {
        //网页黑名单
        if(needDeletedIds.count > 0) {
            //删除
            DatabaseUnit* unit = [DatabaseUnit removeWebBlacklistArray:needDeletedIds];
            DB_EXEC(unit);
        }
        
        if(needNewArray.count > 0) {
            //新建
            DatabaseUnit* unit = [DatabaseUnit addWebBlacklistArray:needNewArray];
            DB_EXEC(unit);
        }
        
        if(needUpdateArray.count > 0) {
            //更新
            DatabaseUnit* unit = [DatabaseUnit updateWebBlacklistArray:needUpdateArray];
            DB_EXEC(unit);
        }
    }
    
    if(!hasPrivateChangeToken) {
        /// 只有全量更新才更新iCloud
        /// 同步到CloudKit
        if(needUploadRecords.count > 0) {
            //上传到CloudKit
            [self syncRecordsToCloudKit:needUploadRecords recordIDsToDelete:nil completion:nil];
        }
    }

    if(cloudType == CloudModelTypeUserScript) {
        if(needDeletedIds.count>0 || needNewArray.count>0 || needUpdateArray.count>0) {
            //有更新才刷新，否则每次打开都会刷新一次
            dispatch_async(dispatch_get_main_queue(), ^{
                //处理相关逻辑
                [[NSNotificationCenter defaultCenter] postNotificationName:kCloudKitDataDidChangeNotification object:nil userInfo:@{
                    @"needDeleteScriptIds" : needDeletedIds,
                    @"needNewScripts" : needNewArray,
                    @"needUpdateScripts" : needUpdateArray
                }];
                
                //重新加载脚本
                [[NSNotificationCenter defaultCenter] postNotificationName:kReloadUserScriptNotification object:nil userInfo:nil];
            });
        }
    }
}


@end
