//
//  ContextMenuBaseView.m
//  PPBrowser
//
//  Created by qingbin on 2023/12/1.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import "ContextMenuBaseView.h"

#import "Masonry.h"
#import "MaizyHeader.h"
#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "ReactiveCocoa.h"
#import "UIView+FrameHelper.h"
#import "UIViewController+Helper.h"
#import "NSObject+Helper.h"

#import "BrowserUtils.h"
#import "ThemeProtocol.h"

#import "UIView+FrameHelper.h"
#import "PPNotifications.h"

@interface ContextMenuBaseView ()<ThemeProtocol>

@property (nonatomic, weak) UIView *sourceView;
/// 主要用来定位（不显示）
@property (nonatomic, strong) UIImageView *snapshotImageView;

@property (nonatomic, strong) UIView *contextMenuView;

@property (nonatomic, strong) UIVisualEffectView *contextMenuBlurView;

@property (nonatomic, assign) BOOL isAnimating;

@end

@implementation ContextMenuBaseView

- (instancetype)initWithSourceView:(UIView *)sourceView
{
    self = [super init];
    if(self) {
        self.backgroundColor = UIColor.clearColor;
        self.sourceView = sourceView;
        
        [self addSubviews];
        [self defineLayout];
        [self setupObservers];
        
        [self applyTheme];
        
        [self commonInit];
    }
    
    return self;
}

- (void)show
{
    [self fadeIn];
}

- (void)commonInit
{
    //子类初始化方法
}

- (void)dealloc
{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        UIBlurEffect* blurEffect = [UIBlurEffect effectWithStyle:UIBlurEffectStyleDark];
        self.contextMenuBlurView.effect = blurEffect;
    } else {
        UIBlurEffect* blurEffect = [UIBlurEffect effectWithStyle:UIBlurEffectStyleLight];
        self.contextMenuBlurView.effect = blurEffect;
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}

#pragma mark -- 目标视图在下方
- (BOOL)isContextMenuUp
{
    return self.snapshotImageView.centerY > kScreenHeight/2.0;
}

#pragma mark -- 目标视图在右边
- (BOOL)isContextMenuRight
{
    return self.snapshotImageView.centerX > kScreenWidth/2.0;
}

#pragma mark -- 显示

- (void)fadeIn
{
    if(self.isAnimating) return;
    self.isAnimating = YES;
    
    [self _showContenxtMenu];
}

- (void)_showContenxtMenu
{
    self.contextMenuView.transform = [self optionsViewFirstTransform:[self isContextMenuUp]];
    
    @weakify(self)
    [UIView animateWithDuration:0.5
                          delay:0
         usingSpringWithDamping:0.7
          initialSpringVelocity:5
                        options:UIViewAnimationOptionCurveLinear animations:^{
        @strongify(self)
        self.contextMenuView.alpha = 1.0;
        self.contextMenuView.transform = [self optionsViewSecondTransform];
    } completion:^(BOOL finished) {
        @strongify(self)
        @weakify(self)
        [UIView animateWithDuration:0.2 animations:^{
            @strongify(self)
            self.contextMenuView.transform = [self optionsViewThirdTransform];
        } completion:^(BOOL finished) {
            @strongify(self)
            self.isAnimating = NO;
        }];
    }];
}

#pragma mark -- 关闭

- (void)fadeOutAndClose
{
    if(self.isAnimating) return;
    self.isAnimating = YES;
    
    @weakify(self)
    [UIView animateWithDuration:0.3 animations:^{
        @strongify(self)
        self.contextMenuView.alpha = 0;
        self.contextMenuView.transform = [self optionsViewFirstTransform:[self isContextMenuUp]];
    } completion:^(BOOL finished) {
        @strongify(self)
        self.isAnimating = NO;
        
        [self removeFromSuperview];
    }];
}

#pragma mark -- 三个变换矩阵

- (CGAffineTransform)optionsViewFirstTransform:(BOOL)isContextMenuUp
{
    float scale = 0.05;
    CGAffineTransform transform1 = CGAffineTransformConcat(CGAffineTransformMakeScale(scale, scale), CGAffineTransformIdentity);
    CGAffineTransform transform2 = CGAffineTransformConcat(CGAffineTransformMakeTranslation(0, (!isContextMenuUp ? -1 : 1) * 120), transform1);

    return transform2;
}

- (CGAffineTransform)optionsViewSecondTransform
{
    float scale = 1.0;
    CGAffineTransform transform = CGAffineTransformConcat(CGAffineTransformMakeScale(scale, scale), CGAffineTransformIdentity);
    return transform;
}

- (CGAffineTransform)optionsViewThirdTransform
{
    return CGAffineTransformIdentity;
}

#pragma mark -- handle events

- (void)setupObservers
{
    UITapGestureRecognizer* tap = [UITapGestureRecognizer new];
    [self addGestureRecognizer:tap];
    @weakify(self)
    [tap.rac_gestureSignal subscribeNext:^(id x) {
        @strongify(self)
        [self fadeOutAndClose];
    }];
    
    //暗黑模式
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(darkThemeDidChangeNotification:)
                                                 name:kDarkThemeDidChangeNotification
                                               object:nil];
}

#pragma mark -- layout

- (void)addSubviews
{
    [self addSubview:self.snapshotImageView];
    [self addSubview:self.contextMenuView];
    
    [self.contextMenuView addSubview:self.contextMenuBlurView];
}

- (void)defineLayout
{
    CGRect rect = [self.sourceView convertRect:self.sourceView.bounds toView:self.sourceView.window];
    self.snapshotImageView.frame = rect;
    
    float spacing = [self offsetSpacing];
    [self.contextMenuView mas_makeConstraints:^(MASConstraintMaker *make) {
        if([self isContextMenuUp]) {
            //在下面
            make.bottom.equalTo(self.snapshotImageView.mas_top).offset(-spacing);
        } else {
            //在上面
            make.top.equalTo(self.snapshotImageView.mas_bottom).offset(spacing);
        }
        
        if([self isContextMenuRight]) {
            //在右边
            make.right.equalTo(self.snapshotImageView);
        } else {
            //在左边
            make.left.equalTo(self.snapshotImageView);
        }
    }];
    
    [self.contextMenuBlurView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.contextMenuView);
    }];
}

/// 偏移距离
- (float)offsetSpacing
{
    return 15.0;
}

#pragma mark -- Getters

- (UIView *)contextMenuView
{
    if(!_contextMenuView) {
        _contextMenuView = [UIView new];
        
        _contextMenuView.alpha = 0;
        _contextMenuView.layer.cornerRadius = 14;
        _contextMenuView.layer.shadowColor = UIColor.blackColor.CGColor;
        _contextMenuView.layer.shadowOffset = CGSizeMake(2, 5);
        _contextMenuView.layer.shadowRadius = 6;
        _contextMenuView.layer.shadowOpacity = 0.08;
    }
    
    return _contextMenuView;
}

- (UIVisualEffectView *)contextMenuBlurView
{
    if(!_contextMenuBlurView) {
        UIBlurEffect* blurEffect = [UIBlurEffect effectWithStyle:UIBlurEffectStyleLight];
        UIVisualEffectView* blurView = [[UIVisualEffectView alloc]initWithEffect:blurEffect];
        
        blurView.layer.cornerRadius = 14;
        blurView.clipsToBounds = YES;
        
        _contextMenuBlurView = blurView;
    }
    
    return _contextMenuBlurView;
}

- (UIImageView *)snapshotImageView
{
    if(!_snapshotImageView) {
        _snapshotImageView = [UIImageView new];
        _snapshotImageView.hidden = YES;
    }
    
    return _snapshotImageView;
}

@end
