//
//  ContextMenuBaseView.h
//  PPBrowser
//
//  Created by qingbin on 2023/12/1.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import <UIKit/UIKit.h>

#import "ThemeProtocol.H"

@interface ContextMenuBaseView : UIView<ThemeProtocol>

- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)new NS_UNAVAILABLE;

- (instancetype)initWithSourceView:(UIView *)sourceView;

- (void)commonInit;

- (void)addSubviews;

- (void)defineLayout;

/// 偏移距离
- (float)offsetSpacing;

- (void)show;

/// 需要指定宽+高
@property (readonly) UIVisualEffectView *contextMenuBlurView;

/// 主要用来定位（不显示）
@property (readonly) UIImageView *snapshotImageView;

@end
