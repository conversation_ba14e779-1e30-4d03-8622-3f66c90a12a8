//
//  ContextMenuCell.m
//  PPBrowser
//
//  Created by qingbin on 2023/12/1.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import "ContextMenuCell.h"

#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "UIColor+Helper.h"
#import "UIView+Helper.h"

#import "MaizyHeader.h"
#import "UIImage+Extension.h"

#import "ThemeProtocol.h"

@interface ContextMenuCell ()<ThemeProtocol>

//左icon
@property (nonatomic, strong) UIImageView *leftImageView;
//标题
@property (nonatomic, strong) UILabel *titleLabel;
//右icon
@property (nonatomic, strong) UIImageView *rightImageView;
//分割线
@property (nonatomic, strong) UIView *line;

@property (nonatomic, strong) ContextMenuItem *model;

@end

@implementation ContextMenuCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        self.backgroundColor = UIColor.clearColor;
        
        [self addSubviews];
        [self defineLayout];
        [self setupObservers];
        
        [self applyTheme];
    }
    
    return self;
}

- (void)updateWithModel:(ContextMenuItem *)model
{
    self.model = model;
    
    self.leftImageView.hidden = YES;
    self.rightImageView.hidden = YES;
    self.leftImageView.transform = CGAffineTransformMakeRotation(0);
    
    self.line.hidden = model.isLastInSection;
    
    if(model.type == ContextMenuTypeTitle) {
        //一级标题
        self.leftImageView.hidden = NO;
        
        //chevron.forward
        UIImage* image = [UIImage ext_systemImageNamed:@"chevron.right" pointSize:22 renderMode:UIImageRenderingModeAlwaysTemplate];
        self.leftImageView.image = image;
        //折叠与展开
        if(model.isOpen) {
            self.leftImageView.transform = CGAffineTransformMakeRotation(M_PI/2);
        } else {
            self.leftImageView.transform = CGAffineTransformMakeRotation(0);
        }
        
        //加粗
        self.titleLabel.font = [UIFont boldSystemFontOfSize:15];
        
    } else if(model.type == ContextMenuTypeItem) {
        //二级标题
        if(model.isSelected) {
            self.leftImageView.hidden = NO;
            
            UIImage* image = [UIImage ext_systemImageNamed:@"checkmark" pointSize:22 renderMode:UIImageRenderingModeAlwaysTemplate];
            self.leftImageView.image = image;
        } else {
            self.leftImageView.hidden = YES;
        }
        
        self.titleLabel.font = [UIFont systemFontOfSize:15];
    }
    
    self.titleLabel.text = model.title;
    
    [self applyTheme];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme || self.model.isKeepDarkMode) {
        self.titleLabel.textColor = UIColor.whiteColor;
        self.line.backgroundColor = [UIColor colorWithHexString:kDarkThemeColorLine];
        
        self.leftImageView.tintColor = UIColor.whiteColor;
        self.rightImageView.tintColor = UIColor.whiteColor;
    } else {
        self.titleLabel.textColor = [UIColor colorWithHexString:@"#333333"];
//        self.line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.line.backgroundColor = [UIColor colorWithHexString:@"#999999"];
        
        self.leftImageView.tintColor = [UIColor colorWithHexString:@"#333333"];
        self.rightImageView.tintColor = [UIColor colorWithHexString:@"#333333"];
    }
}

#pragma mark -- handle events

- (void)setupObservers
{
    UITapGestureRecognizer* tap = [UITapGestureRecognizer new];
    [self.contentView addGestureRecognizer:tap];
    @weakify(self)
    [tap.rac_gestureSignal subscribeNext:^(id x) {
        @strongify(self)
        if(self.model.type == ContextMenuTypeTitle) {
            //一级标题，折叠与展开
            self.model.isOpen = !self.model.isOpen;
            [UIView animateWithDuration:0.25 animations:^{
                if(self.model.isOpen) {
                    self.leftImageView.transform = CGAffineTransformMakeRotation(M_PI/2);
                } else {
                    self.leftImageView.transform = CGAffineTransformMakeRotation(0);
                }
            }];
            
            if(self.foldAction) {
                self.foldAction(self.model);
            }
            
            // 小震动
            UIImpactFeedbackGenerator* feedback = [[UIImpactFeedbackGenerator alloc]initWithStyle:UIImpactFeedbackStyleLight];
            [feedback prepare];
            [feedback impactOccurred];
            
        } else if(self.model.type == ContextMenuTypeItem) {
            //二级标题，点击效果
            if(self.model.isSelected) return;
            
            if(self.selectAction) {
                self.selectAction(self.model);
            }
        }
    }];
}

#pragma mark -- layout

- (void)addSubviews
{
    [self.contentView addSubview:self.titleLabel];
    [self.contentView addSubview:self.line];
    
    [self.contentView addSubview:self.leftImageView];
    [self.contentView addSubview:self.rightImageView];
}

- (void)defineLayout
{
    float size = 18;
    [self.leftImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.left.mas_offset(15);
        make.size.mas_equalTo(size);
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.left.equalTo(self.leftImageView.mas_right).offset(15);
    }];
    
    [self.rightImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.right.mas_offset(-15);
        make.size.mas_equalTo(size);
    }];
    
    [self.line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.bottom.mas_offset(0);
        make.height.mas_equalTo(0.5);
    }];
    
    [self.titleLabel setContentHuggingPriority:UILayoutPriorityRequired-1 forAxis:UILayoutConstraintAxisHorizontal];
    [self.titleLabel setContentCompressionResistancePriority:UILayoutPriorityRequired-1 forAxis:UILayoutConstraintAxisHorizontal];
}

#pragma mark -- Getters

- (UIImageView *)leftImageView
{
    if(!_leftImageView) {
        _leftImageView = [UIImageView new];
        _leftImageView.hidden = YES;
        _leftImageView.contentMode = UIViewContentModeScaleAspectFit;
    }
    
    return _leftImageView;
}

- (UIImageView *)rightImageView
{
    if(!_rightImageView) {
        _rightImageView = [UIImageView new];
        _rightImageView.hidden = YES;
    }
    
    return _rightImageView;
}

- (UILabel *)titleLabel
{
    if(!_titleLabel) {
        _titleLabel = [UIView createLabelWithTitle:@""
                                         textColor:[UIColor colorWithHexString:@"#333333"]
                                           bgColor:UIColor.clearColor
                                          fontSize:16
                                     textAlignment:NSTextAlignmentLeft
                                             bBold:NO];
    }
    
    return _titleLabel;
}

- (UIView *)line
{
    if(!_line) {
        _line = [UIView new];
    }
    
    return _line;
}

@end
