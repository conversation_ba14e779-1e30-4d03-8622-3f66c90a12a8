//
//  ContextMenuCell.h
//  PPBrowser
//
//  Created by qingbin on 2023/12/1.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import <UIKit/UIKit.h>

#import "ContextMenuItem.h"

@interface ContextMenuCell : UITableViewCell

- (void)updateWithModel:(ContextMenuItem *)model;

/// 折叠与展开，一级标题
@property (nonatomic, copy) void (^foldAction)(ContextMenuItem *model);
/// 点击选择，二级标题
@property (nonatomic, copy) void (^selectAction)(ContextMenuItem *model);

@end

