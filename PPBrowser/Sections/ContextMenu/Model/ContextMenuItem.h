//
//  ContextMenuItem.h
//  PPBrowser
//
//  Created by qingbin on 2023/12/1.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

#import "PPEnums.h"

@interface ContextMenuItem : NSObject

@property (nonatomic, strong) NSString *title;

@property (nonatomic, strong) UIImage *image;

@property (nonatomic, assign) ContextMenuType type;
//二级标题是否选中
@property (nonatomic, assign) BOOL isSelected;
//一级标题是否折叠/展开
@property (nonatomic, assign) BOOL isOpen;

@property (nonatomic, copy) void (^handler)(void);
//始终保持暗黑模式（例如视频模式）
@property (nonatomic, assign) BOOL isKeepDarkMode;
//相关model
@property (nonatomic, strong) id value;

@property (nonatomic, assign) BOOL isLastInSection;

@end

