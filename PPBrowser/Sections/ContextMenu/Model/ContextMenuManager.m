//
//  ContextMenuManager.m
//  PPBrowser
//
//  Created by qingbin on 2023/12/1.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import "ContextMenuManager.h"

#import "ContextMenuBaseView.h"

#import "ReactiveCocoa.h"
#import "NSObject+Helper.h"
#import "Masonry.h"

@interface ContextMenuManager ()

@end

@implementation ContextMenuManager

+ (void)showWithParentView:(UIView *)parentView
               contextMenu:(ContextMenuBaseView *)contextMenu
{
    if(!parentView) {
        parentView = [NSObject normalWindow];
    }
    [parentView addSubview:contextMenu];
    
    [contextMenu mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_offset(0);
    }];

    [contextMenu show];
}

- (void)hide
{
    
}

@end
