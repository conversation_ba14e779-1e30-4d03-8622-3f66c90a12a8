//
//  ContextMenuModel.m
//  PPBrowser
//
//  Created by qingbin on 2022/8/26.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "ContextMenuModel.h"

@implementation ContextMenuModel

- (LongPressElementType)elementType
{
    LongPressElementType element = LongPressElementTypeDefault;
    if(self.localName.length > 0) {
        NSString* localName = [self.localName lowercaseString];
        if([localName isEqualToString:@"video"]) {
            element = LongPressElementTypeVideo;
        } else if([localName isEqualToString:@"audio"]) {
            element = LongPressElementTypeAudio;
        } else if([localName isEqualToString:@"img"] || [localName isEqualToString:@"image"]) {
            element = LongPressElementTypeImage;
        } else {
            //4种类型都有可能，所以只能列举一些常见的格式
            NSString* name = self.src.lastPathComponent;
            if([name rangeOfString:@".m3u8"].location != NSNotFound
               || [name rangeOfString:@".mp4"].location != NSNotFound) {
                element = LongPressElementTypeVideo;
            } else if([name rangeOfString:@".mp3"].location != NSNotFound) {
                element = LongPressElementTypeAudio;
            } else if([name rangeOfString:@".png"].location != NSNotFound
                      || [name rangeOfString:@".jpeg"].location != NSNotFound) {
                element = LongPressElementTypeImage;
            } else {
                element = LongPressElementTypeLink;
            }
        }
    }

    return element;
}

@end




