//
//  PlaySettingContextMenuView.m
//  PPBrowser
//
//  Created by qingbin on 2023/12/1.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import "PlaySettingContextMenuView.h"

#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "NSObject+Helper.h"

#import "MaizyHeader.h"
#import "ThemeProtocol.h"

#import "ContextMenuCell.h"
#import "ContextMenuItem.h"

@interface PlaySettingContextMenuView ()<UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) UITableView* tableView;

@property (nonatomic, strong) NSMutableArray* model;

@property (nonatomic, strong) NSMutableArray* playRateModelArray;

@property (nonatomic, strong) NSMutableArray* scaleModelArray;

@property (nonatomic, strong) ContextMenuItem* playRateSectionModel;

@property (nonatomic, strong) ContextMenuItem* scaleModeSectionModel;

@end

@implementation PlaySettingContextMenuView

#pragma mark -- override

- (void)commonInit
{
    [super commonInit];
    
    [self updateWithModel];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        
    } else {
        
    }
    
    //视频模式保持暗黑模式
    UIBlurEffect* blurEffect = [UIBlurEffect effectWithStyle:UIBlurEffectStyleDark];
    self.contextMenuBlurView.effect = blurEffect;
    
    [self.tableView reloadData];
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}

- (float)cellHeight
{
    return 50;
}

//ZFPlayerScalingMode, 更新已选
- (void)updateWithRate:(float)rate scaleMode:(int)scaleMode
{
    //播放速率
    for(ContextMenuItem* item in self.playRateModelArray) {
        if(fabsf([item.value floatValue]-rate) <= 0.01) {
            item.isSelected = YES;
        } else {
            item.isSelected = NO;
        }
    }
    
    //画面尺寸
    for(ContextMenuItem* item in self.scaleModelArray) {
        if([item.value intValue] == scaleMode) {
            item.isSelected = YES;
        } else {
            item.isSelected = NO;
        }
    }
    
    [self.tableView reloadData];
}

- (void)updateWithModel
{
    self.playRateModelArray = [NSMutableArray array];
    self.scaleModelArray = [NSMutableArray array];
    
    //播放速率
    NSArray* playRates = @[@0.5, @0.75, @1.0, @1.25, @1.5, @1.75, @2.0];
    
    ContextMenuItem* item = [[ContextMenuItem alloc]init];
    item.title = NSLocalizedString(@"play.speed", nil);
    item.type = ContextMenuTypeTitle;
    item.isKeepDarkMode = YES;
    self.playRateSectionModel = item;
    [self.model addObject:item];
    
    for(NSNumber* obj in playRates) {
        NSString* text = [[NSString alloc]initWithFormat:@"%.2f", obj.floatValue];
        
        ContextMenuItem* item = [[ContextMenuItem alloc]init];
        item.title = text;
        item.type = ContextMenuTypeItem;
        item.isKeepDarkMode = YES;
        item.value = obj;
        
        [self.playRateModelArray addObject:item];
    }
    
    //画面尺寸
    NSArray* aspectOptions = @[
        NSLocalizedString(@"play.aspect.default", nil),
        NSLocalizedString(@"play.aspect.fix", nil),
        NSLocalizedString(@"play.aspect.aspectFill", nil),
        NSLocalizedString(@"play.aspect.fill", nil),
    ];
    
    item = [[ContextMenuItem alloc]init];
    item.title = NSLocalizedString(@"play.aspect.title", nil);
    item.type = ContextMenuTypeTitle;
    item.isKeepDarkMode = YES;
    self.scaleModeSectionModel = item;
    [self.model addObject:item];
    
    for(int i=0;i<aspectOptions.count;i++) {
        NSString* str = aspectOptions[i];
        
        ContextMenuItem* item = [[ContextMenuItem alloc]init];
        item.title = str;
        item.type = ContextMenuTypeItem;
        item.isKeepDarkMode = YES;
        item.value = @(i);
        [self.scaleModelArray addObject:item];
    }
    
    item = self.model.lastObject;
    item.isLastInSection = YES;
    
    [self.tableView reloadData];
}

#pragma mark -- layout

- (void)addSubviews
{
    [super addSubviews];
    
    [self.contextMenuBlurView.contentView addSubview:self.tableView];
}

- (void)defineLayout
{
    [super defineLayout];
    
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_offset(0);
    }];
    
    [self.contextMenuBlurView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(250);
        make.height.mas_equalTo([self cellHeight]*2);
    }];
}

#pragma mark -- 偏移距离
- (float)offsetSpacing
{
    return 5;
}

#pragma mark - UITableViewDataSource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return self.model.count;
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    ContextMenuItem* model = self.model[indexPath.row];

    ContextMenuCell* cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass(ContextMenuCell.class)];
    @weakify(self)
    [cell setFoldAction:^(ContextMenuItem *model) {
        @strongify(self)
        //查找要插入节点的下标位置
        int index = 0;
        for(int i=0;i<self.model.count;i++) {
            id obj = self.model[i];
            if(obj == model) {
                index = i;
                break;
            }
        }
        [self handleOpenAction:model index:index];
    }];
    
    [cell setSelectAction:^(ContextMenuItem *model) {
        @strongify(self)
        [self handleSelect:model];
    }];
    
    [cell updateWithModel:model];

    return cell;
}

///点击事件
- (void)handleSelect:(ContextMenuItem *)item
{
    //全部反选
    for(ContextMenuItem* item in self.model) {
        item.isSelected = NO;
    }
    //选择当前
    item.isSelected = YES;
    
    BOOL isPlayRateModel = NO;
    for(ContextMenuItem* obj in self.playRateModelArray) {
        if(obj == item) {
            isPlayRateModel = YES;
            break;
        }
    }
    
    if(isPlayRateModel) {
        //播放速率
        if(self.selectPlayRateAction) {
            self.selectPlayRateAction([item.value floatValue]);
        }
    } else {
        //画面尺寸
        if(self.selectScaleModeAction) {
            self.selectScaleModeAction([item.value intValue]);
        }
    }
    
    [self.tableView reloadData];
}

///展开/折叠
- (void)handleOpenAction:(ContextMenuItem *)model index:(int)index
{
    //折叠/展开
    NSArray* datas = nil;
    if(index == 0) {
        datas = self.playRateModelArray;
    } else {
        datas = self.scaleModelArray;
    }
    NSInteger numberOfRow = datas.count;
    NSMutableArray *rowArray = [NSMutableArray array];
    if (numberOfRow) {
        for (NSInteger i = index+1; i < numberOfRow+index+1; i++) {
            [rowArray addObject:[NSIndexPath indexPathForRow:i inSection:0]];
        }
    }
    
    if(rowArray.count > 0) {
        if(model.isOpen) {
            //展开
            for(NSInteger i=0, j=index+1;i<datas.count;i++,j++) {
                ContextMenuItem* item = datas[i];
                
//                if([item.src isEqualToString:self.selectSrc]) {
//                    item.isSelected = YES;
//                } else {
//                    item.isSelected = NO;
//                }
                
                [self.model insertObject:item atIndex:j];
            }
            
            //会导致文字堆叠，只能通过去掉动画来解决了
//            [self.tableView insertRowsAtIndexPaths:[NSArray arrayWithArray:rowArray] withRowAnimation:UITableViewRowAnimationNone];
        } else {
            //折叠
            NSMutableArray* removeList = [NSMutableArray array];
            for(NSInteger i=0, j=index+1;i<datas.count;i++,j++) {
                [removeList addObject:self.model[j]];
            }
            [self.model removeObjectsInArray:removeList];
            
            //会导致文字堆叠，只能通过去掉动画来解决了
//            [self.tableView deleteRowsAtIndexPaths:[NSArray arrayWithArray:rowArray] withRowAnimation:UITableViewRowAnimationNone];
        }
        
        //分割线逻辑
        for(ContextMenuItem* item in self.model) {
            item.isLastInSection = NO;
        }
        ContextMenuItem* item = self.model.lastObject;
        item.isLastInSection = YES;
        
        
        float maxHeight = kScreenHeight - [self offsetSpacing] - CGRectGetMaxY(self.snapshotImageView.frame) - 10;
        float height = 0;
        //判断当前展开的类型,高度自适应
        if(self.playRateSectionModel.isOpen && self.scaleModeSectionModel.isOpen) {
            //全部展开
            height = [self cellHeight]*(self.playRateModelArray.count+self.scaleModelArray.count+2);
        } else if(self.playRateSectionModel.isOpen) {
            //只展开了播放速率
            height = [self cellHeight]*(self.playRateModelArray.count+2);
        } else if(self.scaleModeSectionModel.isOpen) {
            //只展开了画面尺寸
            height = [self cellHeight]*(self.scaleModelArray.count+2);
        } else {
            //全部收起
            height = [self cellHeight]*2;
        }
        
        height = MIN(maxHeight, height);
        [self.contextMenuBlurView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(height);
        }];
        
        [self.tableView reloadData];
    }
}

#pragma mark -- Getters

- (UITableView *)tableView
{
    if(!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
        
        _tableView.separatorStyle  = UITableViewCellSeparatorStyleNone;
        _tableView.showsHorizontalScrollIndicator = NO;
        _tableView.showsVerticalScrollIndicator   = YES;
        _tableView.delegate   = self;
        _tableView.dataSource = self;
        _tableView.rowHeight = [self cellHeight];
        _tableView.showsVerticalScrollIndicator = NO;
        _tableView.showsHorizontalScrollIndicator = NO;
        _tableView.backgroundColor = UIColor.clearColor;
        
        [_tableView registerClass:[ContextMenuCell class] forCellReuseIdentifier:NSStringFromClass([ContextMenuCell class])];
        
        _tableView.tableFooterView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, CGFLOAT_MIN, CGFLOAT_MIN)];
        _tableView.tableHeaderView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, CGFLOAT_MIN, CGFLOAT_MIN)];
    }
    
    return _tableView;
}

- (NSMutableArray *)model
{
    if(!_model) {
        _model = [NSMutableArray array];
    }
    
    return _model;
}

@end
