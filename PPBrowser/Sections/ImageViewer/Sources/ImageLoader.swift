import Foundation
//#if canImport(SDWebImage)
//import SDWebImage
//#endif

import UIKit
import Kingfisher

public protocol ImageLoader {
    func loadImage(_ url: URL, placeholder: UIImage?, imageView: UIImageView, completion: @escaping (_ image: UIImage?) -> Void)
}

@objcMembers
public class URLSessionImageLoader: NSObject,ImageLoader {
//    public init() {}

    public func loadImage(_ url: URL, placeholder: UIImage?, imageView: UIImageView, completion: @escaping (UIImage?) -> Void) {
        if let placeholder = placeholder {
            imageView.image = placeholder
        }

        DispatchQueue.global(qos: .background).async {
            guard let data = try? Data(contentsOf: url), let image = UIImage(data: data) else {
                completion(nil)
                return
            }

            DispatchQueue.main.async {
                UIView.setAnimationsEnabled(false)
                imageView.image = image
                completion(image)
                UIView.setAnimationsEnabled(true)
            }
        }
    }
}

struct SDWebImageLoader: ImageLoader {
    func loadImage(_ url: URL, placeholder: UIImage?, imageView: UIImageView, completion: @escaping (UIImage?) -> Void) {
        imageView.sd_setImage(with: url) { (img, error, type, url) in
            DispatchQueue.main.async {
                completion(img)
            }
        }
    }
}

struct KingfisherLoader: ImageLoader {
    func loadImage(_ url: URL, placeholder: UIImage?, imageView: UIImageView, completion: @escaping (UIImage?) -> Void) {
        imageView.kf.setImage(with: url) { result in
            switch result {
                case .success(let value):
                    completion(value.image)
                case .failure(_):
                    imageView.image = placeholder
                    completion(nil)
            }
        }
    }
}
