//
//  PhotoBrowserController.m
//  PPBrowser
//
//  Created by qingbin on 2022/4/26.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "PhotoBrowserController.h"

#import "MaizyHeader.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"

#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "NSObject+Helper.h"
#import "UIView+FrameHelper.h"

#import "GalleryFlowLayout.h"

#import "ImageViewerCell.h"
#import "PPBrowser-Swift.h"

#import "ThemeProtocol.h"
#import "BrowserUtils.h"

#import "BookToolbar.h"

@interface PhotoBrowserController ()<UICollectionViewDelegate, UICollectionViewDataSource>

@property (nonatomic, strong) NSMutableArray<NSURL *>* imageUrls;
@property (nonatomic, strong) NSMutableArray<PhotoBrowserModel *>* model;

@property (nonatomic, strong) UICollectionView *collectionView;

@property (nonatomic, strong) UIVisualEffectView *blurView;

@property (nonatomic, strong) UIButton* rightButton;
//选择状态
@property (nonatomic, assign) BOOL isEditStatus;
//底部工具栏
@property (nonatomic, strong) BookToolbar *toolbar;

//当前网站对应的cookie
@property (nonatomic, strong) NSDictionary *cookies;
//当前网站对应的referer
@property (nonatomic, strong) NSString *referer;
//当前对应的UserAgent
@property (nonatomic, strong) NSString *userAgent;

@end

@implementation PhotoBrowserController

- (instancetype)initWithCookie:(NSDictionary *)cookies
                       referer:(NSString *)referer
                     userAgent:(NSString *)userAgent
{
    self = [super init];
    if(self) {
        self.cookies = cookies;
        self.referer = referer;
        self.userAgent = userAgent;
        
        [self addSubviews];
        [self defineLayout];
    }
    
    return self;
}

- (void)viewDidLoad
{
    [super viewDidLoad];
    
    self.navigationItem.title = NSLocalizedString(@"photoBrowser.title", nil);
    self.navigationController.navigationBar.backgroundColor = UIColor.whiteColor;
    
    self.view.backgroundColor = UIColor.clearColor;
    
    [self createCustomLeftBarButtonItem];
    [self _createCustomRightBarButtonItem];
    [self handleEvents];
    [self applyTheme];
}

- (void)updateWithModel:(NSArray<PhotoBrowserModel*>*)model
{
    [self.imageUrls removeAllObjects];
    self.model = [model mutableCopy];
    
    for(PhotoBrowserModel* obj in model) {
        [self.imageUrls addObject:[NSURL URLWithString:obj.url]];
    }
    
    [self.collectionView reloadData];
}

//更新底部工具栏编辑状态
- (void)reloadEditStatus
{
    BOOL isSelectAll = YES;
    BOOL hasAtLeastOne = NO;
    for(PhotoBrowserModel* obj in self.model) {
        if(obj.editStauts == EditStatusUnSelected) {
            isSelectAll = NO;
        } else if(obj.editStauts == EditStatusSelected) {
            hasAtLeastOne = YES;
        }
    }
    
    [self.toolbar updateSelectAll:isSelectAll];
    [self.toolbar updateButtonEnabled:hasAtLeastOne];
}

- (void)handleEvents
{
    @weakify(self)
    [self.toolbar setSelectAllAction:^(BOOL isSelectedAll) {
        @strongify(self)
        [self.toolbar updateSelectAll:isSelectedAll];
        
        for(PhotoBrowserModel *obj in self.model) {
            if(isSelectedAll) {
                //全选
                obj.editStauts = EditStatusSelected;
            } else {
                obj.editStauts = EditStatusUnSelected;
            }
        }
        
        [self reloadEditStatus];
    }];
    
    [self.toolbar setConfirmAction:^{
        @strongify(self)
        [self handleBatchAction];
    }];
    
    [RACObserve(self, isEditStatus) subscribeNext:^(id x) {
        @strongify(self)
        if(self.isEditStatus) {
            [self.rightButton setTitle:NSLocalizedString(@"common.cancel", nil) forState:UIControlStateNormal];
        } else {
            [self.rightButton setTitle:NSLocalizedString(@"photoBrowser.select", nil) forState:UIControlStateNormal];
        }
    }];
}

- (void)handleBatchAction
{
    //导出到相册
    NSMutableArray* selectedImageUrls = [NSMutableArray array];
    for(PhotoBrowserModel* obj in self.model) {
        if(obj.editStauts == EditStatusSelected) {
            [selectedImageUrls addObject:obj.url];
        }
    }
    
    [UIView showLoading:NSLocalizedString(@"tips.handling", nil)];
    
    [NSObject requestAuthorizationWithAuthorizedBlock:^{
        
        [KingfisherWrapper batchDownloadImages:selectedImageUrls completion:^(NSArray<UIImage *> * imageArray) {
            [KingfisherWrapper saveImagesToPhotos:imageArray completion:^(BOOL succ, NSError *error) {
                if(succ) {
                    [UIView showSucceed:NSLocalizedString(@"tips.savesuccess", nil)];
                } else {
                    [UIView showFailed:NSLocalizedString(@"tips.savefail", nil)];
                }
            }];
        }];
    } rejectBlock:^{
        [UIView showFailed:NSLocalizedString(@"tips.savefail", nil)];
    }];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        [self.rightButton setTitleColor:[UIColor colorWithHexString:@"#ffffff"] forState:UIControlStateNormal];
    } else {
        [self.rightButton setTitleColor:[UIColor colorWithHexString:@"#2D7AFE"] forState:UIControlStateNormal];
    }
}

#pragma mark -- 导航栏
- (void)_createCustomRightBarButtonItem
{
    UIButton* rightButton = [[UIButton alloc] initWithFrame:CGRectMake(0.0, 0.0f, 44.0f, 44.0)];
    [rightButton setBackgroundColor:[UIColor clearColor]];

    float font = iPadValue(20, 16);
    rightButton.titleLabel.font = [UIFont systemFontOfSize:font];
    [rightButton setTitle:NSLocalizedString(@"photoBrowser.select", nil) forState:UIControlStateNormal];
    [rightButton setTitleColor:[UIColor colorWithHexString:@"#2D7AFE"] forState:UIControlStateNormal];
    [rightButton addTarget:self action:@selector(rightBarbuttonClick) forControlEvents:UIControlEventTouchUpInside];
    UIBarButtonItem* barItem = [[UIBarButtonItem alloc] initWithCustomView:rightButton];
    UIBarButtonItem *spacer = [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemFixedSpace target:nil action:nil];
    spacer.width = -16.0f; // for example shift right bar button to the right

    self.rightButton = rightButton;
    
    self.navigationItem.rightBarButtonItems = @[spacer, barItem];
}

- (void)rightBarbuttonClick
{
    self.isEditStatus = !self.isEditStatus;
    //选择
    for(PhotoBrowserModel* obj in self.model) {
        if(self.isEditStatus) {
            //选择状态
            obj.editStauts = EditStatusUnSelected;
        } else {
            //重置状态
            obj.editStauts = EditStatusDefault;
        }
    }
    
    if(self.isEditStatus) {
        //进入编辑状态
        [UIView animateWithDuration:0.25 animations:^{
            [self.toolbar mas_updateConstraints:^(MASConstraintMaker *make) {
                make.bottom.equalTo(self.view).offset(0);
            }];
            
            [self.view layoutIfNeeded];
        }];
    } else {
        //退出编辑状态
        [UIView animateWithDuration:0.25 animations:^{
            [self.toolbar mas_updateConstraints:^(MASConstraintMaker *make) {
                make.bottom.equalTo(self.view).offset([BookToolbar toolbarHeight]);
            }];
            
            [self.view layoutIfNeeded];
        }];
        
        //重置
        [self.toolbar updateSelectAll:NO];
        [self.toolbar updateButtonEnabled:NO];
    }
}

#pragma mark -- layout

- (void)addSubviews
{
    [self.view addSubview:self.blurView];
    [self.view addSubview:self.collectionView];
    [self.view addSubview:self.toolbar];
}

- (void)defineLayout
{
    [self.blurView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
    }];
    
    [self.collectionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.right.equalTo(self.view);
        make.bottom.equalTo(self.toolbar.mas_top);
    }];
    
    [self.toolbar mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.view);
        make.height.mas_equalTo([BookToolbar toolbarHeight]);
        make.bottom.equalTo(self.view).offset([BookToolbar toolbarHeight]);
    }];
}

#pragma mark -- UICollectionViewDelegate

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section
{
    return self.model.count;
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath
{
    ImageViewerCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:NSStringFromClass(ImageViewerCell.class) forIndexPath:indexPath];
    
    [cell.imageView setupImageViewerWithUrls:self.imageUrls
                                initialIndex:indexPath.item
                                        from:self];
    
    [cell updateWithModel:self.model[indexPath.row]];
    
    @weakify(self)
    [cell setRemoveSvgAction:^(PhotoBrowserModel *model) {
        @strongify(self)
        [self.model removeObject:model];
        
        [self updateWithModel:self.model];
    }];
    
    return cell;
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath
{
    if(self.isEditStatus) {
        //选择状态
        PhotoBrowserModel* item = self.model[indexPath.row];
        if(item.editStauts == EditStatusSelected) {
            item.editStauts = EditStatusUnSelected;
        } else if(item.editStauts == EditStatusUnSelected) {
            item.editStauts = EditStatusSelected;
        }
        
        [self reloadEditStatus];
    }
}

#pragma mark -- layout init
- (UICollectionView *)collectionView
{
    if(!_collectionView) {
        GalleryFlowLayout *layout = [[GalleryFlowLayout alloc] init];
        _collectionView = [[UICollectionView alloc] initWithFrame:CGRectZero collectionViewLayout:layout];
        _collectionView.showsHorizontalScrollIndicator = NO;
        _collectionView.showsVerticalScrollIndicator = NO;
        _collectionView.backgroundColor = [UIColor clearColor];
        [_collectionView registerClass:[ImageViewerCell class] forCellWithReuseIdentifier:NSStringFromClass(ImageViewerCell.class)];
        _collectionView.delegate = self;
        _collectionView.dataSource = self;
    }
    
    return _collectionView;
}

- (NSMutableArray *)model
{
    if(!_model) {
        _model = [NSMutableArray array];
    }
    
    return _model;
}

- (NSMutableArray *)imageUrls
{
    if(!_imageUrls) {
        _imageUrls = [NSMutableArray array];
    }
    
    return _imageUrls;
}

- (UIVisualEffectView *)blurView
{
    if(!_blurView) {
        UIBlurEffect* blurEffect = [UIBlurEffect effectWithStyle:UIBlurEffectStyleLight];
        UIVisualEffectView* blurView = [[UIVisualEffectView alloc]initWithEffect:blurEffect];
        _blurView = blurView;
    }
    
    return _blurView;
}

- (BookToolbar *)toolbar
{
    if(!_toolbar) {
        _toolbar = [BookToolbar new];
        [_toolbar updateWithButtonTitle:NSLocalizedString(@"photoBrowser.export.album", nil)];
    }
    
    return _toolbar;
}

@end
