//
//  PhotoBrowserController.h
//  PPBrowser
//
//  Created by qing<PERSON> on 2022/4/26.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <UIKit/UIKit.h>

#import "BaseViewController.h"
#import "PhotoBrowserModel.h"

@interface PhotoBrowserController : BaseViewController

- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)new NS_UNAVAILABLE;

- (instancetype)initWithCookie:(NSDictionary *)cookies
                       referer:(NSString *)referer
                     userAgent:(NSString *)useragent;

- (void)updateWithModel:(NSArray<PhotoBrowserModel*>*)model;

@end

