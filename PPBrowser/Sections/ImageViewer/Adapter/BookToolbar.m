//
//  BookToolbar.m
//  Reader
//
//  Created by qingbin on 2023/9/15.
//

#import "BookToolbar.h"

#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "UIView+FrameHelper.h"
#import "NSObject+Helper.h"
#import "NSFileManager+Helper.h"

#import "BrowserUtils.h"
#import "AppButton.h"

#import "ThemeProtocol.h"

@interface BookToolbar ()
//删除
@property (nonatomic, strong) UIButton *actionButton;
//全选
@property (nonatomic, strong) AppButton *selectAllBtn;

@end

@implementation BookToolbar

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if(self) {
        [self addSubviews];
        [self defineLayout];
        [self setupObservers];
        
        self.backgroundColor = [UIColor colorWithHexString:@"#ffffff"];
        
        [self applyTheme];
    }
    
    return self;
}

- (void)updateWithButtonTitle:(NSString *)title
{
    [self.actionButton setTitle:title forState:UIControlStateNormal];
}

- (void)updateButtonEnabled:(BOOL)enabled
{
    self.actionButton.enabled = enabled;
    
    if(enabled) {
        self.actionButton.backgroundColor = [[UIColor colorWithHexString:@"#2D7AFE"] colorWithAlphaComponent:1.0];
    } else {
        self.actionButton.backgroundColor = [[UIColor colorWithHexString:@"#2D7AFE"] colorWithAlphaComponent:0.6];
    }
}

- (void)updateSelectAll:(BOOL)selectAll
{
    if(selectAll) {
        //点击全选
        self.selectAllBtn.tag = 1;
        UIImage* image = [UIImage imageNamed:@"standard_checkmark_Selected"];
        [self.selectAllBtn setImage:image];
    } else {
        self.selectAllBtn.tag = 0;
        UIImage* image = [UIImage imageNamed:@"standard_checkmark_UnSelected"];
        [self.selectAllBtn setImage:image];
    }
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        [self.selectAllBtn setTitleColor:[UIColor colorWithHexString:@"#ffffff"]];
    } else {
        self.backgroundColor = [UIColor colorWithHexString:@"#ffffff"];
        [self.selectAllBtn setTitleColor:[UIColor colorWithHexString:@"#333333"]];
    }
}

- (void)addSubviews
{
    [self addSubview:self.actionButton];
    [self addSubview:self.selectAllBtn];
}

- (void)defineLayout
{
    [self.actionButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_offset(-20);
        make.size.mas_equalTo(CGSizeMake(150, 44));
        make.top.mas_offset(5);
    }];
    
    [self.selectAllBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_offset(0);
        make.left.mas_offset(0);
        make.size.mas_equalTo(CGSizeMake(150, 54));
    }];
}

- (void)setupObservers
{
    @weakify(self)
    [self.selectAllBtn setTapAction:^{
        @strongify(self)
        if(self.selectAllBtn.tag == 0) {
            //点击全选
            self.selectAllBtn.tag = 1;
            
            UIImage* image = [UIImage imageNamed:@"standard_checkmark_Selected"];
            [self.selectAllBtn setImage:image];
        } else {
            self.selectAllBtn.tag = 0;
            UIImage* image = [UIImage imageNamed:@"standard_checkmark_UnSelected"];
            [self.selectAllBtn setImage:image];
        }
        
        if(self.selectAllAction) {
            self.selectAllAction(self.selectAllBtn.tag == 1);
        }
    }];
    
    [[self.actionButton rac_signalForControlEvents:UIControlEventTouchUpInside]
     subscribeNext:^(id x) {
        @strongify(self)
        if(self.confirmAction) {
            self.confirmAction();
        }
    }];
}

+ (float)toolbarHeight
{
    if([BrowserUtils isiPad]) {
        //iPad
        return 90;
    } else {
        //iPhone
        UIWindow* window = YBIBNormalWindow();
        return 60+window.safeAreaInsets.bottom;
    }
}

//- (void)show
//{
//    self.hidden = NO;
//    
//    if([BrowserUtils isiPad]) {
//        //iPad
//        float height = [BookToolbar toolbarHeight];
//        float offsetY = self.superview.bounds.size.height - height;
//        
//        [UIView animateWithDuration:0.3 animations:^{
//            self.frame = CGRectMake(0, offsetY, self.superview.bounds.size.width, height);
//        }];
//    } else {
//        //iPhone
//        UIWindow* window = YBIBNormalWindow();
//        float topOffset = window.safeAreaInsets.top;
//        
//        float height = [BookToolbar toolbarHeight];
//        float offsetY = kScreenHeight - topOffset - 44 - height;
//        
//        [UIView animateWithDuration:0.3 animations:^{
//            self.frame = CGRectMake(0, offsetY, kScreenWidth, height);
//        }];
//    }
//}
//
//- (void)dismiss
//{
//    if([BrowserUtils isiPad]) {
//        //iPad
//        [UIView animateWithDuration:0.3 animations:^{
//            self.frame = CGRectMake(0, kScreenHeight, self.superview.bounds.size.width, [BookToolbar toolbarHeight]);
//        }];
//    } else {
//        //iPhone
//        [UIView animateWithDuration:0.3 animations:^{
//            self.frame = CGRectMake(0, kScreenHeight, kScreenWidth, [BookToolbar toolbarHeight]);
//        }];
//    }
//}

- (UIButton *)actionButton
{
    if(!_actionButton) {
        _actionButton = [UIButton new];
        [_actionButton setTitle:@"" forState:UIControlStateNormal];
        _actionButton.layer.cornerRadius = 5;
        _actionButton.layer.masksToBounds = YES;
        _actionButton.backgroundColor = [[UIColor colorWithHexString:@"#2D7AFE"] colorWithAlphaComponent:0.6];
        
        float font = iPadValue(22, 18);
        _actionButton.titleLabel.font = [UIFont systemFontOfSize:font];
        _actionButton.enabled = NO;
    }
    
    return _actionButton;
}

- (AppButton *)selectAllBtn
{
    if(!_selectAllBtn) {
        _selectAllBtn = [AppButton new];
        UIImageView* logo = _selectAllBtn.imageView;
        UILabel* label = _selectAllBtn.titleLabel;
        
        [_selectAllBtn.stackView addArrangedSubview:logo];
        [_selectAllBtn.stackView addArrangedSubview:label];
        _selectAllBtn.stackView.axis = UILayoutConstraintAxisHorizontal;
        _selectAllBtn.stackView.spacing = 10;
        
        label.text = NSLocalizedString(@"toolbar.selectAll", nil);
        UIImage* image = [UIImage imageNamed:@"standard_checkmark_UnSelected"];
        [self.selectAllBtn setImage:image];
        
        [logo mas_makeConstraints:^(MASConstraintMaker *make) {
            make.size.mas_equalTo(22);
        }];
    }
    
    return _selectAllBtn;
}

@end
