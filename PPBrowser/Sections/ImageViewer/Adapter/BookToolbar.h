//
//  BookToolbar.h
//  Reader
//
//  Created by q<PERSON><PERSON> on 2023/9/15.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface BookToolbar : UIView

@property (nonatomic, copy) void (^selectAllAction)(BOOL isSelectedAll);
@property (nonatomic, copy) void (^confirmAction)(void);

+ (float)toolbarHeight;

- (void)updateSelectAll:(BOOL)selectAll;

- (void)updateButtonEnabled:(BOOL)enabled;

- (void)updateWithButtonTitle:(NSString *)title;

@end

NS_ASSUME_NONNULL_END
