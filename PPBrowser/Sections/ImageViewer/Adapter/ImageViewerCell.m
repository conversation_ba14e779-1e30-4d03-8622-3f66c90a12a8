//
//  ImageViewerCell.m
//  PPBrowser
//
//  Created by qingbin on 2022/4/26.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "ImageViewerCell.h"

#import "Masonry.h"
#import "ReactiveCocoa.h"

#import "UIColor+Helper.h"
#import "NSObject+Helper.h"
#import "MaizyHeader.h"

#import "PPBrowser-Swift.h"
#import "PaddingNewLabel.h"
#import "ImageTypeChecker.h"

@interface ImageViewerCell ()
//标签，显示图片的格式
@property (nonatomic, strong) PaddingNewLabel *tagView;
//
@property (nonatomic, strong) PhotoBrowserModel *model;
//加载的loading
@property (nonatomic, strong) UIActivityIndicatorView* activityIndicator;
//选择logo
@property (nonatomic, strong) UIImageView* selectLogo;
//
@property (nonatomic, strong) RACDisposable *disposable;

@end

@implementation ImageViewerCell

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    
    if(self) {
        [self addSubviews];
        [self defineLayout];
    }
    
    return self;
}

- (void)updateWithModel:(PhotoBrowserModel *)model
{
    self.model = model;
    
    [KingfisherWrapper setImageWithURLString:model.url
                                     referer:model.referer
                                   userAgent:model.userAgent
                                     cookies:model.cookies
                                   imageView:self.imageView];
    
    if(self.model.imageType == NULL) {
        [self.activityIndicator startAnimating];
        self.activityIndicator.hidden = NO;
        
        @weakify(self)
        [ImageTypeChecker checkImageTypeFromURL:[NSURL URLWithString:model.url]
                                     completion:^(NSURL* imageURL, NSString *imageType, NSError *error) {
            @strongify(self)
            if(![self.model.url isEqualToString:imageURL.absoluteString]) return;
            
            [self.activityIndicator stopAnimating];
            self.activityIndicator.hidden = YES;
            self.model.imageType = imageType;
            
            if([imageType isEqualToString:@"svg"]) {
                if(self.removeSvgAction) {
                    self.removeSvgAction(self.model);
                }
                
                return;
            }
            
            if(imageType.length > 0) {
                //有值
                self.tagView.text = imageType;
            } else {
                //没有识别出来
                self.tagView.text = NSLocalizedString(@"userscript.noVersion", nil);
                //为了不用重新触发loading，区分@""和NULL
                self.model.imageType = @"";
            }
            
            self.tagView.backgroundColor = [self getBackgroundColorWithImageType:imageType];
        }];
    } else {
        [self.activityIndicator stopAnimating];
        self.activityIndicator.hidden = YES;

        if(self.model.imageType.length > 0) {
            //有值
            self.tagView.text = self.model.imageType;
        } else {
            //没有识别出来
            self.tagView.text = NSLocalizedString(@"userscript.noVersion", nil);
        }
        
        self.tagView.backgroundColor = [self getBackgroundColorWithImageType:self.model.imageType];
    }
    
    [self _handleUpdateWithModel:model];
    
    @weakify(self)
    self.disposable = [RACObserve(self.model, editStauts) subscribeNext:^(id x) {
        @strongify(self)
        @weakify(self)
        dispatch_async(dispatch_get_main_queue(), ^{
            @strongify(self)
            if(![self.model.url isEqualToString:model.url]) return;
            [self _handleUpdateWithModel:model];
        });
    }];
}

- (void)_handleUpdateWithModel:(PhotoBrowserModel *)model
{
    //0-默认，非编辑模式，1-编辑模式未选，2-编辑模式已选
    if(model.editStauts == EditStatusDefault) {
        self.selectLogo.hidden = YES;
        self.imageView.userInteractionEnabled = YES;
    } else {
        self.selectLogo.hidden = NO;
        self.imageView.userInteractionEnabled = NO;
        
        if(model.editStauts == EditStatusUnSelected) {
            self.selectLogo.image = [UIImage imageNamed:@"standard_checkmark_Normal"];
        } else if(model.editStauts == EditStatusSelected) {
            self.selectLogo.image = [UIImage imageNamed:@"standard_checkmark_Selected"];
        }
    }
}

- (UIColor *)getBackgroundColorWithImageType:(NSString *)imageType
{
    NSString* hexText = nil;
    
    //    @"jpg": @"JPEG",
    //    @"jpeg": @"JPEG",
    //    @"png": @"PNG",
    //    @"gif": @"GIF",
    //    @"tif": @"TIFF",
    //    @"tiff": @"TIFF",
    //    @"webp": @"WEBP",
    //    @"svg": @"SVG"
    
    if(!imageType || imageType.length==0) {
        hexText = @"#ff2e63";
    }
    
    //jpeg png gif tiff webp svg
    if([imageType isEqualToString:@"jpeg"]) {
        hexText = @"#2CBF8A";
    } else if([imageType isEqualToString:@"png"]) {
        hexText = @"#1890FF";
    } else if([imageType isEqualToString:@"gif"]) {
        hexText = @"#f08a5d";
    } else if([imageType isEqualToString:@"tiff"]) {
        hexText = @"#3f72af";
    } else if([imageType isEqualToString:@"webp"]) {
        hexText = @"#e0f9b5";
    } else if([imageType isEqualToString:@"svg"]) {
        hexText = @"#FE6200";
    }
    
    return [UIColor colorWithHexString:hexText];
}

- (void)setHighlighted:(BOOL)highlighted
{
    if(highlighted) {
        [UIView animateWithDuration:0.1 animations:^{
            self.transform = CGAffineTransformMakeScale(0.98, 0.98);
        }];
    } else {
        [UIView animateWithDuration:0.1 animations:^{
            self.transform = CGAffineTransformMakeScale(1.0, 1.0);
        }];
    }
}

#pragma mark -- layout

- (void)addSubviews
{
    [self.contentView addSubview:self.imageView];
    [self.contentView addSubview:self.tagView];
    [self.contentView addSubview:self.selectLogo];
    [self.contentView addSubview:self.activityIndicator];
}

- (void)defineLayout
{
    [self.imageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.contentView);
    }];
    
    [self.tagView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.right.equalTo(self.contentView);
    }];
    
    [self.selectLogo mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.bottom.equalTo(self.imageView).offset(-5);
        make.size.mas_equalTo(22);
    }];
    
    [self.activityIndicator mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.contentView);
    }];
}

#pragma mark -- Getters

- (UIImageView *)imageView
{
    if(!_imageView) {
        _imageView = [UIImageView new];
        _imageView.contentMode = UIViewContentModeScaleAspectFill;
        _imageView.clipsToBounds = YES;
    }
    
    return _imageView;
}

- (PaddingNewLabel *)tagView
{
    if(!_tagView) {
        _tagView = [PaddingNewLabel new];
        _tagView.textColor = UIColor.whiteColor;
        _tagView.font = [UIFont systemFontOfSize:13];
        _tagView.edgeInsets = UIEdgeInsetsMake(3, 5, 3, 5);
        _tagView.layer.cornerRadius = 2;
        _tagView.layer.masksToBounds = YES;
    }
    
    return _tagView;
}

- (UIActivityIndicatorView *)activityIndicator
{
    if(!_activityIndicator) {
        _activityIndicator = [[UIActivityIndicatorView alloc]initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleMedium];
        _activityIndicator.hidden = YES;
    }
    
    return _activityIndicator;
}

- (UIImageView *)selectLogo
{
    if(!_selectLogo) {
        _selectLogo = [UIImageView new];
        _selectLogo.image = [UIImage imageNamed:@"standard_checkmark_Normal"];
        _selectLogo.hidden = YES;
    }
    
    return _selectLogo;
}

@end
