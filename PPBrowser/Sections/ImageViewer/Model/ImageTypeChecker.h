//
//  ImageTypeChecker.h
//  PPBrowser
//
//  Created by qing<PERSON> on 2024/8/23.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>

@interface ImageTypeChecker : NSObject

+ (void)checkImageTypeFromURL:(NSURL *)imageURL 
                   completion:(void(^)(NSURL* imageURL, NSString *imageType, NSError *error))completion;

+ (NSString *)checkImageTypeFromUrl:(NSString *)imageUrl;

@end

