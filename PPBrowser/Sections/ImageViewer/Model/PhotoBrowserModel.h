//
//  PhotoBrowserModel.h
//  PPBrowser
//
//  Created by qingbin on 2024/8/23.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>

typedef NS_ENUM(NSInteger, EditStatus) {
    EditStatusDefault = 0, //非编辑模式
    EditStatusUnSelected = 1, //编辑模式未选
    EditStatusSelected = 2, //编辑模式已选
};

@interface PhotoBrowserModel : NSObject

@property (nonatomic, strong) NSString* url;

@property (nonatomic, strong) NSString* imageType;
//当前网站对应的cookie
@property (nonatomic, strong) NSDictionary *cookies;
//当前网站对应的referer
@property (nonatomic, strong) NSString *referer;
//当前对应的UserAgent
@property (nonatomic, strong) NSString *userAgent;

//0-默认，非编辑模式，1-编辑模式未选，2-编辑模式已选
@property (nonatomic, assign) EditStatus editStauts;

@end

