//
//  GalleryFlowLayout.m
//  PPBrowser
//
//  Created by qingbin on 2022/4/26.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "GalleryFlowLayout.h"

#import "Masonry.h"
#import "ReactiveCocoa.h"

#import "UIColor+Helper.h"
#import "NSObject+Helper.h"
#import "MaizyHeader.h"

@interface GalleryFlowLayout ()

@property (nonatomic, assign) int columns;

@property (nonatomic, assign) float horizontalCellSpacing;

@property (nonatomic, assign) float verticalCellSpacing;

@property (nonatomic, strong) NSMutableArray<UICollectionViewLayoutAttributes*> *cache;

@property (nonatomic, assign) float width;

@property (nonatomic, assign) float cellContentWidth;

@property (nonatomic, assign) float cellContentHeight;

@property (nonatomic, assign) float contentHeight;

@end

@implementation GalleryFlowLayout

- (instancetype)init
{
    self = [super init];
    if(self) {
        self.columns = 3;
        self.horizontalCellSpacing = 1;
        self.verticalCellSpacing  = 1;
        self.width = kScreenWidth;
        self.cellContentWidth = (self.width - (self.horizontalCellSpacing*(self.columns-1)))/(self.columns*1.0);
        self.cellContentHeight = self.cellContentWidth;
    }
    
    return self;
}

- (CGSize)collectionViewContentSize
{
    int numberOfItems = (int)[self.collectionView numberOfItemsInSection:0];
    int rows = numberOfItems/self.columns + (numberOfItems%self.columns==0?0:1);
    self.contentHeight = rows*self.verticalCellSpacing + rows*self.cellContentHeight;
    
    return CGSizeMake(self.width, self.contentHeight);
}

- (void)prepareLayout
{
    if(self.cache.count > 0) return;
    
    int numberOfItems = (int)[self.collectionView numberOfItemsInSection:0];
    for(int i=0;i<numberOfItems;i++) {
        NSIndexPath* indexPath = [NSIndexPath indexPathForRow:i inSection:0];
        
        int columnIndex = indexPath.row % self.columns;
        int rowIndex = (int)indexPath.row / self.columns;
        
        // create a frame for the item
        float x = columnIndex * self.horizontalCellSpacing + (self.cellContentWidth * columnIndex);
        float y = rowIndex * self.verticalCellSpacing + (self.cellContentHeight * rowIndex);
        
        CGRect itemRect = CGRectMake(x, y, self.cellContentWidth, self.cellContentHeight);
        UICollectionViewLayoutAttributes* attributes = [UICollectionViewLayoutAttributes layoutAttributesForCellWithIndexPath:indexPath];
        attributes.frame = itemRect;
        [self.cache addObject:attributes];
    }
}

- (UICollectionViewLayoutAttributes *)layoutAttributesForItemAtIndexPath:(NSIndexPath *)indexPath
{
    return self.cache[indexPath.row];
}

- (NSArray<__kindof UICollectionViewLayoutAttributes *> *)layoutAttributesForElementsInRect:(CGRect)rect
{
    NSMutableArray* layoutAttributes = [NSMutableArray array];
    for(UICollectionViewLayoutAttributes* item in self.cache) {
        CGRect intersect = CGRectIntersection(rect, item.frame);
        if(!CGRectEqualToRect(intersect, CGRectZero)) {
            [layoutAttributes addObject:item];
        }
    }
    
    return layoutAttributes;
}

- (BOOL)shouldInvalidateLayoutForBoundsChange:(CGRect)newBounds
{
    BOOL isEqual = CGSizeEqualToSize(newBounds.size, self.collectionView.bounds.size);
    return !isEqual;
}

- (void)invalidateLayout
{
    [self.cache removeAllObjects];
    [super invalidateLayout];
}

- (NSMutableArray<UICollectionViewLayoutAttributes *> *)cache
{
    if(!_cache) {
        _cache = [NSMutableArray array];
    }
    
    return _cache;
}

@end
