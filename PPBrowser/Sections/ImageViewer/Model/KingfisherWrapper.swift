//
//  KingfisherBridge.swift
//  PPBrowser
//
//  Created by qing<PERSON> on 2024/8/23.
//  Copyright © 2024 qingbin. All rights reserved.
//

import Foundation
import Kingfisher
import KingfisherWebP
import Photos

@objc
class KingfisherWrapper: NSObject {
    @objc static func setImageWithURL(_ url: URL, imageView: UIImageView) {
        imageView.kf.setImage(with: url)
    }
    
    @objc static func setImageWithURLString(_ urlString: String, 
                                            referer: String,
                                            userAgent: String,
                                            cookies: Dictionary<String, String>,
                                            imageView: UIImageView) {
        let modifier = AnyModifier { request in
            var r = request
            r.setValue(userAgent, forHTTPHeaderField: "User-Agent")
            r.setValue(referer, forHTTPHeaderField: "Referer")
            
            let cookieString = cookies.map { "\($0.key)=\($0.value)" }.joined(separator: "; ")
            r.setValue(cookieString, forHTTPHeaderField: "<PERSON>ie")
            
            return r
        }
        
        if let url = URL(string: urlString) {
            imageView.kf.setImage(with: url, placeholder: UIImage(named: "image_load_fail"), options: [.requestModifier(modifier)])
        }
    }
    
    @objc static func enableWebPSupport() {
        KingfisherManager.shared.defaultOptions += [
            .processor(WebPProcessor.default),
            .cacheSerializer(WebPSerializer.default)
        ]
    }
    
    @objc static func batchDownloadImages(_ urlStrings: [String], completion: @escaping ([UIImage]) -> Void) {
        let group = DispatchGroup()
        var downloadedImages: [UIImage] = []

        for urlString in urlStrings {
            group.enter()
            if let url = URL(string: urlString) {
                KingfisherManager.shared.retrieveImage(with: url) { result in
                    switch result {
                    case .success(let value):
                        downloadedImages.append(value.image)
                    case .failure(let error):
                        print("Failed to download image: \(urlString), error: \(error.localizedDescription)")
                    }
                    group.leave()
                }
            } else {
                group.leave()
            }
        }

        group.notify(queue: .main) {
            completion(downloadedImages)
        }
    }
    
    @objc static func saveImagesToPhotos(_ images: [UIImage], completion: @escaping (Bool, Error?) -> Void) {
        PHPhotoLibrary.requestAuthorization { status in
            guard status == .authorized else {
                completion(false, NSError(domain: "PhotoLibraryAccess", code: 0, userInfo: [NSLocalizedDescriptionKey: "Photo library access denied"]))
                return
            }

            PHPhotoLibrary.shared().performChanges({
                for image in images {
                    PHAssetChangeRequest.creationRequestForAsset(from: image)
                }
            }) { success, error in
                DispatchQueue.main.async {
                    completion(success, error)
                }
            }
        }
    }

    @objc static func saveImagesToFiles(_ images: [UIImage], completion: @escaping (Bool, Error?) -> Void) {
        let fileManager = FileManager.default
        guard let documentsDirectory = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first else {
            completion(false, NSError(domain: "FileManagerError", code: 0, userInfo: [NSLocalizedDescriptionKey: "Unable to access documents directory"]))
            return
        }

        var success = true
        var savedURLs: [URL] = []

        for (index, image) in images.enumerated() {
            let fileName = "image_\(index).jpg"
            let fileURL = documentsDirectory.appendingPathComponent(fileName)

            if let data = image.jpegData(compressionQuality: 0.8) {
                do {
                    try data.write(to: fileURL)
                    savedURLs.append(fileURL)
                } catch {
                    success = false
                    print("Error saving image: \(error.localizedDescription)")
                }
            }
        }

        completion(success, nil)
    }
}


