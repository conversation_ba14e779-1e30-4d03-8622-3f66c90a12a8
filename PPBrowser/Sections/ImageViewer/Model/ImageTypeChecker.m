//
//  ImageTypeChecker.m
//  PPBrowser
//
//  Created by qing<PERSON> on 2024/8/23.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import "ImageTypeChecker.h"

@implementation ImageTypeChecker

+ (void)checkImageTypeFromURL:(NSURL *)imageURL 
                   completion:(void(^)(NSURL* imageURL, NSString *imageType, NSError *error))completion {
    // 先检查URL的路径扩展名
    NSString *typeFromExtension = [self checkImageTypeFromUrl:imageURL.absoluteString];
    
    if (typeFromExtension) {
        // 如果能从扩展名判断出类型,直接返回结果
        completion(imageURL, typeFromExtension, nil);
        return;
    }
    
    // 如果无法从扩展名判断,则下载数据并检查内容
    // 创建一个可变请求，并设置 URL
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:imageURL];
    // 设置 Range 头字段，请求前 10 个字节
    [request setValue:@"bytes=0-10" forHTTPHeaderField:@"Range"];
    
    NSURLSession *session = [NSURLSession sharedSession];
    NSURLSessionDataTask *task = [session dataTaskWithRequest:request completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
        dispatch_async(dispatch_get_main_queue(), ^{
            if (error) {
                completion(imageURL, nil, error);
                return;
            }
            
            NSString *imageType = [self getImageTypeFromData:data];
            completion(imageURL, imageType, nil);
        });
    }];
    
    [task resume];
}

+ (NSString *)checkImageTypeFromUrl:(NSString *)imageUrl
{
    if([imageUrl hasPrefix:@"data:image/svg+xml"]) return @"svg";
    
    NSString *extension = [[NSURL URLWithString:imageUrl].pathExtension lowercaseString];
    NSString *typeFromExtension = [self getImageTypeFromExtension:extension];
    
    return typeFromExtension;
}

+ (NSString *)getImageTypeFromExtension:(NSString *)extension 
{
    NSDictionary *extensionMap = @{
        @"jpg": @"jpeg",
        @"jpeg": @"jpeg",
        @"png": @"png",
        @"gif": @"gif",
        @"tif": @"tiff",
        @"tiff": @"tiff",
        @"webp": @"webp",
        @"svg": @"svg"
    };
    
    return extensionMap[extension] ?: NULL;
}

+ (NSString *)getImageTypeFromData:(NSData *)data {
    if (data.length < 4) {
        return NULL;
    }
    
    uint8_t c;
    [data getBytes:&c length:1];
    
    switch (c) {
        case 0xFF:
            return @"jpeg";
        case 0x89:
            return @"png";
        case 0x47:
            return @"gif";
        case 0x49:
        case 0x4D:
            return @"tiff";
        case 0x52: {
            if (data.length < 12) {
                return NULL;
            }
            NSString *testString = [[NSString alloc] initWithData:[data subdataWithRange:NSMakeRange(0, 12)] encoding:NSASCIIStringEncoding];
            if ([testString hasPrefix:@"RIFF"] && [testString hasSuffix:@"WEBP"]) {
                return @"webp";
            }
            return NULL;
        }
            
        case 0x3C: {
            // '<' character, potential SVG
            if (data.length < 100) {
                return NULL;
            }
            NSString *svgTestString = [[NSString alloc] initWithData:[data subdataWithRange:NSMakeRange(0, 100)] encoding:NSUTF8StringEncoding];
            if ([svgTestString containsString:@"<svg"] || [svgTestString containsString:@"<?xml"] || [svgTestString containsString:@"<!DOCTYPE svg"]) {
                return @"svg";
            }
            return NULL;
        }

        default:
            return NULL;
    }
    
    return NULL;
}

@end
