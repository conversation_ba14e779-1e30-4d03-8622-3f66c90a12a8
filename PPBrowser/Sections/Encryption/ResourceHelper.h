//
//  ResourceHelper.h
//  PPBrowser
//
//  Created by qingbin on 2023/3/23.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>

@interface ResourceHelper : NSObject

+ (instancetype)shareInstance;

/// 脚本解析
@property (readonly) NSString *supported_apis;

@property (readonly) NSString *convert2RegExp;

@property (readonly) NSString *MatchPattern;

@property (readonly) NSString *parse_meta_line;

@property (readonly) NSString *parse_user_script;


@property (readonly) NSString *contextMenuHelper;

@property (readonly) NSString *installHelper;

@property (readonly) NSString *playlistDetector;

@property (readonly) NSString *rdBookDetail;

@property (readonly) NSString *sourceCodeGenerate;

@property (readonly) NSString *nightModeHelper;

@property (readonly) NSString *tagitHelper;

@property (readonly) NSString *jsAdBlockConvert;

@property (readonly) NSString *translateHelper;

@property (readonly) NSString *sessionRestoreHtml;

@property (readonly) NSString *blobHelper;

@property (readonly) NSString *autoPageHelper;

@property (readonly) NSString *markHelper;

@property (readonly) NSString *swipeHelper;

@property (readonly) NSString *readabilityHelper;

@property (readonly) NSString *readerHelper;

@end

