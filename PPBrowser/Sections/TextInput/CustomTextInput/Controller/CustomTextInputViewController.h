//
//  CustomTextInputViewController.h
//  PPBrowser
//
//  Created by qing<PERSON> on 2024/11/11.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import "BaseViewController.h"

NS_ASSUME_NONNULL_BEGIN

@interface CustomTextInputViewController : BaseViewController

- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)new NS_UNAVAILABLE;

- (instancetype)initWithTitle:(NSString *)title;

//更新placeholder
- (void)updateWithPlaceholder:(NSAttributedString *)placeholder;
//更新最大高度
- (void)updateWithMaxHeight:(float)maxHeight;
//更新最小高度
- (void)updateWithMinHeight:(float)minHeight;
//更新inset
- (void)updateEdgeInsets:(UIEdgeInsets)insets;
//更新字体大小
- (void)updateWithFont:(UIFont *)font;
//获取当前文本
- (NSString *)getText;

@property (nonatomic, copy) void (^saveAction)(NSString *text);

@end

NS_ASSUME_NONNULL_END
