//
//  CustomTextInputViewController.m
//  PPBrowser
//
//  Created by qingbin on 2024/11/11.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import "CustomTextInputViewController.h"

#import "CustomTextView.h"

#import "MaizyHeader.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"

#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "NSObject+Helper.h"

#import "BrowserUtils.h"

@interface CustomTextInputViewController ()<UITextViewDelegate, ThemeProtocol>
//沉底提示
@property (nonatomic ,strong) NSAttributedString *placeHolder;
//输入的文本内容
@property (nonatomic, strong) CustomTextView *textView;
//最小高度
@property (nonatomic, assign) float minHeight;
//最大高度
@property (nonatomic, assign) float maxHeight;
//保存并返回
@property (nonatomic, strong) UIButton *finishButton;

@end

@implementation CustomTextInputViewController

- (instancetype)initWithTitle:(NSString *)title
{
    self = [super init];
    if(self) {
        self.navigationItem.title = title;
    }
    
    return self;
}

- (void)viewDidLoad
{
    [super viewDidLoad];

    [self commonInit];
    
    [self addSubviews];
    [self defineLayout];
    [self handleEvents];
    [self createCustomLeftBarButtonItem];
    
    [self applyTheme];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        
        self.textView.textColor = UIColor.whiteColor;
        self.textView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
    } else {
        self.view.backgroundColor = UIColor.whiteColor;
        
        self.textView.textColor = [UIColor colorWithHexString:@"#1A1A1A"];
        self.textView.backgroundColor = [UIColor colorWithHexString:@"#FAFAFA"];
    }
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    //只有当前的controller会触发一次, 其它已存在的controller走通知
    if(needReload) {
        //修改暗黑模式
        [self applyTheme];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}

- (void)commonInit
{
    self.placeHolder = [[NSAttributedString alloc] initWithString:@""
                                    attributes:@{
                                                 NSForegroundColorAttributeName: [UIColor colorWithHexString:@"#B3B3B3"],
                                                 NSFontAttributeName: [UIFont systemFontOfSize:13.0f]
                                                 }];
    [self.textView updateWithPlaceholder:self.placeHolder];
    
    self.minHeight = 200;
    self.maxHeight = INT_MAX;
}

#pragma mark - 外部更新的方法

//更新placeholder
- (void)updateWithPlaceholder:(NSAttributedString *)placeholder
{
    self.placeHolder = placeholder;
    
    [self.textView updateWithPlaceholder:placeholder];
}

//更新最小高度
- (void)updateWithMinHeight:(float)minHeight
{
    self.minHeight = minHeight;
    [self.textView updateWithMinHeight:minHeight];
    
    [self.textView mas_updateConstraints:^(MASConstraintMaker *make) {
        //最小高度
        make.height.mas_greaterThanOrEqualTo(self.minHeight);
    }];
}

//更新最大高度
- (void)updateWithMaxHeight:(float)maxHeight
{
    self.maxHeight = maxHeight;
    [self.textView updateWithMaxHeight:maxHeight];
}

//更新inset
- (void)updateEdgeInsets:(UIEdgeInsets)insets
{
    self.textView.textContainerInset = insets;
}

//更新字体大小
- (void)updateWithFont:(UIFont *)font
{
    self.textView.font = font;
}

//获取当前文本
- (NSString *)getText
{
    if(![self.textView.text isEqualToString:self.placeHolder.string]) {
        return self.textView.text;
    }
    
    return NULL;
}

#pragma mark - handle events

- (void)handleEvents
{
    @weakify(self)
    [[self.finishButton rac_signalForControlEvents:UIControlEventTouchUpInside]
    subscribeNext:^(id x) {
        @strongify(self)
        [self.view endEditing:YES];
        
        if(self.saveAction) {
            self.saveAction([self getText]);
        }
    }];
}

#pragma mark - layout

- (void)addSubviews
{
    [self.view addSubview:self.textView];
    [self.view addSubview:self.finishButton];
}

- (void)defineLayout
{
    [self.textView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.top.mas_offset(0);
        //最小高度
        make.height.mas_greaterThanOrEqualTo(self.minHeight);
        make.bottom.lessThanOrEqualTo(self.finishButton.mas_top).offset(-iPadValue(15, 10));
    }];
    
    float offset = iPadValue(30, 15);
    [self.finishButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(offset);
        make.right.mas_offset(-offset);
        make.bottom.equalTo(self.view.mas_safeAreaLayoutGuideBottom);
        make.height.mas_equalTo(iPadValue(60, 44));
    }];
}

#pragma mark - getters

- (CustomTextView *)textView
{
    if(!_textView) {
        _textView = [[CustomTextView alloc]init];
        _textView.layer.cornerRadius = 5;
        _textView.layer.masksToBounds = YES;

        _textView.textColor = [UIColor colorWithHexString:@"#1A1A1A"];
        _textView.backgroundColor = [UIColor colorWithHexString:@"#FAFAFA"];
        _textView.font = [UIFont systemFontOfSize:13.0f];
        _textView.delegate = self;
        _textView.attributedText = self.placeHolder;
        _textView.textContainer.lineFragmentPadding = 0;    //会干扰textContainerInset
        _textView.textContainerInset = UIEdgeInsetsMake(10, 10, 29, 8);
        
        [_textView updateWithMinHeight:self.minHeight maxHeight:self.maxHeight];
    }

    return _textView;
}

- (UIButton *)finishButton
{
    if(!_finishButton) {
        _finishButton = [UIButton new];
        [_finishButton setTitle:@"保存并退出" forState:UIControlStateNormal];
        
        _finishButton.layer.cornerRadius = 5;
        _finishButton.layer.masksToBounds = YES;
        
        [_finishButton setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
        _finishButton.titleLabel.font = [UIFont boldSystemFontOfSize:iPadValue(20, 15)];
        [_finishButton setTitleColor:[UIColor colorWithHexString:@"#ffffff"] forState:UIControlStateNormal];
        [_finishButton setBackgroundColor:[UIColor colorWithHexString:@"#2D7AFE"]];
    }
    
    return _finishButton;
}

@end
