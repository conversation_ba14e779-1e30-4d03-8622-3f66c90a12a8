//
//  PopUpMenuController.m
//  PPBrowser
//
//  Created by qingbin on 2024/3/27.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import "PopUpMenuController.h"

#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "NSObject+Helper.h"

#import "MaizyHeader.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"

#import "PreferenceModel.h"
#import "PreferenceManager.h"
#import "BrowserUtils.h"
#import "PPNotifications.h"

#import "SettingSwitchView.h"
#import "SettingArrowView.h"
#import "SettingTextView.h"
#import "SettingTextAndArrowView.h"
#import "UIAlertController+SafePresentation.h"

@interface PopUpMenuController ()

@property (nonatomic, strong) UIStackView *stackView;
/// 弹出式窗口打开方式
@property (nonatomic, strong) SettingTextAndArrowView* popupWindowOptionView;
/// 视频模式
@property (nonatomic, strong) SettingSwitchView *playerView;
/// 阅读模式
@property (nonatomic, strong) SettingSwitchView *readerView;
/// 屏蔽广告
@property (nonatomic, strong) SettingSwitchView *adView;
/// 长按识别图片
@property (nonatomic, strong) SettingSwitchView *detectPhotoView;
/// 开启网页滑动手势
@property (nonatomic, strong) SettingSwitchView *swipeGestureView;

@end

@implementation PopUpMenuController

- (void)viewDidLoad
{
    [super viewDidLoad];
    
    [self addSubviews];
    [self defineLayout];
    [self setupObservers];
    
    [self updateWithModel];
    [self applyTheme];
}

// 导航栏样式
- (BaseNavigationBarStyle)preferredNavigationBarStyle
{
    return BaseNavigationBarStyleNoneWithDefaultContent;
}

- (CGSize)preferredContentSize
{
    float height = [SettingSwitchView height]*5 + [SettingTextAndArrowView height];
    //最宽320
    return CGSizeMake(MIN(320, ceil(0.8*kScreenWidth)), height);
}

+ (void)showAt:(UIViewController *)controller sourceRect:(CGRect)sourceRect
{
    PopUpMenuController* vc = [PopUpMenuController new];
    vc.modalPresentationStyle = UIModalPresentationPopover;
    vc.popoverPresentationController.sourceView = controller.view;
    vc.popoverPresentationController.sourceRect = sourceRect;
    vc.popoverPresentationController.permittedArrowDirections = UIPopoverArrowDirectionAny;
    
    vc.popoverPresentationController.delegate = vc;
    
    [controller presentViewController:vc animated:YES completion:nil];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
    } else {
        self.view.backgroundColor = [UIColor colorWithHexString:@"#ffffff"];
    }
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    //只有当前的controller会触发一次, 其它已存在的controller走通知
    if(needReload) {
        //修改暗黑模式
        [self applyTheme];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}

#pragma mark -- UIPopoverPresentationControllerDelegate

- (UIModalPresentationStyle)adaptivePresentationStyleForPresentationController:(UIPresentationController *)controller
{
    return UIModalPresentationNone;
}


- (void)updateWithModel
{
    BOOL enabledPlayer = [[PreferenceManager shareInstance].items.enabledPlayer boolValue];
    [self.playerView updateWithTitle:NSLocalizedString(@"webSetting.videomode.text", nil) isOn:enabledPlayer];
    
    BOOL enabledReader = [[PreferenceManager shareInstance].items.enabledReader boolValue];
    [self.readerView updateWithTitle:NSLocalizedString(@"webSetting.reader.text", nil) isOn:enabledReader];
    
    BOOL enabledAdblock = [[PreferenceManager shareInstance].items.enabledAdblock boolValue];
    [self.adView updateWithTitle:NSLocalizedString(@"webSetting.adfilter.text", nil) isOn:enabledAdblock];
    
    PopupWindowOption option = [[PreferenceManager shareInstance].items.popupWindowOption intValue];
    if(option == PopupWindowOptionAlwaysAsk) {
        [self.popupWindowOptionView updateWithTitle:NSLocalizedString(@"popupwindow.setting.title", nil) content:NSLocalizedString(@"popupwindow.setting.alwaysAsk", nil)];
    } else if(option == PopupWindowOptionDefault) {
        [self.popupWindowOptionView updateWithTitle:NSLocalizedString(@"popupwindow.setting.title", nil) content:NSLocalizedString(@"popupwindow.setting.default", nil)];
    } else if(option == PopupWindowOptionOpenInCurrent) {
        [self.popupWindowOptionView updateWithTitle:NSLocalizedString(@"popupwindow.setting.title", nil) content:NSLocalizedString(@"popupwindow.setting.openInCurrent", nil)];
    } else if(option == PopupWindowOptionOpenInNewWindow) {
        [self.popupWindowOptionView updateWithTitle:NSLocalizedString(@"popupwindow.setting.title", nil) content:NSLocalizedString(@"popupwindow.setting.openInInNewWindow", nil)];
    } else if(option == PopupWindowOptionOpenInBackWindow) {
        [self.popupWindowOptionView updateWithTitle:NSLocalizedString(@"popupwindow.setting.title", nil) content:NSLocalizedString(@"popupwindow.setting.openInInBackWindow", nil)];
    }
    
    BOOL enabledDetectPhoto = [[PreferenceManager shareInstance].items.enabledDetectPhoto boolValue];
    [self.detectPhotoView updateWithTitle:NSLocalizedString(@"webSetting.detect.photo.text", nil) isOn:enabledDetectPhoto];
    
    BOOL enableSwipeNavigation = [[PreferenceManager shareInstance].items.enableSwipeNavigation boolValue];
    [self.swipeGestureView updateWithTitle:NSLocalizedString(@"toolbar.enableSwipeNavigation.text", nil) isOn:enableSwipeNavigation];
}

- (void)setupObservers
{
    @weakify(self)
    [self.playerView setDidSwithAction:^(BOOL isOn) {
        [PreferenceManager shareInstance].items.enabledPlayer = @(isOn);
        [[PreferenceManager shareInstance] encode];
        
        [[NSNotificationCenter defaultCenter] postNotificationName:kReloadCurrentWebViewNotification object:nil];
    }];
    
    [self.readerView setDidSwithAction:^(BOOL isOn) {
        [PreferenceManager shareInstance].items.enabledReader = @(isOn);
        [[PreferenceManager shareInstance] encode];
        
        [[NSNotificationCenter defaultCenter] postNotificationName:kReloadCurrentWebViewNotification object:nil];
    }];
    
    [self.popupWindowOptionView setDidAction:^{
        @strongify(self)
        [self showPopupWindowOptionView];
    }];
    
    [self.adView setDidSwithAction:^(BOOL isOn) {
        [PreferenceManager shareInstance].items.enabledAdblock = @(isOn);
        [[PreferenceManager shareInstance] encode];
        
        [[NSNotificationCenter defaultCenter] postNotificationName:kReloadUserScriptNotification object:nil];
    }];
    
    [self.detectPhotoView setDidSwithAction:^(BOOL isOn) {
        [PreferenceManager shareInstance].items.enabledDetectPhoto = @(isOn);
        [[PreferenceManager shareInstance] encode];
    }];
    
    [self.swipeGestureView setDidSwithAction:^(BOOL isOn) {
        [PreferenceManager shareInstance].items.enableSwipeNavigation = @(isOn);
        [[PreferenceManager shareInstance] encode];
        
        //发送通知
        [[NSNotificationCenter defaultCenter] postNotificationName:kUpdateSwipeGesutreNotification object:nil];
    }];
}

- (void)notify
{
    //重新加载脚本
    [[NSNotificationCenter defaultCenter] postNotificationName:kReloadUserScriptNotification object:nil];
    //切换夜间模式
    [[NSNotificationCenter defaultCenter] postNotificationName:kDarkThemeDidChangeNotification object:nil];
}

#pragma mark -- layout
- (void)addSubviews
{
    [self.view addSubview:self.stackView];
}

- (void)defineLayout
{
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.view);
        make.left.mas_offset(5);
        make.right.mas_offset(-5);
    }];
    
    [self.playerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([SettingSwitchView height]);
    }];
    
    [self.readerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([SettingSwitchView height]);
    }];
    
    [self.popupWindowOptionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([SettingTextAndArrowView height]);
    }];
    
    [self.adView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([SettingSwitchView height]);
    }];
    
    [self.detectPhotoView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([SettingSwitchView height]);
    }];
    
    [self.swipeGestureView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([SettingSwitchView height]);
    }];
}

- (void)showPopupWindowOptionView
{
    UIAlertControllerStyle style = UIAlertControllerStyleActionSheet;
    if([BrowserUtils isiPad]) {
        //iPad
        style = UIAlertControllerStyleAlert;
    }
    UIAlertController *alertController = [UIAlertController alertControllerWithTitle:nil message:nil preferredStyle:style];
    [alertController addAction:([UIAlertAction actionWithTitle:NSLocalizedString(@"alert.cancel", nil) style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {

    }])];
    [alertController addAction:([UIAlertAction actionWithTitle:NSLocalizedString(@"popupwindow.setting.default", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [PreferenceManager shareInstance].items.popupWindowOption = @(PopupWindowOptionDefault);
        [[PreferenceManager shareInstance] encode];
        [self updateWithModel];
    }])];
    [alertController addAction:([UIAlertAction actionWithTitle:NSLocalizedString(@"popupwindow.setting.alwaysAsk", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [PreferenceManager shareInstance].items.popupWindowOption = @(PopupWindowOptionAlwaysAsk);
        [[PreferenceManager shareInstance] encode];
        [self updateWithModel];
    }])];
    [alertController addAction:([UIAlertAction actionWithTitle:NSLocalizedString(@"popupwindow.setting.openInCurrent", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [PreferenceManager shareInstance].items.popupWindowOption = @(PopupWindowOptionOpenInCurrent);
        [[PreferenceManager shareInstance] encode];
        [self updateWithModel];
    }])];
    [alertController addAction:([UIAlertAction actionWithTitle:NSLocalizedString(@"popupwindow.setting.openInInNewWindow", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [PreferenceManager shareInstance].items.popupWindowOption = @(PopupWindowOptionOpenInNewWindow);
        [[PreferenceManager shareInstance] encode];
        [self updateWithModel];
    }])];
    [alertController addAction:([UIAlertAction actionWithTitle:NSLocalizedString(@"popupwindow.setting.openInInBackWindow", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [PreferenceManager shareInstance].items.popupWindowOption = @(PopupWindowOptionOpenInBackWindow);
        [[PreferenceManager shareInstance] encode];
        [self updateWithModel];
    }])];
    
//    [self presentViewController:alertController animated:YES completion:nil];
    //v2.6.8, 统一present方法，防止崩溃
    [alertController presentSafelyFromViewController:self];
}

#pragma mark -- Getters

- (UIStackView *)stackView
{
    if(!_stackView) {
        _stackView = [[UIStackView alloc]initWithArrangedSubviews:@[
            self.popupWindowOptionView,
            self.adView,
            self.playerView,
            self.readerView,
            self.detectPhotoView,
            self.swipeGestureView,
        ]];
        _stackView.axis = UILayoutConstraintAxisVertical;
        _stackView.spacing = 0;
        
        _stackView.layer.cornerRadius = 10;
        _stackView.layer.masksToBounds = YES;
    }
    
    return _stackView;
}

- (SettingSwitchView *)adView
{
    if(!_adView) {
        _adView = [[SettingSwitchView alloc]initWithShowLine:YES];
    }
    
    return _adView;
}

- (SettingSwitchView *)playerView
{
    if(!_playerView) {
        _playerView = [[SettingSwitchView alloc]initWithShowLine:YES];
    }
    
    return _playerView;
}

- (SettingSwitchView *)readerView
{
    if(!_readerView) {
        _readerView = [[SettingSwitchView alloc]initWithShowLine:YES];
    }
    
    return _readerView;
}

- (SettingTextAndArrowView *)popupWindowOptionView
{
    if(!_popupWindowOptionView) {
        _popupWindowOptionView = [[SettingTextAndArrowView alloc]initWithShowLine:YES];
    }
    
    return _popupWindowOptionView;
}

- (SettingSwitchView *)detectPhotoView
{
    if(!_detectPhotoView) {
        _detectPhotoView = [[SettingSwitchView alloc]initWithShowLine:YES];
    }
    
    return _detectPhotoView;
}

- (SettingSwitchView *)swipeGestureView
{
    if(!_swipeGestureView) {
        _swipeGestureView = [[SettingSwitchView alloc]initWithShowLine:NO];
    }
    
    return _swipeGestureView;
}

@end
