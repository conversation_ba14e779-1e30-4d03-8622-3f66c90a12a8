//
//  SearchHelper.m
//  PPBrowser
//
//  Created by qingbin on 2025/4/12.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "SearchHelper.h"

@implementation SearchHelper

// 根据网页链接获取搜索词
+ (NSString *)searchKeywordFromUrl:(NSString *)url
{
    //v2.7.0, 如果匹配到是当前搜索引擎的url，那么提取搜索词，只显示搜索词
    NSArray<SearchModel *> *builtInSearchEngines = [[SearchManager shareInstance] getAllBuiltInSearchEngines];
    for (SearchModel *obj in builtInSearchEngines) {
        NSString *searchKeyword = [self searchKeywordWithModel:obj url:url];
        if (searchKeyword && searchKeyword.length > 0) {
            return searchKeyword;
        }
    }
    
    return nil;
}

+ (NSString *)searchKeywordWithModel:(SearchModel *)engine
                                 url:(NSString *)url
{
    NSString* searchTemplate = engine.searchTemplate;
    
    //替换searchTermComponent，否则生成的templateComponents可能为空
    NSString* searchTermComponent = @"focus_searchTermComponent";
    searchTemplate = [searchTemplate stringByReplacingOccurrencesOfString:engine.searchTermComponent withString:searchTermComponent];
    
    // 从searchTemplate中提取基础URL（不包含查询参数）和搜索参数名
    NSURLComponents* templateComponents = [NSURLComponents componentsWithString:searchTemplate];
    NSString* baseURL = [NSString stringWithFormat:@"%@://%@%@",
                         templateComponents.scheme,
                         templateComponents.host,
                         templateComponents.path];
    
    // 检查URL是否匹配当前搜索引擎的基础URL
    if([url containsString:baseURL]) {
        // 解析URL获取搜索词
        NSURLComponents* components = [NSURLComponents componentsWithString:url];
        NSArray* queryItems = components.queryItems;
        
        // 从searchTemplate中提取搜索参数名
        NSString* searchParam = nil;
        for(NSURLQueryItem* item in templateComponents.queryItems) {
            if([item.value isEqualToString:searchTermComponent]) {
                searchParam = item.name;
                break;
            }
        }
        
        if(searchParam) {
            // 在URL中查找对应的搜索参数
            for(NSURLQueryItem* item in queryItems) {
                if([item.name isEqualToString:searchParam]) {
                    // 找到搜索词参数，进行URL解码
                    NSString* searchTerm = [item.value stringByRemovingPercentEncoding];
                    if(searchTerm.length > 0) {
                        return searchTerm;
                    }
                }
            }
        }
    }
    
    return nil;
}

@end
