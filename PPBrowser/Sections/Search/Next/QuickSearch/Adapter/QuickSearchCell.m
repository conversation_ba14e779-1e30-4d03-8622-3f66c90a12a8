//
//  QuickSearchCell.m
//  PPBrowser
//
//  Created by qingbin on 2022/10/16.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "QuickSearchCell.h"

#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "UIView+FrameHelper.h"
#import "NSObject+Helper.h"

#import "ThemeProtocol.h"
#import "PreferenceManager.h"
#import "BrowserUtils.h"

@interface QuickSearchCell ()<ThemeProtocol>

@property (nonatomic, strong) SearchModel* model;

@property (nonatomic, strong) UIView* backView;

@property (nonatomic, strong) UIImageView* logo;

@property (nonatomic, strong) UILabel* titleLabel;

@property (nonatomic, strong) UISwitch *switchView;

@property(nonatomic,strong) UIView* line;

@end

@implementation QuickSearchCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        //多选
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        self.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        [self addSubviews];
        [self defineLayout];
        
        [self applyTheme];
    }
    
    return self;
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.backView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        self.line.backgroundColor = [UIColor colorWithHexString:kDarkThemeColorLine];
        self.titleLabel.textColor = UIColor.whiteColor;
    } else {
        self.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.backView.backgroundColor = UIColor.whiteColor;
        self.line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.titleLabel.textColor = [UIColor colorWithHexString:@"#333333"];
    }
}

- (void)updateWithModel:(SearchModel *)model
{
    self.model = model;
    
    self.titleLabel.text = model.shortName;
    self.logo.image = model.image;
//    self.switchView.on = model.isOn;
    
    [self updateCornerRadius];
    [self applyTheme];
}

- (void)updateCornerRadius
{
    // 圆角
    self.line.hidden = NO;
    self.backView.layer.cornerRadius = iPadValue(20, 10);
    self.backView.layer.masksToBounds = YES;
    
    if(self.model.isFirstInSection && self.model.isLastInSection) {
        //只有一个
        self.backView.layer.masksToBounds = YES;
        self.backView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner | kCALayerMinXMaxYCorner | kCALayerMaxXMaxYCorner;
        self.line.hidden = YES;
    } else {
        if(self.model.isFirstInSection) {
            //添加上圆角
            self.backView.layer.masksToBounds = YES;
            self.backView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
        } else if(self.model.isLastInSection) {
            //添加下圆角
            self.backView.layer.masksToBounds = YES;
            self.backView.layer.maskedCorners = kCALayerMinXMaxYCorner | kCALayerMaxXMaxYCorner;
            self.line.hidden = YES;
        } else {
            self.backView.layer.masksToBounds = NO;
            self.backView.layer.cornerRadius = 0;
        }
    }
}

- (void)addSubviews
{
    [self.contentView addSubview:self.backView];
    [self.backView addSubview:self.logo];
    [self.backView addSubview:self.titleLabel];
    [self.backView addSubview:self.switchView];
}

- (void)defineLayout
{
    float offset = iPadValue(30, 15);
    [self.backView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(offset);
        make.right.mas_offset(-offset);
        make.top.bottom.equalTo(self.contentView);
    }];
    
    float height = iPadValue(40,30);
    offset = iPadValue(30, 15);
    [self.logo mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.backView);
        make.left.mas_offset(offset);
        make.size.mas_equalTo(height);
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.backView);
        make.left.equalTo(self.logo.mas_right).offset(10);
        make.right.equalTo(self.switchView.mas_left).offset(-5);
    }];
    
    [self.switchView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_offset(-offset);
        make.centerY.equalTo(self);
    }];
    
    UIView* line = [UIView new];
    line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    [self.backView addSubview:line];
    [line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.backView);
        make.left.mas_offset(0);
        make.right.mas_offset(0);
        make.height.mas_equalTo(0.5);
    }];
    self.line = line;
}

- (UISwitch *)switchView
{
    if(!_switchView) {
        _switchView = [UISwitch new];
        _switchView.on = YES;
    }
    
    return _switchView;
}

- (UIImageView *)logo
{
    if(!_logo) {
        _logo = [UIImageView new];
    }
    
    return _logo;
}

- (UILabel *)titleLabel
{
    if(!_titleLabel) {
        float font = iPadValue(20, 15);
        _titleLabel = [UIView createLabelWithTitle:@""
                                         textColor:[UIColor colorWithHexString:@"#333333"]
                                           bgColor:UIColor.clearColor
                                          fontSize:font
                                     textAlignment:NSTextAlignmentLeft
                                             bBold:NO];
    }
    
    return _titleLabel;
}

- (UIView *)backView
{
    if(!_backView) {
        _backView = [UIView new];
        _backView.backgroundColor = UIColor.whiteColor;
    }
    
    return _backView;
}

@end
