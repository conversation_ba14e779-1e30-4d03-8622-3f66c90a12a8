//
//  QuickSearchController.m
//  PPBrowser
//
//  Created by qingbin on 2022/10/15.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "QuickSearchController.h"

#import "MaizyHeader.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "NSObject+Helper.h"

#import "ThemeProtocol.h"
#import "PreferenceManager.h"
#import "PPNotifications.h"

#import "QuickSearchCell.h"
#import "BrowserUtils.h"
#import "SearchManager.h"

@interface QuickSearchController ()<UITableViewDelegate,UITableViewDataSource,UITableViewDragDelegate,UITableViewDropDelegate,ThemeProtocol>

@property (nonatomic, strong) NSMutableArray* model;

@property (nonatomic, strong) UITableView* tableView;

@end

@implementation QuickSearchController

- (void)viewDidLoad
{
    [super viewDidLoad];

    self.title = NSLocalizedString(@"search.quicksearch", nil);
    self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    
    [self createCustomLeftBarButtonItem];
    
    [self addSubviews];
    [self defineLayout];
    [self setupObservers];
    [self updateWithModel];
    
    //莫名其妙的bug,需要在这里调用一次,暗黑模式下标题才会变成白色
    [self configNavigationBarStype];
}

- (void)dealloc
{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)updateWithModel
{
    [self.model removeAllObjects];
    
    NSArray* allEngines = [[SearchManager shareInstance] getAllSearchEngines];
    SearchModel* item = allEngines.firstObject;
    item.isFirstInSection = YES;
    item = allEngines.lastObject;
    item.isLastInSection = YES;
    
    [self.model addObjectsFromArray:allEngines];
    
    [self applyTheme];
    
    [self.tableView reloadData];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        self.tableView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.tableView.tableHeaderView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.tableView.tableFooterView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
    } else {
        self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.tableView.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.tableView.tableHeaderView.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.tableView.tableFooterView.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
}


// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    //只有当前的controller会触发一次, 其它已存在的controller走通知
    if(needReload) {
        //修改暗黑模式
        [self applyTheme];
        [self.tableView reloadData];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
    [self.tableView reloadData];
}

#pragma mark -- 屏幕旋转
- (void)viewWillTransitionToSize:(CGSize)size withTransitionCoordinator:(id<UIViewControllerTransitionCoordinator>)coordinator
{
    [self.tableView reloadData];
}

#pragma mark -- layout
- (void)addSubviews
{
    [self.view addSubview:self.tableView];
}

- (void)defineLayout
{
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(0);
        make.right.mas_offset(-0);
        make.top.equalTo(self.view).offset(0);
        make.bottom.equalTo(self.view);
    }];
}

- (void)setupObservers
{

}

#pragma mark -- UITableViewDragDelegate
- (NSArray<UIDragItem *> *)tableView:(UITableView *)tableView itemsForBeginningDragSession:(id<UIDragSession>)session atIndexPath:(NSIndexPath *)indexPath
{
    // 小震动
    UIImpactFeedbackGenerator* feedback = [[UIImpactFeedbackGenerator alloc]initWithStyle:UIImpactFeedbackStyleMedium];
    [feedback prepare];
    [feedback impactOccurred];

    SearchModel* item = self.model[indexPath.row];

    UIDragItem* dragItem = [[UIDragItem alloc] initWithItemProvider:[NSItemProvider new]];
    dragItem.localObject = item;

    return @[dragItem];
}

#pragma mark -- UITableViewDropDelegate
- (UITableViewDropProposal *)tableView:(UITableView *)tableView dropSessionDidUpdate:(id<UIDropSession>)session withDestinationIndexPath:(nullable NSIndexPath *)destinationIndexPath
{
    return [[UITableViewDropProposal alloc]initWithDropOperation:UIDropOperationMove intent:UITableViewDropIntentInsertAtDestinationIndexPath];
}

- (void)tableView:(UITableView *)tableView performDropWithCoordinator:(id<UITableViewDropCoordinator>)coordinator
{
    UIDragItem* dragItem = coordinator.items.firstObject.dragItem;
    SearchModel* item = dragItem.localObject;

    NSIndexPath* targetIndexPath = coordinator.destinationIndexPath;

    //最后一个item判断
    if(targetIndexPath.row >= self.model.count) {
        //get the last IndexPath
        //跨区间 section 拖放操作, 拖出结尾的时候，coordinator.destinationIndexPath是没有的。
        //如果没有coordinator.destinationIndexPath那么强行指定最后一格
        targetIndexPath = [NSIndexPath indexPathForRow:self.model.count-1 inSection:0];
    }

    @weakify(self)
    [tableView performBatchUpdates:^{
        @strongify(self)
        id<UITableViewDropItem> dropItem = coordinator.items.firstObject;
        NSIndexPath* sourceIndexPath = dropItem.sourceIndexPath;

        if(sourceIndexPath.row < targetIndexPath.row) {
            [self.model insertObject:item atIndex:targetIndexPath.row+1];
            [self.model removeObjectAtIndex:sourceIndexPath.row];
        } else {
            [self.model removeObject:item];
            NSInteger index = targetIndexPath.row;
            [self.model insertObject:item atIndex:index];
        }

        [self updateCellStyle];
        
        [tableView reloadData];
    } completion:^(BOOL finished) {
        //更新order
        
    }];
}

#pragma mark -- 更新圆角信息
- (void)updateCellStyle
{
    for(SearchModel* item in self.model) {
        item.isFirstInSection = NO;
        item.isLastInSection = NO;
    }
    SearchModel* item = self.model.firstObject;
    item.isFirstInSection = YES;
    
    item = self.model.lastObject;
    item.isLastInSection = YES;
}

#pragma mark - UITableViewDataSource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return self.model.count;
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 1;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section
{
    return iPadValue(30, 20);
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section
{
    UIView* header = [UIView new];
    header.backgroundColor = UIColor.clearColor;
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        header.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
    } else {
        header.backgroundColor = UIColor.clearColor;
    }
    
    return header;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    SearchModel* model = self.model[indexPath.row];
    
    QuickSearchCell *cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass(QuickSearchCell.class)];
    [cell updateWithModel:model];
    
    return cell;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{

}

#pragma mark -- lazy init
- (UITableView *)tableView
{
    if(!_tableView)
    {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];

        _tableView.separatorStyle  = UITableViewCellSeparatorStyleNone;
        _tableView.showsHorizontalScrollIndicator = NO;
        _tableView.showsVerticalScrollIndicator   = NO;
        _tableView.delegate   = self;
        _tableView.dataSource = self;
        _tableView.rowHeight = iPadValue(88, 65);
        _tableView.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        
        [_tableView registerClass:[QuickSearchCell class] forCellReuseIdentifier:NSStringFromClass([QuickSearchCell class])];
        
        UIView* view = [[UIView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, CGFLOAT_MIN)];
        view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        _tableView.tableHeaderView = view;
        
        UIWindow* window = [NSObject normalWindow];
        view = [[UIView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, iPadValue(30, 10) + window.safeAreaInsets.bottom)];
        view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        _tableView.tableFooterView = view;
        
        _tableView.sectionFooterHeight = 0.0;
        _tableView.sectionHeaderHeight = 0.0f;
        
        _tableView.dragInteractionEnabled = self;
        _tableView.dragDelegate = self;
        _tableView.dropDelegate = self;
    }
    
    return _tableView;
}

- (NSMutableArray *)model
{
    if(!_model) {
        _model = [NSMutableArray array];
    }
    
    return _model;
}

@end
