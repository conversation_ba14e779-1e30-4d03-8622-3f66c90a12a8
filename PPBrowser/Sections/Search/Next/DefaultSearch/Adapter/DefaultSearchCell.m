//
//  DefaultSearchCell.m
//  PPBrowser
//
//  Created by qingbin on 2022/10/21.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "DefaultSearchCell.h"

#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "UIView+FrameHelper.h"
#import "NSObject+Helper.h"

#import "SearchManager.h"

#import "ThemeProtocol.h"
#import "PreferenceManager.h"

#import "BrowserUtils.h"

@interface DefaultSearchCell ()<ThemeProtocol>

@property(nonatomic,strong) SearchModel* model;

@property(nonatomic, strong) UIView* backView;

@property(nonatomic,strong) UIImageView* logo;

@property(nonatomic,strong) UILabel* titleLabel;

@property(nonatomic,strong) UIImageView* selectedLogo;

@property(nonatomic,strong) UIView* line;

@end

@implementation DefaultSearchCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        self.backgroundColor = UIColor.clearColor;
        [self addSubviews];
        [self defineLayout];
    }
    
    return self;
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.backView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        self.titleLabel.textColor = UIColor.whiteColor;
        self.line.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
    } else {
        self.titleLabel.textColor = [UIColor colorWithHexString:@"#434343"];
        
        if(self.model.isSelected) {
            self.backView.backgroundColor = [UIColor colorWithHexString:@"#EEF7FF"];
        } else {
            self.backView.backgroundColor = UIColor.whiteColor;
        }
        
        self.line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
}

- (void)updateWithModel:(SearchModel*)model
{
    self.model = model;
    self.titleLabel.text = model.shortName;
    self.logo.image = model.image;
    
    if(model.isSelected) {
        self.backView.backgroundColor = [UIColor colorWithHexString:@"#EEF7FF"];
        self.selectedLogo.hidden = NO;
    } else {
        self.backView.backgroundColor = UIColor.whiteColor;
        self.selectedLogo.hidden = YES;
    }
    
    [self updateCornerRadius];
    [self applyTheme];
}

- (void)updateCornerRadius
{
    // 圆角
    self.line.hidden = NO;
    self.backView.layer.cornerRadius = iPadValue(20, 10);
    self.backView.layer.masksToBounds = YES;
    
    if(self.model.isFirstInSection && self.model.isLastInSection) {
        //只有一个
        self.backView.layer.masksToBounds = YES;
        self.backView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner | kCALayerMinXMaxYCorner | kCALayerMaxXMaxYCorner;
        self.line.hidden = YES;
    } else {
        if(self.model.isFirstInSection) {
            //添加上圆角
            self.backView.layer.masksToBounds = YES;
            self.backView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
        } else if(self.model.isLastInSection) {
            //添加下圆角
            self.backView.layer.masksToBounds = YES;
            self.backView.layer.maskedCorners = kCALayerMinXMaxYCorner | kCALayerMaxXMaxYCorner;
            self.line.hidden = YES;
        } else {
            self.backView.layer.masksToBounds = NO;
            self.backView.layer.cornerRadius = 0;
        }
    }
}

- (void)addSubviews
{
    [self.contentView addSubview:self.backView];
    [self.backView addSubview:self.logo];
    [self.backView addSubview:self.titleLabel];
    [self.backView addSubview:self.selectedLogo];
}

- (void)defineLayout
{
    float offset = iPadValue(30, 15);
    [self.backView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(offset);
        make.right.mas_offset(-offset);
        make.top.bottom.equalTo(self.contentView);
    }];
    
    float height = iPadValue(40,30);
    offset = iPadValue(30, 15);
    [self.logo mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.left.mas_offset(offset);
        make.size.mas_equalTo(height);
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.left.equalTo(self.logo.mas_right).offset(offset);
        make.right.mas_offset(-offset);
    }];
    
    [self.selectedLogo mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.right.mas_offset(-offset);
        make.size.mas_equalTo(iPadValue(35, 25));
    }];
    
    UIView* line = [UIView new];
    line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    [self.backView addSubview:line];
    [line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.contentView);
        make.left.mas_offset(0);
        make.right.mas_offset(-0);
        make.height.mas_equalTo(0.5);
    }];
    self.line = line;
}

- (UIView *)backView
{
    if(!_backView) {
        _backView = [UIView new];
    }
    
    return _backView;
}

- (UILabel *)titleLabel
{
    if(!_titleLabel) {
        _titleLabel = [UIView createLabelWithTitle:@""
                                         textColor:[UIColor colorWithHexString:@"#333333"]
                                           bgColor:UIColor.clearColor
                                          fontSize:iPadValue(20, 15)
                                     textAlignment:NSTextAlignmentLeft
                                             bBold:NO];
    }
    
    return _titleLabel;
}

- (UIImageView *)logo
{
    if(!_logo) {
        _logo = [UIImageView new];
        _logo.contentMode = UIViewContentModeScaleAspectFit;
//        _logo.layer.cornerRadius = 20;
        _logo.layer.masksToBounds = YES;
    }
    
    return _logo;
}

- (UIImageView *)selectedLogo
{
    if(!_selectedLogo) {
        _selectedLogo = [UIImageView new];
        _selectedLogo.image = [UIImage imageNamed:@"common_check_icon"];
    }
    
    return _selectedLogo;
}

@end
