//
//  DefaultSearchController.m
//  PPBrowser
//
//  Created by qingbin on 2022/10/21.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "DefaultSearchController.h"

#import "DefaultSearchCell.h"

#import "MaizyHeader.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "NSObject+Helper.h"

#import "SearchManager.h"

#import "ThemeProtocol.h"
#import "PreferenceManager.h"
#import "PPNotifications.h"
#import "BrowserUtils.h"

@interface DefaultSearchController ()<UITableViewDelegate, UITableViewDataSource, ThemeProtocol>

@property (nonatomic, strong) NSMutableArray* model;

@property (nonatomic, strong) UITableView* tableView;

@end

@implementation DefaultSearchController

- (void)viewDidLoad
{
    [super viewDidLoad];

    self.navigationItem.title = NSLocalizedString(@"search.default", nil);
    self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    
    [self createCustomLeftBarButtonItem];
    
    [self addSubviews];
    [self defineLayout];
    [self updateWithModel];
}

- (void)updateWithModel
{
    [self.model removeAllObjects];
    
    NSArray* allEngines = [[SearchManager shareInstance] getAllSearchEngines];
    SearchModel* item = allEngines.firstObject;
    item.isFirstInSection = YES;
    item = allEngines.lastObject;
    item.isLastInSection = YES;
    
    [self.model addObjectsFromArray:allEngines];
    
    [self applyTheme];
    
    [self.tableView reloadData];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.tableView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.tableView.tableHeaderView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.tableView.tableFooterView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
    } else {
        self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.tableView.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.tableView.tableHeaderView.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.tableView.tableFooterView.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    //只有当前的controller会触发一次, 其它已存在的controller走通知
    if(needReload) {
        //修改暗黑模式
        [self applyTheme];
        [self.tableView reloadData];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
    [self.tableView reloadData];
}

#pragma mark -- 屏幕旋转
- (void)viewWillTransitionToSize:(CGSize)size withTransitionCoordinator:(id<UIViewControllerTransitionCoordinator>)coordinator
{
    [self.tableView reloadData];
}

#pragma mark -- layout
- (void)addSubviews
{
    [self.view addSubview:self.tableView];
}

- (void)defineLayout
{
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(0);
        make.right.mas_offset(-0);
        make.top.equalTo(self.view).offset(0);
        make.bottom.equalTo(self.view);
    }];
}

#pragma mark - UITableViewDataSource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return self.model.count;
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    SearchModel* model = self.model[indexPath.row];
    DefaultSearchCell *cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass(DefaultSearchCell.class)];
    [cell updateWithModel:model];
        
    return cell;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    SearchModel* model = self.model[indexPath.row];
    [[SearchManager shareInstance] updateCurrentSearchEngine:model];
    
    [self.tableView reloadData];
}

#pragma mark -- lazy init
- (UITableView *)tableView
{
    if(!_tableView)
    {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStyleGrouped];

        _tableView.separatorStyle  = UITableViewCellSeparatorStyleNone;
        _tableView.showsHorizontalScrollIndicator = NO;
        _tableView.showsVerticalScrollIndicator   = NO;
        _tableView.delegate   = self;
        _tableView.dataSource = self;
        _tableView.rowHeight = iPadValue(88, 60);
        _tableView.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        
        [_tableView registerClass:[DefaultSearchCell class] forCellReuseIdentifier:NSStringFromClass([DefaultSearchCell class])];
        
        UIView* view = [[UIView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, iPadValue(30, 10))];
        view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        _tableView.tableHeaderView = view;
        
        UIWindow* window = [NSObject normalWindow];
        view = [[UIView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, iPadValue(30, 10)+window.safeAreaInsets.bottom)];
        view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        _tableView.tableFooterView = view;
        
        _tableView.sectionFooterHeight = 0.0;
        _tableView.sectionHeaderHeight = 0.0f;
    }
    
    return _tableView;
}

- (NSMutableArray *)model
{
    if(!_model) {
        _model = [NSMutableArray array];
    }
    
    return _model;
}



@end
