//
//  SearchListView.m
//  PPBrowser
//
//  Created by qingbin on 2025/4/11.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "SearchListView.h"

#import "Masonry.h"
#import "MaizyHeader.h"
#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "ReactiveCocoa.h"
#import "UIView+FrameHelper.h"
#import "NSObject+Helper.h"
#import "BrowserUtils.h"

#import "PaddingNewLabel.h"
#import "CustomTitleAndImageView.h"
#import "ThemeProtocol.h"

@interface SearchListView()

@property (nonatomic, strong) UIScrollView *scrollView;
@property (nonatomic, strong) UIStackView *stackView;
@property (nonatomic, strong) NSArray<CustomTitleAndImageView *> *engineViews;
@property (nonatomic, strong) NSArray<SearchListModel *> *engineData;

@end

@implementation SearchListView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self setupData];
        [self addSubviews];
        [self defineLayout];
        [self setupObservers];
    }
    return self;
}

- (void)applyTheme {
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    
    if (isDarkTheme) {
        self.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
    } else {
        self.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
    
    for (int i=0;i<self.engineViews.count;i++) {
        SearchListModel *data = self.engineData[i];
        CustomTitleAndImageView *view = self.engineViews[i];
        view.backgroundColor = isDarkTheme ? [UIColor colorWithHexString:@"#1e1e1e"] : UIColor.whiteColor;
        
        //搜索引擎
        [view updateWith:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            // 更新标题颜色
            titleLabel.textColor = isDarkTheme ? UIColor.whiteColor : [UIColor colorWithHexString:@"#1A1A1A"];
            // 图标
            if (data.needAdaptNightMode) {
                imageView.tintColor = [ThemeProtocol isDarkTheme] ? UIColor.whiteColor : [UIColor colorWithHexString:@"#1A1A1A"];
            }
        }];
        
        // 更新阴影效果
        view.layer.shadowColor = isDarkTheme ?
            [UIColor colorWithHexString:@"#000000"].CGColor :
            [UIColor colorWithHexString:@"#000000"].CGColor;
        view.layer.shadowOpacity = isDarkTheme ? 0.2 : 0.1;
        
        if (isDarkTheme) {
            view.layer.borderColor = [UIColor colorWithHexString:@"#4a4a4a"].CGColor;
            view.layer.borderWidth = 0.3;
        } else {
            view.layer.borderColor = UIColor.clearColor.CGColor;
            view.layer.borderWidth = 0;
        }
    }
}


- (void)setupData {
    SearchListModel* settingsModel = [SearchListModel new];
    settingsModel.listType = SearchSuggestListTypeSearchSettings;
    settingsModel.needAdaptNightMode = YES;
    
    LocalizableOption localOption = [BrowserUtils localizableOption];
    if (localOption == LocalizableOptionZh_Hans
        || localOption == LocalizableOptionZh_Hant) {
        //简体或者繁体
        self.engineData = @[
            [[SearchListModel alloc] initWithSearchText:@"https://"],
            [[SearchListModel alloc] initWithSearchText:@".com"],
            [[SearchListModel alloc] initWithSearchType:SearchEngineTypeBaidu],
            [[SearchListModel alloc] initWithSearchType:SearchEngineTypeMetaso],
            [[SearchListModel alloc] initWithSearchType:SearchEngineTypeGoogle],
            [[SearchListModel alloc] initWithSearchType:SearchEngineTypeBing],
            [[SearchListModel alloc] initWithSearchType:SearchEngineType360],
            [[SearchListModel alloc] initWithSearchType:SearchEngineTypeYandex],
            [[SearchListModel alloc] initWithSearchType:SearchEngineTypeDuckduckgo],
            [[SearchListModel alloc] initWithSearchType:SearchEngineTypeEcosia],
            [[SearchListModel alloc] initWithSearchType:SearchEngineTypeGoogleTranslate],
            [[SearchListModel alloc] initWithSearchType:SearchEngineTypeYoutube],
            [[SearchListModel alloc] initWithSearchType:SearchEngineTypeGreasyfork],
            settingsModel,
        ];
    } else {
        //其他
        self.engineData = @[
            [[SearchListModel alloc] initWithSearchText:@"https://"],
            [[SearchListModel alloc] initWithSearchText:@".com"],
            [[SearchListModel alloc] initWithSearchType:SearchEngineTypeGoogle],
            [[SearchListModel alloc] initWithSearchType:SearchEngineTypeBing],
            [[SearchListModel alloc] initWithSearchType:SearchEngineTypeBaidu],
            [[SearchListModel alloc] initWithSearchType:SearchEngineTypeMetaso],
            [[SearchListModel alloc] initWithSearchType:SearchEngineType360],
            [[SearchListModel alloc] initWithSearchType:SearchEngineTypeYandex],
            [[SearchListModel alloc] initWithSearchType:SearchEngineTypeDuckduckgo],
            [[SearchListModel alloc] initWithSearchType:SearchEngineTypeEcosia],
            [[SearchListModel alloc] initWithSearchType:SearchEngineTypeGoogleTranslate],
            [[SearchListModel alloc] initWithSearchType:SearchEngineTypeYoutube],
            [[SearchListModel alloc] initWithSearchType:SearchEngineTypeGreasyfork],
            settingsModel,
        ];
    }
}

- (void)addSubviews {
    [self addSubview:self.scrollView];
    [self.scrollView addSubview:self.stackView];
    
    NSMutableArray *views = [NSMutableArray array];
    
    //占位节点，用来调整间距
    UIView* header = [UIView new];
    [self.stackView addArrangedSubview:header];
    
    @weakify(self)
    for (SearchListModel *engine in self.engineData) {
        CustomTitleAndImageView *view = nil;
        if (engine.listType == SearchSuggestListTypeSearchEngine
            || engine.listType == SearchSuggestListTypeSearchSettings) {
            //搜索引擎
            view = [self createEngineViewWithData:engine];
        } else if (engine.listType == SearchSuggestListTypeCommonInputWords) {
            //输入词            
            view = [[CustomTitleAndImageView alloc] initWithLayout:^(CustomTitleAndImageView *view, UIImageView *imageView, UILabel *titleLabel) {
                // 设置标题
                titleLabel.text = [engine text];
                titleLabel.font = [UIFont systemFontOfSize:14];
                titleLabel.textColor = [ThemeProtocol isDarkTheme] ? UIColor.whiteColor : [UIColor colorWithHexString:@"#1A1A1A"];
                
                // 设置视图样式
                view.backgroundColor = [ThemeProtocol isDarkTheme] ?
                    [UIColor colorWithHexString:@"#1A1A1A"] : UIColor.whiteColor;
                view.layer.cornerRadius = 10;
                view.layer.masksToBounds = NO;
                
                // 添加阴影效果
                view.layer.shadowColor = [UIColor colorWithHexString:@"#000000"].CGColor;
                view.layer.shadowOffset = CGSizeMake(0, 2);
                view.layer.shadowOpacity = 0.1;
                view.layer.shadowRadius = 4;
                                
                [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.left.mas_offset(12);
                    make.centerY.equalTo(view);
                    make.right.equalTo(view).offset(-12);
                }];
            }];
        }
        
        if (view) {
            [view setTapAction:^{
                @strongify(self)
                if (self.tapBlock) {
                    self.tapBlock(engine);
                }
            }];
            [views addObject:view];
                    
            [self.stackView addArrangedSubview:view];
        }
    }
    
    //占位节点，用来调整间距
    UIView* footer = [UIView new];
    [self.stackView addArrangedSubview:footer];
    
    self.engineViews = [views copy];
}

- (void)defineLayout {
    [self.scrollView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
    
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.scrollView);
        make.height.equalTo(self.scrollView);
    }];
    
    // 设置每个搜索引擎视图的约束
    for (CustomTitleAndImageView *view in self.engineViews) {
        [view mas_makeConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(30);
        }];
    }
}

- (void)setupObservers {
    // 监听主题变化
    @weakify(self)
    [[RACObserve([PreferenceManager shareInstance].items, isDarkTheme) distinctUntilChanged]
     subscribeNext:^(id x) {
        @strongify(self)
        [self applyTheme];
    }];
}

- (CustomTitleAndImageView *)createEngineViewWithData:(SearchListModel *)data {
    return [[CustomTitleAndImageView alloc] initWithLayout:^(CustomTitleAndImageView *view, UIImageView *imageView, UILabel *titleLabel) {
        // 设置图标
        if (data.needAdaptNightMode) {
            imageView.image = [[data icon] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
            imageView.tintColor = [ThemeProtocol isDarkTheme] ? UIColor.whiteColor : [UIColor colorWithHexString:@"#1A1A1A"];
        } else {
            imageView.image = [data icon];
        }
        
        // 设置标题
        titleLabel.text = [data text];
        titleLabel.font = [UIFont systemFontOfSize:14];
        titleLabel.textColor = [ThemeProtocol isDarkTheme] ? UIColor.whiteColor : [UIColor colorWithHexString:@"#1A1A1A"];
        
        // 设置视图样式
        view.backgroundColor = [ThemeProtocol isDarkTheme] ?
            [UIColor colorWithHexString:@"#1A1A1A"] : UIColor.whiteColor;
        view.layer.cornerRadius = 15;
        view.layer.masksToBounds = NO;
        
        // 添加阴影效果
        view.layer.shadowColor = [UIColor colorWithHexString:@"#000000"].CGColor;
        view.layer.shadowOffset = CGSizeMake(0, 2);
        view.layer.shadowOpacity = 0.1;
        view.layer.shadowRadius = 4;
        
        // 设置布局
        [imageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(view).offset(12);
            make.centerY.equalTo(view);
            make.width.height.mas_equalTo(16);
        }];
        
        [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(imageView.mas_right).offset(6);
            make.centerY.equalTo(view);
            make.right.equalTo(view).offset(-12);
        }];
    }];
}

#pragma mark - Lazy Load

- (UIScrollView *)scrollView {
    if (!_scrollView) {
        _scrollView = [[UIScrollView alloc] init];
        _scrollView.showsHorizontalScrollIndicator = NO;
        _scrollView.showsVerticalScrollIndicator = NO;
        _scrollView.bounces = YES;
        _scrollView.alwaysBounceHorizontal = YES;
    }
    return _scrollView;
}

- (UIStackView *)stackView {
    if (!_stackView) {
        _stackView = [[UIStackView alloc] init];
        _stackView.axis = UILayoutConstraintAxisHorizontal;
        _stackView.alignment = UIStackViewAlignmentCenter;
        _stackView.distribution = UIStackViewDistributionEqualSpacing;
        _stackView.spacing = iPadValue(15, 10);
    }
    return _stackView;
}

@end
