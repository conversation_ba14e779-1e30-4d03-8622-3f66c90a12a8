//
//  SearchListModel.m
//  PPBrowser
//
//  Created by qingbin on 2025/4/11.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "SearchListModel.h"

@implementation SearchListModel

//根据搜索引擎初始化
- (instancetype)initWithSearchType:(SearchEngineType)searchType
{
    self = [super init];
    if (self) {
        self.searchType = searchType;
        self.listType = SearchSuggestListTypeSearchEngine;
    }
    
    return self;
}

//根据常用搜索词
- (instancetype)initWithSearchText:(NSString *)searchText
{
    self = [super init];
    if (self) {
        self.searchText = searchText;
        self.listType = SearchSuggestListTypeCommonInputWords;
    }
    
    return self;
}

- (UIImage *)icon
{
    if (self.listType == SearchSuggestListTypeSearchEngine) {
        SearchModel* searchModel = [SearchModel searchModelWithSearchType:self.searchType];
        return searchModel.image;
    } else if (self.listType == SearchSuggestListTypeSearchSettings) {
        //设置
        return [UIImage imageNamed:@"searchlist_setting"];
    }
    
    return nil;
}

- (NSString *)text
{
    if (self.listType == SearchSuggestListTypeCommonInputWords) {
        return self.searchText;
    } else if (self.listType == SearchSuggestListTypeSearchEngine) {
        SearchModel* searchModel = [SearchModel searchModelWithSearchType:self.searchType];
        return searchModel.shortName;
    } else if (self.listType == SearchSuggestListTypeSearchSettings) {
        //设置
        return NSLocalizedString(@"preference.setting", nil);
    }
    
    return nil;
}

@end
