//
//  SearchListModel.h
//  PPBrowser
//
//  Created by qingbin on 2025/4/11.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "PPEnums.h"

#import "SearchModel.h"
#import "SearchManager.h"

@interface SearchListModel : NSObject
// 列表类型
@property (nonatomic, assign) SearchSuggestListType listType;
// 搜索引擎类型
@property (nonatomic, assign) SearchEngineType searchType;
// 如果是常用搜索词，那么其文案
@property (nonatomic, strong) NSString *searchText;
// 图标是否需要适配夜间模式
@property (nonatomic, assign) BOOL needAdaptNightMode;

//根据搜索引擎初始化
- (instancetype)initWithSearchType:(SearchEngineType)searchType;
//根据常用搜索词
- (instancetype)initWithSearchText:(NSString *)searchText;

- (UIImage *)icon;
- (NSString *)text;

@end
