//
//  SearchSuggestionController.m
//  PPBrowser
//
//  Created by qingbin on 2022/4/21.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "SearchSuggestionController.h"

#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "NSObject+Helper.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"

#import "MaizyHeader.h"
#import "CustomTextField.h"
#import "UIView+FrameHelper.h"

#import "DatabaseUnit+History.h"
#import "HistoryModel.h"

#import "SearchSuggestManager.h"
#import "SearchSuggestionCell.h"

#import "Tab.h"

#import "PPEnums.h"
#import "URIFixup.h"
#import "SearchManager.h"

#import "SearchSlider.h"
#import "UITextField+Helper.h"

#import "InternalURL.h"
#import "PPNotifications.h"

#import "ThemeProtocol.h"
#import "PreferenceManager.h"
#import "SearchViewController.h"
#import "BaseNavigationController.h"
#import "BrowserUtils.h"
#import "UIAlertController+SafePresentation.h"
#import "SearchListView.h"
#import "SearchHelper.h"
#import "SearchSettingViewController.h"

@interface SearchSuggestionController ()<UITextFieldDelegate,UITableViewDelegate,UITableViewDataSource,ThemeProtocol>

@property (nonatomic, strong) CustomTextField *textField;

@property(nonatomic,strong) UITableView* tableView;
@property(nonatomic,strong) NSMutableArray* model;

@property(nonatomic,strong) NSArray* historyModel;
// 标识是否是历史记录,只有历史记录才有删除功能
@property(nonatomic,assign) BOOL isHistoryList;

@property(nonatomic,assign) int currentOffset;

@property(nonatomic,strong) UIImageView* leftImageView;

@property (nonatomic, strong) UIButton* rightButton;

@property (nonatomic, strong) UIView* tableViewFooter;

@end

@implementation SearchSuggestionController

- (instancetype)initWithTab:(Tab*)tab
{
    self = [super init];
    if(self) {
        self.tab = tab;
    }

    return self;
}

- (void)viewDidLoad
{
    [super viewDidLoad];
    
    self.view.backgroundColor = UIColor.whiteColor;
    
    [self _createSearchTitleView];
    [self createCustomLeftBarButtonItem];
    [self _createCustomRightBarButtonitem];
    
    [self addSubviews];
    [self defineLayout];
    [self setupObservers];
    
    //v2.2.1,隐私模式不显示历史记录
    [self requestHistory];
    
    [self applyTheme];
}

- (void)dealloc
{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:animated];
    
    dispatch_async(dispatch_get_main_queue(), ^{
        
        [self.textField becomeFirstResponder];
        
        NSURL* url = self.tab.webView.URL;
        //首页点击进来没有url
        if(!url) return;
        if(![InternalURL isValid:url]) {
            [self updateSearchBarWithURL:url.absoluteString];
        } else if([InternalURL isErrorPage:url.absoluteString]) {
            NSString* urlString = [url absoluteString];
            //如果是错误页, 那么需要从中提取真正的url
            NSRange range = [urlString rangeOfString:@"errorUrl="];
            NSInteger location = range.location+range.length;
            NSInteger length = urlString.length - (range.location+range.length);
            NSString* errorUrl = [urlString substringWithRange:NSMakeRange(location, length)];
            [self updateSearchBarWithURL:errorUrl];
        }
        
        if(self.textField.text.length > 0) {
            self.currentOffset = (int)self.textField.text.length;
            [self.textField selectAll:nil];
        }
    });
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.tableView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        
        [self.rightButton setTitleColor:[UIColor colorWithHexString:@"#ffffff"] forState:UIControlStateNormal];
        
        self.textField.backgroundColor = [UIColor colorWithHexString:kDarkThemeColorTextFiledBackgroundColor];
        self.textField.textColor = UIColor.whiteColor;
        [self.textField updatePlaceHolder:NSLocalizedString(@"textfiled.placeholder", nil) color:[UIColor colorWithHexString:kDarkThemeColorTextFiledContent]];
    } else {
        self.view.backgroundColor = UIColor.whiteColor;
        self.tableView.backgroundColor = UIColor.whiteColor;
        
        [self.rightButton setTitleColor:[UIColor colorWithHexString:@"#2D7AFE"] forState:UIControlStateNormal];
        
        self.textField.textColor = [UIColor colorWithHexString:@"#333333"];
        
        [self.textField setBackgroundColor:[UIColor colorWithHexString:@"#f5f5f5"]];
        [self.textField updatePlaceHolder:NSLocalizedString(@"textfiled.placeholder", nil) color:[UIColor colorWithHexString:@"#999999"]];
    }
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    //只有当前的controller会触发一次, 其它已存在的controller走通知
    if(needReload) {
        //修改暗黑模式
        [self applyTheme];
        [self.tableView reloadData];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
    [self.tableView reloadData];
}

//更新搜索框的url
- (void)updateSearchBarWithURL:(NSString *)url
{
    NSString* searchKeyword = [SearchHelper searchKeywordFromUrl:url];
    if (searchKeyword.length > 0) {
        self.textField.text = searchKeyword;
    } else {
        // 如果不匹配搜索引擎URL或未找到搜索词，则显示完整URL
        self.textField.text = url;
    }
}

#pragma mark -- layout
- (void)addSubviews
{
    [self.view addSubview:self.tableView];
    
    //2.6.8 阿拉伯语布局适配
    [NSObject rtlLayoutSupportWithViews:@[self.textField, self.textField.leftView]];
}

- (void)defineLayout
{
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_offset(0);
        make.left.right.equalTo(self.view);
        make.bottom.equalTo(self.view);
    }];
}

- (void)setupObservers
{
    @weakify(self)
    [[[[self.textField rac_textSignal] distinctUntilChanged] throttle:0.2]
    subscribeNext:^(id x) {
        @strongify(self)
        //是否显示搜索建议
        BOOL showSearchSuggestion = [[PreferenceManager shareInstance].items.isShowSearchSuggestion boolValue];
        if (!showSearchSuggestion) return;
        
        [self getSuggestions];
    }];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(_updateSearchEngine)
                                                 name:kUpdateSearchEngineNotification
                                               object:nil];
    
    [RACObserve(self, isHistoryList) subscribeNext:^(id x) {
        @strongify(self)
        dispatch_async(dispatch_get_main_queue(), ^{
            if(self.isHistoryList && self.model.count > 0) {
                self.tableViewFooter.hidden = NO;
            } else {
                self.tableViewFooter.hidden = YES;
            }
        });
    }];
    
    UITapGestureRecognizer* tap = [UITapGestureRecognizer new];
    [self.tableViewFooter addGestureRecognizer:tap];
    [tap.rac_gestureSignal subscribeNext:^(id x) {
        @strongify(self)
        [self _alertClearAllHistory];
    }];
}

- (void)_alertClearAllHistory
{
    UIAlertController* alertController = [UIAlertController alertControllerWithTitle:NSLocalizedString(@"searchSuggestion.clear.history.alert", nil) message:nil preferredStyle:UIAlertControllerStyleAlert];
    UIAlertAction* action = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.cancel", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [alertController dismissViewControllerAnimated:YES completion:nil];
    }];
    [alertController addAction:action];

    @weakify(self)
    action = [UIAlertAction actionWithTitle:NSLocalizedString(@"tableview.delete", nil) style:UIAlertActionStyleDestructive handler:^(UIAlertAction * _Nonnull action) {
        @strongify(self)
        DatabaseUnit* unit = [DatabaseUnit removeAllHistory];
        @weakify(self)
        [unit setCompleteBlock:^(id result, BOOL success) {
            @strongify(self)
            [self requestHistory];
        }];
        DB_EXEC(unit);
    }];
    [alertController addAction:action];
    
//    [self presentViewController:alertController animated:YES completion:nil];
    //v2.6.8, 统一present方法，防止崩溃
    [alertController presentSafelyFromViewController:self];
}

#pragma mark -- 获取搜索引擎联想词
- (void)getSuggestions
{
    NSString* text = self.textField.text;
    if(text.length == 0) return;
    
    [[SearchSuggestManager shareInstance] querySuggest:text completedBlock:^(NSArray *result, BOOL succ) {
        if(succ) {
            self.isHistoryList = NO;
            [self.model removeAllObjects];
            for(NSString* str in result) {
                HistoryModel* item = [HistoryModel new];
                item.keyword = str;
                //联想词
                item.searchType =  HistorySearchTypeKeyword;
                
                [self.model addObject:item];
            }
            
            dispatch_async(dispatch_get_main_queue(), ^{
                [self.tableView reloadData];
            });
        }
    }];
}

#pragma mark -- 获取历史记录
- (void)requestHistory
{
    [self.model removeAllObjects];
    
    DatabaseUnit* unit = [DatabaseUnit queryAllHistoryWithMaxCount:20];
    @weakify(self)
    [unit setCompleteBlock:^(NSArray* result, BOOL success) {
        @strongify(self)
        if(!self) return;
        if(success) {
            self.isHistoryList = YES;
            
            NSArray* filterModel;
            //v2.2.1,隐私模式不显示历史记录
            BOOL isPrivate = [[PreferenceManager shareInstance].items.isPrivate boolValue];
            BOOL isShowSearchHistory = [[PreferenceManager shareInstance].items.isShowSearchHistory boolValue];
            if(!isPrivate && isShowSearchHistory) {
                filterModel = [self _filterHistoryModel:result];
            }
            
            BOOL isReadPasteboard = [[PreferenceManager shareInstance].items.isReadPasteboard boolValue];
            if(isReadPasteboard) {
                //创建系统剪切板
                UIPasteboard *systemBoard = [UIPasteboard generalPasteboard];
                //读取剪切板的内容
                NSString *content = systemBoard.string;
                if(content.length > 0) {
                    HistoryModel* item = [HistoryModel new];
                    item.title = NSLocalizedString(@"searchSuggestion.copy.text", nil);
                    item.keyword = content;
                    item.searchType = HistorySearchTypePasteboard;
                    [self.model addObject:item];
                }
            }
            
            if(filterModel.count > 0) {
                [self.model addObjectsFromArray:filterModel];
            }
            self.tableViewFooter.hidden = filterModel.count == 0;
            
            [self.tableView reloadData];
            
            self.historyModel = filterModel;
        }
    }];
    
    DB_EXEC(unit);
}

- (NSArray*)_filterHistoryModel:(NSArray*)items
{
    //历史提醒页不显示重复的搜索关键字/url
    NSMutableArray* model = [NSMutableArray array];
    NSMutableDictionary* dict = [NSMutableDictionary dictionary];
    for(HistoryModel* item in items) {
        if(item.title.length == 0) {
            //例如百度搜索过渡页,是没有标题的,那么直接过滤掉
            continue;
        }
        
        if(item.searchType == HistorySearchTypeKeyword) {
            NSString* key = [NSString stringWithFormat:@"%ld-%@",(long)item.searchType,item.keyword];
            if(!dict[key]) {
                [model addObject:item];
                dict[key] = item;
            }
        } else if(item.searchType == HistorySearchTypeUrl) {
            NSString* key = [NSString stringWithFormat:@"%ld-%@",(long)item.searchType,item.url];
            if(!dict[key]) {
                [model addObject:item];
                dict[key] = item;
            }
        }
    }
    
    return model;
}

#pragma mark -- UITextFieldDelegate
- (BOOL)textFieldShouldReturn:(UITextField *)textField
{
    if(textField.text.length == 0) {
        return NO;
    }
    
    //点击搜索
    HistoryModel* item = [HistoryModel new];
    item.keyword = textField.text;
    item.historyId = [NSUUID UUID].UUIDString;
    item.searchType = HistorySearchTypeUnknown;
    
    [self selectItem:item selectedSearchEngine:nil];
    
    return YES;
}

- (BOOL)textFieldShouldClear:(UITextField *)textField
{
    self.isHistoryList = YES;
    self.model = [self.historyModel mutableCopy];
    [self.tableView reloadData];
    
    return YES;
}

#pragma mark -- 更新搜索引擎
- (void)_updateSearchEngine
{
    SearchModel* engine = [[SearchManager shareInstance] getCurrentSearchEngine];
    self.leftImageView.image = engine.image;
    
    if (engine.type == SearchEngineTypeMetaso) {
        //秘塔AI
        self.leftImageView.layer.cornerRadius = iPadValue(3, 3);
        self.leftImageView.layer.masksToBounds = YES;
        
        [self.leftImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.center.mas_offset(0);
            make.size.mas_equalTo(20);
        }];
    } else {
        self.leftImageView.layer.cornerRadius = 0;
        
        [self.leftImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.center.mas_offset(0);
            make.height.mas_equalTo(20);
        }];
    }
}

#pragma mark -- 选择搜索引擎
- (void)selectSearchEngine
{
    [self.textField endEditing:YES];
    
    if([BrowserUtils isiPad]) {
        //iPad
        SearchViewController* vc = [SearchViewController new];
        CGSize size = [UIScreen mainScreen].bounds.size;
        float width = size.width * (1-0.12*2);
        float height = size.height * (1-0.12*2);
        vc.preferredContentSize = CGSizeMake(width, height);
        
        BaseNavigationController* navc = [[BaseNavigationController alloc]initWithRootViewController:vc];
        navc.view.backgroundColor = UIColor.whiteColor;
        navc.modalPresentationStyle = UIModalPresentationFormSheet;
        
        [self presentViewController:navc animated:YES completion:nil];
    } else {
        //iPhone
        SearchViewController* vc = [SearchViewController new];
        CGSize size = [UIScreen mainScreen].bounds.size;
        float width = kScreenWidth;
        float height = size.height * (1-0.12*2);
        vc.preferredContentSize = CGSizeMake(width, height);
        
        BaseNavigationController* navc = [[BaseNavigationController alloc]initWithRootViewController:vc];
        navc.view.backgroundColor = UIColor.whiteColor;
        navc.modalPresentationStyle = UIModalPresentationFormSheet;
        
        [self presentViewController:navc animated:YES completion:nil];
    }
}

#pragma mark -- 设置导航栏
// 创建顶部输入框
- (void)_createSearchTitleView
{
    float height = iPadValue(40, 36);
    CustomTextField* textField = [[CustomTextField alloc] initWithFrame:CGRectMake(0, 7.5f, self.navigationController.navigationBar.width, height)];
    //2.6.8 阿拉伯语布局适配
    textField.textAlignment = NSTextAlignmentLeft;
    
    float font = iPadValue(18, 14);
    textField.font = [UIFont systemFontOfSize:font];
    textField.leftViewRect = CGRectMake(5.0, 0, height, height);
    
//    textField.layer.cornerRadius = height/2.0;
    textField.layer.cornerRadius = iPadValue(8, 5);
    textField.layer.masksToBounds = YES;
    
    textField.leftViewMode = UITextFieldViewModeAlways;
    textField.textColor = [UIColor colorWithHexString:@"#333333"];
    textField.returnKeyType = UIReturnKeySearch;
    
//    textField.inputAccessoryView = inputAccessoryView;
    [textField setBackgroundColor:[UIColor colorWithHexString:@"#f5f5f5"]];
    [textField updatePlaceHolder:NSLocalizedString(@"textfiled.placeholder", nil) color:[UIColor colorWithHexString:@"#999999"]];
    [textField setClearButtonMode:UITextFieldViewModeAlways];

    [textField setDelegate:self];

    UIView* leftView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, height+15, height)];
    UIImageView* imageView = [UIImageView new];
    imageView.layer.masksToBounds = YES;
    self.leftImageView = imageView;
    [leftView addSubview:imageView];
    imageView.contentMode = UIViewContentModeScaleAspectFit;
    
    [self _updateSearchEngine];
    
    UITapGestureRecognizer* tap = [UITapGestureRecognizer new];
    @weakify(self)
    [tap.rac_gestureSignal subscribeNext:^(id x) {
        @strongify(self)
        [self selectSearchEngine];
    }];
    [leftView addGestureRecognizer:tap];
    
    textField.leftView = leftView;
    
//    SearchSlider* slider = [SearchSlider toolBar];
//    __block int firstValue = 0;
//    slider.didChangedValue = ^(float value) {
//        @strongify(self)
//        int count = (int)self.textField.text.length;
//        int midCount = 0.5 * count;
//        
//        int currentOffset = value * count - midCount;
//        currentOffset = firstValue + currentOffset;
//        
//        if(currentOffset <= 0) currentOffset = 0;
//        if(currentOffset >= count) currentOffset = count;
//        
//        self.currentOffset = currentOffset;
//        
//        [self.textField makeOffsetFromBeginning:self.currentOffset];
//    };
//    
//    slider.didSelectOption = ^(NSString * _Nonnull text) {
//        @strongify(self)
//        NSString* content = self.textField.text;
//        int location = (int)[self.textField currentOffset];
//
//        NSString* leftContent = [content substringToIndex:location];
//        if(leftContent.length == 0) leftContent = @"";
//        NSMutableString* mutableString = [[NSMutableString alloc]initWithString:leftContent];
//        
//        [mutableString appendString:text];
//        
//        NSString* rightContent = [content substringFromIndex:location];
//        if(rightContent.length == 0) rightContent = @"";
//        [mutableString appendString:rightContent];
//        
//        self.textField.text = [mutableString copy];
//        
//        int currentOffset = location+(int)text.length;
//        self.currentOffset = currentOffset;
//        [self.textField makeOffsetFromBeginning:currentOffset];
//    };
//    
//    slider.didTouchDown = ^{
//        firstValue = self.currentOffset;
//        [self.textField makeOffsetFromBeginning:self.currentOffset];
//    };
//    
//    slider.didTouchUp = ^{
//        //reset
//        firstValue = self.currentOffset;
//    };
    
//    textField.inputAccessoryView = slider;
    //v2.7.1
    SearchListView* listView = [[SearchListView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, 44)];
    [listView setTapBlock:^(SearchListModel * _Nonnull model) {
        @strongify(self)
        [self _handleTapSearchListItem:model];
    }];
    
    textField.inputAccessoryView = listView;
    
    self.navigationItem.titleView = textField;
    self.textField = textField;
}

#pragma mark - 点击搜索列表中的单项
- (void)_handleTapSearchListItem:(SearchListModel *)item
{
    //点击搜索列表中按钮
    if (item.listType == SearchSuggestListTypeSearchEngine) {
        //点击搜索引擎
        NSString* text = self.textField.text;
        if (text.length == 0) {
            return;
        }
        
        SearchEngineType searchType = item.searchType;
        
        if ([text.lowercaseString hasPrefix:@"http"]) {
            //判断是否能提取出搜索词
            NSString* keyword = [SearchHelper searchKeywordFromUrl:text];
            if (keyword.length == 0) {
                //网址
                HistoryModel* item = [HistoryModel new];
                item.keyword = text;
                item.historyId = [NSUUID UUID].UUIDString;
                item.searchType = HistorySearchTypeUnknown;
                
                [self selectItem:item selectedSearchEngine:@(searchType)];
            } else {
                //搜索词
                HistoryModel* item = [HistoryModel new];
                item.keyword = keyword;
                item.historyId = [NSUUID UUID].UUIDString;
                item.searchType = HistorySearchTypeUnknown;
                
                [self selectItem:item selectedSearchEngine:@(searchType)];
            }
        } else {
            //搜索词
            HistoryModel* item = [HistoryModel new];
            item.keyword = text;
            item.historyId = [NSUUID UUID].UUIDString;
            item.searchType = HistorySearchTypeUnknown;
            
            [self selectItem:item selectedSearchEngine:@(searchType)];
        }
    } else if (item.listType == SearchSuggestListTypeSearchSettings) {
        //设置页
        SearchSettingViewController* vc = [SearchSettingViewController new];
        [self.navigationController pushViewController:vc animated:YES];
    } else if (item.listType == SearchSuggestListTypeCommonInputWords) {
        //搜索词
        self.textField.text = [NSString stringWithFormat:@"%@%@", self.textField.text, item.searchText];
    }
}

- (void)_createCustomRightBarButtonitem
{
    UIButton* rightButton = [[UIButton alloc] initWithFrame:CGRectMake(0.0, 0.0f, 44.0f, 44.0)];
    [rightButton setBackgroundColor:[UIColor clearColor]];

    float font = iPadValue(20, 16);
    rightButton.titleLabel.font = [UIFont systemFontOfSize:font];
    [rightButton setTitle:NSLocalizedString(@"navc.cancel", nil) forState:UIControlStateNormal];
    [rightButton setTitleColor:[UIColor colorWithHexString:@"#2D7AFE"] forState:UIControlStateNormal];
    [rightButton addTarget:self action:@selector(rightBarbuttonClick) forControlEvents:UIControlEventTouchUpInside];
    UIBarButtonItem* barItem = [[UIBarButtonItem alloc] initWithCustomView:rightButton];
    UIBarButtonItem *spacer = [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemFixedSpace target:nil action:nil];
    
    float offset = iPadValue(20, 16);
    spacer.width = -offset; // for example shift right bar button to the right
    
    self.rightButton = rightButton;
    
    self.navigationItem.rightBarButtonItems = @[spacer, barItem];
}

- (void)rightBarbuttonClick
{
    [self dismissViewControllerAnimated:YES completion:nil];
}

#pragma mark - UIScrollViewDelegate
- (void)scrollViewWillBeginDragging:(UIScrollView *)scrollView
{
    [self.textField resignFirstResponder];
    [self.view endEditing:YES];
}

#pragma mark - UITableViewDataSource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return self.model.count;
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    SearchSuggestionCell *cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass(SearchSuggestionMutiCell.class)];
    
    if(indexPath.row < self.model.count) {
        HistoryModel* item = self.model[indexPath.row];
        if(item.searchType == HistorySearchTypeUrl) {
            cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass(SearchSuggestionMutiCell.class)];
        } else {
            //联想词不显示url
            cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass(SearchSuggestionCell.class)];
        }
        
        [cell updateWithModel:item];
        
        @weakify(self)
        cell.didCopyToTextBlock = ^(HistoryModel *item) {
            @strongify(self)
            if(item.searchType == HistorySearchTypeKeyword) {
                self.textField.text = item.keyword;
            } else if(item.searchType == HistorySearchTypeUrl) {
                self.textField.text = item.url;
            }
        };
    }
    
    return cell;
}

- (BOOL)tableView:(UITableView *)tableView canEditRowAtIndexPath:(NSIndexPath *)indexPath
{
    return self.isHistoryList;
}

// 只适用于ios11 以及以后版本
- (UISwipeActionsConfiguration *)tableView:(UITableView *)tableView trailingSwipeActionsConfigurationForRowAtIndexPath:(NSIndexPath *)indexPath  API_AVAILABLE(ios(11.0))
{
    // 删除数据
    @weakify(self)
    UIContextualAction *deleteAction = [UIContextualAction contextualActionWithStyle:UIContextualActionStyleDestructive title:NSLocalizedString(@"tableview.delete", nil) handler:^(UIContextualAction * _Nonnull action, __kindof UIView * _Nonnull sourceView, void (^ _Nonnull completionHandler)(BOOL)) {
        @strongify(self)
        [tableView setEditing:NO animated:YES];//退出编辑模式，隐藏左滑菜单
    
        HistoryModel* item = self.model[indexPath.row];

        DatabaseUnit* unit = [DatabaseUnit removeHistoryInSearchSuggestionWithItem:item];
        unit.completeBlock = ^(id result, BOOL success) {
            dispatch_async(dispatch_get_main_queue(), ^{
                if(success) {
                    [tableView beginUpdates];
                    [self.model removeObject:item];
                    [tableView deleteRowsAtIndexPaths:@[indexPath] withRowAnimation:UITableViewRowAnimationFade];
                    [tableView endUpdates];
                }
                
                completionHandler(YES);
            });
        };
        
        DB_EXEC(unit);
    }];
    [deleteAction setBackgroundColor:UIColor.redColor];
    
    // 添加
    UISwipeActionsConfiguration *actions = [UISwipeActionsConfiguration configurationWithActions:@[deleteAction]];
    actions.performsFirstActionWithFullSwipe = NO;
    return actions;
}

#pragma mark - UITableViewDelegate
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    [tableView deselectRowAtIndexPath:indexPath animated:NO];
    
    if(indexPath.row >= self.model.count) return;
    
    HistoryModel* item = self.model[indexPath.row];
    //新增一条历史记录
    item.historyId = [[NSUUID UUID] UUIDString];
    [self selectItem:item selectedSearchEngine:nil];
}

- (void)selectItem:(HistoryModel *)item selectedSearchEngine:(NSNumber *)selectedSearchEngine
{
    //通过搜索引擎生成的url不记录在表中,
    //因为有可能会切换搜索引擎,那么会引起搜索错误
    //不在这里保存到历史表了, 当触发url/title/didFinish时再保存
    
    NSString* content;
    if(item.searchType == HistorySearchTypePasteboard) {
        content = item.keyword;
    } else if(item.searchType == HistorySearchTypeUnknown) {
        content = item.keyword;
    } else if(item.searchType == HistorySearchTypeKeyword) {
        content = item.keyword;
    } else {
        content = item.url;
    }
    
    self.tab.currentHistoryModel = item;
    
    //不管是否是url,都要判断一下
    NSURL* fixupURL = [URIFixup getURL:content];
    if(fixupURL) {
        item.searchType = HistorySearchTypeUrl;
        
        // The user entered a URL, so use it.
        [self.tab loadRequest:[NSURLRequest requestWithURL:fixupURL]];
    } else {
        item.searchType = HistorySearchTypeKeyword;
        
        // We couldn't build a URL, so pass it on to the search engine.
        NSURL* url = nil;
        if (!selectedSearchEngine) {
            //默认搜索引擎
            url = [[SearchManager shareInstance] searchURLForQuery:content];
        } else {
            //指定搜索引擎
            SearchEngineType searchType = [selectedSearchEngine intValue];
            url = [[SearchManager shareInstance] searchURLForQuery:content searchType:searchType];
        }
        
        [self.tab loadRequest:[NSURLRequest requestWithURL:url]];
    }
    
    //保存到历史表中
    DatabaseUnit* unit = [DatabaseUnit addHistoryWithItem:item];
    DB_EXEC(unit);
    
    //移除首页,进入webview加载页
    if(self.delegate && [self.delegate respondsToSelector:@selector(searchSuggestionControllerDidStartLoadRequest)]) {
        [self.delegate searchSuggestionControllerDidStartLoadRequest];
    }
    
    [self dismissViewControllerAnimated:YES completion:nil];
}

#pragma mark -- lazy init
- (UITableView *)tableView
{
    if(!_tableView)
    {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];

        _tableView.separatorStyle  = UITableViewCellSeparatorStyleNone;
        _tableView.showsHorizontalScrollIndicator = NO;
        _tableView.showsVerticalScrollIndicator   = NO;
        _tableView.delegate   = self;
        _tableView.dataSource = self;
        _tableView.rowHeight = iPadValue(80, 50);
        _tableView.backgroundColor = UIColor.whiteColor;
        
        [_tableView registerClass:[SearchSuggestionCell class] forCellReuseIdentifier:NSStringFromClass([SearchSuggestionCell class])];
        [_tableView registerClass:[SearchSuggestionMutiCell class] forCellReuseIdentifier:NSStringFromClass([SearchSuggestionMutiCell class])];
        
        _tableView.tableFooterView = self.tableViewFooter;
        
        float height = iPadValue(20, 10);
        UIView* header = [[UIView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, height)];
        header.backgroundColor = UIColor.clearColor;
        _tableView.tableHeaderView = header;
        
        _tableView.sectionFooterHeight = 0.0;
        _tableView.sectionHeaderHeight = 0.0f;
    }
    
    return _tableView;
}

- (NSMutableArray *)model
{
    if(!_model) {
        _model = [NSMutableArray array];
    }
    
    return _model;
}

- (UIView *)tableViewFooter
{
    if(!_tableViewFooter) {
        UIWindow* window = [NSObject normalWindow];
        _tableViewFooter = [[UIView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, iPadValue(80, 60)+window.safeAreaInsets.bottom)];
        _tableViewFooter.backgroundColor = UIColor.clearColor;
        _tableViewFooter.hidden = YES;
        
        float font = iPadValue(20, 15);
        UILabel* titleLabel = [UIView createLabelWithTitle:NSLocalizedString(@"searchSuggestion.clear.history.title", nil)
                                         textColor:[UIColor colorWithHexString:@"#666666"]
                                           bgColor:UIColor.clearColor
                                          fontSize:font
                                     textAlignment:NSTextAlignmentLeft
                                             bBold:NO];
        [_tableViewFooter addSubview:titleLabel];
        [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.mas_offset(0);
            make.top.mas_offset(iPadValue(30, 15));
        }];
    }
    
    return _tableViewFooter;
}

@end
