//
//  SearchSuggestionController.h
//  PPBrowser
//
//  Created by qingbin on 2022/4/21.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "BaseViewController.h"
@class Tab;

@protocol SearchSuggestionControllerDelegate <NSObject>

- (void)searchSuggestionControllerDidStartLoadRequest;

@end

//待优化 -- 历史搜索词建议显示用户输入的关键字
@interface SearchSuggestionController : BaseViewController

- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)new NS_UNAVAILABLE;

- (instancetype)initWithTab:(Tab*)tab;

//更新搜索框的url
//- (void)updateSearchBarWithURL:(NSString*)url;

@property(nonatomic,weak) Tab *tab;
@property(nonatomic,weak) id<SearchSuggestionControllerDelegate> delegate;

@end

