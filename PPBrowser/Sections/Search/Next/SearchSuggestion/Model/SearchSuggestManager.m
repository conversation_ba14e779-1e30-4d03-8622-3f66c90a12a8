//
//  SearchSuggestManager.m
//  PPBrowser
//
//  Created by qingbin on 2022/4/22.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "SearchSuggestManager.h"
#import "UserAgentManager.h"
#import "NSString+Helper.h"

@interface SearchSuggestManager ()

@property (nonatomic, strong) NSURLSessionTask *request;

@end

@implementation SearchSuggestManager

+ (instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    static SearchSuggestManager* obj;
    dispatch_once(&onceToken, ^{
        obj = [SearchSuggestManager new];
    });
    
    return obj;
}

- (void)querySuggest:(NSString*)content completedBlock:(void(^)(NSArray* result, BOOL succ))completedBlock
{
    [self cancelPendingRequest];
    
    NSURL* url = [[SearchManager shareInstance] suggestURLForQuery:content];
    if(!url) {
        if(completedBlock) {
            completedBlock(nil, NO);
        }
        
        return;
    }
    
    //设置userAgent
    NSURLSessionConfiguration* configuration = [NSURLSessionConfiguration ephemeralSessionConfiguration];
    configuration.HTTPAdditionalHeaders = @{@"User-Agent": [[UserAgentManager shareInstance] getCurrentUserAgent]};
    NSURLSession* session = [NSURLSession sessionWithConfiguration:configuration];
    
    self.request = [session dataTaskWithURL:url completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
        if(error) {
            if(completedBlock) {
                completedBlock(nil, NO);
            }
            
            return;
        }
        
//        NSHTTPURLResponse* httpResponse = (NSHTTPURLResponse*)response;
        NSHTTPURLResponse* httpResponse = [response isKindOfClass:[NSHTTPURLResponse class]] ? (NSHTTPURLResponse*)response : nil;
        if(!httpResponse || httpResponse.statusCode < 200 || httpResponse.statusCode >= 300) {
            if(completedBlock) {
                completedBlock(nil, NO);
            }
            
            return;
        }
        
        // The response will be of the following format:
        //    ["foobar",["foobar","foobar2000 mac","foobar skins",...]]
        // That is, an array of at least two elements: the search term and an array of suggestions.
        NSArray* result = [NSJSONSerialization JSONObjectWithData:data options:kNilOptions error:&error];
        if(error) {
            if(completedBlock) {
                completedBlock(nil,NO);
            }
            
            return;
        }
        
        if(result.count < 2) {
            if(completedBlock) {
                completedBlock(nil, NO);
            }
            
            return;
        }
        
        if(completedBlock) {
            if([result isKindOfClass:[NSArray class]] && result.count >= 2) {
                completedBlock(result[1], YES);
            } else {
                completedBlock(nil, NO);
            }
        }
    }];
    
    [self.request resume];
}

- (void)cancelPendingRequest
{
    [self.request cancel];
}

@end
