//
//  SearchSuggestManager.h
//  PPBrowser
//
//  Created by qingbin on 2022/4/22.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>

#import "SearchModel.h"
#import "SearchManager.h"

@interface SearchSuggestManager : NSObject

+ (instancetype)shareInstance;

- (void)querySuggest:(NSString*)content completedBlock:(void(^)(NSArray* result, BOOL succ))completedBlock;

@end


