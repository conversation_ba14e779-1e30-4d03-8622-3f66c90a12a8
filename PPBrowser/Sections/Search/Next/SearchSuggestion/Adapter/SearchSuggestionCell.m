//
//  SearchSuggestionCell.m
//  PPBrowser
//
//  Created by qingbin on 2022/4/22.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "SearchSuggestionCell.h"

#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "UIView+FrameHelper.h"
#import "NSObject+Helper.h"

#import "ThemeProtocol.h"
#import "PreferenceManager.h"
#import "BrowserUtils.h"

@interface SearchSuggestionCell ()

@property(nonatomic,strong) HistoryModel* model;

@property(nonatomic,strong) UIImageView* logo;

@property(nonatomic,strong) UILabel* titleLabel;

@property(nonatomic,strong) UILabel* urlLabel;

@property(nonatomic,strong) UIImageView* arrow;

@property(nonatomic,strong) UIView* line;

@end

@implementation SearchSuggestionCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        self.backgroundColor = [UIColor colorWithHexString:@"#ffffff"];
        [self addSubviews];
        [self defineLayout];
        [self setupObservers];
    }
    
    return self;
}

- (void)updateWithModel:(HistoryModel*)model
{
    //可以增加区分, 联想词和url的联想词使用不同的logo
    self.model = model;
    
    if(model.searchType == HistorySearchTypeKeyword) {
        self.titleLabel.text = model.keyword;
    } else {
        self.titleLabel.text = model.title;
    }
    
    if(model.searchType == HistorySearchTypePasteboard) {
        self.arrow.hidden = YES;
    } else {
        self.arrow.hidden = NO;
    }
    
    self.urlLabel.text = model.url;
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        self.titleLabel.textColor = UIColor.whiteColor;
        self.logo.tintColor = UIColor.whiteColor;
        self.arrow.tintColor = UIColor.whiteColor;
        self.line.hidden = YES;
    } else {
        self.backgroundColor = [UIColor colorWithHexString:@"#ffffff"];
        self.titleLabel.textColor = [UIColor colorWithHexString:@"#333333"];
        self.logo.tintColor = [UIColor colorWithHexString:@"#222222"];
        self.arrow.tintColor = [UIColor colorWithHexString:@"#222222"];
        self.line.hidden = NO;
    }
}

- (void)setupObservers
{
    self.arrow.userInteractionEnabled = YES;
    UITapGestureRecognizer* tap = [UITapGestureRecognizer new];
    [self.arrow addGestureRecognizer:tap];
    @weakify(self)
    [tap.rac_gestureSignal subscribeNext:^(id x) {
        @strongify(self)
        if(self.didCopyToTextBlock) {
            self.didCopyToTextBlock(self.model);
        }
    }];
}

- (void)addSubviews
{
    [self.contentView addSubview:self.logo];
    [self.contentView addSubview:self.titleLabel];
    [self.contentView addSubview:self.arrow];
}

- (void)defineLayout
{
    float height = 25;
    float leftOffset = iPadValue(30, 15);
    [self.logo mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.left.mas_offset(leftOffset);
        make.size.mas_equalTo(height);
    }];
        
    [self.arrow mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.right.mas_offset(-leftOffset);
        make.size.mas_equalTo(height);
    }];
    
    UIView* line = [UIView new];
    line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    [self.contentView addSubview:line];
    [line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.contentView);
        make.left.mas_offset(leftOffset);
        make.right.mas_offset(-leftOffset);
        make.height.mas_equalTo(0.5);
    }];
    self.line = line;
    
    [self customDefineLayout];
}

- (void)customDefineLayout
{
    float leftOffset = iPadValue(30, 15);
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.left.equalTo(self.logo.mas_right).offset(20);
        make.right.mas_offset(-leftOffset-25-5);
    }];
}

- (UILabel *)titleLabel
{
    if(!_titleLabel) {
        float font = iPadValue(20, 15);
        _titleLabel = [UIView createLabelWithTitle:@""
                                         textColor:[UIColor colorWithHexString:@"#333333"]
                                           bgColor:UIColor.clearColor
                                          fontSize:font
                                     textAlignment:NSTextAlignmentLeft
                                             bBold:NO];
    }
    
    return _titleLabel;
}

- (UILabel *)urlLabel
{
    if(!_urlLabel) {
        float font = iPadValue(16, 12);
        _urlLabel = [UIView createLabelWithTitle:@""
                                         textColor:[UIColor colorWithHexString:@"#999999"]
                                           bgColor:UIColor.clearColor
                                          fontSize:font
                                     textAlignment:NSTextAlignmentLeft
                                             bBold:NO];
    }
    
    return _urlLabel;
}

- (UIImageView *)logo
{
    if(!_logo) {
        _logo = [UIImageView new];
        
        UIImage* image = [UIImage imageNamed:@"search_scan_icon"];
        image = [image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        
        _logo.image = image;
        _logo.contentMode = UIViewContentModeScaleAspectFit;
    }
    
    return _logo;
}

- (UIImageView *)arrow
{
    if(!_arrow) {
        _arrow = [UIImageView new];
        
        UIImage* image = [UIImage imageNamed:@"search_downleft_arrow"];
        image = [image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        
        _arrow.image = image;
        _arrow.contentMode = UIViewContentModeScaleAspectFit;
    }
    
    return _arrow;
}

@end

@implementation SearchSuggestionMutiCell

- (void)addSubviews
{
    [super addSubviews];
    
    [self.contentView addSubview:self.urlLabel];
}

- (void)customDefineLayout
{
    float leftOffset = iPadValue(30, 15);
    float offset = iPadValue(5, 2);
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.mas_centerY).offset(-offset);
        make.left.equalTo(self.logo.mas_right).offset(20);
        make.right.mas_offset(-leftOffset-25-5);
    }];
    
    [self.urlLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.mas_centerY).offset(offset);
        make.left.equalTo(self.logo.mas_right).offset(20);
        make.right.equalTo(self.titleLabel);
    }];
}

@end
