//
//  SearchSlider.m
//  PPBrowser
//
//  Created by qingbin on 2022/5/2.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "SearchSlider.h"

#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "UIView+FrameHelper.h"
#import "NSObject+Helper.h"

#import "ThemeProtocol.h"
#import "PPNotifications.h"

@interface SearchSlider ()<ThemeProtocol>

@property (nonatomic, strong) UIButton* dotBtn;
@property (nonatomic, strong) UIButton* lineBtn;
@property (nonatomic, strong) UIButton* cnBtn;
@property (nonatomic, strong) UIButton* comBtn;

@property (nonatomic, strong) UISlider* slider;

@property (nonatomic, assign) BOOL isValid;

@property (nonatomic, strong) NSArray* actionButtons;

@end

@implementation SearchSlider

- (instancetype)initWithFrame:(CGRect)frame
{
    if (self = [super initWithFrame:frame]) {
        [self addSubviews];
        [self defineLayout];
        [self setupObservers];
        
        self.actionButtons = @[self.dotBtn, self.lineBtn, self.cnBtn, self.comBtn];
        
        [self applyTheme];
    }
    return self;
}

- (void)dealloc
{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

+ (instancetype)toolBar
{
    SearchSlider* slider = [SearchSlider new];
    slider.frame = CGRectMake(0, 0, kScreenWidth, 44);
    
    return slider;
}

- (void)setupObservers
{
    @weakify(self)
    [[self.slider rac_signalForControlEvents:UIControlEventValueChanged]
     subscribeNext:^(id x) {
        @strongify(self)
        if(!self.isValid) return;
        if(self.didChangedValue) {
            self.didChangedValue(self.slider.value);
        }
    }];
    
    [[self.slider rac_signalForControlEvents:UIControlEventTouchDown]
     subscribeNext:^(id x) {
        @strongify(self)
        self.isValid = YES;
        [self touchDown];
    }];
    
    [[self.slider rac_signalForControlEvents:UIControlEventTouchUpInside]
     subscribeNext:^(id x) {
        @strongify(self)
        self.isValid = NO;
        [self touchUp];
    }];
    
    [[self.slider rac_signalForControlEvents:UIControlEventTouchUpOutside]
     subscribeNext:^(id x) {
        @strongify(self)
        [self touchUp];
    }];
    
    [[self.dotBtn rac_signalForControlEvents:UIControlEventTouchUpInside]
     subscribeNext:^(id x) {
        @strongify(self)
        if(self.didSelectOption){
            self.didSelectOption(@".");
        }
    }];
    
    [[self.lineBtn rac_signalForControlEvents:UIControlEventTouchUpInside]
     subscribeNext:^(id x) {
        @strongify(self)
        if(self.didSelectOption){
            self.didSelectOption(@"/");
        }
    }];
    
    [[self.cnBtn rac_signalForControlEvents:UIControlEventTouchUpInside]
     subscribeNext:^(id x) {
        @strongify(self)
        if(self.didSelectOption){
            self.didSelectOption(@".cn");
        }
    }];
    
    [[self.comBtn rac_signalForControlEvents:UIControlEventTouchUpInside]
     subscribeNext:^(id x) {
        @strongify(self)
        if(self.didSelectOption){
            self.didSelectOption(@".com");
        }
    }];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(darkThemeDidChangeNotification:)
                                                 name:kDarkThemeDidChangeNotification
                                               object:nil];
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}

#pragma mark -- 暗黑模式
- (void)applyTheme
{
    BOOL isDarkTheme = [[PreferenceManager shareInstance].items.isDarkTheme boolValue];
    if(isDarkTheme) {
        self.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        
        for(UIButton* button in self.actionButtons) {
            [button setTitleColor:[UIColor colorWithHexString:@"#ffffff"] forState:UIControlStateNormal];
            button.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        }
        
        UIColor* color = [UIColor colorWithHexString:kDarkThemeColor030];
        self.slider.minimumTrackTintColor = color;
        self.slider.maximumTrackTintColor = color;
        self.slider.thumbTintColor = color;
    } else {
        self.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        
        for(UIButton* button in self.actionButtons) {
            [button setTitleColor:[UIColor colorWithHexString:@"#333333"] forState:UIControlStateNormal];
            button.backgroundColor = UIColor.whiteColor;
        }
        
        UIColor* color = UIColor.whiteColor;
        self.slider.minimumTrackTintColor = color;
        self.slider.maximumTrackTintColor = color;
        self.slider.thumbTintColor = color;
    }
}

- (void)touchDown
{
    [self.slider mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self);
        make.height.equalTo(self);
        make.left.mas_offset(15);
        make.right.mas_offset(-15);
    }];
    
    self.slider.minimumTrackTintColor = [UIColor colorWithHexString:@"#2D7AFE"];
    self.slider.maximumTrackTintColor = [UIColor colorWithHexString:@"#2D7AFE"];
    
    self.dotBtn.hidden = YES;
    self.lineBtn.hidden = YES;
    self.cnBtn.hidden = YES;
    self.comBtn.hidden = YES;
    
    if(self.didTouchDown) {
        self.didTouchDown();
    }
}

- (void)touchUp
{
    [self.slider setValue:0.5 animated:YES];
    
    [self.slider mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self);
        make.height.equalTo(self);
        make.width.mas_equalTo(100);
    }];
    
//    self.slider.minimumTrackTintColor = UIColor.whiteColor;
//    self.slider.maximumTrackTintColor = UIColor.whiteColor;
    [self applyTheme];
    
    self.dotBtn.hidden = NO;
    self.lineBtn.hidden = NO;
    self.cnBtn.hidden = NO;
    self.comBtn.hidden = NO;
    
    if(self.didTouchUp) {
        self.didTouchUp();
    }
}

- (void)addSubviews
{
    [self addSubview:self.slider];
    [self addSubview:self.dotBtn];
    [self addSubview:self.lineBtn];
    [self addSubview:self.comBtn];
    [self addSubview:self.cnBtn];
}

- (void)defineLayout
{
    [self.dotBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self).offset(10);
        make.centerY.equalTo(self);
        make.height.mas_equalTo(44-15);
        make.right.equalTo(self.lineBtn.mas_left).offset(-10);
        make.width.equalTo(self.lineBtn);
    }];
    
    [self.lineBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self);
        make.height.equalTo(self.dotBtn);
        make.right.equalTo(self.slider.mas_left).offset(-10);
    }];
    
    [self.slider mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self);
        make.height.equalTo(self);
        make.width.mas_equalTo(100);
    }];
    
    [self.comBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self).offset(-10);
        make.centerY.equalTo(self);
        make.height.equalTo(self.dotBtn);
        make.left.equalTo(self.cnBtn.mas_right).offset(10);
        make.width.equalTo(self.lineBtn);
    }];
    
    [self.cnBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self);
        make.height.equalTo(self.dotBtn);
        make.left.equalTo(self.slider.mas_right).offset(10);
        make.width.equalTo(self.lineBtn);
    }];
}

- (UISlider *)slider
{
    if(!_slider) {
        _slider = [UISlider new];
        _slider.value = 0.5;
        _slider.minimumValue = 0;
        _slider.maximumValue = 1;
        _slider.minimumTrackTintColor = UIColor.whiteColor;
        _slider.maximumTrackTintColor = UIColor.whiteColor;
        _slider.thumbTintColor = UIColor.whiteColor;
    }
    
    return _slider;
}

- (UIButton *)dotBtn
{
    if(!_dotBtn) {
        _dotBtn = [UIButton new];
        [_dotBtn setTitle:@"." forState:UIControlStateNormal];
        [_dotBtn setTitleColor:[UIColor colorWithHexString:@"#333333"] forState:UIControlStateNormal];
        _dotBtn.backgroundColor = UIColor.whiteColor;
        _dotBtn.layer.cornerRadius = (44-15)/2.0;
        _dotBtn.layer.masksToBounds = YES;
    }
    
    return _dotBtn;
}

- (UIButton *)lineBtn
{
    if(!_lineBtn) {
        _lineBtn = [UIButton new];
        [_lineBtn setTitle:@"/" forState:UIControlStateNormal];
        [_lineBtn setTitleColor:[UIColor colorWithHexString:@"#333333"] forState:UIControlStateNormal];
        _lineBtn.backgroundColor = UIColor.whiteColor;
        _lineBtn.layer.cornerRadius = (44-15)/2.0;
        _lineBtn.layer.masksToBounds = YES;
    }
    
    return _lineBtn;
}

- (UIButton *)cnBtn
{
    if(!_cnBtn) {
        _cnBtn = [UIButton new];
        [_cnBtn setTitle:@".cn" forState:UIControlStateNormal];
        [_cnBtn setTitleColor:[UIColor colorWithHexString:@"#333333"] forState:UIControlStateNormal];
        _cnBtn.backgroundColor = UIColor.whiteColor;
        _cnBtn.layer.cornerRadius = (44-15)/2.0;
        _cnBtn.layer.masksToBounds = YES;
    }
    
    return _cnBtn;
}

- (UIButton *)comBtn
{
    if(!_comBtn) {
        _comBtn = [UIButton new];
        [_comBtn setTitle:@".com" forState:UIControlStateNormal];
        [_comBtn setTitleColor:[UIColor colorWithHexString:@"#333333"] forState:UIControlStateNormal];
        _comBtn.backgroundColor = UIColor.whiteColor;
        _comBtn.layer.cornerRadius = (44-15)/2.0;
        _comBtn.layer.masksToBounds = YES;
    }
    
    return _comBtn;
}

@end
