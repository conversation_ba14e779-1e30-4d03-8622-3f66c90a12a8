//
//  SearchModel.m
//  PPBrowser
//
//  Created by qingbin on 2022/5/26.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "SearchModel.h"
#import "BrowserUtils.h"

@implementation SearchModel

//秘塔AI搜索
+ (instancetype)metaso
{
//    https://metaso.cn/?s=foucll&referrer_s=foucll&q=
    
    SearchModel* item = [SearchModel new];
    item.shortName = NSLocalizedString(@"search.metaso", nil);
    
    if([BrowserUtils isiPad]) {
        item.searchTemplate = @"https://metaso.cn/?s=foucll&referrer_s=foucll&q={searchTerms}";
    } else {
        item.searchTemplate = @"https://metaso.cn/?s=foucll&referrer_s=foucll&q={searchTerms}";
    }
    
    item.image = [UIImage imageNamed:@"mita"];
    item.type = SearchEngineTypeMetaso;
    
    item.searchTermComponent = @"{searchTerms}";
    
    return item;
}

+ (instancetype)baidu
{
    SearchModel* item = [SearchModel new];
    item.shortName = NSLocalizedString(@"search.baidu", nil);
    
    if([BrowserUtils isiPad]) {
        item.searchTemplate = @"https://www.baidu.com/s?word={searchTerms}";
        item.suggestTemplate = @"https://www.baidu.com/su?wd={searchTerms}&action=opensearch&ie=utf-8";
    } else {
        item.searchTemplate = @"https://m.baidu.com/s?word={searchTerms}";
        item.suggestTemplate = @"https://m.baidu.com/su?wd={searchTerms}&action=opensearch&ie=utf-8";
    }
    
    item.image = [UIImage imageNamed:@"baidu_logo"];
    item.type = SearchEngineTypeBaidu;
    
    item.searchTermComponent = @"{searchTerms}";
    
    return item;
}

+ (instancetype)biying
{
    SearchModel* item = [SearchModel new];
    item.shortName = NSLocalizedString(@"search.bing", nil);
    item.searchTemplate = @"https://www.bing.com/search?q={searchTerms}";
    item.suggestTemplate = @"https://www.bing.com/osjson.aspx?query={searchTerms}&language={moz:locale}";
    item.image = [UIImage imageNamed:@"bing_logo"];
    item.type = SearchEngineTypeBing;
    
    item.searchTermComponent = @"{searchTerms}";
    item.localeTermComponent = @"{moz:locale}";
    
    return item;
}

//中国必应
+ (instancetype)biyingZhCN
{
    SearchModel* item = [SearchModel new];
    item.shortName = NSLocalizedString(@"search.bing", nil);
    item.searchTemplate = @"https://cn.bing.com/search?q={searchTerms}";
    item.suggestTemplate = @"https://www.bing.com/osjson.aspx?query={searchTerms}&language={moz:locale}";
    item.image = [UIImage imageNamed:@"bing_logo"];
    item.type = SearchEngineTypeBing;
    
    item.searchTermComponent = @"{searchTerms}";
    item.localeTermComponent = @"{moz:locale}";
    
    return item;
}

+ (instancetype)google
{
    SearchModel* item = [SearchModel new];
    item.shortName = NSLocalizedString(@"search.google", nil);
    item.searchTemplate = @"https://www.google.com/search?q={searchTerms}&ie=utf-8&oe=utf-8";
    item.suggestTemplate = @"https://www.google.com/complete/search?oe=utf-8&ie=utf-8&client=firefox&q={searchTerms}";
    item.image = [UIImage imageNamed:@"google_logo_2"];
    item.type = SearchEngineTypeGoogle;
    
    item.searchTermComponent = @"{searchTerms}";
    
    return item;
}

+ (instancetype)search360
{
    SearchModel* item = [SearchModel new];
    item.shortName = @"360";
    
    if([BrowserUtils isiPad]) {
        item.searchTemplate = @"https://www.so.com/s?ie=utf-8&fr=none&src=360sou_newhome&nlpv=basest&q={searchTerms}";
    } else {
        item.searchTemplate = @"https://m.so.com/s?ie=utf-8&fr=none&src=360sou_newhome&nlpv=basest&q={searchTerms}";
    }

    item.image = [UIImage imageNamed:@"search_360_icon"];
    item.type = SearchEngineType360;
    
    item.searchTermComponent = @"{searchTerms}";
    
    return item;
}

//+ (instancetype)sougou
//{
//    SearchModel* item = [SearchModel new];
//    item.shortName = NSLocalizedString(@"search.sougou", nil);
//    
//    if([BrowserUtils isiPad]) {
//        item.searchTemplate = @"https://m.sogou.com/web/searchList.jsp?s_from=pcsearch&keyword={searchTerms}";
//    } else {
//        item.searchTemplate = @"https://m.sogou.com/web/searchList.jsp?s_from=pcsearch&keyword={searchTerms}";
//    }
//
//    item.image = [UIImage imageNamed:@"sougou_logo"];
//    item.type = SearchEngineTypeSougou;
//    
//    item.searchTermComponent = @"{searchTerms}";
//    
//    return item;
//}

+ (instancetype)toutiao
{
    SearchModel* item = [SearchModel new];
    item.shortName = NSLocalizedString(@"search.toutiao", nil);
    item.searchTemplate = @"https://so.toutiao.com/search?source=input&keyword={searchTerms}";
//    item.suggestTemplate = @"https://www.google.com/complete/search?q={searchTerms}";
    item.image = [UIImage imageNamed:@"toutiao_logo"];
    item.type = SearchEngineTypeTouTiao;
    
    item.searchTermComponent = @"{searchTerms}";
    
    return item;
}

+ (instancetype)duckduckgo
{
    SearchModel* item = [SearchModel new];
    item.shortName = @"DuckDuckGo";
    item.searchTemplate = @"https://duckduckgo.com/?q={searchTerms}";
    item.image = [UIImage imageNamed:@"duckduckgo_logo"];
    item.type = SearchEngineTypeDuckduckgo;
    
    item.searchTermComponent = @"{searchTerms}";
    
    return item;
}

+ (instancetype)yandex
{
    SearchModel* item = [SearchModel new];
    item.shortName = @"Yandex";
    item.searchTemplate = @"https://yandex.com/search?text={searchTerms}";
    item.image = [UIImage imageNamed:@"yandex_logo"];
    item.type = SearchEngineTypeYandex;
    
    item.searchTermComponent = @"{searchTerms}";
    
    return item;
}

+ (instancetype)yahoo
{
    SearchModel* item = [SearchModel new];
    item.shortName = @"Yahoo";
    item.searchTemplate = @"https://search.yahoo.com/search?&ei=UTF-8&p={searchTerms}";
    item.image = [UIImage imageNamed:@"yahoo_logo"];
    item.type = SearchEngineTypeYahoo;
    
    item.searchTermComponent = @"{searchTerms}";
    
    return item;
}

+ (instancetype)aol
{
    SearchModel* item = [SearchModel new];
    item.shortName = @"Aol";
    item.searchTemplate = @"https://search.aol.com/aol/search?q={searchTerms}";
    item.image = [UIImage imageNamed:@"aol_logo"];
    item.type = SearchEngineTypeAol;
    
    item.searchTermComponent = @"{searchTerms}";
    
    return item;
}

+ (instancetype)ecosia
{
    SearchModel* item = [SearchModel new];
    item.shortName = @"Ecosia";
    item.searchTemplate = @"https://ecosia.org/search?q={searchTerms}";
    item.image = [UIImage imageNamed:@"ecosia_logo"];
    item.type = SearchEngineTypeEcosia;
    
    item.searchTermComponent = @"{searchTerms}";
    
    return item;
}

//APP内搜索
+ (instancetype)googleTranslate
{
    SearchModel* item = [SearchModel new];
//    item.shortName = @"Google Translate";
    item.shortName = NSLocalizedString(@"search.googletranslate", nil);
    item.searchTemplate = @"https://translate.google.com/#view=home&op=translate&sl=auto&tl=en&text={searchTerms}";
    item.image = [UIImage imageNamed:@"google_translate_logo"];
    item.type = SearchEngineTypeGoogleTranslate;
    
    item.searchTermComponent = @"{searchTerms}";
    
    return item;
}

+ (instancetype)greasyfork
{
    SearchModel* item = [SearchModel new];
    item.shortName = @"Greasyfork";
    item.searchTemplate = @"https://greasyfork.org/scripts?q={searchTerms}";
    item.image = [UIImage imageNamed:@"greasyfork_logo"];
    item.type = SearchEngineTypeGreasyfork;
    
    item.searchTermComponent = @"{searchTerms}";
    
    return item;
}

+ (instancetype)twitter
{
    SearchModel* item = [SearchModel new];
    item.shortName = @"Twitter";
    item.searchTemplate = @"https://twitter.com/search?q={searchTerms}";
    item.image = [UIImage imageNamed:@"twitter_logo"];
    item.type = SearchEngineTypeTwitter;
    
    item.searchTermComponent = @"{searchTerms}";
    
    return item;
}

+ (instancetype)youtube
{
    SearchModel* item = [SearchModel new];
    item.shortName = @"Youtube";
    item.searchTemplate = @"https://www.youtube.com/results?search_query={searchTerms}";
    item.image = [UIImage imageNamed:@"youtube_logo"];
    item.type = SearchEngineTypeYoutube;
    
    item.searchTermComponent = @"{searchTerms}";
    
    return item;
}

+ (instancetype)reddit
{
    SearchModel* item = [SearchModel new];
    item.shortName = @"Reddit";
    item.searchTemplate = @"https://www.reddit.com/search?q={searchTerms}";
    item.image = [UIImage imageNamed:@"reddit_logo"];
    item.type = SearchEngineTypeReddit;
    
    item.searchTermComponent = @"{searchTerms}";
    
    return item;
}

+ (SearchModel *)searchModelWithSearchType:(SearchEngineType)searchType
{
    switch (searchType) {
        case SearchEngineTypeBaidu:
            return [SearchModel baidu];
            break;
        case SearchEngineTypeGoogle:
            return [SearchModel google];
            break;
        case SearchEngineTypeTouTiao:
            return [SearchModel toutiao];
            break;
        case SearchEngineTypeBing:
            return [SearchModel biying];
            break;
        case SearchEngineType360:
            return [SearchModel search360];
            break;
        case SearchEngineTypeDuckduckgo:
            return [SearchModel duckduckgo];
            break;
        case SearchEngineTypeEcosia:
            return [SearchModel ecosia];
            break;
        case SearchEngineTypeYoutube:
            return [SearchModel youtube];
            break;
        case SearchEngineTypeReddit:
            return [SearchModel reddit];
            break;
        case SearchEngineTypeGreasyfork:
            return [SearchModel greasyfork];
            break;
        case SearchEngineTypeGoogleTranslate:
            return [SearchModel googleTranslate];
            break;
        case SearchEngineTypeYandex:
            return [SearchModel yandex];
            break;
        case SearchEngineTypeMetaso:
            return [SearchModel metaso];
            break;
            
        default:
            break;
    }
    
    return nil;
}

@end
