//
//  SearchManager.m
//  PandaBrowser
//
//  Created by q<PERSON><PERSON> on 2022/3/8.
//

#import "SearchManager.h"
#import <UIKit/UIKit.h>

#import "ONOXMLDocument.h"
#import "NSCharacterSet+Extension.h"

#import "PreferenceManager.h"
#import "PPNotifications.h"
#import "SearchModel.h"

#import "BrowserUtils.h"

@interface SearchManager ()

@property(nonatomic,strong) SearchModel *currentSearchEngine;

@property(nonatomic,strong) NSCharacterSet* searchTermsAllowed;
@property(nonatomic,strong) NSCharacterSet* URLAllowed;
//当前语言地区对应的搜索引擎组合
@property(nonatomic,strong) NSMutableArray* allSearchEngines;
//所有内置搜索引擎
@property (nonatomic, strong) NSMutableArray *allBuiltInSearchEngines;

@property(nonatomic,strong) NSMutableDictionary* typeForEngines;

@end

@implementation SearchManager

+ (instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    static SearchManager* obj;
    dispatch_once(&onceToken, ^{
        obj = [SearchManager new];
    });
    
    return obj;
}

- (instancetype)init
{
    self = [super init];
    if(self) {
        self.typeForEngines = [NSMutableDictionary dictionary];
        
        [self commonInit];
    }
    
    return self;
}

- (void)commonInit
{
    self.allBuiltInSearchEngines = [@[
        [SearchModel metaso],
        [SearchModel baidu],
        [SearchModel biying],
        [SearchModel biyingZhCN],
        [SearchModel google],
        [SearchModel search360],
        [SearchModel toutiao],
        [SearchModel duckduckgo],
        [SearchModel yahoo],
        [SearchModel aol],
        [SearchModel ecosia],
        [SearchModel yandex],
        [SearchModel googleTranslate],
        [SearchModel greasyfork],
        [SearchModel twitter],
        [SearchModel youtube],
        [SearchModel reddit],
    ] mutableCopy];
}

//根据默认配置更新当前搜索引擎
- (void)updateCurrentSearchEngineInPreference
{
    SearchEngineType searchEngineType = [[PreferenceManager shareInstance].items.searchEngineType intValue];
    
    for(SearchModel* item in self.allSearchEngines) {
        item.isSelected = NO;
    }
    
    SearchModel* engine = self.typeForEngines[@(searchEngineType)];
    if(!engine) {
        engine = self.allSearchEngines.firstObject;
    }
    
    engine.isSelected = YES;
    self.currentSearchEngine = engine;
}

//设置当前搜索引擎
- (void)updateCurrentSearchEngine:(SearchModel*)searchEngine
{
    for(SearchModel* item in self.allSearchEngines) {
        item.isSelected = NO;
    }
    
    searchEngine.isSelected = YES;
    self.currentSearchEngine = searchEngine;
    
    //更新当前搜索引擎
    [PreferenceManager shareInstance].items.searchEngineType = @(searchEngine.type);
    [[PreferenceManager shareInstance] encode];
    
    //发通知更新
    [[NSNotificationCenter defaultCenter] postNotificationName:kUpdateSearchEngineNotification object:nil];
}

//获取当前搜索引擎
- (SearchModel*)getCurrentSearchEngine
{
    return self.currentSearchEngine;
}

//获取内置的所有搜索引擎
- (NSArray<SearchModel *> *)getAllBuiltInSearchEngines
{
    return self.allBuiltInSearchEngines;
}

//获取所有的搜索引擎
- (NSArray*)getAllSearchEngines
{
    if(self.allSearchEngines.count == 0) {
        SearchModel* item;
        [self.searchEngines removeAllObjects];
        [self.appEngines removeAllObjects];
        
        //初始化
//        NSLocale* locale = [NSLocale currentLocale];
//        NSString* localeString = [locale.localeIdentifier lowercaseString];
        //https://www.jianshu.com/p/90649f97fcaf
        //https://blog.csdn.net/qq_18683985/article/details/87793294
        
        LocalizableOption option = [BrowserUtils localizableOption];
        
        if(option == LocalizableOptionZh_Hans) {
            //中国大陆
            item = [SearchModel metaso];
            [self.searchEngines addObject:item];
            
            item = [SearchModel baidu];
            [self.searchEngines addObject:item];
            
            item = [SearchModel google];
            [self.searchEngines addObject:item];
            
            item = [SearchModel biying];
            [self.searchEngines addObject:item];
            
            item = [SearchModel duckduckgo];
            [self.searchEngines addObject:item];
            
            item = [SearchModel ecosia];
            [self.searchEngines addObject:item];
            
            item = [SearchModel search360];
            [self.searchEngines addObject:item];
            
//            item = [SearchModel sougou];
//            [self.searchEngines addObject:item];
            
            item = [SearchModel toutiao];
            [self.searchEngines addObject:item];
            
            item = [SearchModel yandex];
            [self.searchEngines addObject:item];
            
            //APP内搜索
        } else {
            //默认英语+其他华语地区
            item = [SearchModel google];
            [self.searchEngines addObject:item];
            
            item = [SearchModel biying];
            [self.searchEngines addObject:item];
            
            item = [SearchModel duckduckgo];
            [self.searchEngines addObject:item];
            
            item = [SearchModel baidu];
            [self.searchEngines addObject:item];
            
            item = [SearchModel yandex];
            [self.searchEngines addObject:item];
            
            item = [SearchModel yahoo];
            [self.searchEngines addObject:item];
            
            item = [SearchModel ecosia];
            [self.searchEngines addObject:item];
            
            item = [SearchModel aol];
            [self.searchEngines addObject:item];
        
//            item = [SearchModel sougou];
//            [self.searchEngines addObject:item];
            
//            item = [SearchModel search360];
//            [self.searchEngines addObject:item];
            
            //APP内搜索
        }
        
        //APP内搜索
        item = [SearchModel googleTranslate];
        [self.appEngines addObject:item];
        
        item = [SearchModel greasyfork];
        [self.appEngines addObject:item];
        
        item = [SearchModel youtube];
        [self.appEngines addObject:item];
        
        item = [SearchModel twitter];
        [self.appEngines addObject:item];
        
        item = [SearchModel reddit];
        [self.appEngines addObject:item];
    
        //初始化
        [self.allSearchEngines addObjectsFromArray:self.searchEngines];
        [self.allSearchEngines addObjectsFromArray:self.appEngines];
        for(SearchModel* item in self.allSearchEngines) {
            self.typeForEngines[@(item.type)] = item;
        }
    }
    
    return self.allSearchEngines;
}

// 搜索(默认搜索引擎)
- (NSURL *)searchURLForQuery:(NSString *)query
{
    NSLocale* locale = [NSLocale currentLocale];
    if(!self.currentSearchEngine) {
        [self updateCurrentSearchEngine:self.allSearchEngines.firstObject];
    }
    
    return [self getURLFromTemplate:self.currentSearchEngine.searchTemplate query:query locale:locale];
}

// 搜索(指定搜索引擎)
- (NSURL *)searchURLForQuery:(NSString *)query
                  searchType:(SearchEngineType)searchType
{
    SearchModel* searchModel = [SearchModel searchModelWithSearchType:searchType];
    if (!searchModel) {
        searchModel = self.currentSearchEngine;
    }
    
    NSLocale* locale = [NSLocale currentLocale];
    return [self getURLFromTemplate:searchModel.searchTemplate query:query locale:locale];
}

//搜索建议
- (NSURL*)suggestURLForQuery:(NSString*)query
{
    NSLocale* locale = [NSLocale currentLocale];
    return [self getURLFromTemplate:self.currentSearchEngine.suggestTemplate query:query locale:locale];
}

- (NSURL*)getURLFromTemplate:(NSString*)searchTemplate query:(NSString*)query locale:(NSLocale*)locale
{
    NSString* escapedQuery = [query stringByAddingPercentEncodingWithAllowedCharacters:self.searchTermsAllowed];
    if(escapedQuery.length == 0) return nil;
    
    // Escape the search template as well in case it contains not-safe characters like symbols
    NSMutableCharacterSet* templateAllowedSet = [NSMutableCharacterSet new];
    [templateAllowedSet formUnionWithCharacterSet:self.URLAllowed];
    
    // Allow brackets since we use them in our template as our insertion point
    [templateAllowedSet formUnionWithCharacterSet:[NSCharacterSet characterSetWithCharactersInString:@"{}"]];
    NSString* encodedSearchTemplate = [searchTemplate stringByAddingPercentEncodingWithAllowedCharacters:templateAllowedSet];
    
    NSString* localeString = locale.localeIdentifier;
    NSString* urlString;
    if(self.currentSearchEngine.searchTermComponent.length > 0) {
        urlString = [encodedSearchTemplate stringByReplacingOccurrencesOfString:self.currentSearchEngine.searchTermComponent withString:escapedQuery];
    }
    
    if(self.currentSearchEngine.localeTermComponent.length > 0) {
        urlString = [urlString stringByReplacingOccurrencesOfString:self.currentSearchEngine.localeTermComponent withString:localeString];
    }
     
    return [NSURL URLWithString:urlString];
}

#pragma mark - getters

- (NSCharacterSet*)URLAllowed
{
    if(!_URLAllowed) {
        _URLAllowed = [NSCharacterSet URLAllowed];
    }
    
    return _URLAllowed;
}

- (NSCharacterSet*)searchTermsAllowed
{
    if(!_searchTermsAllowed) {
        _searchTermsAllowed = [NSCharacterSet searchTermsAllowed];
    }
    
    return _searchTermsAllowed;
}

- (NSMutableArray *)allSearchEngines
{
    if(!_allSearchEngines) {
        _allSearchEngines = [NSMutableArray array];
    }
    
    return _allSearchEngines;
}

- (NSMutableArray *)searchEngines
{
    if(!_searchEngines) {
        _searchEngines = [NSMutableArray array];
    }
    
    return _searchEngines;
}

- (NSMutableArray *)appEngines
{
    if(!_appEngines) {
        _appEngines = [NSMutableArray array];
    }
    
    return _appEngines;
}

- (NSMutableArray *)allBuiltInSearchEngines
{
    if (!_allBuiltInSearchEngines) {
        _allBuiltInSearchEngines = [NSMutableArray array];
    }
    
    return _allBuiltInSearchEngines;
}

@end
