//
//  SearchManager.h
//  PandaBrowser
//
//  Created by q<PERSON><PERSON> on 2022/3/8.
//

#import <Foundation/Foundation.h>

#import "URIFixup.h"
#import "SearchModel.h"

@interface SearchManager : NSObject

+ (instancetype)shareInstance;

//根据默认配置更新当前搜索引擎
- (void)updateCurrentSearchEngineInPreference;

//设置当前搜索引擎
- (void)updateCurrentSearchEngine:(SearchModel*)searchEngine;

//获取当前搜索引擎
- (SearchModel*)getCurrentSearchEngine;

//获取内置的所有搜索引擎
- (NSArray<SearchModel *> *)getAllBuiltInSearchEngines;

// 搜索(默认搜索引擎)
- (NSURL *)searchURLForQuery:(NSString *)query;
// 搜索(指定搜索引擎)
- (NSURL *)searchURLForQuery:(NSString *)query searchType:(SearchEngineType)searchType;

//搜索建议
- (NSURL *)suggestURLForQuery:(NSString *)query;

//获取所有的搜索引擎
- (NSArray*)getAllSearchEngines;
//搜索引擎
@property(nonatomic,strong) NSMutableArray* searchEngines;
//APP搜索
@property(nonatomic,strong) NSMutableArray* appEngines;

@end


