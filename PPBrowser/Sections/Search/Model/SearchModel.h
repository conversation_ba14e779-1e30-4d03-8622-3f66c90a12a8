//
//  SearchModel.h
//  PPBrowser
//
//  Created by qingbin on 2022/5/26.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import "PPEnums.h"

/*
 品牌站logo
 logo:
 https://ixintu.com/
 https://worldvectorlogo.com/
 https://www.iloveimg.com/zh-cn/resize-image/resize-svg#resize-options,pixels
 https://svgtopng.com/
 */

@interface SearchModel : NSObject
//类型
@property (nonatomic, assign) SearchEngineType type;
//v2.7.1, 排序
@property (nonatomic, assign) NSInteger position;

//以下是非数据库字段

//简称
@property (nonatomic, strong) NSString *shortName;
//搜索模板
@property (nonatomic, strong) NSString *searchTemplate;
//搜索建议模板
@property (nonatomic, strong) NSString *suggestTemplate;
//logo
@property (nonatomic, strong) UIImage *image;

//搜索词替换模板
@property(nonatomic,strong) NSString *searchTermComponent;
@property(nonatomic,strong) NSString *localeTermComponent;
@property(nonatomic,strong) NSString *regionalClientComponent;

//辅助
/// 是否是选中状态
@property (nonatomic, assign) BOOL isSelected;

//辅助字段,主要是卡片化
@property (nonatomic, assign) BOOL isFirstInSection;
@property (nonatomic, assign) BOOL isLastInSection;

//秘塔AI搜索
+ (instancetype)metaso;
+ (instancetype)baidu;
+ (instancetype)biying;
+ (instancetype)google;
+ (instancetype)search360;
//+ (instancetype)sougou;
+ (instancetype)toutiao;

+ (instancetype)duckduckgo;
+ (instancetype)yahoo;
+ (instancetype)aol;
+ (instancetype)ecosia;
+ (instancetype)yandex;

//中国必应
+ (instancetype)biyingZhCN;

//APP内搜索
+ (instancetype)googleTranslate;
+ (instancetype)greasyfork;
+ (instancetype)twitter;
+ (instancetype)youtube;
+ (instancetype)reddit;

+ (SearchModel *)searchModelWithSearchType:(SearchEngineType)searchType;

@end

