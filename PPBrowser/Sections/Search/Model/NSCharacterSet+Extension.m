//
//  NSCharacterSet+Extension.m
//  PandaBrowser
//
//  Created by q<PERSON><PERSON> on 2022/3/8.
//

#import "NSCharacterSet+Extension.h"

@implementation NSCharacterSet (Extension)

+ (NSCharacterSet*)URLAllowed
{
    return [NSCharacterSet
            characterSetWithCharactersInString:@"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~:/?#[]@!$&'()*+,;="];
}

+ (NSCharacterSet*)searchTermsAllowed
{
    return [NSCharacterSet
            characterSetWithCharactersInString:@"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789*-_."];
}

@end
