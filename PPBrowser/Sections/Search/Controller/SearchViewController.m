//
//  SearchViewController.m
//  PPBrowser
//
//  Created by qingbin on 2022/10/3.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "SearchViewController.h"

#import "SearchSettingCell.h"

#import "MaizyHeader.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "NSObject+Helper.h"

#import "SearchManager.h"

#import "ThemeProtocol.h"
#import "PreferenceManager.h"
#import "PPNotifications.h"
#import "BrowserUtils.h"

@interface SearchViewController ()<UITableViewDelegate, UITableViewDataSource, ThemeProtocol>

@property (nonatomic, strong) NSMutableArray* model;

@property (nonatomic, strong) UITableView* tableView;
//自定义搜索引擎
@property (nonatomic, strong) UIImageView* rightImageView;

@end

@implementation SearchViewController

- (void)viewDidLoad
{
    [super viewDidLoad];

    self.navigationItem.title = NSLocalizedString(@"search.ipad.title", nil);
    self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    
    [self createCustomLeftBarButtonItem];
//    [self _createCustomRightBarButtonItem];
    
    [self addSubviews];
    [self defineLayout];
    [self updateWithModel];
}

- (void)updateWithModel
{
    [self.model removeAllObjects];
    
    NSArray* searchEngines = [SearchManager shareInstance].searchEngines;
    SearchModel* item = searchEngines.firstObject;
    item.isFirstInSection = YES;
    item = searchEngines.lastObject;
    item.isLastInSection = YES;
    [self.model addObject:searchEngines];
    
    NSArray* appEngines = [SearchManager shareInstance].appEngines;
    item = appEngines.firstObject;
    item.isFirstInSection = YES;
    item = appEngines.lastObject;
    item.isLastInSection = YES;
    [self.model addObject:appEngines];
    
    [self applyTheme];
    
    [self.tableView reloadData];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.tableView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.tableView.tableHeaderView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.tableView.tableFooterView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
    } else {
        self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.tableView.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.tableView.tableHeaderView.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.tableView.tableFooterView.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    //只有当前的controller会触发一次, 其它已存在的controller走通知
    if(needReload) {
        //修改暗黑模式
        [self applyTheme];
        [self.tableView reloadData];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
    [self.tableView reloadData];
}

#pragma mark -- 屏幕旋转

- (void)viewWillTransitionToSize:(CGSize)size withTransitionCoordinator:(id<UIViewControllerTransitionCoordinator>)coordinator
{
    [self.tableView reloadData];
}

#pragma mark -- 导航栏

- (void)_createCustomRightBarButtonItem
{
    UIButton* rightButton = [[UIButton alloc] initWithFrame:CGRectMake(0.0, 0.0f, 44.0f, 44.0)];
    [rightButton setBackgroundColor:[UIColor clearColor]];

    UIImageView* imageView = [UIImageView new];
    
    UIImage* image = [UIImage imageNamed:@"script_add_icon"];
    image = [image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
    imageView.image = image;
    self.rightImageView = imageView;
    
    [rightButton addSubview:imageView];
    [imageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(rightButton);
        make.size.mas_equalTo(20);
    }];
    
    [rightButton addTarget:self action:@selector(rightBarbuttonClick) forControlEvents:UIControlEventTouchUpInside];
    UIBarButtonItem* barItem = [[UIBarButtonItem alloc] initWithCustomView:rightButton];

    UIBarButtonItem *spacer = [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemFixedSpace target:nil action:nil];
    spacer.width = -16.0f; // for example shift right bar button to the right

    self.navigationItem.rightBarButtonItems = @[spacer, barItem];
}

- (void)rightBarbuttonClick
{
    
}

#pragma mark -- layout

- (void)addSubviews
{
    [self.view addSubview:self.tableView];
}

- (void)defineLayout
{
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(0);
        make.right.mas_offset(-0);
        make.top.equalTo(self.view).offset(0);
        make.bottom.equalTo(self.view);
    }];
}

#pragma mark - UITableViewDataSource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    NSArray* items = self.model[section];
    return items.count;
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return self.model.count;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section
{
    return iPadValue(60, 45);
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section
{
    UIView* header = [UIView new];
    header.backgroundColor = UIColor.clearColor;
    
    UILabel* titleLabel = [UIView createLabelWithTitle:@""
        textColor:[UIColor colorWithHexString:@"#666666"]
          bgColor:UIColor.clearColor
         fontSize:iPadValue(18, 14)
    textAlignment:NSTextAlignmentLeft
            bBold:NO];
    [header addSubview:titleLabel];
    [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_offset(-iPadValue(15, 5));
        make.left.mas_offset(iPadValue(60, 30));
    }];
    
    if(section == 0) {
        titleLabel.text = NSLocalizedString(@"search.search.engine", nil);
    } else if(section == 1) {
        titleLabel.text = NSLocalizedString(@"search.search.app", nil);
    }
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        header.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        titleLabel.textColor = [UIColor colorWithHexString:@"#666666"];
    } else {
        header.backgroundColor = UIColor.clearColor;
        titleLabel.textColor = [UIColor colorWithHexString:@"#666666"];
    }
    
    return header;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    NSArray* items = self.model[indexPath.section];
    SearchModel* model = items[indexPath.row];
    
    SearchSettingCell *cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass(SearchSettingCell.class)];
    [cell updateWithModel:model];
        
    return cell;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    NSArray* items = self.model[indexPath.section];
    SearchModel* model = items[indexPath.row];
    
//    if(self.delegate && [self.delegate respondsToSelector:@selector(searchEngineDidSelected:)]) {
//        [self.delegate searchEngineDidSelected:model];
//    }
    if (self.updateSearchEngineBlock) {
        self.updateSearchEngineBlock(model.shortName);
    }
    
    if(self.navigationController.viewControllers.count == 1) {
        //表示是present的行为
        [self dismissViewControllerAnimated:YES completion:nil];
    } else {
        for(NSArray* items in self.model) {
            for(SearchModel* item in items) {
                item.isSelected = NO;
            }
        }
        model.isSelected = YES;
        
        [self.tableView reloadData];
    }
    
    dispatch_async(dispatch_get_main_queue(), ^{
        [[SearchManager shareInstance] updateCurrentSearchEngine:model];
    });
}

#pragma mark -- lazy init
- (UITableView *)tableView
{
    if(!_tableView)
    {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStyleGrouped];

        _tableView.separatorStyle  = UITableViewCellSeparatorStyleNone;
        _tableView.showsHorizontalScrollIndicator = NO;
        _tableView.showsVerticalScrollIndicator   = NO;
        _tableView.delegate   = self;
        _tableView.dataSource = self;
        _tableView.rowHeight = iPadValue(88, 60);
        _tableView.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        
        [_tableView registerClass:[SearchSettingCell class] forCellReuseIdentifier:NSStringFromClass([SearchSettingCell class])];
        
        //不能用CGFLOAT_MIN,否则iPhone8的iOS14.7会有一大块空白,系统BUG
        float height = iPadValue(10, 0.5);
        UIView* view = [[UIView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, height)];
        view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        _tableView.tableHeaderView = view;
        
        UIWindow* window = [NSObject normalWindow];
        view = [[UIView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, iPadValue(20, 10)+window.safeAreaInsets.bottom)];
        view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        _tableView.tableFooterView = view;
        
        _tableView.sectionFooterHeight = 0.0;
        _tableView.sectionHeaderHeight = 0.0f;
    }
    
    return _tableView;
}

- (NSMutableArray *)model
{
    if(!_model) {
        _model = [NSMutableArray array];
    }
    
    return _model;
}

@end
