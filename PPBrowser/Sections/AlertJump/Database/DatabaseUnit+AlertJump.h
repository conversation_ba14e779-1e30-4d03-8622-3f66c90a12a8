//
//  DatabaseUnit+AlertJump.h
//  Reader
//
//  Created by q<PERSON><PERSON> on 2024/12/15.
//

#import "DatabaseUnit.h"

#import "AlertJumpModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface DatabaseUnit (AlertJump)
//添加数据
+ (DatabaseUnit *)addAlertJumpWithItem:(AlertJumpModel *)item;
//根据tagitId删除
+ (DatabaseUnit *)removeAlertJumpWithId:(NSString *)uuid;
//查询所有数据
+ (DatabaseUnit *)queryAllAlertJump;

@end

NS_ASSUME_NONNULL_END
