//
//  DatabaseUnit+AlertJump.m
//  Reader
//
//  Created by q<PERSON><PERSON> on 2024/12/15.
//

#import "DatabaseUnit+AlertJump.h"
#import "ReactiveCocoa.h"

#import "FMDatabaseQueue.h"
#import "FMDatabase.h"

#import "PPEnums.h"

@implementation DatabaseUnit (AlertJump)

+ (DatabaseUnit *)addAlertJumpWithItem:(AlertJumpModel *)item
{
    //https://stackoverflow.com/questions/3634984/insert-if-not-exists-else-update
    DatabaseUnit* unit = [DatabaseUnit new];
    
    //必须有id
    if(item.uuid.length == 0) {
        item.uuid = [[NSUUID UUID] UUIDString];
    }
    
    if(item.scheme.length == 0) item.scheme = @"";
    item.ctime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        
        NSString* command = @"INSERT INTO t_alertJump(uuid, scheme, ctime) VALUES (?,?,?)\
        on CONFLICT(uuid) DO UPDATE SET scheme=excluded.scheme WHERE excluded.uuid=t_alertJump.uuid;";

        BOOL result = [db executeUpdate:command, item.uuid, item.scheme, item.ctime];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

+ (DatabaseUnit *)removeAlertJumpWithId:(NSString *)uuid
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = @"DELETE FROM t_alertJump WHERE uuid=?;";
        BOOL result = [db executeUpdate:command, uuid];

        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

+ (DatabaseUnit *)queryAllAlertJump
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = [NSString stringWithFormat:@"SELECT * FROM t_alertJump ORDER BY ctime ASC"];
        FMResultSet* set = [db executeQuery:command];
        
        NSMutableArray *array = [[NSMutableArray alloc] init];
        while([set next]) {
            AlertJumpModel* item = [[AlertJumpModel alloc]initWithDictionary:[set resultDictionary] error:nil];
            [array addObject:item];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(array,YES);
            }
        });
    };
    
    return unit;
}

@end
