//
//  AlertJumpModel.h
//  Reader
//
//  Created by q<PERSON><PERSON> on 2024/12/15.
//

#import "BaseModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface AlertJumpModel : BaseModel

@property (nonatomic, strong) NSString* uuid;
//拦截的scheme
@property (nonatomic, strong) NSString* scheme;

@property (nonatomic, strong) NSString* ctime;

//辅助字段,主要是卡片化
@property (nonatomic, assign) BOOL isFirstInSection;
@property (nonatomic, assign) BOOL isLastInSection;

@end

NS_ASSUME_NONNULL_END
