//
//  AlertJumpHelper.h
//  Reader
//
//  Created by q<PERSON><PERSON> on 2024/12/15.
//

#import <Foundation/Foundation.h>

#import "AlertJumpModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface AlertJumpHelper : NSObject

+ (instancetype)shareInstance;

//提前缓存
- (void)reloadData;
//添加屏蔽APP跳转
- (void)addAlertJumpWithUrl:(NSString *)url;
//删除屏蔽APP跳转
- (void)removeAlertJumpWithId:(NSString *)uuid;

//是否在屏蔽名单中
- (BOOL)isBlockURL:(NSURL *)URL;


@end

NS_ASSUME_NONNULL_END
