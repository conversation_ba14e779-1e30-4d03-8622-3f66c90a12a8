//
//  AlertJumpHelper.m
//  Reader
//
//  Created by q<PERSON><PERSON> on 2024/12/15.
//

#import "AlertJumpHelper.h"
#import "DatabaseUnit+AlertJump.h"

#import "ReactiveCocoa.h"
#import "NSURL+Extension.h"

@interface AlertJumpHelper ()

@property (nonatomic, strong) NSMutableDictionary* map;

@end

@implementation AlertJumpHelper

+ (instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    static AlertJumpHelper* obj;
    dispatch_once(&onceToken, ^{
        obj = [AlertJumpHelper new];
    });
    
    return obj;
}

- (instancetype)init
{
    self = [super init];
    if(self) {
        self.map = [NSMutableDictionary dictionary];
    }
    
    return self;
}

//提前缓存
- (void)reloadData
{
    [self.map removeAllObjects];
    
    DatabaseUnit* unit = [DatabaseUnit queryAllAlertJump];
    @weakify(self)
    [unit setCompleteBlock:^(NSArray* result, BOOL success) {
        @strongify(self)
        for(AlertJumpModel *item in result) {
            self.map[item.scheme] = item;
        }
    }];
    
    DB_EXEC(unit);
}

//是否在屏蔽名单中
- (BOOL)isBlockURL:(NSURL *)URL
{
    if(!URL) return NO;
    
    NSString* scheme = URL.scheme;
    if(scheme.length == 0) return NO;
    
    if(self.map[scheme] != nil) return YES;
    
    return NO;
}

//添加屏蔽APP跳转
- (void)addAlertJumpWithUrl:(NSString *)url
{
    NSURL* URL = [NSURL URLWithString:url];
    if(!URL) return;
    NSString* scheme = URL.scheme;
    if(scheme.length == 0) return;
    
    AlertJumpModel* item = [AlertJumpModel new];
    item.scheme = scheme;
    
    DatabaseUnit* unit = [DatabaseUnit addAlertJumpWithItem:item];
    DB_EXEC(unit);
    
    [self reloadData];
}

//删除屏蔽APP跳转
- (void)removeAlertJumpWithId:(NSString *)uuid
{
    if(uuid.length == 0) return;
    
    DatabaseUnit* unit = [DatabaseUnit removeAlertJumpWithId:uuid];
    DB_EXEC(unit);
    
    [self reloadData];
}

@end
