//
//  DMRControl.m
//
//  Created by xj<PERSON><PERSON><PERSON> on 2018/8/30.
//  Copyright © 2018年 xjThree. All rights reserved.
//

#import "DMRControl.h"
#import "PltMediaController.h"
#import <Platinum/Platinum.h>

@interface DMRControl()
{
    PLT_UPnP *_upnp;
    PltMediaController *_controller;
}

@end

@implementation DMRControl

- (instancetype)init
{
    if (self = [super init]) {
        _upnp = new PLT_UPnP();
        PLT_CtrlPointReference ctrlPoint(new PLT_CtrlPoint());
        _upnp->AddCtrlPoint(ctrlPoint);
        _controller = new PltMediaController(ctrlPoint,self);
    }
    return self;
}

- (void)dealloc
{
    delete _upnp;
    delete _controller;
    
    NSLog(@"%d, %s", __LINE__, __func__);
}

- (BOOL)isRunning
{
    return _upnp->IsRunning();
}

- (void)upnpStart
{
    if(!_upnp->IsRunning()){
        _upnp->Start();
    } else {
        [self upnpStop];
        [self upnpStart];
    }
}

- (void)upnpStop
{
    if(_upnp->IsRunning()) {
        _upnp->Stop();
    }
}

- (NSArray<RenderDeviceModel*> *)getActiveRenders
{
    NSMutableArray<RenderDeviceModel *> *renderArray = [NSMutableArray new];
    const PLT_StringMap rendersNameTable = _controller->getMediaRenderersNameTable();
    NPT_List<PLT_StringMapEntry *>::Iterator entry = rendersNameTable.GetEntries().GetFirstItem();
    while (entry) {
        RenderDeviceModel * renderModel = [[RenderDeviceModel alloc] init];
        renderModel.uuid = [NSString stringWithUTF8String:(const char *)(*entry)->GetKey()];
        renderModel.name = [NSString stringWithUTF8String:(const char *)(*entry)->GetValue()];
        
        [renderArray addObject:renderModel];
        ++entry;
    }
    
    return renderArray;
}

- (void)chooseRenderWithUUID:(NSString *)uuid
{
    if (uuid.length > 0) {
        _controller->chooseMediaRenderer([uuid UTF8String]);
    }
}

- (void)renderSetAVTransportWithURI:(NSString *)url mediaInfo:(NSString *)Info
{
    if (url.length > 0) {
        _controller->setRendererAVTransportURI([url UTF8String], [Info UTF8String]);
    }
}

//播放
- (void)play
{
    _controller->setRendererPlay();
}

//停止
- (void)stop
{
    _controller->setRendererStop();
}

//暂停
- (void)pause
{
    _controller->setRendererPause();
}

//跳转到进度
- (void)setSeekTime:(NSInteger)seekTime
{
    NSString *timeString = [self getMMSSFromSS:seekTime];
    const char *cString = (const char *)[timeString UTF8String];
    _controller->setSeekTime(cString);
}

//播放信息
- (void)getInfo
{
    _controller->getTransportInfo();
}

//获取当前播放时间
- (void)getPlayTimeForNow
{
    _controller->getTransportPlayTimeForNow();
}

//获取播放音量
- (void)getVolume
{
    _controller->getRendererVolume();
}

- (void)setVolume:(NSInteger)volume
{
    int newVolume = (int)volume;
    _controller->setRenderVolume(newVolume);
}

//秒转时分秒字符串
-(NSString *)getMMSSFromSS:(NSInteger)totalTime
{
    //format of hour
    NSString *str_hour = [NSString stringWithFormat:@"%02ld",totalTime/3600];
    //format of minute
    NSString *str_minute = [NSString stringWithFormat:@"%02ld",(totalTime%3600)/60];
    //format of second
    NSString *str_second = [NSString stringWithFormat:@"%02ld",totalTime%60];
    //format of time
    NSString *format_time = [NSString stringWithFormat:@"%@:%@:%@",str_hour,str_minute,str_second];
    
    return format_time;
}

@end
