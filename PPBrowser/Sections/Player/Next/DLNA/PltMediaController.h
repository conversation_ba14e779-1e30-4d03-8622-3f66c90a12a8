//
//  PltMediaController.h
//
//  Created by xj<PERSON><PERSON><PERSON> on 2018/8/30.
//  Copyright © 2018年 xjThree-XJ. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <Platinum/PltLeaks.h>
#import <Platinum/PltDownloader.h>
#import <Platinum/Platinum.h>
#import <Platinum/PltMediaServer.h>
#import <Platinum/PltSyncMediaBrowser.h>
#import <Platinum/PltMediaController.h>
#import <Platinum/Neptune.h>

#include <stdio.h>
#include <string.h>
#include <stdlib.h>

#import "DMRControl.h"

typedef NPT_Map<NPT_String, NPT_String>              PLT_StringMap;
typedef NPT_Lock<PLT_StringMap>                      PLT_LockStringMap;
typedef NPT_Map<NPT_String, NPT_String>::Entry       PLT_StringMapEntry;

class PltMediaController : public PLT_SyncMediaBrowser,
public PLT_MediaController,
public PLT_MediaControllerDelegate{
public:
    //构造方法
    PltMediaController(PLT_CtrlPointReference &ctrlPoint,DMRControl *delegateWrapper);
    virtual ~PltMediaController();
    virtual bool OnMRAdded(PLT_DeviceDataReference &device);
    virtual void OnMRRemoved(PLT_DeviceDataReference& device);
    void GetCurMediaRenderer(PLT_DeviceDataReference& renderer);
    const PLT_StringMap getMediaRenderersNameTable();
    void setRendererAVTransportURI(const char *uriStr, const char *metaData);
    void chooseMediaRenderer(NPT_String chosenUUID);
    
    void setRendererPlay();
    void setRendererStop();
    void setRendererPause();
    void getTransportInfo();
    void getTransportPlayTimeForNow();
    void getRendererVolume();
    void setRenderVolume(int volume);
    void setSeekTime(const char* command);
    
    
    void OnGetTransportInfoResult(
                                  NPT_Result               /* res */,
                                  PLT_DeviceDataReference& /* device */,
                                  PLT_TransportInfo*       /* info */,
                                  void*                    /* userdata */) ;
    
    void OnGetPositionInfoResult(
                                 NPT_Result               /* res */,
                                 PLT_DeviceDataReference& /* device */,
                                 PLT_PositionInfo*        /* info */,
                                 void*                    /* userdata */) ;
    
    void OnSeekResult(NPT_Result res,PLT_DeviceDataReference &device,void *userdata);
    
    void OnSetVolumeResult(NPT_Result res,PLT_DeviceDataReference& device,void* userdata);
    void OnGetVolumeResult( NPT_Result res,PLT_DeviceDataReference &device,const char *channel,NPT_UInt32 volume,void *userdata) ;
    
    
    
private:
    DMRControl *_dMRControl;
    NPT_Mutex _curMediaRendererLock;
    PLT_DeviceDataReference _curMediaRenderer;
    NPT_Stack<NPT_String> _curBrowseDirectoryStack;
    NPT_Lock<PLT_DeviceMap> _mediaRenderers;
    NPT_Lock<PLT_DeviceMap> _mediaServers;
    PLT_DeviceDataReference ChooseDevice(const NPT_Lock<PLT_DeviceMap>& deviceList, NPT_String chosenUUID);
};

