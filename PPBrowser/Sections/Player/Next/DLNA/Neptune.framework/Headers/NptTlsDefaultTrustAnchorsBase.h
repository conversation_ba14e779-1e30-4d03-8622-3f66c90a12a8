/*****************************************************************
|
|   Neptune - Trust Anchors
|
|   This file is automatically generated by a script, do not edit!
|
| Copyright (c) 2002-2010, Axiomatic Systems, LLC.
| All rights reserved.
|
| Redistribution and use in source and binary forms, with or without
| modification, are permitted provided that the following conditions are met:
|     * Redistributions of source code must retain the above copyright
|       notice, this list of conditions and the following disclaimer.
|     * Redistributions in binary form must reproduce the above copyright
|       notice, this list of conditions and the following disclaimer in the
|       documentation and/or other materials provided with the distribution.
|     * Neither the name of Axiomatic Systems nor the
|       names of its contributors may be used to endorse or promote products
|       derived from this software without specific prior written permission.
|
| THIS SOFTWARE IS PROVIDED BY AXIOMATIC SYSTEMS ''AS IS'' AND ANY
| EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
| WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
| DISCLAIMED. IN NO EVENT SHALL AXIOMATIC SYSTEMS BE LIABLE FOR ANY
| DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
| (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
| LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
| ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
| (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
| SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
|
 ****************************************************************/

 /* This file is automatically generated by GenTrustAnchorsTables.py, do not edit */

#include "NptTls.h"

extern const NPT_TlsTrustAnchorData NptTlsDefaultTrustAnchorsBase[137];

/* Verisign/RSA Secure Server CA */
extern const unsigned int NptTlsTrustAnchor_Base_0000_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0000_Data[];

/* GTE CyberTrust Root CA */
extern const unsigned int NptTlsTrustAnchor_Base_0001_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0001_Data[];

/* GTE CyberTrust Global Root */
extern const unsigned int NptTlsTrustAnchor_Base_0002_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0002_Data[];

/* Thawte Personal Basic CA */
extern const unsigned int NptTlsTrustAnchor_Base_0003_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0003_Data[];

/* Thawte Personal Premium CA */
extern const unsigned int NptTlsTrustAnchor_Base_0004_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0004_Data[];

/* Thawte Personal Freemail CA */
extern const unsigned int NptTlsTrustAnchor_Base_0005_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0005_Data[];

/* Thawte Server CA */
extern const unsigned int NptTlsTrustAnchor_Base_0006_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0006_Data[];

/* Thawte Premium Server CA */
extern const unsigned int NptTlsTrustAnchor_Base_0007_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0007_Data[];

/* Equifax Secure CA */
extern const unsigned int NptTlsTrustAnchor_Base_0008_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0008_Data[];

/* Digital Signature Trust Co. Global CA 1 */
extern const unsigned int NptTlsTrustAnchor_Base_0009_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0009_Data[];

/* Digital Signature Trust Co. Global CA 3 */
extern const unsigned int NptTlsTrustAnchor_Base_0010_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0010_Data[];

/* Digital Signature Trust Co. Global CA 2 */
extern const unsigned int NptTlsTrustAnchor_Base_0011_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0011_Data[];

/* Digital Signature Trust Co. Global CA 4 */
extern const unsigned int NptTlsTrustAnchor_Base_0012_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0012_Data[];

/* Verisign Class 1 Public Primary Certification Authority */
extern const unsigned int NptTlsTrustAnchor_Base_0013_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0013_Data[];

/* Verisign Class 2 Public Primary Certification Authority */
extern const unsigned int NptTlsTrustAnchor_Base_0014_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0014_Data[];

/* Verisign Class 3 Public Primary Certification Authority */
extern const unsigned int NptTlsTrustAnchor_Base_0015_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0015_Data[];

/* Verisign Class 1 Public Primary Certification Authority - G2 */
extern const unsigned int NptTlsTrustAnchor_Base_0016_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0016_Data[];

/* Verisign Class 2 Public Primary Certification Authority - G2 */
extern const unsigned int NptTlsTrustAnchor_Base_0017_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0017_Data[];

/* Verisign Class 3 Public Primary Certification Authority - G2 */
extern const unsigned int NptTlsTrustAnchor_Base_0018_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0018_Data[];

/* Verisign Class 4 Public Primary Certification Authority - G2 */
extern const unsigned int NptTlsTrustAnchor_Base_0019_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0019_Data[];

/* GlobalSign Root CA */
extern const unsigned int NptTlsTrustAnchor_Base_0020_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0020_Data[];

/* GlobalSign Root CA - R2 */
extern const unsigned int NptTlsTrustAnchor_Base_0021_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0021_Data[];

/* ValiCert Class 1 VA */
extern const unsigned int NptTlsTrustAnchor_Base_0022_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0022_Data[];

/* ValiCert Class 2 VA */
extern const unsigned int NptTlsTrustAnchor_Base_0023_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0023_Data[];

/* RSA Root Certificate 1 */
extern const unsigned int NptTlsTrustAnchor_Base_0024_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0024_Data[];

/* Verisign Class 1 Public Primary Certification Authority - G3 */
extern const unsigned int NptTlsTrustAnchor_Base_0025_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0025_Data[];

/* Verisign Class 2 Public Primary Certification Authority - G3 */
extern const unsigned int NptTlsTrustAnchor_Base_0026_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0026_Data[];

/* Verisign Class 3 Public Primary Certification Authority - G3 */
extern const unsigned int NptTlsTrustAnchor_Base_0027_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0027_Data[];

/* Verisign Class 4 Public Primary Certification Authority - G3 */
extern const unsigned int NptTlsTrustAnchor_Base_0028_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0028_Data[];

/* Entrust.net Secure Server CA */
extern const unsigned int NptTlsTrustAnchor_Base_0029_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0029_Data[];

/* Entrust.net Secure Personal CA */
extern const unsigned int NptTlsTrustAnchor_Base_0030_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0030_Data[];

/* Entrust.net Premium 2048 Secure Server CA */
extern const unsigned int NptTlsTrustAnchor_Base_0031_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0031_Data[];

/* Baltimore CyberTrust Root */
extern const unsigned int NptTlsTrustAnchor_Base_0032_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0032_Data[];

/* Equifax Secure Global eBusiness CA */
extern const unsigned int NptTlsTrustAnchor_Base_0033_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0033_Data[];

/* Equifax Secure eBusiness CA 1 */
extern const unsigned int NptTlsTrustAnchor_Base_0034_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0034_Data[];

/* Equifax Secure eBusiness CA 2 */
extern const unsigned int NptTlsTrustAnchor_Base_0035_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0035_Data[];

/* Visa International Global Root 2 */
extern const unsigned int NptTlsTrustAnchor_Base_0036_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0036_Data[];

/* AddTrust Low-Value Services Root */
extern const unsigned int NptTlsTrustAnchor_Base_0037_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0037_Data[];

/* AddTrust External Root */
extern const unsigned int NptTlsTrustAnchor_Base_0038_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0038_Data[];

/* AddTrust Public Services Root */
extern const unsigned int NptTlsTrustAnchor_Base_0039_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0039_Data[];

/* AddTrust Qualified Certificates Root */
extern const unsigned int NptTlsTrustAnchor_Base_0040_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0040_Data[];

/* Verisign Time Stamping Authority CA */
extern const unsigned int NptTlsTrustAnchor_Base_0041_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0041_Data[];

/* Thawte Time Stamping CA */
extern const unsigned int NptTlsTrustAnchor_Base_0042_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0042_Data[];

/* Entrust.net Global Secure Server CA */
extern const unsigned int NptTlsTrustAnchor_Base_0043_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0043_Data[];

/* Entrust.net Global Secure Personal CA */
extern const unsigned int NptTlsTrustAnchor_Base_0044_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0044_Data[];

/* Entrust Root Certification Authority */
extern const unsigned int NptTlsTrustAnchor_Base_0045_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0045_Data[];

/* AOL Time Warner Root Certification Authority 1 */
extern const unsigned int NptTlsTrustAnchor_Base_0046_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0046_Data[];

/* AOL Time Warner Root Certification Authority 2 */
extern const unsigned int NptTlsTrustAnchor_Base_0047_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0047_Data[];

/* beTRUSTed Root CA */
extern const unsigned int NptTlsTrustAnchor_Base_0048_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0048_Data[];

/* beTRUSTed Root CA-Baltimore Implementation */
extern const unsigned int NptTlsTrustAnchor_Base_0049_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0049_Data[];

/* beTRUSTed Root CA - Entrust Implementation */
extern const unsigned int NptTlsTrustAnchor_Base_0050_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0050_Data[];

/* beTRUSTed Root CA - RSA Implementation */
extern const unsigned int NptTlsTrustAnchor_Base_0051_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0051_Data[];

/* RSA Security 2048 v3 */
extern const unsigned int NptTlsTrustAnchor_Base_0052_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0052_Data[];

/* RSA Security 1024 v3 */
extern const unsigned int NptTlsTrustAnchor_Base_0053_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0053_Data[];

/* GeoTrust Global CA */
extern const unsigned int NptTlsTrustAnchor_Base_0054_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0054_Data[];

/* GeoTrust Global CA 2 */
extern const unsigned int NptTlsTrustAnchor_Base_0055_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0055_Data[];

/* GeoTrust Universal CA */
extern const unsigned int NptTlsTrustAnchor_Base_0056_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0056_Data[];

/* GeoTrust Universal CA 2 */
extern const unsigned int NptTlsTrustAnchor_Base_0057_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0057_Data[];

/* UTN-USER First-Network Applications */
extern const unsigned int NptTlsTrustAnchor_Base_0058_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0058_Data[];

/* America Online Root Certification Authority 1 */
extern const unsigned int NptTlsTrustAnchor_Base_0059_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0059_Data[];

/* America Online Root Certification Authority 2 */
extern const unsigned int NptTlsTrustAnchor_Base_0060_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0060_Data[];

/* Visa eCommerce Root */
extern const unsigned int NptTlsTrustAnchor_Base_0061_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0061_Data[];

/* TC TrustCenter, Germany, Class 2 CA */
extern const unsigned int NptTlsTrustAnchor_Base_0062_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0062_Data[];

/* TC TrustCenter, Germany, Class 3 CA */
extern const unsigned int NptTlsTrustAnchor_Base_0063_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0063_Data[];

/* Certum Root CA */
extern const unsigned int NptTlsTrustAnchor_Base_0064_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0064_Data[];

/* Comodo AAA Services root */
extern const unsigned int NptTlsTrustAnchor_Base_0065_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0065_Data[];

/* Comodo Secure Services root */
extern const unsigned int NptTlsTrustAnchor_Base_0066_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0066_Data[];

/* Comodo Trusted Services root */
extern const unsigned int NptTlsTrustAnchor_Base_0067_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0067_Data[];

/* IPS Chained CAs root */
extern const unsigned int NptTlsTrustAnchor_Base_0068_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0068_Data[];

/* IPS CLASE1 root */
extern const unsigned int NptTlsTrustAnchor_Base_0069_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0069_Data[];

/* IPS CLASE3 root */
extern const unsigned int NptTlsTrustAnchor_Base_0070_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0070_Data[];

/* IPS CLASEA1 root */
extern const unsigned int NptTlsTrustAnchor_Base_0071_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0071_Data[];

/* IPS CLASEA3 root */
extern const unsigned int NptTlsTrustAnchor_Base_0072_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0072_Data[];

/* IPS Servidores root */
extern const unsigned int NptTlsTrustAnchor_Base_0073_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0073_Data[];

/* IPS Timestamping root */
extern const unsigned int NptTlsTrustAnchor_Base_0074_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0074_Data[];

/* QuoVadis Root CA */
extern const unsigned int NptTlsTrustAnchor_Base_0075_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0075_Data[];

/* QuoVadis Root CA 2 */
extern const unsigned int NptTlsTrustAnchor_Base_0076_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0076_Data[];

/* QuoVadis Root CA 3 */
extern const unsigned int NptTlsTrustAnchor_Base_0077_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0077_Data[];

/* Security Communication Root CA */
extern const unsigned int NptTlsTrustAnchor_Base_0078_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0078_Data[];

/* Sonera Class 1 Root CA */
extern const unsigned int NptTlsTrustAnchor_Base_0079_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0079_Data[];

/* Sonera Class 2 Root CA */
extern const unsigned int NptTlsTrustAnchor_Base_0080_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0080_Data[];

/* Staat der Nederlanden Root CA */
extern const unsigned int NptTlsTrustAnchor_Base_0081_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0081_Data[];

/* TDC Internet Root CA */
extern const unsigned int NptTlsTrustAnchor_Base_0082_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0082_Data[];

/* TDC OCES Root CA */
extern const unsigned int NptTlsTrustAnchor_Base_0083_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0083_Data[];

/* UTN DATACorp SGC Root CA */
extern const unsigned int NptTlsTrustAnchor_Base_0084_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0084_Data[];

/* UTN USERFirst Email Root CA */
extern const unsigned int NptTlsTrustAnchor_Base_0085_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0085_Data[];

/* UTN USERFirst Hardware Root CA */
extern const unsigned int NptTlsTrustAnchor_Base_0086_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0086_Data[];

/* UTN USERFirst Object Root CA */
extern const unsigned int NptTlsTrustAnchor_Base_0087_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0087_Data[];

/* Camerfirma Chambers of Commerce Root */
extern const unsigned int NptTlsTrustAnchor_Base_0088_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0088_Data[];

/* Camerfirma Global Chambersign Root */
extern const unsigned int NptTlsTrustAnchor_Base_0089_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0089_Data[];

/* NetLock Qualified (Class QA) Root */
extern const unsigned int NptTlsTrustAnchor_Base_0090_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0090_Data[];

/* NetLock Notary (Class A) Root */
extern const unsigned int NptTlsTrustAnchor_Base_0091_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0091_Data[];

/* NetLock Business (Class B) Root */
extern const unsigned int NptTlsTrustAnchor_Base_0092_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0092_Data[];

/* NetLock Express (Class C) Root */
extern const unsigned int NptTlsTrustAnchor_Base_0093_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0093_Data[];

/* XRamp Global CA Root */
extern const unsigned int NptTlsTrustAnchor_Base_0094_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0094_Data[];

/* Go Daddy Class 2 CA */
extern const unsigned int NptTlsTrustAnchor_Base_0095_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0095_Data[];

/* Starfield Class 2 CA */
extern const unsigned int NptTlsTrustAnchor_Base_0096_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0096_Data[];

/* StartCom Ltd. */
extern const unsigned int NptTlsTrustAnchor_Base_0097_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0097_Data[];

/* StartCom Certification Authority */
extern const unsigned int NptTlsTrustAnchor_Base_0098_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0098_Data[];

/* Firmaprofesional Root CA */
extern const unsigned int NptTlsTrustAnchor_Base_0099_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0099_Data[];

/* Wells Fargo Root CA */
extern const unsigned int NptTlsTrustAnchor_Base_0100_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0100_Data[];

/* Swisscom Root CA 1 */
extern const unsigned int NptTlsTrustAnchor_Base_0101_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0101_Data[];

/* DigiCert Assured ID Root CA */
extern const unsigned int NptTlsTrustAnchor_Base_0102_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0102_Data[];

/* DigiCert Global Root CA */
extern const unsigned int NptTlsTrustAnchor_Base_0103_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0103_Data[];

/* DigiCert High Assurance EV Root CA */
extern const unsigned int NptTlsTrustAnchor_Base_0104_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0104_Data[];

/* Certplus Class 2 Primary CA */
extern const unsigned int NptTlsTrustAnchor_Base_0105_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0105_Data[];

/* DST Root CA X3 */
extern const unsigned int NptTlsTrustAnchor_Base_0106_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0106_Data[];

/* DST ACES CA X6 */
extern const unsigned int NptTlsTrustAnchor_Base_0107_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0107_Data[];

/* SwissSign Platinum CA - G2 */
extern const unsigned int NptTlsTrustAnchor_Base_0108_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0108_Data[];

/* SwissSign Gold CA - G2 */
extern const unsigned int NptTlsTrustAnchor_Base_0109_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0109_Data[];

/* SwissSign Silver CA - G2 */
extern const unsigned int NptTlsTrustAnchor_Base_0110_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0110_Data[];

/* GeoTrust Primary Certification Authority */
extern const unsigned int NptTlsTrustAnchor_Base_0111_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0111_Data[];

/* thawte Primary Root CA */
extern const unsigned int NptTlsTrustAnchor_Base_0112_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0112_Data[];

/* VeriSign Class 3 Public Primary Certification Authority - G5 */
extern const unsigned int NptTlsTrustAnchor_Base_0113_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0113_Data[];

/* SecureTrust CA */
extern const unsigned int NptTlsTrustAnchor_Base_0114_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0114_Data[];

/* Secure Global CA */
extern const unsigned int NptTlsTrustAnchor_Base_0115_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0115_Data[];

/* COMODO Certification Authority */
extern const unsigned int NptTlsTrustAnchor_Base_0116_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0116_Data[];

/* DigiNotar Root CA */
extern const unsigned int NptTlsTrustAnchor_Base_0117_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0117_Data[];

/* Network Solutions Certificate Authority */
extern const unsigned int NptTlsTrustAnchor_Base_0118_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0118_Data[];

/* WellsSecure Public Root Certificate Authority */
extern const unsigned int NptTlsTrustAnchor_Base_0119_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0119_Data[];

/* IGC/A */
extern const unsigned int NptTlsTrustAnchor_Base_0120_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0120_Data[];

/* Security Communication EV RootCA1 */
extern const unsigned int NptTlsTrustAnchor_Base_0121_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0121_Data[];

/* TC TrustCenter Class 2 CA II */
extern const unsigned int NptTlsTrustAnchor_Base_0122_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0122_Data[];

/* TC TrustCenter Class 3 CA II */
extern const unsigned int NptTlsTrustAnchor_Base_0123_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0123_Data[];

/* TC TrustCenter Universal CA I */
extern const unsigned int NptTlsTrustAnchor_Base_0124_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0124_Data[];

/* Deutsche Telekom Root CA 2 */
extern const unsigned int NptTlsTrustAnchor_Base_0125_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0125_Data[];

/* ComSign CA */
extern const unsigned int NptTlsTrustAnchor_Base_0126_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0126_Data[];

/* ComSign Secured CA */
extern const unsigned int NptTlsTrustAnchor_Base_0127_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0127_Data[];

/* Cybertrust Global Root */
extern const unsigned int NptTlsTrustAnchor_Base_0128_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0128_Data[];

/* Buypass Class 2 CA 1 */
extern const unsigned int NptTlsTrustAnchor_Base_0129_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0129_Data[];

/* Buypass Class 3 CA 1 */
extern const unsigned int NptTlsTrustAnchor_Base_0130_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0130_Data[];

/* certSIGN ROOT CA */
extern const unsigned int NptTlsTrustAnchor_Base_0131_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0131_Data[];

/* ApplicationCA - Japanese Government */
extern const unsigned int NptTlsTrustAnchor_Base_0132_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0132_Data[];

/* GeoTrust Primary Certification Authority - G3 */
extern const unsigned int NptTlsTrustAnchor_Base_0133_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0133_Data[];

/* thawte Primary Root CA - G2 */
extern const unsigned int NptTlsTrustAnchor_Base_0134_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0134_Data[];

/* GeoTrust Primary Certification Authority - G2 */
extern const unsigned int NptTlsTrustAnchor_Base_0135_Size;
extern const unsigned char NptTlsTrustAnchor_Base_0135_Data[];

