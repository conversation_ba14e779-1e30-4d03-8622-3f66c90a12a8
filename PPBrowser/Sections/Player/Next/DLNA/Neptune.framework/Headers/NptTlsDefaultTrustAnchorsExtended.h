/*****************************************************************
|
|   Neptune - Trust Anchors
|
|   This file is automatically generated by a script, do not edit!
|
| Copyright (c) 2002-2010, Axiomatic Systems, LLC.
| All rights reserved.
|
| Redistribution and use in source and binary forms, with or without
| modification, are permitted provided that the following conditions are met:
|     * Redistributions of source code must retain the above copyright
|       notice, this list of conditions and the following disclaimer.
|     * Redistributions in binary form must reproduce the above copyright
|       notice, this list of conditions and the following disclaimer in the
|       documentation and/or other materials provided with the distribution.
|     * Neither the name of Axiomatic Systems nor the
|       names of its contributors may be used to endorse or promote products
|       derived from this software without specific prior written permission.
|
| THIS SOFTWARE IS PROVIDED BY AXIOMATIC SYSTEMS ''AS IS'' AND ANY
| EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
| WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
| DISCLAIMED. IN NO EVENT SHALL AXIOMATIC SYSTEMS BE LIABLE FOR ANY
| DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
| (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
| LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
| ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
| (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
| SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
|
 ****************************************************************/

 /* This file is automatically generated by GenTrustAnchorsTables.py, do not edit */

#include "NptTls.h"

extern const NPT_TlsTrustAnchorData NptTlsDefaultTrustAnchorsExtended[14];

/* ABAecom (sub., Am. Bankers Assn.) Root CA */
extern const unsigned int NptTlsTrustAnchor_Extended_0000_Size;
extern const unsigned char NptTlsTrustAnchor_Extended_0000_Data[];

/* Taiwan GRCA */
extern const unsigned int NptTlsTrustAnchor_Extended_0001_Size;
extern const unsigned char NptTlsTrustAnchor_Extended_0001_Data[];

/* TURKTRUST Certificate Services Provider Root 1 */
extern const unsigned int NptTlsTrustAnchor_Extended_0002_Size;
extern const unsigned char NptTlsTrustAnchor_Extended_0002_Data[];

/* TURKTRUST Certificate Services Provider Root 2 */
extern const unsigned int NptTlsTrustAnchor_Extended_0003_Size;
extern const unsigned char NptTlsTrustAnchor_Extended_0003_Data[];

/* OISTE WISeKey Global Root GA CA */
extern const unsigned int NptTlsTrustAnchor_Extended_0004_Size;
extern const unsigned char NptTlsTrustAnchor_Extended_0004_Data[];

/* S-TRUST Authentication and Encryption Root CA 2005 PN */
extern const unsigned int NptTlsTrustAnchor_Extended_0005_Size;
extern const unsigned char NptTlsTrustAnchor_Extended_0005_Data[];

/* Microsec e-Szigno Root CA */
extern const unsigned int NptTlsTrustAnchor_Extended_0006_Size;
extern const unsigned char NptTlsTrustAnchor_Extended_0006_Data[];

/* Certigna */
extern const unsigned int NptTlsTrustAnchor_Extended_0007_Size;
extern const unsigned char NptTlsTrustAnchor_Extended_0007_Data[];

/* AC Ra\xC3\xADz Certic\xC3\xA1mara S.A. */
extern const unsigned int NptTlsTrustAnchor_Extended_0008_Size;
extern const unsigned char NptTlsTrustAnchor_Extended_0008_Data[];

/* ePKI Root Certification Authority */
extern const unsigned int NptTlsTrustAnchor_Extended_0009_Size;
extern const unsigned char NptTlsTrustAnchor_Extended_0009_Data[];

/* TUBITAK UEKAE Kok Sertifika Hizmet Saglayicisi - Surum 3 */
extern const unsigned int NptTlsTrustAnchor_Extended_0010_Size;
extern const unsigned char NptTlsTrustAnchor_Extended_0010_Data[];

/* CNNIC ROOT */
extern const unsigned int NptTlsTrustAnchor_Extended_0011_Size;
extern const unsigned char NptTlsTrustAnchor_Extended_0011_Data[];

/* EBG Elektronik Sertifika Hizmet Saglayicisi */
extern const unsigned int NptTlsTrustAnchor_Extended_0012_Size;
extern const unsigned char NptTlsTrustAnchor_Extended_0012_Data[];

