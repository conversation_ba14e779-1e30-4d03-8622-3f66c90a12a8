//
//  XJ_DMRControl.h
//
//  Created by xj<PERSON>hree on 2018/8/30.
//  Copyright © 2018年 xjThree. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "RenderDeviceModel.h"
#import "WDTransportResponseInfo.h"

@protocol DMRProtocolDelegate <NSObject>
@optional

-(void)onDMRAdded;

-(void)onDMRRemoved;

- (void)getTransportInfoResponse:(WDTransportResponseInfo *)response;

- (void)getTransportPositionInfoResponse:(WDTransportPositionInfo *)response;

- (void)getVolumeResponse:(WDTransportVolumeInfo *)response;

- (void)setVolumeResponse:(WDTransportResponseInfo *)response;

- (void)OnSeekResult:(WDTransportResponseInfo *)response;

@end

@interface DMRControl : NSObject
//属于upnp的方法
- (BOOL)isRunning;

- (void)upnpStart;

- (void)upnpStop;

// 属于PltMediaController的方法
- (NSArray <RenderDeviceModel *> *)getActiveRenders;

- (void)chooseRenderWithUUID:(NSString *)uuid;

- (void)renderSetAVTransportWithURI:(NSString *)url mediaInfo:(NSString *)Info;

- (void)play;

- (void)stop;

- (void)pause;

- (void)getInfo;

- (void)getPlayTimeForNow;

- (void)setSeekTime:(NSInteger)seekTime;

- (void)getVolume;

- (void)setVolume:(NSInteger)volume;

@property (nonatomic,weak) id<DMRProtocolDelegate> delegate;

@end
