//
//  Platinum.h
//  Platinum
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 4/3/15.
//
//

//#include <UIKit/UIKit.h>
//
////! Project version number for Platinum.
//FOUNDATION_EXPORT double PlatinumVersionNumber;
//
////! Project version string for Platinum.
//FOUNDATION_EXPORT const unsigned char PlatinumVersionString[];

#ifndef _PLATINUM_H_
#define _PLATINUM_H_

#include <Platinum/PltUPnP.h>
#include <Platinum/PltCtrlPoint.h>
#include <Platinum/PltDeviceData.h>
#include <Platinum/PltHttpServer.h>
#include <Platinum/PltVersion.h>

#include <Platinum/PltMimeType.h>
#include <Platinum/PltProtocolInfo.h>
#include <Platinum/PltAction.h>
#include <Platinum/PltArgument.h>
#include <Platinum/PltConstants.h>
#include <Platinum/PltCtrlPointTask.h>
#include <Platinum/PltDatagramStream.h>
#include <Platinum/PltDeviceHost.h>
#include <Platinum/PltEvent.h>
#include <Platinum/PltHttp.h>
#include <Platinum/PltHttpClientTask.h>
#include <Platinum/PltHttpServer.h>
#include <Platinum/PltHttpServerTask.h>
#include <Platinum/PltService.h>
#include <Platinum/PltSsdp.h>
#include <Platinum/PltStateVariable.h>
#include <Platinum/PltTaskManager.h>
#include <Platinum/PltThreadTask.h>
#include <Platinum/PltUtilities.h>

#include <Platinum/PltMediaServer.h>
#include <Platinum/PltMediaBrowser.h>
#include <Platinum/PltMediaRenderer.h>
#include <Platinum/PltMediaController.h>
#include <Platinum/PltDidl.h>
#include <Platinum/PltFileMediaServer.h>
#include <Platinum/PltMediaCache.h>
#include <Platinum/PltMediaItem.h>
#include <Platinum/PltSyncMediaBrowser.h>

#include <Platinum/PltXbox360.h>
#include <Platinum/PltMediaConnect.h>

#include <Platinum/PltDownloader.h>
#include <Platinum/PltStreamPump.h>
#include <Platinum/PltFrameBuffer.h>
#include <Platinum/PltFrameServer.h>
#include <Platinum/PltFrameStream.h>
#include <Platinum/PltRingBufferStream.h>

#endif // _PLATINUM_H_
