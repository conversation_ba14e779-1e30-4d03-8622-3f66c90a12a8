//
//  RenderDeviceModel.h
//
//  Created by xj<PERSON><PERSON><PERSON> on 2018/8/31.
//  Copyright © 2018年 xjThree. All rights reserved.
//

#import <Foundation/Foundation.h>

@interface RenderDeviceModel : NSObject

//设备uuid
@property (nonatomic, copy) NSString *uuid;
//设备名称
@property (nonatomic, copy) NSString *name;
//是否是自定义的AirPlay
@property (nonatomic, assign) BOOL isAirPlay;

////生产商
//@property (nonatomic, copy) NSString *XJ_producers;
////型号名
//@property (nonatomic, copy) NSString *XJ_modelname;
////型号编号
//@property (nonatomic, copy) NSString *XJ_modelNumber;
////设备生产串号
//@property (nonatomic, copy) NSString *XJ_productionString;
////设备地址
//@property (nonatomic, copy) NSString *XJ_deviceDescriptionURL;

//辅助字段,主要是卡片化
@property (nonatomic, assign) BOOL isFirstInSection;
@property (nonatomic, assign) BOOL isLastInSection;

@end
