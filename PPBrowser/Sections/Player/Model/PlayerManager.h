//
//  PlayerManager.h
//  PPBrowser
//
//  Created by qingbin on 2023/12/3.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>

#import "PlayModel.h"
#import "PlayerView.h"

@interface PlayerManager : NSObject

+ (instancetype)shareInstance;

/// 进入音频模式
- (void)enterHeadPhoneWithPlayer:(PlayerView *)playerView;

/// 退出音频模式，恢复正常播放
- (void)endHeaderPhonePlay;

/// 当前playerView是否正在音频模式（后台播放）
- (BOOL)isHeadPhonePlaying:(PlayerView *)playerView;

/// 判断是否存在正在进行中的音频模式
- (BOOL)isRunningHeadPhone;

@end

