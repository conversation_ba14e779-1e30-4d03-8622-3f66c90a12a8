//
//  PlayerManager.m
//  PPBrowser
//
//  Created by qingbin on 2023/12/3.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import "PlayerManager.h"

#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "NSObject+Helper.h"
#import "UIColor+Helper.h"

#import "BrowserUtils.h"
#import "TopToolbar.h"
#import "BottomToolbar.h"

#import "BrowserViewController.h"
#import "AppDelegate.h"
#import "MessageItemView.h"

@interface PlayerManager ()

/// 音频模式只能保留一个，如果超过1个则关闭之前的那个
//音频模式(和嗅探按钮大小一致)
@property (nonatomic, strong) UIButton* headPhoneButton;
//当前的播放器
@property (nonatomic, strong) PlayerView* playerView;

@end

@implementation PlayerManager

+ (instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    static PlayerManager* obj;
    dispatch_once(&onceToken, ^{
        obj = [PlayerManager new];
    });
    
    return obj;
}

/// 当前playerView是否正在音频模式（后台播放）
- (BOOL)isHeadPhonePlaying:(PlayerView *)playerView
{    
    return self.playerView == playerView;
}

/// 进入音频模式
- (void)enterHeadPhoneWithPlayer:(PlayerView *)playerView
{
    //防止保活程序的影响
    [[AVAudioSession sharedInstance] setCategory:AVAudioSessionCategoryPlayback error:nil];
    [[AVAudioSession sharedInstance] setActive:YES error:nil];
    
    if(self.playerView) {
        //如果之前已经有音频模式，先关掉
        [self.playerView close];
    }
    
    self.playerView = playerView;
    playerView.hidden = YES;
    
    if(!self.headPhoneButton.superview) {
        UIWindow* window = [NSObject normalWindow];
        [window addSubview:self.headPhoneButton];
    
        float sizeHeight = [MessageItemView sizeHeight];
        
        float rightOffset = iPadValue(40, 20);
        float bottomOffset = [TopToolbar toolbarHeight] + iPadValue(kScreenHeight/6.0, 50) + sizeHeight + iPadValue(40, 60);
        
        [self.headPhoneButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.mas_offset(-rightOffset);
            make.bottom.mas_offset(-bottomOffset);
            make.size.mas_equalTo(sizeHeight);
        }];
    }
    
    self.headPhoneButton.hidden = NO;
}

/// 退出音频模式，恢复正常播放
- (void)endHeaderPhonePlay
{
    if(self.playerView) {
        self.playerView.hidden = NO;
    }
    //删掉引用
    self.playerView = nil;
    
    self.headPhoneButton.hidden = YES;
}

/// 判断是否存在正在进行中的音频模式
- (BOOL)isRunningHeadPhone
{
    return self.playerView != nil;
}

#pragma mark -- 拖动事件
- (void)viewDragged:(UIPanGestureRecognizer *)gestureRecognizer
{
    //该代码段由ChatGPT生成
    UIView *view = gestureRecognizer.view;
        
    // 获取拖动的偏移量
    CGPoint translation = [gestureRecognizer translationInView:view.superview];
    
    // 移动视图
    view.center = CGPointMake(view.center.x + translation.x, view.center.y + translation.y);
    
    // 最后停留的位置
    if (gestureRecognizer.state == UIGestureRecognizerStateEnded) {
        CGRect frame = view.frame;
        frame.origin.y += translation.y;
        
        float sizeHeight = [MessageItemView sizeHeight];
        float rightOffset = iPadValue(30, 20);
        
        float y = MAX(frame.origin.y, sizeHeight/2.0+[TopToolbar toolbarHeight]);
        y = MIN(y, kScreenHeight-sizeHeight/2.0-[BottomToolbar toolbarHeight]);
        
        if(frame.origin.x >= kScreenWidth/2.0) {
            //右边
            view.center = CGPointMake(kScreenWidth-sizeHeight/2-rightOffset, y);
        } else {
            //左边
            view.center = CGPointMake(rightOffset+sizeHeight/2.0, y);
        }
    }
    
    // 重置拖动的偏移量
    [gestureRecognizer setTranslation:CGPointZero inView:view.superview];
}

#pragma mark -- 点击事件

- (void)viewTap:(UITapGestureRecognizer *)gestureRecognizer
{
    [self endHeaderPhonePlay];
}

#pragma mark -- Getters

- (UIButton *)headPhoneButton
{
    if(!_headPhoneButton) {
        _headPhoneButton = [UIButton new];
        _headPhoneButton.backgroundColor = [UIColor colorWithHexString:@"#2D7AFE"];
        _headPhoneButton.layer.cornerRadius = [MessageItemView sizeHeight] / 2.0;
        _headPhoneButton.layer.masksToBounds = YES;
        
        [_headPhoneButton setImage:[UIImage imageNamed:@"player_headphone_icon"] forState:UIControlStateNormal];
        
        UIPanGestureRecognizer* panGesture = [[UIPanGestureRecognizer alloc]initWithTarget:self action:@selector(viewDragged:)];
        [_headPhoneButton addGestureRecognizer:panGesture];
        
        UITapGestureRecognizer* tapGesture = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(viewTap:)];
        [_headPhoneButton addGestureRecognizer:tapGesture];
        
        if([BrowserUtils isiPhone]) {
            //iPhone 50->26
            _headPhoneButton.contentEdgeInsets = UIEdgeInsetsMake(12, 12, 12, 12);
        } else {
            _headPhoneButton.contentEdgeInsets = UIEdgeInsetsMake(12, 12, 12, 12);
        }
    }
    
    return _headPhoneButton;
}

@end
