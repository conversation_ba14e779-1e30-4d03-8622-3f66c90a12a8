//
//  PlayModel.h
//  PPBrowser
//
//  Created by qing<PERSON> on 2022/4/16.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "BaseModel.h"
#import "PPEnums.h"
#import "SnifferModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface PlayModel : BaseModel

@property(nonatomic,strong) NSString* name;

@property(nonatomic,strong) NSString* src;

@property(nonatomic,strong) NSString* pageSrc;

@property(nonatomic,strong) NSString* pageTitle;

@property(nonatomic,assign) float duration;
// v2.6.3 请求头
@property (nonatomic, strong) JsSnifferModel *jsModel;

//辅助
@property(nonatomic,assign) PlayModelItemStatus status;
//网址的url
@property(nonatomic,strong) NSString* originUrl;
//播放时间
@property(nonatomic,assign) double seekTime;

//根据判断得出的最后的标题
- (NSString*)theLastTitle;

//辅助字段
@property (nonatomic, assign) BOOL isFirstInSection;
@property (nonatomic, assign) BOOL isLastInSection;

@end

NS_ASSUME_NONNULL_END
