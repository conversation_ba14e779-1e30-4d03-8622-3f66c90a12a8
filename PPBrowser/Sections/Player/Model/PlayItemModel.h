//
//  PlayItemModel.h
//  PPBrowser
//
//  Created by qingbin on 2022/5/1.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>

@protocol PlayLandScapeDelegate <NSObject>
@end

@protocol PlayPortraitDelegate <NSObject>
@end

@interface PlayItemRateModel : NSObject

@property(nonatomic,assign) float rate;

@end

@interface PlayItemScalingModeModel : NSObject

@property(nonatomic,assign) int scalingMode;

@end

@interface PlayItemModel : NSObject

@property(nonatomic,strong) NSString* title;

@property(nonatomic,assign) BOOL isSelected;

@property(nonatomic,strong) id object;

@property(nonatomic,assign) BOOL orientation;

@end

