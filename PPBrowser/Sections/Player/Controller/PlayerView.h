//
//  PlayerView.h
//  PPBrowser
//
//  Created by qingbin on 2022/4/16.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "ZFPlayerController.h"

@class PlayModel;
@class Tab;

/*
投屏dlna调研:
 https://www.iliunian.cn/14822892841111.html
 https://eliyar.biz/DLNA_with_iOS_Android/
 https://github.com/plutinosoft/Platinum
 
 https://eliyar.biz/DLNA_with_iOS_Android_Part_1_Find_Device_Using_SSDP/
 https://eliyar.biz/DLNA_with_iOS_Android_Part_2_Control_Using_SOAP/
 https://eliyar.biz/DLNA_with_iOS_Android_Part_3_Subscribe_Event/index.html
 
 https://github.com/xjThree/XJDLNAProgram
 投屏最后采用了Platinum的框架, 具体例子来源于XJDLNAProgram;
 但是现在有一个问题,就是iOS14之后, 真机如果要使用udp多播, 那么需要申请权限
 具体参考: https://www.cnblogs.com/chao8888/p/13749383.html
 投屏只能在模拟器使用。因此上架的时候, 需要申请Multicast Networking Entitlement Request权限。
 
 Platinum加入库文件的时候需要选择(embed & sign)
 
 投屏参考:
 http://blog.jokerhub.cn/geek/Develop%20DLNA%20Using%20Platinum%20Library/
 https://github.com/liaogang/iDLNA/
 */

/*
 采用的是swift版的Tiercel3.2.5版本。
 然后通过TiercelObjCBridge桥接。
 Build setting设置swift languager version
 Build setting设置Defines Moudle为YES
 通过项目创建一个swift测试文件, 然后自动生成了PPBrowser-Bridging-Header.h
 在需要引用到swift的地方, 需要引入"PPBrowser-Swift.h"的头文件
 
 //关于iPAD屏幕旋转
参考
 https://mobiarch.wordpress.com/2017/04/22/controlling-screen-orientation-of-ios-apps/
 http://hongchaozhang.github.io/blog/2019/05/21/understand-ios-app-orientations/
 */
@interface PlayerView : UIView

- (void)playWithModel:(PlayModel*)model;

- (void)stop;

// 是否正在播放
- (BOOL)isPlaying;

// 结束播放
- (void)endPlayer;

// 手动关闭
- (void)close;

@property (readonly) ZFPlayerController *player;

@property (readonly) PlayModel* model;
/// 手动关闭播放器
@property (nonatomic, copy) void(^closePlayerBlock)(void);

@end

