//
//  PlayerView.m
//  PPBrowser
//
//  Created by qingbin on 2022/4/16.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "PlayerView.h"

#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "UIView+FrameHelper.h"
#import "NSObject+Helper.h"

#import "ZFPlayer.h"
#import "ZFPlayerControlView.h"
#import "ZFAVPlayerManager.h"
#import "ZFUtilities.h"
#import "ZFPlayerConst.h"

#import "UIImageView+WebCache.h"

#import <AVKit/AVKit.h>
#import <MediaPlayer/MediaPlayer.h>
#import "AppDelegate.h"

#import "PlayModel.h"

#import "DMRControl.h"
#import "DLNADeviceAlertView.h"
#import "PopupBottomView.h"
#import "PopupBottomViewTitleHandler.h"

#import "PPBrowser-Swift.h"

#import "CopyrightHelper.h"
#import "NSURL+Extension.h"
#import "PPNotifications.h"

#import "BrowserViewController.h"
#import "AppDelegate.h"

#import "Tampermonkey.h"

#import "SnifferHelper.h"

#import "PlayerManager.h"

#import "SJRemoteCommandHandler.h"

#import "CommonDataManager.h"
#import "WeakProxy.h"

@interface PlayerView ()<AVPictureInPictureControllerDelegate, DMRProtocolDelegate, DLNADeviceAlertViewDelegate>

@property (nonatomic, strong) ZFPlayerController *player;
@property (nonatomic, strong) UIImageView *containerView;
@property (nonatomic, strong) ZFPlayerControlView *controlView;

@property (nonatomic, strong) PlayModel* model;
@property (nonatomic, strong) UIImage* coverImage;

//画中画
@property (nonatomic, strong) AVPictureInPictureController *pictureInPictureController;
@property (nonatomic, strong) AVPictureInPictureController* pipController;
@property (nonatomic, strong) UIView *avLayerContainer;
@property (nonatomic, assign) BOOL isRunningMiniScreen;
//投屏
@property (nonatomic, strong) DMRControl* dmrController;
@property (nonatomic, strong) PopupBottomView *alertView;
@property (nonatomic, strong) DLNADeviceAlertView* dlnaView;

//记录本地播放时间,关闭窗口的时候才记录
@property (nonatomic, assign) double seekTime;

//列表循环定时器，防止一种情况，就是所有的视频都看完了，然后开启了列表循环模式，那么此时会无限循环中，无法再看视频
//因此搞一个定时器，如果开启了列表循环模式，下个视频至少要播放8秒，否则会重新播放，而不是死循环
@property (nonatomic, strong) NSTimer *repeatTimer;
@property (nonatomic, assign) BOOL isRepeatTimerEnd;

@end

@implementation PlayerView

- (instancetype)init
{
    self = [super init];
    if(self) {
        [self commonInit];
    }
    
    return self;
}

- (void)commonInit
{
    self.coverImage = [ZFUtilities imageWithColor:UIColor.blackColor size:CGSizeMake(1, 1)];
    //初始化
    
    [self setupPlayer];
    [self addSubviews];
    [self defineLayout];
    [self setupObservers];
    
    self.player.viewControllerDisappear = NO;
}

- (void)dealloc
{    
    self.player.viewControllerDisappear = YES;
    
    if(self.dmrController) {
        [self.dmrController upnpStop];
    }
    
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)stop
{
    self.player.viewControllerDisappear = YES;
    
    if(self.dmrController) {
        [self.dmrController upnpStop];
    }
    
    //切换到下一个视频需要重置一下,否则全屏模式有问题
    [self.player stop];
}

#pragma mark -- 手动关闭
- (void)close
{
    self.player.viewControllerDisappear = YES;
    
    [self.player stop];
    
    if(self.dmrController) {
        [self.dmrController upnpStop];
    }
    
    self.model = nil;
    
    if(self.closePlayerBlock) {
        self.closePlayerBlock();
    }
    
    [self removeFromSuperview];
    
    //更新正在播放的状态
    [[CommonDataManager shareInstance] endPlayer:self];
}

- (void)repeatTimerDidEnd
{
    //列表循环定时器
    self.isRepeatTimerEnd = YES;
}

- (void)_handleRepeatTimer
{
    //重置
    [self.repeatTimer invalidate];
    self.repeatTimer = nil;
    self.isRepeatTimerEnd = NO;
    
    PlayModeStatus status = [[PreferenceManager shareInstance].items.playStatus intValue];
    if(status == PlayModeStatusRepeatList) {
        //列表循环
        //开启定时器
        self.repeatTimer = [NSTimer scheduledTimerWithTimeInterval:8
                                                             target:[WeakProxy proxyWithTarget:self]
                                                           selector:@selector(repeatTimerDidEnd)
                                                           userInfo:nil
                                                            repeats:NO];
    }
}

// 播放视频，必须在主线程调用
- (void)playWithModel:(PlayModel*)model
{
    if([[NSThread currentThread] isMainThread]) {
        [self _playWithModel:model];
    } else {
        @weakify(self)
        dispatch_async(dispatch_get_main_queue(), ^{
            @strongify(self)
            [self _playWithModel:model];
        });
    }
}

- (void)_playWithModel:(PlayModel*)model
{
    if([model.src isEqualToString:self.model.src]) return;

    [self _handleRepeatTimer];
        
    //切换到下一个视频需要重置一下,否则全屏模式有问题
    [self.player stop];
    
    //更新正在播放的状态
    [[CommonDataManager shareInstance] addPlayer:self];
        
    self.model = model;
    
    //按钮逻辑
    BOOL isFileUrl = (model.status == PlayModelItemStatusFile);
    // 设置 AVAudioSession
    NSError *error = nil;
    [[AVAudioSession sharedInstance] setCategory:AVAudioSessionCategoryPlayback error:&error];
    [[AVAudioSession sharedInstance] setActive:YES error:&error];
    
    /// 解决横屏播放+播放下个时，如果不保留设置，那么播放界面会出问题(先切换为竖屏，而且zfplayerview不见了)
    /// 主要是ZFOrientationObserver中的fullScreenContainerView引起的
    ZFFullScreenMode fullScreenMode = ZFFullScreenModeAutomatic;
    if(self.player.isFullScreen) {
        fullScreenMode = ZFFullScreenModeLandscape;
    }
    
    if(isFileUrl) {
        self.seekTime = self.model.seekTime;
        
        [self.controlView showTitle:[model theLastTitle] coverImage:self.coverImage fullScreenMode:fullScreenMode];
        
        //本地文件播放
        self.player.assetURLs = @[[NSURL fileURLWithPath:model.src]];
        
        ZFAVPlayerManager *playerManager = (ZFAVPlayerManager*)self.player.currentPlayerManager;
        playerManager.seekTime = self.model.seekTime;
                
        [self.player playTheIndex:0];
    } else {
        model.src = [model.src stringByReplacingOccurrencesOfString:@"|" withString:@""];
        NSURL* url = [NSURL URLWithString:model.src];
        if(!url) {
            [UIView showFailed:NSLocalizedString(@"play.video.url.invalid", nil)];
            return;
        }
        
        [self.controlView showTitle:[model theLastTitle] coverImage:self.coverImage fullScreenMode:fullScreenMode];
        NSURL* originUrl = [NSURL URLWithString:model.originUrl];
                
        self.player.assetURLs = @[url];
                
        @weakify(self)
        [[SnifferHelper shareInstance] asycSnifferWithOriginUrl:originUrl.absoluteString
                                                       videoUrl:url.absoluteString
                                                     completion:^(SnifferModel *header) {
            @strongify(self)
            ZFAVPlayerManager *playerManager = (ZFAVPlayerManager*)self.player.currentPlayerManager;
            playerManager.requestHeader = header.requestHeader.count == 0 ? nil : header.requestHeader;
            [self.player playTheIndex:0];
        }];
    }
}

- (UIView *)hitTest:(CGPoint)point withEvent:(UIEvent *)event
{
    //小屏模式下,也能正常交互
    CGPoint pt = [self convertPoint:point toView:self.controlView];
    if(CGRectContainsPoint(self.controlView.frame, pt)) {
        return [super hitTest:point withEvent:event];
    }
    
    return nil;
}

#pragma mark -- layout
- (void)addSubviews
{
    [self addSubview:self.containerView];
}

- (void)layoutSubviews
{
    [super layoutSubviews];
    
    //横竖屏切换时更新frames
    UIWindow* window = YBIBNormalWindow();
    CGFloat x = 0;
    CGFloat y = window.safeAreaInsets.top;
    CGFloat w = kScreenWidth;
    CGFloat h = w*9/16;
    self.containerView.frame = CGRectMake(x, y, kScreenWidth, h);
}

- (void)defineLayout
{}

- (void)setupObservers
{
    // 进入后台的控制
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(_applicationWillResignActive:)
                                                 name:UIApplicationWillResignActiveNotification
                                               object:nil];
    // 进入前台的控制
    // 往上滑动进入后台, 而不是通过home键进入后台的情况, 再次切换回前台, 不会触发UIApplicationWillEnterForegroundNotification
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(_didBecomeActive:)
                                                 name:UIApplicationDidBecomeActiveNotification
                                               object:nil];
    //监听进入后台通知
    [NSNotificationCenter.defaultCenter addObserver:self
                                           selector:@selector(applicationDidEnterBackground:)
                                               name:UIApplicationDidEnterBackgroundNotification
                                             object:nil];
    
    //循环模式切换
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(_handleRepeatTimer)
                                                 name:kChangePlayStatusNotification
                                               object:nil];
}

#pragma mark -- 音频模式
- (void)applicationDidEnterBackground:(NSNotification *)notification
{
    //暂停播放则直接返回
    if(![self.player.currentPlayerManager isPlaying]) return;
    
    //如果正在播放音频模式
    if([[PlayerManager shareInstance] isHeadPhonePlaying:self]) {
#warning -- 待优化
        //音频进入后台之后，没有继续播放，暂时没找到具体原因，只能通过这种方法继续播放
        //进入后台后，大概在零点几秒的时候卡一下的效果
        //网络播放视频会这样，本地播放视频则不会，和网络缓存有关系
        ZFAVPlayerManager* playManager  = (ZFAVPlayerManager*)self.player.currentPlayerManager;
        [playManager play];

        @weakify(self)
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            @strongify(self)
            if ([UIApplication sharedApplication].applicationState == UIApplicationStateBackground
                || [UIApplication sharedApplication].applicationState == UIApplicationStateInactive) {
                [self.player.currentPlayerManager play];
            }
        });
    }
}

#pragma mark -- 前后台切换
- (void)_applicationWillResignActive:(NSNotification *)notification
{
    //进入后台
    //如果正在播放，那么切换成画中画模式
    //如果正在音频播放，也不暂停
    ZFAVPlayerManager *playerManager = (ZFAVPlayerManager*)self.player.currentPlayerManager;
    if(playerManager && playerManager.isPlaying) {
        if(!self.isRunningMiniScreen && ![[PlayerManager shareInstance] isHeadPhonePlaying:self]) {
            [playerManager pause];
        }
    }
}

- (void)_didBecomeActive:(NSNotification *)notification
{
    //进入前台
}

#pragma mark -- 拖动手势
- (void)handlePanGesture:(UIPanGestureRecognizer*)pan
{
    if(!self.player.isFullScreen) {
        //小屏模式才允许拖动
        CGPoint point = [pan translationInView:self];
        float centerX = kScreenWidth/2.0;
        float centerY = self.containerView.center.y+point.y;
        
        if(centerY <= self.containerView.height/2.0) {
            centerY = self.containerView.height/2.0;
        }
        
        if(centerY >= kScreenHeight-self.containerView.height/2.0) {
            centerY = kScreenHeight-self.containerView.height/2.0;
        }
        self.containerView.center = CGPointMake(centerX, centerY);
        
        [pan setTranslation:CGPointZero inView:self];
    }
}

#pragma mark -- 是否正在播放
- (BOOL)isPlaying
{
    ZFAVPlayerManager *playerManager = (ZFAVPlayerManager*)self.player.currentPlayerManager;
    return playerManager.isPlaying;
}

#pragma mark -- 结束播放
- (void)endPlayer
{
    [self.player stop];
    
    self.model = nil;
    
    if(self.closePlayerBlock) {
        self.closePlayerBlock();
    }
    
    [self removeFromSuperview];
}

#pragma mark -- 播放器相关初始化
- (void)setupPlayer
{
    ZFAVPlayerManager *playerManager = [[ZFAVPlayerManager alloc] init];
    playerManager.shouldAutoPlay = YES;
    
    @weakify(self)
    self.controlView.closePlayerBlock = ^{
        @strongify(self)
        [self close];
    };
    
    //投屏
    self.controlView.castScreenActionBlock = ^{
        @strongify(self)
        if(self.player.isFullScreen) {
            //全屏模式,先转回竖屏
            [self.player enterFullScreen:NO animated:YES completion:^{
                //修复界面错乱的bug
                dispatch_async(dispatch_get_main_queue(), ^{
                    [self setupDLNA];
                });
            }];
        } else {
            [self setupDLNA];
        }
    };
    
    //画中画
    self.controlView.miniScreenActionBlock = ^{
        @strongify(self)
        if(self.player.isFullScreen) {
            //全屏模式,先转回竖屏
            //全屏模式,先转回竖屏
            [self.player enterFullScreen:NO animated:YES completion:^{
                //修复界面错乱的bug
                dispatch_async(dispatch_get_main_queue(), ^{
                    [self startMiniScreen];
                });
            }];
        } else {
            [self startMiniScreen];
        }
    };
    
    //音频模式
    self.controlView.headPhoneActionBlock = ^{
        @strongify(self)
        //切换到音频模式
        if(self.player.isFullScreen) {
            //全屏模式,先转回竖屏
            //全屏模式,先转回竖屏
            @weakify(self)
            [self.player enterFullScreen:NO animated:YES completion:^{
                @strongify(self)
                //修复界面错乱的bug
                @weakify(self)
                dispatch_async(dispatch_get_main_queue(), ^{
                    @strongify(self)
                    [[PlayerManager shareInstance] enterHeadPhoneWithPlayer:self];
                });
            }];
        } else {
            [[PlayerManager shareInstance] enterHeadPhoneWithPlayer:self];
        }
    };
    
    self.controlView.panActionBlock = ^(UIPanGestureRecognizer *gesture) {
        @strongify(self)
        [self handlePanGesture:gesture];
    };
    
    /// 播放器相关
    self.player = [ZFPlayerController playerWithPlayerManager:playerManager containerView:self.containerView];
    self.player.controlView = self.controlView;
    /// 设置退到后台继续播放
    self.player.pauseWhenAppResignActive = NO;
    /// 播放结束不退出全屏
    self.player.exitFullScreenWhenStop = NO;
    
//    self.player.orientationWillChange = ^(ZFPlayerController * _Nonnull player, BOOL isFullScreen) {
//        AppDelegate* app = (AppDelegate*)[[UIApplication sharedApplication] delegate];
//        app.allowOrentitaionRotation = isFullScreen;
//    };
    
    self.player.orientationDidChanged = ^(ZFPlayerController * _Nonnull player, BOOL isFullScreen) {
        @strongify(self)
        //如果不强制刷新, 那么全屏模式锁屏之后, 再次打开屏幕,
        //然后转小屏, 那么不会刷新
        dispatch_async(dispatch_get_main_queue(), ^{
            if(!isFullScreen) {
                if(self.containerView.width != kScreenWidth) {
                    [self layoutSubviews];
                }
            }
        });
    };
    
    /// 播放完成
    self.player.playerDidToEnd = ^(id  _Nonnull asset) {
        //这里不能stop,否则播放结束之后,无法交互
        //stop里面移除了controlView;
//        [self.player stop];
        @strongify(self)
        [self _handleRepeatActionAfterFinish];
    };
    
    /// 播放进度监听，如果是本地文件播放，那么记录播放的时间
    [self.player setPlayerPlayTimeChanged:^(id<ZFPlayerMediaPlayback>  _Nonnull asset, NSTimeInterval currentTime, NSTimeInterval duration) {
        @strongify(self)
        //远程控制
        NSDictionary *info =
        @{MPMediaItemPropertyTitle:self.model.name?:@"",
          MPMediaItemPropertyMediaType:@(MPMediaTypeAny),
          MPNowPlayingInfoPropertyElapsedPlaybackTime:@(currentTime),
          MPMediaItemPropertyPlaybackDuration:@(duration),
          MPNowPlayingInfoPropertyPlaybackRate:@(self.player.currentPlayerManager.rate)};
        [SJRemoteCommandHandler.shared updateNowPlayingInfo:info];
        
//        NSLog(@"当前播放时间: %f", currentTime);
        
        BOOL isFileUrl = (self.model.status == PlayModelItemStatusFile);
        if(!isFileUrl) return;
        self.seekTime = currentTime;
    }];
    
    //远程控制
    [self _setupRemoteCommandHandler];
}

#pragma mark -- 远程控制
- (void)_setupRemoteCommandHandler
{
    //远程控制会受到KeepAliveManager的影响
    //注意下面这行代码:
    //[[AVAudioSession sharedInstance] setCategory:AVAudioSessionCategoryPlayback error:&error];
    
    NSError *error = nil;
    [[AVAudioSession sharedInstance] setCategory:AVAudioSessionCategoryPlayback error:&error];
    [[AVAudioSession sharedInstance] setActive:YES error:&error];
    
    @weakify(self)
    SJRemoteCommandHandler.shared.pauseCommandHandler = ^(id<SJRemoteCommandHandler>  _Nonnull handler) {
        @strongify(self)
        if (!self) return;
        [self.player.currentPlayerManager pause];
    };
    
    SJRemoteCommandHandler.shared.playCommandHandler = ^(id<SJRemoteCommandHandler>  _Nonnull handler) {
        @strongify(self)
        if (!self) return;
        [self.player.currentPlayerManager play];
    };
    
    SJRemoteCommandHandler.shared.forwardCommandHandler = ^(id<SJRemoteCommandHandler>  _Nonnull handler) {
        @strongify(self)
        if (!self) return;
        float currentTime = self.player.currentPlayerManager.currentTime + 10;
        currentTime = MAX(0, currentTime);
        currentTime = MIN(currentTime, self.player.currentPlayerManager.totalTime);
        BOOL isPlaying = [self.player.currentPlayerManager isPlaying];
        @weakify(self)
        [self.player seekToTime:currentTime completionHandler:^(BOOL finished) {
            @strongify(self)
            if(isPlaying) {
                [self.player.currentPlayerManager play];
            }
        }];
    };
    
    SJRemoteCommandHandler.shared.backwardCommandHandler = ^(id<SJRemoteCommandHandler>  _Nonnull handler) {
        @strongify(self)
        if (!self) return;
        float currentTime = self.player.currentPlayerManager.currentTime - 10;
        currentTime = MAX(0, currentTime);
        BOOL isPlaying = [self.player.currentPlayerManager isPlaying];
        @weakify(self)
        [self.player seekToTime:currentTime completionHandler:^(BOOL finished) {
            @strongify(self)
            if(isPlaying) {
                [self.player.currentPlayerManager play];
            }
        }];
    };
    
    SJRemoteCommandHandler.shared.seekToTimeCommandHandler = ^(id<SJRemoteCommandHandler>  _Nonnull handler, NSTimeInterval secs) {
        @strongify(self)
        if (!self) return;
        BOOL isPlaying = [self.player.currentPlayerManager isPlaying];
        @weakify(self)
        [self.player seekToTime:secs completionHandler:^(BOOL finished) {
            @strongify(self)
            if(isPlaying) {
                [self.player.currentPlayerManager play];
            }
        }];
    };
    
    // 允许远程控制事件
    [MPRemoteCommandCenter sharedCommandCenter].playCommand.enabled = YES;
    [MPRemoteCommandCenter sharedCommandCenter].pauseCommand.enabled = YES;
    [MPRemoteCommandCenter sharedCommandCenter].nextTrackCommand.enabled = NO;
    [MPRemoteCommandCenter sharedCommandCenter].previousTrackCommand.enabled = NO;
    [MPRemoteCommandCenter sharedCommandCenter].skipForwardCommand.enabled = YES;
    [MPRemoteCommandCenter sharedCommandCenter].skipBackwardCommand.enabled = YES;
}

#pragma mark -- 处理循环播放逻辑
- (void)_handleRepeatActionAfterFinish
{
    @weakify(self)
    PlayModeStatus status = [[PreferenceManager shareInstance].items.playStatus intValue];
    if(status == PlayModeStatusDefautl) {
        //默认，不做任何处理
    } else if(status == PlayModeStatusRepeatOne) {
        //单曲循环
        [self.player.currentPlayerManager seekToTime:0 completionHandler:^(BOOL finished) {
            @strongify(self)
            [self.player.currentPlayerManager play];
        }];
    } else if(status == PlayModeStatusRepeatList) {
        //循环播放
    }
}

#pragma mark -- 投屏
- (void)setupDLNA
{
    if([self.dmrController isRunning]) {
        [self.dmrController stop];
    }
    self.dmrController = nil;
    
    self.dmrController = [[DMRControl alloc]init];
    self.dmrController.delegate = self;
    
    //弹窗
    [self showSearchDeviceView];
    
    //开始搜索设备
    [self.dmrController upnpStart];
}

- (void)updateDLNAWithDevices:(NSArray*)devices
{
    [self.dlnaView updateWithModel:devices];
    
    [self.dlnaView stopLoading];
}

- (void)showSearchDeviceView
{
    // 展示弹窗    
    DLNADeviceAlertView* dlnaView = [DLNADeviceAlertView new];
    dlnaView.delegate = self;
    
    //先显示AirPlay
    [dlnaView updateWithModel:nil];
    [dlnaView startLoading];
    self.dlnaView = dlnaView;
    
    [dlnaView show];
}

#pragma mark -- 画中画
- (void)startMiniScreen
{
    //参考https://welkinx.com/2021/01/10/pip/
    
    [self.avLayerContainer removeFromSuperview];
    self.avLayerContainer = nil;
    
    NSError *error = nil;
    [[AVAudioSession sharedInstance] setCategory:AVAudioSessionCategoryPlayback error:&error];
    [[AVAudioSession sharedInstance] setActive:YES error:&error];
    
    if(!AVPictureInPictureController.isPictureInPictureSupported || error) return;
    
    self.avLayerContainer = [UIView new];
    UIWindow* window = YBIBNormalWindow();
    [window addSubview:self.avLayerContainer];
    
    ZFAVPlayerManager *playerManager = (ZFAVPlayerManager*)self.player.currentPlayerManager;
    AVPlayerLayer* avLayer = playerManager.avPlayerLayer;
    avLayer.hidden = YES;
    
    //要新建一个layer,如果用原来的layer那么恢复的时候会丢失
    AVPlayerLayer *pipAvlayer = [AVPlayerLayer playerLayerWithPlayer:playerManager.player];
    [self.avLayerContainer.layer addSublayer:pipAvlayer];
    
    self.pipController = [[AVPictureInPictureController alloc]initWithPlayerLayer:avLayer];
    [self.pipController addObserver:self forKeyPath:@"pictureInPicturePossible" options:NSKeyValueObservingOptionNew context:nil];
    self.pipController.delegate = self;
    
    if(self.pipController.pictureInPicturePossible) {
        //第二次再次触发画中画时,pip已经初始化,直接就可以开启
        dispatch_async(dispatch_get_main_queue(), ^{
            [self.pipController startPictureInPicture];
        });
    }
}

- (void)observeValueForKeyPath:(NSString *)keyPath ofObject:(id)object change:(NSDictionary<NSKeyValueChangeKey,id> *)change context:(void *)context
{
    //pip开启时需要监听pictureInPicturePossible状态,才能正常开启
    if (object == self.pipController && [keyPath isEqualToString:@"pictureInPicturePossible"]){
        if (self.pipController.pictureInPicturePossible && !self.pipController.pictureInPictureActive) {
            dispatch_async(dispatch_get_main_queue(), ^{
                [self.pipController startPictureInPicture];
            });
        }
    }
}

#pragma mark -- AVPictureInPictureControllerDelegate
- (void)pictureInPictureControllerWillStartPictureInPicture:(AVPictureInPictureController *)pictureInPictureController
{
    self.isRunningMiniScreen = YES;
    self.hidden = YES;
    
    //如果已经开启了一次画中画, 然后再次开启画中画的时候, 会暂停当前视频
    //所以需要判断一下
    ZFAVPlayerManager *playerManager = (ZFAVPlayerManager*)self.player.currentPlayerManager;
    if(playerManager) {
        [playerManager play];
    }
}

- (void)pictureInPictureControllerWillStopPictureInPicture:(AVPictureInPictureController *)pictureInPictureController
{
    [self.avLayerContainer removeFromSuperview];
    self.avLayerContainer = nil;
    
    ZFAVPlayerManager *playerManager = (ZFAVPlayerManager*)self.player.currentPlayerManager;
    AVPlayerLayer* avLayer = playerManager.avPlayerLayer;
    avLayer.hidden = NO;
    
    self.hidden = NO;
    self.isRunningMiniScreen = NO;
}

#pragma mark -- DLNADeviceAlertViewDelegate
- (void)dlnaDidSelectDevice:(RenderDeviceModel*)device
{
    if(device.isAirPlay) {
        //点击了AirPlay
    } else {
        [self.dmrController chooseRenderWithUUID:device.uuid];
        [self.dmrController renderSetAVTransportWithURI:self.model.src mediaInfo:@""];
        [self.dmrController play];
    }
    
    [self.alertView hide];
}

- (void)dlnaDidCancel
{
    [self.dlnaView stopLoading];
    
    //停止搜索
    [self.dmrController upnpStop];
    [self.alertView hide];
}

- (void)dlnaDidRefresh
{
    [self.dlnaView startLoading];
    
    //刷新
    [self.dmrController upnpStop];
    [self.dmrController upnpStart];
}

#pragma mark -- 投屏回调
-(void)onDMRAdded
{
    //发现设备
    NSArray* devices = [self.dmrController getActiveRenders];
    dispatch_async(dispatch_get_main_queue(), ^{
        [self updateDLNAWithDevices:devices];
    });
}

-(void)onDMRRemoved
{
    //移除设备
    NSArray* devices = [self.dmrController getActiveRenders];
    dispatch_async(dispatch_get_main_queue(), ^{
        [self updateDLNAWithDevices:devices];
    });
}

- (void)getTransportInfoResponse:(WDTransportResponseInfo *)response
{
    //播放状态回调
}

- (void)getTransportPositionInfoResponse:(WDTransportPositionInfo *)response
{
    //播放进度回调
}

- (void)getVolumeResponse:(WDTransportVolumeInfo *)response
{
    //获取音量回调
}

- (void)setVolumeResponse:(WDTransportResponseInfo *)response
{
    //设置音量回调
}

- (void)OnSeekResult:(WDTransportResponseInfo *)response
{
    //拖动进度条回调
}

#pragma mark -- layout init
- (ZFPlayerControlView *)controlView
{
    if (!_controlView) {
        _controlView = [ZFPlayerControlView new];
        _controlView.fastViewAnimated = YES;
        _controlView.autoHiddenTimeInterval = 5;
        _controlView.autoFadeTimeInterval = 0.25;
        _controlView.prepareShowLoading = YES;
        _controlView.prepareShowControlView = NO;
    }
    return _controlView;
}

- (UIImageView *)containerView
{
    if (!_containerView) {
        _containerView = [UIImageView new];
        _containerView.image = self.coverImage;
        _containerView.userInteractionEnabled = YES;
    }
    return _containerView;
}

@end
