//
//  DLNADeviceAlertView.h
//  PPBrowser
//
//  Created by qingbin on 2022/4/20.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "RenderDeviceModel.h"

@protocol DLNADeviceAlertViewDelegate <NSObject>
@optional
- (void)dlnaDidSelectDevice:(RenderDeviceModel*)device;
- (void)dlnaDidCancel;
- (void)dlnaDidRefresh;
@end

@interface DLNADeviceAlertView : UIView

- (void)show;
- (void)close;

- (void)updateWithModel:(NSArray*)model;

- (void)startLoading;
- (void)stopLoading;

@property (nonatomic, weak) id<DLNADeviceAlertViewDelegate> delegate;

@end
