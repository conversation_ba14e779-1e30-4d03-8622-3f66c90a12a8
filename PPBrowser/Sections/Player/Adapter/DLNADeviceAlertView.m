//
//  DLNADeviceAlertView.m
//  PPBrowser
//
//  Created by qingbin on 2022/4/20.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "DLNADeviceAlertView.h"

#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "UIView+FrameHelper.h"
#import "NSObject+Helper.h"

#import <AVKit/AVKit.h>
#import <MediaPlayer/MediaPlayer.h>
#import "BrowserUtils.h"

#import "ThemeProtocol.h"
#import "PPNotifications.h"

@interface DLNADeviceAlertCell : UITableViewCell

@property(nonatomic,strong) RenderDeviceModel* model;

@property(nonatomic,strong) UIView* backView;
@property(nonatomic,strong) UILabel* titleLabel;
@property(nonatomic,strong) MPVolumeView* airPlayView;

@end

@implementation DLNADeviceAlertCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        [self addSubviews];
        [self defineLayout];
        
        self.backgroundColor = UIColor.clearColor;
    }
    
    return self;
}

- (void)updateWithModel:(RenderDeviceModel*)model
{
    self.model = model;
    
    self.titleLabel.text = model.name;
    self.airPlayView.hidden = !model.isAirPlay;
    
    [self updateCornerRadius];
}

- (void)updateCornerRadius
{
    // 圆角
    self.backView.layer.cornerRadius = 10;
    self.backView.layer.masksToBounds = YES;
    
    if(self.model.isFirstInSection && self.model.isLastInSection) {
        //只有一个
        self.backView.layer.masksToBounds = YES;
        self.backView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner | kCALayerMinXMaxYCorner | kCALayerMaxXMaxYCorner;
    } else {
        if(self.model.isFirstInSection) {
            //添加上圆角
            self.backView.layer.masksToBounds = YES;
            self.backView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
        } else if(self.model.isLastInSection) {
            //添加下圆角
            self.backView.layer.masksToBounds = YES;
            self.backView.layer.maskedCorners = kCALayerMinXMaxYCorner | kCALayerMaxXMaxYCorner;
        } else {
            self.backView.layer.masksToBounds = NO;
            self.backView.layer.cornerRadius = 0;
        }
    }
}

- (void)triggerAirPlayAction
{
    if(self.model.isAirPlay) {
        for (UIButton *button in self.airPlayView.subviews) {
            if([button isKindOfClass:UIButton.class]) {
                [button sendActionsForControlEvents:UIControlEventTouchUpInside];
            }
        }
    }
}

- (void)addSubviews
{
    [self.contentView addSubview:self.backView];
    [self.backView addSubview:self.airPlayView];
    [self.backView addSubview:self.titleLabel];
}

- (void)defineLayout
{
    [self.backView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(0);
        make.right.mas_offset(-0);
        make.top.bottom.equalTo(self.contentView);
    }];
    
    float offset = iPadValue(30, 15);
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.left.mas_offset(offset);
        make.right.mas_offset(-offset);
    }];
    
    //白色背景,所有看不到
    [self.airPlayView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.centerY.equalTo(self.contentView);
        make.size.mas_equalTo(50);
    }];
    
    UIView* line = [UIView new];
    line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    [self.contentView addSubview:line];
    [line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.contentView);
        make.left.mas_offset(offset);
        make.right.mas_offset(-offset);
        make.height.mas_equalTo(0.5);
    }];
}

- (UILabel *)titleLabel
{
    if(!_titleLabel) {
        _titleLabel = [UIView createLabelWithTitle:@""
                                         textColor:[UIColor colorWithHexString:@"#333333"]
                                           bgColor:UIColor.clearColor
                                          fontSize:20
                                     textAlignment:NSTextAlignmentLeft
                                             bBold:NO];
    }
    
    return _titleLabel;
}

- (MPVolumeView *)airPlayView
{
    if(!_airPlayView) {
        _airPlayView = [MPVolumeView new];
        _airPlayView.showsVolumeSlider = NO;
        _airPlayView.hidden = YES;
        _airPlayView.backgroundColor = UIColor.whiteColor;//以防万一,还是改成白色背景
    }
    
    return _airPlayView;
}

- (UIView *)backView
{
    if(!_backView) {
        _backView = [UIView new];
        _backView.backgroundColor = UIColor.whiteColor;
    }
    
    return _backView;
}

@end

@interface DLNADeviceAlertView ()<UITableViewDelegate,UITableViewDataSource>

@property (nonatomic, strong) UIView* dialogView;

@property (nonatomic, strong) UILabel *titleLabel;

@property (nonatomic, strong) UIActivityIndicatorView* loadingView;

@property (nonatomic, strong) UIButton *closeButton;

@property (nonatomic, strong) UIView *dialogBackgroundView;

@property (nonatomic, strong) UITableView* tableView;

@property (nonatomic, strong) NSMutableArray* model;

@end

@implementation DLNADeviceAlertView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if(self) {
        [self addSubviews];
        [self defineLayout];
        [self setupObservers];
        
        [self applyTheme];
    }
    
    return self;
}

- (void)startLoading
{
    [self.loadingView startAnimating];
    self.loadingView.hidden = NO;
}

- (void)stopLoading
{
    [self.loadingView stopAnimating];
    self.loadingView.hidden = YES;
}

- (void)updateWithModel:(NSArray*)model
{
    [self.model removeAllObjects];

    RenderDeviceModel* airplayItem = [RenderDeviceModel new];
    airplayItem.isAirPlay = YES;
    airplayItem.name = @"AirPlay";

    [self.model addObject:airplayItem];
    [self.model addObjectsFromArray:model];
    
    for(RenderDeviceModel* obj in self.model) {
        obj.isFirstInSection = NO;
        obj.isLastInSection = NO;
    }
    
    RenderDeviceModel* obj = self.model.firstObject;
    obj.isFirstInSection = YES;
    
    obj = self.model.lastObject;
    obj.isLastInSection = YES;
    
    [self.tableView reloadData];
}

- (void)show
{
    UIWindow* window = [NSObject normalWindow];
    [window endEditing:YES];
    [window addSubview:self];
   
    [self mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_offset(0);
    }];
    
    self.backgroundColor = [UIColor colorWithRed:0 green:0 blue:0 alpha:0];
    self.dialogView.layer.opacity = 0.5f;
    
    [UIView animateWithDuration:0.25
                          delay:0.0
                        options:UIViewAnimationOptionCurveEaseInOut
                     animations:^{
            self.backgroundColor = [UIColor colorWithRed:0x00 / 255.0f green:0x00 / 255.0f blue:0x00 / 255.0f alpha:0.4f];
            self.dialogView.layer.opacity = 1.0f;
        } completion:^(BOOL finished) {

        }
    ];
}

- (void)close
{
    self.dialogView.layer.opacity = 1.0f;
    
    [UIView animateWithDuration:0.25f delay:0.0 options:UIViewAnimationOptionCurveEaseInOut
        animations:^{
            self.backgroundColor = [UIColor colorWithRed:0.0f green:0.0f blue:0.0f alpha:0.0f];
            self.dialogView.layer.opacity = 0.0f;
        } completion:^(BOOL finished) {
            if(self.delegate && [self.delegate respondsToSelector:@selector(dlnaDidCancel)]) {
                [self.delegate dlnaDidCancel];
            }
        
            [self removeFromSuperview];
        }
    ];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.titleLabel.textColor = [UIColor colorWithHexString:@"#ffffff"];
        [self.closeButton setImage:[UIImage imageNamed:@"sheet_close_dark_button"] forState:UIControlStateNormal];
        self.dialogBackgroundView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.dialogView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
    } else {
        self.titleLabel.textColor = [UIColor colorWithHexString:@"#222222"];
        [self.closeButton setImage:[UIImage imageNamed:@"sheet_close_button"] forState:UIControlStateNormal];
        self.dialogBackgroundView.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.dialogView.backgroundColor = UIColor.whiteColor;
    }
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    //只有当前的controller会触发一次, 其它已存在的controller走通知
    if(needReload) {
        //修改暗黑模式
        [self applyTheme];
        
        [self.tableView reloadData];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}

- (void)setupObservers
{
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(darkThemeDidChangeNotification:)
                                                 name:kDarkThemeDidChangeNotification
                                               object:nil];
}

- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event
{
    UITouch *touch = [touches anyObject];
    CGPoint touchPoint = [touch locationInView:self];
    
    if (CGRectContainsPoint(self.dialogView.frame, touchPoint)) {
        // 点击在子视图范围内，不响应
        return;
    } else {
        [self close];
    }

    // 点击不在子视图范围内，执行点击事件
    [super touchesBegan:touches withEvent:event];
}

#pragma mark -- layout
- (void)addSubviews
{
    [self addSubview:self.dialogView];
    [self.dialogView addSubview:self.titleLabel];
    [self.dialogView addSubview:self.loadingView];
    [self.dialogView addSubview:self.closeButton];
    [self.dialogView addSubview:self.dialogBackgroundView];
    [self.dialogBackgroundView addSubview:self.tableView];
}

- (void)defineLayout
{
    float offset = iPadValue(30, 10);
    [self.dialogView mas_makeConstraints:^(MASConstraintMaker *make) {
        if([BrowserUtils isiPhone]) {
            //iPhone
            make.center.mas_offset(0);
            make.height.mas_equalTo(350);
            make.left.mas_offset(offset);
            make.right.mas_offset(-offset);
        } else {
            float maxWidth = MIN(kScreenWidth-2*offset, 768);
            make.center.mas_offset(0);
            make.height.mas_equalTo(350);
            make.width.mas_equalTo(maxWidth);
        }
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_offset(15);
        make.centerX.mas_offset(0);
    }];
    
    [self.loadingView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.closeButton);
        make.left.mas_offset(10);
    }];
    
    [self.closeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_offset(10);
        make.right.mas_offset(-10);
        make.size.mas_equalTo(30);
    }];
    
    [self.dialogBackgroundView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_offset(50);
        make.left.mas_offset(10);
        make.right.mas_offset(-10);
        make.bottom.mas_offset(-10);
    }];
    
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_offset(10);
        make.left.mas_offset(10);
        make.right.mas_offset(-10);
        make.bottom.mas_offset(-10);
    }];
}

#pragma mark - UITableViewDataSource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return self.model.count;
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    RenderDeviceModel* model = self.model[indexPath.row];

    DLNADeviceAlertCell* cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass(DLNADeviceAlertCell.class)];
    [cell updateWithModel:model];
    
    return cell;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    RenderDeviceModel* model = self.model[indexPath.row];
    
    if(model.isAirPlay) {
        DLNADeviceAlertCell* cell = [tableView cellForRowAtIndexPath:indexPath];
        [cell triggerAirPlayAction];
    }

    if(self.delegate && [self.delegate respondsToSelector:@selector(dlnaDidSelectDevice:)]) {
        [self.delegate dlnaDidSelectDevice:self.model[indexPath.row]];
    }
    
    [self close];
}

#pragma mark -- Getters

- (UILabel *)titleLabel
{
    if(!_titleLabel) {
        _titleLabel = [UIView createLabelWithTitle:NSLocalizedString(@"common.video.list.title", nil)
                                         textColor:[UIColor colorWithHexString:@"#222222"]
                                           bgColor:UIColor.clearColor
                                          fontSize:16
                                     textAlignment:NSTextAlignmentCenter
                                             bBold:NO];
    }
    
    return _titleLabel;
}

- (UIButton *)closeButton
{
    if(!_closeButton) {
        _closeButton = [UIButton new];
        [_closeButton setImage:[UIImage imageNamed:@"sheet_close_button"] forState:UIControlStateNormal];
        
        @weakify(self)
        [[_closeButton rac_signalForControlEvents:UIControlEventTouchUpInside]
            subscribeNext:^(id x) {
            @strongify(self)
            [self close];
        }];
    }
    
    return _closeButton;
}

- (UIView *)dialogView
{
    if(!_dialogView) {
        _dialogView = [UIView new];
        _dialogView.layer.cornerRadius = 10;
    
        // 设置阴影颜色
        _dialogView.layer.shadowColor = [UIColor colorWithRed:0 green:0 blue:0 alpha:0.4].CGColor;
        // 设置阴影的偏移量
        _dialogView.layer.shadowOffset = CGSizeMake(0, 5);
        // 设置阴影的不透明度
        _dialogView.layer.shadowOpacity = 1.0;
        // 设置阴影的半径
        _dialogView.layer.shadowRadius = 10;
    }
    
    return _dialogView;
}

- (UIView *)dialogBackgroundView
{
    if(!_dialogBackgroundView) {
        _dialogBackgroundView = [UIView new];
        _dialogBackgroundView.layer.cornerRadius = 10;
    }
    
    return _dialogBackgroundView;
}

- (UITableView *)tableView
{
    if(!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
        
        _tableView.separatorStyle  = UITableViewCellSeparatorStyleNone;
        _tableView.showsHorizontalScrollIndicator = NO;
        _tableView.showsVerticalScrollIndicator   = YES;
        _tableView.delegate   = self;
        _tableView.dataSource = self;
        _tableView.rowHeight = iPadValue(88, 60);
        _tableView.backgroundColor = UIColor.clearColor;
        
        [_tableView registerClass:[DLNADeviceAlertCell class] forCellReuseIdentifier:NSStringFromClass([DLNADeviceAlertCell class])];
        
        float height = iPadValue(30, 10);
        UIWindow* window = [NSObject normalWindow];
        _tableView.tableFooterView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, CGFLOAT_MIN, height+window.safeAreaInsets.bottom)];
        _tableView.tableHeaderView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, CGFLOAT_MIN, height)];
    }
    
    return _tableView;
}

- (NSMutableArray *)model
{
    if(!_model) {
        _model = [NSMutableArray array];
    }
    
    return _model;
}

- (UIActivityIndicatorView *)loadingView
{
    if(!_loadingView) {
        _loadingView = [[UIActivityIndicatorView alloc]initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleMedium];
    }
    
    return _loadingView;
}

@end
