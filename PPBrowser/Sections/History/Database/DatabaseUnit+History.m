//
//  DatabaseUnit+History.m
//  PPBrowser
//
//  Created by qingbin on 2022/4/22.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "DatabaseUnit+History.h"
#import "ReactiveCocoa.h"

#import "FMDatabaseQueue.h"
#import "FMDatabase.h"

#import "HistoryModel.h"
#import "PPEnums.h"
#import "PreferenceManager.h"

@implementation DatabaseUnit (History)

+ (DatabaseUnit*)addHistoryWithItem:(HistoryModel*)item
{
    //https://stackoverflow.com/questions/3634984/insert-if-not-exists-else-update
    DatabaseUnit* unit = [DatabaseUnit new];
    
    //如果当前是无痕模式,那么则不记录历史
    BOOL isPrivate = [[PreferenceManager shareInstance].items.isPrivate boolValue];
    if(isPrivate) return unit;
    
    //必须有id
    if(item.historyId.length == 0) {
        item.historyId = [[NSUUID UUID] UUIDString];
    }
    
    if(item.keyword.length == 0) item.keyword = @"";
    if(item.url.length == 0) item.url = @"";
    if(item.title.length == 0) item.title = @"";
    item.ctime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        
        NSString* command = @"INSERT INTO t_history(historyId, keyword, title, url, searchType, ctime) VALUES (?,?,?,?,?,?)\
        on CONFLICT(historyId) DO UPDATE SET title=excluded.title, url=excluded.url,\
                keyword=excluded.keyword, searchType=excluded.searchType \
        WHERE excluded.historyId=t_history.historyId;\
        ";

        BOOL result = [db executeUpdate:command, item.historyId?:@"", item.keyword?:@"", item.title?:@"", item.url?:@"",  @(item.searchType), item.ctime];
        
        NSNumber* insertId = nil;
        if(result) {
            NSString* command = @"SELECT last_insert_rowid() FROM t_history;";
            FMResultSet* set = [db executeQuery:command];
            
            if([set next]) {
                insertId = [set resultDictionary][@"last_insert_rowid()"];
            }
        }

        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(insertId, result);
            }
        });
    };
    
    return unit;
}

+ (DatabaseUnit*)removeHistoryWithItem:(HistoryModel*)item
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = @"DELETE FROM t_history WHERE historyId=?;";
        BOOL result = [db executeUpdate:command, item.historyId];

        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

//搜索联想页,删除的时候根据名称来删除
+ (DatabaseUnit*)removeHistoryInSearchSuggestionWithItem:(HistoryModel*)item
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = @"DELETE FROM t_history WHERE searchType=? AND keyword=?;";
        BOOL result = [db executeUpdate:command, @(HistorySearchTypeKeyword), item.keyword];

        command = @"DELETE FROM t_history WHERE searchType=? AND url=?;";
        result = [db executeUpdate:command, @(HistorySearchTypeUrl), item.url] && result;
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

+ (DatabaseUnit*)removeAllHistory
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = @"DELETE FROM t_history;";
        
        BOOL result = [db executeUpdate:command];

        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

//+ (DatabaseUnit*)searchWithQuery:(NSString*)query
//                        maxCount:(NSUInteger)maxCount
//{
//    DatabaseUnit* unit = [DatabaseUnit new];
//
//    @weakify(unit)
//    unit.executeBlock = ^(FMDatabase *db) {
//        @strongify(unit)
//        NSString* command = [NSString stringWithFormat:@"SELECT * FROM t_history WHERE title like '%%%@%%' OR url like '%%%@%%' OR keyword like '%%%@%%' ORDER BY ctime DESC LIMIT %lu", query, query, query, (unsigned long)maxCount];
//        FMResultSet* set = [db executeQuery:command];
//        
//        NSMutableArray *array = [[NSMutableArray alloc] init];
//        while([set next]) {
//            HistoryModel* item = [[HistoryModel alloc]initWithDictionary:[set resultDictionary] error:nil];
//            [array addObject:item];
//        }
//        
//        dispatch_async(dispatch_get_main_queue(), ^{
//            if(unit.completeBlock) {
//                unit.completeBlock(array,YES);
//            }
//        });
//    };
//    
//    return unit;
//}

+ (DatabaseUnit*)queryAllHistoryWithMaxCount:(NSUInteger)maxCount
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = [NSString stringWithFormat:@"SELECT * FROM t_history ORDER BY ctime DESC LIMIT %lu",(unsigned long)maxCount];
        FMResultSet* set = [db executeQuery:command];
        
        NSMutableArray *array = [[NSMutableArray alloc] init];
        while([set next]) {
            HistoryModel* item = [[HistoryModel alloc]initWithDictionary:[set resultDictionary] error:nil];
            [array addObject:item];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(array,YES);
            }
        });
    };
    
    return unit;
}

+ (DatabaseUnit*)queryAllHistory
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = [NSString stringWithFormat:@"SELECT * FROM t_history ORDER BY ctime DESC"];
        FMResultSet* set = [db executeQuery:command];
        
        NSMutableArray *array = [[NSMutableArray alloc] init];
        while([set next]) {
            HistoryModel* item = [[HistoryModel alloc]initWithDictionary:[set resultDictionary] error:nil];
            [array addObject:item];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(array,YES);
            }
        });
    };
    
    return unit;
}

// 分页查询
+ (DatabaseUnit*)queryAllHistoryWithCurrentPage:(int)currentPage
                                       pageSize:(int)pageSize
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSInteger offset = (currentPage - 1) * pageSize;
        NSString* command = [NSString stringWithFormat:@"SELECT * FROM t_history ORDER BY ctime DESC LIMIT %ld OFFSET %ld", (long)pageSize, (long)offset];
        FMResultSet* set = [db executeQuery:command];
        
        NSMutableArray *array = [[NSMutableArray alloc] init];
        while([set next]) {
            HistoryModel* item = [[HistoryModel alloc]initWithDictionary:[set resultDictionary] error:nil];
            [array addObject:item];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(array,YES);
            }
        });
    };
    
    return unit;
}

//带搜索词的分页
+ (DatabaseUnit*)queryAllHistoryWithCurrentPage:(int)currentPage
                                       pageSize:(int)pageSize
                                        keyword:(NSString *)keyword
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSInteger offset = (currentPage - 1) * pageSize;
        NSString* command = [NSString stringWithFormat:@"SELECT * FROM t_history WHERE title like '%%%@%%' OR url like '%%%@%%' OR keyword like '%%%@%%' ORDER BY ctime DESC LIMIT %ld OFFSET %ld", keyword?:@"", keyword?:@"", keyword?:@"", (long)pageSize, (long)offset];
        FMResultSet* set = [db executeQuery:command];
        
        NSMutableArray *array = [[NSMutableArray alloc] init];
        while([set next]) {
            HistoryModel* item = [[HistoryModel alloc]initWithDictionary:[set resultDictionary] error:nil];
            [array addObject:item];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(array,YES);
            }
        });
    };
    
    return unit;
}

@end
