//
//  DatabaseUnit+History.h
//  PPBrowser
//
//  Created by qingbin on 2022/4/22.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "DatabaseUnit.h"
#import "PPEnums.h"
#import "HistoryModel.h"

@interface DatabaseUnit (History)

+ (DatabaseUnit*)addHistoryWithItem:(HistoryModel*)item;

+ (DatabaseUnit*)removeHistoryWithItem:(HistoryModel*)item;

+ (DatabaseUnit*)removeAllHistory;

//+ (DatabaseUnit*)searchWithQuery:(NSString*)query
//                        maxCount:(NSUInteger)maxCount;

+ (DatabaseUnit*)queryAllHistory;

+ (DatabaseUnit*)queryAllHistoryWithMaxCount:(NSUInteger)maxCount;

//分页查询(currentPage从1开始)
+ (DatabaseUnit*)queryAllHistoryWithCurrentPage:(int)currentPage
                                       pageSize:(int)pageSize;

//带搜索词的分页
+ (DatabaseUnit*)queryAllHistoryWithCurrentPage:(int)currentPage
                                       pageSize:(int)pageSize
                                        keyword:(NSString *)keyword;

//搜索联想页,删除的时候根据名称来删除
+ (DatabaseUnit*)removeHistoryInSearchSuggestionWithItem:(HistoryModel*)item;

@end
