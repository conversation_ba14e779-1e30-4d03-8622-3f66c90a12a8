//
//  HistoryViewController.m
//  PPBrowser
//
//  Created by qingbin on 2022/4/28.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "HistoryViewController.h"

#import "MaizyHeader.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"

#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "NSObject+Helper.h"
#import "UIView+FrameHelper.h"

#import "HistoryCell.h"
#import "DatabaseUnit+History.h"
#import "CustomTextField.h"

#import "Tab.h"

#import "PPEnums.h"
#import "URIFixup.h"
#import "SearchManager.h"
#import "DateHelper.h"

#import "UITableView+HintMessage.h"

#import "ThemeProtocol.h"
#import "PreferenceManager.h"
#import "PaddingNewLabel.h"
#import "UIImage+Extension.h"
#import "UIScrollView+Refresh.h"
#import "UIAlertController+SafePresentation.h"

@interface HistoryViewController ()<UITextFieldDelegate,UITableViewDelegate,UITableViewDataSource,ThemeProtocol>
//
@property (nonatomic, strong) UIView *searchBarView;
@property (nonatomic, strong) CustomTextField *textField;
//
@property (nonatomic, strong) UITableView* tableView;
//
@property (nonatomic, strong) NSMutableArray* model;

@property (nonatomic, strong) NSArray* historyDates;
//
@property (nonatomic, weak) Tab *tab;
//
@property (nonatomic, strong) UIImageView *deleteImageView;
// 当前页数，从1开始
@property (nonatomic, assign) int currentPage;
// 搜索时当前页数，从1开始
@property (nonatomic, assign) int searchCurrentPage;
// 一次加载的页数，不能太少
@property (nonatomic, assign) int pageSize;
// 原始数据
@property (nonatomic, strong) NSMutableArray *originDatas;
// 搜索原始数据
@property (nonatomic, strong) NSMutableArray *searchOriginDatas;

@end

@implementation HistoryViewController

- (instancetype)initWithTab:(Tab*)tab
{
    self = [super init];
    if(self) {
        self.tab = tab;
    }
    
    return self;
}

- (void)viewDidLoad
{
    [super viewDidLoad];
    
    self.currentPage = 1;
    self.searchCurrentPage = 1;
    self.pageSize = 30;
    
    // 设置导航栏
    [self setupNavigationBar];
    [self setupRefresh];
    
    [self addSubviews];
    [self defineLayout];
    [self setupObservers];
    
    [self requestHistory];
    
    [self applyTheme];
}

#pragma mark -- 设置导航栏
- (void)setupNavigationBar
{
    self.title = NSLocalizedString(@"history.title", nil);
    [self createCustomLeftBarButtonItem];
    
    // 删除按钮
    UIButton *deleteBtn = [UIButton buttonWithType:UIButtonTypeSystem];
    
    self.deleteImageView = [UIImageView new];
    UIImage* image = [UIImage ext_systemImageNamed:@"trash"
                                         pointSize:22
                                        renderMode:UIImageRenderingModeAlwaysTemplate];
    self.deleteImageView.image = [image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
    self.deleteImageView.tintColor = [UIColor colorWithHexString:@"#333333"];
    
    [deleteBtn addSubview:self.deleteImageView];
    [self.deleteImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.mas_offset(0);
        make.size.mas_equalTo(22);
    }];
    [deleteBtn addTarget:self action:@selector(clearAllHistory) forControlEvents:UIControlEventTouchUpInside];
    
    // 设置导航栏右侧按钮
    self.navigationItem.rightBarButtonItem = [[UIBarButtonItem alloc] initWithCustomView:deleteBtn];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.tableView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        
        self.searchBarView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        self.textField.backgroundColor = [UIColor colorWithHexString:kDarkThemeColorTextFiledBackgroundColor];
        self.textField.textColor = UIColor.whiteColor;
        [self.textField updatePlaceHolder:NSLocalizedString(@"textfiled.placeholder", nil) color:[UIColor colorWithHexString:kDarkThemeColorTextFiledContent]];
        
        self.deleteImageView.tintColor = UIColor.whiteColor;
    } else {
        self.view.backgroundColor = UIColor.whiteColor;
        self.tableView.backgroundColor = UIColor.whiteColor;
        
        self.searchBarView.backgroundColor = [UIColor whiteColor];
        self.textField.textColor = [UIColor colorWithHexString:@"#333333"];
        self.searchBarView.backgroundColor = [UIColor whiteColor];
        
        [self.textField setBackgroundColor:[UIColor colorWithHexString:@"#f5f5f5"]];
        [self.textField updatePlaceHolder:NSLocalizedString(@"textfiled.placeholder", nil) color:[UIColor colorWithHexString:@"#999999"]];
        
        self.deleteImageView.tintColor = [UIColor colorWithHexString:@"#333333"];
    }
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    //只有当前的controller会触发一次, 其它已存在的controller走通知
    if(needReload) {
        //修改暗黑模式
        [self applyTheme];
        [self.tableView reloadData];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
    [self.tableView reloadData];
}

#pragma mark - Setup Refresh

- (void)setupRefresh {
    @weakify(self)
    [self.tableView addImmediateFooterWithCallback:^{
        @strongify(self)
        [self _loadMoreData];
    } pageSize:self.pageSize];
}

// 加载下一页数据
- (void)_loadMoreData {
    [self requestHistory];
}

#pragma mark -- layout
- (void)addSubviews
{
    [self.view addSubview:self.searchBarView];
    [self.searchBarView addSubview:self.textField];
    
    [self.view addSubview:self.tableView];
    
    //2.6.8 阿拉伯语布局适配
    [NSObject rtlLayoutSupportWithViews:@[self.textField, self.textField.leftView,
                                          self.searchBarView, self.tableView]];
}

- (void)defineLayout
{
    float offset = iPadValue(30, 15);
    
    [self.searchBarView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view);
        make.left.right.equalTo(self.view);
        make.height.mas_equalTo(60);
    }];
    
    [self.textField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.searchBarView);
        make.left.equalTo(self.searchBarView).offset(offset);
        make.right.equalTo(self.searchBarView).offset(-offset);
        make.height.mas_equalTo(iPadValue(44, 36));
    }];
    
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.searchBarView.mas_bottom);
        make.left.right.bottom.equalTo(self.view);
    }];
}

#pragma mark - 事件处理

- (void)setupObservers
{
    @weakify(self)
    [[[[[self.textField rac_textSignal] skip:1] distinctUntilChanged] throttle:0.2]
    subscribeNext:^(id x) {
        @strongify(self)
        [self searchFromDatabase];
    }];
}

#pragma mark -- 获取历史表数据库联想词
- (void)searchFromDatabase
{
    NSString* text = [self.textField.text stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
    if(text.length == 0
       || [text stringByReplacingOccurrencesOfString:@" " withString:@""].length == 0)
    {
        //默认为退出搜索，显示原始列表
        [self _endSearch];
        return;
    }
    
    //重置搜索
    [self _resetSearch];
    [self requestHistory];
}

- (BOOL)isSearching
{
    NSString* text = [self.textField.text stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
    return text.length > 0;
}

#pragma mark -- 获取历史记录
- (void)requestHistory
{
    if ([self isSearching]) {
        //搜索中
        DatabaseUnit* unit = [DatabaseUnit queryAllHistoryWithCurrentPage:self.searchCurrentPage pageSize:self.pageSize keyword:self.textField.text];
        @weakify(self)
        [unit setCompleteBlock:^(NSArray<HistoryModel *>* result, BOOL success) {
            @strongify(self)
            if(!self) return;
            if(success) {
                [self.model removeAllObjects];
                
                if (self.searchCurrentPage == 1) {
                    [self.searchOriginDatas removeAllObjects];
                }
                [self.searchOriginDatas addObjectsFromArray:result];
                
                [self _filterHistoryModel:self.searchOriginDatas];
                [self showOrHideHintMessage];
                [self.tableView reloadData];
                
                self.searchCurrentPage++;
                [self.tableView endImmediateFooterRefresh:result];
            }
        }];
        
        DB_EXEC(unit);
    } else {
        //正常浏览
        DatabaseUnit* unit = [DatabaseUnit queryAllHistoryWithCurrentPage:self.currentPage pageSize:self.pageSize];
        @weakify(self)
        [unit setCompleteBlock:^(NSArray<HistoryModel *>* result, BOOL success) {
            @strongify(self)
            if(!self) return;
            if(success) {
                [self.model removeAllObjects];
                
                if (self.currentPage == 1) {
                    [self.originDatas removeAllObjects];
                }
                [self.originDatas addObjectsFromArray:result];
                
                [self _filterHistoryModel:self.originDatas];
                [self showOrHideHintMessage];
                [self.tableView reloadData];
                
                self.currentPage++;
                [self.tableView endImmediateFooterRefresh:result];
            }
        }];
        
        DB_EXEC(unit);
    }
}

#pragma mark -- 无数据提醒
- (void)showOrHideHintMessage
{
    if(self.model.count > 0) {
        [self.tableView hideHintMessage];
    } else {
        UIImage* image = [UIImage imageNamed:@"empty_data_logo"];
        [self.tableView showHintMessage:NSLocalizedString(@"tableview.emptyTips", nil)
                                  image:image
                          sectionMargin:iPadValue(150, 100)];
    }
}

#pragma mark -- 清除历史记录
- (void)clearAllHistory
{
    UIAlertController* alertController = [UIAlertController alertControllerWithTitle:NSLocalizedString(@"history.clearAllHistory", nil) message:nil preferredStyle:UIAlertControllerStyleAlert];

    UIAlertAction* action = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.confirm", nil) style:UIAlertActionStyleDestructive handler:^(UIAlertAction * _Nonnull action) {
        DatabaseUnit* unit = [DatabaseUnit removeAllHistory];
        DB_EXEC(unit);
        
        //重置数据
        self.currentPage = 1;
        [self.originDatas removeAllObjects];
        [self.model removeAllObjects];
        [self showOrHideHintMessage];
    
        [self.tableView reloadData];
    }];
    [alertController addAction:action];
    
    action = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.cancel", nil) style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
        [alertController dismissViewControllerAnimated:YES completion:nil];
    }];
    [alertController addAction:action];
    
//    [self presentViewController:alertController animated:YES completion:nil];
    //v2.6.8, 统一present方法，防止崩溃
    [alertController presentSafelyFromViewController:self];
}

- (NSArray*)_filterHistoryModel:(NSArray*)items
{
    //根据时间区分
    NSMutableArray* model = [NSMutableArray array];
    NSMutableDictionary* dict = [NSMutableDictionary dictionary];
    
    NSMutableArray* dates = [NSMutableArray array];
    
    for(HistoryModel* item in items) {
        if(item.title.length == 0) {
            //例如百度搜索过渡页,是没有标题的,那么直接过滤掉
            continue;
        }
        
        NSString* ctime = item.ctime;
        NSInteger miliSeconds = [ctime integerValue];
        
        NSDateFormatter* dateFormatter = [NSDateFormatter new];
        [dateFormatter setDateFormat:NSLocalizedString(@"history.formatter", nil)];
        NSDate* date = [NSDate dateWithTimeIntervalSince1970:miliSeconds];
        NSString* formatter = [dateFormatter stringFromDate:date];
        
        BOOL isToday = [DateHelper isToday:date];
        BOOL isYesterday = [DateHelper isYesterday:date];
        
        NSString* realDate = formatter;
        NSString* week = [DateHelper getWeekDayFromDate:date];
        
        LocalizableOption localOption = [BrowserUtils localizableOption];
        //根据不同的语言，设置不同的搜索引擎(有外国用户反馈这个问题了)
        if(localOption != LocalizableOptionZh_Hans) {
            //只有大陆地区设置
            week = @"";
        }

        if(isToday) {
            NSString* today = NSLocalizedString(@"history.today", nil);
            realDate = [NSString stringWithFormat:@"%@ - %@%@", today, realDate, week];
        } else if(isYesterday) {
            NSString* yesterday = NSLocalizedString(@"history.yesterday", nil);
            realDate = [NSString stringWithFormat:@"%@ - %@%@", yesterday, realDate, week];
        }
        
        if(!dict[realDate]) {
            NSMutableArray* section = [NSMutableArray array];
            [section addObject:item];
            dict[realDate] = section;
            
            [dates addObject:realDate];
        } else {
            NSMutableArray* section = dict[realDate];
            [section addObject:item];
        }
    }
    
    for(NSString* date in dates) {
        NSMutableArray* section = dict[date];
        
        //卡片化
        HistoryModel* firstItem = section.firstObject;
        firstItem.isFirstInSection = YES;
        HistoryModel* lastItem = section.lastObject;
        lastItem.isLastInSection = YES;
        
        [self.model addObject:section];
    }
    
    self.historyDates = dates;
    
    return model;
}

#pragma mark -- UITextFieldDelegate
- (BOOL)textFieldShouldReturn:(UITextField *)textField
{
    //点击搜索按钮
    return YES;
}

- (BOOL)textFieldShouldClear:(UITextField *)textField
{
    return YES;
}

- (BOOL)textFieldShouldBeginEditing:(UITextField *)textField
{
    //进入搜索
    [self _resetSearch];
    
    return YES;
}

- (void)_resetSearch
{
    //重置数据
    self.searchCurrentPage = 1;
    [self.searchOriginDatas removeAllObjects];
    
    //重置没有更多数据状态
    [self.tableView resetImmediateFooterRefreshBMore];
}

- (void)_endSearch
{
    //退出搜索
    self.searchCurrentPage = 1;
    
    //重置没有更多数据状态
    [self.tableView resetImmediateFooterRefreshBMore];
    
    //重置数据
    [self.searchOriginDatas removeAllObjects];
    [self.model removeAllObjects];
    
    [self _filterHistoryModel:self.originDatas];
    [self.tableView reloadData];
}

#pragma mark - UIScrollViewDelegate

- (void)scrollViewWillBeginDragging:(UIScrollView *)scrollView
{
    [self.textField resignFirstResponder];
    [self.view endEditing:YES];
}

#pragma mark - UITableViewDataSource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    NSArray* items = self.model[section];
    return items.count;
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return self.model.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    NSArray* items = self.model[indexPath.section];
    HistoryModel* item = items[indexPath.row];
    HistoryCell *cell =  [tableView dequeueReusableCellWithIdentifier:NSStringFromClass(HistoryCell.class)];
    [cell updateWithModel:item];
    
    @weakify(self)
    [cell setDidTapAction:^(HistoryModel *model){
        @strongify(self)
        [self selectItem:model];
    }];
    
    return cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section
{
    float height = iPadValue(60, 36);
    return height;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section
{
    if (section >= self.historyDates.count) {
        return [UIView new];
    }
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    UIColor* headerColor = [UIColor colorWithHexString:@"#ffffff"];
    UIColor* textColor = [UIColor colorWithHexString:@"#333333"];
    if(isDarkTheme) {
        headerColor = [UIColor colorWithHexString:kDarkThemeColor222];
        textColor = UIColor.whiteColor;
    }
    
    UIView* header = [UIView new];
    header.backgroundColor = headerColor;
    
    PaddingNewLabel* titleLabel = [PaddingNewLabel new];
    titleLabel.text = self.historyDates[section];
    titleLabel.textColor = textColor;
    titleLabel.textAlignment = NSTextAlignmentLeft;
    
    if(isDarkTheme) {
        titleLabel.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
    } else {
        titleLabel.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
    
    titleLabel.edgeInsets = UIEdgeInsetsMake(0, iPadValue(10, 8), 0, 0);
    
    float font = iPadValue(20, 15);
    titleLabel.font = [UIFont systemFontOfSize:font];
    [header addSubview:titleLabel];
    
    float offset = iPadValue(30, 15);
    [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_offset(0);
        make.left.mas_offset(offset);
        make.right.mas_offset(-offset);
        make.height.equalTo(header);
    }];
    
    return header;
}

- (BOOL)tableView:(UITableView *)tableView canEditRowAtIndexPath:(NSIndexPath *)indexPath
{
    return YES;
}

// 只适用于ios11 以及以后版本
- (UISwipeActionsConfiguration *)tableView:(UITableView *)tableView trailingSwipeActionsConfigurationForRowAtIndexPath:(NSIndexPath *)indexPath  API_AVAILABLE(ios(11.0))
{
    // 删除数据
    @weakify(self)
    UIContextualAction *deleteAction = [UIContextualAction contextualActionWithStyle:UIContextualActionStyleDestructive title:NSLocalizedString(@"tableview.delete", nil) handler:^(UIContextualAction * _Nonnull action, __kindof UIView * _Nonnull sourceView, void (^ _Nonnull completionHandler)(BOOL)) {
        @strongify(self)
        [tableView setEditing:NO animated:YES];//退出编辑模式，隐藏左滑菜单
    
        NSArray* items = self.model[indexPath.section];
        HistoryModel* item = items[indexPath.row];
    
        DatabaseUnit* unit = [DatabaseUnit removeHistoryWithItem:item];
        unit.completeBlock = ^(id result, BOOL success) {
            dispatch_async(dispatch_get_main_queue(), ^{
                if(success) {
                    //删除一条数据
                    [tableView beginUpdates];
                    
                    //当section只剩下一个item之后，需要判断
                    NSArray* items = self.model[indexPath.section];
                    
                    //这里的顺序很重要, 先删除model, 再删除ui
                    [self.model removeAllObjects];
                    [self.originDatas removeObject:item];
                    [self.searchOriginDatas removeObject:item];
                    
                    if ([self isSearching]) {
                        //搜索中
                        [self _filterHistoryModel:self.searchOriginDatas];
                    } else {
                        //正常浏览
                        [self _filterHistoryModel:self.originDatas];
                    }

                    if(items.count == 1) {
                        [tableView deleteSections:[NSIndexSet indexSetWithIndex:indexPath.section] withRowAnimation:UITableViewRowAnimationFade];
                    } else {
                        [tableView deleteRowsAtIndexPaths:@[indexPath] withRowAnimation:UITableViewRowAnimationFade];
                    }

                    [tableView endUpdates];
                    
                    [self showOrHideHintMessage];
                }

                completionHandler(YES);
            });
        };

        DB_EXEC(unit);
    }];
    [deleteAction setBackgroundColor:UIColor.redColor];
    
    // 添加
    UISwipeActionsConfiguration *actions = [UISwipeActionsConfiguration configurationWithActions:@[deleteAction]];
    actions.performsFirstActionWithFullSwipe = NO;
    return actions;
}

#pragma mark - UITableViewDelegate
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    [tableView deselectRowAtIndexPath:indexPath animated:NO];
}

- (void)selectItem:(HistoryModel*)item
{
    NSURL* url = [NSURL URLWithString:item.url];
    [self.tab loadRequest:[NSURLRequest requestWithURL:url]];
    
    if(self.navigationController.viewControllers.count > 1) {
        [self.navigationController popToRootViewControllerAnimated:YES];
    } else {
        [self dismissViewControllerAnimated:YES completion:nil];
    }
}

#pragma mark -- lazy init

- (UITableView *)tableView
{
    if(!_tableView)
    {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];

        _tableView.separatorStyle  = UITableViewCellSeparatorStyleNone;
        _tableView.showsHorizontalScrollIndicator = NO;
        _tableView.showsVerticalScrollIndicator   = NO;
        _tableView.delegate   = self;
        _tableView.dataSource = self;
        _tableView.rowHeight = UITableViewAutomaticDimension;
        _tableView.estimatedRowHeight = 80;
        _tableView.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        
        [_tableView registerClass:[HistoryCell class] forCellReuseIdentifier:NSStringFromClass([HistoryCell class])];
        
        _tableView.tableFooterView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, CGFLOAT_MIN, iPadValue(20, 10))];
        
        float height = iPadValue(10, 10);
        UIView* header = [[UIView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, height)];
        header.backgroundColor = UIColor.clearColor;
        
        _tableView.tableHeaderView = header;
        _tableView.sectionFooterHeight = 0.0;
    }
    
    return _tableView;
}

- (NSMutableArray *)model
{
    if(!_model) {
        _model = [NSMutableArray array];
    }
    
    return _model;
}

- (UIView *)searchBarView
{
    if (!_searchBarView) {
        _searchBarView = [UIView new];
        _searchBarView.backgroundColor = [UIColor whiteColor];
    }
    return _searchBarView;
}

- (CustomTextField *)textField
{
    if(!_textField) {
        _textField = [[CustomTextField alloc] init];
        //2.6.8 阿拉伯语布局适配
        _textField.textAlignment = NSTextAlignmentLeft;
        
        _textField.font = [UIFont systemFontOfSize:iPadValue(18, 14)];
        _textField.layer.cornerRadius = iPadValue(10, 8);
        _textField.layer.masksToBounds = YES;
        
        _textField.leftViewMode = UITextFieldViewModeAlways;
        _textField.textColor = [UIColor colorWithHexString:@"#333333"];
        _textField.returnKeyType = UIReturnKeySearch;

        [_textField setBackgroundColor:[UIColor colorWithHexString:@"#f2f2f7"]];
        [_textField updatePlaceHolder:NSLocalizedString(@"history.search.placeholder", nil) color:[UIColor colorWithHexString:@"#999999"]];
        [_textField setClearButtonMode:UITextFieldViewModeAlways];

        [_textField setDelegate:self];

        // 创建搜索图标
        float height = iPadValue(44, 36);
        float searchIconHeight = iPadValue(24, 20);
        float y = (height - searchIconHeight) / 2.0;
        CGRect rect = CGRectMake(0, 0, height, height);
        UIView *leftView = [[UIView alloc] initWithFrame:rect];
        UIImageView *searchIcon = [[UIImageView alloc] initWithFrame:CGRectMake(iPadValue(15, 10), y, searchIconHeight, searchIconHeight)];
        searchIcon.image = [UIImage imageNamed:@"navi_search_icon"];
        searchIcon.tintColor = [UIColor colorWithHexString:@"#999999"];
        searchIcon.contentMode = UIViewContentModeScaleAspectFit;
        [leftView addSubview:searchIcon];
        
        _textField.leftView = leftView;
        _textField.leftViewRect = rect;
    }
    
    return _textField;
}

- (NSMutableArray *)originDatas
{
    if (!_originDatas) {
        _originDatas = [NSMutableArray array];
    }
    
    return _originDatas;
}

- (NSMutableArray *)searchOriginDatas
{
    if (!_searchOriginDatas) {
        _searchOriginDatas = [NSMutableArray array];
    }
    
    return _searchOriginDatas;
}

@end
