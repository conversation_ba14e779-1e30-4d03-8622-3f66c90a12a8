//
//  HistoryViewController.h
//  PPBrowser
//
//  Created by qingbin on 2022/4/28.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "BaseViewController.h"
@class Tab;

/*
相关逻辑:
1、搜索联想页
1) 如果是关键字, 那么只显示关键字, 复制到搜索框的是关键字
2) 如果是网址, 那么显示 网址标题doucument.title + 网址链接,
 复制到搜索框的是网址
 
2、历史页
 1)图标(图标可能是网址的自带图标,如果没有那么则是根据域名首个字母大写生成的图标)
 2)doucument.title
 3)具体的网址(包括搜索引擎+关键字这样的网址)
 
// v2.6.5 用户反馈，数据比较多时会卡顿，必须要分页
*/
@interface HistoryViewController : BaseViewController

- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)new NS_UNAVAILABLE;

- (instancetype)initWithTab:(Tab*)tab;

@end
