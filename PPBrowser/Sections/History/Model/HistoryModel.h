//
//  HistoryModel.h
//  PPBrowser
//
//  Created by qing<PERSON> on 2022/4/22.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "BaseModel.h"
#import "PPEnums.h"

NS_ASSUME_NONNULL_BEGIN

@interface HistoryModel : BaseModel
//id
@property (nonatomic, strong) NSString *historyId;
//网页链接或者搜索引擎+关键字
@property (nonatomic, strong) NSString *url;
//关键字,可能为空
@property (nonatomic, strong) NSString *keyword;
//网页标题
@property (nonatomic, strong) NSString *title;
//生成时间
@property (nonatomic, strong) NSString *ctime;
///1-url是搜索引擎+关键字
///2-是网址url
@property (nonatomic, assign) HistorySearchType searchType;

//辅助字段,主要是卡片化
@property (nonatomic, assign) BOOL isFirstInSection;
@property (nonatomic, assign) BOOL isLastInSection;

@end

NS_ASSUME_NONNULL_END
