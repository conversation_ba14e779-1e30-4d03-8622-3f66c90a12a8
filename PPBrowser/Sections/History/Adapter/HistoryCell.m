//
//  HistoryCell.m
//  PPBrowser
//
//  Created by qingbin on 2022/4/28.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "HistoryCell.h"

#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "UIView+FrameHelper.h"
#import "NSObject+Helper.h"
#import "ThemeProtocol.h"
#import "PreferenceManager.h"
#import "BrowserUtils.h"
#import "NSURL+Extension.h"
#import "CommonGradientView.h"

@interface HistoryCell ()<ThemeProtocol>

@property (nonatomic, strong) HistoryModel* model;

@property (nonatomic, strong) UIView* logoContainer;
//@property (nonatomic, strong) CommonGradientView* logoGradientContainer;
@property (nonatomic, strong) UILabel* logoText;
//
@property (nonatomic, strong) UIStackView *stackView;
@property (nonatomic, strong) UILabel* titleLabel;
@property (nonatomic, strong) UILabel* urlLabel;
@property (nonatomic, strong) UILabel* timeLabel;

@property (nonatomic, strong) UIView* line;

@end

@implementation HistoryCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        self.backgroundColor = [UIColor colorWithHexString:@"#FFFFFF"];
        [self addSubviews];
        [self defineLayout];
        [self setupObservers];
    }
    
    return self;
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        self.titleLabel.textColor = UIColor.whiteColor;
        self.urlLabel.textColor = [UIColor colorWithHexString:@"#999999"];
        self.timeLabel.textColor = [UIColor colorWithHexString:@"#666666"];
        self.line.backgroundColor = [UIColor colorWithHexString:kDarkThemeColorLine];
    } else {
        self.backgroundColor = UIColor.whiteColor;
        self.titleLabel.textColor = [UIColor colorWithHexString:@"#333333"];
        self.urlLabel.textColor = [UIColor colorWithHexString:@"#999999"];
        self.timeLabel.textColor = [UIColor colorWithHexString:@"#999999"];
        self.line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
}

- (void)updateWithModel:(HistoryModel *)model
{
    //可以增加区分, 联想词和url的联想词使用不同的logo
    self.model = model;
    
    self.titleLabel.text = model.title;
    NSURL* URL = [NSURL URLWithString:model.url];
    if (URL) {
        self.urlLabel.text = [URL normalizedHost];
    } else {
        self.urlLabel.text = model.url;
    }

    // 设置时间
    NSString* ctime = model.ctime;
    NSInteger miliSeconds = [ctime integerValue];
    NSDate* date = [NSDate dateWithTimeIntervalSince1970:miliSeconds];
    NSDateFormatter* timeFormatter = [NSDateFormatter new];
    [timeFormatter setDateFormat:@"HH:mm"];
    self.timeLabel.text = [timeFormatter stringFromDate:date];
    
    // 设置网站图标/首字母
    [self setupLogoForModel:model];

    //分割线
    self.line.hidden = model.isLastInSection;
    
    [self applyTheme];
}

- (void)setupLogoForModel:(HistoryModel *)model
{
    NSString *firstChar = @"";
    
    // 设置网站首字母
    if (model.title.length > 0) {
        firstChar = [model.title substringToIndex:1];
    }
    
    self.logoContainer.backgroundColor = [UIColor colorWithURL:model.url];
//    NSArray<NSString *>* gradientColors = [UIColor gradientColorWithURL:model.url];
//    UIColor* fromColor = [UIColor colorWithHexString:gradientColors.firstObject];
//    UIColor* toColor = [UIColor colorWithHexString:gradientColors.lastObject];
//    [self.logoGradientContainer updateFromColor:fromColor toColor:toColor direction:CommonGradientDirectionTopLeftToBottomRight];
    
    self.logoText.text = firstChar;
}

- (void)setupObservers
{
    UITapGestureRecognizer* tap = [[UITapGestureRecognizer alloc]init];
    @weakify(self)
    [tap.rac_gestureSignal subscribeNext:^(id x) {
        @strongify(self)
        if(self.didTapAction) {
            self.didTapAction(self.model);
        }
    }];
    [self.contentView addGestureRecognizer:tap];
}


#pragma mark - layout

- (void)addSubviews
{
    [self.contentView addSubview:self.logoContainer];
    [self.logoContainer addSubview:self.logoText];
    
//    [self.contentView addSubview:self.logoGradientContainer];
//    [self.logoGradientContainer addSubview:self.logoText];
    
    [self.contentView addSubview:self.stackView];
    
    //2.6.8 阿拉伯语布局适配
    [NSObject rtlLayoutSupportWithViews:@[self.stackView]];
}

- (void)defineLayout
{
    float offset = iPadValue(30, 15);
    float size = iPadValue(40, 32);
    [self.logoContainer mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(self.contentView);
        make.left.mas_equalTo(self.contentView).offset(offset);
        make.size.mas_equalTo(size);
    }];
    
    [self.logoText mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.mas_equalTo(self.logoContainer);
    }];
    
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.contentView).offset(iPadValue(15, 10));
        make.left.mas_equalTo(self.logoContainer.mas_right).offset(iPadValue(15, 12));
        make.right.equalTo(self.contentView).offset(-8);
        //决定高度
        make.bottom.mas_equalTo(self.contentView).offset(-iPadValue(15, 10));
    }];
    
    UIView* line = [UIView new];
    line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    [self.contentView addSubview:line];
    [line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.contentView);
        make.left.equalTo(self.logoContainer);
        make.right.mas_equalTo(self.contentView);
        make.height.mas_equalTo(0.5);
    }];
    self.line = line;
}

#pragma mark - getters

- (UIView *)logoContainer
{
    if (!_logoContainer) {
        _logoContainer = [[UIView alloc] init];
        _logoContainer.backgroundColor = [UIColor colorWithHexString:@"#4285F4"];
        _logoContainer.layer.cornerRadius = iPadValue(10, 8.0);
        _logoContainer.clipsToBounds = YES;
    }
    return _logoContainer;
}

//- (CommonGradientView *)logoGradientContainer
//{
//    if (!_logoGradientContainer) {
//        _logoGradientContainer = [[CommonGradientView alloc]init];
//        _logoGradientContainer.layer.cornerRadius = 8.0;
//        _logoGradientContainer.clipsToBounds = YES;
//        
//        // 添加微妙的阴影效果
//        _logoGradientContainer.layer.shadowColor = [UIColor blackColor].CGColor;
//        _logoGradientContainer.layer.shadowOffset = CGSizeMake(0, 1);
//        _logoGradientContainer.layer.shadowOpacity = 0.2;
//        _logoGradientContainer.layer.shadowRadius = 2.0;
//    }
//    return _logoGradientContainer;
//}

- (UILabel *)logoText
{
    if (!_logoText) {
        _logoText = [[UILabel alloc] init];
        _logoText.textColor = [UIColor whiteColor];
        _logoText.font = [UIFont boldSystemFontOfSize:iPadValue(20, 16)];
        _logoText.textAlignment = NSTextAlignmentCenter;
    }
    return _logoText;
}

- (UIStackView *)stackView
{
    if(!_stackView) {
        _stackView = [[UIStackView alloc]initWithArrangedSubviews:@[
            self.titleLabel,
            self.urlLabel,
            self.timeLabel
        ]];
        
        _stackView.axis = UILayoutConstraintAxisVertical;
        _stackView.spacing = iPadValue(5, 3);
        _stackView.alignment = UIStackViewAlignmentLeading;
    }
    
    return _stackView;
}

- (UILabel *)titleLabel
{
    if(!_titleLabel) {
        _titleLabel = [UIView createLabelWithTitle:@""
                                         textColor:[UIColor colorWithHexString:@"#333333"]
                                           bgColor:UIColor.clearColor
                                          fontSize:iPadValue(18, 14)
                                     textAlignment:NSTextAlignmentLeft
                                             bBold:NO];
        _titleLabel.font = [UIFont systemFontOfSize:iPadValue(18, 14) weight:UIFontWeightMedium];
    }
    
    return _titleLabel;
}

- (UILabel *)urlLabel
{
    if(!_urlLabel) {
        float font = iPadValue(16, 12);
        _urlLabel = [UIView createLabelWithTitle:@""
                                         textColor:[UIColor colorWithHexString:@"#999999"]
                                           bgColor:UIColor.clearColor
                                          fontSize:font
                                     textAlignment:NSTextAlignmentLeft
                                             bBold:NO];
    }
    
    return _urlLabel;
}

- (UILabel *)timeLabel
{
    if(!_timeLabel) {
        float font = iPadValue(16, 12);
        _timeLabel = [UIView createLabelWithTitle:@""
                                         textColor:[UIColor colorWithHexString:@"#999999"]
                                           bgColor:UIColor.clearColor
                                          fontSize:font
                                     textAlignment:NSTextAlignmentLeft
                                             bBold:NO];
    }
    
    return _timeLabel;
}

//- (UIButton *)moreButton
//{
//    if (!_moreButton) {
//        _moreButton = [UIButton buttonWithType:UIButtonTypeSystem];
//        [_moreButton setImage:[[UIImage imageNamed:@"history_ellipsis_v_icon"] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate] forState:UIControlStateNormal];
//        _moreButton.tintColor = [UIColor colorWithHexString:@"#999999"];
//    }
//    return _moreButton;
//}

@end
