//
//  UserScriptInputController.m
//  PPBrowser
//
//  Created by qingbin on 2022/12/7.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "UserScriptInputController.h"

#import "MaizyHeader.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"

#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "NSObject+Helper.h"
#import "UIView+FrameHelper.h"

#import "ThemeProtocol.h"
#import "BrowserUtils.h"

@interface UserScriptInputController ()<UITextViewDelegate,UITextPasteDelegate,ThemeProtocol>

@property (nonatomic, strong) UITextView *textView;

@property (nonatomic, strong) NSAttributedString *tips;

@property (nonatomic, strong) UIButton* rightButton;

@end

@implementation UserScriptInputController

- (void)viewDidLoad
{
    [super viewDidLoad];
    self.navigationItem.title = NSLocalizedString(@"userscriptInput.title", nil);
    self.navigationController.navigationBar.backgroundColor = UIColor.whiteColor;
    
    self.tips = [[NSAttributedString alloc]initWithString:NSLocalizedString(@"userscriptInput.title", nil) attributes:@{
        NSForegroundColorAttributeName: [UIColor colorWithHexString:@"#999999"],
        NSFontAttributeName: [UIFont systemFontOfSize:15.0f]
    }];
    
    [self addSubviews];
    [self defineLayout];
    [self setupObservers];
    
    [self createCustomLeftBarButtonItem];
    [self _createCustomRightBarButtonItem];
    
    [self applyTheme];
}

- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event
{
    [self.view endEditing:YES];
}

- (void)dealloc
{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)setupObservers
{
//    [[NSNotificationCenter defaultCenter] addObserver:self
//                                             selector:@selector(_keyboardWillShown:)
//                                                 name:UIKeyboardWillShowNotification
//                                               object:nil];
//
//    [[NSNotificationCenter defaultCenter] addObserver:self
//                                             selector:@selector(_keyboardWillHidden:)
//                                                 name:UIKeyboardWillHideNotification
//                                               object:nil];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        [self.rightButton setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
        
        self.textView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];

    } else {
        self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        [self.rightButton setTitleColor:[UIColor colorWithHexString:@"#2D7AFE"] forState:UIControlStateNormal];
    
        self.textView.backgroundColor = UIColor.whiteColor;
    }
}

#pragma mark -- 导航栏
- (void)_createCustomRightBarButtonItem
{
    UIButton* rightButton = [[UIButton alloc] initWithFrame:CGRectMake(0.0, 0.0f, 44.0f, 44.0)];
    [rightButton setBackgroundColor:[UIColor clearColor]];

    rightButton.titleLabel.font = [UIFont systemFontOfSize:16.0];
    [rightButton setTitle:NSLocalizedString(@"userscriptEdit.save.confirm", nil) forState:UIControlStateNormal];
    [rightButton setTitleColor:[UIColor colorWithHexString:@"#2D7AFE"] forState:UIControlStateNormal];
    
    [rightButton addTarget:self action:@selector(rightBarbuttonClick) forControlEvents:UIControlEventTouchUpInside];
    self.rightButton = rightButton;
    
    UIBarButtonItem* barItem = [[UIBarButtonItem alloc] initWithCustomView:rightButton];

    UIBarButtonItem *spacer = [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemFixedSpace target:nil action:nil];
    spacer.width = -16.0f; // for example shift right bar button to the right

    self.navigationItem.rightBarButtonItems = @[spacer, barItem];
}

- (void)rightBarbuttonClick
{
    //保存
    NSString* text = self.textView.text;
    text = [text stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
    if([text isEqualToString:self.tips.string]) {
        text = nil;
    }
    
    if(self.didPasteUrlAction) {
        self.didPasteUrlAction(text);
    }
    
    [self dismissViewControllerAnimated:YES completion:nil];
}

#pragma mark -- UITextPasteDelegate
//会导致复制了两份一样的
//参考https://stackoverflow.com/questions/47227011/uitextview-paste-action-inserts-empty-string-on-ios-11
//bug:直接添加脚本 -- 填写标题 -- 粘贴代码,崩溃
//Set a Hook to know there's a paste action while I'm the first responder - fired when Paste was tapped
//- (void)textPasteConfigurationSupporting:(id<UITextPasteConfigurationSupporting>)textPasteConfigurationSupporting transformPasteItem:(id<UITextPasteItem>)item API_AVAILABLE(ios(11.0)) {
//    [self paste:textPasteConfigurationSupporting];
//}
//
////generic paste action handler
//- (void)paste:(id)sender {
//    UIPasteboard *pasteBoard = [UIPasteboard generalPasteboard];
//    if ([pasteBoard hasStrings]) {
//        id<UITextPasteConfigurationSupporting> pasteItem = (id<UITextPasteConfigurationSupporting>)sender;
//        if ([pasteItem isKindOfClass:[UITextView class]]) {
//            UITextView* myTextView = (UITextView*)pasteItem;
//                [myTextView insertText:[pasteBoard string]];
//        }
//    }
//}

#pragma mark -- UITextViewDelegate
- (BOOL)textViewShouldBeginEditing:(UITextView *)textView
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    
    if(isDarkTheme) {
        self.textView.textColor = UIColor.whiteColor;
    } else {
        self.textView.textColor = [UIColor colorWithHexString:@"#333333"];
    }
    
    if([self.textView.text isEqualToString:self.tips.string]) {
        self.textView.text = @"";
    }
    
    return YES;
}

- (void)textViewDidEndEditing:(UITextView *)textView
{
    if(textView.text.length <= 0) {
        textView.attributedText = self.tips;
        textView.textColor = [UIColor colorWithHexString:@"#999999"];
    }
}

#pragma mark -- layout
- (void)addSubviews
{
    [self.view addSubview:self.textView];
}

- (void)defineLayout
{
    float offset = iPadValue(30, 15);
    [self.textView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(20);
        make.left.equalTo(self.view).offset(offset);
        make.right.equalTo(self.view).offset(-offset);
        make.height.mas_equalTo(iPadValue(200, 150));
    }];
}

- (UITextView *)textView
{
    if(!_textView) {
        _textView = [UITextView new];
//        _textView.contentInset = UIEdgeInsetsMake(10, 15, 10, 15);
//        _textView.layer.borderColor = [UIColor colorWithHexString:@"#e5e5e5"].CGColor;
//        _textView.layer.borderWidth = 0.5f;
        _textView.layer.cornerRadius = 10;
        _textView.layer.masksToBounds = YES;
        
        _textView.textColor = [UIColor colorWithHexString:@"#333333"];
        _textView.backgroundColor = UIColor.whiteColor;
        
        float font = iPadValue(20, 15);
        _textView.font = [UIFont systemFontOfSize:font];
        
        _textView.delegate = self;
        _textView.pasteDelegate = self;
        
        //会干扰textContainerInset
//        _textView.textContainer.lineFragmentPadding = 0;
        _textView.attributedText = self.tips;
    }
    
    return _textView;
}

@end
