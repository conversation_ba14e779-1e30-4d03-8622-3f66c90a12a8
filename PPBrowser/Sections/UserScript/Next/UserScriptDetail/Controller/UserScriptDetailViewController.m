//
//  UserScriptDetailViewController.m
//  PPBrowser
//
//  Created by qingbin on 2024/11/2.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import "UserScriptDetailViewController.h"

#import "MaizyHeader.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"

#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "NSObject+Helper.h"
#import "UIView+FrameHelper.h"

#import "UIImageView+WebCache.h"
#import "UIImage+Extension.h"

#import "UserScriptDetailHeader.h"
#import "SettingSwitchView.h"
#import "UserScriptDetailTextView.h"

#import "DatabaseUnit+UserScript.h"
#import "Tampermonkey.h"

#import "BrowserUtils.h"
#import "SettingSegmentView.h"

#import "XLScrollView.h"
#import "SettingSwitchView.h"

#import "UserScriptUpdateManager.h"
#import "BaseNavigationController.h"

#import "PPNotifications.h"
#import "UserScriptEditController.h"

#import "PaymentManager.h"
#import "VIPController.h"
#import "UIAlertController+SafePresentation.h"

@interface UserScriptDetailViewController ()<UIScrollViewDelegate>

@property (nonatomic, strong) UserScript* userScript;

@property (nonatomic, strong) UIImageView* rightImageView;

@property (nonatomic, strong) XLScrollView* scrollView;
@property (nonatomic, strong) UIView* contentView;

@property (nonatomic, strong) UIStackView *stackView;

@property (nonatomic, strong) UserScriptDetailHeader *header;
//自动更新
@property (nonatomic, strong) SettingSwitchView *updateView;
//是否运行在mainFrame
@property (nonatomic, strong) SettingSegmentView *onlyMainFrameView;
//执行白名单
@property (nonatomic, strong) UIView *whiteListView;
//执行黑名单
@property (nonatomic, strong) UIView *blackListView;

//分割线
@property (nonatomic, strong) UIView *blank1View;
//分割线
@property (nonatomic, strong) UIView *blank2View;

@end

@implementation UserScriptDetailViewController

- (instancetype)initWithModel:(UserScript *)userScript
{
    self = [super init];
    if(self) {
        self.userScript = userScript;
    }
    
    return self;
}

- (void)viewDidLoad
{
    [super viewDidLoad];

    self.title = NSLocalizedString(@"script.detail.title", nil);
    self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    
    [self addSubviews];
    [self defineLayout];
    [self setupObservers];
    [self updateWithModel];

    [self createCustomLeftBarButtonItem];
    [self _createCustomRightBarButtonItem];
    
    [self applyTheme];
}

- (void)updateWithModel
{
    for(UIView* view in self.stackView.arrangedSubviews) {
        [self.stackView removeArrangedSubview:view];
        [view removeFromSuperview];
    }
    
    //头部
    //如果没有logo则用默认logo
    self.header = [UserScriptDetailHeader new];
    
    @weakify(self)
    [self.header setCodeEditAction:^{
        @strongify(self)
        [self handleCodeEdit];
    }];
    
    [self.header updateWithModel:self.userScript];
    [self.stackView addArrangedSubview:self.header];
    
    //分割线
    [self.stackView addArrangedSubview:self.blank1View];
    
    //自动更新
    [self.stackView addArrangedSubview:self.updateView];
    BOOL isAutoUpdate = self.userScript.isAutoUpdate;
    [self.updateView updateWithTitle:NSLocalizedString(@"script.detail.auto.update", nil) isOn:isAutoUpdate];
    
    [self.blank1View mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(self.stackView);
        make.height.mas_equalTo(iPadValue(15, 10));
    }];
    
    //执行范围: default/mainFrame/allFrame
    [self.stackView addArrangedSubview:self.onlyMainFrameView];
    [self.onlyMainFrameView updateWithSelectIndex:(int)self.userScript.frameOption];
    
    //分割线
    [self.stackView addArrangedSubview:self.blank2View];
    [self.blank2View mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(self.stackView);
        make.height.mas_equalTo(iPadValue(15, 10));
    }];
    
    NSMutableArray* data = [NSMutableArray array];
    NSMutableArray<UserScriptDetailTextSectionModel*>* sectionData = [NSMutableArray array];
    
    //白名单
    NSMutableArray* whiteListArray = [NSMutableArray array];
    NSArray* whiteList = [self.userScript.whiteList componentsSeparatedByString:@","];
    for(NSString* str in whiteList) {
        if(str.length > 0) {
            [whiteListArray addObject:str];
        }
    }
    UserScriptDetailTextSectionModel* sectionItem = [UserScriptDetailTextSectionModel new];
    sectionItem.title = NSLocalizedString(@"script.detail.whitelist", nil);
    sectionItem.showAddButton = YES;
    [sectionItem setAddAction:^{
        @strongify(self)
        BOOL isVip = [VIPController checkIsVipWithMessage:NSLocalizedString(@"vip.alert.script.text", nil) controller:self];
        if(isVip) {
            //添加白名单
            [self _addWhiteList];
        }
    }];
    [sectionData addObject:sectionItem];

    [data addObject:whiteListArray];
    
    //黑名单
    NSMutableArray* blackListArray = [NSMutableArray array];
    NSArray* blackList = [self.userScript.blackList componentsSeparatedByString:@","];
    for(NSString* str in blackList) {
        if(str.length > 0) {
            [blackListArray addObject:str];
        }
    }
    sectionItem = [UserScriptDetailTextSectionModel new];
    sectionItem.title = NSLocalizedString(@"script.detail.blacklist", nil);
    sectionItem.showAddButton = YES;
    [sectionItem setAddAction:^{
        @strongify(self)
        BOOL isVip = [VIPController checkIsVipWithMessage:NSLocalizedString(@"vip.alert.script.text", nil) controller:self];
        if(isVip) {
            //添加黑名单
            [self _addBlackList];
        }
    }];
    [sectionData addObject:sectionItem];
    
    [data addObject:blackListArray];
    
    //matches
    NSMutableArray* matches = [NSMutableArray array];
    [matches addObjectsFromArray:self.userScript.matches];
    [matches addObjectsFromArray:self.userScript.includes];
    if(matches.count > 0) {
        UserScriptDetailTextSectionModel* sectionItem = [UserScriptDetailTextSectionModel new];
        sectionItem.title = @"Matches";
        [sectionData addObject:sectionItem];
        
        [data addObject:matches];
    }
    
    //Excludes
    if(self.userScript.excludes.count > 0) {
        UserScriptDetailTextSectionModel* sectionItem = [UserScriptDetailTextSectionModel new];
        sectionItem.title = @"Excludes";
        [sectionData addObject:sectionItem];
        
        NSMutableArray* items = [NSMutableArray array];
        [items addObjectsFromArray:self.userScript.excludes];
        [data addObject:items];
    }
    
    //Requires
    if(self.userScript.requireUrls.count > 0) {
        UserScriptDetailTextSectionModel* sectionItem = [UserScriptDetailTextSectionModel new];
        sectionItem.title = @"Requires";
        [sectionData addObject:sectionItem];
        
        NSMutableArray* items = [NSMutableArray array];
        [items addObjectsFromArray:self.userScript.requireUrls];
        [data addObject:items];
    }
    
    //Resources
    if(self.userScript.requireUrls.count > 0) { 
        NSMutableArray* items = [NSMutableArray array];
        [self.userScript.resourceUrls enumerateKeysAndObjectsUsingBlock:^(id  _Nonnull key, NSString*  _Nonnull obj, BOOL * _Nonnull stop) {
            [items addObject:obj];
        }];
        //有数据才添加
        if(items.count > 0) {
            UserScriptDetailTextSectionModel* sectionItem = [UserScriptDetailTextSectionModel new];
            sectionItem.title = @"Resources";
            [sectionData addObject:sectionItem];
            
            [data addObject:items];
        }
    }
    
    //grants
    if(self.userScript.grants.count > 0) {
        UserScriptDetailTextSectionModel* sectionItem = [UserScriptDetailTextSectionModel new];
        sectionItem.title = @"Grants";
        [sectionData addObject:sectionItem];
        
        NSMutableArray* items = [NSMutableArray array];
        [items addObjectsFromArray:self.userScript.grants];
        [data addObject:items];
    }
    
    //updateUrl
    if(self.userScript.updateUrl.length > 0
       || self.userScript.downloadUrl.length > 0) {
        NSMutableArray* items = [NSMutableArray array];
//        if(self.userScript.updateUrl.length > 0) {
//            [items addObject:self.userScript.updateUrl];
//        }
        if(self.userScript.downloadUrl.length > 0) {
            [items addObject:self.userScript.downloadUrl];
        }
        [data addObject:items];
        
        UserScriptDetailTextSectionModel* sectionItem = [UserScriptDetailTextSectionModel new];
        sectionItem.title = @"UpdateUrls";
        [sectionData addObject:sectionItem];
    }
    
    if(data.count > 0) {
        UserScriptDetailTextView* textView = [UserScriptDetailTextView new];
        [textView updateWithModel:data sectionArray:sectionData userScript:self.userScript];
    
        [self.stackView addArrangedSubview:textView];
    }
}

#pragma mark - 编辑脚本代码

- (void)handleCodeEdit
{
    UserScriptEditController* vc = [[UserScriptEditController alloc]initWithUserScript:self.userScript];
    [self.navigationController pushViewController:vc animated:YES];
}

#pragma mark - 添加白名单

- (void)_addWhiteList
{
    // 创建UIAlertController
    UIAlertController *alertController = [UIAlertController alertControllerWithTitle:@""
                                                                             message:NSLocalizedString(@"script.add.whitelist.url.title", nil)
                                                                      preferredStyle:UIAlertControllerStyleAlert];

    // 添加链接输入框
    [alertController addTextFieldWithConfigurationHandler:^(UITextField * _Nonnull textField) {
        textField.placeholder = NSLocalizedString(@"script.add.whitelist.url.title", nil);
        textField.text = @"";  // 可以设置默认值
    }];

    // 添加"确定"按钮
    @weakify(self)
    UIAlertAction *confirmAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.confirm", nil)
                                                            style:UIAlertActionStyleDefault
                                                          handler:^(UIAlertAction * _Nonnull action) {
        @strongify(self)
        // 获取输入框的值
        NSString *link = alertController.textFields[0].text;
        link = [link stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceCharacterSet]];
        if(link.length == 0) {
            [UIView showToast:NSLocalizedString(@"adblock.enter.link", nil)];
            return;
        }
        
        [self.userScript addWhiteListItem:link];
        
        [self updateWithModel];
    }];

    // 添加"取消"按钮
    UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.cancel", nil)
                                                           style:UIAlertActionStyleCancel
                                                         handler:nil];

    // 将操作按钮添加到UIAlertController
    [alertController addAction:confirmAction];
    [alertController addAction:cancelAction];

    // 显示UIAlertController
//    [self presentViewController:alertController animated:YES completion:nil];
    //v2.6.8, 统一present方法，防止崩溃
    [alertController presentSafelyFromViewController:self];
}

#pragma mark - 添加黑名单

- (void)_addBlackList
{
    // 创建UIAlertController
    UIAlertController *alertController = [UIAlertController alertControllerWithTitle:@""
                                                                             message:NSLocalizedString(@"script.add.blacklist.url.title", nil)
                                                                      preferredStyle:UIAlertControllerStyleAlert];

    // 添加链接输入框
    [alertController addTextFieldWithConfigurationHandler:^(UITextField * _Nonnull textField) {
        textField.placeholder = NSLocalizedString(@"script.add.blacklist.url.title", nil);
        textField.text = @"";  // 可以设置默认值
    }];

    // 添加"确定"按钮
    @weakify(self)
    UIAlertAction *confirmAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.confirm", nil)
                                                            style:UIAlertActionStyleDefault
                                                          handler:^(UIAlertAction * _Nonnull action) {
        @strongify(self)
        // 获取输入框的值
        NSString *link = alertController.textFields[0].text;
        link = [link stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceCharacterSet]];
        if(link.length == 0) {
            [UIView showToast:NSLocalizedString(@"adblock.enter.link", nil)];
            return;
        }
        
        [self.userScript addBlackListItem:link];
        
        [self updateWithModel];
    }];

    // 添加"取消"按钮
    UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.cancel", nil)
                                                           style:UIAlertActionStyleCancel
                                                         handler:nil];

    // 将操作按钮添加到UIAlertController
    [alertController addAction:confirmAction];
    [alertController addAction:cancelAction];

    // 显示UIAlertController
//    [self presentViewController:alertController animated:YES completion:nil];
    //v2.6.8, 统一present方法，防止崩溃
    [alertController presentSafelyFromViewController:self];
}

#pragma mark - handle events

- (void)setupObservers
{
    @weakify(self)
    [self.updateView setDidSwithAction:^(BOOL isOn) {
        @strongify(self)        
        BOOL isVip = [VIPController checkIsVipWithMessage:NSLocalizedString(@"vip.alert.script.text", nil) controller:self];
        if(!isVip) {
            self.updateView.switchView.on = NO;
            return;
        }
        
        self.userScript.isAutoUpdate = isOn;
        
        DatabaseUnit* unit = [DatabaseUnit updateUserScriptWithId:self.userScript.uuid
                                                     isAutoUpdate:isOn
                                                           script:self.userScript];
        DB_EXEC(unit);
    }];
    
    [self.onlyMainFrameView setSelectIndexBlock:^(int index) {
        @strongify(self)
        self.userScript.frameOption = index;
        
        DatabaseUnit* unit = [DatabaseUnit updateUserScriptWithId:self.userScript.uuid
                                                      frameOption:index
                                                           script:self.userScript];
        DB_EXEC(unit);
        
        //发送通知
        [[NSNotificationCenter defaultCenter] postNotificationName:kReloadUserScriptNotification object:nil];
    }];
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(handleEditCodeResult:) name:kReloadUserScriptNotification object:nil];
}

- (void)handleEditCodeResult:(NSNotification *)notification
{
    //如果不是从脚本编辑返回的，那么则无需关心这个通知
    if(notification.object == nil) return;
    
    self.userScript = notification.object;
    [self updateWithModel];
}

- (void)dealloc
{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.rightImageView.tintColor = UIColor.whiteColor;
    } else {
//        self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.view.backgroundColor = UIColor.whiteColor;
        self.blank1View.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.blank2View.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.rightImageView.tintColor = [UIColor colorWithHexString:@"#222222"];
    }
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    //只有当前的controller会触发一次, 其它已存在的controller走通知
    if(needReload) {
        //修改暗黑模式
        [self applyTheme];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}

#pragma mark -- 导航栏
- (void)_createCustomRightBarButtonItem
{
    UIButton* rightButton = [[UIButton alloc] initWithFrame:CGRectMake(0.0, 0.0f, 44.0f, 44.0)];
    [rightButton setBackgroundColor:[UIColor clearColor]];

    UIImageView* imageView = [UIImageView new];

    UIImage* image = [UIImage ext_systemImageNamed:@"ellipsis.circle"
                                         pointSize:22
                                        renderMode:UIImageRenderingModeAlwaysTemplate];
    imageView.image = image;
    
    self.rightImageView = imageView;
    
    [rightButton addSubview:imageView];
    [imageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(rightButton);
    }];
    
    [rightButton addTarget:self action:@selector(rightBarbuttonClick:) forControlEvents:UIControlEventTouchUpInside];
    UIBarButtonItem* barItem = [[UIBarButtonItem alloc] initWithCustomView:rightButton];

    UIBarButtonItem *spacer = [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemFixedSpace target:nil action:nil];
    spacer.width = -16.0f; // for example shift right bar button to the right

    self.navigationItem.rightBarButtonItems = @[spacer, barItem];
}

- (void)rightBarbuttonClick:(id)sender
{
    UIAlertController* alertController = [UIAlertController alertControllerWithTitle:nil message:nil preferredStyle:UIAlertControllerStyleActionSheet];

//    UIPopoverPresentationController* popover = alertController.popoverPresentationController;
//    if([BrowserUtils isiPad]) {
//        //iPad
////        UIBarButtonItem* barButton = [[UIBarButtonItem alloc]initWithCustomView:sender];
////        popover.barButtonItem = barButton;
//    } else {
//        //iPhone
//        popover.barButtonItem = sender;
//    }
    
    UIAlertAction *action = [UIAlertAction actionWithTitle:NSLocalizedString(@"script.detail.share", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [self shareToFriends];
    }];
    [alertController addAction:action];

    @weakify(self)
    action = [UIAlertAction actionWithTitle:NSLocalizedString(@"script.detail.copy", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        @strongify(self)
        //复制链接
        UIPasteboard *pasteboard = [UIPasteboard generalPasteboard];
        NSString* downloadUrl = [self.userScript getUpdateUrl];
        if(downloadUrl.length > 0) {
            pasteboard.string = downloadUrl;
            [UIView showSucceed:NSLocalizedString(@"common.copy.success", nil)];
        } else {
            [UIView showFailed:NSLocalizedString(@"common.copy.fail", nil)];
        }
    }];
    [alertController addAction:action];
    
    action = [UIAlertAction actionWithTitle:NSLocalizedString(@"script.detail.update", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        @strongify(self)
        //更新脚本
        [UIView showLoading:NSLocalizedString(@"tips.updating", nil)];
        [[UserScriptUpdateManager shareInstance] updateWithUserScript:self.userScript completion:^(BOOL needUpdate, UserScript* script){
            @strongify(self)
            if(!self) return;
            if(script) {
                self.userScript = script;
                [self updateWithModel];
            }
            
            [UIView hideHud:YES];
        }];
        
         
    }];
    [alertController addAction:action];
    
    action = [UIAlertAction actionWithTitle:NSLocalizedString(@"script.detail.delete", nil) style:UIAlertActionStyleDestructive handler:^(UIAlertAction * _Nonnull action) {
        @strongify(self)
        //删除数据库数据+暂停脚本
        @weakify(self)
        [[Tampermonkey shareInstance] removeJSWithUserScript:self.userScript completionBlock:^{
            @strongify(self)
            [self.navigationController popViewControllerAnimated:YES];
        }];
    }];
    [alertController addAction:action];

    action = [UIAlertAction actionWithTitle:NSLocalizedString(@"common.cancel", nil) style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
        [alertController dismissViewControllerAnimated:YES completion:nil];
    }];
    [alertController addAction:action];

//    if([BrowserUtils isiPad]) {
//        //适配iPad
//        UIPopoverPresentationController* popover = alertController.popoverPresentationController;
//        if(popover) {
//            UIView* button = sender ?: self.rightImageView;
//            popover.sourceView = button;
//            
//            float width = CGRectGetWidth(button.frame);
//            float height = CGRectGetHeight(button.frame);
//            popover.sourceRect = CGRectMake(width/2.0,height/2.0+height, 0, 0); //指向的位置
//            popover.permittedArrowDirections = UIPopoverArrowDirectionAny;
//        }
//    }
    
//    [self presentViewController:alertController animated:YES completion:nil];
    //v2.6.8, 统一present方法，防止崩溃
    if([BrowserUtils isiPad]) {
        //iPad
        UIView* button = sender ?: self.rightImageView;
        float width = CGRectGetWidth(button.frame);
        float height = CGRectGetHeight(button.frame);
        CGRect sourceRect = CGRectMake(width/2.0,height, 1, 1); //指向的位置
        [alertController presentSafelyFromViewController:self sourceView:button sourceRect:sourceRect];
    } else {
        //iPhone
        [alertController presentSafelyFromViewController:self barButtonItem:sender];
    }
}

- (void)shareToFriends
{
    NSString* textToShare = self.userScript.name ?:NSLocalizedString(@"script.detail.share", nil);
    
    NSFileManager* fileManager = [NSFileManager defaultManager];
    NSString *tmpFileName = [NSString stringWithFormat:@"tmp/%@.js",textToShare];
    NSString *path = [NSHomeDirectory() stringByAppendingPathComponent:tmpFileName];
    if([fileManager fileExistsAtPath:path]) {
        [fileManager removeItemAtPath:path error:nil];
    }
    [self.userScript.content writeToFile:path atomically:YES encoding:NSUTF8StringEncoding error:nil];
    NSURL* urlToShare = [NSURL fileURLWithPath:path];
    
    NSArray* items = @[urlToShare];
    
    UIActivityViewController *vc = [[UIActivityViewController alloc] initWithActivityItems:items applicationActivities:nil];
    vc.completionWithItemsHandler = ^(UIActivityType __nullable activityType, BOOL completed, NSArray * __nullable returnedItems, NSError * __nullable activityError) {
        if([fileManager fileExistsAtPath:path]) {
            [fileManager removeItemAtPath:path error:nil];
        }
    };
    
    if([BrowserUtils isiPad]) {
        //iPad
        vc.popoverPresentationController.sourceView = self.view;
        vc.popoverPresentationController.sourceRect = CGRectMake(CGRectGetMidX(self.view.frame), CGRectGetMidY(self.view.frame), 0, 0);
    }
    
    [self presentViewController:vc animated:YES completion:nil];
}

#pragma mark -- UIScrollViewDelegate
//- (void)scrollViewWillEndDragging:(UIScrollView *)scrollView
//                     withVelocity:(CGPoint)velocity
//              targetContentOffset:(inout CGPoint *)targetContentOffset
//{
//    if(velocity.y > 0) {
//        //往下滑动
//        self.enterEditView.alpha = 0;
//    } else if(velocity.y < 0) {
//        //往上滑动
//        self.enterEditView.alpha = 1;
//    }
//}
//
//- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView
//{
//    //停止滑动
//    self.enterEditView.alpha = 1;
//}

#pragma mark -- layout
- (void)addSubviews
{
    [self.view addSubview:self.scrollView];
    [self.scrollView addSubview:self.contentView];
    
    [self.contentView addSubview:self.stackView];
//    [self.contentView addSubview:self.enterEditView];
}

- (void)defineLayout
{
    [self.scrollView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
    }];

    [self.contentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.scrollView);
        make.width.equalTo(self.view);
        make.bottom.equalTo(self.stackView).offset(30);
    }];
    
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(0);
        make.right.mas_offset(-0);
        make.top.mas_offset(10);
    }];
    
    [self.updateView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([SettingSwitchView height]);
    }];
    [self.onlyMainFrameView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([SettingSegmentView height]);
    }];
    
//    [self.enterEditView mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.size.mas_equalTo(iPadValue(80, 60));
//        make.right.mas_offset(-20);
//        make.top.equalTo(self.view).offset(kScreenHeight*0.5);
//    }];
}

#pragma mark -- lazy init

- (UIView *)contentView
{
    if(!_contentView) {
        _contentView = [[UIView alloc]init];
        _contentView.backgroundColor = UIColor.clearColor;
    }
    
    return _contentView;
}

- (XLScrollView *)scrollView
{
    if(!_scrollView) {
        _scrollView = [[XLScrollView alloc]init];
        _scrollView.showsVerticalScrollIndicator = NO;
        _scrollView.backgroundColor = UIColor.clearColor;
        _scrollView.delegate = self;
    }
    
    return _scrollView;
}

- (UIStackView *)stackView
{
    if(!_stackView) {
        _stackView = [[UIStackView alloc]init];
        _stackView.spacing = 0;
        _stackView.axis = UILayoutConstraintAxisVertical;
        //https://stackoverflow.com/questions/66157717/uistackview-with-setcustomspacing-for-dynamic-number-of-subviews
//        _stackView.distribution = UIStackViewDistributionEqualSpacing;
    }
    
    return _stackView;
}

- (SettingSwitchView *)updateView
{
    if(!_updateView) {
        _updateView = [[SettingSwitchView alloc]initWithShowLine:YES leftOffset:iPadValue(20, 15)];
        [_updateView updateWithTitle:NSLocalizedString(@"script.detail.auto.update", nil) isOn:NO];
    }
    
    return _updateView;
}

- (SettingSegmentView *)onlyMainFrameView
{
    if(!_onlyMainFrameView) {
        _onlyMainFrameView = [[SettingSegmentView alloc]initWithTitle:NSLocalizedString(@"script.frame.title", nil)
                                                          showLine:NO
                                                          showInfo:YES
                                                        leftOffset:iPadValue(20, 15)
                                                          segments:@[
            NSLocalizedString(@"script.frame.default", nil),
            NSLocalizedString(@"script.frame.main", nil),
            NSLocalizedString(@"script.frame.all", nil)
        ]];
    }
    
    return _onlyMainFrameView;
}

//- (UIView *)enterEditView
//{
//    if(!_enterEditView) {
//        _enterEditView = [UIView new];
//        _enterEditView.backgroundColor = [UIColor colorWithHexString:@"#5ab266"];
//        _enterEditView.layer.cornerRadius = iPadValue(40, 30);
//        _enterEditView.layer.masksToBounds = YES;
//        
//        UIImage* image = [UIImage ext_systemImageNamed:@"square.and.pencil" pointSize:iPadValue(30, 24) renderMode:UIImageRenderingModeAlwaysTemplate];
//        UIImageView* logo = [UIImageView new];
//        logo.image = image;
//        logo.tintColor = UIColor.whiteColor;
//        
//        [_enterEditView addSubview:logo];
//        [logo mas_makeConstraints:^(MASConstraintMaker *make) {
//            make.center.equalTo(_enterEditView);
//        }];
//    }
//    
//    return _enterEditView;
//}

- (UIView *)blank1View
{
    if(!_blank1View) {
        _blank1View = [UIView new];
    }
    
    return _blank1View;
}

- (UIView *)blank2View
{
    if(!_blank2View) {
        _blank2View = [UIView new];
    }
    
    return _blank2View;
}

@end
