//
//  UserScriptDetailTextView.m
//  Saber
//
//  Created by qing<PERSON> on 2023/2/16.
//

#import "UserScriptDetailTextView.h"

#import "MaizyHeader.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"

#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "NSObject+Helper.h"
#import "UIView+FrameHelper.h"

#import "UIImageView+WebCache.h"
#import "UIImage+Extension.h"

#import "PaddingLabel.h"
#import "UILabel+Extension.h"
#import "XLTableView.h"

#import "ThemeProtocol.h"

#import "BrowserUtils.h"

@interface UserScriptDetailTextView ()<UITableViewDelegate,UITableViewDataSource,ThemeProtocol>

@property (nonatomic, strong) XLTableView* tableView;

@property (nonatomic, strong) NSMutableArray* model;

@property (nonatomic, strong) NSMutableArray* sectionModel;

@property (nonatomic, strong) UserScript* userScript;

@end

@implementation UserScriptDetailTextView

- (instancetype)init
{
    self = [super init];
    if(self) {
        [self addSubviews];
        [self defineLayout];
        
        self.backgroundColor = UIColor.whiteColor;
    }
    
    return self;
}

- (void)updateWithModel:(NSArray<NSArray<NSString*>*> *)model
           sectionArray:(NSArray<UserScriptDetailTextSectionModel*> *)sectionArray
             userScript:(UserScript *)userScript
{
    self.model = [model mutableCopy];
    self.sectionModel = [sectionArray mutableCopy];
    self.userScript = userScript;
    
    [self updateWithHeight];
    [self.tableView reloadData];
    
    [self applyTheme];
}

- (void)updateWithHeight
{
    float height = 0;
    for(int i=0;i<self.sectionModel.count;i++) {
        UserScriptDetailTextSectionModel* item = self.sectionModel[i];
        if(item.isOpen) {
            NSArray* items = self.model[i];
            height = height + items.count*iPadValue(70, 50) + iPadValue(80, 60);
        } else {
            height = height + iPadValue(80, 60);
        }
    }
    
    height += 350/*预留一部分白色区域,防止折叠展开时动画突兀*/;
    [self mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(height);
    }];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.tableView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
    } else {
        self.backgroundColor = [UIColor colorWithHexString:@"#ffffff"];
        self.tableView.backgroundColor = [UIColor colorWithHexString:@"#ffffff"];
    }
}

#pragma mark -- layout
- (void)addSubviews
{
    [self addSubview:self.tableView];
}

- (void)defineLayout
{
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
}

#pragma mark - UITableViewDataSource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    UserScriptDetailTextSectionModel* model = self.sectionModel[section];
    if(!model.isOpen) {
        return 0;
    }
    
    NSArray* items = self.model[section];
    return items.count;
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return self.model.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    NSArray* items = self.model[indexPath.section];
    NSString* item = items[indexPath.row];
    UserScriptDetailTextCell *cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass(UserScriptDetailTextCell.class)];
    [cell updateWithModel:item];
    
    return cell;
}

- (BOOL)tableView:(UITableView *)tableView canEditRowAtIndexPath:(NSIndexPath *)indexPath
{
    UserScriptDetailTextSectionModel* model = self.sectionModel[indexPath.section];
    //显示添加按钮的，则可以删除
    if(!model.showAddButton) {
        return false;
    }
    
    NSArray* items = self.model[indexPath.section];
    NSString* item = items[indexPath.row];
    
    if(item.length == 0) {
        return false;
    }
    
    return true;
}

// 只适用于ios11 以及以后版本
- (UISwipeActionsConfiguration *)tableView:(UITableView *)tableView trailingSwipeActionsConfigurationForRowAtIndexPath:(NSIndexPath *)indexPath  API_AVAILABLE(ios(11.0))
{
    // 删除数据
    @weakify(self)
    UIContextualAction *deleteAction = [UIContextualAction contextualActionWithStyle:UIContextualActionStyleDestructive title:NSLocalizedString(@"tableview.delete", nil) handler:^(UIContextualAction * _Nonnull action, __kindof UIView * _Nonnull sourceView, void (^ _Nonnull completionHandler)(BOOL)) {
        @strongify(self)
        [tableView setEditing:NO animated:YES];//退出编辑模式，隐藏左滑菜单
    
        NSMutableArray* items = [self.model[indexPath.section] mutableCopy];
        NSString* item = items[indexPath.row];
        
        //这里的逻辑粗暴一点，根据section index来判断白名单和黑名单
        if(indexPath.section == 0) {
            //白名单
            [self.userScript removeWhiteLisItem:item];
        } else if(indexPath.section == 1) {
            //黑名单
            [self.userScript removeBlackLisItem:item];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            [tableView beginUpdates];
            [items removeObject:item];
            self.model[indexPath.section] = items;
            [tableView deleteRowsAtIndexPaths:@[indexPath] withRowAnimation:UITableViewRowAnimationFade];
            [tableView endUpdates];
        });
    }];
    [deleteAction setBackgroundColor:UIColor.redColor];
    
    // 添加
    UISwipeActionsConfiguration *actions = [UISwipeActionsConfiguration configurationWithActions:@[deleteAction]];
    actions.performsFirstActionWithFullSwipe = NO;
    return actions;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section
{
    UserScriptDetailTextSectionHeader* header = [UserScriptDetailTextSectionHeader new];
    
    UserScriptDetailTextSectionModel* model = self.sectionModel[section];
    
    [header updateWithModel:model];
    @weakify(self)
    [header setDidTapAction:^(UserScriptDetailTextSectionModel *model) {
        @strongify(self)
        NSArray* items = self.model[section];
        NSInteger numberOfRow = items.count;
        NSMutableArray *rowArray = [NSMutableArray array];
        if (numberOfRow) {
            for (NSInteger i = 0; i < numberOfRow; i++) {
                [rowArray addObject:[NSIndexPath indexPathForRow:i inSection:section]];
            }
        }
        if (rowArray.count) {
            [CATransaction setCompletionBlock:^{
                [self updateWithHeight];
            }];
            
            [CATransaction begin];
            if (!model.isOpen) {
                [self.tableView deleteRowsAtIndexPaths:[NSArray arrayWithArray:rowArray] withRowAnimation:UITableViewRowAnimationTop];
            }else{
                [self.tableView insertRowsAtIndexPaths:[NSArray arrayWithArray:rowArray] withRowAnimation:UITableViewRowAnimationTop];
            }
            [CATransaction commit];
        }
    }];
    
    return header;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section
{
    return iPadValue(80, 60);
}

#pragma mark - UITableViewDelegate
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    [tableView deselectRowAtIndexPath:indexPath animated:NO];

}


#pragma mark -- lazy init
- (XLTableView *)tableView
{
    if(!_tableView) {
        _tableView = [[XLTableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];

        _tableView.separatorStyle  = UITableViewCellSeparatorStyleNone;
        _tableView.showsHorizontalScrollIndicator = NO;
        _tableView.showsVerticalScrollIndicator   = NO;
        _tableView.delegate   = self;
        _tableView.dataSource = self;
        _tableView.rowHeight = iPadValue(70, 50);
        _tableView.backgroundColor = UIColor.clearColor;
        
        [_tableView registerClass:[UserScriptDetailTextCell class] forCellReuseIdentifier:NSStringFromClass([UserScriptDetailTextCell class])];
        
        _tableView.tableFooterView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, CGFLOAT_MIN, CGFLOAT_MIN)];
        _tableView.tableHeaderView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, CGFLOAT_MIN, CGFLOAT_MIN)];
        
        _tableView.sectionFooterHeight = 0.0;
        _tableView.sectionHeaderHeight = 0.0f;
        //通过禁止滑动，从而解决滑动冲突的问题
        _tableView.scrollEnabled = NO;
    }
    
    return _tableView;
}

- (NSMutableArray *)model
{
    if(!_model) {
        _model = [NSMutableArray array];
    }
    
    return _model;
}

- (NSMutableArray *)sectionModel
{
    if(!_sectionModel) {
        _sectionModel = [NSMutableArray array];
    }
    
    return _sectionModel;
}

@end

#pragma mark -- Cell
@interface UserScriptDetailTextCell ()

@property(nonatomic,strong) UILabel* titleLabel;

@property(nonatomic,strong) UIView* line;

@end

@implementation UserScriptDetailTextCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        self.backgroundColor = [UIColor colorWithHexString:@"#F5F5F5"];
        [self addSubviews];
        [self defineLayout];
        [self handleEvents];
    }
    
    return self;
}

- (void)updateWithModel:(NSString *)text
{
    self.titleLabel.text = text;
    
    [self applyTheme];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.titleLabel.textColor = [UIColor colorWithHexString:kDarkThemeColorText];
        self.line.backgroundColor = [UIColor colorWithHexString:kDarkThemeColorLine];
    } else {
        self.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.titleLabel.textColor = [UIColor colorWithHexString:@"#333333"];
        self.line.backgroundColor = [[UIColor colorWithHexString:@"#999999"] colorWithAlphaComponent:0.3];
    }
}

- (void)handleEvents
{
    UILongPressGestureRecognizer* longPressGesture = [UILongPressGestureRecognizer new];
    [self.contentView addGestureRecognizer:longPressGesture];
    
    @weakify(self)
    [[longPressGesture rac_gestureSignal]
     subscribeNext:^(UILongPressGestureRecognizer* x) {
        @strongify(self)
        if(x.state == UIGestureRecognizerStateBegan) {
            UIPasteboard *pasteboard = [UIPasteboard generalPasteboard];
            pasteboard.string = self.titleLabel.text;
            
            [UIView showSucceed:NSLocalizedString(@"tips.pasteboard.success", nil)];
        }
    }];
}

#pragma mark - layout

- (void)addSubviews
{
    [self.contentView addSubview:self.titleLabel];
}

- (void)defineLayout
{
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(20);
        make.centerY.equalTo(self.contentView);
        make.right.mas_offset(-10);
    }];

    UIView* line = [UIView new];
    line.backgroundColor = [[UIColor colorWithHexString:@"#999999"] colorWithAlphaComponent:0.3];
    [self.contentView addSubview:line];
    [line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.contentView);
        make.left.mas_offset(10);
        make.right.mas_offset(-0);
        make.height.mas_equalTo(0.5);
    }];
    self.line = line;
}

- (UILabel *)titleLabel
{
    if(!_titleLabel) {
        float font = iPadValue(20, 15);
        _titleLabel = [UIView createLabelWithTitle:@""
                                         textColor:[UIColor colorWithHexString:@"#333333"]
                                           bgColor:UIColor.clearColor
                                          fontSize:font
                                     textAlignment:NSTextAlignmentLeft
                                             bBold:NO];
        
        _titleLabel.lineBreakMode = NSLineBreakByTruncatingMiddle;
    }
    
    return _titleLabel;
}

@end

#pragma mark -- Section Model
@implementation UserScriptDetailTextSectionModel
@end

#pragma mark -- Section
@interface UserScriptDetailTextSectionHeader ()<ThemeProtocol>

@property (nonatomic, strong) UIImageView* arrow;

@property (nonatomic, strong) UILabel* titleLabel;

@property (nonatomic, strong) UIButton *addButton;

@property (nonatomic, strong) UIView* line;

@property (nonatomic, strong) UserScriptDetailTextSectionModel *model;

@end

@implementation UserScriptDetailTextSectionHeader

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    
    if(self) {
        [self addSubviews];
        [self defineLayout];
        [self setupObservers];
    }
    
    return self;
}

- (void)updateWithModel:(UserScriptDetailTextSectionModel *)model
{
    self.model = model;
    
    self.titleLabel.text = model.title;
    
    if(model.isOpen) {
        self.arrow.transform = CGAffineTransformMakeRotation(M_PI/2);
    } else {
        self.arrow.transform = CGAffineTransformMakeRotation(0);
    }
    
    self.addButton.hidden = !model.showAddButton;
    
    [self applyTheme];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.arrow.tintColor = UIColor.whiteColor;
        self.line.backgroundColor = [UIColor colorWithHexString:kDarkThemeColorLine];
        self.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        self.titleLabel.textColor = UIColor.whiteColor;
        
        self.addButton.imageView.tintColor = UIColor.whiteColor;
    } else {
        self.arrow.tintColor = [UIColor colorWithHexString:@"#222222"];
        self.line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.backgroundColor = [UIColor colorWithHexString:@"#ffffff"];
        self.titleLabel.textColor = [UIColor colorWithHexString:@"#1A1A1A"];
        
        self.addButton.imageView.tintColor = [UIColor colorWithHexString:@"#222222"];
    }
}

- (void)setupObservers
{
    UITapGestureRecognizer* tap = [UITapGestureRecognizer new];
    [self addGestureRecognizer:tap];
    @weakify(self)
    [tap.rac_gestureSignal subscribeNext:^(id x) {
        @strongify(self)
        self.model.isOpen = !self.model.isOpen;
        
        if(self.didTapAction) {
            self.didTapAction(self.model);
        }
        
        [self animateWithTapAction];
    }];
    
    [[self.addButton rac_signalForControlEvents:UIControlEventTouchUpInside]
    subscribeNext:^(id x) {
        @strongify(self)
        if(self.model.addAction) {
            self.model.addAction();
        }
    }];
}

- (void)animateWithTapAction
{
    [UIView animateWithDuration:0.25 animations:^{
        if(self.model.isOpen) {
            self.arrow.transform = CGAffineTransformMakeRotation(M_PI/2);
        } else {
            self.arrow.transform = CGAffineTransformMakeRotation(0);
        }
    } completion:^(BOOL finished) {
        if(finished) {
            self.line.hidden = self.model.isOpen;
        }
    }];
}

- (void)addSubviews
{
    [self addSubview:self.titleLabel];
    [self addSubview:self.arrow];
    [self addSubview:self.addButton];
}

- (void)defineLayout
{
    [self.arrow mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(0);
        make.centerY.equalTo(self);
        make.size.mas_equalTo(24);
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(32);
        make.centerY.equalTo(self);
        make.right.lessThanOrEqualTo(self);
    }];
    
    [self.addButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self);
        make.right.mas_offset(-iPadValue(30, 15));
    }];

    UIView* line = [UIView new];
    line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    [self addSubview:line];
    [line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self);
        make.left.mas_offset(0);
        make.right.mas_offset(-0);
        make.height.mas_equalTo(0.5);
    }];
    self.line = line;
}

- (UIImageView *)arrow
{
    if(!_arrow) {
        _arrow = [UIImageView new];
        UIImage* image = [[UIImage imageNamed:@"Arrowhead"] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        _arrow.image = image;
        _arrow.tintColor = [UIColor colorWithHexString:@"#222222"];
    }
    
    return _arrow;
}

- (UILabel *)titleLabel
{
    if(!_titleLabel) {
        float font = iPadValue(22, 16);
        _titleLabel = [UIView createLabelWithTitle:@""
                                         textColor:[UIColor colorWithHexString:@"#1A1A1A"]
                                           bgColor:UIColor.clearColor
                                          fontSize:font
                                     textAlignment:NSTextAlignmentLeft
                                             bBold:NO];
    }
    
    return _titleLabel;
}

- (UIButton *)addButton
{
    if(!_addButton) {
        _addButton = [UIButton new];
        
        UIImage* image = [UIImage ext_systemImageNamed:@"plus.circle"
                                             pointSize:22
                                            renderMode:UIImageRenderingModeAlwaysTemplate];
        [_addButton setImage:image forState:UIControlStateNormal];
        
        _addButton.hidden = YES;
    }
    
    return _addButton;
}

@end

