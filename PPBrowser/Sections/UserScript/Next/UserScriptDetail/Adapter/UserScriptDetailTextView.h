//
//  UserScriptDetailTextView.h
//  Saber
//
//  Created by qingbin on 2023/2/16.
//

#import <UIKit/UIKit.h>
#import "UserScript.h"

@class UserScriptDetailTextSectionModel;

/// https://github.com/liufengting/FTFoldingTableView
@interface UserScriptDetailTextView : UIView

- (void)updateWithModel:(NSArray<NSArray<NSString*>*> *)model
           sectionArray:(NSArray<UserScriptDetailTextSectionModel*> *)sectionArray
             userScript:(UserScript *)userScript;

@end

@interface UserScriptDetailTextCell : UITableViewCell

- (void)updateWithModel:(NSString *)text;

@end

@interface UserScriptDetailTextSectionModel : NSObject

@property (nonatomic, strong) NSString *title;

@property (nonatomic, assign) BOOL isOpen;

@property (nonatomic, assign) BOOL showAddButton;

@property (nonatomic, copy) void (^addAction)(void);

@end

@interface UserScriptDetailTextSectionHeader : UIView

- (void)updateWithModel:(UserScriptDetailTextSectionModel *)model;

@property (nonatomic, copy) void (^didTapAction)(UserScriptDetailTextSectionModel* model);

@end
