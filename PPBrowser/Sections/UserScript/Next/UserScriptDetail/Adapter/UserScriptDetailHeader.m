//
//  UserScriptDetailHeader.m
//  Saber
//
//  Created by qing<PERSON> on 2023/2/16.
//

#import "UserScriptDetailHeader.h"

#import "MaizyHeader.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"

#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "NSObject+Helper.h"
#import "UIView+FrameHelper.h"

#import "UIImageView+WebCache.h"
#import "UIImage+Extension.h"

#import "PaddingNewLabel.h"
#import "UILabel+Extension.h"
#import "DatabaseUnit+UserScript.h"
#import "ThemeProtocol.h"

#import "BrowserUtils.h"
#import "PPBrowser-Swift.h"

#import "CustomTitleAndImageView.h"
#import "PPNotifications.h"

#import "PaddingNewLabel.h"

@interface UserScriptDetailHeader ()<ThemeProtocol>

@property (nonatomic, strong) UserScript* model;

@property (nonatomic, strong) UIStackView* stackView;
//标题
@property (nonatomic, strong) UILabel* titleLabel;
//作者
@property (nonatomic, strong) UILabel* authorLabel;
//版本号
@property (nonatomic, strong) UILabel* versionLabel;
//是否已激活
@property (nonatomic, strong) PaddingNewLabel* activeLabel;
//编辑代码按钮
@property (nonatomic, strong) CustomTitleAndImageView* editCodeView;
//分割线
@property (nonatomic, strong) UIView *line1;
//详情
@property (nonatomic, strong) PaddingNewLabel *detailLabel;

@property (nonatomic, strong) UIView* borderView;
@property (nonatomic, strong) UIImageView* logo;

@end

@implementation UserScriptDetailHeader

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if(self) {
        [self commonInit];
        [self addSubviews];
        [self defineLayout];
        [self setupObserver];
        
        self.backgroundColor = UIColor.whiteColor;
    }
    
    return self;
}

- (void)updateWithModel:(UserScript *)model
{
    _model = model;
    
    if(model.iconUrl.length > 0) {
        UIImage* image = [UIImage imageNamed:@"fail"];
        NSURL* URL = [NSURL URLWithString:model.iconUrl];
        
        [self.logo sd_setImageWithURL:URL placeholderImage:image];
    } else {
        //https://www.iconfont.cn/search/index?searchType=icon&q=%E8%84%9A%E6%9C%AC&page=12&fromCollection=-1
        //https://www.iconfont.cn/search/index?searchType=icon&q=%E8%84%9A%E6%9C%AC&page=30&fromCollection=-1
        //7ab5f7
        self.logo.image = [UIImage imageNamed:@"script_default_icon"];
    }
    
    self.titleLabel.text = model.name;
    
    self.authorLabel.text = model.author;
    
    NSString* version = NSLocalizedString(@"userscript.empty.version", nil);
    if(model.version.length > 0) version = model.version;
    NSString* versionTitle = NSLocalizedString(@"userscript.version", nil);
    self.versionLabel.text = [NSString stringWithFormat:@"%@: %@", versionTitle, version];
    
    if(model.isActive) {
        //已激活
        self.activeLabel.text = NSLocalizedString(@"script.detail.active", nil);
        self.activeLabel.backgroundColor = [UIColor colorWithHexString:@"#2D7AFE"];
        self.activeLabel.textColor = UIColor.whiteColor;
    } else {
        //已停止
        self.activeLabel.text = NSLocalizedString(@"script.detail.deactive", nil);
        self.activeLabel.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.activeLabel.textColor = [UIColor colorWithHexString:@"#333333"];
    }
    
    if(model.iconUrl.length > 0) {
        //有logo
    } else {
        //没有logo
        self.titleLabel.font = [UIFont boldSystemFontOfSize:iPadValue(20, 16)];
    }
    
    [self.detailLabel setText:model.desc];
    
    //决定高度
    [self mas_updateConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.detailLabel).offset(20);
    }];
    
    [self applyTheme];
}

#pragma mark -- 暗黑模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.titleLabel.textColor = UIColor.whiteColor;
        self.authorLabel.textColor = [UIColor colorWithHexString:@"#666666"];
        self.versionLabel.textColor = [UIColor colorWithHexString:@"#666666"];
        
        self.line1.backgroundColor = [UIColor colorWithHexString:kDarkThemeColorLine];
        
        self.detailLabel.textColor = [UIColor colorWithHexString:@"#ffffff"];
        
        [self.editCodeView updateWith:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            imageView.tintColor = [UIColor colorWithHexString:@"#ffffff"];;
            titleLabel.textColor = [UIColor colorWithHexString:@"#ffffff"];
        }];
        
        self.borderView.layer.borderColor = [UIColor colorWithHexString:kDarkThemeColorLine].CGColor;
    } else {
        self.backgroundColor = UIColor.whiteColor;
        self.titleLabel.textColor = [UIColor colorWithHexString:@"#1A1A1A"];
        self.authorLabel.textColor = [UIColor colorWithHexString:@"#999999"];
        self.versionLabel.textColor = [UIColor colorWithHexString:@"#999999"];
        
        self.line1.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        
        self.detailLabel.textColor = [UIColor colorWithHexString:@"#333333"];
        
        [self.editCodeView updateWith:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            imageView.tintColor = [UIColor colorWithHexString:@"#1A1A1A"];;
            titleLabel.textColor = [UIColor colorWithHexString:@"#1A1A1A"];
        }];
        
        self.borderView.layer.borderColor = [UIColor colorWithHexString:@"#e6e6e6"].CGColor;
    }
}

- (void)commonInit
{
    self.line1 = [UIView new];
    self.line1.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    
    [self addSubview:self.line1];
}

- (void)setupObserver
{
    @weakify(self)
    UITapGestureRecognizer* tap = [UITapGestureRecognizer new];
    [self.activeLabel addGestureRecognizer:tap];
    [tap.rac_gestureSignal subscribeNext:^(id x) {
        @strongify(self)
        self.model.isActive = !self.model.isActive;
        [self _handleActiveAction:self.model];
        [self updateWithModel:self.model];
    }];
    
    tap = [UITapGestureRecognizer new];
    [self.detailLabel addGestureRecognizer:tap];
    [tap.rac_gestureSignal subscribeNext:^(id x) {
        @strongify(self)
        int numberOfLines = self.detailLabel.numberOfLines == 3 ? 0 : 3;
        self.detailLabel.numberOfLines = numberOfLines;
    }];
    
    tap = [UITapGestureRecognizer new];
    [self.editCodeView addGestureRecognizer:tap];
    [tap.rac_gestureSignal subscribeNext:^(id x) {
        @strongify(self)
        if(self.codeEditAction) {
            self.codeEditAction();
        }
    }];
}

- (void)_handleActiveAction:(UserScript *)item
{
    //更新数据库标志位
    DatabaseUnit* unit = [DatabaseUnit updateUserScriptWithId:item.uuid isActive:item.isActive script:item];
    [unit setCompleteBlock:^(id result, BOOL success) {
        if(success) {
            //暂停/开启脚本
            //发送通知
            [[NSNotificationCenter defaultCenter] postNotificationName:kReloadUserScriptNotification object:nil];
        }
    }];
    DB_EXEC(unit);
}

#pragma mark -- layout
- (void)addSubviews
{
    [self addSubview:self.borderView];
    [self.borderView addSubview:self.logo];
    
    [self addSubview:self.stackView];
    [self addSubview:self.detailLabel];
    [self addSubview:self.editCodeView];
}

- (void)defineLayout
{
    [self.borderView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(iPadValue(15, 10));
        make.centerY.equalTo(self.stackView);
        make.size.mas_equalTo(iPadValue(100, 80));
    }];
    
    [self.logo mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.borderView);
        make.size.mas_equalTo(iPadValue(80, 60));
    }];
    
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.borderView.mas_right).offset(iPadValue(20, 10));
        make.top.mas_offset(iPadValue(15, 10));
        make.right.equalTo(self);
    }];
    
//    CGSize size = CGSizeMake(65, 24);
//    if([BrowserUtils isiPad]) {
//        //iPad
//        size = CGSizeMake(95, 40);
//    }
//
//    int localizable = [NSLocalizedString(@"opensearch.value", nil) intValue];
//    if(localizable == 0 || localizable == 1) {
//        //中国
//        CGSize size = CGSizeMake(65, 24);
//        if([BrowserUtils isiPad]) {
//            //iPad
//            size = CGSizeMake(95, 40);
//        }
//        [self.activeButton mas_makeConstraints:^(MASConstraintMaker *make) {
//            make.size.mas_equalTo(size);
//        }];
//    } else {
//        //外国
//        CGSize size = CGSizeMake(75, 24);
//        if([BrowserUtils isiPad]) {
//            //iPad
//            size = CGSizeMake(100, 40);
//        }
//        [self.activeButton mas_makeConstraints:^(MASConstraintMaker *make) {
//            make.size.mas_equalTo(size);
//        }];
//    }
    
    [self.line1 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.activeLabel).offset(20);
        make.height.mas_equalTo(1);
        make.left.equalTo(self.borderView);
        make.right.equalTo(self);
    }];
    
    [self.detailLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self);
        make.top.equalTo(self.line1.mas_bottom);
    }];
    
    [self.editCodeView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.activeLabel);
        make.height.mas_equalTo(40);
        make.right.equalTo(self).offset(-iPadValue(30, 15));
    }];
}

- (UILabel *)titleLabel
{
    if(!_titleLabel) {
        float font = iPadValue(20, 16);
        _titleLabel = [UIView createLabelWithTitle:@""
                                         textColor:[UIColor colorWithHexString:@"#1A1A1A"]
                                           bgColor:UIColor.clearColor
                                          fontSize:font
                                     textAlignment:NSTextAlignmentLeft
                                             bBold:YES];
    }
    
    return _titleLabel;
}

- (UILabel *)authorLabel
{
    if(!_authorLabel) {
        float font = iPadValue(18, 13);
        _authorLabel = [UIView createLabelWithTitle:@""
                                         textColor:[UIColor colorWithHexString:@"#999999"]
                                           bgColor:UIColor.clearColor
                                          fontSize:font
                                     textAlignment:NSTextAlignmentLeft
                                             bBold:NO];
    }
    
    return _authorLabel;
}

- (UILabel *)versionLabel
{
    if(!_versionLabel) {
        float font = iPadValue(18, 13);
        _versionLabel = [UIView createLabelWithTitle:@""
                                         textColor:[UIColor colorWithHexString:@"#999999"]
                                           bgColor:UIColor.clearColor
                                          fontSize:font
                                     textAlignment:NSTextAlignmentLeft
                                             bBold:NO];
    }
    
    return _versionLabel;
}

- (UIStackView *)stackView
{
    if(!_stackView) {
        _stackView = [[UIStackView alloc]initWithArrangedSubviews:@[
            self.titleLabel,
            self.authorLabel,
            self.versionLabel,
            self.activeLabel,
        ]];
        
        _stackView.spacing = iPadValue(10, 6);
        _stackView.axis = UILayoutConstraintAxisVertical;
        _stackView.alignment = UIStackViewAlignmentLeading;
    }
    
    return _stackView;
}

- (PaddingNewLabel *)activeLabel
{
    if(!_activeLabel) {
        _activeLabel = [PaddingNewLabel new];
        
        _activeLabel.layer.cornerRadius = iPadValue(31.5/2.0, 27/2.0);
        _activeLabel.layer.masksToBounds = YES;
        _activeLabel.font = [UIFont systemFontOfSize:iPadValue(18, 14)];
        _activeLabel.textColor = UIColor.whiteColor;
        _activeLabel.backgroundColor = [UIColor colorWithHexString:@"#2D7AFE"];
        _activeLabel.text = NSLocalizedString(@"script.detail.active", nil);
        
        _activeLabel.userInteractionEnabled = YES;
        _activeLabel.edgeInsets = UIEdgeInsetsMake(5, 10, 5, 10);
    }
    
    return _activeLabel;
}

- (CustomTitleAndImageView *)editCodeView
{
    if(!_editCodeView) {
        _editCodeView = [[CustomTitleAndImageView alloc] initWithLayout:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerY.mas_offset(0);
                make.left.mas_offset(0);
            }];
            
            [imageView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.right.mas_offset(0);
                make.left.equalTo(titleLabel.mas_right).offset(4);
                make.centerY.mas_offset(0);
                make.size.mas_equalTo(20);
            }];
            
            UIImage* image = [[UIImage imageNamed:@"script_edit_icon"] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
            imageView.image = image;
            
            titleLabel.font = [UIFont systemFontOfSize:iPadValue(18, 15)];
            titleLabel.text = NSLocalizedString(@"script.code.edit", nil);
        }];
    }
    
    return _editCodeView;
}

- (PaddingNewLabel *)detailLabel
{
    if(!_detailLabel) {
        _detailLabel = [PaddingNewLabel new];
        _detailLabel.font = [UIFont systemFontOfSize:iPadValue(20, 14)];
        _detailLabel.textColor = [UIColor colorWithHexString:@"#333333"];
        _detailLabel.numberOfLines = 3;
        _detailLabel.textAlignment = NSTextAlignmentLeft;
        _detailLabel.userInteractionEnabled = YES;
        
        float offset = iPadValue(30, 15);
        float topOffset = iPadValue(15, 10);
        _detailLabel.edgeInsets = UIEdgeInsetsMake(topOffset, offset, 0, offset);
    }
    
    return _detailLabel;
}

- (UIView *)borderView
{
    if(!_borderView) {
        _borderView = [UIView new];
        _borderView.layer.cornerRadius = 10;
        _borderView.layer.masksToBounds = YES;
        _borderView.layer.borderWidth = 1;
        _borderView.layer.borderColor = [UIColor colorWithHexString:@"#e6e6e6"].CGColor;
    }
    
    return _borderView;
}

- (UIImageView *)logo
{
    if(!_logo) {
        _logo = [UIImageView new];
    }
    
    return _logo;
}

@end

