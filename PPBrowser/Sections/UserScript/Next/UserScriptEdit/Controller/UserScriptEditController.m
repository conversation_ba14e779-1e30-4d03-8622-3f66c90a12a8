//
//  UserScriptEditController.m
//  PPBrowser
//
//  Created by qingbin on 2022/5/24.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "UserScriptEditController.h"

#import "MaizyHeader.h"
#import "Masonry.h"
#import "PPNotifications.h"
#import "ReactiveCocoa.h"

#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "NSObject+Helper.h"
#import "NSString+Helper.h"
#import "UIView+FrameHelper.h"
#import "Tampermonkey.h"

#import "UIScrollView+TPKeyboardAvoidingAdditions.h"
#import "DatabaseUnit+UserScript.h"

#import "ThemeProtocol.h"
#import "PreferenceManager.h"
#import "BrowserUtils.h"

NSString* jsPlaceHolder;

@interface UserScriptEditController ()<UITextViewDelegate,UITextFieldDelegate, UITextPasteDelegate, ThemeProtocol>

@property (nonatomic, strong) UITextView *textView;

@property (nonatomic, strong) UserScript* userScript;
// 是否是新增脚本模式
@property (nonatomic, assign) BOOL isNewScript;

@property (nonatomic, strong) UIButton* rightButton;

@end

@implementation UserScriptEditController

- (instancetype)initWithUserScript:(UserScript*)userScript
{
    self = [super init];
    if(self) {
        self.userScript = userScript;
        self.isNewScript = NO;
    }
    
    return self;
}

// 点击添加进来
- (instancetype)initWithNewScript
{
    self = [super init];
    if(self) {
        self.isNewScript = YES;
        self.userScript = [UserScript new];
    }
    
    return self;
}

- (void)viewDidLoad
{
    [super viewDidLoad];

    self.navigationController.navigationBar.backgroundColor = UIColor.whiteColor;
    self.navigationItem.title = NSLocalizedString(@"userscriptEdit.title", nil);
    self.view.backgroundColor = UIColor.whiteColor;
    
    jsPlaceHolder = NSLocalizedString(@"userscriptEdit.empty.code", nil);
    
    [self createCustomLeftBarButtonItem];
    [self _createCustomRightBarButtonItem];
    
    [self addSubviews];
    [self defineLayout];
    [self setupObservers];
    
    if(!self.isNewScript) {
        //从脚本点击进来
        self.textView.text = self.userScript.content;
    } else {
        self.textView.text = jsPlaceHolder;
    }
    
    [self applyTheme];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        [self.rightButton setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
        
        self.textView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.textView.textColor = [UIColor colorWithHexString:@"#ffffff"];
    } else {
        self.view.backgroundColor = UIColor.whiteColor;
        [self.rightButton setTitleColor:[UIColor colorWithHexString:@"#2D7AFE"] forState:UIControlStateNormal];
        
        self.textView.backgroundColor = UIColor.whiteColor;
        self.textView.textColor = [UIColor colorWithHexString:@"#1A1A1A"];
    }
}

- (void)dealloc
{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)setupObservers
{
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(_keyboardWillShown:)
                                                 name:UIKeyboardWillShowNotification
                                               object:nil];

    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(_keyboardWillHidden:)
                                                 name:UIKeyboardWillHideNotification
                                               object:nil];
}

#pragma mark -- 键盘弹起事件
- (void)_keyboardWillShown:(NSNotification *)notification
{
    NSDictionary *info = [notification userInfo];
    CGRect keyboardRect = [[info objectForKey:UIKeyboardFrameEndUserInfoKey] CGRectValue];
    if (CGRectIsEmpty(keyboardRect)) {
        return;
    }
    
    //https://stackoverflow.com/questions/27992591/resize-the-uitextview-when-keyboard-appears
    [UIView beginAnimations:nil context:NULL];
    [UIView setAnimationCurve:[[[notification userInfo] objectForKey:UIKeyboardAnimationCurveUserInfoKey] intValue]];
    [UIView setAnimationDuration:[[[notification userInfo] objectForKey:UIKeyboardAnimationDurationUserInfoKey] floatValue]];
    
    self.textView.contentInset = UIEdgeInsetsMake(0, 0, keyboardRect.size.height, 0);
    
    [self.view layoutIfNeeded];
    [UIView commitAnimations];
}

#pragma mark -- 键盘收起事件
- (void)_keyboardWillHidden:(NSNotification *)notification
{
    [UIView beginAnimations:nil context:NULL];
    [UIView setAnimationCurve:[[[notification userInfo] objectForKey:UIKeyboardAnimationCurveUserInfoKey] intValue]];
    [UIView setAnimationDuration:[[[notification userInfo] objectForKey:UIKeyboardAnimationDurationUserInfoKey] floatValue]];
    
    self.textView.contentInset = UIEdgeInsetsZero;
    
    [self.view layoutIfNeeded];
    [UIView commitAnimations];
}

#pragma mark -- UITextViewDelegate
- (BOOL)textViewShouldBeginEditing:(UITextView *)textView
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    
    if(self.isNewScript) {
        if(isDarkTheme) {
            self.textView.textColor = UIColor.whiteColor;
        } else {
            self.textView.textColor = [UIColor colorWithHexString:@"#333333"];
        }
        
        if([self.textView.text isEqualToString:jsPlaceHolder]) {
            self.textView.text = @"";
        }
    }
    
    return YES;
}

- (BOOL)textViewShouldEndEditing:(UITextView *)textView
{
    if([self.textView.text isEqualToString:@""]) {
        self.textView.textColor = [UIColor colorWithHexString:@"#999999"];
        self.textView.text = jsPlaceHolder;
    }
    
    return YES;
}

#pragma mark -- UITextPasteDelegate
//参考https://stackoverflow.com/questions/47227011/uitextview-paste-action-inserts-empty-string-on-ios-11
//bug:直接添加脚本 -- 填写标题 -- 粘贴代码,崩溃
//Set a Hook to know there's a paste action while I'm the first responder - fired when Paste was tapped
- (void)textPasteConfigurationSupporting:(id<UITextPasteConfigurationSupporting>)textPasteConfigurationSupporting transformPasteItem:(id<UITextPasteItem>)item API_AVAILABLE(ios(11.0)) {
    [self paste:textPasteConfigurationSupporting];
}

//generic paste action handler
- (void)paste:(id)sender {
    UIPasteboard *pasteBoard = [UIPasteboard generalPasteboard];
    if ([pasteBoard hasStrings]) {
        id<UITextPasteConfigurationSupporting> pasteItem = (id<UITextPasteConfigurationSupporting>)sender;
        if ([pasteItem isKindOfClass:[UITextView class]]) {
            UITextView* myTextView = (UITextView*)pasteItem;
                [myTextView insertText:[pasteBoard string]];
        }
    }
}

#pragma mark -- layout
- (void)addSubviews
{
    [self.view addSubview:self.textView];
}

- (void)defineLayout
{
    float offset = iPadValue(30, 15);
    [self.textView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(offset);
        make.left.equalTo(self.view).offset(offset);
        make.right.equalTo(self.view).offset(-offset);
        make.bottom.equalTo(self.view);
    }];
}

#pragma mark -- 导航栏
- (void)leftBarbuttonClick
{
    if(self.navigationController.viewControllers.count > 1) {
        [self.navigationController popViewControllerAnimated:YES];
    } else {
        [self dismissViewControllerAnimated:YES completion:nil];
    }
}

- (void)_createCustomRightBarButtonItem
{
    UIButton* rightButton = [[UIButton alloc] initWithFrame:CGRectMake(0.0, 0.0f, 44.0f, 44.0)];
    [rightButton setBackgroundColor:[UIColor clearColor]];

    rightButton.titleLabel.font = [UIFont systemFontOfSize:16.0];
    [rightButton setTitle:NSLocalizedString(@"userscriptEdit.save.confirm", nil) forState:UIControlStateNormal];
    [rightButton setTitleColor:[UIColor colorWithHexString:@"#2D7AFE"] forState:UIControlStateNormal];
    
    [rightButton addTarget:self action:@selector(rightBarbuttonClick) forControlEvents:UIControlEventTouchUpInside];
    self.rightButton = rightButton;
    
    UIBarButtonItem* barItem = [[UIBarButtonItem alloc] initWithCustomView:rightButton];

    UIBarButtonItem *spacer = [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemFixedSpace target:nil action:nil];
    spacer.width = -16.0f; // for example shift right bar button to the right

    self.navigationItem.rightBarButtonItems = @[spacer, barItem];
}

- (void)rightBarbuttonClick
{
    [self saveChangedContent];
}

#pragma mark -- 保存修改的内容
- (void)saveChangedContent
{
    [[NSObject normalWindow] endEditing:YES];
    
    NSString* scriptContent = self.textView.text;
    
    if(scriptContent.length == 0) {
        //弹toast,请输入脚本
        [UIView showWarning:jsPlaceHolder];
        return;
    }
    
    if(self.userScript.uuid.length == 0) {
        self.userScript.uuid = [[NSUUID UUID] UUIDString];
    }
    
    //1、移除内存缓存
    [[Tampermonkey shareInstance] removeUserScriptCacheByUUID:self.userScript.uuid];
    
    //3、从源码加载缓存
    @weakify(self)
    [[Tampermonkey shareInstance] loadJsWithUUID:self.userScript.uuid
                                      sourceCode:scriptContent
                                      completion:^(UserScript *userScript) {
        @strongify(self)
        
        [UIView hideHud:NO];
        if(!userScript) return;
        
        //将自定义的值赋值给新的model
        if(self.isNewScript == NO) {
            userScript.isActive = self.userScript.isActive;
            userScript.isAutoUpdate = self.userScript.isAutoUpdate;
            userScript.frameOption = self.userScript.frameOption;
            userScript.whiteList = self.userScript.whiteList;
            userScript.blackList = self.userScript.blackList;
            userScript.ppOrder = self.userScript.ppOrder;
            
            self.userScript = userScript;
            
            //更新数据库
            DatabaseUnit* unit = [DatabaseUnit updateUserScriptWithItem:userScript];
            DB_EXEC(unit);
        } else {
            //添加新脚本
            //3、保存脚本相关信息到数据库
            DatabaseUnit* unit = [DatabaseUnit addUserScriptWithItem:userScript];
            DB_EXEC(unit);
        }
        
        //4、保存到内存缓存
        [[Tampermonkey shareInstance] saveUserScriptToCache:userScript];
        
        //5、加载@require和@resource相关资源
        [[Tampermonkey shareInstance] loadResourceWithUserScript:userScript completion:nil];
        
        //6、更新代码执行文件
        [userScript updateExecutorJs];
        
        //重新加载脚本
        //不用发通知，因为UserSciptController会自动更新数据
        //但是需要重新加载脚本
        [[NSNotificationCenter defaultCenter] postNotificationName:kReloadUserScriptNotification object:self.userScript];
        
        if(self.navigationController.viewControllers.count > 1) {
            [self.navigationController popViewControllerAnimated:YES];
        } else {
            [self dismissViewControllerAnimated:YES completion:nil];
        }
    }];
}

- (UITextView *)textView
{
    if(!_textView) {
        _textView = [UITextView new];
        _textView.delegate = self;
        _textView.pasteDelegate = self;
        _textView.textColor = [UIColor colorWithHexString:@"#1a1a1a"];
        
        float font = iPadValue(20, 15);
        _textView.font = [UIFont systemFontOfSize:font];
        _textView.text = jsPlaceHolder;
        
        float bottomOffset = [NSObject normalWindow].safeAreaInsets.bottom;
        _textView.contentInset = UIEdgeInsetsMake(0, 0, bottomOffset, 0);
    }
    
    return _textView;
}

@end
