//
//  UserScriptUpdateManager.m
//  Saber
//
//  Created by q<PERSON><PERSON> on 2023/2/18.
//

#import "UserScriptUpdateManager.h"

#import "Tampermonkey.h"
#import "DatabaseUnit+UserScript.h"

#import "ReactiveCocoa.h"

#import "MaizyHeader.h"
#import "NSObject+Helper.h"

#import "AFNetworking.h"
#import "AFNetworkReachabilityManager.h"

#import "BrowserUtils.h"
#import "NetworkUtil.h"
#import "PPNotifications.h"

@interface UserScriptUpdateManager ()

@property (nonatomic, strong) dispatch_queue_t queue;

@property (nonatomic, strong) NSMutableDictionary* mapper;

@end

@implementation UserScriptUpdateManager

+ (instancetype)shareInstance
{
    static UserScriptUpdateManager* instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[self alloc]init];
    });

    return instance;
}

- (instancetype)init
{
    self = [super init];
    if(self) {
        self.queue = dispatch_queue_create("com.userscript.update.queue", DISPATCH_QUEUE_CONCURRENT);
        self.mapper = [NSMutableDictionary dictionary];
    }
    
    return self;
}

+ (KUserScriptVersion)compareVersion:(NSString *)newVersion
                           toVersion:(NSString *)oldVersion
{
    NSArray *list1 = [newVersion componentsSeparatedByString:@"."];
    NSArray *list2 = [oldVersion componentsSeparatedByString:@"."];
    for (int i = 0; i < list1.count || i < list2.count; i++) {
        NSInteger a = 0, b = 0;
        if (i < list1.count) {
            a = [list1[i] integerValue];
        }
        if (i < list2.count) {
            b = [list2[i] integerValue];
        }
        if (a > b) {
            //newVersion大于oldVersion
            return KUserScriptVersionNew;
        } else if (a < b) {
            //newVersion小于oldVersion
            return KUserScriptVersionOld;
        }
    }
    //newVersion等于newVersion
    return KUserScriptVersionEqual;
}

#pragma mark -- 更新所有需要更新的脚本
- (void)updateUserScripts
{
    DatabaseUnit* unit = [DatabaseUnit queryAllUserScripts];
    @weakify(self)
    [unit setCompleteBlock:^(NSArray* result, BOOL success) {
        @strongify(self)
        if(!self) return;
        if(success) {
            [self _handleUpdateScritps:result];
        }
    }];

    DB_EXEC(unit);
}

- (void)_handleUpdateScritps:(NSArray *)result
{
    for(UserScript* item in result) {
        UserScriptUpdateStatus status = [self.mapper[item.uuid] intValue];
        if(status == UserScriptUpdateStatusFinishRequest
           || status == UserScriptUpdateStatusLoading) {
            continue;
        }
        
        if(item.isAutoUpdate) {
            item.updateStatus = UserScriptUpdateStatusLoading;
            @synchronized (self) {
                self.mapper[item.uuid] = @(UserScriptUpdateStatusLoading);
            }
            
            //刷新更新中的状态
            [[NSNotificationCenter defaultCenter] postNotificationName:kUpdateUserScriptNotification object:nil userInfo:@{
                @"script" : item,
                @"needUpdate" : @(YES),
            }];
            
            dispatch_async(self.queue, ^{
                [self updateWithUserScript:item completion:^(BOOL needUpdate, UserScript *script){
                    @synchronized (self) {
                        self.mapper[item.uuid] = @(script.updateStatus);
                    }
                    
                    dispatch_async(dispatch_get_main_queue(), ^{
                        [[NSNotificationCenter defaultCenter] postNotificationName:kUpdateUserScriptNotification object:nil userInfo:@{
                            @"script" : script,
                            @"needUpdate" : @(needUpdate),
                        }];
                    });
                }];
            });
        }
    }
}

#pragma mark -- 更新指定脚本
- (void)updateWithUserScript:(UserScript *)oldVersion
                  completion:(void(^)(BOOL needUpdate, UserScript* script))completion
{
    NSURL* URL;
    NSString* selectUrl = [oldVersion getUpdateUrl];
    
    URL = [NSURL URLWithString:selectUrl];
    if(!URL) {
        if(completion) {
            oldVersion.updateStatus = UserScriptUpdateStatusFinishRequest;
            completion(NO, oldVersion);
        }
        return;
    }
    
    //设置安装中状态
    oldVersion.updateStatus = UserScriptUpdateStatusLoading;
    
    [NetworkUtil requestGet:selectUrl completion:^(BOOL succ, id responseObject) {
        UserScript* userScript = nil;
        if(succ) {
            NSString* jsContent = [[NSString alloc] initWithData:responseObject encoding:NSUTF8StringEncoding];
            userScript = [[Tampermonkey shareInstance] parseJSContent:jsContent];
            
            KUserScriptVersion versionResult = [UserScriptUpdateManager compareVersion:userScript.version
                                                                             toVersion:oldVersion.version];
            if(versionResult == KUserScriptVersionNew) {
                //有新版本
                [[Tampermonkey shareInstance] updateUserScriptWithNewVersion:userScript
                                                                  oldVersion:oldVersion];
                
                userScript.updateStatus = UserScriptUpdateStatusFinishRequest;
                if(completion) {
                    completion(YES, userScript);
                }
            } else {
                //没有新版本
                userScript = oldVersion;
                userScript.updateStatus = UserScriptUpdateStatusFinishRequest;
                
                if(completion) {
                    completion(NO, userScript);
                }
            }
        } else {
            //更新失败
            userScript = oldVersion;
            userScript.updateStatus = UserScriptUpdateStatusFailed;
            
            if(completion) {
                completion(NO, userScript);
            }
        }
    }];
}

- (UserScriptUpdateStatus)getScriptUpdateStatusWithScriptId:(NSString *)scriptId
{
    if(scriptId.length == 0) return UserScriptUpdateStatusDefault;
    
    UserScriptUpdateStatus status;
    @synchronized (self) {
        status = [self.mapper[scriptId] intValue];
    }
    
    return status;
}

@end
