//
//  UserScriptUpdateManager.h
//  Saber
//
//  Created by qing<PERSON> on 2023/2/18.
//

#import <Foundation/Foundation.h>
#import "UserScript.h"

typedef NS_ENUM(NSInteger,KUserScriptVersion) {
    KUserScriptVersionEqual = 0,
    KUserScriptVersionNew = 1,
    KUserScriptVersionOld = 2,
};

@interface UserScriptUpdateManager : NSObject

+ (instancetype)shareInstance;

/// 版本比较
+ (KUserScriptVersion)compareVersion:(NSString *)newVersion
                           toVersion:(NSString *)oldVersion;

/// 更新所有需要更新的脚本
- (void)updateUserScripts;

/// 更新指定脚本
- (void)updateWithUserScript:(UserScript *)oldVersion
                  completion:(void(^)(BOOL needUpdate, UserScript* script))completion;

/// 获取更新状态
- (UserScriptUpdateStatus)getScriptUpdateStatusWithScriptId:(NSString *)scriptId;

@end

