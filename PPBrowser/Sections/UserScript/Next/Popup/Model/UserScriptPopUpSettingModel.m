//
//  UserScriptPopUpSettingModel.m
//  PPBrowser
//
//  Created by qingbin on 2024/4/28.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import "UserScriptPopUpSettingModel.h"

@implementation UserScriptPopUpSettingModel

/// 更多脚本
+ (instancetype)moreScriptModel
{
    UserScriptPopUpSettingModel* model = [UserScriptPopUpSettingModel new];
    model.type = 0;
    model.title = NSLocalizedString(@"script.search.title", nil);
    model.imageName = @"script_search_icon";
    
    return model;
}

/// 脚本管理页
+ (instancetype)scriptSettingModel
{
    UserScriptPopUpSettingModel* model = [UserScriptPopUpSettingModel new];
    model.type = 1;
    model.title = NSLocalizedString(@"script.setting.title", nil);
    model.imageName = @"script_setting_icon";
    
    return model;
}

@end
