//
//  UserScriptPopUpCell.m
//  PPBrowser
//
//  Created by qingbin on 2024/4/28.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import "UserScriptPopUpCell.h"

#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "UIView+FrameHelper.h"
#import "NSObject+Helper.h"

#import "UIImageView+WebCache.h"
#import "UIImage+Extension.h"

#import "ThemeProtocol.h"
#import "BrowserUtils.h"

@interface UserScriptPopUpCell ()<ThemeProtocol>

@property (nonatomic, strong) UserScript* model;

@property (nonatomic, strong) UserScriptPopUpSettingModel *settingModel;

@property (nonatomic, strong) UIImageView* logo;

@property (nonatomic, strong) UILabel* titleLabel;

@property (nonatomic, strong) UIImageView *menuImageView;

@property (nonatomic, strong) UIView *line;

@property (nonatomic, strong) RACDisposable *commandDisposable;

@end

@implementation UserScriptPopUpCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
                
        [self addSubviews];
        [self defineLayout];
        [self setupObservers];
    }
    
    return self;
}

- (void)updateWithModel:(UserScript*)model
{
    self.model = model;
    
    self.titleLabel.text = model.name;
    
    if(model.iconUrl.length > 0) {
        UIImage* image = [UIImage imageNamed:@"fail"];
        NSURL* URL = [NSURL URLWithString:model.iconUrl];
        
        [self.logo sd_setImageWithURL:URL placeholderImage:image];
    } else {
        //https://www.iconfont.cn/search/index?searchType=icon&q=%E8%84%9A%E6%9C%AC&page=12&fromCollection=-1
        //https://www.iconfont.cn/search/index?searchType=icon&q=%E8%84%9A%E6%9C%AC&page=30&fromCollection=-1
        //https://www.iconfont.cn/search/index?searchType=icon&q=%E8%84%9A%E6%9C%AC&page=35&fromCollection=-1
        //7ab5f7
        self.logo.image = [UIImage imageNamed:@"script_default_icon"];
    }

    self.menuImageView.hidden = model.commands.count==0;
    //先解绑属性观察
    if(self.commandDisposable) {
        [self.commandDisposable dispose];
    }
    @weakify(self)
    self.commandDisposable = [RACObserve(self.model, commands) subscribeNext:^(id x) {
        @strongify(self)
        //菜单项
        self.menuImageView.hidden = model.commands.count==0;
    }];
    
    self.line.hidden = model.isLastInSection;
    
    [self applyTheme];
}

- (void)updateWithSettingModel:(UserScriptPopUpSettingModel *)model
{
    self.settingModel = model;
    
    self.titleLabel.text = model.title;
    self.logo.image = [[UIImage imageNamed:model.imageName] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
    
    self.line.hidden = model.isLastInSection;
    
    self.menuImageView.hidden = YES;
    
    [self applyTheme];
}

#pragma mark -- 暗黑模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.titleLabel.textColor = UIColor.whiteColor;
        self.line.backgroundColor = [UIColor colorWithHexString:kDarkThemeColorLine];
        
        //只针对设置项
        self.logo.tintColor = UIColor.whiteColor;
        self.menuImageView.tintColor = UIColor.whiteColor;
    } else {
        self.backgroundColor = UIColor.whiteColor;
        self.titleLabel.textColor = [UIColor colorWithHexString:@"#1A1A1A"];
        self.line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        
        //只针对设置项
        self.logo.tintColor = [UIColor colorWithHexString:@"#333333"];
        self.menuImageView.tintColor = [UIColor colorWithHexString:@"#333333"];
    }
}

- (void)setupObservers
{
}

- (void)addSubviews
{
    [self.contentView addSubview:self.logo];
    [self.contentView addSubview:self.titleLabel];
    [self.contentView addSubview:self.menuImageView];
    [self.contentView addSubview:self.line];
}

- (void)defineLayout
{
    [self.logo mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(iPadValue(15, 10));
        make.centerY.mas_offset(0);
        make.size.mas_equalTo(iPadValue(30, 22));
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_offset(0);
        make.left.equalTo(self.logo.mas_right).offset(iPadValue(15, 10));
        make.right.equalTo(self.contentView).offset(-5-iPadValue(30, 22));
    }];
    
    [self.menuImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_offset(0);
        make.right.mas_offset(-10);
        make.size.mas_equalTo(iPadValue(30, 22));
    }];
    
    [self.line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.bottom.mas_offset(0);
        make.height.mas_equalTo(1.0);
        make.left.equalTo(self.titleLabel);
    }];
}

#pragma mark -- Getters

- (UILabel *)titleLabel
{
    if(!_titleLabel) {
        float font = iPadValue(18, 14);
        _titleLabel = [UIView createLabelWithTitle:@""
                                         textColor:[UIColor colorWithHexString:@"#1A1A1A"]
                                           bgColor:UIColor.clearColor
                                          fontSize:font
                                     textAlignment:NSTextAlignmentLeft
                                             bBold:NO];
    }
    
    return _titleLabel;
}

- (UIImageView *)logo
{
    if(!_logo) {
        _logo = [UIImageView new];
    }
    
    return _logo;
}

- (UIView *)line
{
    if(!_line) {
        _line = [UIView new];
    }
    
    return _line;
}

- (UIImageView *)menuImageView
{
    if(!_menuImageView) {
        _menuImageView = [UIImageView new];
        _menuImageView.image = [UIImage ext_systemImageNamed:@"ellipsis.circle"
                                                   pointSize:iPadValue(30, 22)
                                                  renderMode:UIImageRenderingModeAlwaysTemplate];
        _menuImageView.hidden = YES;
    }
    
    return _menuImageView;
}

@end
