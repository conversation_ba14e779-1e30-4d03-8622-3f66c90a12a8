//
//  UserScriptPopUpViewController.m
//  PPBrowser
//
//  Created by qingbin on 2024/4/28.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import "UserScriptPopUpViewController.h"

#import "UserScriptPopUpCell.h"

#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "PPNotifications.h"

#import "PreferenceContentView.h"
#import "NSObject+Helper.h"
#import "UIColor+Helper.h"

#import "BrowserViewController.h"
#import "UserScriptPopUpSettingModel.h"

@interface UserScriptPopUpViewController ()<UITableViewDelegate,UITableViewDataSource,ThemeProtocol>

@property (nonatomic, strong) UITableView* tableView;

@property (nonatomic, strong) NSMutableArray* model;

@property (nonatomic, strong) NSMutableArray* settingModel;

@property (nonatomic, copy) void (^clickAction)(UserScript *script, UserScriptPopUpSettingModel* settingModel, UIViewController* viewController);

@property (nonatomic, weak) UserCommandManager *userCommandManager;

@end

@implementation UserScriptPopUpViewController

- (void)viewDidLoad
{
    [super viewDidLoad];
    
    [self addSubviews];
    [self defineLayout];
    [self setupObservers];
    [self updateWithModel];

    [self applyTheme];
}

- (void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:animated];
}

+ (void)showAt:(BrowserViewController *)controller
    sourceRect:(CGRect)sourceRect
userScriptArray:(NSArray<UserScript*>*)userScriptArray
userCommandManager:(UserCommandManager *)userCommandManager
   clickAction:(void (^)(UserScript *script, UserScriptPopUpSettingModel* settingModel, UIViewController* viewController))clickAction
{
    UserScriptPopUpViewController* vc = [UserScriptPopUpViewController new];
    [vc.model addObjectsFromArray:userScriptArray];
    vc.clickAction = clickAction;
    
    vc.userCommandManager = userCommandManager;
    
    vc.modalPresentationStyle = UIModalPresentationPopover;
    vc.popoverPresentationController.sourceView = controller.view;
    vc.popoverPresentationController.sourceRect = sourceRect;
    vc.popoverPresentationController.permittedArrowDirections = UIPopoverArrowDirectionAny;
    
    vc.popoverPresentationController.delegate = vc;
    
    [controller presentViewController:vc animated:YES completion:nil];
}

- (CGSize)preferredContentSize
{
    return [self popUpSize];
}

- (CGSize)popUpSize
{
    float width = MIN(270, ceil(0.8*kScreenWidth));
    if([BrowserUtils isiPhone]) {
        //横屏iPhone
    } else {
        //iPad或者其他
        width = MIN(580, ceil(0.8*kScreenWidth));
    }
    
    float cellHeight = [UserScriptPopUpViewController cellHeight];
    float height = 0;
    if(self.model.count == 0) {
        height = cellHeight*2 + 15;
    } else {
        //最多显示8个
        height = MIN(self.model.count, 8) * cellHeight + 10/*中间间距*/ + cellHeight*2 + 15;
    }
    
    return CGSizeMake(width, height);
}

+ (float)cellHeight
{
    return iPadValue(60, 40);
}

- (void)updateWithModel
{
    [self.settingModel removeAllObjects];
    //更多脚本
    UserScriptPopUpSettingModel* item = [UserScriptPopUpSettingModel moreScriptModel];
    [self.settingModel addObject:item];
    //脚本管理页
    item = [UserScriptPopUpSettingModel scriptSettingModel];
    [self.settingModel addObject:item];
    
    item = self.settingModel.firstObject;
    item.isFirstInSection = YES;

    item = self.settingModel.lastObject;
    item.isLastInSection = YES;
    
    UserScript* script = self.model.firstObject;
    script.isFirstInSection = YES;
    
    script = self.model.lastObject;
    script.isLastInSection = YES;
    
    [self.tableView reloadData];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
    } else {
        self.view.backgroundColor = [UIColor colorWithHexString:@"#FFFFFF"];
    }
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    //只有当前的controller会触发一次, 其它已存在的controller走通知
    if(needReload) {
        //修改暗黑模式
        [self applyTheme];
        [self.tableView reloadData];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
    [self.tableView reloadData];
}

#pragma mark -- 事件处理

- (void)setupObservers
{
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(_updateCommandWithNotification:)
                                                 name:kupdateCommandNotification
                                               object:nil];
}

#pragma mark -- 更新菜单栏
- (void)_updateCommandWithNotification:(NSNotification *)notification
{
    NSDictionary* infos = notification.userInfo;
    NSString* scriptId = infos[@"scriptId"];
    if(scriptId.length <= 0) return;
    
    UserScript* script = nil;
    for(UserScript* item in self.model) {
        if([item.uuid isEqualToString:scriptId]) {
            script = item;
            break;
        }
    }
    
    if(!script) return;
    
    //已经添加属性观察器
    script.commands = [self.userCommandManager getMenusWithScriptId:scriptId];
}

#pragma mark -- layout

- (void)addSubviews
{
    [self.view addSubview:self.tableView];
}

- (void)defineLayout
{
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.view);
        make.top.bottom.equalTo(self.view);
    }];
}

#pragma mark -- UIPopoverPresentationControllerDelegate

- (UIModalPresentationStyle)adaptivePresentationStyleForPresentationController:(UIPresentationController *)controller
{
    return UIModalPresentationNone;
}

#pragma mark - UITableViewDataSource

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    if(section == 0) {
        return self.model.count;
    } else {
        return self.settingModel.count;
    }
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 2;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    if(indexPath.section == 0) {
        UserScript* item = self.model[indexPath.row];
        UserScriptPopUpCell *cell =  [tableView dequeueReusableCellWithIdentifier:NSStringFromClass(UserScriptPopUpCell.class)];
        [cell updateWithModel:item];
        
        return cell;
    } else {
        UserScriptPopUpSettingModel* item = self.settingModel[indexPath.row];
        UserScriptPopUpCell *cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass(UserScriptPopUpCell.class)];
        [cell updateWithSettingModel:item];
        
        return cell;
    }
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section
{
    if(section == 0 && self.model.count > 0) {
        return 10.0f;
    }
    
    return CGFLOAT_MIN;
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section
{
    float height = CGFLOAT_MIN;
    if(section == 0 && self.model.count > 0) {
        height = 10.0f;
    }
    
    UIView* footer = [[UIView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, height)];
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        footer.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
    } else {
        footer.backgroundColor = [UIColor colorWithHexString:@"#F5F5F5"];
    }
    
    return footer;
}

#pragma mark - UITableViewDelegate

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    [tableView deselectRowAtIndexPath:indexPath animated:NO];

    if(indexPath.section == 0) {
        //脚本
        UserScript* item = self.model[indexPath.row];
        if(self.clickAction) {
            self.clickAction(item, nil, self);
        }
    } else {
        //设置
        UserScriptPopUpSettingModel* item = self.settingModel[indexPath.row];
        if(self.clickAction) {
            self.clickAction(nil, item, self);
        }
    }
}

#pragma mark -- Getters

- (UITableView *)tableView
{
    if(!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStyleGrouped];

        _tableView.separatorStyle  = UITableViewCellSeparatorStyleNone;
        _tableView.showsHorizontalScrollIndicator = NO;
        _tableView.showsVerticalScrollIndicator   = NO;
        _tableView.delegate   = self;
        _tableView.dataSource = self;
        _tableView.rowHeight = [UserScriptPopUpViewController cellHeight];
        _tableView.backgroundColor = UIColor.clearColor;
        
        [_tableView registerClass:[UserScriptPopUpCell class] forCellReuseIdentifier:NSStringFromClass([UserScriptPopUpCell class])];
        
        _tableView.tableHeaderView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, 20)];
        _tableView.tableFooterView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, CGFLOAT_MIN)];
        
        _tableView.sectionHeaderHeight = 0.0;
    }
    
    return _tableView;
}

- (NSMutableArray *)model
{
    if(!_model) {
        _model = [NSMutableArray array];
    }
    
    return _model;
}

- (NSMutableArray *)settingModel
{
    if(!_settingModel) {
        _settingModel = [NSMutableArray array];
    }
    
    return _settingModel;
}

@end
