//
//  UserScriptPopUpViewController.h
//  PPBrowser
//
//  Created by qingbin on 2024/4/28.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import "BaseViewController.h"
#import "UserScript.h"
#import "UserScriptPopUpSettingModel.h"
#import "UserCommandManager.h"

@class BrowserViewController;

@interface UserScriptPopUpViewController : BaseViewController<UIPopoverPresentationControllerDelegate>

//userScriptArray: 正在运行的脚步数组
+ (void)showAt:(BrowserViewController *)controller
    sourceRect:(CGRect)sourceRect
userScriptArray:(NSArray<UserScript*>*)userScriptArray
userCommandManager:(UserCommandManager *)userCommandManager
   clickAction:(void (^)(UserScript *script, UserScriptPopUpSettingModel* settingModel, UIViewController* viewController))clickAction;

@end


