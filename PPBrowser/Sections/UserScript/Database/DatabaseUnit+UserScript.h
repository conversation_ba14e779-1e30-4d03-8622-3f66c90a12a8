//
//  DatabaseUnit+UserScript.h
//  PPBrowser
//
//  Created by qingbin on 2022/5/24.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "DatabaseUnit.h"
#import "UserScript.h"

@interface DatabaseUnit (UserScript)

// 添加一个脚本
+ (DatabaseUnit*)addUserScriptWithItem:(UserScript*)item;

// 删除一个脚本
+ (DatabaseUnit*)removeUserScriptWithId:(NSString*)uuid;

// 更新是否激活状态
+ (DatabaseUnit*)updateUserScriptWithId:(NSString*)uuid
                               isActive:(NSInteger)isActive
                                 script:(UserScript *)script;

// 更新更改了标题/frame
//+ (DatabaseUnit*)updateUserScriptWithId:(NSString*)uuid
//                                   name:(NSString*)name
//                            selectFrame:(NSInteger)selectFrame
//                                 script:(UserScript *)script;

// 更新脚本代码
+ (DatabaseUnit*)updateUserScriptWithId:(NSString*)uuid
                                content:(NSString*)content
                                 script:(UserScript *)script;

// 更新顺序
+ (DatabaseUnit*)updateUserScriptWithId:(NSString*)uuid
                                ppOrder:(NSInteger)ppOrder
                                 script:(UserScript *)script;

// 更新是否自动更新
+ (DatabaseUnit*)updateUserScriptWithId:(NSString*)uuid
                           isAutoUpdate:(NSInteger)isAutoUpdate
                                 script:(UserScript *)script;

// 更新执行顺序
+ (DatabaseUnit*)updateUserScriptWithId:(NSString*)uuid
                            frameOption:(UserScriptFrameOption)frameOption
                                 script:(UserScript *)script;

//批量更新顺序
+ (DatabaseUnit*)updateUserScriptAllOrder:(NSArray*)allItems;

// 查询所有脚本
+ (DatabaseUnit*)queryAllUserScripts;

// 更新一个脚本
+ (DatabaseUnit*)updateUserScriptWithItem:(UserScript*)item;

// iCloud, 删除所有脚本
+ (DatabaseUnit*)removeAllUserScripts;

// CloudKit, 添加多个脚本, 同时插入多个脚本
+ (DatabaseUnit*)addUserScriptArray:(NSArray<UserScript*>*)items;

// CloudKit, 批量更新多个脚本
+ (DatabaseUnit*)updateUserScriptArray:(NSArray<UserScript*>*)array;

// CloudKit, 批量删除多个脚本
+ (DatabaseUnit*)removeUserScriptArray:(NSArray*)scriptIds;

@end

