//
//  DatabaseUnit+UserScript.m
//  PPBrowser
//
//  Created by qing<PERSON> on 2022/5/24.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "DatabaseUnit+UserScript.h"
#import "ReactiveCocoa.h"

#import "FMDatabaseQueue.h"
#import "FMDatabase.h"

#import "PPEnums.h"
#import "Tampermonkey.h"
#import "SyncEngine.h"
#import "CloudKitHelper.h"

@implementation DatabaseUnit (UserScript)

/**
 @"CREATE TABLE IF NOT EXISTS t_userscript(uuid TEXT PRIMARY KEY, name TEXT, author TEXT, namespace TEXT, version TEXT,  desc TEXT, iconUrl TEXT, runAt TEXT, selectFrame INTEGER, updateUrl TEXT, downloadUrl TEXT, content TEXT, isActive INTEGER, ppOrder INTEGER, sql_includes TEXT, sql_matches TEXT, sql_excludes TEXT, sql_grants TEXT, sql_requireUrls TEXT, sql_resourceUrls TEXT, ctime TEXT)";
 */
// 添加一个脚本
+ (DatabaseUnit*)addUserScriptWithItem:(UserScript*)item
{
    //解析脚本的时候已经加入到内存缓存, 所以不需要再在这里添加
    DatabaseUnit* unit = [DatabaseUnit new];
    
    [item jsonModelToSql];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)

        NSString* ctime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        item.ctime = ctime;
        item.updateTime = ctime;
        
        NSString* command = @"INSERT INTO t_userscript(uuid, name, author, version, namespace, desc, iconUrl, runAt, selectFrame, frameOption, isAutoUpdate, updateUrl, downloadUrl, content, isActive, ppOrder, sql_includes, sql_matches, sql_excludes, sql_grants, sql_requireUrls, sql_resourceUrls, whiteList, blackList, updateTime, ctime) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

        BOOL result = [db executeUpdate:command, item.uuid, item.name?:@"", item.author?:@"", item.version?:@"", item.namespace?:@"", item.desc?:@"", item.iconUrl?:@"", item.runAt, @(item.selectFrame), @(item.frameOption), @(item.isAutoUpdate), item.updateUrl?:@"", item.downloadUrl?:@"", item.content?:@"", @(item.isActive),@(item.ppOrder), item.sql_includes?:@"", item.sql_matches?:@"", item.sql_excludes?:@"",item.sql_grants?:@"", item.sql_requireUrls?:@"", item.sql_resourceUrls?:@"", item.whiteList?:@"", item.blackList?:@"", item.updateTime, item.ctime];
        
        if(result) {
            //同步到CloudKit中
            [[SyncEngine shareInstance] syncRecordsToCloudKit:@[[item toCKRecord]] recordIDsToDelete:nil completion:nil];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

// 删除一个脚本
+ (DatabaseUnit*)removeUserScriptWithId:(NSString*)uuid
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    //删除本地源码
    //删除本地的@require和@resource
    [[Tampermonkey shareInstance] removeJsFromScriptPathWithUUID:uuid];
    
    //内存缓存
    [[Tampermonkey shareInstance] removeUserScriptCacheByUUID:uuid];
    //删除数据库记录
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
    
        NSString* command = [NSString stringWithFormat:@"DELETE FROM t_userscript WHERE uuid=?;"];
        BOOL result = [db executeUpdate:command, uuid];

        if(result) {
            //同步到CloudKit中
            CKRecordID* recordID = [[CKRecordID alloc]initWithRecordName:uuid zoneID:[CloudKitHelper focusZoneID]];
            [[SyncEngine shareInstance] syncRecordsToCloudKit:nil recordIDsToDelete:@[recordID] completion:nil];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

// 查询所有脚本
+ (DatabaseUnit*)queryAllUserScripts
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = [NSString stringWithFormat:@"SELECT * FROM t_userscript ORDER BY ppOrder ASC, ctime DESC"];
        FMResultSet* set = [db executeQuery:command];
        
        NSMutableArray *array = [[NSMutableArray alloc] init];
        while([set next]) {
            UserScript* _item = [[UserScript alloc]initWithDictionary:[set resultDictionary] error:nil];
            
            if(_item.content.length > 0) {
                //已经写入了数据库
                //从sql初始化
                [_item jsonModelFromSql];
                
                //生成可执行文件
                [_item updateExecutorJs];
                
                //保存到内存缓存中
                [[Tampermonkey shareInstance] saveUserScriptToCache:_item];
                [array addObject:_item];
            } else {
                //旧数据，需要将脚本数据移植到数据库中
                //从scriptPath中解析脚本, 然后赋值数据库的字段给UserScript, UserScript中的字段是从源码js解析出来的
//                UserScript* userScript = [[Tampermonkey shareInstance] loadJsWithUUID:_item.uuid
//                                                                                 name:_item.name
//                                                                             isActive:@(_item.isActive)
//                                                                          selectFrame:@(_item.selectFrame)];
                
                [[Tampermonkey shareInstance] loadJsWithUUID:_item.uuid
                                                  completion:^(UserScript *userScript) {
                    if(!userScript) {
                        //生成错误的脚本，把数据删掉
                        NSString* command = [NSString stringWithFormat:@"DELETE FROM t_userscript WHERE uuid=?;"];
                        [db executeUpdate:command,_item.uuid];
                        
                        //删除旧数据(包括@require和@resource)
                        [[Tampermonkey shareInstance] removeJsFromScriptPathWithUUID:_item.uuid];
                        
                        return;
                    }
                    
                    userScript.name = _item.name;
                    userScript.isActive = _item.isActive;
                    userScript.selectFrame = _item.selectFrame;
                    
                    //保存到内存缓存中
                    [[Tampermonkey shareInstance] saveUserScriptToCache:userScript];
                    [array addObject:userScript];
                    
                    //保存到数据库
                    [userScript jsonModelToSql];
                    
                    NSString* command = @"UPDATE t_userscript SET name=?, author=?, version=?, namespace=?, desc=?, iconUrl=?, runAt=?, selectFrame=?, frameOption=?, isAutoUpdate=?, updateUrl=?, downloadUrl=?, content=?, isActive=?, ppOrder=?, sql_includes=?, sql_matches=?, sql_excludes=?, sql_grants=?, sql_requireUrls=?, sql_resourceUrls=?, whiteList=?, blackList=?, updateTime=?, ctime=? WHERE uuid=?;";

                    [db executeUpdate:command, userScript.name?:@"", userScript.author?:@"", userScript.version?:@"", userScript.namespace?:@"", userScript.desc?:@"", userScript.iconUrl?:@"", userScript.runAt, @(userScript.selectFrame), @(userScript.frameOption), @(userScript.isAutoUpdate), userScript.updateUrl?:@"", userScript.downloadUrl?:@"", userScript.content?:@"", @(userScript.isActive), @(userScript.ppOrder), userScript.sql_includes?:@"", userScript.sql_matches?:@"", userScript.sql_excludes?:@"", userScript.sql_grants?:@"", userScript.sql_requireUrls?:@"", userScript.sql_resourceUrls?:@"", userScript.whiteList?:@"", userScript.blackList?:@"", _item.ctime?:@"0", _item.ctime?:@"0", userScript.uuid];
                    
                    //删除旧数据
                    [[Tampermonkey shareInstance] removeUserScriptFileWithUUID:userScript.uuid];
                }];
            }
        }
                
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(array, YES);
            }
        });
    };
    
    return unit;
}

// 更新是否激活状态
+ (DatabaseUnit*)updateUserScriptWithId:(NSString*)uuid
                               isActive:(NSInteger)isActive
                                 script:(UserScript *)script
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* time = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        
        NSString* command = [NSString stringWithFormat:@"UPDATE t_userscript SET isActive = ?, updateTime = ? WHERE uuid=?;"];
        BOOL result = [db executeUpdate:command, @(isActive), time, uuid];
        
        if(result) {
            //同步到CloudKit中
            CKRecord* record = [script toDefaultCKRecord];
            record[@"isActive"] = @(isActive);
            record[@"updateTime"] = time;
            [[SyncEngine shareInstance] syncRecordsToCloudKit:@[record] recordIDsToDelete:nil completion:nil];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil,result);
            }
        });
    };
    
    return unit;
}

// 更新更改了标题/frame
//+ (DatabaseUnit*)updateUserScriptWithId:(NSString*)uuid
//                                   name:(NSString*)name
//                            selectFrame:(NSInteger)selectFrame
//                                 script:(UserScript *)script
//{
//    DatabaseUnit* unit = [DatabaseUnit new];
//    
//    @weakify(unit)
//    unit.executeBlock = ^(FMDatabase *db) {
//        @strongify(unit)
//        NSString* time = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
//        
//        NSString* command = [NSString stringWithFormat:@"UPDATE t_userscript SET name = ?,selectFrame = ?, updateTime = ? WHERE uuid=?;"];
//        BOOL result = [db executeUpdate:command, name, @(selectFrame), time, uuid];
//        
//        if(result) {
//            //同步到CloudKit中
//            CKRecord* record = [script toDefaultCKRecord];
//            record[@"name"] = name?:@"";
//            record[@"selectFrame"] = @(selectFrame);
//            record[@"updateTime"] = time;
//            [[SyncEngine shareInstance] syncRecordsToCloudKit:@[record] recordIDsToDelete:nil completion:nil];
//        }
//        
//        dispatch_async(dispatch_get_main_queue(), ^{
//            if(unit.completeBlock) {
//                unit.completeBlock(nil,result);
//            }
//        });
//    };
//    
//    return unit;
//}

// 更新脚本代码
+ (DatabaseUnit*)updateUserScriptWithId:(NSString*)uuid
                                content:(NSString*)content
                                 script:(UserScript *)script
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* time = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        
        NSString* command = [NSString stringWithFormat:@"UPDATE t_userscript SET content = ?, updateTime = ? WHERE uuid=?;"];
        BOOL result = [db executeUpdate:command, content, time, uuid];
        
        if(result) {
            //同步到CloudKit中
            CKRecord* record = [script toDefaultCKRecord];
            record[@"content"] = content?:@"";
            record[@"updateTime"] = time;
            [[SyncEngine shareInstance] syncRecordsToCloudKit:@[record] recordIDsToDelete:nil completion:nil];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil,result);
            }
        });
    };
    
    return unit;
}

// 更新顺序
+ (DatabaseUnit*)updateUserScriptWithId:(NSString*)uuid
                                ppOrder:(NSInteger)ppOrder
                                 script:(UserScript *)script
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* time = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        NSString* command = [NSString stringWithFormat:@"UPDATE t_userscript SET ppOrder = ?, updateTime = ?, ctime = ? WHERE uuid=?;"];
        BOOL result = [db executeUpdate:command, @(ppOrder), time, time, uuid];
        
        if(result) {
            //同步到CloudKit中
            CKRecord* record = [script toDefaultCKRecord];
            record[@"ppOrder"] = @(ppOrder);
            record[@"ctime"] = time;
            record[@"updateTime"] = time;
            [[SyncEngine shareInstance] syncRecordsToCloudKit:@[record] recordIDsToDelete:nil completion:nil];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil,result);
            }
        });
    };
    
    return unit;
}

//批量更新顺序
+ (DatabaseUnit*)updateUserScriptAllOrder:(NSArray*)allItems
{
    DatabaseUnit* unit = [DatabaseUnit new];
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        BOOL result = YES;
        NSString* time = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        
        for(int i=0;i<allItems.count;i++) {
            UserScript* item = allItems[i];
            item.ppOrder = i;
            NSString* command = @"UPDATE t_userscript SET ppOrder=? , updateTime = ? WHERE uuid=?";
            result = [db executeUpdate:command, @(item.ppOrder), time, item.uuid] && result;
        }

        if(result) {
            //同步到CloudKit中
            NSMutableArray* records = [NSMutableArray array];
            for(UserScript* item in allItems) {
                CKRecord* record = [item toDefaultCKRecord];
                record[@"ppOrder"] = @(item.ppOrder);
                record[@"updateTime"] = time;
                
                [records addObject:record];
            }
            
            [[SyncEngine shareInstance] syncRecordsToCloudKit:records recordIDsToDelete:nil completion:nil];
        }
        
        if(unit.completeBlock) {
            unit.completeBlock(nil, result);
        }
    };
    
    return unit;
}

// 更新一个脚本
+ (DatabaseUnit*)updateUserScriptWithItem:(UserScript*)item
{
    //CREATE TABLE IF NOT EXISTS t_userscript(uuid TEXT PRIMARY KEY, name TEXT, author TEXT, version TEXT,  desc TEXT, icon TEXT, runAt TEXT, selectFrame INTEGER, updateUrl TEXT, downloadUrl TEXT, content TEXT, isActive INTEGER, sql_includes TEXT, sql_matches TEXT, sql_excludes TEXT, sql_grants TEXT, sql_requireUrls TEXT, resourceUrls TEXT, ctime TEXT)
    
    //解析脚本的时候已经加入到内存缓存, 所以不需要再在这里添加
    DatabaseUnit* unit = [DatabaseUnit new];
    
    [item jsonModelToSql];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* time = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        
        NSString* command = @"UPDATE t_userscript SET name=?, author=?, version=?, namespace=?, desc=?, iconUrl=?, runAt=?, selectFrame=?, frameOption=?, isAutoUpdate=?, updateUrl=?, downloadUrl=?, content=?, isActive=?, ppOrder=?, sql_includes=?, sql_matches=?, sql_excludes=?, sql_grants=?, sql_requireUrls=?, sql_resourceUrls=?, whiteList=?, blackList=?, updateTime=?, ctime=? WHERE uuid=?;";

        BOOL result = [db executeUpdate:command, item.name?:@"", item.author?:@"", item.version?:@"", item.namespace?:@"", item.desc?:@"", item.iconUrl?:@"", item.runAt, @(item.selectFrame), @(item.frameOption), @(item.isAutoUpdate), item.updateUrl?:@"", item.downloadUrl?:@"", item.content?:@"", @(item.isActive), @(item.ppOrder), item.sql_includes?:@"", item.sql_matches?:@"", item.sql_excludes?:@"", item.sql_grants?:@"", item.sql_requireUrls?:@"", item.sql_resourceUrls?:@"", item.whiteList?:@"", item.blackList?:@"", time, time, item.uuid];
        
        if(result) {
            //同步到CloudKit中
            [[SyncEngine shareInstance] syncRecordsToCloudKit:@[[item toCKRecord]] recordIDsToDelete:nil completion:nil];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

// 更新是否自动更新
+ (DatabaseUnit*)updateUserScriptWithId:(NSString*)uuid
                           isAutoUpdate:(NSInteger)isAutoUpdate
                                 script:(UserScript *)script
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* updateTime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        script.updateTime = updateTime;
        
        NSString* command = [NSString stringWithFormat:@"UPDATE t_userscript SET isAutoUpdate = ?, updateTime = ? WHERE uuid=?;"];
        BOOL result = [db executeUpdate:command, @(isAutoUpdate),  updateTime, uuid];
        
        if(result) {
            //同步到CloudKit中
            CKRecord* record = [script toDefaultCKRecord];
            record[@"isAutoUpdate"] = @(isAutoUpdate);
            record[@"updateTime"] = updateTime;
            [[SyncEngine shareInstance] syncRecordsToCloudKit:@[record] recordIDsToDelete:nil completion:nil];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil,result);
            }
        });
    };
    
    return unit;
}

// 更新执行顺序
+ (DatabaseUnit*)updateUserScriptWithId:(NSString*)uuid
                            frameOption:(UserScriptFrameOption)frameOption
                                 script:(UserScript *)script
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* updateTime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        script.updateTime = updateTime;
        
        NSString* command = [NSString stringWithFormat:@"UPDATE t_userscript SET frameOption = ?, updateTime = ? WHERE uuid=?;"];
        BOOL result = [db executeUpdate:command, @(frameOption),  updateTime, uuid];
        
        if(result) {
            //同步到CloudKit中
            CKRecord* record = [script toDefaultCKRecord];
            record[@"frameOption"] = @(frameOption);
            record[@"updateTime"] = updateTime;
            [[SyncEngine shareInstance] syncRecordsToCloudKit:@[record] recordIDsToDelete:nil completion:nil];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil,result);
            }
        });
    };
    
    return unit;
}

#pragma mark -- iCloud相关操作

// iCloud, 删除所有脚本
+ (DatabaseUnit*)removeAllUserScripts
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        
        NSString* command = [NSString stringWithFormat:@"DELETE FROM t_userscript"];
        BOOL result = [db executeUpdate:command];

        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

// CloudKit, 添加多个节点, 同时插入多个节点
+ (DatabaseUnit*)addUserScriptArray:(NSArray<UserScript*>*)items
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = @"INSERT INTO t_userscript(uuid, name, author, version, namespace, desc, iconUrl, runAt, selectFrame, frameOption, isAutoUpdate, updateUrl, downloadUrl, content, isActive, ppOrder, sql_includes, sql_matches, sql_excludes, sql_grants, sql_requireUrls, sql_resourceUrls, whiteList, blackList, updateTime, ctime) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

        BOOL result = YES;
        for(int i=0;i<items.count;i++) {
            UserScript* item = items[i];
            
            result = [db executeUpdate:command, item.uuid, item.name?:@"", item.author?:@"", item.version?:@"", item.namespace?:@"", item.desc?:@"", item.iconUrl?:@"", item.runAt, @(item.selectFrame), @(item.frameOption), @(item.isAutoUpdate), item.updateUrl?:@"", item.downloadUrl?:@"", item.content?:@"", @(item.isActive), @(item.ppOrder), item.sql_includes?:@"", item.sql_matches?:@"", item.sql_excludes?:@"",item.sql_grants?:@"", item.sql_requireUrls?:@"", item.sql_resourceUrls?:@"", item.whiteList?:@"", item.blackList?:@"", item.updateTime?:@"1", item.ctime];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

// CloudKit, 批量更新多个脚本
+ (DatabaseUnit*)updateUserScriptArray:(NSArray<UserScript*>*)array
{    
    //解析脚本的时候已经加入到内存缓存, 所以不需要再在这里添加
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        BOOL result = NO;
        for(UserScript* item in array) {
            [item jsonModelToSql];
            
            NSString* command = @"UPDATE t_userscript SET name=?, author=?, version=?, namespace=?, desc=?, iconUrl=?, runAt=?, selectFrame=?, frameOption=?, isAutoUpdate=?, updateUrl=?, downloadUrl=?, content=?, isActive=?, ppOrder=?, sql_includes=?, sql_matches=?, sql_excludes=?, sql_grants=?, sql_requireUrls=?, sql_resourceUrls=?, whiteList=?, blackList=?, updateTime=?, ctime=? WHERE uuid=?;";

            result = [db executeUpdate:command, item.name?:@"", item.author?:@"", item.version?:@"", item.namespace?:@"", item.desc?:@"", item.iconUrl?:@"", item.runAt, @(item.selectFrame), @(item.frameOption), @(item.isAutoUpdate), item.updateUrl?:@"", item.downloadUrl?:@"", item.content?:@"", @(item.isActive), @(item.ppOrder), item.sql_includes?:@"", item.sql_matches?:@"", item.sql_excludes?:@"",item.sql_grants?:@"", item.sql_requireUrls?:@"", item.sql_resourceUrls?:@"", item.whiteList?:@"", item.blackList?:@"", item.updateTime?:@"1", item.ctime, item.uuid];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

// CloudKit, 批量删除多个脚本
+ (DatabaseUnit*)removeUserScriptArray:(NSArray*)scriptIds
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = [NSString stringWithFormat:@"DELETE FROM t_userscript WHERE uuid=?;"];

        BOOL result = YES;
        for(int i=0;i<scriptIds.count;i++) {
            NSString* scriptId = scriptIds[i];
            result = [db executeUpdate:command, scriptId];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}


@end
