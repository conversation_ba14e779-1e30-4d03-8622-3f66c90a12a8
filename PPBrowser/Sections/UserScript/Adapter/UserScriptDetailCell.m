//
//  UserScriptDetailCell.m
//  Saber
//
//  Created by qingbin on 2023/2/15.
//

#import "UserScriptDetailCell.h"

#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "UIView+FrameHelper.h"
#import "NSObject+Helper.h"

#import "UIImageView+WebCache.h"

#import "ThemeProtocol.h"
#import "BrowserUtils.h"

#import "CustomTitleView.h"
#import "CustomTitleAndImageView.h"

@interface UserScriptDetailCell ()<ThemeProtocol>

@property (nonatomic, strong) UserScript* model;

@property (nonatomic, strong) UIImageView* logo;

@property (nonatomic, strong) UIStackView* stackView;

@property (nonatomic, strong) UIView* backView;

@property (nonatomic, strong) UISwitch* switchView;

@property (nonatomic, strong) UILabel* descLabel;

@property (nonatomic, strong) CustomTitleView* titleView;

@property (nonatomic, strong) UIStackView* bottomStackView;

@property (nonatomic, strong) UILabel* versionLabel;

@property (nonatomic, strong) CustomTitleAndImageView* menuButton;

@property (nonatomic, strong) RACDisposable *commandDisposable;

@end

@implementation UserScriptDetailCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        
        float radius = iPadValue(20, 10);
        self.backView.layer.cornerRadius = radius;
        self.backView.layer.masksToBounds = YES;
        
        [self addSubviews];
        [self defineLayout];
        [self setupObservers];
    }
    
    return self;
}

- (void)updateWithModel:(UserScript*)model
{
    self.model = model;

    [self.titleView updateWithTitle:model.name status:model.titleStatus updateStatus:model.updateStatus];
    self.descLabel.text = model.desc;
    
    NSString* version = NSLocalizedString(@"userscript.empty.version", nil);
    if(model.version.length > 0) version = model.version;
    NSString* versionTitle = NSLocalizedString(@"userscript.version", nil);
    self.versionLabel.text = [NSString stringWithFormat:@"%@: %@", versionTitle, version];
        
    self.switchView.on = model.isActive;
    
    if(model.iconUrl.length > 0) {
        UIImage* image = [UIImage imageNamed:@"fail"];
        NSURL* URL = [NSURL URLWithString:model.iconUrl];
        
        [self.logo sd_setImageWithURL:URL placeholderImage:image];
    } else {
        //https://www.iconfont.cn/search/index?searchType=icon&q=%E8%84%9A%E6%9C%AC&page=12&fromCollection=-1
        //https://www.iconfont.cn/search/index?searchType=icon&q=%E8%84%9A%E6%9C%AC&page=30&fromCollection=-1
        //7ab5f7
        self.logo.image = [UIImage imageNamed:@"script_default_icon"];
    }
    
    //菜单项
    self.menuButton.hidden = model.commands.count==0;
    //先解绑属性观察
    if(self.commandDisposable) {
        [self.commandDisposable dispose];
    }
    @weakify(self)
    self.commandDisposable = [RACObserve(self.model, commands) subscribeNext:^(id x) {
        @strongify(self)
        //菜单项
        self.menuButton.hidden = model.commands.count==0;
    }];
    
    [self applyTheme];
}

#pragma mark -- 暗黑模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.backView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        self.descLabel.textColor = [UIColor colorWithHexString:@"#666666"];
        self.versionLabel.textColor = [UIColor colorWithHexString:@"#666666"];
        
        [self.menuButton updateWith:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            titleLabel.textColor = [UIColor colorWithHexString:@"#FFFFFF"];
            imageView.tintColor = [UIColor colorWithHexString:@"#FFFFFF"];
        }];
        self.menuButton.tintColor = [UIColor colorWithHexString:@"#FFFFFF"];
        self.menuButton.backgroundColor =  [[UIColor colorWithHexString:@"#999999"] colorWithAlphaComponent:0.6];
        
        self.menuButton.layer.borderWidth = 0.0;
        self.menuButton.layer.borderColor = [UIColor clearColor].CGColor;
    } else {
        self.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.backView.backgroundColor = UIColor.whiteColor;
        self.descLabel.textColor = [UIColor colorWithHexString:@"#999999"];
        self.versionLabel.textColor = [UIColor colorWithHexString:@"#999999"];

        [self.menuButton updateWith:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            titleLabel.textColor = [UIColor colorWithHexString:@"#333333"];
            imageView.tintColor = [UIColor colorWithHexString:@"#333333"];
        }];
        
        self.menuButton.tintColor = [UIColor colorWithHexString:@"#333333"];
        self.menuButton.backgroundColor = UIColor.whiteColor;
        
        self.menuButton.layer.borderWidth = 0.5;
        self.menuButton.layer.borderColor = [UIColor colorWithHexString:@"#e2e2e2"].CGColor;
    }
    
    [self.titleView applyTheme];
}

- (void)setupObservers
{
    @weakify(self)
    [[self.switchView rac_newOnChannel] subscribeNext:^(id x) {
        @strongify(self)
        self.model.isActive = !self.model.isActive;
        if(self.didActiveAction) {
            self.didActiveAction(self.model);
        }
    }];
    
    [self.menuButton setTapAction:^{
        @strongify(self)
        if(self.menuAction) {
            self.menuAction(self.model);
        }
    }];
}

- (void)addSubviews
{
    [self.contentView addSubview:self.backView];
    [self.backView addSubview:self.logo];
    [self.backView addSubview:self.stackView];
    [self.backView addSubview:self.switchView];
    
    [self.backView addSubview:self.bottomStackView];
}

- (void)defineLayout
{
    [self.backView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(iPadValue(20, 15));
        make.right.mas_offset(-iPadValue(20, 15));
        make.top.bottom.equalTo(self.contentView);
    }];
    
    [self.logo mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(iPadValue(15, 10));
        make.top.mas_offset(iPadValue(15, 10));
        make.size.mas_equalTo(iPadValue(55, 40));
    }];
    
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.logo.mas_right).offset(iPadValue(15, 10));
        make.top.mas_offset(iPadValue(15, 10));
        make.right.equalTo(self.switchView.mas_left).offset(-iPadValue(10, 5));
    }];
    
    [self.bottomStackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_offset(-iPadValue(15, 10));
        make.left.equalTo(self.logo);
        make.height.mas_equalTo(22);
    }];
    
    [self.versionLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(self.bottomStackView);
    }];
    
    [self.menuButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(22);
    }];
    
    [self.switchView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.backView);
        make.right.mas_offset(-iPadValue(20, 10));
    }];
    if([BrowserUtils isiPad]) {
        //iPad
        self.switchView.transform = CGAffineTransformMakeScale(1.2, 1.2);
    }
    
    [self.switchView setContentCompressionResistancePriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    [self.switchView setContentHuggingPriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    
    [self.stackView setContentCompressionResistancePriority:UILayoutPriorityDefaultHigh forAxis:UILayoutConstraintAxisHorizontal];
    [self.stackView setContentHuggingPriority:UILayoutPriorityDefaultHigh forAxis:UILayoutConstraintAxisHorizontal];
}

- (UILabel *)descLabel
{
    if(!_descLabel) {
        float font = iPadValue(16, 12);
        _descLabel = [UIView createLabelWithTitle:@""
                                         textColor:[UIColor colorWithHexString:@"#999999"]
                                           bgColor:UIColor.clearColor
                                          fontSize:font
                                     textAlignment:NSTextAlignmentLeft
                                             bBold:NO];
        _descLabel.numberOfLines = 1;
    }
    
    return _descLabel;
}

- (UILabel *)versionLabel
{
    if(!_versionLabel) {
        float font = iPadValue(16, 12);
        _versionLabel = [UIView createLabelWithTitle:@""
                                         textColor:[UIColor colorWithHexString:@"#999999"]
                                           bgColor:UIColor.clearColor
                                          fontSize:font
                                     textAlignment:NSTextAlignmentLeft
                                             bBold:NO];
    }
    
    return _versionLabel;
}

- (UIStackView *)stackView
{
    if(!_stackView) {
        _stackView = [[UIStackView alloc]initWithArrangedSubviews:@[
            self.titleView,
            self.descLabel
        ]];
        
        _stackView.spacing = iPadValue(12, 10);
        _stackView.axis = UILayoutConstraintAxisVertical;
        _stackView.distribution = UIStackViewDistributionEqualSpacing;
    }
    
    return _stackView;
}

- (UISwitch *)switchView
{
    if(!_switchView) {
        _switchView = [[UISwitch alloc]init];
    }
    
    return _switchView;
}

- (UIView *)backView
{
    if(!_backView) {
        _backView = [UIView new];
        _backView.backgroundColor = UIColor.whiteColor;
    }
    
    return _backView;
}

- (UIImageView *)logo
{
    if(!_logo) {
        _logo = [UIImageView new];
    }
    
    return _logo;
}

- (CustomTitleView *)titleView
{
    if(!_titleView) {
        _titleView = [CustomTitleView new];
    }
    
    return _titleView;
}

- (UIStackView *)bottomStackView
{
    if(!_bottomStackView) {
        _bottomStackView = [[UIStackView alloc]initWithArrangedSubviews:@[
            self.versionLabel,
            self.menuButton
        ]];
        
        _bottomStackView.spacing = iPadValue(20, 10);
        _bottomStackView.axis = UILayoutConstraintAxisHorizontal;
        _bottomStackView.alignment = UIStackViewAlignmentLeading;
    }
    
    return _bottomStackView;
}

- (CustomTitleAndImageView *)menuButton
{
    if(!_menuButton) {
        _menuButton = [[CustomTitleAndImageView alloc]initWithLayout:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.mas_offset(10);
                make.centerY.mas_offset(0);
            }];
            
            [imageView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.right.mas_offset(-8);
                make.centerY.mas_offset(0);
                make.size.mas_equalTo(10);
                make.left.equalTo(titleLabel.mas_right).offset(2);
            }];
            
            imageView.image = [[UIImage imageNamed:@"standard_right_arrow"] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
            
            titleLabel.text = NSLocalizedString(@"userscript.command", nil);
            titleLabel.font = [UIFont systemFontOfSize:13];
            titleLabel.textAlignment = NSTextAlignmentLeft;
        }];
        
        _menuButton.layer.cornerRadius = 11;
        _menuButton.layer.masksToBounds = YES;
    }
    
    return _menuButton;
}

@end
