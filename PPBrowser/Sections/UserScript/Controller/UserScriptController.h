//
//  UserScriptController.h
//  PPBrowser
//
//  Created by qingbin on 2022/5/10.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "BaseViewController.h"
@class Tab;

NS_ASSUME_NONNULL_BEGIN

@interface UserScriptController : BaseViewController

- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)new NS_UNAVAILABLE;

- (instancetype)initWithTab:(Tab*)tab;

@end

NS_ASSUME_NONNULL_END


/*
 脚本测试案例
 1、GM_addValueChangeListener
 https://greasyfork.org/en/scripts/381682-html5%E8%A7%86%E9%A2%91%E6%92%AD%E6%94%BE%E5%99%A8%E5%A2%9E%E5%BC%BA%E8%84%9A%E6%9C%AC
 
 */
