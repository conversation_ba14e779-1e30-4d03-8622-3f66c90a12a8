//
//  UserScriptPreviewController.m
//  PPBrowser
//
//  Created by qingbin on 2023/11/30.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import "UserScriptPreviewController.h"

#import "UserScriptDetailCell.h"

#import "Masonry.h"
#import "MaizyHeader.h"
#import "BrowserUtils.h"

@interface UserScriptPreviewController ()

@end

@implementation UserScriptPreviewController

- (void)viewDidLoad
{
    [super viewDidLoad];

}

- (void)updateWithModel:(UserScript *)model
{
    UIView* view = nil;
    UserScriptDetailCell* cell = [[UserScriptDetailCell alloc]initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"cell"];
    [cell updateWithModel:model];
    
    view = cell;
    
    [self.view addSubview:view];
    view.frame = CGRectMake(0, 15, kScreenWidth, iPadValue(140, 92));
}

@end
