//
//  UserScriptController.m
//  PPBrowser
//
//  Created by qingbin on 2022/5/10.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "UserScriptController.h"

#import "MaizyHeader.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"

#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "NSObject+Helper.h"
#import "UIView+FrameHelper.h"

#import "UITableView+HintMessage.h"

#import "DatabaseUnit+UserScript.h"
#import "UserScript.h"
#import "Tampermonkey.h"
#import "UserScriptEditController.h"
#import "PPNotifications.h"

#import "Tab.h"
#import "URIFixup.h"

#import "ThemeProtocol.h"
#import "PreferenceManager.h"
#import "BrowserUtils.h"

#import <UniformTypeIdentifiers/UTCoreTypes.h>

#import "AFNetworking.h"
#import "AFNetworkReachabilityManager.h"

#import "UserScriptInputController.h"
#import "BaseNavigationController.h"
#import "UserScriptDetailCell.h"

#import "ExMatchURLHelper.h"
#import "UserCommandManager.h"

#import "UserScriptPreviewController.h"
#import "UserScriptUpdateManager.h"
#import "UserScriptHelper.h"

#import "UIImage+Extension.h"
#import "UserScriptDetailViewController.h"
#import "UIAlertController+SafePresentation.h"

@interface UserScriptController ()<UITableViewDelegate,UITableViewDataSource,ThemeProtocol,UITableViewDragDelegate,UITableViewDropDelegate,UIDocumentPickerDelegate>

@property (nonatomic, strong) UITableView* tableView;

@property (nonatomic, strong) NSMutableArray* model;

@property (nonatomic, weak) Tab *tab;

@property (nonatomic, strong) UIImageView* rightImageView;

@property (nonatomic, strong) dispatch_queue_t queue;

@end

@implementation UserScriptController

- (instancetype)initWithTab:(Tab*)tab
{
    self = [super init];
    if(self) {
        self.tab = tab;
        self.queue = dispatch_queue_create("com.userscript.queue", DISPATCH_QUEUE_CONCURRENT);
    }
    
    return self;
}

- (void)viewDidLoad
{
    [super viewDidLoad];
    
    self.navigationItem.title = NSLocalizedString(@"userscript.title", nil);
    self.navigationController.navigationBar.backgroundColor = UIColor.whiteColor;
    
    self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    
    [self createCustomLeftBarButtonItem];
    [self _createCustomRightBarButtonItem];
    
    [self addSubviews];
    [self defineLayout];
    [self setupObservers];

    [self applyTheme];
}

- (void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:animated];
    
    [self reloadData];
}

- (void)dealloc
{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.rightImageView.tintColor = UIColor.whiteColor;
    } else {
        self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.rightImageView.tintColor = [UIColor colorWithHexString:@"#222222"];
    }
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    //只有当前的controller会触发一次, 其它已存在的controller走通知
    if(needReload) {
        //修改暗黑模式
        [self applyTheme];
        [self.tableView reloadData];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
    [self.tableView reloadData];
}

- (void)reloadData
{
    [self.tableView hideHintMessage];

    DatabaseUnit* unit = [DatabaseUnit queryAllUserScripts];
    @weakify(self)
    [unit setCompleteBlock:^(NSArray* result, BOOL success) {
        @strongify(self)
        if(!self) return;
        if(success) {
            [self.model removeAllObjects];
            [self.model addObjectsFromArray:result];
            
            [self showHintMessageIfNeed];
            
            //判断当前脚本是否正在运行状态
            for(UserScript* item in self.model) {
                if(!item.isActive) {
                    //没有打开,默认
                    item.titleStatus = CustomTitleViewStatusDefault;
                } else {
                    //已经激活,查看是否在当前网站运行
                    if([ExMatchURLHelper matchesUserScript:item url:self.tab.webView.URL.absoluteString]) {
                        item.titleStatus = CustomTitleViewStatusWorking;
                    }
                    
                    //检测已注册的菜单栏
                    item.commands = [self.tab.commandManager getMenusWithScriptId:item.uuid];
                }
                
                //获取当前脚本更新的状态
                item.updateStatus = [[UserScriptUpdateManager shareInstance] getScriptUpdateStatusWithScriptId:item.uuid];
            }
            
            UserScript* firstItem = self.model.firstObject;
            firstItem.isFirstInSection = YES;
            UserScript* lastItem = self.model.lastObject;
            lastItem.isLastInSection = YES;
            
            [self.tableView reloadData];
        }
    }];
    
    DB_EXEC(unit);
}

- (void)showHintMessageIfNeed
{
    if(self.model.count == 0) {
        UIImage* image = [UIImage imageNamed:@"empty_data_logo"];
        [self.tableView showHintMessage:NSLocalizedString(@"tableview.emptyTips", nil)
                                  image:image
                          sectionMargin:iPadValue(120, 80)];
    } else {
        [self.tableView hideHintMessage];
    }
}

- (void)setupObservers
{
    //主要是导入分享脚本,如果当前脚本管理页是打开状态，那么前后台切换时则不会刷新脚本列表
    //添加脚本监听
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(_addUserScript:)
                                                 name:kAddUserScriptNotification
                                               object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(_updateCommandWithNotification:)
                                                 name:kupdateCommandNotification
                                               object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(updateData:)
                                                 name:kUpdateUserScriptNotification
                                               object:nil];
    
//    [[NSNotificationCenter defaultCenter] addObserver:self
//                                             selector:@selector(reloadData)
//                                                 name:kReloadUserScriptNotification
//                                               object:nil];
}

#pragma mark -- 更新数据
- (void)updateData:(NSNotification *)notification
{
    NSDictionary* infos = notification.userInfo;
    
    UserScript* script = infos[@"script"];
    BOOL needUpdate = infos[@"needUpdate"];
    
    //需要去除更新中的状态
//    if(!needUpdate) return;
    
    for(int i=0;i<self.model.count;i++) {
        UserScript* item = self.model[i];
        if([script.uuid isEqualToString:item.uuid]) {
            if(i == 0) {
                script.isFirstInSection = YES;
            }
            
            if(i==self.model.count-1) {
                script.isLastInSection = YES;
            }
            
            self.model[i] = script;
            [self.tableView reloadRowsAtIndexPaths:@[[NSIndexPath indexPathForRow:i inSection:0]] withRowAnimation:UITableViewRowAnimationNone];
            break;
        }
    }
}

#pragma mark -- 更新菜单栏
- (void)_updateCommandWithNotification:(NSNotification *)notification
{
    NSDictionary* infos = notification.userInfo;
    NSString* scriptId = infos[@"scriptId"];
    if(scriptId.length <= 0) return;
    
    UserScript* script = nil;
    for(UserScript* item in self.model) {
        if([item.uuid isEqualToString:scriptId]) {
            script = item;
            break;
        }
    }
    
    if(!script) return;
    
    //已经添加属性观察器
    script.commands = [self.tab.commandManager getMenusWithScriptId:scriptId];
}

#pragma mark -- 添加了一个脚本
- (void)_addUserScript:(NSNotification *)notification
{
    [self reloadData];
}

#pragma mark -- 导航栏
- (void)_createCustomRightBarButtonItem
{
    UIButton* rightButton = [[UIButton alloc] initWithFrame:CGRectMake(0.0, 0.0f, 44.0f, 44.0)];
    [rightButton setBackgroundColor:[UIColor clearColor]];

    UIImageView* imageView = [UIImageView new];
    
//    UIImage* image = [UIImage imageNamed:@"script_add_icon"];
//    image = [image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
    UIImage* image = [UIImage ext_systemImageNamed:@"ellipsis.circle"
                                         pointSize:24
                                        renderMode:UIImageRenderingModeAlwaysTemplate];
    imageView.image = image;
    self.rightImageView = imageView;
    
    [rightButton addSubview:imageView];
    [imageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(rightButton);
        make.size.mas_equalTo(28);
    }];
    
    [rightButton addTarget:self action:@selector(rightBarbuttonClick) forControlEvents:UIControlEventTouchUpInside];
    UIBarButtonItem* barItem = [[UIBarButtonItem alloc] initWithCustomView:rightButton];

    UIBarButtonItem *spacer = [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemFixedSpace target:nil action:nil];
    spacer.width = -16.0f; // for example shift right bar button to the right

    self.navigationItem.rightBarButtonItems = @[spacer, barItem];
}

- (void)rightBarbuttonClick
{
    UIAlertController* alertController = [UIAlertController alertControllerWithTitle:nil message:nil preferredStyle:UIAlertControllerStyleActionSheet];

//    UIPopoverPresentationController* popover = alertController.popoverPresentationController;
//    if(popover) {
//        popover.sourceView = self.view;
//                
//        CGRect sourceRect = [self.rightImageView.superview convertRect:self.rightImageView.frame toView:self.view];
//        popover.sourceRect = sourceRect;
//        popover.permittedArrowDirections = UIPopoverArrowDirectionAny;
//    }
    
    UIAlertAction *action = [UIAlertAction actionWithTitle:NSLocalizedString(@"userscript.add.directly", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        //添加脚本
        UserScriptEditController* vc = [[UserScriptEditController alloc]initWithNewScript];
        [self.navigationController pushViewController:vc animated:YES];
    }];
    [alertController addAction:action];

    action = [UIAlertAction actionWithTitle:NSLocalizedString(@"userscript.add.fromUrl", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [self _showAlertView2ImportURL];
    }];
    [alertController addAction:action];

    action = [UIAlertAction actionWithTitle:NSLocalizedString(@"userscript.add.fromFile", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [self _importUserscriptFromFiles];
    }];
    [alertController addAction:action];
    
    action = [UIAlertAction actionWithTitle:NSLocalizedString(@"userscript.add.fromGreasyfork", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [self _importUserscriptFromGreasyfork];
    }];
    [alertController addAction:action];
    
    action = [UIAlertAction actionWithTitle:NSLocalizedString(@"userscript.update.all", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [self _updateAllUserscripts];
    }];
    [alertController addAction:action];

    action = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.cancel", nil) style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
        [alertController dismissViewControllerAnimated:YES completion:nil];
    }];
    [alertController addAction:action];

//    [self presentViewController:alertController animated:YES completion:nil];
    CGRect sourceRect = [self.rightImageView.superview convertRect:self.rightImageView.frame toView:self.view];
    //v2.6.8, 统一present方法，防止崩溃
    [alertController presentSafelyFromViewController:self sourceView:self.view sourceRect:sourceRect];
}

#pragma mark -- 从链接导入
/// 显示弹窗获取脚本链接
- (void)_showAlertView2ImportURL
{
    UserScriptInputController* vc = [UserScriptInputController new];
    BaseNavigationController* navc = [[BaseNavigationController alloc]initWithRootViewController:vc];
    navc.view.backgroundColor = UIColor.whiteColor;
    
    @weakify(self)
    [vc setDidPasteUrlAction:^(NSString * url) {
        @strongify(self)
        [self _importUserscriptFromURL:url];
    }];
    
//    if ([BrowserUtils isiPad]) {
//        // iPad
//        navc.modalPresentationStyle = UIModalPresentationFormSheet;
//        vc.preferredContentSize = CGSizeMake(kScreenWidth-90, kScreenHeight-90);
//        
//        [self presentViewController:navc animated:YES completion:nil];
//    } else {
//        // iPhone
//        [self presentViewController:navc animated:YES completion:nil];
//    }
    //v2.6.8,统一present
    [self presentCustomToViewController:navc];
}

- (void)_importUserscriptFromURL:(NSString *)url
{
    if(url.length <= 0) {
        [UIView showFailed:NSLocalizedString(@"userscript.add.invalid.url", nil)];
        return;
    }
    

    AFHTTPSessionManager *manager = [AFHTTPSessionManager manager];
    manager.responseSerializer = [AFHTTPResponseSerializer serializer];
    
    [manager.requestSerializer setTimeoutInterval:20];
    
    [manager GET:url parameters:nil headers:nil progress:nil success:^(NSURLSessionDataTask * _Nonnull task, id  _Nullable responseObject) {
        //responseObject返回NSZeroData,也是空值,例如抖音
        //https://www.anycodings.com/1questions/2566953/how-to-check-if-id-object-is-null-in-ios
        
        NSData* data = (NSData*)responseObject;
        NSLog(@"data = %@, length = %lu",data, (unsigned long)data.length);
        
        BOOL isValid = YES;
        if(data.length == 0) {
            isValid = NO;
        }
        
        if(isValid) {
            if(responseObject == (id)[NSNull null]) {
                isValid = NO;
            }
        }
        
        if(isValid) {
            @try {
                NSString* scriptContent;
                if([responseObject isMemberOfClass:NSString.class]) {
                    scriptContent = responseObject;
                } else {
                    scriptContent = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
                }
                
                if(scriptContent.length > 0) {
                    [self _handleImportUserscript:scriptContent];
                }
            } @catch (NSException *exception) {
            } @finally {
            }
        } else {
            [UIView showFailed:NSLocalizedString(@"userscript.import.fail", nil)];
        }
    } failure:^(NSURLSessionDataTask * _Nullable task, NSError * _Nonnull error) {
        [UIView showFailed:NSLocalizedString(@"userscript.import.fail", nil)];
    }];
}

#pragma mark -- 从Greasyfork导入
- (void)_importUserscriptFromGreasyfork
{
    NSString* url = @"https://greasyfork.org/scripts?q=";
    NSURL* URL = [NSURL URLWithString:url];
    NSURLRequest* request = [NSURLRequest requestWithURL:URL];
    [self.tab loadRequest:request];
    
    [self dismissViewControllerAnimated:YES completion:nil];
}

#pragma mark -- 从文件导入
- (void)_importUserscriptFromFiles
{
    UIDocumentPickerViewController* vc;
    if (@available(iOS 14.0, *)) {
        vc = [[UIDocumentPickerViewController alloc]initForOpeningContentTypes:@[UTTypeJavaScript]];
    } else {
        //iOS13适配
        vc = [[UIDocumentPickerViewController alloc]initWithDocumentTypes:@[@"public.source-code", @"public.executable"] inMode:UIDocumentPickerModeOpen];
    }
    
    vc.delegate = self;
    [self presentViewController:vc animated:YES completion:nil];
}

#pragma mark -- 一键更新所有脚本
- (void)_updateAllUserscripts
{
    [[UserScriptUpdateManager shareInstance] updateUserScripts];
}

#pragma mark -- UIDocumentPickerDelegate
- (void)documentPicker:(UIDocumentPickerViewController *)controller didPickDocumentAtURL:(NSURL *)url
{
    BOOL shouldStopAccessing = [url startAccessingSecurityScopedResource];
    @try {
        NSError* error;
        NSString* scriptContent = [NSString stringWithContentsOfURL:url encoding:NSUTF8StringEncoding error:&error];
        if(scriptContent.length > 0) {
            [self _handleImportUserscript:scriptContent];
        }
    } @catch (NSException *exception) {
        
    } @finally {
        if(shouldStopAccessing) {
            [url stopAccessingSecurityScopedResource];
        }
    }
}

#pragma mark -- 导入脚本
- (void)_handleImportUserscript:(NSString *)scriptContent
{
    [UserScriptHelper installScriptFromFileWithJsContent:scriptContent tab:self.tab];
}

#pragma mark -- layout
- (void)addSubviews
{
    [self.view addSubview:self.tableView];
}

- (void)defineLayout
{
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_offset(0);
        make.left.right.equalTo(self.view);
        make.bottom.equalTo(self.view);
    }];
}

#pragma mark -- 长按预览窗口(长按更新)
- (UIContextMenuConfiguration *)tableView:(UITableView *)tableView contextMenuConfigurationForRowAtIndexPath:(NSIndexPath *)indexPath point:(CGPoint)point
{
    UserScript* item = self.model[indexPath.section];
    
    @weakify(self)
    UIContextMenuConfiguration *configuration = [UIContextMenuConfiguration configurationWithIdentifier:nil previewProvider:^UIViewController * _Nullable{
        // 根据需要更新预览窗口的内容
        UserScriptPreviewController* vc = [[UserScriptPreviewController alloc]init];
        vc.preferredContentSize = CGSizeMake(kScreenWidth, iPadValue(30, 15)*2 + iPadValue(140, 92));
        
        [vc updateWithModel:item];
        
        return vc;
    } actionProvider:^UIMenu * _Nullable(NSArray<UIMenuElement *> * _Nonnull suggestedActions) {
        @strongify(self)
        // 更新脚本
        @weakify(self)
        UIAction* updateScriptAction = [UIAction actionWithTitle:NSLocalizedString(@"userscript.update.script.title", nil) image:nil identifier:nil handler:^(__kindof UIAction * _Nonnull action) {
            @strongify(self)
            @weakify(self)
            [[UserScriptUpdateManager shareInstance] updateWithUserScript:item completion:^(BOOL needUpdate, UserScript *script) {
                @strongify(self)
                dispatch_async(dispatch_get_main_queue(), ^{
                    [[NSNotificationCenter defaultCenter] postNotificationName:kUpdateUserScriptNotification object:nil userInfo:@{
                        @"script" : script,
                        @"needUpdate" : @(needUpdate),
                    }];
                });
            }];
        }];
        
        //复制脚本链接
        UIAction* copyScriptAction = [UIAction actionWithTitle:NSLocalizedString(@"userscript.copy.script.url", nil) image:nil identifier:nil handler:^(__kindof UIAction * _Nonnull action) {
            NSString* url = [item getUpdateUrl];
            if(!url) {
                //复制失败
                [UIView showToast:NSLocalizedString(@"common.copy.fail", nil)];
            } else {
                //复制到剪贴板
                UIPasteboard *pasteboard = [UIPasteboard generalPasteboard];
                pasteboard.string = url;
                [UIView showToast:NSLocalizedString(@"common.copy.success", nil)];
            }
        }];
        
        //删除脚本
        UIAction* deleteAction = [UIAction actionWithTitle:NSLocalizedString(@"userscript.delete.script.title", nil) image:nil identifier:nil handler:^(__kindof UIAction * _Nonnull action) {
            @strongify(self)
            [self _deleteUserscriptAlertWithIndexPath:indexPath];
        }];
        deleteAction.attributes = UIMenuElementAttributesDestructive;
        
        UIMenu* deleteMenu = [UIMenu menuWithTitle:@"" image:nil identifier:nil options:UIMenuOptionsDisplayInline children:@[deleteAction]];
        
        return [UIMenu menuWithTitle:@"" children:@[updateScriptAction, copyScriptAction, deleteMenu]];
    }];
    
    return configuration;
}

#pragma mark - UITableViewDataSource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return 1;
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return self.model.count;
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section
{
    return iPadValue(20, 10);
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section
{
    UIView* footer = [[UIView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, iPadValue(20, 10))];
    footer.backgroundColor = UIColor.clearColor;
    
    return footer;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    UserScriptDetailCell *cell;
    UserScript* item = self.model[indexPath.section];
    
    //由于添加了默认图标，所以统一用UserScriptDetailCell
    cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass(UserScriptDetailCell.class)];

    [cell updateWithModel:item];
    
    @weakify(self)
    [cell setDidActiveAction:^(UserScript * _Nonnull item) {
        @strongify(self)
        //更新内存缓存标志
        UserScript* userScript = [[Tampermonkey shareInstance] getUserScriptCacheByUUID:item.uuid];
        userScript.isActive = item.isActive;
        [[Tampermonkey shareInstance] saveUserScriptToCache:userScript];
        
        //更新数据库标志位
        DatabaseUnit* unit = [DatabaseUnit updateUserScriptWithId:item.uuid isActive:item.isActive script:item];
        [unit setCompleteBlock:^(id result, BOOL success) {
            if(success) {
                //暂停/开启脚本
                [[NSNotificationCenter defaultCenter] postNotificationName:kReloadUserScriptNotification object:nil];
            }
        }];
        DB_EXEC(unit);
        
        [self reloadData];
    }];

    //点击了菜单栏
    [cell setMenuAction:^(UserScript *item) {
        @strongify(self)
        [tableView setEditing:NO animated:YES];//退出编辑模式，隐藏左滑菜单
    
        [self _showCommandMenu:item];
    }];
    
    return cell;
}

- (BOOL)tableView:(UITableView *)tableView canEditRowAtIndexPath:(NSIndexPath *)indexPath
{
    return YES;
}

// 只适用于ios11 以及以后版本
- (UISwipeActionsConfiguration *)tableView:(UITableView *)tableView trailingSwipeActionsConfigurationForRowAtIndexPath:(NSIndexPath *)indexPath  API_AVAILABLE(ios(11.0))
{
    // 删除数据
    @weakify(self)
    UIContextualAction *deleteAction = [UIContextualAction contextualActionWithStyle:UIContextualActionStyleDestructive title:NSLocalizedString(@"tableview.delete", nil) handler:^(UIContextualAction * _Nonnull action, __kindof UIView * _Nonnull sourceView, void (^ _Nonnull completionHandler)(BOOL)) {
        @strongify(self)
        [tableView setEditing:NO animated:YES];//退出编辑模式，隐藏左滑菜单
    
        [self _deleteUserscriptAlertWithIndexPath:indexPath];
    }];
    [deleteAction setBackgroundColor:UIColor.redColor];

    // 添加
    UISwipeActionsConfiguration *actions = [UISwipeActionsConfiguration configurationWithActions:@[deleteAction]];
    actions.performsFirstActionWithFullSwipe = NO;
    return actions;
}

#pragma mark -- 删除脚本
- (void)_deleteUserscriptAlertWithIndexPath:(NSIndexPath *)indexPath
{
    UIAlertController* alertController = [UIAlertController alertControllerWithTitle:NSLocalizedString(@"userscript.delete.alert.title", nil) message:nil preferredStyle:UIAlertControllerStyleAlert];
    UIAlertAction* action = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.cancel", nil) style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
        [alertController dismissViewControllerAnimated:YES completion:nil];
    }];
    [alertController addAction:action];
    
    action = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.confirm", nil) style:UIAlertActionStyleDestructive handler:^(UIAlertAction * _Nonnull action) {
        //确定
        [self _deleteUserscriptWithIndexPath:indexPath];
    }];
    [alertController addAction:action];
    
//    [self presentViewController:alertController animated:YES completion:nil];
    //v2.6.8, 统一present方法，防止崩溃
    [alertController presentSafelyFromViewController:self];
}

- (void)_deleteUserscriptWithIndexPath:(NSIndexPath *)indexPath
{
    UserScript* item = self.model[indexPath.section];

    //删除数据库数据+暂停脚本
    DatabaseUnit* unit = [DatabaseUnit removeUserScriptWithId:item.uuid];
    @weakify(self)
    [unit setCompleteBlock:^(id result, BOOL success) {
        @strongify(self)
        if(success) {
            [self.tableView beginUpdates];
            [self.model removeObject:item];
            [self.tableView deleteSections:[NSIndexSet indexSetWithIndex:indexPath.section] withRowAnimation:UITableViewRowAnimationFade];
            [self.tableView endUpdates];
            
            [self showHintMessageIfNeed];
            
            //重新加载脚本
            [[NSNotificationCenter defaultCenter] postNotificationName:kReloadUserScriptNotification object:nil];
        }
    }];
    
    DB_EXEC(unit);
}

- (void)_showCommandMenu:(UserScript *)model
{
    UIAlertController* alertController = [UIAlertController alertControllerWithTitle:nil message:nil preferredStyle:UIAlertControllerStyleAlert];
    UIAlertAction* action = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.cancel", nil) style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
        [alertController dismissViewControllerAnimated:YES completion:nil];
    }];
    [alertController addAction:action];

    @weakify(self)
    for(UserComandModel* item in model.commands) {
        UIAlertAction* action = [UIAlertAction actionWithTitle:item.name style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            @strongify(self)
            NSLog(@"点击了: %@", item.name);
            self.tab.commandManager.triggerCommandAction(item);
            [self dismissViewControllerAnimated:NO completion:nil];
        }];
        [alertController addAction:action];
    }
    
//    [self presentViewController:alertController animated:YES completion:nil];
    //v2.6.8, 统一present方法，防止崩溃
    [alertController presentSafelyFromViewController:self];
}

#pragma mark - UITableViewDelegate
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    [tableView deselectRowAtIndexPath:indexPath animated:NO];
    
    if(indexPath.section < self.model.count) {
        UserScript* item = self.model[indexPath.section];
        
//        UserScriptEditController* vc = [[UserScriptEditController alloc]initWithUserScript:item];
//        [self.navigationController pushViewController:vc animated:YES];
        
        UserScriptDetailViewController* vc = [[UserScriptDetailViewController alloc]initWithModel:item];
        [self.navigationController pushViewController:vc animated:YES];
    }
}

#pragma mark -- UITableViewDragDelegate
- (NSArray<UIDragItem *> *)tableView:(UITableView *)tableView itemsForBeginningDragSession:(id<UIDragSession>)session atIndexPath:(NSIndexPath *)indexPath
{
    // 小震动
    UIImpactFeedbackGenerator* feedback = [[UIImpactFeedbackGenerator alloc]initWithStyle:UIImpactFeedbackStyleHeavy];
    [feedback prepare];
    [feedback impactOccurred];

    UserScript* item = self.model[indexPath.section];

    UIDragItem* dragItem = [[UIDragItem alloc] initWithItemProvider:[NSItemProvider new]];
    dragItem.localObject = item;

    return @[dragItem];
}

#pragma mark -- UITableViewDropDelegate
- (UITableViewDropProposal *)tableView:(UITableView *)tableView dropSessionDidUpdate:(id<UIDropSession>)session withDestinationIndexPath:(nullable NSIndexPath *)destinationIndexPath
{
    return [[UITableViewDropProposal alloc]initWithDropOperation:UIDropOperationMove intent:UITableViewDropIntentInsertAtDestinationIndexPath];
}

- (void)tableView:(UITableView *)tableView performDropWithCoordinator:(id<UITableViewDropCoordinator>)coordinator
{
    UIDragItem* dragItem = coordinator.items.firstObject.dragItem;
    UserScript* item = dragItem.localObject;

    NSIndexPath* targetIndexPath = coordinator.destinationIndexPath;

    //最后一个item判断
    if(targetIndexPath.section >= self.model.count) {
        //get the last IndexPath
        //跨区间 section 拖放操作, 拖出结尾的时候，coordinator.destinationIndexPath是没有的。
        //如果没有coordinator.destinationIndexPath那么强行指定最后一格
        targetIndexPath = [NSIndexPath indexPathForRow:0 inSection:self.model.count-1];
    }

    @weakify(self)
    [tableView performBatchUpdates:^{
        @strongify(self)
        id<UITableViewDropItem> dropItem = coordinator.items.firstObject;
        NSIndexPath* sourceIndexPath = dropItem.sourceIndexPath;

        if(sourceIndexPath.section < targetIndexPath.section) {
            [self.model insertObject:item atIndex:targetIndexPath.section+1];
            [self.model removeObjectAtIndex:sourceIndexPath.section];
        } else {
            [self.model removeObject:item];
            NSInteger index = targetIndexPath.section;
            [self.model insertObject:item atIndex:index];
        }

        [self updateCellStyle];
        
        [tableView reloadData];
    } completion:^(BOOL finished) {
        @strongify(self)
        //更新order
        NSMutableArray* items = [NSMutableArray array];
        for(int i=0;i<self.model.count;i++) {
            UserScript* item = self.model[i];
            item.ppOrder = i;

            [items addObject:item];
        }

        DatabaseUnit* unit = [DatabaseUnit updateUserScriptAllOrder:items];
        DB_EXEC(unit);
    }];
}

#pragma mark -- 更新圆角信息
- (void)updateCellStyle
{
    for(UserScript* item in self.model) {
        item.isFirstInSection = NO;
        item.isLastInSection = NO;
    }
    
    UserScript* item = self.model.firstObject;
    item.isFirstInSection = YES;
    
    item = self.model.lastObject;
    item.isLastInSection = YES;
}

#pragma mark -- lazy init
- (UITableView *)tableView
{
    if(!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];

        _tableView.separatorStyle  = UITableViewCellSeparatorStyleNone;
        _tableView.showsHorizontalScrollIndicator = NO;
        _tableView.showsVerticalScrollIndicator   = NO;
        _tableView.delegate   = self;
        _tableView.dataSource = self;
        _tableView.rowHeight = iPadValue(140, 92);
        _tableView.backgroundColor = UIColor.clearColor;

        [_tableView registerClass:[UserScriptDetailCell class] forCellReuseIdentifier:NSStringFromClass([UserScriptDetailCell class])];
                
        float height = iPadValue(20, 10);
        UIView* header = [[UIView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, height)];
        header.backgroundColor = UIColor.clearColor;
        _tableView.tableHeaderView = header;
        
        UIWindow* window = [NSObject normalWindow];
        UIView* footer = [[UIView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, height+window.safeAreaInsets.bottom)];
        footer.backgroundColor = UIColor.clearColor;
        _tableView.tableFooterView = footer;
        
        _tableView.sectionFooterHeight = 0.0;
        _tableView.sectionHeaderHeight = 0.0f;
        
        _tableView.dragDelegate = self;
        _tableView.dropDelegate = self;
    }
    
    return _tableView;
}

- (NSMutableArray *)model
{
    if(!_model) {
        _model = [NSMutableArray array];
    }
    
    return _model;
}

@end
