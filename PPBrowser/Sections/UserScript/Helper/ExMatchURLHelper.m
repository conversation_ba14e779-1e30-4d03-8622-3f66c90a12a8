//
//  ExMatchURLHelper.m
//  Saber Extension
//
//  Created by qing<PERSON> on 2023/2/24.
//

#import "ExMatchURLHelper.h"
#import "MatchPattern.h"

#import "InternalURL.h"

@implementation ExMatchURLHelper

//https://wiki.greasespot.net/Include_and_exclude_rules
+ (NSRegularExpression *)convert2GlobsRegExp:(NSString *)str
{
    NSString *expr = [[[[str stringByReplacingOccurrencesOfString:@"." withString:@"\\."]
                       stringByReplacingOccurrencesOfString:@"?" withString:@"\\?"]
                        stringByReplacingOccurrencesOfString:@"*" withString:@".*"]
                       stringByReplacingOccurrencesOfString:@"*." withString:@"*\\."];
    return [[NSRegularExpression alloc] initWithPattern:[NSString stringWithFormat:@"^%@$",expr]  options:0 error:nil];
}

+ (BOOL)matchesUserScript:(UserScript *)userScript
                      url:(NSString *)url
{
    //添加白名单和黑名单逻辑
    NSMutableArray* matches = [NSMutableArray array];
    [matches addObjectsFromArray:userScript.matches];
    [matches addObjectsFromArray:[userScript getWhiteList]];
    
    NSMutableArray* excludes = [NSMutableArray array];
    [excludes addObjectsFromArray:userScript.excludes];
    [excludes addObjectsFromArray:[userScript getBlackList]];
 
    return [self matchesCheckWithUrl:url matches:matches includes:userScript.includes excludes:excludes];
}

+ (BOOL)matchesCheckWithUrl:(NSString *)url
                    matches:(NSArray *)matches
                   includes:(NSArray *)includes
                   excludes:(NSArray *)excludes
{
    //判断url是否为空
    if(url.length <= 0) return NO;
 
    //判断url是否为空
    NSURL* URL = [NSURL URLWithString:url];
    if(!URL) return NO;
    
    //判断url是否为空
    if([InternalURL isValid:URL]) return NO;
    
    BOOL isMatch = NO;
    for (NSString *match in matches){
        @autoreleasepool {
            MatchPattern *matchPattern = [[MatchPattern alloc] initWithPattern:match];
            if ([matchPattern doMatch:url]){
                isMatch = YES;
                break;
            }
        }
    }
    
    if (!isMatch){ //Fallback and treat match as globs expr
        for (NSString *match in matches){
            NSRegularExpression *fallbackMatchExpr = [self convert2GlobsRegExp:match];
            NSArray<NSTextCheckingResult *> *result = [fallbackMatchExpr matchesInString:url options:0 range:NSMakeRange(0, url.length)];
            if (result.count > 0){
                isMatch = YES;
                break;
            }
        }
    }
    
    if (!isMatch){
        for (NSString *include in includes){
            NSRegularExpression *includeExpr = [self convert2GlobsRegExp:include];
            NSArray<NSTextCheckingResult *> *result = [includeExpr matchesInString:url options:0 range:NSMakeRange(0, url.length)];
            if (result.count > 0){
                isMatch = YES;
                break;
            }
        }
    }
    
    if (isMatch){
        for (NSString *exclude in excludes){
            NSRegularExpression *excludeExpr = [self convert2GlobsRegExp:exclude];
            NSArray<NSTextCheckingResult *> *result = [excludeExpr matchesInString:url options:0 range:NSMakeRange(0, url.length)];
            if (result.count > 0){
                isMatch = NO;
                break;
            }
        }
    }
    
    return isMatch;
}

@end
