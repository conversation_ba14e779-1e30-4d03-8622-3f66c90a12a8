//
//  ViaModel.h
//  PPBrowser
//
//  Created by qingbin on 2022/5/28.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "BaseModel.h"
#import "UserScript.h"
#import "ViaModel.h"
#import "UserComandModel.h"

//https://wenku.baidu.com/view/b73d2ae9a2c7aa00b52acfc789eb172ded639938.html

@interface ViaModel : BaseModel

@property (nonatomic, strong) NSString *name;

@property (nonatomic, strong) NSString *author;

@property (nonatomic, strong) NSString *url;

@property (nonatomic, strong) NSString *code;

/// 下面几个字段是从code解析出来的
@property (nonatomic, strong) NSString *scriptName;

@property (nonatomic, strong) NSString *desc;

@property (nonatomic, strong) NSString *includes;

@property (nonatomic, strong) NSString *version;

+ (ViaModel*)parseSourceCodeFromCommand:(NSString*)commandValue;

@end

