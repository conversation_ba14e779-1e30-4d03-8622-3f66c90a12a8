//
//  UserCommandManager.m
//  PPBrowser
//
//  Created by qingbin on 2022/5/20.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "UserCommandManager.h"
#import "UserComandModel.h"
#import "NSString+Helper.h"
#import "ReactiveCocoa.h"
#import "UserScript.h"
#import "NSString+MKNetworkKitAdditions.h"
#import "NSFileManager+Helper.h"

#import "PPNotifications.h"
#import "CustomRequestSerializer.h"

@interface UserCommandTabManager ()
//saveTab/getTab
@property (nonatomic, strong) NSMutableDictionary *allValues;

@end

@implementation UserCommandTabManager

+ (instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    static UserCommandTabManager* obj;
    dispatch_once(&onceToken, ^{
        obj = [UserCommandTabManager new];
    });
    
    return obj;
}

- (void)saveTab:(UserComandModel *)item
          tabId:(NSString *)tabId
{
    self.allValues[tabId] = item;
}

- (NSString *)getTab:(NSString *)tabId
{
    UserComandModel* resItem = self.allValues[tabId];
    NSString* res = [resItem toJSONString];
    return res;
}

#pragma mark -- lazy init
- (NSMutableDictionary *)allValues
{
    if(!_allValues) {
        _allValues = [NSMutableDictionary dictionary];
    }
    
    return _allValues;
}

@end

@interface UserCommandManager ()

@property (nonatomic, strong) NSMutableDictionary *allResources;

@property (nonatomic, weak) Tab* tab;

@property (nonatomic, strong) dispatch_queue_t queue;

@property (nonatomic, strong) NSUserDefaults* userDefaults;

@property (nonatomic, strong) NSMutableDictionary *registerMenuCommandMapper;

@end

@implementation UserCommandManager

- (instancetype)initWithTab:(Tab*)tab
{
    self = [super init];
    if(self) {
        self.tab = tab;
        //2.5.8对应v1版本
        self.userDefaults = [[NSUserDefaults alloc]initWithSuiteName:@"com.focus.userscripts.v1"];
        
        self.queue = dispatch_queue_create("com.userCommandManager.queue", DISPATCH_QUEUE_CONCURRENT);
        self.registerMenuCommandMapper = [NSMutableDictionary dictionary];
    }
    
    return self;
}

- (void)setValue:(UserComandModel*)item
{
    NSMutableDictionary* dict = [NSMutableDictionary dictionaryWithDictionary:[self.userDefaults objectForKey:item.uuid]];
    
    NSString* key = item.name?:@"";
    NSString* value = item.value ?:@"";
    [dict setObject:value forKey:key];
    
    [self.userDefaults setObject:dict forKey:item.uuid];
    [self.userDefaults synchronize];
}

- (NSString*)getData:(UserComandModel*)item
{
    NSMutableDictionary* dict = [NSMutableDictionary dictionary];
    dict[@"uuid"] = item.uuid;
    dict[@"randomId"] = item.randomId;
    
    NSDictionary *data = [self.userDefaults objectForKey:item.uuid];
    dict[@"data"] = data;

    return [NSString convertToJsonString:dict options:NO];
}

- (void)deleteValue:(UserComandModel*)item
{
    NSMutableDictionary *dict = [NSMutableDictionary dictionaryWithDictionary:[self.userDefaults objectForKey:item.uuid]];
    NSString* key = item.name?:@"";
    [dict removeObjectForKey:key];
    
    [self.userDefaults setObject:dict forKey:item.uuid];
    [self.userDefaults synchronize];
}

#pragma mark -- 注册菜单栏
- (void)registerMenuCommand:(UserComandModel*)item
{
    NSLog(@"绑定 name = %@", item.name);
    NSMutableArray* menus = self.registerMenuCommandMapper[item.uuid];
    if(!menus) {
        menus = [NSMutableArray array];
    }
    
    //注册时会有重复项(例如自动无缝翻页)，需要去重处理
    UserComandModel* _removedObj;
    for(UserComandModel* obj in menus) {
        if([obj.name isEqualToString:item.name]) {
            _removedObj = obj;
            break;
        }
    }
    if(_removedObj) {
        [menus removeObject:_removedObj];
    }
    
    [menus addObject:item];
    self.registerMenuCommandMapper[item.uuid] = menus;
    
    //发通知更新
    [[NSNotificationCenter defaultCenter] postNotificationName:kupdateCommandNotification object:nil userInfo:@{
        @"scriptId" : item.uuid?:@""
    }];
}

#pragma mark -- 解绑菜单栏
- (void)unregisterMenuCommand:(UserComandModel*)item
{
    NSLog(@"解绑 name = %@", item.name);
    
    NSMutableArray* menus = self.registerMenuCommandMapper[item.uuid];
    if(!menus) return;
    
    UserComandModel* _removedObj;
    for(UserComandModel* obj in menus) {
        if([obj.randomId isEqualToString:item.randomId]) {
            _removedObj = obj;
            break;
        }
    }
    if(_removedObj) {
        [menus removeObject:_removedObj];
    }
    
    //发通知更新
    [[NSNotificationCenter defaultCenter] postNotificationName:kupdateCommandNotification object:nil userInfo:@{
        @"scriptId" : item.uuid?:@""
    }];
}

#pragma mark -- 刷新网页时清理菜单栏
- (void)clearCommands
{
    [self.registerMenuCommandMapper removeAllObjects];
    self.registerMenuCommandMapper = [NSMutableDictionary dictionary];
}

#pragma mark -- 根据tabId和脚本的uuid，获取菜单栏信息
- (NSArray*)getMenusWithScriptId:(NSString*)scriptId
{
    NSMutableArray* menus = self.registerMenuCommandMapper[scriptId];
    
    return menus;
}

#pragma mark -- 网络请求
- (void)xmlhttpRequest:(UserComandModel*)item
{
    AFHTTPSessionManager *manager = [AFHTTPSessionManager manager];
        
    /**常见的三种序列化器：
     1、application/x-www-form-urlencoded，key1=value1&key2=value2
     2、application/json， { "key1": "value1", "key2": "value2"}
     3、application/x-plist，
     <?xml version="1.0" encoding="UTF-8"?>
     <!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
     <plist version="1.0">
     <dict>
         <key>key1</key>
         <string>value1</string>
         <key>key2</key>
         <string>value2</string>
     </dict>
     </plist>
     */
    //指定请求序列化器为JSON格式
    //根据Content-Type区分序列化器
    
    /// 请求体的处理
    //将body从字符串转换为字典，因为“沉浸式翻译”传了body之后，item.data是字符串形式，导致被AFNetworking拼接成"=item.data"的形式
    //key和value不对
    id body = item.data;
    if(item.data) {
        @try {
            body = [NSString jsonConvertToObject:item.data];
        } @catch (NSException *exception) {
            NSLog(@"Error, 脚本转换请求体失败: %@", exception);
        } @finally {
        }
    }
    //再做一个保险判断
    if(!body) {
        body = item.data;
        //json都转换失败了，说明只能是默认传送类型了，适配海角脚本
        manager.requestSerializer = [CustomRequestSerializer serializer];
    } else {
        //默认值
        manager.requestSerializer = [CustomRequestSerializer serializer];
        if(item.headers) {
            NSString* contentType = item.headers[@"Content-Type"];
            if(contentType.length == 0) {
                contentType = item.headers[@"content-type"];
            }
            if(contentType.length>0) {
                if([contentType rangeOfString:@"application/json"].location != NSNotFound) {
                    //json
                    manager.requestSerializer = [AFJSONRequestSerializer serializer];
                } else if([contentType rangeOfString:@"application/x-plist"].location != NSNotFound) {
                    manager.requestSerializer = [AFPropertyListRequestSerializer serializer];
                } else if([contentType rangeOfString:@"application/x-www-form-urlencoded"].location != NSNotFound) {
                    manager.requestSerializer = [AFHTTPRequestSerializer serializer];
                }
            }
        }
    }
    
    //指定响应类型
    manager.responseSerializer = [AFHTTPResponseSerializer serializer];
    
    ///请求头的处理
    //需要清除headers中为nil的值,否则会报错崩溃
    /*
     自动无缝翻页脚本
     Accept = "text/html,application/xhtml+xml,application/xml";
     Referer = "https://m.baidu.com/s?word=app";
     "User-Agent" = "Mozilla/5.0 (iPhone; CPU iPhone OS 16_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like MAC OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16_0 Mobile/15E148 Safari/604.1";
     "x-requested-with" = "<null>";
     */
    
    NSMutableDictionary* headers = [NSMutableDictionary dictionary];
    for (NSString *headerField in item.headers.keyEnumerator) {
        if(headerField.length>0 && item.headers[headerField] && !isNull(item.headers[headerField])) {
            headers[headerField] = item.headers[headerField];
        }
    }
    
    //v2.5.6 请求头的值必须是字符串类型
    //否则AFHTTPSessionManager中的[request setValue:headers[headerField] forHTTPHeaderField:headerField];会报错
    NSMutableDictionary *mutableDictionary = [NSMutableDictionary dictionary];
    for (id key in headers) {
        id value = headers[key];
        if ([value isKindOfClass:[NSNumber class]]) {
            value = [value stringValue];
        }
        [mutableDictionary setObject:value forKey:key];
    }
    
    headers = [mutableDictionary copy];
    
    //设置超时时间(单位:秒)，上面requestSerializer重新new了，注意否则设置无效
    NSInteger timeout = 30;
    if(item.timeout > 0) {
        //xmlhttpRequest的timeout超时时间是以毫秒为单位
        timeout = item.timeout / 1000;
    }
    [manager.requestSerializer setTimeoutInterval:timeout];
    
    @weakify(self)
    if([item.method.lowercaseString isEqualToString:@"get"]) {
        [manager GET:item.url parameters:body headers:headers progress:^(NSProgress * _Nonnull downloadProgress) {
        } success:^(NSURLSessionDataTask * _Nonnull task, id  _Nullable responseObject) {
            @strongify(self)
            [self handleXmlhttpRequestSuccess:task responseObject:responseObject item:item];
        } failure:^(NSURLSessionDataTask * _Nullable task, NSError * _Nonnull error) {
            @strongify(self)
            [self handleXmlhttpRequestFailure:task error:error item:item];
        }];
    } else if([item.method.lowercaseString isEqualToString:@"post"]) {
        [manager POST:item.url parameters:body headers:headers progress:^(NSProgress * _Nonnull uploadProgress) {
        } success:^(NSURLSessionDataTask * _Nonnull task, id  _Nullable responseObject) {
            @strongify(self)
            [self handleXmlhttpRequestSuccess:task responseObject:responseObject item:item];
        } failure:^(NSURLSessionDataTask * _Nullable task, NSError * _Nonnull error) {
            @strongify(self)
            [self handleXmlhttpRequestFailure:task error:error item:item];
        }];
    }
}

- (void)handleXmlhttpRequestSuccess:(NSURLSessionDataTask*)task
                     responseObject:(id)responseObject
                               item:(UserComandModel*)item
{
    NSDictionary* responseHeader;
    NSString* responseUrl;
    NSInteger statusCode = 0;
    
    if([task.response isKindOfClass:NSHTTPURLResponse.class]) {
        NSHTTPURLResponse* httpResponse = (NSHTTPURLResponse*)task.response;
        responseHeader = httpResponse.allHeaderFields;
        responseUrl = [httpResponse.URL absoluteString];
        statusCode = httpResponse.statusCode;
        
        item.mimeType = httpResponse.MIMEType;
    }
    
    NSString* responseContent;
    //有可能是NSData类型，例如image/png，此时需要将其转换为base64格式, 在js中再通过atob解压
    if([responseObject isKindOfClass:NSData.class] && ([item.responseType isEqualToString:@"blob"] || [item.responseType isEqualToString:@"arraybuffer"])) {
        //只处理blob/arraybuffer
        NSData *imageData = (NSData *)responseObject;
        // 将 NSData 转换为 Base64 字符串
        responseContent = [imageData base64EncodedStringWithOptions:0];
    } else {
        responseContent = [[NSString alloc] initWithData:responseObject encoding:NSUTF8StringEncoding];
    }
    
    if(responseHeader.count > 0) {
        NSMutableString* header = [NSMutableString new];
        [responseHeader enumerateKeysAndObjectsUsingBlock:^(NSString*  _Nonnull key, NSString*  _Nonnull obj, BOOL * _Nonnull stop) {
            NSString* item = [NSString stringWithFormat:@"%@: %@\r\n",key,obj];
            [header appendString:item];
        }];
        item.responseHeader = header;
    } else {
        item.responseHeader = @"";
    }
    item.responseUrl = responseUrl;
    item.statusCode = statusCode;
    item.responseContent = responseContent;
    
//    NSLog(@"responseContent = %@", responseContent);
    
    if(self.triggerRequestSuccessAction) {
        self.triggerRequestSuccessAction(item);
    }
}

- (void)handleXmlhttpRequestFailure:(NSURLSessionDataTask*)task
                              error:(NSError*)error
                               item:(UserComandModel*)item
{
    if([task.response isKindOfClass:NSHTTPURLResponse.class]) {
        NSHTTPURLResponse* httpResponse = (NSHTTPURLResponse*)task.response;
        NSInteger statusCode = httpResponse.statusCode;
        
        item.statusCode = statusCode;
    }
    
    if(error.code == NSURLErrorTimedOut) {
        //timeout
        if(self.triggerRequestTimeoutAction) {
            self.triggerRequestTimeoutAction(item);
        }
    } else {
        if(self.triggerRequestErrorAction) {
            self.triggerRequestErrorAction(item);
        }
    }
}

#pragma mark -- getResourceText/getResourceURL
- (void)handleGetResourceData:(UserComandModel*)item
                    completed:(void(^)(NSString* result))completedBlock
{
    if(!item.resourceUrls) {
        if(completedBlock) {
            completedBlock(nil);
        }
        return;
    }
    
    NSMutableDictionary* resourceDatas = self.allResources[item.uuid];
    if(!resourceDatas) {
        resourceDatas = [NSMutableDictionary dictionary];
        self.allResources[item.uuid] = resourceDatas;
    } else {
        //有内存缓存
        NSMutableDictionary* dict = [NSMutableDictionary dictionary];
         [resourceDatas enumerateKeysAndObjectsUsingBlock:^(NSString*  _Nonnull key, GMResourceModel*  _Nonnull obj, BOOL * _Nonnull stop) {
             NSString* json = [obj toJSONString];
             dict[key] = json;
         }];
         
        NSString* res = [NSString convertToJsonString:dict options:NO];
        if(completedBlock) {
            completedBlock(res);
        }
        
        return;
    }
    
    //查看有没有本地文件缓存
    NSString* userscriptPath;
    NSString* dir;
    NSFileManager* fm;
    userscriptPath = [NSFileManager userScriptPath];
    NSString* path = [item.uuid stringByReplacingOccurrencesOfString:@"-" withString:@"_"];
    dir = [NSString stringWithFormat:@"%@/%@/resource", userscriptPath, path];
    fm = [NSFileManager defaultManager];
    if(![fm fileExistsAtPath:dir]) {
        [fm createDirectoryAtPath:dir withIntermediateDirectories:YES attributes:nil error:nil];
    }
    
    NSString* storageUrl = [NSString stringWithFormat:@"%@/resource.json",dir];
    if([fm fileExistsAtPath:storageUrl]) {
        //有本地缓存
        NSError* error = nil;
        NSString* source = [NSString stringWithContentsOfFile:storageUrl encoding:NSUTF8StringEncoding error:&error];
        if(error) {
            NSLog(@"@resource加载失败,error = %@", error.localizedDescription);
            
            if(completedBlock) {
                completedBlock(nil);
            }
            return;
        } else {
            NSArray* allResource = [NSString jsonConvertToObject:source];
            //GMResourceModel
            for(NSDictionary* item in allResource) {
                GMResourceModel* obj = [[GMResourceModel alloc]initWithDictionary:item error:nil];
                resourceDatas[obj.key] = obj;
            }
            
            //保存到内存缓存
            self.allResources[item.uuid] = resourceDatas;
            
            NSMutableDictionary* dict = [NSMutableDictionary dictionary];
            [resourceDatas enumerateKeysAndObjectsUsingBlock:^(NSString*  _Nonnull key, GMResourceModel*  _Nonnull obj, BOOL * _Nonnull stop) {
                 NSString* json = [obj toJSONString];
                 dict[key] = json;
             }];
             
            NSString* res = [NSString convertToJsonString:dict options:NO];
            dispatch_async(dispatch_get_main_queue(), ^{
                if(completedBlock) {
                    completedBlock(res);
                }
            });
        }
    
        return;
    }
    
    //没有缓存
    dispatch_async(self.queue, ^{
        [self _loadAllResources:item];
        
        NSMutableDictionary* dict = [NSMutableDictionary dictionary];
        [resourceDatas enumerateKeysAndObjectsUsingBlock:^(NSString*  _Nonnull key, GMResourceModel*  _Nonnull obj, BOOL * _Nonnull stop) {
             NSString* json = [obj toJSONString];
             dict[key] = json;
         }];
         
        NSString* res = [NSString convertToJsonString:dict options:NO];
        dispatch_async(dispatch_get_main_queue(), ^{
            if(completedBlock) {
                completedBlock(res);
            }
        });
    });
}

- (void)_loadAllResources:(UserComandModel*)model
{
    NSMutableDictionary* resourceDatas = self.allResources[model.uuid];
    __block NSMutableArray* items = [NSMutableArray array];
    
    [model.resourceUrls enumerateKeysAndObjectsUsingBlock:^(NSString*  _Nonnull key, NSString*  _Nonnull url, BOOL * _Nonnull stop) {
        GMResourceModel* obj = [GMResourceModel new];
        obj.key = key;
        obj.url = url;
        obj.text = [self _loadResourceFromRequest:url];
        
        resourceDatas[key] = obj;
        
        //同时保存到本地文件缓存
        NSDictionary* dict = [obj toDictionary];
        [items addObject:dict];
    }];
    
    //同时保存到本地文件缓存
    if(items.count > 0) {
        NSString* text = [NSString convertToJsonString:items options:NO];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            NSString* userscriptPath;
            NSString* dir;
            NSFileManager* fm;
            userscriptPath = [NSFileManager userScriptPath];
            NSString* path = [model.uuid stringByReplacingOccurrencesOfString:@"-" withString:@"_"];
            dir = [NSString stringWithFormat:@"%@/%@/resource", userscriptPath, path];
            fm = [NSFileManager defaultManager];
            if(![fm fileExistsAtPath:dir]) {
                [fm createDirectoryAtPath:dir withIntermediateDirectories:YES attributes:nil error:nil];
            }
            
            NSString* storageUrl = [NSString stringWithFormat:@"%@/resource.json",dir];
            if([fm fileExistsAtPath:storageUrl]) {
                [fm removeItemAtPath:storageUrl error:nil];
            }
            [text writeToFile:storageUrl atomically:YES encoding:NSUTF8StringEncoding error:nil];
        });
    }
}

- (NSString*)_loadResourceFromRequest:(NSString*)urlString
{
    NSURL* url = [NSURL URLWithString:urlString];
    NSURLRequest *request = [[NSURLRequest alloc]initWithURL:url cachePolicy:NSURLRequestUseProtocolCachePolicy timeoutInterval:30];
    NSError *error;
    NSData *receiveData = [NSURLConnection sendSynchronousRequest:request returningResponse:nil error:&error];
    if(!error && receiveData) {
        NSString *content = [[NSString alloc]initWithData:receiveData encoding:NSUTF8StringEncoding];
        return content;
    }
    
    return nil;
}

#pragma mark -- lazy init
- (NSMutableDictionary *)allResources
{
    if(!_allResources) {
        _allResources = [NSMutableDictionary dictionary];
    }
    
    return _allResources;
}


@end
