//
//  UserScript.h
//  PPBrowser
//
//  Created by qingbin on 2022/5/19.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "BaseModel.h"
#import "PPEnums.h"

#import "UserComandModel.h"
#import <CloudKit/CloudKit.h>
#import "SyncProtocol.h"

//油猴脚本语法
//https://www.tampermonkey.net/documentation.php

/**
 视频缓存的GM_download根据上面的语法，有2种入参方法，无法同时实现
 但是考虑到下面的脚本，用details作为入参的比较常见，因此用details
 https://greasyfork.org/zh-CN/scripts/449581-m3u8%E8%A7%86%E9%A2%91%E4%BE%A6%E6%B5%8B%E4%B8%8B%E8%BD%BD%E5%99%A8-%E8%87%AA%E5%8A%A8%E5%97%85%E6%8E%A2/code
 */

@interface GMResourceInfoModel : BaseModel

@property (nonatomic, copy) NSString *name;
@property (nonatomic, copy) NSString *url;

@end

@protocol GMResourceInfoModel <NSObject>
@end

@interface UserScript : BaseModel<SyncProtocol>

@property (nonatomic, copy) NSString *uuid;
@property (nonatomic, copy) NSString *name;
@property (nonatomic, copy) NSString *namespace;
@property (nonatomic, copy) NSString *author;
@property (nonatomic, copy) NSString *version;
@property (nonatomic, copy) NSString *desc;
@property (nonatomic, copy) NSString *iconUrl;
@property (nonatomic, copy) NSString *homepage;
@property (nonatomic, copy) NSString *icon;
@property (nonatomic, copy) NSArray<NSString *> *includes;
@property (nonatomic, copy) NSArray<NSString *> *matches;
@property (nonatomic, copy) NSArray<NSString *> *excludes;
@property (nonatomic, copy) NSString *runAt;
@property (nonatomic, copy) NSArray<NSString *> *grants;
@property (nonatomic, assign) BOOL noFrames;
@property (nonatomic, assign) BOOL pass;
@property (nonatomic, copy) NSString *errorMessage;
@property (nonatomic, copy) NSString *sourcePage;
@property (nonatomic, copy) NSString *updateUrl;
@property (nonatomic, copy) NSString *downloadUrl;
/// js源码
@property (nonatomic, copy) NSString *content;
/// 去掉头部注释的js源码
//@property (nonatomic, copy) NSString *parsedContent;
/// 通过source-code-generate生成的js源码,译为可执行源码
@property (nonatomic, copy) NSString *executorJs;
/// 顺序
@property (nonatomic, assign) int ppOrder;

// 更新时间，和iCloud的对比
@property (nonatomic, strong) NSString *updateTime;
// 是否激活
@property (nonatomic, assign) BOOL isActive;
// 是否自动更新
@property (nonatomic, assign) BOOL isAutoUpdate;

@property (nonatomic, assign) BOOL isMatched;
@property (nonatomic, assign) BOOL updateSwitch;
@property (nonatomic, copy) NSArray<NSString *> *requireUrls;

@property (nonatomic, copy) NSArray<NSString *> *requireCodes;
@property (nonatomic, copy) NSDictionary *resourceUrls;
@property (nonatomic, copy) NSArray<NSString *> *notes;
@property (nonatomic, copy) NSDictionary<NSString *,NSDictionary *> *locales;

//2.4.8添加
//数据库使用相关字段
@property (nonatomic, copy) NSString *sql_includes;
@property (nonatomic, copy) NSString *sql_matches;
@property (nonatomic, copy) NSString *sql_excludes;
@property (nonatomic, copy) NSString *sql_grants;
@property (nonatomic, copy) NSString *sql_requireUrls;
@property (nonatomic, copy) NSString *sql_resourceUrls;

//selectFrame-0,运行在MainFrame, selectFrame-1,运行在allFrame
@property (nonatomic, assign) NSInteger selectFrame;

//2.6.0添加
//0-默认, 1-mainframe, 2-allframe
@property (nonatomic, assign) UserScriptFrameOption frameOption;
//白名单(用","拼接的数组)
@property (nonatomic, strong) NSString *whiteList;
//黑名单(用","拼接的数组)
@property (nonatomic, strong) NSString *blackList;

//当前更新状态(辅助字段)
@property (nonatomic, assign) UserScriptUpdateStatus updateStatus;

// 根据frameOption和selectFrame，综合获取当前的执行范围
- (BOOL)isRunAtMainFrame;

// 获取脚本信息
- (NSString*)GM_Info;
// 生成可执行文件
- (void)updateExecutorJs;

// 辅助,用于GM_info, 组合是name+url
@property (nonatomic, copy) NSArray<GMResourceInfoModel> *resources;

@property (nonatomic, strong) NSString *ctime;

//辅助字段,主要是卡片化
@property (nonatomic, assign) BOOL isFirstInSection;
@property (nonatomic, assign) BOOL isLastInSection;
//运行标志
@property (nonatomic, assign) CustomTitleViewStatus titleStatus;
//注册菜单
@property (nonatomic, strong) NSArray* commands;

//获取脚本的安装链接/更新链接(其中一个),有时候安装链接和更新链接不是同一个
- (NSString *)getUpdateUrl;

//获取白名单
- (NSArray<NSString *> *)getWhiteList;
//添加白名单
- (void)addWhiteListItem:(NSString *)whiteListItem;
//删除白名单
- (void)removeWhiteLisItem:(NSString *)whiteListItem;
//获取黑名单
- (NSArray<NSString *> *)getBlackList;
//添加黑名单
- (void)addBlackListItem:(NSString *)blackListItem;
//删除黑名单
- (void)removeBlackLisItem:(NSString *)blackListItem;

//修改脚本代码后，需要重新生成可执行代码，重新加载脚本
- (void)reloadUserScript;

// 从sql初始化到model
- (void)jsonModelFromSql;
// 从model初始化到sql字段
- (void)jsonModelToSql;

//CloudKit同步
//获取一个默认的CKRecord
- (CKRecord *)toDefaultCKRecord;
//转换成CKRecord
- (CKRecord *)toCKRecord;
//从CKRecord转换为UserScript
- (instancetype)initWithCKRecord:(CKRecord *)record;
//判断两个脚本是否一致，1、根据uuid,2、判断内容是否相等，即updateUrl/downloadUrl是否相等
- (BOOL)objectIsEqualTo:(id)obj;

//返回uuid
- (NSString*)getUuid;
//更新时间
- (NSString*)getUpdateTime;

@end

@protocol UserScript <NSObject>
@end

//https://wiki.greasespot.net/GM.info
@interface GMInfoModel : BaseModel

@property (nonatomic, strong) UserScript* script;
//脚本头部所有的信息(去掉源码)
@property (nonatomic, copy) NSString* scriptMetaStr;
//脚本引擎的名称, 默认Greasemonkey
@property (nonatomic, copy) NSString* scriptHandler;
//脚本引擎的版本号, 默认4.0
@property (nonatomic, copy) NSString* version;

@end
