//
//  Tampermonkey.m
//  PPBrowser
//
//  Created by qingbin on 2022/5/19.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "Tampermonkey.h"
#import "UserScript.h"
#import "PPNotifications.h"
#import "NSString+MKNetworkKitAdditions.h"
#import "ResourceHelper.h"
#import "NetworkUtil.h"
#import "NSString+Helper.h"
#import "NSFileManager+Helper.h"

#import "DatabaseUnit+UserScript.h"

@interface Tampermonkey ()

@property (nonatomic, strong) JSContext *jsContext;

@property (nonatomic, strong) NSMutableDictionary *uuidForUserScriptMapper;

@property (nonatomic, strong) NSString* scriptPath;

// source-code-generate 源码
@property (nonatomic, strong) NSString* source_code_generate;

@end

@implementation Tampermonkey

+ (instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    static Tampermonkey* obj;
    dispatch_once(&onceToken, ^{
        obj = [<PERSON>permonkey new];
    });
    
    return obj;
}

- (instancetype)init
{
    self = [super init];
    if(self) {
        self.uuidForUserScriptMapper = [NSMutableDictionary dictionary];
        
        self.source_code_generate = [ResourceHelper shareInstance].sourceCodeGenerate;
    }
    
    return self;
}

- (NSString *)scriptPath
{
    return [NSFileManager userScriptPath];
}

#pragma mark -- 删除所有用户脚本
- (void)removeAllUserScriptsFromDisk
{
    NSFileManager* fileManager = [NSFileManager defaultManager];
    if([fileManager fileExistsAtPath:self.scriptPath]) {
        [fileManager removeItemAtPath:self.scriptPath error:nil];
    }
    
    [[NSUserDefaults standardUserDefaults] removePersistentDomainForName:@"com.focus.userscripts"];
}

#pragma mark -- 解析via脚本meta信息
- (ViaModel*)parseViaWithSourceCode:(NSString*)source
{
    JSValue *parseUserScript = [self.jsContext evaluateScript:@"window.parseViaScript"];
    JSValue *userScriptHeader = [parseUserScript callWithArguments:@[source,@""]];

    NSDictionary* dict = [userScriptHeader toDictionary];
    ViaModel* item = [[ViaModel alloc]initWithDictionary:dict error:nil];
        
    return item;
}

/// 根据脚本内容解析得到脚本Model，主要是为了更新对比用
- (UserScript *)parseJSContent:(NSString *)jsContent
{
    if(jsContent.length == 0) return nil;
    
    JSValue *parseUserScript = [self.jsContext evaluateScript:@"window.parseUserScript"];
    JSValue *userScriptHeader = [parseUserScript callWithArguments:@[jsContent, @""]];

    NSDictionary* dict = [userScriptHeader toDictionary];
    UserScript *userScript = [[UserScript alloc] initWithDictionary:dict error:nil];
    userScript.content = jsContent;
    userScript.desc = dict[@"description"];
    
    //步骤:
    //1、直接匹配
    //2、根据语言匹配
    //3、取默认值
    NSString* locale = [[NSLocale currentLocale] localeIdentifier];
    if(locale.length>0 && userScript.locales) {
        //将iOS的zh_CN转为js的zh-CN
        locale = [locale stringByReplacingOccurrencesOfString:@"_" withString:@"-"];
        
        __block NSDictionary* locales = userScript.locales[locale];
        //如果直接获取没有
        if(!locales) {
            //根据语言提取
            NSRange range = [locale rangeOfString:@"-"];
            NSString* language = [locale substringToIndex:range.location];
            
            [userScript.locales enumerateKeysAndObjectsUsingBlock:^(NSString*  _Nonnull key, NSDictionary*  _Nonnull obj, BOOL * _Nonnull stop) {
                if([key hasPrefix:language]) {
                    locales = obj;
                    *stop = YES;
                }
            }];
        }
        
        if(locales) {
            NSString* name = locales[@"name"];
            NSString* desc = locales[@"description"];
            if(name.length > 0) {
                userScript.name = name;
            }
            
            if(desc.length > 0) {
                userScript.desc = desc;
            }
        }
    }
    
    return userScript;
}

/// 更新一个脚本
//- (void)updateUserScriptWithNewVersion:(UserScript *)newVersion
//                            oldVersion:(UserScript *)oldVersion
//{
//    newVersion.uuid = oldVersion.uuid;
//    newVersion.isActive = oldVersion.isActive;
//    
//    //加载@require
//    [self loadRequiredWithUserScript:newVersion completion:nil];
//    
//    //加载@resource
//    [self loadRequiredWithUserScript:newVersion completion:nil];
//    
//    //自动更新相关信息
//    newVersion.updateUrl = oldVersion.updateUrl;
//    newVersion.downloadUrl = oldVersion.downloadUrl;
//    newVersion.isActive = oldVersion.isActive;
//    newVersion.ppOrder = oldVersion.ppOrder;
//    newVersion.name = oldVersion.name;
//    newVersion.selectFrame = oldVersion.selectFrame;
//    
//    DatabaseUnit* unit = [DatabaseUnit updateUserScriptWithItem:newVersion];
//    DB_EXEC(unit);
//}

#pragma mark -- 加载自动添加GreasyFork/openuserjs脚本
- (void)loadInstallJsHelperWithCompletion:(void(^)(UserScript *userScript))completion
{
//    NSString* source = [self _loadJsFromBundleWithFileName:@"installHelper"];
    NSString* source = [ResourceHelper shareInstance].installHelper;
    
    NSString* uuid = [[NSUUID UUID] UUIDString];
    [self loadJsWithUUID:uuid
              sourceCode:source
              completion:^(UserScript *userScript) {
        if(completion) {
            completion(userScript);
        }
    }];
}

#pragma mark - 测试js文件
- (void)loadTestJsHelperWithCompletion:(void(^)(UserScript *userScript))completion
{
    NSString* source = [self _loadJsFromBundleWithFileName:@"unittest"];
    NSString* uuid = [[NSUUID UUID] UUIDString];
    [self loadJsWithUUID:uuid
              sourceCode:source
              completion:^(UserScript *userScript) {
        if(completion) {
            completion(userScript);
        }
    }];
}

#pragma mark -- log
- (void)nslog:(id)object
{
#if DEBUG
    //脚本相关信息打印
//    NSLog(@"log:\n{\nvalue: %@\ntype: %@\n}",object,[object class]);
#endif
}

#pragma mark -- 从ScriptPath重新加载源码, 根据uuid获取可执行源码
- (void)loadJsWithUUID:(NSString*)uuid
            completion:(void(^)(UserScript *userScript))completion
{
    NSString* source = [self _loadJsFromScriptPathWithUUID:uuid];
    if(source.length == 0) {
        //出错了，需要处理
        if(completion) {
            completion(nil);
        }
        return;
    }
    
    [self loadJsWithUUID:uuid
              sourceCode:source
              completion:^(UserScript *userScript) {
        if(completion) {
            completion(userScript);
        }
    }];
}

#pragma mark -- 根据scriptContent生成用户脚本, uuid是赋值用的
- (void)loadJsWithUUID:(NSString*)uuid
            sourceCode:(NSString*)scriptContent
            completion:(void(^)(UserScript *userScript))completion
{
    JSValue *parseUserScript = [self.jsContext evaluateScript:@"window.parseUserScript"];
    JSValue *userScriptHeader = [parseUserScript callWithArguments:@[scriptContent,@""]];

    NSDictionary* dict = [userScriptHeader toDictionary];
    UserScript *userScript = [[UserScript alloc]initWithDictionary:dict error:nil];
    
    //存在解析失败的情况
    if(userScript == nil) {
        if(completion) {
            completion(nil);
        }
        
        return;
    }
    
    userScript.uuid = uuid;
    
    userScript.content = scriptContent;
    userScript.desc = dict[@"description"];
    userScript.namespace = dict[@"namespace"]?:@"";
    
    //去除头部的脚本
    NSString* jsContent = [self _removeComment:scriptContent];
    
    //步骤:
    //1、直接匹配
    //2、根据语言匹配
    //3、取默认值
    NSString* locale = [[NSLocale currentLocale] localeIdentifier];
    if(locale.length>0 && userScript.locales) {
        //将iOS的zh_CN转为js的zh-CN
        locale = [locale stringByReplacingOccurrencesOfString:@"_" withString:@"-"];
        
        __block NSDictionary* locales = userScript.locales[locale];
        //如果直接获取没有
        if(!locales) {
            //根据语言提取
            NSRange range = [locale rangeOfString:@"-"];
            NSString* language = [locale substringToIndex:range.location];
            
            [userScript.locales enumerateKeysAndObjectsUsingBlock:^(NSString*  _Nonnull key, NSDictionary*  _Nonnull obj, BOOL * _Nonnull stop) {
                if([key hasPrefix:language]) {
                    locales = obj;
                    *stop = YES;
                }
            }];
        }
        
        if(locales) {
            NSString* name = locales[@"name"];
            NSString* desc = locales[@"description"];
            if(name.length > 0) {
                userScript.name = name;
            }
            
            if(desc.length > 0) {
                userScript.desc = desc;
            }
        }
    }
    
    //默认值
    userScript.isActive = true;
    
    //userScript.name
    //从源码中解析出来的字段
    
    //mainFrame/allFrame
    //安装的时候通过noFrames来判断
    //selectFrame: 如果有值表示是手动修改过的，从selectFrame中读取，否则读取@noframes字段
    if(userScript.noFrames) {
        userScript.selectFrame = 0;
    } else {
        userScript.selectFrame = 1;
    }
    
    //生成可执行源码
    __block NSString* executorJs = nil;
    @weakify(self)
    [self _loadRequiredWithScriptId:uuid
                        forceReload:NO
                        requireUrls:userScript.requireUrls
                         completion:^(NSString *requireCode) {
        @strongify(self)
        //@require替换
        executorJs = [self.source_code_generate stringByReplacingOccurrencesOfString:@"{{GMRequireContent}}" withString:requireCode?:@""];

        //油猴脚本替换
        executorJs = [executorJs stringByReplacingOccurrencesOfString:@"{{GMScriptContent}}" withString:jsContent?:@""];
        
        //GM_Info替换
        NSString* GMScriptInfo = [userScript GM_Info];
        userScript.executorJs = [executorJs stringByReplacingOccurrencesOfString:@"{{GMScriptInfo}}" withString:GMScriptInfo];
        
        if(completion) {
            completion(userScript);
        }
    }];
}

/// 删除油猴脚本头部注释
- (NSString *)_removeComment:(NSString *)script
{
    NSString *process = [script copy];

    NSRegularExpression *regex = [NSRegularExpression regularExpressionWithPattern:@"^\\/\\/.*" options:NSRegularExpressionAnchorsMatchLines error:NULL];

    process = [regex stringByReplacingMatchesInString:process
                                              options:0
                                                range:NSMakeRange(0, process.length)
                                         withTemplate:@""];

    process = [process stringByTrimmingCharactersInSet:[NSCharacterSet newlineCharacterSet]];

    return process;
}

#pragma mark -- 获取油猴注释的头部
- (NSString*)scriptMetaStringWithSourceCode:(NSString *)sourceCode
{
    NSString *process = [sourceCode copy];

    NSRegularExpression *regex = [NSRegularExpression regularExpressionWithPattern:@"^\\/\\/.*" options:NSRegularExpressionAnchorsMatchLines error:NULL];

    NSArray<NSTextCheckingResult *> *matches = [regex matchesInString:process options:0 range:NSMakeRange(0, process.length)];

    NSMutableArray *array = [NSMutableArray array];
    for (NSTextCheckingResult *match in matches) {
        for (int i = 0; i < [match numberOfRanges]; i++) {
            NSString *component = [process substringWithRange:[match rangeAtIndex:i]];
            [array addObject:component];
        }
    }

    NSString* result = [array componentsJoinedByString:@"\n"];

    return result;
}

#pragma mark -- 更新一个脚本
- (void)updateUserScriptWithNewVersion:(UserScript *)newVersion
                            oldVersion:(UserScript *)oldVersion
{
    newVersion.uuid = oldVersion.uuid;
    newVersion.isActive = oldVersion.isActive;
    newVersion.isAutoUpdate = oldVersion.isAutoUpdate;
    newVersion.frameOption = oldVersion.frameOption;
    newVersion.whiteList = oldVersion.whiteList;
    newVersion.blackList = oldVersion.blackList;
    newVersion.ppOrder = oldVersion.ppOrder;
    
    //加载@require
    [self loadRequiredWithUserScript:newVersion forceReload:YES completion:nil];
    
    //加载@resource
    [self loadResourceWithUserScript:newVersion completion:nil];
    
    DatabaseUnit* unit = [DatabaseUnit updateUserScriptWithItem:newVersion];
    DB_EXEC(unit);
    
    [self saveUserScriptToCache:newVersion];
}

#pragma mark -- 根据脚本id删除脚本，包括脚本的缓存的资源
- (void)removeJSWithUserScript:(UserScript *)script
               completionBlock:(void(^)(void))completionBlock
{
    DatabaseUnit* unit = [DatabaseUnit removeUserScriptWithId:script.uuid];
    @weakify(self)
    [unit setCompleteBlock:^(id result, BOOL success) {
        @strongify(self)
        if(success) {
            [self removeJsFromScriptPathWithUUID:script.uuid];
        }
        
        if(completionBlock) {
            completionBlock();
        }
    }];
    
    DB_EXEC(unit);
}

#pragma mark -- 删除ScriptPath目录下的本地脚本和删除@required和删除@resource
- (void)removeJsFromScriptPathWithUUID:(NSString*)uuid
{    
    [self removeUserScriptFileWithUUID:uuid];
    
    //删除@required和删除@resource(实际上直接删除整个脚本目录)
    NSFileManager* fileManager = [NSFileManager defaultManager];
    NSString* userscriptPath;
    NSString* dir;
    userscriptPath = [NSFileManager userScriptPath];
    NSString* path = [uuid stringByReplacingOccurrencesOfString:@"-" withString:@"_"];
    dir = [NSString stringWithFormat:@"%@/%@", userscriptPath, path];
    if([fileManager fileExistsAtPath:dir]) {
        [fileManager removeItemAtPath:dir error:nil];
    }
    
    NSUserDefaults* userDefaults = [[NSUserDefaults alloc]initWithSuiteName:@"com.focus.userscripts"];
    [userDefaults removeObjectForKey:uuid];
    [userDefaults synchronize];
}

#pragma mark -- 只删除ScriptPath目录下的本地脚本
- (void)removeUserScriptFileWithUUID:(NSString*)uuid
{
    //将uuid转为_之后, 作为文件的名称
    NSString* fileName = [uuid stringByReplacingOccurrencesOfString:@"-" withString:@"_"];
    fileName = [NSString stringWithFormat:@"%@.js", fileName];

    NSString* filePath = [self.scriptPath stringByAppendingPathComponent:fileName];

    NSFileManager* fileManager = [NSFileManager defaultManager];

    //删除源码js
    if([fileManager fileExistsAtPath:filePath]) {
        [fileManager removeItemAtPath:filePath error:nil];
    }
}

#pragma mark -- 从ScriptPath中加载文件
- (NSString *)_loadJsFromScriptPathWithUUID:(NSString *)uuid
{
    NSString* fileName = [uuid stringByReplacingOccurrencesOfString:@"-" withString:@"_"];
    fileName = [NSString stringWithFormat:@"%@.js", fileName];

    //将uuid转为_之后, 作为文件的名称
    NSString* filePath = [self.scriptPath stringByAppendingPathComponent:fileName];

    NSFileManager* fileManager = [NSFileManager defaultManager];
    NSData* data = [fileManager contentsAtPath:filePath];
    NSString* content = [[NSString alloc]initWithData:data encoding:NSUTF8StringEncoding];

    return content;
}

#pragma mark -- 从bundle中加载文件
- (NSString *)_loadJsFromBundleWithFileName:(NSString *)scriptFileName
{
    NSString* path = [[NSBundle mainBundle]pathForResource:scriptFileName ofType:@"js"];
    NSError* error = nil;
    NSString* source = [NSString stringWithContentsOfFile:path encoding:NSUTF8StringEncoding error:&error];
    if(error) {
        NSLog(@"error = %@", error.localizedDescription);
    }

    return source;
}

#pragma mark -- 内存缓存相关
- (void)saveUserScriptToCache:(UserScript*)userScript
{
    self.uuidForUserScriptMapper[userScript.uuid] = userScript;
}

- (void)removeUserScriptCacheByUUID:(NSString*)uuid
{
    self.uuidForUserScriptMapper[uuid] = nil;
}

- (UserScript*)getUserScriptCacheByUUID:(NSString*)uuid
{
    return self.uuidForUserScriptMapper[uuid];
}

- (void)removeAllUserScriptsFromCache
{
    [self.uuidForUserScriptMapper removeAllObjects];
    self.uuidForUserScriptMapper = nil;
    
    self.uuidForUserScriptMapper = [NSMutableDictionary dictionary];
}

#pragma mark -- 判断脚本是否存在
- (UserScript *)isUserScriptExist:(UserScript *)userScript
{
    __block UserScript *exitsObj = nil;
    [self.uuidForUserScriptMapper enumerateKeysAndObjectsUsingBlock:^(NSString* _Nonnull uuid, UserScript*  _Nonnull obj, BOOL * _Nonnull stop) {
        if(userScript.updateUrl.length>0 && obj.updateUrl.length>0 && [userScript.updateUrl isEqualToString:obj.updateUrl]
           && [userScript.updateUrl.lowercaseString hasPrefix:@"http"]) {
            //策略一，如果都有安装url,那么比较url是否一致
            exitsObj = obj;
            *stop = YES;
        } else if(userScript.downloadUrl.length>0 && obj.downloadUrl.length>0 && [userScript.downloadUrl isEqualToString:obj.downloadUrl]
                  && [userScript.downloadUrl.lowercaseString hasPrefix:@"http"]) {
            //策略一，如果都有安装url,那么比较url是否一致
            exitsObj = obj;
            *stop = YES;
        } else if(userScript.author.length>0 && obj.author.length>0 && [userScript.author isEqualToString:obj.author]) {
            //策略二，如果作者名称和脚本名称一致，那么也是同一个脚本
            if([userScript.name isEqualToString:obj.name]) {
                exitsObj = obj;
                *stop = YES;
            }
        } else {
            if(userScript.name>0 && userScript.desc.length>0 && obj.name>0 && obj.desc.length>0) {
                //策略三，其他属性的组合判断，例如 name+desc
                if([userScript.name isEqualToString:obj.name] && [userScript.desc isEqualToString:obj.desc]) {
                    exitsObj = obj;
                    *stop = YES;
                }
            } else if(userScript.content.length>0 && obj.content.length>0) {
                //策略四，源码一致
                if([userScript.content isEqualToString:obj.content]) {
                    exitsObj = obj;
                    *stop = YES;
                }
            }
        }
    }];
    
    return exitsObj;
}

#pragma mark -- 获取缓存中所有的脚本
- (NSArray<UserScript*>*)getAllUserScriptsFromCache
{
    NSMutableArray* scripts = [NSMutableArray array];
    
    [self.uuidForUserScriptMapper enumerateKeysAndObjectsUsingBlock:^(NSString*  _Nonnull uuid, UserScript*  _Nonnull obj, BOOL * _Nonnull stop) {
        [scripts addObject:obj];
    }];
    
    return scripts;
}

#pragma mark -- 加载@require数据
- (void)loadRequiredWithUserScript:(UserScript *)userScript 
                       forceReload:(BOOL)forceReload
                        completion:(void(^)(NSString* requireCode))completion
{
    [self _loadRequiredWithScriptId:userScript.uuid
                        forceReload:forceReload
                        requireUrls:userScript.requireUrls
                         completion:^(NSString *requireCode) {
        if(completion) {
            completion(requireCode);
        }
    }];
}

//forceReload:强制从网络获取数据
- (void)_loadRequiredWithScriptId:(NSString *)scriptId
                      forceReload:(BOOL)forceReload
                      requireUrls:(NSArray *)requireUrls
                       completion:(void(^)(NSString* requireCode))completion
{
    if(requireUrls.count == 0) {
        if(completion) {
            completion(@"");
        }
        return;
    }
    
    NSString* userscriptPath;
    NSString* dir;
    NSFileManager* fm;
    
    userscriptPath = [NSFileManager userScriptPath];
    NSString* path = [scriptId stringByReplacingOccurrencesOfString:@"-" withString:@"_"];
    dir = [NSString stringWithFormat:@"%@/%@/require", userscriptPath, path];
    fm = [NSFileManager defaultManager];
    
    if(![fm fileExistsAtPath:dir]) {
        [fm createDirectoryAtPath:dir withIntermediateDirectories:YES attributes:nil error:nil];
    }
    
    NSMutableString* requireCode = [[NSMutableString alloc]init];
    
    dispatch_group_t group = dispatch_group_create();
    for(NSString *url in requireUrls) {
        dispatch_group_enter(group);
        
        //不能用url.lastPathComponent这个，因为有重名的
        NSString* fileName = [url md5];
        NSString* storageUrl = [NSString stringWithFormat:@"%@/%@",dir,fileName];
        
        BOOL needToLoadDataFromNetwork = true;
        if(!forceReload) {
            //可以读取本地数据
            if([fm fileExistsAtPath:storageUrl]) {
                NSError* error = nil;
                NSString* source = [NSString stringWithContentsOfFile:storageUrl encoding:NSUTF8StringEncoding error:&error];
                if(!error) {
                    if(source.length > 0) {
                        [requireCode appendFormat:@"%@ \n",source];
                        //只有拿到数据才不需要重新拿数据
                        needToLoadDataFromNetwork = false;
                    }
                }
            }
        } else {
            //强制从网络刷新数据
            needToLoadDataFromNetwork = true;
        }
        
        if(needToLoadDataFromNetwork) {
            [NetworkUtil requestGet:url completion:^(BOOL succ, id responseObject) {
                if(succ) {
                    dispatch_async(dispatch_get_main_queue(), ^{
                        NSString* jsContent = [[NSString alloc] initWithData:responseObject encoding:NSUTF8StringEncoding];
                        //v2.6.1 移除//# sourceMappingURL=xx
                        jsContent = [NSString removeSourceMappingURL:jsContent];
                        
                        NSString* fileName = [url md5];
                        NSString* storageUrl = [NSString stringWithFormat:@"%@/%@",dir,fileName];
                        if([fm fileExistsAtPath:storageUrl]) {
                            [fm removeItemAtPath:storageUrl error:nil];
                        }
                        [jsContent writeToFile:storageUrl atomically:YES encoding:NSUTF8StringEncoding error:nil];
                        
                        [requireCode appendFormat:@"%@ \n",jsContent];
                        
                        dispatch_group_leave(group);
                    });
                } else {
                    dispatch_group_leave(group);
                }
            }];
        } else {
            dispatch_group_leave(group);
        }
    }
    
    dispatch_group_notify(group, dispatch_get_main_queue(), ^{
        if(completion) {
            completion(requireCode);
        }
    });
}

#pragma mark -- 加载@resource数据
- (void)loadResourceWithUserScript:(UserScript *)userScript completion:(void(^)(void))completion
{
    [self _loadResourceWithScriptId:userScript.uuid resourceUrls:userScript.resourceUrls completion:^{
        if(completion) {
            completion();
        }
    }];
}

- (void)_loadResourceWithScriptId:(NSString *)scriptId
                     resourceUrls:(NSDictionary *)resourceUrls
                       completion:(void(^)(void))completion
{
    if(resourceUrls.count == 0) {
        if(completion) {
            completion();
        }
        return;
    }
    
    NSString* userscriptPath;
    NSString* dir;
    NSFileManager* fm;
    userscriptPath = [NSFileManager userScriptPath];
    NSString* path = [scriptId stringByReplacingOccurrencesOfString:@"-" withString:@"_"];
    dir = [NSString stringWithFormat:@"%@/%@/resource", userscriptPath, path];
    fm = [NSFileManager defaultManager];
    if(![fm fileExistsAtPath:dir]) {
        [fm createDirectoryAtPath:dir withIntermediateDirectories:YES attributes:nil error:nil];
    }
    
    dispatch_group_t group = dispatch_group_create();
    __block NSMutableArray* items = [NSMutableArray array];
    
    [resourceUrls enumerateKeysAndObjectsUsingBlock:^(NSString*  _Nonnull key, NSString*  _Nonnull url, BOOL * _Nonnull stop) {
        dispatch_group_enter(group);
        [NetworkUtil requestGet:url completion:^(BOOL succ, id responseObject) {
            if(succ) {
                NSString* jsContent = [[NSString alloc] initWithData:responseObject encoding:NSUTF8StringEncoding];
                GMResourceModel* item = [GMResourceModel new];
                item.key = key;
                item.url = url;
                item.text = jsContent;
                
                NSDictionary* dict = [item toDictionary];
                [items addObject:dict];
            }
            
            dispatch_group_leave(group);
        }];
    }];
    
    dispatch_group_notify(group, dispatch_get_main_queue(), ^{
        NSString* text = [NSString convertToJsonString:items options:NO];
        NSString* storageUrl = [NSString stringWithFormat:@"%@/resource.json",dir];
        if([fm fileExistsAtPath:storageUrl]) {
            [fm removeItemAtPath:storageUrl error:nil];
        }
        [text writeToFile:storageUrl atomically:YES encoding:NSUTF8StringEncoding error:nil];
        
        if(completion) {
            completion();
        }
    });
}

#pragma mark -- 删除@require/@resource
/// 删除@require/@resource
- (void)removeRequireAndResourceWithScriptIdArray:(NSArray *)scriptIds
{
    for(NSString* scriptId in scriptIds) {
        [self removeJsFromScriptPathWithUUID:scriptId];
    }
}


#pragma mark -- lazy init
- (JSContext *)jsContext
{
    if (!_jsContext) {
        //init js from lib
        NSArray *jsValues = @[
            [ResourceHelper shareInstance].supported_apis,
            [ResourceHelper shareInstance].convert2RegExp,
            [ResourceHelper shareInstance].MatchPattern,
            [ResourceHelper shareInstance].parse_meta_line,
            [ResourceHelper shareInstance].parse_user_script,
        ];
        _jsContext = [[JSContext alloc] initWithVirtualMachine:[[JSVirtualMachine alloc] init]];
        _jsContext[@"native"] = self;
        for (NSString *js in jsValues) {
            [_jsContext evaluateScript:js];
        }
        
        _jsContext.exceptionHandler = ^(JSContext *context, JSValue *exceptionValue) {
            context.exception = exceptionValue;
            NSLog(@"油猴脚本解析，捕获到异常：%@", exceptionValue);
        };
    }

    return _jsContext;
}

@end
