//
//  UserCommandManager.h
//  PPBrowser
//
//  Created by qingbin on 2022/5/20.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "BaseModel.h"
#import "Tab.h"
#import "AFNetworking.h"

@class UserComandModel;

//js引擎数据管理池
@interface UserCommandManager : BaseModel

- (instancetype)initWithTab:(Tab*)tab;

- (void)setValue:(UserComandModel*)item;

- (NSString*)getData:(UserComandModel*)item;

- (void)deleteValue:(UserComandModel*)item;

//注册菜单栏
- (void)registerMenuCommand:(UserComandModel*)item;
//解绑菜单栏
- (void)unregisterMenuCommand:(UserComandModel*)item;
//根据tabId和脚本的uuid，获取菜单栏信息
- (NSArray*)getMenusWithScriptId:(NSString*)scriptId;
//刷新网页时清理菜单栏
- (void)clearCommands;

- (void)xmlhttpRequest:(UserComandModel*)item;

- (void)handleGetResourceData:(UserComandModel*)item
                    completed:(void(^)(NSString* result))completedBlock;

@property (nonatomic, copy) void (^triggerRequestSuccessAction)(UserComandModel* item);

@property (nonatomic, copy) void (^triggerRequestErrorAction)(UserComandModel* item);

@property (nonatomic, copy) void (^triggerRequestTimeoutAction)(UserComandModel* item);

@property (nonatomic, copy) void (^triggerCommandAction)(UserComandModel* item);

@end

//主要用在saveTab/getTab
@interface UserCommandTabManager : NSObject

+ (instancetype)shareInstance;

- (void)saveTab:(UserComandModel *)item
          tabId:(NSString *)tabId;

- (NSString *)getTab:(NSString *)tabId;

@end

