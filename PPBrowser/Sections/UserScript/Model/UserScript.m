//
//  UserScript.m
//  PPBrowser
//
//  Created by qingbin on 2022/5/19.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "UserScript.h"
#import "NSString+Helper.h"
#import "Tampermonkey.h"

#import "AppDelegate.h"
#import "BrowserViewController.h"
#import "CloudKitHelper.h"
#import "OpenUDID.h"
#import "PPNotifications.h"
#import "NSFileManager+Helper.h"

#import "ResourceHelper.h"
#import "NSString+MKNetworkKitAdditions.h"

#import "DatabaseUnit+UserScript.h"

@implementation UserScript

- (void)setUuid:(NSString *)uuid
{
    if(uuid.length > 0) {
        _uuid = [uuid stringByReplacingOccurrencesOfString:@"-" withString:@"_"];
    }
}

- (NSArray<NSString *> *)matches
{
    if(_matches.count == 0 && _includes.count == 0) {
        _matches = @[@"*"];
    }
    
    return _matches;
}

- (NSString *)runAt
{
    if(_runAt.length == 0) {
        _runAt = @"document-end";
    }
    
    return _runAt;
}

- (NSArray<GMResourceInfoModel> *)resources
{
    if(self.requireUrls.count == 0) return nil;
    
    NSMutableArray<GMResourceInfoModel>* result = (NSMutableArray<GMResourceInfoModel> *)[NSMutableArray array];
    
    [self.resourceUrls enumerateKeysAndObjectsUsingBlock:^(NSString*  _Nonnull key, NSString*  _Nonnull obj, BOOL * _Nonnull stop) {
        GMResourceInfoModel* item = [GMResourceInfoModel new];
        item.name = key;
        item.url = obj;
        [result addObject:item];
    }];
    
    return result;
}

#pragma mark -- 重写
- (NSDictionary *)toDictionary
{
    NSDictionary* dict = [super toDictionary];
    NSMutableDictionary* mutableDict = [[NSMutableDictionary alloc]initWithDictionary:dict];

    //排除序列化
    mutableDict[@"isFirstInSection"] = nil;
    mutableDict[@"isLastInSection"] = nil;
    
    return [mutableDict copy];
}

- (NSString*)GM_Info
{
    GMInfoModel* info = [GMInfoModel new];
    info.script = [self _generateGM_Info];
    info.scriptMetaStr = [[Tampermonkey shareInstance] scriptMetaStringWithSourceCode:self.content];
    //有脚本会验证这个，通过GM_info.scriptHandler来获取
    //在Tampermonkey中，通过 alert(JSON.stringify(GM_info, null, 2)); 打印所有属性
    info.scriptHandler = @"Tampermonkey";
    info.version = @"5.3.3";
    
    return [info toJSONString];
}

#pragma mark -- 生成需要的GM_Info
- (UserScript*)_generateGM_Info
{
    //参考
    //https://wiki.greasespot.net/GM.info
    UserScript* result = [UserScript new];
    result.uuid = self.uuid;
    result.runAt = self.runAt;
    result.name = self.name;
    result.isActive = self.isActive;
    
    //将自定义的白名单和黑名单合并到matches和excludes中，这样代码逻辑统一
    NSMutableArray* matches = [NSMutableArray array];
    [matches addObjectsFromArray:self.matches];
    [matches addObjectsFromArray:[self getWhiteList]];
    
    NSMutableArray* excludes = [NSMutableArray array];
    [excludes addObjectsFromArray:self.excludes];
    [excludes addObjectsFromArray:[self getBlackList]];
    
//    result.matches = self.matches?self.matches:@[];
    result.matches = matches;
    result.includes = self.includes?self.includes:@[];
//    result.excludes = self.excludes?self.excludes:@[];
    result.excludes = excludes;
    
    result.requireUrls = self.requireUrls?self.requireUrls:@[];
    result.resourceUrls = self.resourceUrls?self.resourceUrls:@{};
    result.version = self.version?:@"0.0";
    //GM_info.script.namespace
    //适配https://greasyfork.org/zh-CN/scripts/462804-keepchatgpt/code
    result.namespace = self.namespace?:@"";
    
    return result;
}

//获取白名单
- (NSArray<NSString *> *)getWhiteList
{
    NSMutableArray* list = [NSMutableArray array];
    if(self.whiteList.length == 0) {
        return @[];
    }
    
    list = [[self.whiteList componentsSeparatedByString:@","] mutableCopy];
    
    return list;
}

//添加白名单
- (void)addWhiteListItem:(NSString *)whiteListItem
{
    if(whiteListItem.length == 0) return;
    
    NSMutableArray<NSString *> *whiteList = [[self getWhiteList] mutableCopy];
    if(![whiteList containsObject:whiteListItem]) {
        [whiteList addObject:whiteListItem];
    }
    
    self.whiteList = [whiteList componentsJoinedByString:@","];
    
    //保存到数据库和iCloud中
    DatabaseUnit* unit = [DatabaseUnit updateUserScriptWithItem:self];
    DB_EXEC(unit);
    
    //更新执行代码
    [self reloadUserScript];
}

//删除白名单
- (void)removeWhiteLisItem:(NSString *)whiteListItem
{
    if(whiteListItem.length == 0) return;
    
    NSMutableArray<NSString *> *whiteList = [[self getWhiteList] mutableCopy];
    [whiteList removeObject:whiteListItem];
    
    self.whiteList = [whiteList componentsJoinedByString:@","];
    
    //保存到数据库和iCloud中
    DatabaseUnit* unit = [DatabaseUnit updateUserScriptWithItem:self];
    DB_EXEC(unit);
    
    //更新执行代码
    [self reloadUserScript];
}

//获取黑名单
- (NSArray<NSString *> *)getBlackList
{
    NSMutableArray* list = [NSMutableArray array];
    if(self.blackList.length == 0) {
        return @[];
    }
    
    list = [[self.blackList componentsSeparatedByString:@","] mutableCopy];
    
    return list;
}

//添加黑名单
- (void)addBlackListItem:(NSString *)blackListItem
{
    if(blackListItem.length == 0) return;
    
    NSMutableArray<NSString *> *blackList = [[self getBlackList] mutableCopy];
    if(![blackList containsObject:blackListItem]) {
        [blackList addObject:blackListItem];
    }
    
    self.blackList = [blackList componentsJoinedByString:@","];
    
    //保存到数据库和iCloud中
    DatabaseUnit* unit = [DatabaseUnit updateUserScriptWithItem:self];
    DB_EXEC(unit);
    
    //更新执行代码
    [self reloadUserScript];
}

//删除黑名单
- (void)removeBlackLisItem:(NSString *)blackListItem
{
    if(blackListItem.length == 0) return;
    
    NSMutableArray<NSString *> *blackList = [[self getBlackList] mutableCopy];
    [blackList removeObject:blackListItem];
    
    self.blackList = [blackList componentsJoinedByString:@","];
    
    //保存到数据库和iCloud中
    DatabaseUnit* unit = [DatabaseUnit updateUserScriptWithItem:self];
    DB_EXEC(unit);
    
    //更新执行代码
    [self reloadUserScript];
}

#pragma mark - 修改脚本代码后，需要重新生成可执行代码，重新加载脚本
- (void)reloadUserScript
{
    [self updateExecutorJs];
    
    [[NSNotificationCenter defaultCenter] postNotificationName:kReloadUserScriptNotification object:nil];
}

// 从model初始化到sql字段
- (void)jsonModelToSql
{
    if(self.name.length == 0) {
        self.name = @"";
    }
    
    if(self.author.length == 0) {
        self.author = @"";
    }
    
    if(self.version.length == 0) {
        self.version = @"1.0.0";
    }
    
    if(self.namespace.length == 0) {
        self.namespace = @"";
    }
    
    if(self.desc.length == 0) {
        self.desc = @"";
    }
    
    if(self.iconUrl.length == 0) {
        self.iconUrl = @"";
    }
    
    if(self.updateUrl.length == 0) {
        self.updateUrl = @"";
    }
    
    if(self.downloadUrl.length == 0) {
        self.downloadUrl = @"";
    }
    
    if(self.content.length == 0) {
        self.content = @"";
    }
    
    if(self.includes.count > 0) {
        self.sql_includes = [self.includes componentsJoinedByString:@","];
    } else {
        self.sql_includes = @"";
    }
    
    if(self.matches.count > 0) {
        self.sql_matches = [self.matches componentsJoinedByString:@","];
    } else {
        self.sql_matches = @"";
    }
    
    if(self.excludes.count > 0) {
        self.sql_excludes = [self.excludes componentsJoinedByString:@","];
    } else {
        self.sql_excludes = @"";
    }
    
    if(self.grants.count > 0) {
        self.sql_grants = [self.grants componentsJoinedByString:@","];
    } else {
        self.sql_grants = @"";
    }
    
    if(self.requireUrls.count > 0) {
        self.sql_requireUrls = [self.requireUrls componentsJoinedByString:@","];
    } else {
        self.sql_requireUrls = @"";
    }
    
    if(self.resourceUrls.count > 0) {
        self.sql_resourceUrls = [NSString convertToJsonString:self.resourceUrls options:NO];
    } else {
        self.sql_resourceUrls = @"";
    }
}

// 从sql初始化到model
- (void)jsonModelFromSql
{
    if(self.sql_includes.length > 0) {
        self.includes = [self.sql_includes componentsSeparatedByString:@","];
    } else {
        self.includes = @[];
    }
    
    if(self.sql_matches.length > 0) {
        self.matches = [self.sql_matches componentsSeparatedByString:@","];
    } else {
        self.matches = @[];
    }
    
    if(self.sql_excludes.length > 0) {
        self.excludes = [self.sql_excludes componentsSeparatedByString:@","];
    } else {
        self.excludes = @[];
    }
    
    if(self.sql_grants.length > 0) {
        self.grants = [self.sql_grants componentsSeparatedByString:@","];
    } else {
        self.grants = @[];
    }
    
    if(self.sql_requireUrls.length > 0) {
        self.requireUrls = [self.sql_requireUrls componentsSeparatedByString:@","];
    } else {
        self.requireUrls = @[];
    }
    
    if(self.sql_resourceUrls.length > 0) {
        self.resourceUrls = [NSString jsonConvertToObject:self.sql_resourceUrls];
    } else {
        self.resourceUrls = @{};
    }
}

//获取脚本的安装链接/更新链接(其中一个),有时候安装链接和更新链接不是同一个
- (NSString *)getUpdateUrl
{
    NSString* url = nil;
//    if(self.updateUrl.length > 0) {
//        url = self.updateUrl;
//    } else if(self.downloadUrl.length > 0) {
//        url = self.downloadUrl;
//    }
    
    //先判断downloadUrl
    if(self.downloadUrl.length>0 && ![self.downloadUrl hasSuffix:@"meta.js"] && [self.downloadUrl.lowercaseString hasPrefix:@"http"]) {
        url = self.downloadUrl;
    } else if(self.updateUrl.length>0 && ![self.updateUrl hasSuffix:@"meta.js"] && [self.updateUrl.lowercaseString hasPrefix:@"http"]) {
        url = self.updateUrl;
    }
    
    return url;
}

// 生成可执行文件
- (void)updateExecutorJs
{
    NSString* executorJs = nil;
    @try {
        //这里有一个参数为空的崩溃
        //@require替换
        //处理@require, 在源码中替换requires
        //gear和stay都已经这样处理，我们也同步这种做法
        //主要是有些脚本会引用全局变量，如果不这样处理，那么会没法正常工作
        //加载@require
        NSMutableString* requireCode = [NSMutableString string];
        //查看有没有本地文件缓存
        NSString* userscriptPath;
        NSString* dir;
        NSFileManager* fm;
        userscriptPath = [NSFileManager userScriptPath];
        NSString* path = [self.uuid stringByReplacingOccurrencesOfString:@"-" withString:@"_"];
        dir = [NSString stringWithFormat:@"%@/%@/require", userscriptPath, path];
        fm = [NSFileManager defaultManager];
        if(![fm fileExistsAtPath:dir]) {
            [fm createDirectoryAtPath:dir withIntermediateDirectories:YES attributes:nil error:nil];
        }
        for(NSString* url in self.requireUrls) {
            NSString* fileName = [url md5];
            NSString* storageUrl = [NSString stringWithFormat:@"%@/%@",dir,fileName];

            if(![fm fileExistsAtPath:storageUrl]) {
                //旧版本逻辑
                fileName = url.lastPathComponent;
                storageUrl = [NSString stringWithFormat:@"%@/%@",dir,fileName];
            }
            
            if([fm fileExistsAtPath:storageUrl]) {
                NSError* error = nil;
                NSString* jsContent = [NSString stringWithContentsOfFile:storageUrl encoding:NSUTF8StringEncoding error:&error];
                if(!error) {
                    if(jsContent.length > 0) {
                        //v2.6.1 移除//# sourceMappingURL=xx
                        jsContent = [NSString removeSourceMappingURL:jsContent];
                        
                        [requireCode appendFormat:@"%@ \n",jsContent];
                    }
                } else {
                    NSLog(@"@require加载出错,error = %@", error.localizedDescription);
                }
            }
        }
        executorJs = [[ResourceHelper shareInstance].sourceCodeGenerate stringByReplacingOccurrencesOfString:@"{{GMRequireContent}}" withString:requireCode?:@""];

        //油猴脚本替换
        executorJs = [executorJs stringByReplacingOccurrencesOfString:@"{{GMScriptContent}}" withString:self.content?:@""];
    } @catch (NSException *exception) {
    } @finally {
    }

    if(executorJs.length > 0) {
        NSString* GMScriptInfo = [self GM_Info];
        executorJs = [executorJs stringByReplacingOccurrencesOfString:@"{{GMScriptInfo}}" withString:GMScriptInfo];
    }
    
    self.executorJs = executorJs;
}

//返回uuid
- (NSString*)getUuid
{
    return self.uuid;
}

//更新时间
- (NSString*)getUpdateTime
{
    return self.updateTime;
}

//CloudKit同步
//获取一个默认的CKRecord
- (CKRecord *)toDefaultCKRecord
{
    CKRecordID* recordID = [[CKRecordID alloc]initWithRecordName:self.uuid zoneID:[CloudKitHelper focusZoneID]];
    CKRecord* record = [[CKRecord alloc]initWithRecordType:NSStringFromClass(UserScript.class) recordID:recordID];
    
    return record;
}

//CloudKit同步
//转换成CKRecord
- (CKRecord *)toCKRecord
{
    //@"CREATE TABLE IF NOT EXISTS t_userscript(uuid TEXT PRIMARY KEY, name TEXT, author TEXT, namespace TEXT, version TEXT,  desc TEXT, iconUrl TEXT, runAt TEXT, selectFrame INTEGER, updateUrl TEXT, downloadUrl TEXT, content TEXT, isActive INTEGER, ppOrder INTEGER, sql_includes TEXT, sql_matches TEXT, sql_excludes TEXT, sql_grants TEXT, sql_requireUrls TEXT, sql_resourceUrls TEXT, ctime TEXT)"
//    NSString* recordIDString = [self.uuid stringByReplacingOccurrencesOfString:@"-" withString:@""];
//    recordIDString = [recordIDString stringByReplacingOccurrencesOfString:@"_" withString:@""];
    
    CKRecord* record = [self toDefaultCKRecord];
    
    [self jsonModelToSql];
    
    record[@"uuid"] = self.uuid;
    record[@"name"] = self.name?:@"";
    record[@"author"] = self.author?:@"";
    record[@"namespace"] = self.namespace?:@"";
    record[@"version"] = self.version?:@"";
    record[@"desc"] = self.desc?:@"";
//    record[@"iconUrl"] = self.iconUrl;
    record[@"runAt"] = self.runAt;
    record[@"selectFrame"] = @(self.selectFrame);
    record[@"updateUrl"] = self.updateUrl?:@"";
    record[@"downloadUrl"] = self.downloadUrl?:@"";
//    record[@"content"] = self.content;
    record[@"isActive"] = @(self.isActive);
    record[@"ppOrder"] = @(self.ppOrder);
    record[@"sql_includes"] = self.sql_includes?:@"";
    record[@"sql_matches"] = self.sql_matches?:@"";
    record[@"sql_excludes"] = self.sql_excludes?:@"";
    record[@"sql_grants"] = self.sql_grants?:@"";
    record[@"sql_requireUrls"] = self.sql_requireUrls?:@"";
    record[@"sql_resourceUrls"] = self.sql_resourceUrls?:@"";
    record[@"ctime"] = self.ctime;
    record[@"updateTime"] = self.updateTime?:@"1";
    record[@"isAutoUpdate"] = @(self.isAutoUpdate);
    record[@"frameOption"] = @(self.frameOption);
    record[@"whiteList"] = self.whiteList?:@"";
    record[@"blackList"] = self.blackList?:@"";
    
    //额外添加的字段，用于CloudKit
    //app版本号
    NSString* version = [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleShortVersionString"];
    record[@"appVersion"] = version?:@"";
    //上传到CloudKit创建时间
    record[@"appCtime"] = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
    //用户的uuid
    record[@"appUserID"] = [OpenUDID value]?:@"";
    //添加类型区分
    record[@"appType"] = @(CloudModelTypeUserScript);
    
    //将iconUrl和content转换为CKAsset
    NSFileManager* fileManager = [NSFileManager defaultManager];
    if(self.iconUrl.length > 0) {
        NSString* fileName = [NSString stringWithFormat:@"%@_iconUrl", self.uuid];
        NSURL* filePath = [[NSFileManager cloudKitPath] URLByAppendingPathComponent:fileName];
        
        //保存到Documents/CloudKit目录
        if([fileManager fileExistsAtPath:filePath.path]) {
            [fileManager removeItemAtURL:filePath error:nil];
        }
        [fileManager createFileAtPath:filePath.path contents:[self.iconUrl dataUsingEncoding:NSUTF8StringEncoding] attributes:nil];
    
        CKAsset* asset = [[CKAsset alloc]initWithFileURL:filePath];
        record[@"iconUrl"] = asset;
    }
    
    if(self.content.length > 0) {
        NSString* fileName = [NSString stringWithFormat:@"%@_content", self.uuid];
        NSURL* filePath = [[NSFileManager cloudKitPath] URLByAppendingPathComponent:fileName];
        
        //保存到Documents/CloudKit目录
        if([fileManager fileExistsAtPath:filePath.path]) {
            [fileManager removeItemAtURL:filePath error:nil];
        }
        [fileManager createFileAtPath:filePath.path contents:[self.content dataUsingEncoding:NSUTF8StringEncoding] attributes:nil];
    
        CKAsset* asset = [[CKAsset alloc]initWithFileURL:filePath];
        record[@"content"] = asset;
    }
    
    return record;
}
//从CKRecord转换为UserScript
- (instancetype)initWithCKRecord:(CKRecord *)record
{
    self = [super init];
    if(self) {
        self.uuid = record[@"uuid"];
        self.name = record[@"name"];
        self.author = record[@"author"];
        
        self.namespace = record[@"namespace"];
        self.version = record[@"version"];
        self.desc = record[@"desc"];
//        self.iconUrl = record[@"iconUrl"];
        self.runAt = record[@"runAt"];
        self.selectFrame = [[record objectForKey:@"selectFrame"] intValue];
        
        self.updateUrl = record[@"updateUrl"];
        self.downloadUrl = record[@"downloadUrl"];
//        self.content = record[@"content"];
        self.isActive = [[record objectForKey:@"isActive"] intValue];
        self.ppOrder = [[record objectForKey:@"ppOrder"] intValue];
        
        self.sql_includes = record[@"sql_includes"];
        self.sql_matches = record[@"sql_matches"];
        self.sql_excludes = record[@"sql_excludes"];
        self.sql_grants = record[@"sql_grants"];
        self.sql_requireUrls = record[@"sql_requireUrls"];
        self.sql_resourceUrls = record[@"sql_resourceUrls"];
        self.ctime = record[@"ctime"];
        
        self.isAutoUpdate = [[record objectForKey:@"isAutoUpdate"] intValue];
        self.frameOption = [[record objectForKey:@"frameOption"] intValue];
        self.whiteList = record[@"whiteList"];
        self.blackList = record[@"blackList"];
        
        NSString* updateTimeText = record[@"updateTime"];
        NSInteger updateTime = [updateTimeText integerValue];
        if(updateTime == 0 || updateTime == 1) {
            //初始化
            updateTime = [record.modificationDate timeIntervalSince1970];
        }
        self.updateTime = [NSString stringWithFormat:@"%ld", updateTime];
        
        [self jsonModelFromSql];
        
        //把CKAsset转换为iconUrl,content
        NSFileManager* fileManager = [NSFileManager defaultManager];
        
        CKAsset* iconUrlAsset = record[@"iconUrl"];
        if(iconUrlAsset) {
            self.iconUrl = [[NSString alloc]initWithData:[fileManager contentsAtPath:iconUrlAsset.fileURL.path] encoding:NSUTF8StringEncoding];
        }
        
        CKAsset* contentAsset = record[@"content"];
        if(contentAsset) {
            self.content = [[NSString alloc]initWithData:[fileManager contentsAtPath:contentAsset.fileURL.path] encoding:NSUTF8StringEncoding];
        }
    }
    
    return self;
}

//判断两个脚本是否一致，1、根据uuid,2、判断内容是否相等，即updateUrl/downloadUrl是否相等
- (BOOL)objectIsEqualTo:(id)obj
{
    UserScript* item = obj;
    if([self.uuid isEqualToString:item.uuid]) return YES;
    
    NSString* url = [self getUpdateUrl];    
    NSString* scriptUrl = [item getUpdateUrl];

    return [url isEqualToString:scriptUrl];
}

// 根据frameOption和selectFrame，综合获取当前的执行范围
- (BOOL)isRunAtMainFrame
{
    if(self.frameOption == UserScriptFrameOptionDefault) {
        //默认
        return self.selectFrame == 0;
    } else if(self.frameOption == UserScriptFrameOptionMain) {
        return true;
    } else if(self.frameOption == UserScriptFrameOptionAll) {
        return false;
    }
    
    return true;
}

@end


@implementation GMInfoModel
@end


@implementation GMResourceInfoModel
@end
