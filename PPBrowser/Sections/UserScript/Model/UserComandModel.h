//
//  UserComandModel.h
//  PPBrowser
//
//  Created by qingbin on 2022/5/19.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "BaseModel.h"

#import "PPEnums.h"

@interface UserComandModel : BaseModel

@property (nonatomic, strong) NSString *uuid;

@property (nonatomic, assign) UserScriptCommand command;

@property (nonatomic, strong) NSString *name;

@property (nonatomic, strong) NSString* value;

//用于对于同一个name产生的多个不同监听/或者网络请求/菜单栏id
@property (nonatomic, strong) NSString *randomId;
//kvo旧值
@property (nonatomic, strong) NSString *oldValue;

//xmlhttprequest请求参数
@property (nonatomic, strong) NSString *method;
@property (nonatomic, strong) NSString *url;
@property (nonatomic, strong) NSString *data;
@property (nonatomic, strong) NSDictionary *headers;
@property (nonatomic, assign) int timeout;
@property (nonatomic, strong) NSString *responseType;

//xmlhttprequest响应参数
//responseHeader的格式参考https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/getAllResponseHeaders
@property (nonatomic, strong) NSString* responseHeader;
@property (nonatomic, strong) NSString* responseUrl;
@property (nonatomic, assign) NSInteger statusCode;
@property (nonatomic, strong) NSString* responseContent;
@property (nonatomic, strong) NSString *mimeType;

//处理@resource
@property (nonatomic, strong) NSDictionary *resourceUrls;
//处理@require
@property (nonatomic, copy) NSArray<NSString *> *requireUrls;
//GM_download
//url
@property (nonatomic, strong) NSString* downloadURL;
//名称
@property (nonatomic, strong) NSString* downloadName;

//openInTab
//打开的url
@property (nonatomic, strong) NSString *openUrl;
//是否激活
@property (nonatomic, assign) BOOL isActive;
//是否以隐私方式打开
@property (nonatomic, assign) BOOL isPrivate;

//脚本的url
@property (nonatomic, strong) NSString *originalUrl;

//url是否正则匹配正确,表示是否能运行在当前网址
@property (nonatomic, assign) BOOL isMatched;

@end

@protocol UserComandModel <NSObject>
@end


@interface GMResourceModel : BaseModel

@property (nonatomic, copy) NSString *key;
@property (nonatomic, copy) NSString *text;
@property (nonatomic, copy) NSString *url;

@end

@protocol GMResourceModel <NSObject>
@end

