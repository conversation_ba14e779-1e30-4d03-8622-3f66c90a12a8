//
//  CustomRequestSerializer.h
//  PPBrowser
//
//  Created by qingbin on 2024/7/6.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import "AFURLRequestSerialization.h"

NS_ASSUME_NONNULL_BEGIN
/// 主要是为了适配油猴脚本的请求体
/// 因为如果直接使用AFNetworking的请求序列化器，那么item.data的处理是不正确的（如果是text/plain）的情况
/// 因为只需要直接传递就可以了。当然我们还需要根据content-type来区分不同的序列化器，因此我们处理了默认的情况
@interface CustomRequestSerializer : AFHTTPRequestSerializer

@end

NS_ASSUME_NONNULL_END
