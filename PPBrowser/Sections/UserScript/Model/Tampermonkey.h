//
//  Tampermonkey.h
//  PPBrowser
//
//  Created by qingbin on 2022/5/19.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>

#import <JavaScriptCore/JavaScriptCore.h>
#import "UserScript.h"
#import "ViaModel.h"

@protocol NativeExport <JSExport>

JSExportAs(nslog, - (void)nslog:(id)object);

@end

/*
 油猴脚本代码生成参考:
 https://gist.githubusercontent.com/arantius/3123124/raw/grant-none-shim.js
 https://wiki.greasespot.net/@grant
 https://gist.github.com/arantius
 脚本列表:
 https://greasyfork.org/zh-CN/scripts
 https://github.com/greasemonkey/greasemonkey
 
 Tampermonkey语法标准:
 https://www.tampermonkey.net/documentation.php?locale=en
 
 暴力猴标准:
 https://violentmonkey.github.io/api/gm/
 
 X浏览器油猴语法:
 https://www.xbext.com/docs/user-script-api-reference.html
 
 遇到的问题:
 调用了window.prompt方法，提示：Use of window.prompt is not allowed in a sandboxed frame when the allow-modals flag is not set.
 
 在 WKWebView 中遇到 window.prompt 的问题，提示 Use of window.prompt is not allowed in a sandboxed frame when the allow-modals flag is not set，通常是因为 WKWebView 的安全沙盒限制。默认情况下，WKWebView 禁止调用 window.prompt、window.alert 和 window.confirm 这些会阻塞用户交互的模态对话框。
 <iframe src="your-page.html" sandbox></iframe>
 
 //v2.6.1遇到的问题，在超星脚本中，用户需要加载https://lib.baomitu.com//blueimp-md5//2.19.0//js//md5.min.js这个require，
 但是这个md5.min.js中，竟然有一个"//# sourceMappingURL=md5.min.js.map"。
 然后引起了报错信息如下：Cannot load user-script://null/md5.min.js.map due to access control checks.
 claude的解析如下：
 这是因为 source map 的加载机制和安全限制导致的。让我详细解释一下：
 Source Map 的加载原理：
 当浏览器看到 //# sourceMappingURL=md5.min.js.map 这行注释时，它会尝试从相应的 URL 加载这个 .map 文件
 对于 Safari 扩展，这个路径会被解析为 user-script://null/md5.min.js.map
 错误产生的原因：
 Safari扩展运行在特殊的 user-script:// 协议下
 出于安全考虑，Safari 对这个协议下的资源访问有严格限制
 当试图加载.map文件时，由于访问控制检查（access control checks）不允许从这个特殊协议加载资源，所以会触发错误
 为什么会有这个限制：
 这是浏览器的安全机制的一部分，防止恶意代码通过扩展访问未经授权的资源
 Source maps 包含源代码信息，从安全角度来说需要特别的访问控制
 这就是为什么你看到"Cannot load user-script://null/md5.min.js.map due to access control checks" 这个错误的技术原因。移除这个 sourceMappingURL 注释是最简单的解决方案，因为在生产环境中通常并不需要 source map。
 
 */
@interface Tampermonkey : NSObject<NativeExport>

+ (instancetype)shareInstance;

/// 加载自动添加GreasyFork/openuserjs脚本
- (void)loadInstallJsHelperWithCompletion:(void(^)(UserScript *userScript))completion;

//测试js文件
- (void)loadTestJsHelperWithCompletion:(void(^)(UserScript *userScript))completion;

/// 解析via脚本meta信息
- (ViaModel*)parseViaWithSourceCode:(NSString*)source;

/// 根据uuid从ScriptPath重新加载源码, 生成用户脚本
- (void)loadJsWithUUID:(NSString*)uuid
            completion:(void(^)(UserScript *userScript))completion;


/// 根据scriptContent生成用户脚本, uuid是赋值用的
- (void)loadJsWithUUID:(NSString*)uuid
            sourceCode:(NSString*)scriptContent
            completion:(void(^)(UserScript *userScript))completion;

/// 获取油猴注释的头部
- (NSString*)scriptMetaStringWithSourceCode:(NSString *)sourceCode;

/// 保存js源文件到ScriptPath目录下
//- (void)saveToScriptPathWitSourceCode:(NSString*)scriptContent uuid:(NSString*)uuid;

/// 更新一个脚本
- (void)updateUserScriptWithNewVersion:(UserScript *)newVersion
                            oldVersion:(UserScript *)oldVersion;

/// 根据脚本id删除脚本，包括脚本的缓存的资源
- (void)removeJSWithUserScript:(UserScript *)script
               completionBlock:(void(^)(void))completionBlock;

/// 删除ScriptPath目录下的本地脚本和删除@required和删除@resource
- (void)removeJsFromScriptPathWithUUID:(NSString*)uuid;

/// 只删除ScriptPath目录下的本地脚本
- (void)removeUserScriptFileWithUUID:(NSString*)uuid;

/// 内存缓存相关
- (void)saveUserScriptToCache:(UserScript*)userScript;

- (void)removeUserScriptCacheByUUID:(NSString*)uuid;

- (UserScript*)getUserScriptCacheByUUID:(NSString*)uuid;

/// 获取缓存中所有的脚本
- (NSArray<UserScript*>*)getAllUserScriptsFromCache;

/// 判断脚本是否存在
- (UserScript *)isUserScriptExist:(UserScript *)userScript;

// 删除所有用户脚本
- (void)removeAllUserScriptsFromDisk;

// 删除所有内存缓存
- (void)removeAllUserScriptsFromCache;

// 加载@require数据
- (void)loadRequiredWithUserScript:(UserScript *)userScript
                       forceReload:(BOOL)forceReload
                        completion:(void(^)(NSString* requireCode))completion;

// 加载@resource数据
- (void)loadResourceWithUserScript:(UserScript *)userScript completion:(void(^)(void))completion;

/// 根据脚本内容解析得到脚本Model，主要是为了更新对比用
- (UserScript *)parseJSContent:(NSString *)jsContent;

/// 更新一个脚本
//- (void)updateUserScriptWithNewVersion:(UserScript *)newVersion
//                            oldVersion:(UserScript *)oldVersion;

/// 删除@require/@resource
- (void)removeRequireAndResourceWithScriptIdArray:(NSArray *)scriptIds;

@end

