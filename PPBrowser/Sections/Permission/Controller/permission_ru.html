<!DOCTYPE html>
<html><head>
<meta http-equiv="content-type" content="text/html; charset=UTF-8">
  <meta charset="UTF-8">
  <title>Focus Browser Privacy Policy</title>
  <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
      
  <style type="text/css">
      * {
          box-sizing: border-box;
          font-size: inherit;
          -webkit-text-size-adjust: none;
          -webkit-font-smoothing: antialiased;
      }
      
      body {
          color: #34495e;
          font-family: 'Source Sans Pro', 'Helvetica Neue', Arial, sans-serif;
          letter-spacing: 0;
      }
      
      h1 {
          font-size: 2rem;
          margin: 0 0 1rem;
          color: #2c3e50;
          font-weight: 600;
          display: block;
      }
      
      h2 {
          font-size: 1.75rem;
          margin: 30px 0 0.8rem;
          color: #2c3e50;
          font-weight: 600;
      }
      
      p {
          line-height: 1.6rem;
          word-spacing: 0.05rem;
          margin: 0 0;
      }
      
      a {
          color: #2D7AFE;
          font-weight: 600;
      }
      
      a:-webkit-any-link {
          cursor: pointer;
          text-decoration: underline;
      }
      
      .div {
         display: block;
      }
      
      .paragraphbottom {
          margin-bottom: 1.2rem;
      }
      
      .segmentbottom {
          margin-bottom: 0.8rem;
      }
      
  </style>
</head>
    <body>
    <div class="paragraphbottom">
        <p>Добро пожаловать в браузер Focus. Для лучшего использования браузера Focus, пожалуйста, внимательно прочитайте и поймите "Политику конфиденциальности".</p>
    </div>
      
    <div class="paragraphbottom">
      <p>Мы будем собирать и использовать информацию в соответствии с Политикой конфиденциальности, но не будем принудительно собирать пакетную информацию только потому, что вы согласились с Политикой конфиденциальности.</p>
    </div>

    <div class="paragraphbottom">
      <p>Вы можете просмотреть полную <a href="javascript:vod(0);" onclick="jumpToDetail();">"Политику конфиденциальности"</a>. Если вы согласны, пожалуйста, нажмите кнопку ниже, чтобы принять наш сервис</p>
    </div>

    <script type="text/javascript">
        function jumpToDetail() {
            webkit.messageHandlers.permissionMessageHandler.postMessage({"res":true});
        }
    </script>

    </body>

</html>
