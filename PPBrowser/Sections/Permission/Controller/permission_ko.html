<!DOCTYPE html>
<html><head>
<meta http-equiv="content-type" content="text/html; charset=UTF-8">
  <meta charset="UTF-8">
  <title>Focus Browser Privacy Policy</title>
  <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
      
  <style type="text/css">
      * {
          box-sizing: border-box;
          font-size: inherit;
          -webkit-text-size-adjust: none;
          -webkit-font-smoothing: antialiased;
      }
      
      body {
          color: #34495e;
          font-family: 'Source Sans Pro', 'Helvetica Neue', Arial, sans-serif;
          letter-spacing: 0;
      }
      
      h1 {
          font-size: 2rem;
          margin: 0 0 1rem;
          color: #2c3e50;
          font-weight: 600;
          display: block;
      }
      
      h2 {
          font-size: 1.75rem;
          margin: 30px 0 0.8rem;
          color: #2c3e50;
          font-weight: 600;
      }
      
      p {
          line-height: 1.6rem;
          word-spacing: 0.05rem;
          margin: 0 0;
      }
      
      a {
          color: #2D7AFE;
          font-weight: 600;
      }
      
      a:-webkit-any-link {
          cursor: pointer;
          text-decoration: underline;
      }
      
      .div {
         display: block;
      }
      
      .paragraphbottom {
          margin-bottom: 1.2rem;
      }
      
      .segmentbottom {
          margin-bottom: 0.8rem;
      }
      
  </style>
</head>
    <body>
    <div class="paragraphbottom">
        <p>Focus 브라우저에 오신 것을 환영합니다. Focus 브라우저를 더 잘 사용하기 위해 "개인정보 보호정책"을 충분히 읽고 이해해 주세요.</p>
    </div>
      
    <div class="paragraphbottom">
      <p>저희는 개인정보 보호정책에 따라 정보를 수집하고 사용할 것이지만, 개인정보 보호정책에 동의했다고 해서 번들 정보 수집을 강제하지는 않을 것입니다.</p>
    </div>

    <div class="paragraphbottom">
      <p>전체 <a href="javascript:vod(0);" onclick="jumpToDetail();">"개인정보 보호정책"</a>을 볼 수 있습니다. 동의하시면 아래 버튼을 클릭하여 저희 서비스를 수락해 주세요</p>
    </div>

    <script type="text/javascript">
        function jumpToDetail() {
            webkit.messageHandlers.permissionMessageHandler.postMessage({"res":true});
        }
    </script>

    </body>

</html>
