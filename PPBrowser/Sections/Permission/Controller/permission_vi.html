<!DOCTYPE html>
<html><head>
<meta http-equiv="content-type" content="text/html; charset=UTF-8">
  <meta charset="UTF-8">
  <title>Focus Browser Privacy Policy</title>
  <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
      
  <style type="text/css">
      * {
          box-sizing: border-box;
          font-size: inherit;
          -webkit-text-size-adjust: none;
          -webkit-font-smoothing: antialiased;
      }
      
      body {
          color: #34495e;
          font-family: 'Source Sans Pro', 'Helvetica Neue', Arial, sans-serif;
          letter-spacing: 0;
      }
      
      h1 {
          font-size: 2rem;
          margin: 0 0 1rem;
          color: #2c3e50;
          font-weight: 600;
          display: block;
      }
      
      h2 {
          font-size: 1.75rem;
          margin: 30px 0 0.8rem;
          color: #2c3e50;
          font-weight: 600;
      }
      
      p {
          line-height: 1.6rem;
          word-spacing: 0.05rem;
          margin: 0 0;
      }
      
      a {
          color: #2D7AFE;
          font-weight: 600;
      }
      
      a:-webkit-any-link {
          cursor: pointer;
          text-decoration: underline;
      }
      
      .div {
         display: block;
      }
      
      .paragraphbottom {
          margin-bottom: 1.2rem;
      }
      
      .segmentbottom {
          margin-bottom: 0.8rem;
      }
      
  </style>
</head>
    <body>
    <div class="paragraphbottom">
        <p>Chào mừng bạn đến với trình duyệt Focus. Để sử dụng trình duyệt Focus tốt hơn, vui lòng đọc và hiểu đầy đủ "Chính sách Bảo mật".</p>
    </div>
      
    <div class="paragraphbottom">
      <p>Chúng tôi sẽ thu thập và sử dụng thông tin theo Chính sách Bảo mật, nhưng sẽ không bắt buộc thu thập thông tin theo gói chỉ vì bạn đã đồng ý với Chính sách Bảo mật.</p>
    </div>

    <div class="paragraphbottom">
      <p>Bạn có thể xem <a href="javascript:vod(0);" onclick="jumpToDetail();">"Chính sách Bảo mật"</a> đầy đủ. Nếu bạn đồng ý, vui lòng nhấp vào nút bên dưới để chấp nhận dịch vụ của chúng tôi</p>
    </div>

    <script type="text/javascript">
        function jumpToDetail() {
            webkit.messageHandlers.permissionMessageHandler.postMessage({"res":true});
        }
    </script>

    </body>

</html>
