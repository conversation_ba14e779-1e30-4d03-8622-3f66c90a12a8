<!DOCTYPE html>
<html><head>
<meta http-equiv="content-type" content="text/html; charset=UTF-8">
  <meta charset="UTF-8">
  <title>Focus Browser Privacy Policy</title>
  <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
      
  <style type="text/css">
      * {
          box-sizing: border-box;
          font-size: inherit;
          -webkit-text-size-adjust: none;
          -webkit-font-smoothing: antialiased;
      }
      
      body {
          color: #34495e;
          font-family: 'Source Sans Pro', 'Helvetica Neue', Arial, sans-serif;
          letter-spacing: 0;
      }
      
      h1 {
          font-size: 2rem;
          margin: 0 0 1rem;
          color: #2c3e50;
          font-weight: 600;
          display: block;
      }
      
      h2 {
          font-size: 1.75rem;
          margin: 30px 0 0.8rem;
          color: #2c3e50;
          font-weight: 600;
      }
      
      p {
          line-height: 1.6rem;
          word-spacing: 0.05rem;
          margin: 0 0;
      }
      
      a {
          color: #2D7AFE;
          font-weight: 600;
      }
      
      a:-webkit-any-link {
          cursor: pointer;
          text-decoration: underline;
      }
      
      .div {
         display: block;
      }
      
      .paragraphbottom {
          margin-bottom: 1.2rem;
      }
      
      .segmentbottom {
          margin-bottom: 0.8rem;
      }
      
  </style>
</head>
    <body>
    <div class="paragraphbottom">
        <p>Focus ब्राउज़र में आपका स्वागत है। Focus ब्राउज़र का बेहतर उपयोग करने के लिए, कृपया "गोपनीयता नीति" को पूरी तरह से पढ़ें और समझें।</p>
    </div>
      
    <div class="paragraphbottom">
      <p>हम गोपनीयता नीति के अनुसार जानकारी एकत्र और उपयोग करेंगे, लेकिन आपके द्वारा गोपनीयता नीति से सहमत होने के कारण बंडल जानकारी संग्रह को मजबूर नहीं करेंगे।</p>
    </div>

    <div class="paragraphbottom">
      <p>आप पूर्ण <a href="javascript:vod(0);" onclick="jumpToDetail();">"गोपनीयता नीति"</a> देख सकते हैं। यदि आप सहमत हैं, तो हमारी सेवा स्वीकार करने के लिए नीचे दिए गए बटन पर क्लिक करें</p>
    </div>

    <script type="text/javascript">
        function jumpToDetail() {
            webkit.messageHandlers.permissionMessageHandler.postMessage({"res":true});
        }
    </script>

    </body>

</html>
