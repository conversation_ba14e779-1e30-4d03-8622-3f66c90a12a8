//
//  PermissionViewController.m
//  PPBrowser
//
//  Created by qingbin on 2022/8/22.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "PermissionViewController.h"

#import "MaizyHeader.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"
#import "UIView+FrameHelper.h"

#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "NSString+Helper.h"
#import "NSObject+Helper.h"
#import "ReactiveCocoa.h"
#import <WebKit/WebKit.h>

#import "WebViewController.h"
#import "PreferenceModel.h"
#import "PreferenceManager.h"

#import "BaseNavigationController.h"
#import "BrowserUtils.h"

@interface PermissionViewController ()<WKNavigationDelegate, WKUIDelegate, WKScriptMessageHandler>

@property (nonatomic, strong) WKWebView *webView;

@property (nonatomic, strong) WKUserContentController *userContentController;

@property (nonatomic, strong) UIButton *agreeBtn;
@property (nonatomic, strong) UIButton *rejectBtn;

@property (nonatomic, copy) void(^completion)(void);

@end

@implementation PermissionViewController

+ (void)showPermissionView:(void(^)(void))completion
{
    PermissionViewController *vc = [[PermissionViewController alloc] init];
    vc.completion = completion;
    
    BaseNavigationController* navc = [[BaseNavigationController alloc]initWithRootViewController:vc];
    navc.view.backgroundColor = UIColor.whiteColor;
//    navc.modalPresentationStyle = UIModalPresentationFullScreen;
    
    UIWindow* window = YBIBNormalWindow();
//    UINavigationController* rootNavc = (UINavigationController*)window.rootViewController;
//    [rootNavc presentViewController:navc animated:YES completion:nil];    
    [window.rootViewController presentCustomToViewController:navc];
}

- (void)viewDidLoad
{
    [super viewDidLoad];
    
    self.title = NSLocalizedString(@"privacy.title", nil);
    //防止下拉关闭,一定要选择同意或者拒绝
    self.modalInPresentation = YES;
    
    [self addSubViews];
    [self defineLayout];
    [self loadFileName:NSLocalizedString(@"privacy.fileUrl", nil)];
}

- (void)loadFileName:(NSString*)file
{
    NSString* url = [[NSBundle mainBundle] pathForResource:file ofType:@"html"];
    NSURL* fileUrl = [NSURL fileURLWithPath:url];
    [self.webView loadRequest:[NSURLRequest requestWithURL:fileUrl]];
}

- (void)addSubViews
{
    [self.view addSubview:self.webView];
    [self.view addSubview:self.agreeBtn];
    [self.view addSubview:self.rejectBtn];
}

- (void)defineLayout
{
    float offset = iPadValue(45, 20);
    float topOffset = iPadValue(30, 10);
    [self.webView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_offset(topOffset);
        make.left.mas_offset(offset);
        make.right.mas_offset(-offset);
        make.bottom.equalTo(self.agreeBtn.mas_top);
    }];
    
    float height = iPadValue(60, 44);
    [self.agreeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(offset);
        make.right.mas_offset(-offset);
        make.height.mas_equalTo(height);
        make.bottom.equalTo(self.rejectBtn.mas_top).offset(-iPadValue(20, 15));
    }];
    
    [self.rejectBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(offset);
        make.right.mas_offset(-offset);
        make.height.mas_equalTo(height);
        make.bottom.equalTo(self.view.mas_safeAreaLayoutGuideBottom).offset(-topOffset);
    }];
}

#pragma mark -- TabContentScript
- (NSString*)name
{
    return @"permissionMessageHandler";
}

- (NSString*)scriptMessageHandlerName
{
    return @"permissionMessageHandler";
}

#pragma mark -- WKScriptMessageHandler
- (void)userContentController:(WKUserContentController *)userContentController didReceiveScriptMessage:(WKScriptMessage *)message
{
    //https://dztx2022.github.io/focus_privacy.html
    //webkit.messageHandlers.permissionMessageHandler.postMessage({"res":true});
    //必须要带参数,否则不触发回调
    //a标签跳转参考http://www.wjhsh.net/SofuBlue-p-8283087.html
    WebViewController* vc = [[WebViewController alloc]initWithWebviewType:WebViewTypePush Title:NSLocalizedString(@"privacy.webview.title", nil)];
    
    NSURL* url = [NSURL URLWithString:NSLocalizedString(@"privacy.policy.fileUrl", nil)];
    NSURLRequest* request = [NSURLRequest requestWithURL:url];
    [vc loadRequest:request];

    [self.navigationController pushViewController:vc animated:YES];
}

- (WKUserContentController *)userContentController
{
    if (!_userContentController) {
        _userContentController = [[WKUserContentController alloc] init];
        [_userContentController addScriptMessageHandler:self name:[self scriptMessageHandlerName]];
    }
    return _userContentController;
}

- (WKWebView *)webView
{
    if (!_webView) {
        //! 使用添加了ScriptMessageHandler的userContentController配置configuration
        WKWebViewConfiguration *configuration = [[WKWebViewConfiguration alloc] init];
        configuration.userContentController = self.userContentController;
        
        _webView = [[WKWebView alloc] initWithFrame:CGRectZero configuration:configuration];
        _webView.navigationDelegate = self;
        _webView.UIDelegate = self;
    }
    return _webView;
}

- (UIButton *)agreeBtn
{
    if (!_agreeBtn) {
        _agreeBtn = [UIButton new];
        [_agreeBtn setTitle:NSLocalizedString(@"privacy.agree", nil) forState:UIControlStateNormal];
        _agreeBtn.titleLabel.font = [UIFont systemFontOfSize:iPadValue(20, 16)];
        [_agreeBtn setTitleColor:[UIColor colorWithHexString:@"#ffffff"] forState:UIControlStateNormal];
        @weakify(self)
        [[_agreeBtn rac_signalForControlEvents:UIControlEventTouchUpInside]
            subscribeNext:^(id x) {
            @strongify(self)
            
            // 更新状态
            [PreferenceManager shareInstance].items.isAgreedUMengPermission = @(YES);
            [[PreferenceManager shareInstance] encode];
            
            if (self.completion) {
                self.completion();
            }
            
            [self dismissViewControllerAnimated:YES completion:nil];
        }];

        _agreeBtn.layer.cornerRadius = 10.0f;
        _agreeBtn.layer.masksToBounds = YES;
        
        _agreeBtn.backgroundColor = [UIColor colorWithHexString:@"#0080fe"];
    }
    return _agreeBtn;
}

- (UIButton *)rejectBtn
{
    if (!_rejectBtn) {
        _rejectBtn = [UIButton new];
        [_rejectBtn setTitle:NSLocalizedString(@"privacy.disagreeAndExit", nil) forState:UIControlStateNormal];
        _rejectBtn.titleLabel.font = [UIFont systemFontOfSize:iPadValue(20, 16)];
        [_rejectBtn setTitleColor:[UIColor colorWithHexString:@"#ffffff"] forState:UIControlStateNormal];
        [[_rejectBtn rac_signalForControlEvents:UIControlEventTouchUpInside]
            subscribeNext:^(id x) {
            // 更新状态
            [PreferenceManager shareInstance].items.isAgreedUMengPermission = @(NO);
            [[PreferenceManager shareInstance] encode];
            
            exit(0);
        }];
        
        _rejectBtn.layer.cornerRadius = 10.0f;
        _rejectBtn.backgroundColor = [UIColor colorWithHexString:@"#999999"];
    }
    return _rejectBtn;
}

@end
