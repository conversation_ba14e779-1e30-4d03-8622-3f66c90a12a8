//
//  PermissionViewController.h
//  PPBrowser
//
//  Created by qingbin on 2022/8/22.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "BaseViewController.h"
#import "TabContentScript.h"

/*
 国际化适配
 https://blog.csdn.net/Mayerlucky/article/details/79425660
 https://www.jianshu.com/p/88c1b65e3ddb
 https://www.jianshu.com/p/13b1c0091736
 1、默认地区选择为美国
 2、默认语言选择为英语
 如果没有找到对应的国际化语言,那么默认就会显示为美国,语言选择为英语
 
 */

@interface PermissionViewController : BaseViewController<TabContentScript>

+ (void)showPermissionView:(void(^)(void))completion;

@end

