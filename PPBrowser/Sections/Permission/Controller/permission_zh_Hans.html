<!DOCTYPE html>
<html lang="en"><head>
<meta http-equiv="content-type" content="text/html; charset=UTF-8">
  <meta charset="UTF-8">
  <title>Focus浏览器隐私政策</title>
  <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
      
  <style type="text/css">
      * {
          box-sizing: border-box;
          font-size: inherit;
          -webkit-text-size-adjust: none;
          -webkit-font-smoothing: antialiased;
      }
      
      body {
          color: #34495e;
          font-family: 'Source Sans Pro', 'Helvetica Neue', Arial, sans-serif;
          letter-spacing: 0;
      }
      
      h1 {
          font-size: 2rem;
          margin: 0 0 1rem;
          color: #2c3e50;
          font-weight: 600;
          display: block;
      }
      
      h2 {
          font-size: 1.75rem;
          margin: 30px 0 0.8rem;
          color: #2c3e50;
          font-weight: 600;
      }
      
      p {
          line-height: 1.6rem;
          word-spacing: 0.05rem;
          margin: 0 0;
      }
      
      a {
          color: #2D7AFE;
          font-weight: 600;
      }
      
      a:-webkit-any-link {
          cursor: pointer;
          text-decoration: underline;
      }
      
      .div {
         display: block;
      }
      
      .paragraphbottom {
          margin-bottom: 1.2rem;
      }
      
      .segmentbottom {
          margin-bottom: 0.8rem;
      }
      
  </style>
</head>
<body>
<div class="paragraphbottom">
    <p>欢迎您使用Focus浏览器，为了让您更好地使用Focus浏览器，请充分阅读并理解《隐私政策》。</p>
</div>
  
<div class="paragraphbottom">
  <p>我们会遵循隐私政策收集、使用信息，但不会因同意了隐私政策而进行强制捆绑式的信息收集。</p>
</div>

<div class="paragraphbottom">
  <p>您可以查看完整版<a href="javascript:vod(0);" onclick="jumpToDetail();">《隐私政策》</a>，如果您同意，请点击下面的按钮以接受我们的服务</p>
</div>

<script type="text/javascript">
    function jumpToDetail() {
        webkit.messageHandlers.permissionMessageHandler.postMessage({"res":true});
    }
</script>

</body>
</html>
