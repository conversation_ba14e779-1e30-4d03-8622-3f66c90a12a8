//
//  TrialManager.m
//  PPBrowser
//
//  Created by qingbin on 2024/10/29.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import "TrialManager.h"
#import "PaymentManager.h"

@interface TrialManager () {
    NSUserDefaults *_customDefaults;
    NSMutableDictionary<NSNumber *, FeatureConfig *> *_featureConfigs;
}

@end

@implementation TrialManager

static NSString *const kCustomSuiteName = @"com.focus.trial";
static NSString *const kHomeVipLastCloseTimeKey = @"home_vip_last_close_time";

#pragma mark - Initialization

+ (instancetype)sharedManager
{
    static TrialManager *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[self alloc] init];
    });
    return instance;
}

- (instancetype)init
{
    self = [super init];
    if (self) {
        _customDefaults = [[NSUserDefaults alloc] initWithSuiteName:kCustomSuiteName];
        [self setupFeatureConfigs];
    }
    
    return self;
}

- (void)setupFeatureConfigs
{
    _featureConfigs = [NSMutableDictionary dictionary];
    
    // 翻译功能, 初始化可以试用5次，每天只能使用3次
    [self addFeatureConfig:[FeatureConfig configWithType:FeatureTypeTranslate
                                        totalTrialCount:5
                                        dailyTrialCount:3]];
    
    // 标记模式, 初始化可以试用5次，每天只能试用1次
    [self addFeatureConfig:[FeatureConfig configWithType:FeatureTypeTagit
                                        totalTrialCount:5
                                        dailyTrialCount:1]];
    
    // 阅读模式，文本过滤
    [self addFeatureConfig:[FeatureConfig configWithType:FeatureTypeTextFilter
                                        totalTrialCount:5
                                        dailyTrialCount:1]];
}

- (void)addFeatureConfig:(FeatureConfig *)config
{
    _featureConfigs[@(config.type)] = config;
}

#pragma mark - Public Methods

- (BOOL)canUseFeature:(FeatureType)featureType
{
    FeatureConfig *config = _featureConfigs[@(featureType)];
    if (!config) {
        return NO;  // 未配置的功能默认不能使用
    }
    
    NSString *totalCountKey = [self keyForFeature:featureType type:@"total"];
    NSString *dailyCountKey = [self keyForFeature:featureType type:@"daily"];
    NSString *lastDateKey = [self keyForFeature:featureType type:@"date"];
    
    // 获取总使用次数
    NSInteger totalCount = [_customDefaults integerForKey:totalCountKey];
    
    // 如果总次数在限制内
    if (totalCount < config.totalTrialCount) {
        [self incrementTotalCount:totalCountKey lastDateKey:lastDateKey];
        return YES;
    }
    
    // 检查是否是新的一天
    NSDate *lastUsageDate = [_customDefaults objectForKey:lastDateKey];
    NSDate *currentDate = [NSDate date];
    
    if (![self isSameDay:lastUsageDate asDate:currentDate]) {
        [self resetDailyCount:dailyCountKey];
    }
    
    // 获取当天使用次数
    NSInteger dailyCount = [_customDefaults integerForKey:dailyCountKey];
    
    if (dailyCount < config.dailyTrialCount) {
        [self incrementDailyCount:dailyCountKey lastDateKey:lastDateKey];
        return YES;
    } else {
        return NO;
    }
}

- (void)resetAllFeatureUsage
{
    for (NSNumber *featureType in _featureConfigs.allKeys) {
        [self resetFeatureUsage:featureType.integerValue];
    }
}

- (void)resetFeatureUsage:(FeatureType)featureType
{
    NSString *totalCountKey = [self keyForFeature:featureType type:@"total"];
    NSString *dailyCountKey = [self keyForFeature:featureType type:@"daily"];
    NSString *lastDateKey = [self keyForFeature:featureType type:@"date"];
    
    [_customDefaults removeObjectForKey:totalCountKey];
    [_customDefaults removeObjectForKey:dailyCountKey];
    [_customDefaults removeObjectForKey:lastDateKey];
    [_customDefaults synchronize];
}

#pragma mark - 是否显示首页入口提示

// 记录用户点击关闭的时间(首页会员入口提示时间)
- (void)recordHomeVipCloseTime
{
    NSDate *currentTime = [NSDate date];
    [_customDefaults setObject:currentTime forKey:kHomeVipLastCloseTimeKey];
    [_customDefaults synchronize];
}

// 判断是否显示首页会员入口提示，检查距离上次关闭是否已超过5天
- (BOOL)needShowHomeVip
{
    BOOL isVip = [[PaymentManager shareInstance] isVip];
    //如果是会员，那么不用显示
    if (isVip) {
        return NO;
    }
    
    NSDate *lastCloseTime = [_customDefaults objectForKey:kHomeVipLastCloseTimeKey];
    
    // 如果没有记录过关闭时间，那么需要显示
    if (!lastCloseTime) {
        return YES;
    }
    
    NSDate *currentTime = [NSDate date];
    NSTimeInterval timeInterval = [currentTime timeIntervalSinceDate:lastCloseTime];
    
    // 18天的秒数：18 * 24 * 60 * 60 = 432000
    float totalInterval = 18 * 24 * 60 * 60;
    // 大于等于18天，才需要显示
    return timeInterval >= totalInterval;
}

#pragma mark - Private Methods

- (NSString *)keyForFeature:(FeatureType)featureType type:(NSString *)type
{
    return [NSString stringWithFormat:@"feature_%ld_%@", (long)featureType, type];
}

- (void)incrementTotalCount:(NSString *)totalCountKey lastDateKey:(NSString *)lastDateKey
{
    NSInteger totalCount = [_customDefaults integerForKey:totalCountKey];
    [_customDefaults setInteger:totalCount + 1 forKey:totalCountKey];
    [_customDefaults setObject:[NSDate date] forKey:lastDateKey];
    [_customDefaults synchronize];
}

- (void)incrementDailyCount:(NSString *)dailyCountKey lastDateKey:(NSString *)lastDateKey
{
    NSInteger dailyCount = [_customDefaults integerForKey:dailyCountKey];
    [_customDefaults setInteger:dailyCount + 1 forKey:dailyCountKey];
    [_customDefaults setObject:[NSDate date] forKey:lastDateKey];
    [_customDefaults synchronize];
}

- (void)resetDailyCount:(NSString *)dailyCountKey
{
    [_customDefaults setInteger:0 forKey:dailyCountKey];
    [_customDefaults synchronize];
}

- (BOOL)isSameDay:(NSDate *)date1 asDate:(NSDate *)date2
{
    if (!date1 || !date2) {
        return NO;
    }
    
    NSCalendar *calendar = [NSCalendar currentCalendar];
    NSCalendarUnit units = NSCalendarUnitYear | NSCalendarUnitMonth | NSCalendarUnitDay;
    NSDateComponents *components1 = [calendar components:units fromDate:date1];
    NSDateComponents *components2 = [calendar components:units fromDate:date2];
    
    return components1.year == components2.year &&
           components1.month == components2.month &&
           components1.day == components2.day;
}

@end
