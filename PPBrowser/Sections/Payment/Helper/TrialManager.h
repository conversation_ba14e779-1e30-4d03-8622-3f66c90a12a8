//
//  TrialManager.h
//  PPBrowser
//
//  Created by qingbin on 2024/10/29.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "FeatureConfig.h"

@interface TrialManager : NSObject

+ (instancetype)sharedManager;

// 是否可以试用某个功能
- (BOOL)canUseFeature:(FeatureType)featureType;

// 重置所有功能的使用次数（比如用户登出时）
- (void)resetAllFeatureUsage;

// 重置特定功能的使用次数
- (void)resetFeatureUsage:(FeatureType)featureType;

// 记录用户点击关闭的时间(首页会员入口提示时间)
- (void)recordHomeVipCloseTime;

// 判断是否显示首页会员入口提示，检查距离上次关闭是否已超过15天
- (BOOL)needShowHomeVip;

@end

