//
//  FeatureConfig.m
//  PPBrowser
//
//  Created by qingbin on 2024/10/29.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import "FeatureConfig.h"

@implementation FeatureConfig

+ (instancetype)configWithType:(FeatureType)type
               totalTrialCount:(NSInteger)totalTrialCount
               dailyTrialCount:(NSInteger)dailyTrialCount
{
    FeatureConfig *config = [[FeatureConfig alloc] init];
    config.type = type;
    config.totalTrialCount = totalTrialCount;
    config.dailyTrialCount = dailyTrialCount;
    return config;
}

@end
