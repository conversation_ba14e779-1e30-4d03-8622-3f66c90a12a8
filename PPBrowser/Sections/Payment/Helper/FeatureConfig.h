//
//  FeatureConfig.h
//  PPBrowser
//
//  Created by qingbin on 2024/10/29.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>

typedef NS_ENUM(NSInteger, FeatureType) {
    FeatureTypeTagit = 0,    // 每天试用1次的功能
    FeatureTypeTranslate = 1, // 每天试用3次的功能
    FeatureTypeTextFilter = 2, // 每天试用1次的功能
};

@interface FeatureConfig : NSObject

@property (nonatomic, assign) FeatureType type;
@property (nonatomic, assign) NSInteger totalTrialCount;     // 总试用次数限制
@property (nonatomic, assign) NSInteger dailyTrialCount;     // 每日试用次数限制

+ (instancetype)configWithType:(FeatureType)type
               totalTrialCount:(NSInteger)totalTrialCount
               dailyTrialCount:(NSInteger)dailyTrialCount;

@end

