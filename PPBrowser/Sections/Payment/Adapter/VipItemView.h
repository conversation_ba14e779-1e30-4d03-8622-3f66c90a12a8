//
//  VipItemView.h
//  Saber
//
//  Created by q<PERSON><PERSON> on 2023/3/25.
//

#import <UIKit/UIKit.h>
#import "ThemeProtocol.h"
#import "PreferenceManager.h"

@interface VipItem : NSObject
// icon背景颜色
@property (nonatomic, strong) NSString *iconBackgroundColor;
// icon
@property (nonatomic, strong) NSString *icon;
// 标题
@property (nonatomic, strong) NSString *title;
// 描述
@property (nonatomic, strong) NSString *detail;

+ (instancetype)adItem;
+ (instancetype)tagItem;
+ (instancetype)translateItem;
+ (instancetype)scriptItem;
+ (instancetype)autoPageItem;
+ (instancetype)iCloudItem;
+ (instancetype)mutiPlatformItem;

@end

@interface VipItemView : UIView<ThemeProtocol>

- (void)updateWithModel:(VipItem *)model;

@property (readonly) UIView *line;

@end

