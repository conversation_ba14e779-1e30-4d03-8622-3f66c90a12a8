//
//  VipDescriptionView.m
//  MaizyClock
//
//  Created by qingbin on 2022/6/15.
//

#import "VipDescriptionView.h"

#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "NSObject+Helper.h"

#import "Masonry.h"
#import "ReactiveCocoa.h"
#import "BrowserUtils.h"

@interface VipDescriptionView()

@property (nonatomic, strong) UIImageView* logo;

@property (nonatomic, strong) UILabel *titleLabel;

@property (nonatomic, strong) UILabel *detailLabel;

@end

@implementation VipDescriptionView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    
    if(self) {
        [self addSubviews];
        [self defineLayout];
        
        self.layer.cornerRadius = 10;
        self.layer.masksToBounds = YES;
        self.backgroundColor = UIColor.whiteColor;
        
        [self applyTheme];
    }
    
    return self;
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        self.titleLabel.textColor = UIColor.whiteColor;
    } else {
        self.backgroundColor = UIColor.whiteColor;
        self.titleLabel.textColor = [UIColor colorWithHexString:@"#333333"];
    }
}

- (void)updateWithTitle:(NSString *)title
                 detail:(NSString *)detail
                   logo:(NSString *)logo
{
    self.titleLabel.text = title;
    self.detailLabel.text = detail;
    
    self.logo.image = [UIImage imageNamed:logo];
}

- (void)addSubviews
{
    [self addSubview:self.logo];
    [self addSubview:self.titleLabel];
    [self addSubview:self.detailLabel];
}

- (void)defineLayout
{
    [self.logo mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self);
        make.size.mas_equalTo(44);
        make.left.mas_offset(15);
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.logo.mas_right).offset(15);
        make.top.mas_offset(15);
    }];
    
    //决定高度
    [self.detailLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.titleLabel);
        make.right.mas_offset(-15);
        make.top.equalTo(self.titleLabel.mas_bottom).offset(8);
        
        make.bottom.equalTo(self).offset(-15);
    }];
}

- (UIImageView *)logo
{
    if(!_logo) {
        _logo = [UIImageView new];
    }
    
    return _logo;
}

- (UILabel *)titleLabel
{
    if(!_titleLabel) {
        float font = iPadValue(20, 16);
        _titleLabel = [UIView createLabelWithTitle:@""
                                        textColor:[UIColor colorWithHexString:@"#333333"]
                                          bgColor:UIColor.clearColor
                                         fontSize:font
                                    textAlignment:NSTextAlignmentLeft
                                            bBold:YES];
    }
    
    return _titleLabel;
}

- (UILabel *)detailLabel
{
    if(!_detailLabel) {
        float font = iPadValue(18, 14);
        _detailLabel = [UIView createLabelWithTitle:@""
                                        textColor:[UIColor colorWithHexString:@"#999999"]
                                          bgColor:UIColor.clearColor
                                         fontSize:font
                                    textAlignment:NSTextAlignmentLeft
                                            bBold:NO];
        _detailLabel.numberOfLines = 2;
    }
    
    return _detailLabel;
}

@end
