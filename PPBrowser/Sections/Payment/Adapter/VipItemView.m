//
//  VipItemView.m
//  Saber
//
//  Created by qingbin on 2023/3/25.
//

#import "VipItemView.h"

#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "NSObject+Helper.h"

#import "Masonry.h"
#import "ReactiveCocoa.h"
#import "BrowserUtils.h"

#import "YYText.h"

@interface VipItemView ()

@property (nonatomic, strong) UIImageView* logo;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *descLabel;
@property (nonatomic, strong) UIView *iconBackground;
@property (nonatomic, strong) UIView *cardBackground;

@end

@implementation VipItemView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    
    if(self) {
        [self addSubviews];
        [self defineLayout];
        
        [self applyTheme];
    }
    
    return self;
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.cardBackground.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        self.titleLabel.textColor = UIColor.whiteColor;
        self.descLabel.textColor = [UIColor colorWithHexString:@"#9ca3af"];
    } else {
        self.cardBackground.backgroundColor = [UIColor colorWithHexString:@"#f8f9fd"];
        self.titleLabel.textColor = [UIColor colorWithHexString:@"#1f2937"];
        self.descLabel.textColor = [UIColor colorWithHexString:@"#6b7280"];
    }
    
    // 设置阴影颜色
//    self.cardBackground.layer.shadowColor = isDarkTheme ? [UIColor blackColor].CGColor : [[UIColor blackColor] colorWithAlphaComponent:0.08].CGColor;
}

- (void)updateWithModel:(VipItem *)model
{
    self.iconBackground.backgroundColor = [UIColor colorWithHexString:model.iconBackgroundColor];
    self.logo.image = [[UIImage imageNamed:model.icon] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
    self.logo.tintColor = UIColor.whiteColor;
    
    self.titleLabel.text = model.title;
    self.descLabel.text = model.detail;
}

#pragma mark - layout

- (void)addSubviews
{
    [self addSubview:self.cardBackground];
    [self.cardBackground addSubview:self.iconBackground];
    [self.iconBackground addSubview:self.logo];
    [self.cardBackground addSubview:self.titleLabel];
    [self.cardBackground addSubview:self.descLabel];
}

- (void)defineLayout
{
    // 卡片背景布局
    float padding = iPadValue(8, 6);
    [self.cardBackground mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self).insets(UIEdgeInsetsMake(padding, 0, padding, 0));
    }];
    
    // 图标背景布局
    float iconSize = iPadValue(44, 36);
    [self.iconBackground mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(iconSize, iconSize));
        make.left.mas_offset(16);
        make.centerY.equalTo(self.cardBackground);
    }];
    
    // logo布局
    float logoSize = iconSize * 0.6;
    [self.logo mas_makeConstraints:^(MASConstraintMaker *make) {
        //宽度自适应
        make.height.mas_equalTo(logoSize);
        make.center.equalTo(self.iconBackground);
    }];
    
    // 标题布局
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.iconBackground.mas_right).offset(14);
        make.right.equalTo(self.cardBackground).offset(-14);
        make.top.equalTo(self.cardBackground).offset(iPadValue(16, 14));
    }];
    
    // 描述布局
    [self.descLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.titleLabel);
        make.right.equalTo(self.titleLabel);
        make.top.equalTo(self.titleLabel.mas_bottom).offset(iPadValue(10, 5));
        make.bottom.lessThanOrEqualTo(self.cardBackground).offset(-iPadValue(16, 14));
    }];
}

#pragma mark - getters

- (UIView *)cardBackground {
    if (!_cardBackground) {
        _cardBackground = [UIView new];
        _cardBackground.backgroundColor = [UIColor whiteColor];
        _cardBackground.layer.cornerRadius = 12.0;
        _cardBackground.layer.masksToBounds = NO;
        
        // 添加阴影
        _cardBackground.layer.shadowColor = [[UIColor blackColor] colorWithAlphaComponent:0.08].CGColor;
        _cardBackground.layer.shadowOffset = CGSizeMake(0, 2);
        _cardBackground.layer.shadowOpacity = 1.0;
        _cardBackground.layer.shadowRadius = 8.0;
    }
    return _cardBackground;
}

- (UIView *)iconBackground {
    if (!_iconBackground) {
        _iconBackground = [UIView new];
        _iconBackground.layer.cornerRadius = iPadValue(44, 36) / 3.0;
        _iconBackground.layer.masksToBounds = YES;
    }
    return _iconBackground;
}

- (UIImageView *)logo
{
    if(!_logo) {
        _logo = [UIImageView new];
        _logo.contentMode = UIViewContentModeScaleAspectFit;
        _logo.tintColor = [UIColor whiteColor];
    }
    
    return _logo;
}

- (UILabel *)titleLabel
{
    if(!_titleLabel) {
        float font = iPadValue(16, 15);
        _titleLabel = [UIView createLabelWithTitle:@""
                                        textColor:[UIColor colorWithHexString:@"#1f2937"]
                                          bgColor:UIColor.clearColor
                                         fontSize:font
                                    textAlignment:NSTextAlignmentLeft
                                            bBold:YES];
        _titleLabel.numberOfLines = 1;
    }
    
    return _titleLabel;
}

- (UILabel *)descLabel {
    if (!_descLabel) {
        float font = iPadValue(14, 13);
        _descLabel = [UIView createLabelWithTitle:@""
                                       textColor:[UIColor colorWithHexString:@"#6b7280"]
                                         bgColor:UIColor.clearColor
                                        fontSize:font
                                   textAlignment:NSTextAlignmentLeft
                                           bBold:NO];
        _descLabel.numberOfLines = 2;
    }
    return _descLabel;
}

@end


@implementation VipItem

+ (instancetype)adItem
{
    VipItem* item = [VipItem new];
    item.iconBackgroundColor = @"#FF3B30";
    item.icon = @"setting_ban";
    item.title = NSLocalizedString(@"vip.ad.free.title", nil);
    item.detail = NSLocalizedString(@"vip.ad.free.text", nil);
    
    return item;
}

+ (instancetype)tagItem
{
    VipItem* item = [VipItem new];
    item.iconBackgroundColor = @"#FF9500";
    item.icon = @"setting_highlighter";
    item.title = NSLocalizedString(@"vip.tagit.title", nil);
    item.detail = NSLocalizedString(@"vip.tagit.text", nil);
    
    return item;
}

+ (instancetype)translateItem
{
    VipItem* item = [VipItem new];
    item.iconBackgroundColor = @"#5AC8FA";
    item.icon = @"setting_language";
    item.title = NSLocalizedString(@"vip.translate.title", nil);
    item.detail = NSLocalizedString(@"vip.translate.text", nil);
    
    return item;
}

+ (instancetype)scriptItem
{
    VipItem* item = [VipItem new];
    item.iconBackgroundColor = @"#5856D6";
    item.icon = @"setting_code";
    item.title = NSLocalizedString(@"vip.script.title", nil);
    item.detail = NSLocalizedString(@"vip.script.text", nil);
    
    return item;
}

+ (instancetype)autoPageItem
{
    VipItem* item = [VipItem new];
    item.iconBackgroundColor = @"#AF52DE";
    item.icon = @"setting_autopage";
    item.title = NSLocalizedString(@"vip.autopage.title", nil);
    item.detail = NSLocalizedString(@"vip.autopage.text", nil);
    
    return item;
}

+ (instancetype)iCloudItem
{
    VipItem* item = [VipItem new];
    item.iconBackgroundColor = @"#64D2FF";
    item.icon = @"setting_cloud";
    item.title = NSLocalizedString(@"vip.icloud.title", nil);
    item.detail = NSLocalizedString(@"vip.icloud.text", nil);
    
    return item;
}

+ (instancetype)mutiPlatformItem
{
    VipItem* item = [VipItem new];
    item.iconBackgroundColor = @"#34C759";
    item.icon = @"setting_mobile";
    item.title = NSLocalizedString(@"vip.allplatform.title", nil);
    item.detail = NSLocalizedString(@"vip.allplatform.text", nil);
    
    return item;
}

@end
