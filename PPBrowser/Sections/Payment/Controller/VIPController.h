//
//  VIPController.h
//  MaizyClock
//
//  Created by q<PERSON><PERSON> on 2022/6/15.
//

#import "BaseViewController.h"

#import "ThemeProtocol.h"
#import "PreferenceManager.h"

/*
 票据验证
 https://www.jianshu.com/p/17e0d11149f3
 https://www.jianshu.com/p/4da5e9f36e6f
 
 https://developer.apple.com/cn/documentation/storekit/in-app_purchase/validating_receipts_with_the_app_store/
 
 https://developer.apple.com/documentation/appstorereceipts/verifyreceipt
 */

@interface VIPController : BaseViewController<ThemeProtocol>

/// 打开Vip页面
+ (void)jumpToVip:(void(^)(void))completion;

#pragma mark -- 打开Vip页面
+ (void)jumpToVipWithController:(UIViewController *)controller completion:(void(^)(void))completion;

// 检查是否是高级版会员，不是则弹出提示语
+ (BOOL)checkIsVipWithMessage:(NSString *)message
                   controller:(UIViewController *)controller;

@end

