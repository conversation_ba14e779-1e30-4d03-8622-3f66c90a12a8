//
//  VIPController.m
//  Maizy<PERSON>lock
//
//  Created by qingbin on 2022/6/15.
//

#import "VIPController.h"

#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "NSObject+Helper.h"

#import "MaizyHeader.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"
#import "BaseNavigationController.h"

#import "VipDescriptionView.h"
#import "PaymentManager.h"
#import "BrowserUtils.h"

#import "PaymentManager.h"

#import "PPNotifications.h"
#import "VipItemView.h"
#import "CommonGradientView.h"
#import "UIAlertController+SafePresentation.h"

@interface VIPController ()

@property (nonatomic, strong) UIScrollView* scrollView;
@property (nonatomic, strong) UIView* contentView;

//功能区域
@property (nonatomic, strong) UIImageView *sectionTitleImageView;
@property (nonatomic, strong) UILabel *sectionTitle;
@property (nonatomic, strong) UIStackView *featuresStackView;

//会员权益区域
@property (nonatomic, strong) UIImageView *benefitsSectionTitleImageView;
@property (nonatomic, strong) UILabel *benefitsSectionTitle;
@property (nonatomic, strong) UIView *benefitsContainer;
@property (nonatomic, strong) NSMutableArray *benefitItems;

@property (nonatomic, strong) UIButton* rightButton;

//立即购买
@property (nonatomic, strong) UIView* buyView;
@property (nonatomic, strong) UIButton* buyBtn;
@property (nonatomic, strong) CAGradientLayer *gradientLayer;

@property (nonatomic, strong) UIActivityIndicatorView* loadingView;

@property (nonatomic, assign) BOOL isVip;

@end

@implementation VIPController

- (void)viewDidLoad
{
    [super viewDidLoad];

    self.title = NSLocalizedString(@"setting.vipTitle", nil);
    self.navigationController.navigationBar.backgroundColor = UIColor.whiteColor;
    
    self.view.backgroundColor = [UIColor colorWithHexString:@"#f8f9fd"];
    
    self.isVip = [[PaymentManager shareInstance] isVip];
    
    [self createCustomLeftBarButtonItem];
    if(!self.isVip) {
        //不是vip才显示恢复购买
        [self _createCustomRightBarButtonItem];
    }
    
    [self addSubviews];
    [self defineLayout];
    [self setupObservers];
    
    [self becomeVip];
    
    [self applyTheme];
}

- (void)viewDidLayoutSubviews
{
    [super viewDidLayoutSubviews];
    
    [self updateGradientFrame];
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.sectionTitle.textColor = UIColor.whiteColor;
        self.benefitsSectionTitle.textColor = UIColor.whiteColor;
        [self.rightButton setTitleColor:[UIColor colorWithHexString:@"#60a5fa"] forState:UIControlStateNormal];
        self.benefitsContainer.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        
        self.buyView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
    } else {
        self.view.backgroundColor = [UIColor colorWithHexString:@"#f8f9fd"];
        self.sectionTitle.textColor = [UIColor colorWithHexString:@"#1f2937"];
        self.benefitsSectionTitle.textColor = [UIColor colorWithHexString:@"#1f2937"];
        [self.rightButton setTitleColor:[UIColor colorWithHexString:@"#3751FF"] forState:UIControlStateNormal];
        self.benefitsContainer.backgroundColor = [UIColor whiteColor];
        
        self.buyView.backgroundColor = UIColor.whiteColor;
    }
    
    // 更新专属功能
    for (VipItemView* view in self.featuresStackView.arrangedSubviews) {
        [view applyTheme];
    }
    
    // 更新会员权益视图的主题
    for (UIView *item in self.benefitItems) {
        UILabel *textLabel = [item viewWithTag:102];
        
        if (isDarkTheme) {
            item.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
            textLabel.textColor = [UIColor colorWithHexString:@"#ffffff"];
        } else {
            item.backgroundColor = [UIColor colorWithHexString:@"#f0f4ff"];
            textLabel.textColor = [UIColor colorWithHexString:@"#4b5563"];
        }
    }
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    //只有当前的controller会触发一次, 其它已存在的controller走通知
    if(needReload) {
        //修改暗黑模式
        [self applyTheme];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}

#pragma mark -- 检查是否是高级版会员，不是则弹出提示语
+ (BOOL)checkIsVipWithMessage:(NSString *)message
                   controller:(UIViewController *)controller
{
    BOOL isVip = [[PaymentManager shareInstance] isVip];
    if(!isVip) {
        UIAlertController* alertController = [UIAlertController alertControllerWithTitle:NSLocalizedString(@"vip.alert.title", nil) message:message preferredStyle:UIAlertControllerStyleAlert];
        UIAlertAction* action = [UIAlertAction actionWithTitle:NSLocalizedString(@"common.cancel", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            [alertController dismissViewControllerAnimated:YES completion:nil];
        }];
        [alertController addAction:action];

        action = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.toKnowMore", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            VIPController* vc = [[VIPController alloc]init];
            BaseNavigationController* navc = [[BaseNavigationController alloc]initWithRootViewController:vc];
            navc.view.backgroundColor = UIColor.whiteColor;

//            [controller presentViewController:navc animated:YES completion:nil];
            //v2.6.8 统一present
            [controller presentCustomToViewController:navc];
        }];
        [alertController addAction:action];

//        [controller presentViewController:alertController animated:YES completion:nil];
        //v2.6.8, 统一present方法，防止崩溃
        [alertController presentSafelyFromViewController:controller];
    }
        
    return isVip;
}

#pragma mark -- 打开Vip页面
+ (void)jumpToVip:(void(^)(void))completion
{
    VIPController* vc = [[VIPController alloc]init];
    BaseNavigationController* navc = [[BaseNavigationController alloc]initWithRootViewController:vc];
    navc.view.backgroundColor = UIColor.whiteColor;

    UIWindow* window = YBIBNormalWindow();
    UIViewController* root = window.rootViewController;
    [root presentViewController:navc animated:YES completion:completion];
}

#pragma mark -- 打开Vip页面
+ (void)jumpToVipWithController:(UIViewController *)controller completion:(void(^)(void))completion
{
    VIPController* vc = [[VIPController alloc]init];
    BaseNavigationController* navc = [[BaseNavigationController alloc]initWithRootViewController:vc];
    navc.view.backgroundColor = UIColor.whiteColor;

    [controller presentViewController:navc animated:YES completion:completion];
}

- (void)setupObservers
{
    @weakify(self)
    [[self.buyBtn rac_signalForControlEvents:UIControlEventTouchUpInside]
    subscribeNext:^(id x) {
        @strongify(self)
        [UIView showLoading:NSLocalizedString(@"tips.handling", nil)];
        @weakify(self)
        [[PaymentManager shareInstance] purchaseProductWithId:kVipProductId completion:^(TRPurchaseResult *res) {
            @strongify(self)
            [UIView hideHud:NO];
            if(res.succ) {
                //保存
                self.isVip = YES;
                
                [UIView showSucceed:NSLocalizedString(@"vip.buy.succeeded", nil)];
                
                [self becomeVip];
            } else {
                //保存
                self.isVip = NO;
                
                [UIView showFailed:NSLocalizedString(@"vip.buy.failed", nil)];
            }
            
            [[NSNotificationCenter defaultCenter] postNotificationName:kReloadVipNotification object:nil];
        }];
    }];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(updateGradientFrame)
                                                 name:kOrientationDidChangeNotification
                                               object:nil];
}

- (void)updateGradientFrame {
    //解决iPad旋转中，渐变层frame不正确的BUG
    CGSize screenSize = [BrowserUtils shareInstance].transitionToSize;
    float leftOffset = iPadValue(24, 20);
    self.gradientLayer.frame = CGRectMake(0, 0, screenSize.width-leftOffset*2, iPadValue(56, 56));
}

#pragma mark -- 购买按钮显示加载中
- (void)showLoading
{
    [self.loadingView startAnimating];
    self.loadingView.hidden = NO;
    
    [self.buyBtn setTitle:@"" forState:UIControlStateNormal];
    [self.buyBtn setBackgroundColor:[UIColor colorWithHexString:@"#b2b2b2"]];
    self.buyBtn.enabled = NO;
}

#pragma mark -- 购买按钮隐藏加载中
- (void)hideLoading
{
    [self.loadingView stopAnimating];
    self.loadingView.hidden = YES;
    
    // 恢复按钮样式
    self.buyBtn.enabled = YES;
}

#pragma mark -- 成为了vip
- (void)becomeVip
{
    if(!self.isVip) {
        [self getProductInfos];
        return;
    }
    
    [self hideLoading];
    
    //隐藏恢复购买
    self.rightButton.hidden = YES;
    
    //更新标题名称
    NSString* title = NSLocalizedString(@"vip.buy.becomeVip", nil);
    self.buyBtn.enabled = NO;
    
    [self.buyBtn setTitle:title forState:UIControlStateNormal];
}

#pragma mark -- 获取所有的内购列表
- (void)getProductInfos
{
    [self showLoading];
    @weakify(self)
    [[PaymentManager shareInstance] getProductInfos:^(BOOL succ, NSString *localizedTitle, NSString *localizedPrice) {
        @strongify(self)
        [self hideLoading];
        if(succ) {
            NSString* title = [NSString stringWithFormat:@"%@ · %@", localizedTitle, localizedPrice];
            [self.buyBtn setTitle:title forState:UIControlStateNormal];
        } else {
            NSString* title = NSLocalizedString(@"vip.buy.labelText", nil);
            [self.buyBtn setTitle:title forState:UIControlStateNormal];
        }
    }];
}

- (void)addSubviews
{
    [self.view addSubview:self.scrollView];
    [self.scrollView addSubview:self.contentView];
    
    [self.contentView addSubview:self.sectionTitleImageView];
    [self.contentView addSubview:self.sectionTitle];
    [self.contentView addSubview:self.featuresStackView];
    
    [self.contentView addSubview:self.benefitsSectionTitleImageView];
    [self.contentView addSubview:self.benefitsSectionTitle];
    [self.contentView addSubview:self.benefitsContainer];
    
    // 创建会员权益项
    self.benefitItems = [NSMutableArray array];
    
    NSArray *benefitTitles = @[
        NSLocalizedString(@"vip.benefit.forever", nil),
        NSLocalizedString(@"vip.benefit.update", nil),
        NSLocalizedString(@"vip.benefit.security", nil),
        NSLocalizedString(@"vip.benefit.support", nil)
    ];
    
    NSArray *benefitIcons = @[
        @"setting_infinity",
        @"setting_rotate",
        @"setting_shield",
        @"setting_headset"
    ];
    
    float benefitIconSize = iPadValue(28, 24);
    
    for (int i = 0; i < benefitTitles.count; i++) {
        UIView *benefitItem = [UIView new];
        benefitItem.backgroundColor = [UIColor colorWithHexString:@"#f0f4ff"];
        benefitItem.layer.cornerRadius = 10;
        [self.benefitsContainer addSubview:benefitItem];
        [self.benefitItems addObject:benefitItem];
        
        // 创建图标视图
        UIView *iconView = [UIView new];
        iconView.tag = 101;
        iconView.backgroundColor = [UIColor colorWithHexString:@"#1A3751FF"];
        iconView.layer.cornerRadius = benefitIconSize / 4.0;
        [benefitItem addSubview:iconView];
        
        // 创建图标
        UIImageView *iconImageView = [UIImageView new];
        iconImageView.contentMode = UIViewContentModeScaleAspectFit;
        iconImageView.tintColor = [UIColor colorWithHexString:@"#3751FF"];
        iconImageView.image = [[UIImage imageNamed:benefitIcons[i]] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        [iconView addSubview:iconImageView];
        
        // 创建文本标签
        UILabel *textLabel = [UILabel new];
        textLabel.tag = 102;
        textLabel.text = benefitTitles[i];
        textLabel.textColor = [UIColor colorWithHexString:@"#4b5563"];
        //适配英语环境，太长了
        int local = [NSLocalizedString(@"opensearch.value", nil) intValue];
        if (local == 2) {
            //英语环境
            textLabel.font = [UIFont systemFontOfSize:iPadValue(15, 13) weight:UIFontWeightRegular];
        } else {
            //中文
            textLabel.font = [UIFont systemFontOfSize:iPadValue(15, 15) weight:UIFontWeightMedium];
        }
       
        [benefitItem addSubview:textLabel];
        
        // 布局
        [iconView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(benefitItem).offset(10);
            make.centerY.equalTo(benefitItem);
            make.size.mas_equalTo(CGSizeMake(benefitIconSize, benefitIconSize));
        }];
        
        [iconImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.center.equalTo(iconView);
            make.size.mas_equalTo(CGSizeMake(benefitIconSize*0.6, benefitIconSize*0.6));
//            make.size.mas_equalTo(benefitIconSize);
        }];
        
        [textLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(iconView.mas_right).offset(iPadValue(15, 8));
            make.centerY.equalTo(benefitItem);
            make.right.equalTo(benefitItem).offset(-10);
        }];
    }
    
    [self.view addSubview:self.buyView];
    [self.buyView addSubview:self.buyBtn];
    [self.buyBtn addSubview:self.loadingView];
}

- (void)defineLayout
{
    [self.scrollView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.right.equalTo(self.view);
        make.bottom.equalTo(self.buyView.mas_top);
    }];

    [self.contentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.scrollView);
        make.width.equalTo(self.view);
        make.bottom.equalTo(self.benefitsContainer).offset(30);
    }];
    
    // 功能区布局
    float topOffset = iPadValue(30, 20);
    float leftOffset = iPadValue(24, 20);
    
    [self.sectionTitleImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentView).offset(topOffset);
        make.left.equalTo(self.contentView).offset(leftOffset);
        //宽度自适应
        make.size.mas_equalTo(iPadValue(24, 21));
    }];
    
    [self.sectionTitle mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.sectionTitleImageView);
        make.left.equalTo(self.sectionTitleImageView.mas_right).offset(iPadValue(10, 8));
    }];
    
    [self.featuresStackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.sectionTitle.mas_bottom).offset(16);
        make.left.equalTo(self.contentView).offset(leftOffset);
        make.right.equalTo(self.contentView).offset(-leftOffset);
    }];
    
    // 会员权益区布局
    [self.benefitsSectionTitleImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.featuresStackView.mas_bottom).offset(30);
        make.left.equalTo(self.contentView).offset(leftOffset);
        //宽度自适应
        make.size.mas_equalTo(iPadValue(24, 21));
    }];
    
    [self.benefitsSectionTitle mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.benefitsSectionTitleImageView);
        make.left.equalTo(self.sectionTitleImageView.mas_right).offset(iPadValue(10, 8));
    }];
    
    [self.benefitsContainer mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.benefitsSectionTitle.mas_bottom).offset(16);
        make.left.equalTo(self.contentView).offset(leftOffset);
        make.right.equalTo(self.contentView).offset(-leftOffset);
        make.height.mas_equalTo(iPadValue(130, 110));
    }];
    
    // 会员权益网格布局
    float benefitHeight = iPadValue(50, 40);
    float benefitMargin = 10;
    NSInteger cols = 2;
    __block UIView* lastView = nil;
    for (int i = 0; i < self.benefitItems.count; i++) {
        UIView *item = self.benefitItems[i];
        int row = i / cols;
        
        [item mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.benefitsContainer).offset(benefitMargin + row * (benefitHeight + benefitMargin));
            make.height.mas_equalTo(benefitHeight);
            if (lastView) {
                //右边
                make.width.equalTo(lastView);
                make.left.equalTo(lastView.mas_right).offset(benefitMargin);
                make.right.mas_offset(-benefitMargin);
                lastView = nil;
            } else {
                //左边
                make.left.mas_offset(benefitMargin);
                lastView = item;
            }
        }];
    }
    
    // 购买按钮布局
    UIWindow* window = [NSObject normalWindow];
    float bottomOffset = window.safeAreaInsets.bottom;
    if (bottomOffset <= 0) {
        bottomOffset = 20;
    }
    [self.buyView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.bottom.equalTo(self.view);
        make.top.equalTo(self.buyBtn).offset(-20);
    }];
    
    float btnHeight = iPadValue(56, 56);
    [self.buyBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).offset(leftOffset);
        make.right.equalTo(self.contentView).offset(-leftOffset);
        make.height.mas_equalTo(btnHeight);
        make.bottom.equalTo(self.buyView).offset(-bottomOffset);
    }];
    
    [self.loadingView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.buyBtn);
    }];
    
    // 添加功能项
    NSArray* itemArray = @[
        [VipItem adItem],
        [VipItem tagItem],
        [VipItem translateItem],
        [VipItem scriptItem],
        [VipItem autoPageItem],
        [VipItem iCloudItem],
        [VipItem mutiPlatformItem],
    ];
    
    for(VipItem *item in itemArray) {
        VipItemView* itemView = [VipItemView new];
        [itemView updateWithModel:item];
        [self.featuresStackView addArrangedSubview:itemView];
    }
}

- (void)_createCustomRightBarButtonItem
{
    UIButton* rightButton = [[UIButton alloc] initWithFrame:CGRectMake(0.0, 0.0f, 44.0f, 44.0)];
    [rightButton setBackgroundColor:[UIColor clearColor]];

    [rightButton setTitle:NSLocalizedString(@"vip.resume", nil) forState:UIControlStateNormal];
    [rightButton setTitleColor:[UIColor colorWithHexString:@"#2D7AFE"] forState:UIControlStateNormal];
    rightButton.titleLabel.font = [UIFont systemFontOfSize:16];
    
    [rightButton addTarget:self action:@selector(rightBarbuttonClick) forControlEvents:UIControlEventTouchUpInside];
    UIBarButtonItem* barItem = [[UIBarButtonItem alloc] initWithCustomView:rightButton];

    UIBarButtonItem *spacer = [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemFixedSpace target:nil action:nil];
    spacer.width = -16.0f; // for example shift right bar button to the right

    self.navigationItem.rightBarButtonItems = @[spacer, barItem];
    
    self.rightButton = rightButton;
}

- (void)rightBarbuttonClick
{
    [UIView showLoading:NSLocalizedString(@"tips.handling", nil)];
    //恢复购买
    [[PaymentManager shareInstance] restorePurchases:^(TRRestoreResults *res) {
        [UIView hideHud:NO];
        if(res.succ) {
            //恢复成功
            //保存
            self.isVip = YES;
            
            [UIView showSucceed:NSLocalizedString(@"vip.resume.succeeded", nil)];
            
            [self becomeVip];
        } else {
            //保存
            self.isVip = NO;
            
            //没有购买记录
            NSString* title = NSLocalizedString(@"vip.resume.failed.title", nil);
            NSString* msg = NSLocalizedString(@"vip.resume.failed.text", nil);
            UIAlertController* alertController = [UIAlertController alertControllerWithTitle:title message:msg preferredStyle:UIAlertControllerStyleAlert];
            UIAlertAction* action = [UIAlertAction actionWithTitle:NSLocalizedString(@"vip.resume.iknown", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            }];
            [alertController addAction:action];
            
//            [self presentViewController:alertController animated:YES completion:nil];
            //v2.6.8, 统一present方法，防止崩溃
            [alertController presentSafelyFromViewController:self];
        }
        
        [[NSNotificationCenter defaultCenter] postNotificationName:kReloadVipNotification object:nil];
    }];
}

#pragma mark -- lazy init

- (UIImageView *)sectionTitleImageView
{
    if (!_sectionTitleImageView) {
        _sectionTitleImageView = [UIImageView new];
        _sectionTitleImageView.image = [[UIImage imageNamed:@"setting_crown"] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        _sectionTitleImageView.tintColor = [UIColor colorWithHexString:@"#3751FF"];
        _sectionTitleImageView.contentMode = UIViewContentModeScaleAspectFit;
    }
    
    return _sectionTitleImageView;
}

- (UILabel *)sectionTitle
{
    if(!_sectionTitle) {
        _sectionTitle = [UILabel new];
        _sectionTitle.text = NSLocalizedString(@"vip.features.title", nil);
        _sectionTitle.textColor = [UIColor colorWithHexString:@"#1f2937"];
        _sectionTitle.font = [UIFont systemFontOfSize:iPadValue(18, 16) weight:UIFontWeightSemibold];
    }
    
    return _sectionTitle;
}

- (UIStackView *)featuresStackView
{
    if(!_featuresStackView) {
        _featuresStackView = [[UIStackView alloc]init];
        _featuresStackView.axis = UILayoutConstraintAxisVertical;
        _featuresStackView.spacing = 0;
        _featuresStackView.distribution = UIStackViewDistributionFill;
        _featuresStackView.alignment = UIStackViewAlignmentFill;
    }
    
    return _featuresStackView;
}

- (UIImageView *)benefitsSectionTitleImageView
{
    if (!_benefitsSectionTitleImageView) {
        _benefitsSectionTitleImageView = [UIImageView new];
        _benefitsSectionTitleImageView.image = [[UIImage imageNamed:@"vip_gift_icon"] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        _benefitsSectionTitleImageView.tintColor = [UIColor colorWithHexString:@"#3751FF"];
        _benefitsSectionTitleImageView.contentMode = UIViewContentModeScaleAspectFit;
    }
    
    return _benefitsSectionTitleImageView;
}

- (UILabel *)benefitsSectionTitle
{
    if (!_benefitsSectionTitle) {
        _benefitsSectionTitle = [UILabel new];
        _benefitsSectionTitle.text = NSLocalizedString(@"vip.benefits.title", nil);
        _benefitsSectionTitle.textColor = [UIColor colorWithHexString:@"#1f2937"];
        _benefitsSectionTitle.font = [UIFont systemFontOfSize:iPadValue(18, 16) weight:UIFontWeightSemibold];
    }
    return _benefitsSectionTitle;
}

- (UIView *)benefitsContainer
{
    if (!_benefitsContainer) {
        _benefitsContainer = [UIView new];
        _benefitsContainer.backgroundColor = [UIColor whiteColor];
        _benefitsContainer.layer.cornerRadius = 16;
        
        // 添加阴影
        _benefitsContainer.layer.shadowColor = [[UIColor blackColor] colorWithAlphaComponent:0.05].CGColor;
        _benefitsContainer.layer.shadowOffset = CGSizeMake(0, 2);
        _benefitsContainer.layer.shadowOpacity = 1.0;
        _benefitsContainer.layer.shadowRadius = 6.0;
        _benefitsContainer.layer.masksToBounds = NO;
    }
    return _benefitsContainer;
}

- (UIButton *)buyBtn
{
    if(!_buyBtn) {
        _buyBtn = [UIButton new];
        _buyBtn.backgroundColor = [UIColor clearColor];
        
        NSString* title = NSLocalizedString(@"vip.buy.labelText", nil);
        if(self.isVip) {
            title = NSLocalizedString(@"vip.buy.becomeVip", nil);
            _buyBtn.enabled = NO;
        }
        
        [_buyBtn setTitle:title forState:UIControlStateNormal];
        [_buyBtn setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
        _buyBtn.titleLabel.font = [UIFont systemFontOfSize:iPadValue(20, 16) weight:UIFontWeightSemibold];
        
//        float btnHeight = iPadValue(56, 56);
        _buyBtn.layer.cornerRadius = 14;
        _buyBtn.layer.masksToBounds = YES;
        
        // 设置按钮阴影 - 需要在外层视图上设置
        _buyBtn.layer.shadowColor = [[UIColor colorWithHexString:@"#3751FF"] colorWithAlphaComponent:0.3].CGColor;
        _buyBtn.layer.shadowOffset = CGSizeMake(0, 4);
        _buyBtn.layer.shadowOpacity = 1.0;
        _buyBtn.layer.shadowRadius = 8.0;
        
        // 添加新的渐变层
        CAGradientLayer *gradientLayer = [CAGradientLayer layer];
        gradientLayer.colors = @[
            (__bridge id)[UIColor colorWithHexString:@"#3751FF"].CGColor,
            (__bridge id)[UIColor colorWithHexString:@"#6E8AFF"].CGColor
        ];
        gradientLayer.startPoint = CGPointMake(0, 0.5);
        gradientLayer.endPoint = CGPointMake(1.0, 0.5);
        gradientLayer.cornerRadius = 14;
        self.gradientLayer = gradientLayer;
        
        [_buyBtn.layer insertSublayer:gradientLayer atIndex:0];
    }
    
    return _buyBtn;
}

- (UIActivityIndicatorView *)loadingView
{
    if(!_loadingView) {
        _loadingView = [[UIActivityIndicatorView alloc]initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleWhite];
        _loadingView.hidden = YES;
    }
    
    return _loadingView;
}

- (UIView *)contentView
{
    if(!_contentView) {
        _contentView = [[UIView alloc]init];
        _contentView.backgroundColor = UIColor.clearColor;
    }
    
    return _contentView;
}

- (UIScrollView *)scrollView
{
    if(!_scrollView) {
        _scrollView = [[UIScrollView alloc]init];
        _scrollView.showsVerticalScrollIndicator = NO;
        _scrollView.backgroundColor = UIColor.clearColor;
    }
    
    return _scrollView;
}

- (UIView *)buyView
{
    if(!_buyView) {
        _buyView = [[UIView alloc]init];
        _buyView.backgroundColor = UIColor.whiteColor;
    }
    
    return _buyView;
}

@end
