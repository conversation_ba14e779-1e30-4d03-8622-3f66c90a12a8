//
//  TRPurchaseResult.swift
//  MaizyClock
//
//  Created by qingbin on 2022/6/15.
//

import Foundation
import StoreKit
import MapKit

@objcMembers
public class TRPurchaseResult : NSObject {
    
    public let purchaseResult: PurchaseResult
    
    public var purchase: TRPurchaseDetails?
    
    public var succ: Int
    
    public var errorMsg: String?
    
    public init(result: PurchaseResult) {
        self.purchaseResult = result
        
        switch result {
        case .success(let purchase):
            self.succ = 1
            self.purchase = TRPurchaseDetails(purchaseDetails: purchase)
        case .deferred(let purchase):
            self.succ = 0
            self.purchase = TRPurchaseDetails(purchaseDetails: purchase)
        case .error(let error):
            self.succ = 0
            switch error.code {
            case .unknown:
                self.errorMsg = error.localizedDescription
            case .clientInvalid: // client is not allowed to issue the request, etc.
                self.errorMsg = "Not allowed to make the payment"
            case .paymentCancelled: // user cancelled the request, etc.
                self.errorMsg = "用户取消了购买"
            case .paymentInvalid: // purchase identifier was invalid, etc.
                self.errorMsg = "The purchase identifier was invalid"
            case .paymentNotAllowed: // this device is not allowed to make the payment
                self.errorMsg = "The device is not allowed to make the payment"
            case .storeProductNotAvailable: // Product is not available in the current storefront
                self.errorMsg = "The product is not available in the current storefront"
            case .cloudServicePermissionDenied: // user has not allowed access to cloud service information
                self.errorMsg = "Access to cloud service information is not allowed"
            case .cloudServiceNetworkConnectionFailed: // the device could not connect to the nework
                self.errorMsg = "Could not connect to the network"
            case .cloudServiceRevoked: // user has revoked permission to use this cloud service
                self.errorMsg = "Cloud service was revoked"
            default:
                self.errorMsg = error.localizedDescription
            }
        }
    }
}


