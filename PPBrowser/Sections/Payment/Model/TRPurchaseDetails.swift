//
//  TRPurchaseDetails.swift
//  MaizyClock
//
//  Created by qingbin on 2022/6/14.
//

import Foundation
import StoreKit

@objcMembers
public class TRPurchaseDetails : NSObject {
    
    private let purchaseDetails : PurchaseDetails
    
    public var productId: String {
        return self.purchaseDetails.productId
    }

    public var product: SKProduct {
        return self.purchaseDetails.product
    }
    
    public init(purchaseDetails: PurchaseDetails) {
        self.purchaseDetails = purchaseDetails
    }
    
}
