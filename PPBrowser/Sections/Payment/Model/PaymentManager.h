//
//  PaymentManager.h
//  MaizyClock
//
//  Created by qingbin on 2022/6/14.
//

#import <Foundation/Foundation.h>

#import <Foundation/Foundation.h>
#import <StoreKit/StoreKit.h>

#import "PPBrowser-Swift.h"

@interface PaymentManager : NSObject

+ (instancetype)shareInstance;

/// 是否是vip，从keychain中初始化
@property (nonatomic, assign) BOOL isVip;

/// 获取所有的内购列表
- (void)getProductInfos:(void(^)(BOOL succ, NSString* localizedTitle, NSString* localizedPrice))completion;

/// 根据商品ID购买商品
- (void)purchaseProductWithId:(NSString *)productId
                   completion:(void(^)(TRPurchaseResult*))completion;

/// 恢复购买
- (void)restorePurchases:(void(^)(TRRestoreResults*))completion;

/// keychain相关操作
// 保存 CKRecord.ID 到 Keychain
- (void)saveDataToKeychain:(NSString *)content forKey:(NSString *)key;

// 从 Keychain 中删除 CKRecord.ID
- (void)deleteDataFromKeychainWithKey:(NSString *)key;

// 从 Keychain 中获取 CKRecord.ID
- (NSString *)getDataFromKeychainWithKey:(NSString *)key;

// 修改 Keychain 中的 CKRecord.ID
- (void)updateDataInKeychain:(NSString *)newDataString forKey:(NSString *)key;

// 会员资格检测
- (void)verifyVipIfNeed;

@end

/*
 票据验证的思路:
 https://stackoverflow.com/questions/58615404/the-receipt-could-not-be-authenticated-should-it-be-checked-again
 
 From my experience, the "21003 - The receipt could not be authenticated." status is related to the App-Specific Shared Secret.
 When you validate receipts with the AppStore, the App-Specific Shared Secret is used to set the value of the password field in the JSON request that you sent to the AppStore validation endpoint. (See Validating Receipts with the App Store documentation)
 Keep in mind that according to Apple's documentation, this apps-specific shared secret is only necessary when validating receipt for apps that use auto-renewable subscriptions. However, in my experience, it might be worth setting it for any app receipt validation, just to avoid the 21003 error.
 1、即使是非消耗商品，也需要传SharedSecret, SharedSecret在"APP信息"中生成
 2、为了防止打扰用户(针对有多AppleID的情况)，增加了方案：
 如果是直接购买或者恢复购买，在keychain中保存一个时间戳，每次启动APP都会校验一次keychain中的时间戳
 如果时间戳在一个月内，那么则不需要校验票据(receipt)。如果时间戳大于一个月，那么则需要校验票据信息，票据验证成功则更新时间戳。
 票据信息主要是验证当前登录的appstore的Apple ID，是否当初购买的的Apple ID。如果不是，那么则取消会员资格，
 只有在恢复购买或者直接购买成功之后，才会更新时间戳。
 
 3、更优化的方案，则是验证当初购买的iCloud ID。因为一般情况下都不会切换iCloud ID，iCloud ID比较隐私。
 因此每次购买成功或者恢复购买都将iCloud ID保存到keychain。这个是比较优化的方案。
 */
