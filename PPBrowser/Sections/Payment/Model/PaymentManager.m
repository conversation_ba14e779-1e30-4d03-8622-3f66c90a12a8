//
//  PaymentManager.m
//  MaizyClock
//
//  Created by qingbin on 2022/6/14.
//

#import "PaymentManager.h"

#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "NSObject+Helper.h"
#import "UIView+Helper.h"

#import <Security/Security.h>

@interface PaymentManager ()

@property (nonatomic, strong) TRSwiftyStoreKit *storeKit;

@end

@implementation PaymentManager

+ (instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    static PaymentManager* obj;
    dispatch_once(&onceToken, ^{
        obj = [PaymentManager new];
    });
    
    return obj;
}

- (instancetype)init
{
    self = [super init];
    if(self) {
        self.storeKit = [TRSwiftyStoreKit new];
        
        //https://github.com/bizz84/SwiftyStoreKit/issues/287
        [self.storeKit completeTransactions];
        //检查vip的状态
        self.isVip = [self checkVipFromKeyChain];
    }
    
    return self;
}

#pragma mark -- 获取所有的内购列表
- (void)getProductInfos:(void(^)(BOOL succ, NSString* localizedTitle, NSString* localizedPrice))completion
{
    [self.storeKit getProductInfo:@[kVipProductId] handler:^(BOOL succ, NSArray<SKProduct *> *results) {
        if(succ) {
            if(results.count > 0) {
                SKProduct* product = results.firstObject;
                NSString* localizedPrice = [TRSwiftyStoreKit localizedPrice:product];
                
                if(completion) {
                    completion(YES, product.localizedTitle, localizedPrice);
                }
                
                NSLog(@"price = %@, title = %@", localizedPrice, product.localizedTitle);
            }
        } else {
            if(completion) {
                completion(NO, nil, nil);
            }
            
            NSLog(@"获取价格信息失败...");
        }
    }];
}

#pragma mark -- 根据商品ID购买商品
- (void)purchaseProductWithId:(NSString *)productId
                   completion:(void(^)(TRPurchaseResult*))completion
{
    [self.storeKit purchaseWithProductId:productId completion:^(TRPurchaseResult * result) {
        if(result.succ) {
            //成功
            NSLog(@"购买成功: %@", result.purchase);
            
            //保存时间戳到keychain
            [self saveCurrentTimestamp];
            //保存vip状态
            [self saveVipStatusWithIsVip:YES];
        } else {
            //失败
            NSLog(@"购买失败: %@", result.errorMsg);
            
            //保存vip状态
            [self saveVipStatusWithIsVip:NO];
        }
        
        if(completion) {
            completion(result);
        }
    }];
}

#pragma mark -- 恢复购买
- (void)restorePurchases:(void(^)(TRRestoreResults*))completion
{
    [self.storeKit restorePurchasesWithCompletion:^(TRRestoreResults * result) {
        if(result.succ) {
            //有非消耗商品
            NSLog(@"恢复购买成功: %@", result);
            
            //保存时间戳到keychain
            [self saveCurrentTimestamp];
            //保存vip状态
            [self saveVipStatusWithIsVip:YES];
        } else {
            //没有非消耗商品
            NSLog(@"恢复购买失败: %@", result.errorMsg);
            
            //保存vip状态
            [self saveVipStatusWithIsVip:NO];
        }
        
        if(completion) {
            completion(result);
        }
    }];
}

#pragma mark -- 从keychain检测是否是vip
- (BOOL)checkVipFromKeyChain
{
    BOOL isVip = NO;
    
    //获取vip的值
    NSString* isVipString = [self getDataFromKeychainWithKey:[self keyForVip]];
    if([isVipString isEqualToString:@"true"]) {
        isVip = YES;
    } else {
        isVip = NO;
    }
    
//#if DEBUG
//    isVip = YES;
//#endif
    
    return isVip;
}

//保存是否是vip到keychain
- (void)saveVipStatusWithIsVip:(BOOL)isVip
{
    self.isVip = isVip;
    
    NSString* value = @"false";
    if(isVip) {
        value = @"true";
    } else {
        value = @"false";
    }
    
    NSString* isVipString = [self getDataFromKeychainWithKey:[self keyForVip]];
    if(isVipString.length == 0) {
        //没有记录，则新增
        [self saveDataToKeychain:value forKey:[self keyForVip]];
    } else {
        //有记录，则更新
        [self updateDataInKeychain:value forKey:[self keyForVip]];
    }
}

#pragma mark -- 校验是否购买成功
- (void)verifyPurchaseWithCompletion:(void(^)(BOOL succ))completion
{
    //校验的步骤:
    //1、ReceiptInfo中的status==0
    //2、ReceiptInfo - receipt - in_app必须有值
    //3、校验in_app中必须存在kVipProductId
    //4、根据cancellation_date过滤退款订单
    [self.storeKit verifyPurchaseWithSharedSecret:kSharedSecret
                                        productId:kVipProductId
                                       completion:completion];
}

// 保存时间戳
- (void)saveCurrentTimestamp
{
    //保存时间戳到keychain
    NSTimeInterval timestamp = [[NSDate date] timeIntervalSince1970];
    NSString *timestampString = [NSString stringWithFormat:@"%ld", (long)timestamp];
    NSString* lastTimestamp = [[PaymentManager shareInstance] getDataFromKeychainWithKey:[self keyForTimestamp]];
    if(lastTimestamp.length == 0) {
        //没有记录，则新增
        [self saveDataToKeychain:timestampString forKey:[self keyForTimestamp]];
    } else {
        //有记录，则更新
        [self updateDataInKeychain:timestampString forKey:[self keyForTimestamp]];
    }
}

// 保存 CKRecord.ID 到 Keychain
- (void)saveDataToKeychain:(NSString *)content forKey:(NSString *)key
{
    NSDictionary *query = @{
        (__bridge id)kSecClass: (__bridge id)kSecClassGenericPassword,
        (__bridge id)kSecAttrAccessible: (__bridge id)kSecAttrAccessibleAfterFirstUnlock,
        (__bridge id)kSecValueData: [content dataUsingEncoding:NSUTF8StringEncoding],
        (__bridge id)kSecAttrService: key // 标识你的应用
    };
    
    OSStatus status = SecItemAdd((__bridge CFDictionaryRef)query, NULL);
    if (status == errSecSuccess) {
        NSLog(@"成功保存数据到Keychain...");
    } else {
        NSLog(@"保存数据Keychain失败: %d", (int)status);
    }
}

// 从 Keychain 中删除 CKRecord.ID
- (void)deleteDataFromKeychainWithKey:(NSString *)key
{
    NSDictionary *query = @{
        (__bridge id)kSecClass: (__bridge id)kSecClassGenericPassword,
        (__bridge id)kSecAttrService: key // 标识你的应用
    };
    
    OSStatus status = SecItemDelete((__bridge CFDictionaryRef)query);
    if (status == errSecSuccess) {
        NSLog(@"成功删除Keychain中的数据");
    } else {
        NSLog(@"删除Keychain中的数据失败: %d", (int)status);
    }
}

// 从 Keychain 中获取 CKRecord.ID
- (NSString *)getDataFromKeychainWithKey:(NSString *)key
{
    NSDictionary *query = @{
        (__bridge id)kSecClass: (__bridge id)kSecClassGenericPassword,
        (__bridge id)kSecAttrService: key, // 标识你的应用
        (__bridge id)kSecReturnData: (__bridge id)kCFBooleanTrue,
        (__bridge id)kSecMatchLimit: (__bridge id)kSecMatchLimitOne
    };
    
    CFTypeRef result = NULL;
    OSStatus status = SecItemCopyMatching((__bridge CFDictionaryRef)query, &result);
    if (status == errSecSuccess && result != NULL) {
        NSData *data = (__bridge_transfer NSData *)result;
        NSString *content = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
        return content;
    } else {
        NSLog(@"从Keychain中获取数据失败: %d", (int)status);
        return nil;
    }
}

// 修改 Keychain 中的 CKRecord.ID
- (void)updateDataInKeychain:(NSString *)newDataString forKey:(NSString *)key
{
    NSDictionary *query = @{
        (__bridge id)kSecClass: (__bridge id)kSecClassGenericPassword,
        (__bridge id)kSecAttrService: key // 标识你的应用
    };
    
    NSDictionary *update = @{
        (__bridge id)kSecValueData: [newDataString dataUsingEncoding:NSUTF8StringEncoding]
    };
    
    OSStatus status = SecItemUpdate((__bridge CFDictionaryRef)query, (__bridge CFDictionaryRef)update);
    if (status == errSecSuccess) {
        NSLog(@"成功更新Keychain中的数据");
    } else {
        NSLog(@"从Keychain中更新数据失败: %d", (int)status);
    }
}

- (NSString *)keyForTimestamp
{
    NSString* key = [NSString stringWithFormat:@"%@-%@",[[NSBundle mainBundle] bundleIdentifier], @"timestamp"];
    return key;
}

- (NSString *)keyForVip
{
    NSString* key = [NSString stringWithFormat:@"%@-%@",[[NSBundle mainBundle] bundleIdentifier], @"vip"];
    return key;
}

// 会员资格检测
- (void)verifyVipIfNeed
{
    //只检测会员
    if(!self.isVip) return;
    
//#if DEBUG
//    //debug模式不检测
//    return;
//#endif
    
    @weakify(self)
    void (^executeBlock)(void) = ^{
        @strongify(self)
        @weakify(self)
        [self verifyPurchaseWithCompletion:^(BOOL succ) {
            @strongify(self)
            if(succ) {
                //receipt检测成功，插入或者时间戳
                NSLog(@"receipt校验成功...");
                [self saveCurrentTimestamp];
                //保存vip状态
                self.isVip = YES;
                [self saveVipStatusWithIsVip:YES];
            } else {
                //receipt检测失败，删除会员资格
                NSLog(@"receipt校验失败...");
                self.isVip = NO;
                [self saveVipStatusWithIsVip:NO];
            }
        }];
    };
    
    NSString* lastTimestampString = [[PaymentManager shareInstance] getDataFromKeychainWithKey:[self keyForTimestamp]];
    if(lastTimestampString.length <= 0) {
        //旧会员的没有插入时间戳，那么需要检测，只有检测成功之后才会插入时间戳
        executeBlock();
    } else {
        //有时间戳，那么检测是否到了检测条件(7天检测一次)
        NSInteger currentTimestamp = [[NSDate date] timeIntervalSince1970];
        NSInteger lastTimestamp = [lastTimestampString integerValue];
        if(currentTimestamp < lastTimestamp) {
            //这个是不正常的数据，检测会员资格
            executeBlock();
        } else if(currentTimestamp - lastTimestamp >= 60*60*24*7) {
            //大于7天时间，检测会员资格
            //60*60*24*7
            executeBlock();
        } else {
            //在7天内，那么不需要检测，防止打扰用户
        }
    }
}

@end
