//
//  TRSwiftyStoreKit.swift
//  MaizyClock
//
//  Created by qing<PERSON> on 2022/6/14.
//

import Foundation
import StoreKit

@objcMembers
public class TRSwiftyStoreKit : NSObject {
    
    /// 转换
    public static func localizedPrice(_ product: SKProduct) -> String {
        return product.localizedPrice ?? ""
    }
    
    ///fix bugs
    public func completeTransactions() {
        SwiftyStoreKit.completeTransactions(atomically: true) { purchases in
            for purchase in purchases {
                if(purchase.transaction.transactionState == .purchased
                   || purchase.transaction.transactionState == .restored) {
                    if purchase.needsFinishTransaction {
                        SwiftyStoreKit.finishTransaction(purchase.transaction)
                    }
                }
            }
        }
    }
    
    /// 获取内购列表
    public func getProductInfo(_ productIds: [String], handler: @escaping (Bool, Array<SKProduct>) -> Void) {
        let productIdSet:Set<String> = Set(productIds)
        SwiftyStoreKit.retrieveProductsInfo(productIdSet) { result in
            let succ:Bool = !result.retrievedProducts.isEmpty
            var products: [SKProduct] = [];
            if succ {
                products = result.retrievedProducts.map{$0};
            }
            
            handler(succ, products)
        }
    }
    
    /// 点击购买商品
    public func purchase(productId: String, completion: @escaping (TRPurchaseResult?) -> Void) {
        SwiftyStoreKit.purchaseProduct(productId, atomically: true) { result in
            if case .success(let purchase) = result {
                let downloads = purchase.transaction.downloads
                if !downloads.isEmpty {
                    SwiftyStoreKit.start(downloads)
                }
                // Deliver content from server, then:
                if purchase.needsFinishTransaction {
                    SwiftyStoreKit.finishTransaction(purchase.transaction)
                }
            }
            
            let res = TRPurchaseResult(result: result)
            completion(res)
        }
    }
    
    /// 检验购买凭证
    func _verifyReceipt(sharedSecret:String, completion: @escaping (VerifyReceiptResult) -> Void) {
        #if DEBUG
            let appleValidator = AppleReceiptValidator(service: .sandbox, sharedSecret: sharedSecret)
        #else
            let appleValidator = AppleReceiptValidator(service: .production, sharedSecret: sharedSecret)
        #endif
        
        //强制从苹果接口拿数据
        //只验证购买收据，不再强制校验账号
        SwiftyStoreKit.verifyReceipt(using: appleValidator, forceRefresh: false, completion: completion)
    }
    
    /// 检验购买凭证
    public func verifyReceipt(sharedSecret:String, completion: @escaping (TRVerifyReceiptResult) -> Void) {
        _verifyReceipt(sharedSecret: sharedSecret) { result in
            let res = TRVerifyReceiptResult(result: result)
            completion(res)
        }
    }
    
    /// 检验是否已经购买
    public func verifyPurchase(sharedSecret:String, productId: String, completion: @escaping (Bool) -> Void) {
        _verifyReceipt(sharedSecret: sharedSecret) { result in
            switch result {
            case .success(let receipt):
                let purchaseResult = SwiftyStoreKit.verifyPurchase(productId: productId, inReceipt: receipt)
                /// 处理成功
                switch purchaseResult {
                    case .purchased(let receiptItem):
                        print("\(productId) is purchased: \(receiptItem)")
                        completion(true)
                    case .notPurchased:
                        print("The user has never purchased \(productId)")
                        completion(false)
                }
            case .error:
                /// 购买凭证处理失败
                completion(false)
                print("\(productId) get receipt error!")
            }
        }
    }
    
    /// 检验订阅
    public func verifySubscriptions(sharedSecret:String, productIds: [String], completion: @escaping (Bool, TRVerifySubscriptionResult?) -> Void) {
        let productIdSet:Set<String> = Set(productIds)
        _verifyReceipt(sharedSecret: sharedSecret) { result in
            switch result {
            case .success(let receipt):
                let purchaseResult = SwiftyStoreKit.verifySubscriptions(productIds: productIdSet, inReceipt: receipt)
                /// 处理成功
                let res = TRVerifySubscriptionResult(result: purchaseResult)
                var succ: Bool = false
                if res.succ == 1 {
                    succ = true
                }
                completion(succ, res)
            case .error:
                /// 购买凭证处理失败
                completion(false, nil)
            }
        }
    }
    
    /// 恢复购买(针对非消耗商品)
    public func restorePurchases(completion: @escaping (TRRestoreResults) -> Void) {
        SwiftyStoreKit.restorePurchases(atomically: true) { results in
            for purchase in results.restoredPurchases {
                let downloads = purchase.transaction.downloads
                if !downloads.isEmpty {
                    SwiftyStoreKit.start(downloads)
                } else if purchase.needsFinishTransaction {
                    // Deliver content from server, then:
                    SwiftyStoreKit.finishTransaction(purchase.transaction)
                }
            }
            
            let res = TRRestoreResults(result: results)
            completion(res)
        }
    }
}
