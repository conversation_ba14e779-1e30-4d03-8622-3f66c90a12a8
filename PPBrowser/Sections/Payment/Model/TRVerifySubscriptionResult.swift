//
//  TRVerifySubscriptionResult.swift
//  MaizyClock
//
//  Created by qingbin on 2022/6/15.
//

import Foundation

@objcMembers
public class TRVerifySubscriptionResult : NSObject {
    
    public var succ: Int
    
    public var errorMsg: String?
    
    private let result : VerifySubscriptionResult
    
    public init(result: VerifySubscriptionResult) {
        
        self.result = result
        
        switch result {
        case .purchased(let expiryDate, let items):
            self.succ = 1;
            print("valid until \(expiryDate)\n\(items)\n")
        case .expired(let expiryDate, let items):
            self.succ = 0;
            self.errorMsg = "Expired since \(expiryDate)\n\(items)\n"
        case .notPurchased:
            self.succ = 0;
            self.errorMsg = "This product has never been purchased"
        }
    }
}
    
