//
//  TRRestoreResults.swift
//  MaizyClock
//
//  Created by qing<PERSON> on 2022/6/15.
//

import Foundation

@objcMembers
public class TRRestoreResults : NSObject {
    
    private let result : RestoreResults
    
    public var succ: Int
    
    public var errorMsg: String?
    
    public init(result: RestoreResults) {
        self.result = result
        
        if result.restoreFailedPurchases.count > 0 {
            self.succ = 0
            self.errorMsg = "Unknown error. Please contact support"
        } else if result.restoredPurchases.count > 0 {
            self.succ = 1
        } else {
            self.succ = 0
            self.errorMsg = "No previous purchases were found"
        }
    }
}

