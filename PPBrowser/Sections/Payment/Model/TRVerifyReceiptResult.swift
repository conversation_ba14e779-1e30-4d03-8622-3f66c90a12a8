//
//  TRVerifyReceiptResult.swift
//  MaizyClock
//
//  Created by qingbin on 2022/6/15.
//

import Foundation

@objcMembers
public class TRVerifyReceiptResult : NSObject {
    
    public var succ: Int
    
    public var errorMsg: String?
    
    private let result : VerifyReceiptResult
    
    public init(result: VerifyReceiptResult) {
        self.result = result
        
        switch result {
        case .success(let receipt):
            print("Verify receipt Success: \(receipt)")
            self.succ = 1
        case .error(let error):
            self.succ = 0
            switch error {
            case .noReceiptData:
                self.errorMsg = "No receipt data. Try again"
            case .networkError(let error):
                self.errorMsg = "Network error while verifying receipt: \(error)"
            default:
                self.errorMsg = "Receipt verification failed: \(error)"
            }
        }
    }
}
