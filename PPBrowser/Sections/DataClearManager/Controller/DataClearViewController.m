//
//  DataClearViewController.m
//  PPBrowser
//
//  Created by qingbin on 2023/3/16.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import "DataClearViewController.h"

#import "Masonry.h"
#import "MaizyHeader.h"
#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "ReactiveCocoa.h"
#import "UIView+FrameHelper.h"
#import "UIViewController+Helper.h"
#import "NSObject+Helper.h"

#import "SettingSwitchView.h"
#import "SettingTextView.h"

#import "PPNotifications.h"
#import "PreferenceManager.h"
#import "BrowserUtils.h"

#import "DataClearHelper.h"
#import "DataClearModel.h"

@interface DataClearViewController ()<ThemeProtocol>

@property (nonatomic, strong) UIView* backView;

@property (nonatomic, strong) UIStackView* stackView;

@property (nonatomic, strong) SettingSwitchView *tmpView;

@property (nonatomic, strong) SettingSwitchView *inboxView;

@property (nonatomic, strong) SettingSwitchView *webItemView;

@property (nonatomic, strong) SettingSwitchView *imageView;

@property (nonatomic, strong) NSArray* datas;

@property (nonatomic, strong) NSArray* views;

@property (nonatomic, strong) UIButton* clearButton;


@property (nonatomic, strong) UILabel *totalLabel;
@property (nonatomic, strong) SettingTextView *totalView;

@end

@implementation DataClearViewController

- (void)viewDidLoad
{
    [super viewDidLoad];

    self.title = NSLocalizedString(@"dataClear.title", nil);
    
    [self commonInit];
    
    [self addSubviews];
    [self defineLayout];
    [self setupObservers];
    [self createCustomLeftBarButtonItem];
    
    [self applyTheme];
}

- (void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:animated];
    
    [self updateWithModel];
}

- (void)commonInit
{
    self.views = @[
        self.tmpView,
        self.inboxView,
        self.webItemView,
        self.imageView,
    ];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.clearButton.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        [self.clearButton setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
    } else {
        self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.clearButton.backgroundColor = UIColor.whiteColor;
        [self.clearButton setTitleColor:[UIColor colorWithHexString:@"#333333"] forState:UIControlStateNormal];
    }
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    //只有当前的controller会触发一次, 其它已存在的controller走通知
    if(needReload) {
        //修改暗黑模式
        [self applyTheme];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}

- (void)updateWithModel
{
    long long total = 0;
    for(int i=0;i<self.datas.count;i++) {
        DataClearModel* item = self.datas[i];
        SettingSwitchView* itemView = self.views[i];
        
        [itemView updateWithTitle:item.title isOn:item.isSelected];
        
        //只有选中才计算大小
        if(item.isSelected) {
            if(item.updateSizeBlock) {
                item.updateSizeBlock();
            }
            
            total += item.size;
        }
    }
    
    NSString* size = [DataClearHelper logFileSize:total];
    [self.totalView updateWithTitle:NSLocalizedString(@"dataClear.total.text", nil) content:size];
}

- (void)setupObservers
{
    @weakify(self)
    for(int i=0;i<self.datas.count;i++) {
        SettingSwitchView* itemView = self.views[i];
        DataClearModel* item = self.datas[i];
        
        [itemView setDidSwithAction:^(BOOL isOn) {
            @strongify(self)
            item.isSelected = isOn;
            [self updateWithModel];
        }];
    }
    
    [[self.clearButton rac_signalForControlEvents:UIControlEventTouchUpInside]
    subscribeNext:^(id x) {
        @strongify(self)
        [self _handleClearAction];
    }];
}

- (void)_handleClearAction
{
    [UIView showLoading:NSLocalizedString(@"tips.handling", nil)];
    
    DataClearModel* webviewItem;
    
    for(int i=0;i<self.datas.count;i++) {
        DataClearModel* item = self.datas[i];
        
        if(item.isSelected) {
            if(item.type == DataClearTypeWebView) {
                //特别处理,有回调
                webviewItem = item;
            } else {
                if(item.path.length > 0) {
                    [DataClearHelper removeFilePath:item.path];
                } else if(item.paths.count > 0) {
                    for(NSString *path in item.paths) {
                        [DataClearHelper removeFilePath:path];
                    }
                }
            }
        }
    }
    
    if(webviewItem) {
        @weakify(self)
        [DataClearHelper removeWKWebViewCache:^{
            @strongify(self)
            dispatch_async(dispatch_get_main_queue(), ^{
                [UIView showSucceed:NSLocalizedString(@"alert.clear.finish", nil)];
            
                [self updateWithModel];
            });
        }];
    } else {
        [UIView showSucceed:NSLocalizedString(@"alert.clear.finish", nil)];
        
        [self updateWithModel];
    }
}

#pragma mark -- layout
- (void)addSubviews
{
    //https://stackoverflow.com/questions/33927914/how-can-i-set-the-cornerradius-of-a-uistackview
    //UIStackView添加圆角适配
    
    [self.view addSubview:self.backView];
    [self.backView addSubview:self.stackView];
    
    [self.view addSubview:self.clearButton];
    
    [self.view addSubview:self.totalLabel];
    [self.view addSubview:self.totalView];
}

- (void)defineLayout
{
    float offset = iPadValue(30, 15);
    [self.backView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.bottom.equalTo(self.stackView);
        make.left.mas_offset(offset);
        make.right.mas_offset(-offset);
    }];
    
    offset = iPadValue(30, 15);
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(0);
        make.right.mas_offset(-0);
        make.top.equalTo(self.view).offset(offset);
    }];
    
    for(int i=0;i<self.views.count;i++) {
        SettingSwitchView* itemView = self.views[i];
        
        [itemView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo([SettingSwitchView height]);
        }];
    }
    
    float height;
    if([BrowserUtils isiPad]) {
        height = 88;
    } else {
        height = 60;
    }
    [self.clearButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.backView);
        make.top.equalTo(self.backView.mas_bottom).offset(20);
        make.height.mas_equalTo(height);
    }];
    
    offset = iPadValue(30, 15);
    [self.totalLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.clearButton.mas_bottom).offset(offset);
        make.left.equalTo(self.backView).offset(offset);
    }];

    [self.totalView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.totalLabel.mas_bottom).offset(10);
        make.centerX.equalTo(self.view);
        make.height.mas_equalTo([SettingTextView height]);
        make.left.mas_offset(offset);
        make.right.mas_offset(-offset);
    }];
}

#pragma mark -- lazy init
- (UIView *)backView
{
    if(!_backView) {
        _backView = [UIView new];
        _backView.backgroundColor = UIColor.whiteColor;
        
        _backView.layer.cornerRadius = 10;
        _backView.layer.masksToBounds = YES;
    }
    
    return _backView;
}

- (UIStackView *)stackView
{
    if(!_stackView) {
        _stackView = [[UIStackView alloc]initWithArrangedSubviews:self.views];
        
        _stackView.spacing = 0;
        _stackView.axis = UILayoutConstraintAxisVertical;
        
        _stackView.backgroundColor = UIColor.whiteColor;
    }
    
    return _stackView;
}

- (SettingSwitchView *)tmpView
{
    if(!_tmpView) {
        _tmpView = [[SettingSwitchView alloc]initWithShowLine:YES];
    }
    
    return _tmpView;
}

- (SettingSwitchView *)inboxView
{
    if(!_inboxView) {
        _inboxView = [[SettingSwitchView alloc]initWithShowLine:YES];
    }
    
    return _inboxView;
}

- (SettingSwitchView *)webItemView
{
    if(!_webItemView) {
        _webItemView = [[SettingSwitchView alloc]initWithShowLine:YES];
    }
    
    return _webItemView;
}

- (SettingSwitchView *)imageView
{
    if(!_imageView) {
        _imageView = [[SettingSwitchView alloc]initWithShowLine:NO];
    }
    
    return _imageView;
}

- (UIButton *)clearButton
{
    if(!_clearButton) {
        _clearButton = [UIButton new];
        
        [_clearButton setTitle:NSLocalizedString(@"dataClear.title", nil) forState:UIControlStateNormal];
        _clearButton.backgroundColor = UIColor.whiteColor;
        _clearButton.titleLabel.font = [UIFont systemFontOfSize:iPadValue(22, 18)];
        [_clearButton setTitleColor:[UIColor colorWithHexString:@"#333333"] forState:UIControlStateNormal];
        _clearButton.layer.cornerRadius = iPadValue(20, 10);
        _clearButton.layer.masksToBounds = YES;
    }
    
    return _clearButton;
}

- (UILabel *)totalLabel
{
    if(!_totalLabel) {
        float font = iPadValue(18, 13);
        _totalLabel = [UIView createLabelWithTitle:NSLocalizedString(@"dataClear.total.title", nil)
                                         textColor:[UIColor colorWithHexString:@"#999999"]
                                           bgColor:UIColor.clearColor
                                          fontSize:font
                                     textAlignment:NSTextAlignmentLeft
                                             bBold:NO];
    }

    return _totalLabel;
}

- (SettingTextView *)totalView
{
    if(!_totalView) {
        _totalView = [[SettingTextView alloc]initWithShowLine:NO];
        _totalView.layer.cornerRadius = 10;
        _totalView.layer.masksToBounds = YES;
    }
    
    return _totalView;
}

- (NSArray *)datas
{
    if(!_datas) {
        _datas = @[
            [DataClearModel tmp],
            [DataClearModel Inbox],
            [DataClearModel webView],
            [DataClearModel images],
        ];
    }
    
    return _datas;
}

@end
