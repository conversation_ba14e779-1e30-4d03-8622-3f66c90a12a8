//
//  DataClearModel.m
//  PPBrowser
//
//  Created by qingbin on 2023/3/16.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import "DataClearModel.h"
#import "ReactiveCocoa.h"
#import "NSFileManager+Helper.h"

@implementation DataClearModel

//tmp目录
+ (instancetype)tmp
{
    DataClearModel* item = [DataClearModel new];
    //    item.path = NSTemporaryDirectory();
    NSString *tempDirectoryPath = NSTemporaryDirectory();
    
    // 不能直接删除tmp目录，只能删除tmp目录下的子目录，否则sqlite会报错
    item.paths = [self tempURLs];
    
    item.size = [DataClearHelper fileSizeForPath:tempDirectoryPath];
    
    item.type = DataClearTypeTmp;
    item.title = NSLocalizedString(@"dataClear.tmp", nil);
    item.isSelected = YES;
    
    @weakify(item)
    [item setUpdateSizeBlock:^{
        @strongify(item)
        NSString *tempDirectoryPath = NSTemporaryDirectory();
        item.size = [DataClearHelper fileSizeForPath:tempDirectoryPath];
    }];
    
    [DataClearHelper logFileSize:item.size];
    
    return item;
}

+ (NSArray *)tempURLs
{
    NSString *tempDirectoryPath = NSTemporaryDirectory();
    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSError *error = nil;
    NSArray *tempDirectoryContents = [fileManager contentsOfDirectoryAtPath:tempDirectoryPath error:&error];
    
    if (error) {
        NSLog(@"Error fetching contents of temporary directory: %@", error);
        return nil;
    }
    
    NSMutableArray* files = [NSMutableArray array];
    for (NSString *file in tempDirectoryContents) {
        NSString *filePath = [tempDirectoryPath stringByAppendingPathComponent:file];
        [files addObject:filePath];
    }
    
    return files;
}


//Documents/Inbox目录
+ (instancetype)Inbox
{
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
    NSString *directory = [paths objectAtIndex:0];
    NSString *inbox = [directory stringByAppendingPathComponent:@"Inbox"];
    
    DataClearModel* item = [DataClearModel new];
    item.path = inbox;
    item.size = [DataClearHelper fileSizeForPath:item.path];
    
    item.type = DataClearTypeInbox;
    item.title = NSLocalizedString(@"dataClear.inbox", nil);
    item.isSelected = YES;
    
    @weakify(item)
    [item setUpdateSizeBlock:^{
        @strongify(item)
        item.size = [DataClearHelper fileSizeForPath:item.path];
    }];
    
    [DataClearHelper logFileSize:item.size];
    
    return item;
}

//WkWebView缓存
+ (instancetype)webView
{
    DataClearModel* item = [DataClearModel new];
    item.type = DataClearTypeWebView;
    item.title = NSLocalizedString(@"dataClear.webview", nil);
    item.isSelected = YES;

    [item setUpdateSizeBlock:^{
    }];
    
    return item;
}

//图片缓存
+ (instancetype)images
{
    DataClearModel* item = [DataClearModel new];
    item.path = [self zf_cachePath];
    item.size = [DataClearHelper fileSizeForPath:item.path];
    
    item.type = DataClearTypeImages;
    item.title = NSLocalizedString(@"dataClear.image", nil);
    item.isSelected = YES;
    
    @weakify(item)
    [item setUpdateSizeBlock:^{
        @strongify(item)
        item.size = [DataClearHelper fileSizeForPath:item.path];
    }];
    
    [DataClearHelper logFileSize:item.size];
    
    return item;
}

+ (NSString *)zf_cachePath
{
    NSString *cachePath = [NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES) lastObject];
    NSString *directoryPath = [NSString stringWithFormat:@"%@/%@",cachePath,@"com.hackemist.SDWebImageCache.default"];
    return directoryPath;
}


@end
