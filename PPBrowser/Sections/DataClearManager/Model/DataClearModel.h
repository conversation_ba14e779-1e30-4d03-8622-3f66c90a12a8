//
//  DataClearModel.h
//  PPBrowser
//
//  Created by qingbin on 2023/3/16.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import "BaseModel.h"
#import "PPEnums.h"
#import "DataClearHelper.h"

//WKWebView内存清理
//https://www.jianshu.com/p/325b8d200114

@interface DataClearModel : BaseModel
//文件路径
@property (nonatomic, strong) NSString *path;
//大小
@property (nonatomic, assign) NSInteger size;
//先检查path, path为空说明是一组path
@property (nonatomic, strong) NSArray* paths;
//类型
@property (nonatomic, assign) DataClearType type;
//标题
@property (nonatomic, strong) NSString* title;
//是否选中
@property (nonatomic, assign) BOOL isSelected;

@property (nonatomic, copy) void (^updateSizeBlock)(void);

//tmp目录
+ (instancetype)tmp;
//Documents/Inbox目录
+ (instancetype)Inbox;
//WkWebView缓存
+ (instancetype)webView;

//图片缓存
+ (instancetype)images;

@end

