//
//  DataClearHelper.h
//  PPBrowser
//
//  Created by qing<PERSON> on 2023/3/16.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>

@interface DataClearHelper : NSObject

//获取某个路径下所有文件大小
+ (long long)fileSizeForPath:(NSString*)path;

//删除指定的文件夹
+ (void)removeFilePath:(NSString*)filePath;

//清除WKWebView缓存
+ (void)removeWKWebViewCache:(void(^)(void))completionHandler;

// 打印文件大小
+ (NSString *)logFileSize:(long long)fileSize;

@end
