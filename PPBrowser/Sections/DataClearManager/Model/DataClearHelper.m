//
//  DataClearHelper.m
//  PPBrowser
//
//  Created by qingbin on 2023/3/16.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import "DataClearHelper.h"
#import <WebKit/WebKit.h>
#import "UIImageView+ZFCache.h"
#import "DatabaseUnit+Helper.h"

#import "ReactiveCocoa.h"
#import "SDImageCache.h"
#import "SDWebImageManager.h"
#import "UIImageView+WebCache.h"

#import "Tab.h"
#import "TabModel.h"

@implementation DataClearHelper

//获取某个路径下所有文件大小
+ (long long)fileSizeForPath:(NSString*)path
{
    long long size = 0;
    
    if (!path.length) {
        return size;
    }
    
    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSArray<NSString *>* subPaths = [fileManager contentsOfDirectoryAtPath:path error:nil];
    
    if (subPaths.count) {
        for (NSString *childPath in subPaths) {
            NSString *c_path = [path stringByAppendingPathComponent:childPath];
            if ([fileManager fileExistsAtPath:c_path]) {
                size += [self fileSizeForPath:c_path];
            }
        }
    } else {
        NSDictionary *fileAttributeDic = [fileManager attributesOfItemAtPath:path error:nil];
        size = fileAttributeDic.fileSize;
    }
        
    return size;
}

//删除指定的文件夹
+ (void)removeFilePath:(NSString*)filePath
{
    NSFileManager* fileManager = [NSFileManager defaultManager];
    
    BOOL isDir;
    if([fileManager fileExistsAtPath:filePath isDirectory:&isDir]) {
        //删除
        NSError* error;
        [fileManager removeItemAtPath:filePath error:&error];
        
        if(error) {
            NSLog(@"删除文件路径失败 : %@", filePath);
        } else {
            NSLog(@"删除文件路径成功 : %@", filePath);
        }
    }
}

//清除WKWebView缓存
+ (void)removeWKWebViewCache:(void(^)(void))completionHandler
{
    //清理类型参考
    //https://www.jianshu.com/p/325b8d200114
    
    NSArray* types = @[
        WKWebsiteDataTypeDiskCache,
        WKWebsiteDataTypeMemoryCache,
        WKWebsiteDataTypeOfflineWebApplicationCache,
        WKWebsiteDataTypeCookies,
        WKWebsiteDataTypeLocalStorage,
        WKWebsiteDataTypeWebSQLDatabases,
        WKWebsiteDataTypeIndexedDBDatabases
    ];
    NSSet *websiteDataTypes = [NSSet setWithArray:types];
    
    NSDate *dateFrom = [NSDate dateWithTimeIntervalSince1970:0];
    [[WKWebsiteDataStore defaultDataStore] removeDataOfTypes:websiteDataTypes modifiedSince:dateFrom completionHandler:^{
        if (completionHandler) {
            completionHandler();
        }
    }];
}

// 打印文件大小
+ (NSString *)logFileSize:(long long)fileSize
{
    NSByteCountFormatter *format = [NSByteCountFormatter new];
    format.countStyle = NSByteCountFormatterCountStyleFile;
    
    NSString* text = [format stringFromByteCount:fileSize];
    
    NSLog(@"file size = %@", text);
    
    return text;
}

+ (NSString *)zf_cachePath
{
    NSString *cachePath = [NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES) lastObject];
    NSString *directoryPath = [NSString stringWithFormat:@"%@/%@",cachePath,@"com.hackemist.SDWebImageCache.default"];
    return directoryPath;
}

@end
