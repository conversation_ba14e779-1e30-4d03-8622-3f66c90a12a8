//
//  WebsiteDataCell.m
//  PPBrowser
//
//  Created by qingbin on 2024/10/13.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import "WebsiteDataCell.h"

#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "UIView+FrameHelper.h"
#import "NSObject+Helper.h"
#import "ThemeProtocol.h"
#import "PreferenceManager.h"
#import "BrowserUtils.h"

@interface WebsiteDataCell ()<ThemeProtocol>

@property (nonatomic, strong) WebsiteDataModel* model;

@property (nonatomic, strong) UILabel* titleLabel;

@property (nonatomic, strong) UIStackView* stackView;

@property (nonatomic, strong) UILabel* sizeLabel;

@property (nonatomic, strong) UIImageView* selectedImageView;

@property (nonatomic, strong) UIView* line;

@end

@implementation WebsiteDataCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        self.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        [self addSubviews];
        [self defineLayout];
    }
    
    return self;
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        self.titleLabel.textColor = UIColor.whiteColor;
        self.line.backgroundColor = [UIColor colorWithHexString:kDarkThemeColorLine];
    } else {
        self.backgroundColor = UIColor.whiteColor;
        self.titleLabel.textColor = [UIColor colorWithHexString:@"#333333"];
        self.line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
}

- (void)updateWithModel:(WebsiteDataModel*)model
{
    //可以增加区分, 联想词和url的联想词使用不同的logo
    self.model = model;
    
    self.titleLabel.text = model.title;
    self.sizeLabel.text = model.size;

    [self updateCornerRadius];
    [self applyTheme];
}

- (void)updateCornerRadius
{
    // 圆角
    self.line.hidden = NO;
    
    if(self.model.isFirstInSection && self.model.isLastInSection) {
        //只有一个
        self.line.hidden = YES;
    } else {
        if(self.model.isFirstInSection) {
            //添加上圆角
        } else if(self.model.isLastInSection) {
            //添加下圆角
            self.line.hidden = YES;
        } else {
        }
    }
}

- (void)addSubviews
{
    [self.contentView addSubview:self.titleLabel];
    [self.contentView addSubview:self.stackView];
}

- (void)defineLayout
{
    float offset = iPadValue(30, 15);

    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.right.mas_offset(-offset);
        make.top.bottom.equalTo(self.contentView);
    }];
    
    [self.selectedImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(iPadValue(35, 25));
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.left.equalTo(self.contentView).offset(offset);
    }];
                
    UIView* line = [UIView new];
    line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    [self.contentView addSubview:line];
    [line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.contentView);
        make.left.equalTo(self.titleLabel);
        make.right.mas_offset(0);
        make.height.mas_equalTo(0.5);
    }];
    self.line = line;
}

- (UILabel *)titleLabel
{
    if(!_titleLabel) {
        float font = iPadValue(20, 15);
        _titleLabel = [UIView createLabelWithTitle:@""
                                         textColor:[UIColor colorWithHexString:@"#333333"]
                                           bgColor:UIColor.clearColor
                                          fontSize:font
                                     textAlignment:NSTextAlignmentLeft
                                             bBold:NO];
    }
    
    return _titleLabel;
}

- (UILabel *)sizeLabel
{
    if(!_sizeLabel) {
        float font = iPadValue(16, 12);
        _sizeLabel = [UIView createLabelWithTitle:@""
                                         textColor:[UIColor colorWithHexString:@"#666"]
                                           bgColor:UIColor.clearColor
                                          fontSize:font
                                     textAlignment:NSTextAlignmentLeft
                                             bBold:NO];
    }
    
    return _sizeLabel;
}

- (UIImageView *)selectedImageView
{
    if(!_selectedImageView) {
        _selectedImageView = [UIImageView new];
        _selectedImageView.contentMode = UIViewContentModeScaleAspectFit;
        _selectedImageView.image = [UIImage imageNamed:@"common_check_icon"];
        _selectedImageView.hidden = YES;
    }
    
    return _selectedImageView;
}

- (UIStackView *)stackView
{
    if(!_stackView) {
        _stackView = [[UIStackView alloc]initWithArrangedSubviews:@[
            self.sizeLabel,
            self.selectedImageView
        ]];
        
        _stackView.spacing = 5;
        _stackView.axis = UILayoutConstraintAxisHorizontal;
    }
    
    return _stackView;
}


@end
