//
//  WebsiteDataViewController.m
//  PPBrowser
//
//  Created by qing<PERSON> on 2024/10/13.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import "WebsiteDataViewController.h"
#import "WebsiteDataCell.h"

#import "MaizyHeader.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"

#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "NSObject+Helper.h"
#import "UIView+FrameHelper.h"

#import "PPEnums.h"
#import "ThemeProtocol.h"
#import "BrowserUtils.h"

@interface WebsiteDataViewController () <UITableViewDelegate,UITableViewDataSource,ThemeProtocol>

@property (nonatomic, strong) UITableView* tableView;

@property (nonatomic, strong) NSMutableArray* model;

@end

@implementation WebsiteDataViewController

- (void)viewDidLoad
{
    [super viewDidLoad];

    self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    
    [self createCustomLeftBarButtonItem];
    
    [self addSubviews];
    [self defineLayout];
    [self setupObservers];
    
    [self applyTheme];
    
    [self updateWithModel];
}

- (void)updateWithModel
{
    
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.tableView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
    } else {
        self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.tableView.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    //只有当前的controller会触发一次, 其它已存在的controller走通知
    if(needReload) {
        //修改暗黑模式
        [self applyTheme];
        [self.tableView reloadData];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
    [self.tableView reloadData];
}

#pragma mark -- layout
- (void)addSubviews
{
    [self.view addSubview:self.tableView];
}

- (void)defineLayout
{
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_offset(0);
        make.left.mas_offset(0);
        make.right.mas_offset(-0);
        make.bottom.mas_offset(-0);
    }];
}

- (void)setupObservers
{
}

#pragma mark - UITableViewDataSource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return self.model.count;
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    NSArray* items = self.model[indexPath.section];
    WebsiteDataModel* item = items[indexPath.row];
    WebsiteDataCell *cell =  [tableView dequeueReusableCellWithIdentifier:NSStringFromClass(WebsiteDataCell.class)];
    [cell updateWithModel:item];
    
    return cell;
}



#pragma mark - UITableViewDelegate
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    [tableView deselectRowAtIndexPath:indexPath animated:NO];
    
    
}

#pragma mark -- lazy init
- (UITableView *)tableView
{
    if(!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];

        _tableView.separatorStyle  = UITableViewCellSeparatorStyleNone;
        _tableView.showsHorizontalScrollIndicator = NO;
        _tableView.showsVerticalScrollIndicator   = NO;
        _tableView.delegate   = self;
        _tableView.dataSource = self;
        _tableView.rowHeight = iPadValue(88, 60);
        _tableView.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        
        [_tableView registerClass:[WebsiteDataCell class] forCellReuseIdentifier:NSStringFromClass([WebsiteDataCell class])];
        
        _tableView.tableFooterView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, CGFLOAT_MIN, iPadValue(20, 10))];
        
        float height = iPadValue(10, 10);
        UIView* header = [[UIView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, height)];
        header.backgroundColor = UIColor.clearColor;
        
        _tableView.tableHeaderView = header;
        _tableView.sectionFooterHeight = 0.0;
    }
    
    return _tableView;
}

- (NSMutableArray *)model
{
    if(!_model) {
        _model = [NSMutableArray array];
    }
    
    return _model;
}

@end
