//
//  URIFixup.m
//  PandaBrowser
//
//  Created by q<PERSON><PERSON> on 2022/3/8.
//

#import "URIFixup.h"

#import <arpa/inet.h>
#import <netdb.h>
#import <netinet/in.h>
#import <unistd.h>

#include <netinet/in.h>
#include <netinet/tcp.h>

#import "NSCharacterSet+Extension.h"
#import "InternalURL.h"
#import "NSURL+Extension.h"
#import "NSString+Helper.h"

#import "SearchManager.h"

#import "PPBrowser-Swift.h"

@implementation URIFixup

+ (BOOL)isValidIPAddress:(NSString*)host
{    
    struct in_addr ipv4 = {0};
    struct in6_addr ipv6 = {0};
    
    if(inet_pton(AF_INET, host.UTF8String, &ipv4) == 1 ||
       inet_pton(AF_INET6, host.UTF8String, &ipv6) == 1) {
        return YES;
    }
    return NO;
}

+ (BOOL)isValidIPAddressURL:(NSString*)string
{
    // IPv4 addresses MUST have a `.` character delimiting the octets.
    // RFC-2732 states an IPv6 URL should contain brackets as in: `[IP_ADDRESS_HERE]`
    BOOL ipv4 = [string rangeOfString:@"."].location != NSNotFound;
    BOOL ipv6 = [string rangeOfString:@"["].location != NSNotFound && [string rangeOfString:@"]"].location != NSNotFound;
    if(!(ipv4 || ipv6)) return NO;
    
    // Validate if the HOST is a valid IP address.
    NSURL* url = [NSURL URLWithString:[NSString stringWithFormat:@"https://%@",string]];
    NSString* host = url.host;
    if(host.length > 0) {
        return [self isValidIPAddress:host];
    } else {
        return NO;
    }
}

+ (NSURL*)validateURL:(NSURL*)URL
{
    // Validate the domain to make sure it doesn't have any invalid characters
    // IE: quotes, etc..
    NSString* host = URL.host;
    if(host.length > 0) {
        NSString* decodedASCIIURL = [host stringByRemovingPercentEncoding];
        if(decodedASCIIURL.length == 0) return nil;
        
        NSCharacterSet* set = [self urlAllowed];
        if([decodedASCIIURL rangeOfCharacterFromSet:set.invertedSet].length > 0) return nil;
        
        // `http://::*********` will produce an invalid URL
        // Its host, path, query, fragment, etc.. will all be empty
        // This prevents bad URLs from being passed to the DNS resolver.
        // Instead, the bad URL is forwarded to the search-engine (same behaviour as Desktop).
        NSURLComponents* component = [NSURLComponents componentsWithURL:URL resolvingAgainstBaseURL:NO];
        if(!component) return nil;
        
        if([self isValidIPAddress:host]) {
            return URL;
        }
    } else {
        return nil;
    }
    
    return URL;
}

#pragma mark -- iOS的系统判断方法

+ (NSURL *)isValidURL:(NSString *)url
{
    //适配中文域名
    //人民网.cn
    if(url.length == 0) return nil;
    
    NSDataDetector *detector;
    NSError *error = nil;
    
    detector = [NSDataDetector dataDetectorWithTypes:NSTextCheckingTypeLink error:&error];
    if (error) {
        return nil;
    }
    
    // 如果字符串不以协议开头，添加 "http://" 前缀
    if (![url hasPrefix:@"http://"] && ![url hasPrefix:@"https://"]) {
        url = [@"http://" stringByAppendingString:url];
    }
    
    NSArray *matches = [detector matchesInString:url options:0 range:NSMakeRange(0, [url length])];
    
    // 检查是否只有一个匹配项，且匹配的范围覆盖整个字符串
    if (matches.count == 1) {
        NSTextCheckingResult *result = [matches firstObject];
        if(result.range.location == 0 && result.range.length == url.length) {
            return [NSURL URLWithString:url];
        }
    }
    
    return nil;
}

#pragma mark - 判断是否是有效的域名

+ (BOOL)isValidDomainSuffix:(NSString *)input {
    //适配下面这种情况:
    //piaotia.com/html/0/74/10846.html
    
    NSURL* URL = [NSURL URLWithString:input];
    if (!URL) return false;
    
    NSString* host = [URL host];
    if (host.length == 0) return false;
    
    return [host isPublicSuffix];
    
//    if (!domain || domain.length == 0) {
//        return NO;
//    }
//    
//    // 提取域名后缀
//    NSString *suffix = [self extractDomainSuffix:domain];
//    if (!suffix) {
//        return NO;
//    }
//    
//    // 检查格式：
//    // 1. 必须包含至少一个字母
//    // 2. 可以包含连字符但不能在开始或结尾
//    // 3. 长度限制在2-63个字符之间
//    // 4. 必须以字母开头和结尾
//    NSString *suffixRegex = @"^[a-zA-Z]([a-zA-Z0-9-]*[a-zA-Z])?$";
//    NSPredicate *suffixTest = [NSPredicate predicateWithFormat:@"SELF MATCHES %@", suffixRegex];
//    
//    if (![suffixTest evaluateWithObject:suffix]) {
//        return NO;
//    }
//    
//    // 检查长度
//    return (suffix.length >= 2 && suffix.length <= 63);
}

//+ (NSString *)extractDomainSuffix:(NSString *)domain {
//    // 移除可能存在的协议部分
//    NSString *cleanDomain = domain;
//    NSArray *protocolPrefixes = @[@"http://", @"https://", @"ftp://", @"sftp://"];
//    
//    for (NSString *prefix in protocolPrefixes) {
//        if ([cleanDomain.lowercaseString hasPrefix:prefix]) {
//            cleanDomain = [cleanDomain substringFromIndex:prefix.length];
//            break;
//        }
//    }
//    
//    // 移除端口号
//    NSArray *portComponents = [cleanDomain componentsSeparatedByString:@":"];
//    if (portComponents.count > 1) {
//        cleanDomain = portComponents[0];
//    }
//    
//    // 移除路径部分
//    NSArray *pathComponents = [cleanDomain componentsSeparatedByString:@"/"];
//    if (pathComponents.count > 1) {
//        cleanDomain = pathComponents[0];
//    }
//    
//    // 分割域名部分
//    NSArray *domainComponents = [cleanDomain componentsSeparatedByString:@"."];
//    if (domainComponents.count > 0) {
//        return [domainComponents lastObject];
//    }
//    
//    return nil;
//}

+ (NSURL *)getURL:(NSString *)entry
{
    //如果是Internal的URL,那么直接返回
    NSURL* URL = [NSURL URLWithString:entry];
    if(URL && [InternalURL isValid:URL]) return URL;
    
    NSString* trimmed = [entry stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
    
    NSString* escaped = [trimmed stringByAddingPercentEncodingWithAllowedCharacters:[self urlAllowed]];
    if(escaped.length <= 0) return nil;
    
    // Then check if the URL includes a scheme. This will handle
    // all valid requests starting with "http://", "about:", etc.
    // However, we ensure that the scheme is one that is listed in
    // the official URI scheme list, so that other such search phrases
    // like "filetype:" are recognised as searches rather than URLs.
    // Use `URL(string: entry)` so it doesn't double percent escape URLs.
    URL = [NSURL URLWithString:entry];
    if(!URL) {
        URL = [NSURL URLWithString:escaped];
    }
    
    if([URL scheme].length > 0 && [URL schemeIsValid]) {
        return [self validateURL:URL];
    }
    
    // If there's no scheme, we're going to prepend "http://". First,
    // make sure there's at least one "." or ":" in the host. This means
    // we'll allow single-word searches (e.g., "foo") at the expense
    // of breaking single-word hosts without a scheme (e.g., "localhost").
    if([trimmed rangeOfString:@"."].location == NSNotFound &&
       [trimmed rangeOfString:@":"].location == NSNotFound) return nil;
    
    //处理空格
    if([trimmed rangeOfString:@" "].location != NSNotFound) return nil;
        
    // Partially canonicalize the URL and check if it has a "user"..
    // If it is, it should go to the search engine and not the DNS server..
    // This behaviour is mimicking SAFARI! It has the safest behaviour so far.
    //
    // 1. If the url contains just "<EMAIL>", ALL browsers take you to the search engine.
    // 2. If it's an email with a PATH or QUERY such as "<EMAIL>/whatever"
    //    where "/whatever" is the path or "<EMAIL>?something=whatever"
    //    where "?something=whatever" is the query:
    //    - Firefox warns you that a site is trying to log you in automatically to the domain.
    //    - Chrome takes you to the domain (seems like a security flaw).
    //    - Safari passes on the entire url to the Search Engine just like it does
    //      without a path or query.
    NSURL* trimmedUrl = [NSURL URLWithString:trimmed];
    NSURL* escapedUrl = [NSURL URLWithString:escaped];
    NSURL* httptrimmedUrl = [NSURL URLWithString:[NSString stringWithFormat:@"http://%@",trimmed]];
    NSURL* httpescapedUrl = [NSURL URLWithString:[NSString stringWithFormat:@"http://%@",escaped]];
    if(trimmedUrl.user != nil || escapedUrl.user != nil || httptrimmedUrl.user != nil || httpescapedUrl.user != nil) return nil;
    
    // If the user enters anything arithmetic
    // Such as 125.5, do not construct a URL from it
    // It is a mathematical expression
    if(trimmedUrl && trimmedUrl.scheme == nil && [trimmed isNumberValue]) {
        return nil;
    }
    
    if(httptrimmedUrl && [self isValidDomainSuffix:[httptrimmedUrl absoluteString]]) {
        //判断entry是否满足顶级域名，因此直接返回
        return httptrimmedUrl;
    }
    
    // The host is a valid IPv4 or IPv6 address
    if([self isValidIPAddressURL:trimmed]) {
        // IP Addresses do NOT require a Scheme.
        // However, Brave requires that URLs have a scheme.
        return [NSURL URLWithString:[NSString stringWithFormat:@"http://%@",escaped]];
    } else {
        // If host is NOT an IP-Address, it should never contain a colon
        // This is because it also doesn't contain a "." so it isn't a domain at all.
        // IE: foo:5000 & brave:8080 are not valid addresses.
        return nil;
    }
    
    return nil;
}

// 如果是URL，那么直接返回URL
// 如果是搜索词，那么返回当前搜索引擎下拼接而成的URL
+ (NSURL*)getCompletionURL:(NSString*)url
{
    //不管是否是url,都要判断一下
    NSURL* fixupURL = [URIFixup getURL:url];
    if(fixupURL) {
    } else {
        // We couldn't build a URL, so pass it on to the search engine.
        fixupURL = [[SearchManager shareInstance] searchURLForQuery:url];
    }
    
    return fixupURL;
}

+ (NSCharacterSet*)urlAllowed
{
    return [NSCharacterSet URLAllowed];
}

@end
