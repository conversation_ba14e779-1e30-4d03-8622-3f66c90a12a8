//
//  URIFixup.h
//  PandaBrowser
//
//  Created by qing<PERSON> on 2022/3/8.
//

#import <Foundation/Foundation.h>
#import "NSString+KKDomain.h"

//参考
/*
 https://github.com/brave/brave-ios/blob/398f8b763aa88cdc23289138863d62f05b2c2a23/Sources/Shared/URIFixup.swift#L7
 https://github.com/mozilla-mobile/firefox-ios/blob/4bd067d861943a744a6e3b14e9fc186ff14cb3aa/focus-ios/Blockzilla/Utilities/URIFixup.swift#L6
 https://github.com/mozilla-mobile/firefox-ios/blob/4bd067d861943a744a6e3b14e9fc186ff14cb3aa/firefox-ios/Client/Frontend/Browser/URIFixup.swift#L9
 
 
 //顶级域名逻辑，最终从ChatGPT得到解决方案：
 什么是域名?
 如何判断一个字符串是否是顶级域名？
 方法 4：使用域名库
 使用现有的库，如 tldextract 或 publicsuffixlist，可以解析和验证域名及顶级域名。
 
 然后从publicsuffixlist中，得到下面的资料：
 https://github.com/publicsuffix/list
 https://publicsuffix.org/
 https://publicsuffix.org/learn/
 https://publicsuffix.org/list/effective_tld_names.dat
 https://github.com/kejinlu/KKDomain
 
*/

@interface URIFixup : NSObject

+ (NSURL *)getURL:(NSString *)entry;

// 如果是URL，那么直接返回URL
// 如果是搜索词，那么返回当前搜索引擎下拼接而成的URL
+ (NSURL *)getCompletionURL:(NSString *)url;

@end

