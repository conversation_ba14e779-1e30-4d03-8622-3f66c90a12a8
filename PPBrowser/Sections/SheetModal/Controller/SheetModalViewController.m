//
//  SheetModalViewController.m
//  PPBrowser
//
//  Created by qingbin on 2023/2/3.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import "SheetModalViewController.h"

#import "MaizyHeader.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"

#import "UIColor+Helper.h"
#import "NSObject+Helper.h"
#import "UIView+Helper.h"

#import "XLScrollView.h"

@interface SheetModalViewController ()<UIScrollViewDelegate>

@property (strong, nonatomic) XLScrollView *scrollView;
// 是否主scrollView滚动
@property (assign, nonatomic) BOOL isMainScroll;
@property (assign, nonatomic) double contentOffsetMinY;
@property (assign, nonatomic) double contentOffsetMidY;

@property (nonatomic, strong) UIView *titleView;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UIButton *closeButton;

@property (nonatomic, strong) UIView *containerView;
@property (nonatomic, strong) UIView *dimmedView;

@property (nonatomic, strong) UIViewController *containerViewController;
@property (nonatomic, strong) NSString *containerTitle;

@property (nonatomic, assign) int height;

@property (nonatomic, weak) UIScrollView* associateScrollView;

@property (nonatomic, assign) SheetModalType type;

//UA
@property (nonatomic, strong) UIButton* rightButton;
@property (nonatomic, copy) void (^addAction)(void);

@end

@implementation SheetModalViewController

- (instancetype)initWithTitle:(NSString *)title
                   controller:(UIViewController *)containerViewController
{
    self = [super init];
    if(self) {
        self.height = kScreenHeight*0.75;
        self.containerTitle = title;
        self.containerViewController = containerViewController;
        self.type = SheetModalTypeDefault;
    }
    
    return self;
}

/// 指定高度
- (instancetype)initWithTitle:(NSString *)title
                   controller:(UIViewController *)containerViewController
                       height:(int)height
{
    self = [self initWithTitle:title controller:containerViewController];
    if(self) {
        self.height = height;
        self.type = SheetModalTypeDefault;
    }
    
    return self;
}

/// 指定高度+类型
- (instancetype)initWithTitle:(NSString *)title
                   controller:(UIViewController *)containerViewController
                       height:(int)height
                         type:(SheetModalType)type
                    addAction:(void(^)(void))addAction
{
    self = [self initWithTitle:title controller:containerViewController height:height];
    if(self) {
        self.type = type;
        self.addAction = addAction;
    }
    
    return self;
}

- (void)commonInit
{
    self.isMainScroll = YES;
    
    // 初始化位置
    CGPoint offset = self.scrollView.contentOffset;
    offset.y = self.contentOffsetMinY;
    self.scrollView.contentOffset = offset;
    
    self.contentOffsetMinY = 0;
    self.contentOffsetMidY = self.height;
}

- (void)preferredContentSizeDidChangeForChildContentContainer:(id<UIContentContainer>)container
{
    [super preferredContentSizeDidChangeForChildContentContainer:container];
    
    float height = MIN(container.preferredContentSize.height + 30 + 15, kScreenHeight);
    [self animateContainerHeight:height];
}

- (void)viewDidLoad
{
    [super viewDidLoad];
    
    self.view.backgroundColor = UIColor.clearColor;
    self.titleLabel.text = self.containerTitle;
    
    [self createCustomLeftBarButtonItem];
    
    [self addSubviews];
    [self defineLayout];
    
    [self commonInit];
    [self applyTheme];
}

- (void)viewDidAppear:(BOOL)animated
{
    [super viewDidAppear:animated];
    
    [self animateShowDimmedView];
    [self animatePresentContainer];
}

#pragma -- override
- (void)leftBarbuttonClick
{
    [self animateDismissView];
}

//导航栏高度
+ (float)navigationBarHeight
{
    return 15+44;
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.titleView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        self.titleLabel.textColor = [UIColor colorWithHexString:@"#ffffff"];
        [self.closeButton setImage:[UIImage imageNamed:@"sheet_close_dark_button"] forState:UIControlStateNormal];
        
        self.rightButton.imageView.tintColor = UIColor.whiteColor;
    } else {
        self.titleView.backgroundColor = UIColor.whiteColor;
        self.titleLabel.textColor = [UIColor colorWithHexString:@"#333333"];
        [self.closeButton setImage:[UIImage imageNamed:@"sheet_close_button"] forState:UIControlStateNormal];
        
        self.rightButton.imageView.tintColor = [UIColor colorWithHexString:@"#222222"];
    }
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    //只有当前的controller会触发一次, 其它已存在的controller走通知
    if(needReload) {
        //修改暗黑模式
        [self applyTheme];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}

#pragma mark -- layout
- (void)addSubviews
{
    [self.view addSubview:self.dimmedView];
    [self.view addSubview:self.scrollView];
    [self.scrollView addSubview:self.containerView];
    
    [self.containerView addSubview:self.titleView];
    [self.titleView addSubview:self.titleLabel];
    
    if(self.type == SheetModalTypeDefault) {
        [self.titleView addSubview:self.closeButton];
    } else if(self.type == SheetModalTypeUA) {
        [self.titleView addSubview:self.leftButton];
        [self.titleView addSubview:self.rightButton];
    }
    
    [self installChildViewController];
}

- (void)defineLayout
{
    [self.dimmedView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
    }];
    
    UIButton* centerBtn = self.closeButton;
    if(self.type == SheetModalTypeDefault) {
        [self.closeButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.containerView).offset(-15);
            make.top.equalTo(self.containerView).offset(15);
            make.size.mas_equalTo(30);
        }];
        
    } else if(self.type == SheetModalTypeUA) {
        [self.leftButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.containerView).offset(10);
            make.top.equalTo(self.containerView).offset(10);
            make.size.mas_equalTo(40);
        }];
        
        [self.rightButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.containerView).offset(-10);
            make.centerY.equalTo(self.leftButton);
            make.size.mas_equalTo(40);
        }];
        
        centerBtn = self.leftButton;
    }
    
    [self.titleView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.right.equalTo(self.containerView);
        make.height.mas_equalTo(44 + 15);
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.mas_offset(0);
        make.centerY.equalTo(centerBtn);
    }];
}

- (void)installChildViewController
{
    [self addChildViewController:self.containerViewController];
    [self.containerView addSubview:self.containerViewController.view];
    
    [self.containerViewController.view mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.bottom.right.equalTo(self.containerView);
        make.top.equalTo(self.titleView.mas_bottom);
    }];
    
    [self.containerViewController didMoveToParentViewController:self];
}

- (void)removeChildViewController
{
    [self.containerViewController.view removeFromSuperview];
    [self.containerViewController removeFromParentViewController];
    [self.containerViewController didMoveToParentViewController:nil];
}

- (void)animatePresentContainer
{
    @weakify(self)
    UIViewPropertyAnimator* animator = [[UIViewPropertyAnimator alloc]initWithDuration:0.25
                                                                                 curve:UIViewAnimationCurveEaseOut
                                                                            animations:^{
         @strongify(self)
//        [self.containerView mas_updateConstraints:^(MASConstraintMaker *make) {
//            make.bottom.equalTo(self.view).offset(0);
//        }];
//
//        [self.view layoutIfNeeded];
        CGPoint offset = self.scrollView.contentOffset;
        offset.y = self.contentOffsetMidY;
        self.scrollView.contentOffset = offset;
    }];
    
    [animator startAnimation];
}

- (void)animateContainerHeight:(float)height
{
    @weakify(self)
    UIViewPropertyAnimator* animator = [[UIViewPropertyAnimator alloc]initWithDuration:0.25
                                                                                 curve:UIViewAnimationCurveEaseOut
                                                                            animations:^{
         @strongify(self)
        [self.containerView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(height);
        }];
        
        [self.view layoutIfNeeded];
    }];
    
    [animator startAnimation];
}

- (void)animateShowDimmedView
{
    [UIView animateWithDuration:0.25 animations:^{
        self.dimmedView.alpha = 0.5;
    }];
}

- (void)animateDismissView
{
    // 小震动
    // 去掉，为了保持一致
//    UIImpactFeedbackGenerator* feedback = [[UIImpactFeedbackGenerator alloc]initWithStyle:UIImpactFeedbackStyleLight];
//    [feedback prepare];
//    [feedback impactOccurred];
    
    self.dimmedView.alpha = 0.5;
    
    UISpringTimingParameters* springTiming = [[UISpringTimingParameters alloc]initWithDampingRatio:0.75 initialVelocity:CGVectorMake(0, 4)];
    UIViewPropertyAnimator* dimmAnimator = [[UIViewPropertyAnimator alloc]initWithDuration:0.25 timingParameters:springTiming];
    UIViewPropertyAnimator* dismissAnimator = [[UIViewPropertyAnimator alloc]initWithDuration:0.25 curve:UIViewAnimationCurveEaseOut animations:nil];
    
    [dismissAnimator addAnimations:^{
//        [self.containerView mas_updateConstraints:^(MASConstraintMaker *make) {
//            make.bottom.equalTo(self.view).offset(kScreenHeight);
//        }];
//
//        [self.view layoutIfNeeded];
        CGPoint offset = self.scrollView.contentOffset;
        offset.y = self.contentOffsetMinY;
        self.scrollView.contentOffset = offset;
    }];
    
    [dimmAnimator addAnimations:^{
        self.dimmedView.alpha = 0;
    }];
    
    [dimmAnimator addCompletion:^(UIViewAnimatingPosition finalPosition) {
        [self dismissViewControllerAnimated:NO completion:nil];
    }];
    
    [dimmAnimator startAnimation];
    [dismissAnimator startAnimation];
}

#pragma mark -- 主控与非主控的切换
- (void)scrollDidScroll:(UIScrollView *)scrollView
{
    [self scrollViewDidScroll:scrollView];
}

#pragma mark - UIScrollViewDelegate
- (void)scrollViewDidScroll:(UIScrollView *)scrollView
{
    if(scrollView != self.scrollView) {
        self.associateScrollView = scrollView;
    }
    
    if(scrollView != self.scrollView) {
        //非主控
        if(scrollView.contentOffset.y < 0) {
            CGPoint offset = scrollView.contentOffset;
            offset.y = 0;
            scrollView.contentOffset = offset;
            
            self.isMainScroll = YES;
            
//            NSLog(@"1.0  、 %d, %s", __LINE__, __func__);
        }
        
        if(self.isMainScroll) {
            if(self.scrollView && self.scrollView.contentOffset.y < self.contentOffsetMidY) {
                CGPoint offset = scrollView.contentOffset;
                offset.y = 0;
                scrollView.contentOffset = offset;
                
//                NSLog(@"1.1  、 %d, %s", __LINE__, __func__);
            } else {
//                NSLog(@"1.2  、 %d, %s", __LINE__, __func__);
            }
        }
        
//        NSLog(@"1.3  、 %d, %s", __LINE__, __func__);
    } else {
        //主控
        if(scrollView.contentOffset.y > self.contentOffsetMidY) {
            CGPoint offset = scrollView.contentOffset;
            offset.y = self.contentOffsetMidY;
            scrollView.contentOffset = offset;

            self.isMainScroll = NO;
            
//            NSLog(@"2.0  、 %d, %s", __LINE__, __func__);
        }

        if(!self.isMainScroll) {
            if(self.associateScrollView && self.associateScrollView.contentOffset.y > 0) {
                CGPoint offset = scrollView.contentOffset;
                offset.y = self.contentOffsetMidY;
                scrollView.contentOffset = offset;
                
//                NSLog(@"2.1  、 %d, %s", __LINE__, __func__);
            }
        }
        
//        NSLog(@"2.2  、 %d, %s", __LINE__, __func__);
    }
}

- (void)scrollViewWillEndDragging:(UIScrollView *)scrollView
                     withVelocity:(CGPoint)velocity
              targetContentOffset:(inout CGPoint *)targetContentOffset
{
//    if(self.scrollView != scrollView) return;
//
//    float currentOffsetY = scrollView.contentOffset.y;
//    float gotoPointY = currentOffsetY;
//
//    if(currentOffsetY <= self.contentOffsetMidY
//       && currentOffsetY > self.contentOffsetMinY) {
//        if(velocity.y > 0) {
//            gotoPointY = self.contentOffsetMidY;
//        } else if(velocity.y < 0) {
//            gotoPointY = self.contentOffsetMinY;
//        } else {
//        }
//    } else {
//    }
//
//    if(velocity.y == 0) {
//        float distance = self.contentOffsetMidY;
//        NSArray* locations = @[@(self.contentOffsetMinY),@(self.contentOffsetMidY)];
//        for(NSNumber* item in locations) {
//            float location = item.floatValue;
//            float temp = fabs(location-currentOffsetY);
//            if(distance > temp) {
//                distance = temp;
//                gotoPointY = location;
//            }
//        }
//    } else {
//    }
//
//    [self moveToScrollView:scrollView velocity:velocity.y movePointY:gotoPointY];
//    *targetContentOffset = CGPointMake(0, gotoPointY);
    
    ///优化交互，参考抖音的评论做法
    if(self.scrollView != scrollView) return;
    
    float currentOffsetY = scrollView.contentOffset.y;
    float gotoPointY = currentOffsetY;
    
    if(velocity.y >= 0) {
        gotoPointY = self.contentOffsetMidY;
    } else if(velocity.y < 0) {
        if(velocity.y < -1.9) {
            //只有当往下拉的速度足够大，才收起
            gotoPointY = self.contentOffsetMinY;
        } else {
            gotoPointY = self.contentOffsetMidY;
        }
    }
    
    NSLog(@"speed = %f", velocity.y);
        
    [self moveToScrollView:scrollView velocity:velocity.y movePointY:gotoPointY];
    *targetContentOffset = CGPointMake(0, gotoPointY);
}

- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView
{
    if(scrollView != self.scrollView) return;
    
    float currentOffsetY = scrollView.contentOffset.y;
    NSArray* locations = @[@(self.contentOffsetMinY),@(self.contentOffsetMidY)];
    if(![locations containsObject:@(currentOffsetY)]) {
        [self moveToScrollView:scrollView velocity:8 movePointY:self.contentOffsetMidY];
    } else {
    }
}

- (void)moveToScrollView:(UIScrollView*)scrollView velocity:(float)velocity movePointY:(float)movePointY
{
    if(movePointY == self.contentOffsetMinY) {
        [self animateDismissView];
    } else {
        [UIView animateWithDuration:0.25 delay:0 usingSpringWithDamping:0.9 initialSpringVelocity:velocity options:UIViewAnimationOptionCurveEaseOut animations:^{
            CGPoint offset = scrollView.contentOffset;
            offset.y = movePointY;
            scrollView.contentOffset = offset;
        } completion:^(BOOL finished) {
        }];
    }
}

#pragma mark -- lazy init
- (UIView *)containerView
{
    if(!_containerView) {
        _containerView = [[UIView alloc]initWithFrame:CGRectMake(0, kScreenHeight, kScreenWidth, self.height)];
        _containerView.backgroundColor = UIColor.clearColor;
        _containerView.layer.cornerRadius = 10;
        _containerView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
        _containerView.layer.shadowColor = UIColor.blackColor.CGColor;
        _containerView.layer.shadowOpacity = 0.12;
        _containerView.layer.shadowRadius = 10;
        _containerView.layer.shadowOffset = CGSizeMake(0, -1);
        _containerView.layer.masksToBounds = YES;
    }
    
    return _containerView;
}

- (UIView *)dimmedView
{
    if(!_dimmedView) {
        _dimmedView = [UIView new];
        _dimmedView.backgroundColor = UIColor.blackColor;
        _dimmedView.alpha = 0;
        
        UITapGestureRecognizer* tap = [UITapGestureRecognizer new];
        [_dimmedView addGestureRecognizer:tap];
        @weakify(self)
        [tap.rac_gestureSignal subscribeNext:^(id x) {
            @strongify(self)
            [self animateDismissView];
        }];
    }
    
    return _dimmedView;
}

- (UIView *)titleView
{
    if(!_titleView) {
        _titleView = [UIView new];
        _titleView.backgroundColor = UIColor.whiteColor;
    }
    
    return _titleView;
}

- (UIButton *)closeButton
{
    if(!_closeButton) {
        _closeButton = [UIButton new];
        [_closeButton setImage:[UIImage imageNamed:@"sheet_close_button"] forState:UIControlStateNormal];
        
        @weakify(self)
        [[_closeButton rac_signalForControlEvents:UIControlEventTouchUpInside]
            subscribeNext:^(id x) {
            @strongify(self)
            [self animateDismissView];
        }];
    }
    
    return _closeButton;
}

- (UIButton *)rightButton
{
    if(!_rightButton) {
        _rightButton = [UIButton new];
        UIImage* image = [UIImage imageNamed:@"script_add_icon"];
        image = [image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        [_rightButton setImage:image forState:UIControlStateNormal];
        _rightButton.contentEdgeInsets = UIEdgeInsetsMake(10, 10, 10, 10);
        
        @weakify(self)
        [[_rightButton rac_signalForControlEvents:UIControlEventTouchUpInside]
            subscribeNext:^(id x) {
            @strongify(self)
            if(self.addAction) {
                self.addAction();
            }
        }];
    }
    
    return _rightButton;
}

- (UILabel *)titleLabel
{
    if(!_titleLabel) {
        _titleLabel = [UIView createLabelWithTitle:@""
                                         textColor:[UIColor colorWithHexString:@"#333333"]
                                           bgColor:UIColor.clearColor
                                          fontSize:18
                                     textAlignment:NSTextAlignmentCenter
                                             bBold:NO];
    }
    
    return _titleLabel;
}

- (XLScrollView *)scrollView
{
    if(!_scrollView) {
        float height = kScreenHeight;
        _scrollView = [[XLScrollView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, height)];
        _scrollView.alwaysBounceVertical = YES;
        _scrollView.showsVerticalScrollIndicator = NO;
        _scrollView.showsHorizontalScrollIndicator = NO;
        _scrollView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
        _scrollView.delegate = self;
        _scrollView.clipsToBounds = NO;
        _scrollView.backgroundColor = UIColor.clearColor;
        _scrollView.contentSize = CGSizeMake(kScreenWidth, height*2);
    }

    return _scrollView;
}

@end
