//
//  SheetModalViewController.h
//  PPBrowser
//
//  Created by qingbin on 2023/2/3.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "BaseViewController.h"
#import "PPEnums.h"

//导航栏样式，2种
//1、默认，右上角关闭按钮
//2、UA，左上角返回，右上角添加按钮

@interface SheetModalViewController : BaseViewController

- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)new NS_UNAVAILABLE;

- (instancetype)initWithTitle:(NSString *)title
                   controller:(UIViewController *)containerViewController;

/// 指定高度
- (instancetype)initWithTitle:(NSString *)title
                   controller:(UIViewController *)containerViewController
                       height:(int)height;

/// 指定高度+类型
- (instancetype)initWithTitle:(NSString *)title
                   controller:(UIViewController *)containerViewController
                       height:(int)height
                         type:(SheetModalType)type
                    addAction:(void(^)(void))addAction;

//导航栏高度
+ (float)navigationBarHeight;

- (void)scrollDidScroll:(UIScrollView *)scrollView;
/// 收起
- (void)animateDismissView;

@end

