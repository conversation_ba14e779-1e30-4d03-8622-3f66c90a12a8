//
//  ReadabilityHelper.h
//  PPBrowser
//
//  Created by qingbin on 2025/5/12.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>

#import "TabContentScript.h"
@class Tab;

/*
 网页中"下一页"的常见场景
 1. 传统链接型分页
     1.1 标准链接分页：
        - 使用 <a href="page2.html">下一页</a> 这样的标准链接
        - 通常包含明确的文本如"下一页"、"Next"、">"等
        - 点击后直接导航到新URL

     1.2 查询参数分页：
        - 使用 <a href="?page=2">下一页</a> 这样的查询参数
        - 常见于搜索结果、列表页面
        - URL模式通常为 ?page=N 或 &page=N
 
 2. JavaScript触发型分页
     2.1 点击事件分页：
        - 使用 <button onclick="loadNextPage()">加载更多</button>
        - 没有href属性，而是通过JavaScript事件处理
        - 常见文本："加载更多"、"Load More"、"Show More"

     2.2 AJAX无限滚动：
        - 当用户滚动到页面底部时自动加载更多内容
        - 没有明确的"下一页"按钮
        - 通常由滚动事件监听器触发

     2.3 SPA路由分页：
        - 单页应用中使用前端路由进行分页
        - 点击后URL会变化但不会完全重新加载页面
        - 通常使用 history.pushState 修改URL
 
 3. 特殊UI元素分页
     3.1 分页控件：
        - 使用专门的分页组件，如 <ul class="pagination">...</ul>
        - 包含当前页、上一页、下一页和页码列表
        - 通常有明确的视觉样式和位置（页面底部）

     3.2 图标按钮：
        - 使用图标而非文本表示下一页，如 <a href="page2.html"><i class="fa fa-arrow-right"></i></a>
        - 没有明确的文本内容，依赖图标或CSS类

     3.3 表格分页：
        - 数据表格特有的分页控件
        - 通常包含每页显示数量的选择器
 
 4. 内容类型特定分页
     4.1 文章/章节导航：
        - 小说、文档等长内容的章节导航
        - 通常在内容底部有"下一章"、"继续阅读"链接
        - 可能包含章节标题预览

     4.2 图片库/轮播图：
         - 图片查看器中的下一张按钮
         - 通常位于图片两侧
         - 可能使用箭头图标或手势控制

     4.3 评论加载：
         - "加载更多评论"按钮
         - 通常是AJAX加载，不刷新页面
 
 v2.7.5
 https://unpkg.com/@mozilla/readability@0.6.0/Readability.js
 
 Readability.js的引入路径，其中"0.6.0"版本号来源于https://github.com/mozilla/readability
 然后Readability.js中，删除了module.exports相关的nodejs的代码
 */

NS_ASSUME_NONNULL_BEGIN

@interface ReadabilityHelper : NSObject<TabContentScript>

- (instancetype)initWithTab:(Tab *)tab;

@end

NS_ASSUME_NONNULL_END
