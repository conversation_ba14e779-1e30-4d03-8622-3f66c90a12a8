//
//  ReaderHelper.m
//  PPBrowser
//
//  Created by qingbin on 2025/5/13.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "ReaderHelper.h"

#import "Tab.h"
#import "PPNotifications.h"
#import "BaseNavigationController.h"
#import "ReactiveCocoa.h"

#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "NSObject+Helper.h"

#import "NSURL+Extension.h"
#import "NSString+Helper.h"

#import "InternalURL.h"
#import "ReaderModel.h"
#import "ReaderManager.h"
#import "BookSettingHelper.h"

@interface ReaderHelper ()

@property (nonatomic, weak) Tab *tab;

@end

@implementation ReaderHelper

- (instancetype)initWithTab:(Tab *)tab
{
    self = [super init];
    if(self) {
        self.tab = tab;
        
        [self handleEvents];
    }
    
    return self;
}

- (void)dealloc
{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

#pragma mark - Public Method

// 进入阅读模式
- (void)enterReader:(NSNotification *)notification
{
    PandaWebView* webView = notification.object;
    if (webView != self.tab.webView) return;
    
    // 从ReaderManager获取保存的阅读模式内容
    ReaderModel *readerModel = [ReaderManager sharedInstance].currentReaderModel;
    
    if (!readerModel) return;
    
    readerModel.styleSettings = [[BookSettingHelper sharedInstance] convertToStyleSettings];
    
    // 1. 将ReaderModel转换为JSON字符串
    NSError *error;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:[readerModel toDictionary]
                                                       options:0
                                                         error:&error];
    
    if (error || !jsonData) {
        NSLog(@"Error serializing ReaderModel: %@", error);
        return;
    }
    
    // 2. 将JSON数据进行base64编码
    NSString *base64String = [jsonData base64EncodedStringWithOptions:0];
    
    // 6. 调用JS方法传递base64编码的ReaderModel
    NSString *jsCode = [NSString stringWithFormat:@"window.__firefox__.readerHelper.updateReaderModelFromBase64('%@')", base64String];
    
    [self.tab.webView evaluateJavaScript:jsCode completionHandler:^(id _Nullable result, NSError * _Nullable error) {
        if (error) {
            NSLog(@"Error setting ReaderModel: %@", error);
        }
    }];
}

#pragma mark - Private Method

- (void)handleEvents
{
    @weakify(self)
    [[[NSNotificationCenter defaultCenter] rac_addObserverForName:kEnterReaderNotification object:nil]
        subscribeNext:^(id x) {
        @strongify(self)
        [self enterReader:x];
    }];
    
//    [[[NSNotificationCenter defaultCenter] rac_addObserverForName:kExitReaderNotification object:nil]
//        subscribeNext:^(id x) {
//        @strongify(self)
//        [self exitReader:x];
//    }];
}

#pragma mark -- TabContentScript

- (NSString *)name
{
    return @"readerHelper";
}

- (NSString*)scriptMessageHandlerName
{
    return @"readerHelper";
}

- (void)userContentController:(WKUserContentController*)userContentController didReceiveScriptMessage:(WKScriptMessage*)message
{
    if(!self.tab || !self.tab.webView) return;
    
    NSDictionary* params = message.body;
    if (isNull(params)) return;
    if (!self.tab || !self.tab.webView) return;
    
    if ([params[@"action"] isEqualToString:@"updateUrl"]) {
        //更新历史url
        //2.6.4,发送通知到TabManager，并且进行saveTab操作
        [[NSNotificationCenter defaultCenter] postNotificationName:kUpdateAutoPageUrlNotification object:nil];
    }
}

@end
