//
//  ReadabilityHelper.m
//  PPBrowser
//
//  Created by qingbin on 2025/5/12.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "ReadabilityHelper.h"

#import "Tab.h"
#import "PPNotifications.h"
#import "BaseNavigationController.h"

#import "UIView+Helper.h"
#import "NSObject+Helper.h"

#import "NSURL+Extension.h"
#import "NSString+Helper.h"

#import "InternalURL.h"
#import "ReaderModel.h"

@interface ReadabilityHelper ()

@property (nonatomic, weak) Tab *tab;

@end

@implementation ReadabilityHelper

- (instancetype)initWithTab:(Tab *)tab
{
    self = [super init];
    if(self) {
        self.tab = tab;
    }
    
    return self;
}

#pragma mark -- TabContentScript

- (NSString *)name
{
    return @"readabilityHelper";
}

- (NSString*)scriptMessageHandlerName
{
    return @"readabilityHelper";
}

- (void)userContentController:(WKUserContentController*)userContentController didReceiveScriptMessage:(WKScriptMessage*)message
{
    if(!self.tab || !self.tab.webView) return;
        
    NSDictionary* params = message.body;
    if (isNull(params)) return;
    if (!self.tab || !self.tab.webView) return;

    ReaderModel* readerModel = [[ReaderModel alloc]initWithDictionary:params error:nil];
    
    // 优先裁员探测的标题
    if (readerModel.title.length == 0 && self.tab.webView.title.length > 0) {
        readerModel.title = self.tab.webView.title;
    }
    
    NSString *url = [self.tab.webView.URL absoluteString];
    if (url.length > 0) {
        readerModel.currentUrl = url;
    }
    
    [[NSNotificationCenter defaultCenter] postNotificationName:kReadabilityMessageHelperNotification object:readerModel userInfo:@{@"webView":self.tab.webView}];
}


@end
