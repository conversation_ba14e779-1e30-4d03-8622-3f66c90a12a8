//
//  ReaderModel.h
//  PPBrowser
//
//  Created by qingbin on 2025/5/12.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "BaseModel.h"

NS_ASSUME_NONNULL_BEGIN

// 参考https://github.com/mozilla/readability
@interface ReaderModel : BaseModel
// 是否符合阅读模式
@property(nonatomic,assign) BOOL isReaderModeAvailable;
// 标题
@property(nonatomic,strong) NSString *title;
// html文本
@property(nonatomic,strong) NSString *content;
// xpath组合
@property (nonatomic, strong) NSString *nextXPath;
// 下一页链接，没有链接，那么则是点击类型，而不是链接类型
@property (nonatomic, strong) NSString *nextUrl;
// pageElement的xpath
@property (nonatomic, strong) NSString *pageElementXPath;

// local字段
// 当前网页地址
@property (nonatomic, strong) NSString *currentUrl;
// 样式
@property (nonatomic, strong) NSDictionary *styleSettings;

@end

NS_ASSUME_NONNULL_END
