//
//  ReaderManager.m
//  PPBrowser
//
//  Created by qing<PERSON> on 2025/5/13.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "ReaderManager.h"
#import "InternalURL.h"
#import "UIColor+Helper.h"

@implementation ReaderManager

+ (instancetype)sharedInstance {
    static ReaderManager *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[self alloc] init];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        // 初始化
    }
    return self;
}

@end
