//
//  ReaderManager.m
//  PPBrowser
//
//  Created by qing<PERSON> on 2025/5/13.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "ReaderManager.h"
#import "InternalURL.h"
#import "UIColor+Helper.h"

@implementation ReaderManager

+ (instancetype)sharedInstance {
    static ReaderManager *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[self alloc] init];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        // 初始化
    }
    return self;
}

//- (void)saveReaderModel:(ReaderModel *)model {
//    if (!model) return;
//    
//    self.currentReaderModel = model;
//}
//
//- (void)clearCurrentReaderModel {
//    self.currentReaderModel = nil;
//}
//
//// 根据正文字体大小获取标题字体大小
//+ (NSInteger)titleFontSizeWithFontSize:(NSInteger)contentFontSize
//{
//    // 计算标题字体大小 - 比正文字体大1.39倍
//    NSInteger titleFontSize = (NSInteger)(contentFontSize * 1.39);
//    return titleFontSize;
//}
//
//// 获取本地的styleSettings
//+ (NSDictionary *)savedStyleSettings:(nullable NSNumber *)currentFontSizeValue
//{
//    // 3. 获取保存的样式设置或使用默认值
//    NSInteger savedFontSize = [[NSUserDefaults standardUserDefaults] integerForKey:@"readingFontSize"];
//    if (savedFontSize < 5 || savedFontSize > 50) {
//        savedFontSize = 18; // 默认字体大小
//    }
//    
//    // 如果有指定值，那么则用指定值
//    if (currentFontSizeValue) {
//        savedFontSize = [currentFontSizeValue intValue];
//    }
//
//    NSInteger savedBackgroundType = [[NSUserDefaults standardUserDefaults] integerForKey:@"readingBackgroundType"];
//    ReadingModeBackgroundTheme *theme = [ReadingModeBackgroundTheme themeForType:(ReadingModeThemeType)savedBackgroundType];
//    if (!theme) {
//        theme = [ReadingModeBackgroundTheme defaultTheme]; // 默认主题
//    }
//    
//    NSInteger titleFontSize = [ReaderManager titleFontSizeWithFontSize:savedFontSize];
//    
//    NSDictionary *styleSettings = @{
//        @"fontSize": [NSString stringWithFormat:@"%ldpx", (long)savedFontSize],
//        @"titleFontSize": [NSString stringWithFormat:@"%ldpx", (long)titleFontSize], // 添加标题字体大小
//        @"textColor": theme.textColorHex,
//        @"backgroundColor": theme.backgroundColorHex,
//        @"secondaryBackgroundColor": [ReaderManager lightenOrDarkenColor:theme.backgroundColorHex isDarken:theme.isDarkTheme],
//        @"accentColor": @"#4a90e2",
//        @"fontFamily": @"-apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', Arial, sans-serif",
//        @"lineHeight": @"1.75",
//        @"paragraphSpacing": @"15px"
//    };
//    
//    return styleSettings;
//}
//
//// 辅助方法：根据主背景色生成次要背景色
//+ (NSString *)lightenOrDarkenColor:(NSString *)hexColor isDarken:(BOOL)isDarken
//{
//    UIColor *color = [UIColor colorWithHexString:hexColor];
//    CGFloat r, g, b, a;
//    [color getRed:&r green:&g blue:&b alpha:&a];
//    
//    // 根据是否为暗色主题调整颜色
//    if (isDarken) {
//        // 暗色主题，使次要背景色更暗
//        r = MAX(0, r - 0.05);
//        g = MAX(0, g - 0.05);
//        b = MAX(0, b - 0.05);
//    } else {
//        // 亮色主题，使次要背景色更亮
//        r = MIN(1, r + 0.05);
//        g = MIN(1, g + 0.05);
//        b = MIN(1, b + 0.05);
//    }
//    
//    UIColor *newColor = [UIColor colorWithRed:r green:g blue:b alpha:a];
//    return [self hexStringFromColor:newColor];
//}
//
//// 辅助方法：将UIColor转换为十六进制字符串
//+ (NSString *)hexStringFromColor:(UIColor *)color
//{
//    CGFloat r, g, b, a;
//    [color getRed:&r green:&g blue:&b alpha:&a];
//    
//    // 将RGB值转换为十六进制字符串
//    return [NSString stringWithFormat:@"#%02X%02X%02X",
//            (int)(r * 255),
//            (int)(g * 255),
//            (int)(b * 255)];
//}

@end
