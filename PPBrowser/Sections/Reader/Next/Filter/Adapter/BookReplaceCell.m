//
//  BookReplaceCell.m
//  Reader
//
//  Created by q<PERSON><PERSON> on 2023/10/10.
//

#import "BookReplaceCell.h"

#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "UIView+FrameHelper.h"
#import "NSObject+Helper.h"
#import "NSString+Helper.h"

#import "ThemeProtocol.h"
#import "BrowserUtils.h"

@interface BookReplaceCell ()<ThemeProtocol>

@property (nonatomic, strong) UIView* backView;

@property (nonatomic, strong) UIStackView* stackView;
// 要被替换的文本
@property (nonatomic, strong) UILabel* replaceTextLabel;
//
@property (nonatomic, strong) UIView *line;
// 替换之后的文本
@property (nonatomic, strong) UILabel* resultTextLabel;

@property( nonatomic, strong) UISwitch* switchView;

@property (nonatomic, strong) BookReplaceModel *model;

@end

@implementation BookReplaceCell


- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        self.backgroundColor = UIColor.clearColor;

        [self addSubviews];
        [self defineLayout];
        [self setupObservers];

        [self applyTheme];
    }

    return self;
}

#pragma mark -- 暗黑模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.replaceTextLabel.textColor = [UIColor colorWithHexString:@"#ffffff"];
        self.resultTextLabel.textColor = [UIColor colorWithHexString:@"#ffffff"];
        self.backView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        self.line.backgroundColor = [UIColor colorWithHexString:kDarkThemeColorLine];
    } else {
        self.replaceTextLabel.textColor = [UIColor colorWithHexString:@"#333333"];
        self.resultTextLabel.textColor = [UIColor colorWithHexString:@"#333333"];
        self.backView.backgroundColor = UIColor.whiteColor;
        self.line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
}

- (void)updateWithModel:(BookReplaceModel *)model
{
    self.model = model;

    NSString* replaceText = model.replaceText;
    //如果开启了正则表达式，需要从Base64解码
    NSString* decodedText = [replaceText convertFromBase64];
    if (decodedText && decodedText.length > 0) {
        replaceText = decodedText;
    } else {
        // 显示友好的错误信息而不是Base64字符串
        replaceText = @"[正则表达式解码失败]";
    }

    // 限制显示文本的长度，避免界面显示过长的文本
    NSString* displayText = replaceText;
    if (displayText.length > 50) {
        displayText = [NSString stringWithFormat:@"%@...", [displayText substringToIndex:47]];
    }

    self.replaceTextLabel.text = [NSString stringWithFormat:@"替换前: %@", displayText];
    self.resultTextLabel.text = [NSString stringWithFormat:@"替换后: %@", model.resultText.length>0?model.resultText:@"无"];

    self.switchView.on = model.isActive;

    [self applyTheme];
}

- (void)setupObservers
{
    @weakify(self)
    [[self.switchView rac_newOnChannel] subscribeNext:^(id x) {
        @strongify(self)
        self.model.isActive = !self.model.isActive;
        if(self.activeAction) {
            self.activeAction(self.model);
        }
    }];
}

- (void)addSubviews
{
    [self.contentView addSubview:self.backView];
    [self.backView addSubview:self.stackView];
    [self.backView addSubview:self.switchView];
}

- (void)defineLayout
{
    [self.backView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(iPadValue(20, 15));
        make.right.mas_offset(-iPadValue(20, 15));
        make.top.bottom.equalTo(self.contentView);
    }];

    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(15);
        make.centerY.mas_offset(0);
        make.right.equalTo(self.switchView.mas_left).offset(-5);
    }];

    [self.line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(self.stackView);
        make.height.mas_equalTo(0.5);
    }];

    [self.switchView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.backView);
        make.right.mas_offset(-iPadValue(20, 10));
    }];

    if([BrowserUtils isiPad]) {
        //iPad
        self.switchView.transform = CGAffineTransformMakeScale(1.2, 1.2);
    }

    [self.switchView setContentCompressionResistancePriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    [self.switchView setContentHuggingPriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];

    [self.stackView setContentCompressionResistancePriority:UILayoutPriorityDefaultHigh forAxis:UILayoutConstraintAxisHorizontal];
    [self.stackView setContentHuggingPriority:UILayoutPriorityDefaultHigh forAxis:UILayoutConstraintAxisHorizontal];
}

- (UILabel *)resultTextLabel
{
    if(!_resultTextLabel) {
        _resultTextLabel = [UIView createLabelWithTitle:@""
                                         textColor:[UIColor colorWithHexString:@"#333333"]
                                           bgColor:UIColor.clearColor
                                          fontSize:15
                                     textAlignment:NSTextAlignmentLeft
                                             bBold:NO];
    }

    return _resultTextLabel;
}

- (UILabel *)replaceTextLabel
{
    if(!_replaceTextLabel) {
        _replaceTextLabel = [UIView createLabelWithTitle:@""
                                         textColor:[UIColor colorWithHexString:@"#333333"]
                                           bgColor:UIColor.clearColor
                                          fontSize:15
                                     textAlignment:NSTextAlignmentLeft
                                             bBold:NO];
    }

    return _replaceTextLabel;
}

- (UIStackView *)stackView
{
    if(!_stackView) {
        _stackView = [[UIStackView alloc]initWithArrangedSubviews:@[
            self.replaceTextLabel,
            self.line,
            self.resultTextLabel
        ]];

        _stackView.axis = UILayoutConstraintAxisVertical;
        _stackView.spacing = 10;
        _stackView.distribution = UIStackViewDistributionEqualCentering;

        [_stackView setCustomSpacing:0 afterView:self.replaceTextLabel];
    }

    return _stackView;
}

- (UISwitch *)switchView
{
    if(!_switchView) {
        _switchView = [[UISwitch alloc]init];
    }

    return _switchView;
}

- (UIView *)backView
{
    if(!_backView) {
        _backView = [UIView new];
        _backView.backgroundColor = UIColor.whiteColor;
        _backView.layer.cornerRadius = 10;
        _backView.layer.masksToBounds = YES;
    }

    return _backView;
}

- (UIView *)line
{
    if(!_line) {
        UIView* line = [UIView new];
        _line = line;
    }

    return _line;
}

@end
