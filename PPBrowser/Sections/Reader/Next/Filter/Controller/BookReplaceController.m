//
//  BookReplaceController.m
//  Reader
//
//  Created by q<PERSON><PERSON> on 2023/10/9.
//

#import "BookReplaceController.h"

#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "UIView+FrameHelper.h"
#import "NSObject+Helper.h"
#import "NSFileManager+Helper.h"

#import "BrowserUtils.h"
#import "UIImage+Extension.h"

#import "PaddingNewLabel.h"

#import "BookReplaceModel.h"
#import "DatabaseUnit+BookReplace.h"

#import "BookReplaceCell.h"
#import "BookAddReplaceController.h"
#import "BaseNavigationController.h"

#import "ThemeProtocol.h"
#import "NSString+Helper.h"

@interface BookReplaceController ()<UITableViewDelegate,UITableViewDataSource,ThemeProtocol>
///帮助提醒
@property (nonatomic, strong) PaddingNewLabel *helpLabel;

@property (nonatomic, strong) UIImageView* rightImageView;

@property (nonatomic, strong) UITableView* tableView;

@property (nonatomic, strong) NSMutableArray* model;
//
@property (nonatomic, weak) Tab *tab;

@end

@implementation BookReplaceController

- (instancetype)initWithTab:(Tab *)tab
{
    self = [super init];
    if (self) {
        self.tab = tab;
    }
    
    return self;
}

- (void)viewDidLoad
{
    [super viewDidLoad];

    self.title = @"文本替换";
    
    [self addSubviews];
    [self defineLayout];
    
    [self createCustomLeftBarButtonItem];
    [self _createCustomRightBarButtonItem];
    
    [self applyTheme];
    [self reloadData];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.rightImageView.tintColor = [UIColor colorWithHexString:@"#ffffff"];
        
        self.helpLabel.textColor = [UIColor colorWithHexString:@"#ffffff"];
        self.helpLabel.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
    } else {
        self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.rightImageView.tintColor = [UIColor colorWithHexString:@"#333333"];
        
        self.helpLabel.textColor = [UIColor colorWithHexString:@"#333333"];
        self.helpLabel.backgroundColor = [UIColor colorWithHexString:@"#ffffff"];
    }
    
    [self reloadData];
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    //只有当前的controller会触发一次, 其它已存在的controller走通知
    if(needReload) {
        //修改暗黑模式
        [self applyTheme];
        
        [self.tableView reloadData];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}

- (void)reloadData
{
    DatabaseUnit* unit = [DatabaseUnit queryAllBookReplace];
    @weakify(self)
    [unit setCompleteBlock:^(NSArray<BookReplaceModel *> *result, BOOL success) {
        @strongify(self)
        if(success) {            
            self.model = [result mutableCopy];
            [self.tableView reloadData];
            
            [self showOrHideHintMessage];
        }
    }];

    DB_EXEC(unit);
}

- (void)showOrHideHintMessage
{
    self.helpLabel.hidden = self.model.count>0;
}

#pragma mark -- 导航栏
- (void)_createCustomRightBarButtonItem
{
    UIButton* rightButton = [[UIButton alloc] initWithFrame:CGRectMake(0.0, 0.0f, 44.0f, 44.0)];
    [rightButton setBackgroundColor:[UIColor clearColor]];

    UIImageView* imageView = [UIImageView new];
    
    UIImage* image = [UIImage ext_systemImageNamed:@"plus"
                                         pointSize:23
                                        renderMode:UIImageRenderingModeAlwaysTemplate];
    imageView.image = image;
    
    self.rightImageView = imageView;
    
    [rightButton addSubview:imageView];
    [imageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(rightButton);
//        make.size.mas_equalTo(20);
    }];
    
    [rightButton addTarget:self action:@selector(rightBarbuttonClick:) forControlEvents:UIControlEventTouchUpInside];
    UIBarButtonItem* barItem = [[UIBarButtonItem alloc] initWithCustomView:rightButton];

    UIBarButtonItem *spacer = [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemFixedSpace target:nil action:nil];
    spacer.width = -16.0f; // for example shift right bar button to the right

    self.navigationItem.rightBarButtonItems = @[spacer, barItem];
}

- (void)rightBarbuttonClick:(id)sender
{
    //新建规则
    [self _showBookReplaceWithModel:nil];
}

- (void)_showBookReplaceWithModel:(BookReplaceModel *)model
{
    BookAddReplaceController* vc = nil;
    if(model) {
        //深拷贝
        NSString* json = [model toJSONString];
        BookReplaceModel* item = [[BookReplaceModel alloc]initWithString:json error:nil];
        
        vc = [[BookAddReplaceController alloc] initWithModel:item];
    } else {
        vc = [BookAddReplaceController new];
    }
    
    @weakify(self)
    [vc setReloadAction:^{
        @strongify(self)
        [self reloadData];
    }];
    
    BaseNavigationController* navc = [[BaseNavigationController alloc]initWithRootViewController:vc];
    //15.0的开启sheet模式
    if (@available(iOS 15.0, *)) {
        UISheetPresentationController* sheet = navc.sheetPresentationController;
        sheet.detents = @[
            UISheetPresentationControllerDetent.mediumDetent,
            UISheetPresentationControllerDetent.largeDetent,
        ];
        sheet.preferredCornerRadius = 10;
    }

    [self presentViewController:navc animated:YES completion:nil];
}

#pragma mark -- layout

- (void)addSubviews
{
    [self.view addSubview:self.tableView];
    [self.view addSubview:self.helpLabel];
}

- (void)defineLayout
{
    [self.helpLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_offset(30);
        make.left.mas_offset(20);
        make.right.mas_offset(-20);
    }];
    
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.right.equalTo(self.view);
        make.bottom.equalTo(self.view.mas_safeAreaLayoutGuideBottom);
    }];
}

#pragma mark - UITableViewDataSource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return 1;
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return self.model.count;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section
{
    if (section == 0) return 0;
    return iPadValue(30, 15);
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    BookReplaceModel* item = self.model[indexPath.section];
    BookReplaceCell *cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass(BookReplaceCell.class)];
    [cell updateWithModel:item];
    
    @weakify(self)
    [cell setActiveAction:^(BookReplaceModel * _Nonnull item) {
        @strongify(self)
        [self _handleActiveAction:item];
    }];

    return cell;
}

- (void)_handleActiveAction:(BookReplaceModel *)item
{
    DatabaseUnit* unit = [DatabaseUnit updateBookReplaceWithId:item.uuid isActive:item.isActive];
    DB_EXEC(unit);
}

- (BOOL)tableView:(UITableView *)tableView canEditRowAtIndexPath:(NSIndexPath *)indexPath
{
    return YES;
}

// 只适用于ios11 以及以后版本
- (UISwipeActionsConfiguration *)tableView:(UITableView *)tableView trailingSwipeActionsConfigurationForRowAtIndexPath:(NSIndexPath *)indexPath  API_AVAILABLE(ios(11.0))
{
    // 删除数据
    @weakify(self)
    UIContextualAction *deleteAction = [UIContextualAction contextualActionWithStyle:UIContextualActionStyleDestructive title:@"删除" handler:^(UIContextualAction * _Nonnull action, __kindof UIView * _Nonnull sourceView, void (^ _Nonnull completionHandler)(BOOL)) {
        @strongify(self)
        [tableView setEditing:NO animated:YES];//退出编辑模式，隐藏左滑菜单
    
        BookReplaceModel* item = self.model[indexPath.section];
        DatabaseUnit* unit = [DatabaseUnit removeBookReplaceWithId:item.uuid];
        DB_EXEC(unit);
        
        [self reloadData];
    }];
    [deleteAction setBackgroundColor:UIColor.redColor];

    UIContextualAction *editAction = [UIContextualAction contextualActionWithStyle:UIContextualActionStyleDestructive title:@"编辑" handler:^(UIContextualAction * _Nonnull action, __kindof UIView * _Nonnull sourceView, void (^ _Nonnull completionHandler)(BOOL)) {
        @strongify(self)
        [tableView setEditing:NO animated:YES];//退出编辑模式，隐藏左滑菜单
    
        BookReplaceModel* item = self.model[indexPath.section];
        [self _showBookReplaceWithModel:item];
    }];
    [editAction setBackgroundColor:[UIColor colorWithHexString:@"#2D7AFE"]];
    
    // 添加
    UISwipeActionsConfiguration *actions = [UISwipeActionsConfiguration configurationWithActions:@[deleteAction,editAction]];
    actions.performsFirstActionWithFullSwipe = NO;
    return actions;
}

#pragma mark -- lazy init
- (UITableView *)tableView
{
    if(!_tableView)
    {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];

        _tableView.separatorStyle  = UITableViewCellSeparatorStyleNone;
        _tableView.showsHorizontalScrollIndicator = NO;
        _tableView.showsVerticalScrollIndicator   = NO;
        _tableView.delegate   = self;
        _tableView.dataSource = self;
        _tableView.rowHeight = 80;
        _tableView.backgroundColor = UIColor.clearColor;
        
        [_tableView registerClass:[BookReplaceCell class] forCellReuseIdentifier:NSStringFromClass([BookReplaceCell class])];
                
        _tableView.tableFooterView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, 50)];
        _tableView.tableHeaderView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, iPadValue(30, 15))];
        
        _tableView.sectionFooterHeight = 0.0;
        _tableView.sectionHeaderHeight = 0.0f;
    }
    
    return _tableView;
}

- (NSMutableArray *)model
{
    if(!_model) {
        _model = [NSMutableArray array];
    }
    
    return _model;
}


#pragma mark -- Getters

- (PaddingNewLabel *)helpLabel
{
    if(!_helpLabel) {
        _helpLabel = [PaddingNewLabel new];
        _helpLabel.font = [UIFont systemFontOfSize:15];
        _helpLabel.textAlignment = NSTextAlignmentLeft;
        _helpLabel.numberOfLines = 0;
        
        _helpLabel.textColor = [UIColor colorWithHexString:@"#333333"];
        _helpLabel.backgroundColor = [UIColor colorWithHexString:@"#ffffff"];
        
        _helpLabel.edgeInsets = UIEdgeInsetsMake(20, 20, 20, 10);
        _helpLabel.layer.cornerRadius = 10;
        _helpLabel.layer.masksToBounds = YES;
        
        _helpLabel.text = @"1、替换规则对所有书籍有效\n\n2、右上角可以添加新规则\n\n3、左滑可以删除规则\n\n4、过多的规则会导致解析速度变慢，谨慎使用";
        
        _helpLabel.hidden = YES;
    }
    
    return _helpLabel;
}

@end
