//
//  BookAddReplaceController.m
//  Reader
//
//  Created by qingbin on 2023/10/10.
//

#import "BookAddReplaceController.h"

#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "UIView+FrameHelper.h"
#import "NSObject+Helper.h"
#import "NSFileManager+Helper.h"

#import "BrowserUtils.h"
#import "UIImage+Extension.h"
#import "NSString+Helper.h"

#import "PaddingLabel.h"

#import "BookReplaceModel.h"
#import "DatabaseUnit+BookReplace.h"

#import "CustomTextField.h"
#import "SettingSwitchAndTextView.h"

#import "ThemeProtocol.h"
#import "PaymentManager.h"
#import "VIPController.h"
#import "TrialManager.h"

@interface BookAddReplaceController ()<ThemeProtocol>

@property (nonatomic, strong) UIStackView* stackView;

@property (nonatomic, strong) UILabel *replaceTextLabel;
// 要被替换的文本
@property (nonatomic, strong) CustomTextField *replaceTextField;

@property (nonatomic, strong) UIView *replaceLine;

@property (nonatomic, strong) UILabel *resultTextLabel;
// 替换之后的文本
@property (nonatomic, strong) CustomTextField *resultTextField;

@property (nonatomic, strong) UIView *resultLine;
//确定
@property (nonatomic, strong) UIButton *confirmButton;

@property (nonatomic, strong) UIImageView* rightImageView;

@property (nonatomic, strong) BookReplaceModel* model;

@end

@implementation BookAddReplaceController

- (instancetype)initWithModel:(BookReplaceModel *)model
{
    self = [super init];
    if(self) {
        self.model = model;
    }

    return self;
}

- (void)viewDidLoad
{
    [super viewDidLoad];

    self.title = @"添加文本替换";

    [self _createCustomRightBarButtonItem];

    [self addSubviews];
    [self defineLayout];
    [self setupObservers];
    [self updateWithModel];

    [self applyTheme];
}

- (void)updateWithModel
{
    NSString *decodedText = [self.model.replaceText convertFromBase64];
    if (decodedText && decodedText.length > 0) {
        self.replaceTextField.text = decodedText;
    } else {
        // 如果Base64解码失败，可能是旧版本数据，直接使用原文本
        self.replaceTextField.text = self.model.replaceText;
    }
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];

    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.rightImageView.tintColor = [UIColor colorWithHexString:@"#ffffff"];
        self.replaceTextLabel.textColor = [UIColor colorWithHexString:@"#ffffff"];
        self.resultTextLabel.textColor = [UIColor colorWithHexString:@"#ffffff"];
        self.replaceLine.backgroundColor = [UIColor colorWithHexString:kDarkThemeColorLine];
        self.resultLine.backgroundColor = [UIColor colorWithHexString:kDarkThemeColorLine];
    } else {
        self.view.backgroundColor = UIColor.whiteColor;
        self.rightImageView.tintColor = [UIColor colorWithHexString:@"#333333"];
        self.replaceTextLabel.textColor = [UIColor colorWithHexString:@"#333333"];
        self.resultTextLabel.textColor = [UIColor colorWithHexString:@"#333333"];
        self.replaceLine.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.resultLine.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    //只有当前的controller会触发一次, 其它已存在的controller走通知
    if(needReload) {
        //修改暗黑模式
        [self applyTheme];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}


- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event
{
    [self.view endEditing:YES];
}

#pragma mark -- 右键取消按钮
- (void)_createCustomRightBarButtonItem
{
    UIButton* menuButton = [self _createRightButton];

    UIBarButtonItem* menuBarButtonItem = [[UIBarButtonItem alloc] initWithCustomView:menuButton];

    UIBarButtonItem *spacer = [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemFixedSpace target:nil action:nil];
    spacer.width = -16.0f; // for example shift right bar button to the right

    self.navigationItem.rightBarButtonItems = @[spacer, menuBarButtonItem];
}

- (UIButton *)_createRightButton
{
    UIButton* rightButton = [[UIButton alloc] initWithFrame:CGRectMake(0.0, 0.0f, 44.0f, 44.0)];
    [rightButton setBackgroundColor:[UIColor clearColor]];

    UIImageView* imageView = [UIImageView new];

    //checkmark.circle
    UIImage* image = [[UIImage imageNamed:@"browser_close2_icon"] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
    imageView.image = image;
    imageView.tintColor = [UIColor colorWithHexString:@"333333"];
    self.rightImageView = imageView;

    [rightButton addSubview:imageView];
    [imageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(rightButton);
        make.size.mas_equalTo(18);
    }];

    @weakify(self)
    [[rightButton rac_signalForControlEvents:UIControlEventTouchUpInside]
    subscribeNext:^(id x) {
        @strongify(self)
        [self dismissViewControllerAnimated:YES completion:nil];
    }];

    return rightButton;
}

#pragma mark -- handle events

- (void)setupObservers
{
    @weakify(self)
    [[self.confirmButton rac_signalForControlEvents:UIControlEventTouchUpInside]
    subscribeNext:^(id x) {
        @strongify(self)
        [self.view endEditing:YES];
        [self _handleConfirm];
    }];
}

- (void)_handleConfirm
{
    //会员检测
    if(![self _checkIsVip]) return;

    if(self.replaceTextField.text.length == 0) {
        [UIView showToast:@"请输入要被替换的文本"];
        return;
    }

    // 获取用户输入的原始文本，避免系统自动转义
    NSString* replaceText = [self _getRawTextFromTextField:self.replaceTextField];
    self.model.replaceText = [replaceText convertToBase64];
    
    self.model.resultText = self.resultTextField.text ?: @"";
    self.model.isActive = YES;

    DatabaseUnit* unit = [DatabaseUnit addBookReplaceWithItem:self.model];
    DB_EXEC(unit);

    if(self.reloadAction) {
        self.reloadAction();
    }

    [self dismissViewControllerAnimated:YES completion:nil];
}

/**
 * 获取TextField中用户输入的原始文本，避免系统自动转义
 * 主要解决反斜杠等特殊字符被自动转义的问题
 */
- (NSString *)_getRawTextFromTextField:(UITextField *)textField
{
    if (!textField || !textField.text) {
        return @"";
    }

    // 获取文本框中的原始文本
    NSString *rawText = textField.text;

    // 对于正则表达式，我们需要确保获取到用户真正输入的内容
    // UITextField在某些情况下会自动转义特殊字符，我们需要还原
    // 检查是否存在被系统自动转义的情况
    // 例如：用户输入 \ 但系统显示为 \\
    // 这里我们通过检查文本内容来判断是否需要还原

    // 使用UITextInput协议来获取更准确的文本内容
    UITextRange *allTextRange = [textField textRangeFromPosition:textField.beginningOfDocument
                                                      toPosition:textField.endOfDocument];
    if (allTextRange) {
        NSString *actualText = [textField textInRange:allTextRange];
        if (actualText) {
            rawText = actualText;
        }
    }

    return rawText;
}

- (BOOL)_checkIsVip
{
    BOOL isVip = [[PaymentManager shareInstance] isVip];
    BOOL canUseFeature = isVip;
    if(!canUseFeature) {
        canUseFeature = [[TrialManager sharedManager] canUseFeature:FeatureTypeTextFilter];
    }
    
    if(!canUseFeature) {
        UIAlertController* alertController = [UIAlertController alertControllerWithTitle:@"需要开通Focus高级版" message:@"非会员每天最多只有1次试用次数哦，升级Focus高级版可以解锁所有功能，感谢您的支持!" preferredStyle:UIAlertControllerStyleAlert];
        UIAlertAction* action = [UIAlertAction actionWithTitle:@"好的" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            [alertController dismissViewControllerAnimated:YES completion:nil];
        }];
        [alertController addAction:action];

        action = [UIAlertAction actionWithTitle:@"了解更多" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            [VIPController jumpToVipWithController:self completion:nil];
        }];
        [alertController addAction:action];

        [self presentViewController:alertController animated:YES completion:nil];
    }

    return canUseFeature;
}

#pragma mark -- layout

- (void)addSubviews
{
    [self.view addSubview:self.stackView];
    [self.view addSubview:self.confirmButton];
}

- (void)defineLayout
{
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_offset(35);
        make.left.mas_offset(15);
        make.right.mas_offset(-15);
    }];

    [self.replaceTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(self.stackView);
        make.height.mas_equalTo(44);
    }];

    [self.resultTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(self.stackView);
        make.height.mas_equalTo(44);
    }];

    [self.resultLine mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(self.stackView);
        make.height.mas_equalTo(0.5);
    }];

    [self.replaceLine mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(self.stackView);
        make.height.mas_equalTo(0.5);
    }];

    [self.confirmButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.mas_offset(0);
        make.left.mas_offset(15);
        make.right.mas_offset(-15);
        make.height.mas_equalTo(44);
        make.top.equalTo(self.resultLine.mas_bottom).offset(30);
    }];
}

#pragma mark -- Getters

- (CustomTextField *)replaceTextField
{
    if(!_replaceTextField) {
        _replaceTextField = [CustomTextField new];
        [_replaceTextField updatePlaceHolder:@"请输入原文本" color:[UIColor colorWithHexString:@"#999999"]];
        [_replaceTextField setClearButtonMode:UITextFieldViewModeAlways];

        _replaceTextField.font = [UIFont systemFontOfSize:13];
    }

    return _replaceTextField;
}

- (UILabel *)replaceTextLabel
{
    if(!_replaceTextLabel) {
        _replaceTextLabel = [UIView createLabelWithTitle:@"1）要被替换的文本"
                                         textColor:[UIColor colorWithHexString:@"#333333"]
                                           bgColor:UIColor.clearColor
                                          fontSize:15
                                     textAlignment:NSTextAlignmentLeft
                                             bBold:NO];
    }

    return _replaceTextLabel;
}

- (UIView *)replaceLine
{
    if(!_replaceLine) {
        UIView* line = [UIView new];
        line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        _replaceLine = line;
    }

    return _replaceLine;
}

- (CustomTextField *)resultTextField
{
    if(!_resultTextField) {
        _resultTextField = [CustomTextField new];
        [_resultTextField updatePlaceHolder:@"请输入自定义文本(选填)" color:[UIColor colorWithHexString:@"#999999"]];
        [_resultTextField setClearButtonMode:UITextFieldViewModeAlways];

        _resultTextField.font = [UIFont systemFontOfSize:13];
    }

    return _resultTextField;
}

- (UILabel *)resultTextLabel
{
    if(!_resultTextLabel) {
        _resultTextLabel = [UIView createLabelWithTitle:@"2）替换之后的文本"
                                         textColor:[UIColor colorWithHexString:@"#333333"]
                                           bgColor:UIColor.clearColor
                                          fontSize:15
                                     textAlignment:NSTextAlignmentLeft
                                             bBold:NO];
    }

    return _resultTextLabel;
}

- (UIView *)resultLine
{
    if(!_resultLine) {
        UIView* line = [UIView new];
        line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        _resultLine = line;
    }

    return _resultLine;
}

- (UIStackView *)stackView
{
    if(!_stackView) {
        _stackView = [[UIStackView alloc]initWithArrangedSubviews:@[
            self.replaceTextLabel,
            self.replaceTextField,
            self.replaceLine,
            self.resultTextLabel,
            self.resultTextField,
            self.resultLine
        ]];

        _stackView.axis = UILayoutConstraintAxisVertical;
        _stackView.spacing = 10;

        [_stackView setCustomSpacing:0 afterView:self.replaceTextField];
        [_stackView setCustomSpacing:0 afterView:self.resultTextField];
    }

    return _stackView;
}

- (UIButton *)confirmButton
{
    if(!_confirmButton) {
        _confirmButton = [UIButton new];
        [_confirmButton setTitle:@"确认" forState:UIControlStateNormal];
        [_confirmButton setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
        _confirmButton.titleLabel.font = [UIFont systemFontOfSize:iPadValue(18, 15) weight:UIFontWeightBold];

        [_confirmButton setBackgroundColor:[UIColor colorWithHexString:@"#2D7AFE"]];
        _confirmButton.layer.cornerRadius = 10;
        _confirmButton.layer.masksToBounds = YES;
    }

    return _confirmButton;
}

- (BookReplaceModel *)model
{
    if(!_model) {
        _model = [BookReplaceModel new];
    }

    return _model;
}

@end
