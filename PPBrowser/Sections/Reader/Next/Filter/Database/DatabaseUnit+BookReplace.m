//
//  DatabaseUnit+BookReplace.m
//  Reader
//
//  Created by q<PERSON><PERSON> on 2023/10/10.
//

#import "DatabaseUnit+BookReplace.h"

#import "ReactiveCocoa.h"

#import "FMDatabaseQueue.h"
#import "FMDatabase.h"

#import "PPEnums.h"

#import "BookReplaceManager.h"
#import "NSString+Helper.h"

@implementation DatabaseUnit (BookReplace)

+ (DatabaseUnit *)addBookReplaceWithItem:(BookReplaceModel *)item
{
    //https://stackoverflow.com/questions/3634984/insert-if-not-exists-else-update
    DatabaseUnit* unit = [DatabaseUnit new];

    // 数据验证
    if (!item) {
        NSLog(@"[BookReplace] Error: item is nil");
        return unit;
    }

    //必须有id
    if(item.uuid.length == 0) {
        item.uuid = [[NSUUID UUID] UUIDString];
    }

    if(item.ctime.length == 0) {
        item.ctime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
    }

    // 验证replaceText不为空
    if (!item.replaceText || item.replaceText.length == 0) {
        NSLog(@"[BookReplace] Error: replaceText is empty");
        return unit;
    }

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)

        //删除旧规则
        NSString* command = @"DELETE FROM t_bookReplace WHERE uuid=?;";
        BOOL result = [db executeUpdate:command, item.uuid];

        if (!result) {
            NSLog(@"[BookReplace] Failed to delete old rule with uuid: %@", item.uuid);
        }

        //插入新规则
        command = @"INSERT INTO t_bookReplace(uuid, replaceText, resultText, isActive, ctime) VALUES (?,?,?,?,?)";
        result = [db executeUpdate:command, item.uuid, item.replaceText?:@"", item.resultText?:@"", @(item.isActive), item.ctime];

        if (!result) {
            NSLog(@"[BookReplace] Failed to insert new rule: %@", [db lastErrorMessage]);
        }

        {
            NSString* command = [NSString stringWithFormat:@"SELECT * FROM t_bookReplace ORDER BY ctime ASC"];
            FMResultSet* set = [db executeQuery:command];

            NSMutableArray *array = [[NSMutableArray alloc] init];
            while([set next]) {
                BookReplaceModel* item = [[BookReplaceModel alloc]initWithDictionary:[set resultDictionary] error:nil];
                if (item) {
                    [array addObject:item];
                }
            }

            [[BookReplaceManager shareInstance] updateWithModel:array];
        }

        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };

    return unit;
}

+ (DatabaseUnit *)removeBookReplaceWithId:(NSString *)uuid
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = @"DELETE FROM t_bookReplace WHERE uuid=?;";
        BOOL result = [db executeUpdate:command, uuid];

        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }

            [[BookReplaceManager shareInstance] reloadData];
        });
    };

    return unit;
}

//根据一组uuid删除
+ (DatabaseUnit *)removeBookReplacesWithIds:(NSArray<NSString *> *)uuids
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)

        NSMutableString* ids = [NSMutableString new];
        for(int i=0;i<uuids.count;i++) {
            NSString* str = uuids[i];
            NSString* option = [NSString stringWithFormat:@"\"%@\"",str];
            [ids appendString:option];
            if(i < uuids.count-1) {
                [ids appendString:@","];
            }
        }

        NSString* command = [NSString stringWithFormat:@"DELETE FROM t_bookReplace WHERE uuid in (%@);",ids];
        BOOL result = [db executeUpdate:command];

        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }

            [[BookReplaceManager shareInstance] reloadData];
        });
    };

    return unit;
}

+ (DatabaseUnit *)removeAllBookReplace
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = @"DELETE FROM t_bookReplace;";

        BOOL result = [db executeUpdate:command];

        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }

            [[BookReplaceManager shareInstance] reloadData];
        });
    };

    return unit;
}

+ (DatabaseUnit *)queryAllBookReplace
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = [NSString stringWithFormat:@"SELECT * FROM t_bookReplace ORDER BY ctime ASC"];
        FMResultSet* set = [db executeQuery:command];

        NSMutableArray *array = [[NSMutableArray alloc] init];
        while([set next]) {
            BookReplaceModel* item = [[BookReplaceModel alloc]initWithDictionary:[set resultDictionary] error:nil];
            //从数据库中读取出来的正则表达式规则，是已经base64解码的
            item.replaceText = [item.replaceText convertFromBase64];
            item.resultText = [item.resultText convertFromBase64];
            
            [array addObject:item];
        }

        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(array,YES);
            }
        });
    };

    return unit;
}

// 更新是否激活状态
+ (DatabaseUnit*)updateBookReplaceWithId:(NSString*)uuid
                          isActive:(NSInteger)isActive
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)

        NSString* command = [NSString stringWithFormat:@"UPDATE t_bookReplace SET isActive = ? WHERE uuid=?;"];
        BOOL result = [db executeUpdate:command, @(isActive), uuid];

        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil,result);
            }

            [[BookReplaceManager shareInstance] reloadData];
        });
    };

    return unit;
}

// 更新是否开启正则表达式
+ (DatabaseUnit*)updateBookReplaceWithId:(NSString*)uuid
                          isRegularMatch:(NSInteger)isRegularMatch
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)

        NSString* command = [NSString stringWithFormat:@"UPDATE t_bookReplace SET isRegularMatch = ? WHERE uuid=?;"];
        BOOL result = [db executeUpdate:command, @(isRegularMatch), uuid];

        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil,result);
            }

            [[BookReplaceManager shareInstance] reloadData];
        });
    };

    return unit;
}

@end
