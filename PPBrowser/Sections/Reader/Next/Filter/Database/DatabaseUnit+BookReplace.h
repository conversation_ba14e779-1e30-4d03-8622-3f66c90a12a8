//
//  DatabaseUnit+BookReplace.h
//  Reader
//
//  Created by q<PERSON><PERSON> on 2023/10/10.
//

#import "DatabaseUnit.h"

#import "BookReplaceModel.h"

@interface DatabaseUnit (BookReplace)

+ (DatabaseUnit *)addBookReplaceWithItem:(BookReplaceModel *)item;

+ (DatabaseUnit *)queryAllBookReplace;

+ (DatabaseUnit *)removeBookReplaceWithId:(NSString *)uuid;

+ (DatabaseUnit *)removeBookReplacesWithIds:(NSArray<NSString *> *)ids;

+ (DatabaseUnit *)removeAllBookReplace;

// 更新是否激活状态
+ (DatabaseUnit*)updateBookReplaceWithId:(NSString*)uuid
                                isActive:(NSInteger)isActive;

// 更新是否开启正则表达式
+ (DatabaseUnit*)updateBookReplaceWithId:(NSString*)uuid
                          isRegularMatch:(NSInteger)isRegularMatch;

@end

