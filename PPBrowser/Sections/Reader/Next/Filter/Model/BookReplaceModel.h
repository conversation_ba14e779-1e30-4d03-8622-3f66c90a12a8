//
//  BookReplaceModel.h
//  Reader
//
//  Created by q<PERSON><PERSON> on 2023/10/10.
//

#import "BaseModel.h"

@interface BookReplaceModel : BaseModel
// id
@property (nonatomic, strong) NSString* uuid;
// 要被替换的文本
@property (nonatomic, strong) NSString* replaceText;
// 替换之后的文本
@property (nonatomic, strong) NSString* resultText;
// 是否正在运行
@property (nonatomic, assign) BOOL isActive;
// 创建时间
@property (nonatomic, strong) NSString* ctime;

//辅助字段
@property (nonatomic, assign) BOOL isFirstInSection;
@property (nonatomic, assign) BOOL isLastInSection;

@end

