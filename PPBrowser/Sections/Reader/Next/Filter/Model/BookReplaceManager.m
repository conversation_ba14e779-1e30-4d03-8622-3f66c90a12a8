//
//  BookReplaceManager.m
//  Reader
//
//  Created by q<PERSON><PERSON> on 2023/10/10.
//

#import "BookReplaceManager.h"

#import "DatabaseUnit+BookReplace.h"
#import "ReactiveCocoa.h"
#import "BookReplaceModel.h"

@interface BookReplaceManager ()

@property (nonatomic, strong) NSArray *bookReplaceArray;

@end

@implementation BookReplaceManager

+ (instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    static BookReplaceManager* obj;
    dispatch_once(&onceToken, ^{
        obj = [BookReplaceManager new];
    });

    return obj;
}

#pragma mark -- 获取已开启的配置
- (NSArray *)getActiveBookReplaces
{
    NSMutableArray* array = [NSMutableArray array];
    for(BookReplaceModel* item in self.bookReplaceArray) {
        if(item.isActive) {
            [array addObject:item];
        }
    }

    return array;
}

#pragma mark -- 重新加载配置
- (void)reloadData
{
    DatabaseUnit* unit = [DatabaseUnit queryAllBookReplace];
    @weakify(self)
    [unit setCompleteBlock:^(NSArray<BookReplaceModel *> *result, BOOL success) {
        @strongify(self)
        if(success) {            
            self.bookReplaceArray = result;
        }
    }];

    DB_EXEC(unit);
}


@end
