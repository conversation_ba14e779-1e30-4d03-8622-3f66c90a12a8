//
//  BookConfigModel.m
//  PPBrowser
//
//  Created by qingbin on 2025/6/3.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "BookConfigModel.h"

@implementation BookConfigModel

- (instancetype)init
{
    self = [super init];
    if (self) {
        // 设置默认值
        self.fontSize = 20.0;
        self.fontFamily = @"-apple-system";
        self.titleFontSize = 25;
        self.lineHeight = 1.8;
//        self.paragraphSpacing = 15.0;
        self.firstLineHeadIndent = 2.0;
        self.horizontalMargin = 20.0;
        self.topMargin = 30.0;
        self.bottomMargin = 20.0;
        self.justifiedAlignment = NO;
        self.theme = ReadingModeThemeTypeWhite; // 默认白色主题
        self.tapPageEnabled = YES;
//        self.selectedLanguage = LanguageTypeAuto;
    }
    
    return self;
}

@end
