//
//  BookSettingModel.h
//  Reader
//
//  Created by q<PERSON><PERSON> on 2023/8/9.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import "PPEnums.h"

typedef NS_ENUM(NSInteger, BookSettingType) {
    BookSettingTypeStepper = 0,
    BookSettingTypeSwitch,
    BookSettingTypeSegment,
    BookSettingTypeArrow,
};

NS_ASSUME_NONNULL_BEGIN

@interface BookSettingModel : NSObject

//类型
@property (nonatomic, assign) BookSettingType type;

@property (nonatomic, strong) NSString *title;
//switch相关
@property (nonatomic, assign) BOOL isOn;
//stepper相关
//最大值
@property (nonatomic, assign) float maxValue;
//最小值
@property (nonatomic, assign) float minValue;
//如果是step时，设置它的步长，默认是1
@property (nonatomic, assign) float stepValue;
//当前值
@property (nonatomic, assign) float value;
//调用的方法
@property (nonatomic, copy) void (^valueChangedAction)(float value);
//调用的方法
@property (nonatomic, copy) void (^newOnChannelAction)(BOOL isOn);
//调用的方法
@property (nonatomic, copy) void (^selectIndexAction)(int index);
//调用的方法
@property (nonatomic, copy) void (^tapAction)(void);
//菜单栏
@property (nonatomic, strong) UIMenu* menu;

//辅助字段,主要是卡片化
@property (nonatomic, assign) BOOL isFirstInSection;
@property (nonatomic, assign) BOOL isLastInSection;

@end

NS_ASSUME_NONNULL_END
