//
//  BookConfigModel.h
//  PPBrowser
//
//  Created by qing<PERSON> on 2025/6/3.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "BaseModel.h"
#import "PPEnums.h"

//简繁体切换
typedef NS_ENUM(NSInteger, LanguageType) {
    LanguageTypeAuto,
    LanguageTypeSimplifiedChinese,
    LanguageTypeTraditionalChinese
};

NS_ASSUME_NONNULL_BEGIN

@interface BookConfigModel : BaseModel
//字体大小
@property (nonatomic, assign) CGFloat fontSize;
//字体
@property (nonatomic, strong) NSString *fontFamily;
//标题大小
@property (nonatomic, assign) CGFloat titleFontSize;
//行高(倍数)
@property (nonatomic, assign) CGFloat lineHeight;
//段落间距(做不到识别html中的段落)
//@property (nonatomic, assign) CGFloat paragraphSpacing;
//首行缩进 0-0, 1-1个字的宽度, 2-2个字的宽度
@property (nonatomic, assign) CGFloat firstLineHeadIndent;
//左右间距
@property (nonatomic, assign) CGFloat horizontalMargin;
//上边距
@property (nonatomic, assign) CGFloat topMargin;
//下边距
@property (nonatomic, assign) CGFloat bottomMargin;
//两端对齐
@property (nonatomic, assign) BOOL justifiedAlignment;
//主题
@property (nonatomic, assign) ReadingModeThemeType theme;
//轻点翻页
@property (nonatomic, assign) BOOL tapPageEnabled;
//简繁体切换, 后面再考虑
//@property (nonatomic, assign) LanguageType selectedLanguage;

@end

NS_ASSUME_NONNULL_END
