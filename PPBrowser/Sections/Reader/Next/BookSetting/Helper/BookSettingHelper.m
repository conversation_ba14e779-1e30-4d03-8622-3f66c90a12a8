//
//  BookSettingHelper.m
//  PPBrowser
//
//  Created by qingbin on 2025/6/3.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "BookSettingHelper.h"
#import "PPEnums.h"
#import "ReadingThemeModel.h"

// UserDefaults Key
static NSString * const kBookConfigKey = @"book_config_data";

@interface BookSettingHelper ()

@property (nonatomic, strong) NSUserDefaults *userDefaults;

@end

@implementation BookSettingHelper

+ (instancetype)sharedInstance {
    static BookSettingHelper *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[self alloc] init];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        // 使用自定义的UserDefaults suite
        self.userDefaults = [[NSUserDefaults alloc] initWithSuiteName:@"com.focus.bookconfig"];
        self.config = [self getCurrentConfig];
    }
    return self;
}

- (BookConfigModel *)getCurrentConfig {
    BookConfigModel *config = [BookConfigModel new];
    
    // 从UserDefaults读取配置数据
    NSDictionary *configDict = [self.userDefaults objectForKey:kBookConfigKey];

    if (configDict && [configDict isKindOfClass:[NSDictionary class]]) {
        // 如果存在配置，从字典创建模型
        config = [[BookConfigModel alloc] initWithDictionary:configDict error:nil];
    }
    
    // 如果不存在配置，返回默认配置
    return config;
}

- (void)saveConfig{
    if (!self.config) {
        return;
    }

    // 转换为字典并保存到UserDefaults
    NSDictionary *configDict = [self.config toDictionary];
    [self.userDefaults setObject:configDict forKey:kBookConfigKey];
    [self.userDefaults synchronize];
}

- (NSDictionary *)convertToStyleSettings {
    if (!self.config) {
        return @{};
    }

    BookConfigModel *config = self.config;

    // 获取主题信息
    ReadingThemeModel *themeModel = [ReadingThemeModel modelFromTheme:config.theme];
    // 生成次要背景色
    NSString *secondaryBackgroundColor = [self lightenOrDarkenColor:themeModel.backgroundColor isDarken:themeModel.theme == ReadingModeThemeTypeDark];

    // 构建样式设置字典
    NSDictionary *styleSettings = @{
        // 字体相关
        @"fontSize": [NSString stringWithFormat:@"%.0fpx", config.fontSize],
        @"titleFontSize": [NSString stringWithFormat:@"%.0fpx", config.titleFontSize],
        @"fontFamily": config.fontFamily ?: @"-apple-system",

        // 布局相关
        @"lineHeight": [NSString stringWithFormat:@"%.2f", config.lineHeight],
//        @"paragraphSpacing": [NSString stringWithFormat:@"%.0fpx", config.paragraphSpacing],
        @"firstLineHeadIndent": [NSString stringWithFormat:@"%.1fem", config.firstLineHeadIndent],
        @"horizontalMargin": [NSString stringWithFormat:@"%.0fpx", config.horizontalMargin],
        @"topMargin": [NSString stringWithFormat:@"%.0fpx", config.topMargin],
        @"bottomMargin": [NSString stringWithFormat:@"%.0fpx", config.bottomMargin],

        // 对齐方式
        @"justifiedAlignment": config.justifiedAlignment ? @"justify" : @"left",

        // 主题颜色
        @"textColor": themeModel.textColor,
        @"backgroundColor": themeModel.backgroundColor,
        @"secondaryBackgroundColor": secondaryBackgroundColor,
        @"accentColor": @"#4a90e2",
        
        // 轻点翻页
        @"tapPageEnabled": @(config.tapPageEnabled),
    };

    return styleSettings;
}

// 辅助方法：根据主背景色生成次要背景色
- (NSString *)lightenOrDarkenColor:(NSString *)hexColor isDarken:(BOOL)isDarken {
    // 移除 # 符号
    NSString *cleanHex = [hexColor stringByReplacingOccurrencesOfString:@"#" withString:@""];

    // 解析RGB值
    unsigned int rgb = 0;
    NSScanner *scanner = [NSScanner scannerWithString:cleanHex];
    [scanner scanHexInt:&rgb];

    CGFloat r = ((rgb >> 16) & 0xFF) / 255.0;
    CGFloat g = ((rgb >> 8) & 0xFF) / 255.0;
    CGFloat b = (rgb & 0xFF) / 255.0;

    // 根据是否为暗色主题调整颜色
    if (isDarken) {
        // 暗色主题，使次要背景色更暗
        r = MAX(0, r - 0.05);
        g = MAX(0, g - 0.05);
        b = MAX(0, b - 0.05);
    } else {
        // 亮色主题，使次要背景色更亮
        r = MIN(1, r + 0.05);
        g = MIN(1, g + 0.05);
        b = MIN(1, b + 0.05);
    }

    // 转换回十六进制字符串
    return [NSString stringWithFormat:@"#%02X%02X%02X",
            (int)(r * 255),
            (int)(g * 255),
            (int)(b * 255)];
}

#pragma mark - Public Method

// 将当前样式设置应用到阅读模式
+ (void)applyStyleSettingsToReader:(Tab *)tab
{
    if (!tab) return;
    // 创建样式设置字典
    NSDictionary *styleSettings = [[BookSettingHelper sharedInstance] convertToStyleSettings];
    
    // 转换为JSON数据
    NSError *error;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:styleSettings
                                                       options:0
                                                         error:&error];
    
    if (error || !jsonData) {
        return;
    }
    
    // 转换为base64编码的字符串
    NSString *base64String = [jsonData base64EncodedStringWithOptions:0];
    
    // 调用JavaScript方法更新样式
    NSString *jsCode = [NSString stringWithFormat:@"window.__firefox__.readerHelper.updateReaderStyleSettings('%@')", base64String];
    
    [tab.webView evaluateJavaScript:jsCode completionHandler:^(id _Nullable result, NSError * _Nullable error) {
        if (error) {
            NSLog(@"Error updating reader style settings: %@", error);
        }
    }];
}

// 是否开启轻点翻页
+ (void)applyTapPageEnabledToReader:(BOOL)enabled tab:(Tab *)tab
{
    if (!tab) return;
    
    // 调用JavaScript方法更新样式
    NSString *jsCode = [NSString stringWithFormat:@"window.__firefox__.readerHelper.updateTapPageSetting(%d)", enabled];
    
    [tab.webView evaluateJavaScript:jsCode completionHandler:^(id _Nullable result, NSError * _Nullable error) {
        if (error) {
            NSLog(@"Error updating reader style settings: %@", error);
        }
    }];
}


@end
