//
//  BookSettingHelper.h
//  PPBrowser
//
//  Created by qingbin on 2025/6/3.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "BookConfigModel.h"
#import "Tab.h"

NS_ASSUME_NONNULL_BEGIN

/**
 * 阅读模式配置管理类
 * 负责BookConfigModel的保存和获取操作
 * 基于自定义UserDefaults实现数据持久化
 */
@interface BookSettingHelper : NSObject

/// 单例实例
+ (instancetype)sharedInstance;

/// 获取当前配置
/// @return 当前配置模型
- (BookConfigModel *)getCurrentConfig;

/// 保存配置
- (void)saveConfig;

/// 将BookConfigModel转换为Reader.js需要的styleSettings格式
- (NSDictionary *)convertToStyleSettings;

// 是否开启轻点翻页
+ (void)applyTapPageEnabledToReader:(BOOL)enabled tab:(Tab *)tab;
// 将当前样式设置应用到阅读模式
+ (void)applyStyleSettingsToReader:(Tab *)tab;

@property (nonatomic, strong) BookConfigModel *config;

@end

NS_ASSUME_NONNULL_END
