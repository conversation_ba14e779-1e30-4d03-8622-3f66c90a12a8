# BookSetting 阅读配置管理系统

## 概述

BookSetting是一个简洁的阅读模式配置管理系统，基于自定义UserDefaults实现数据持久化，为BookConfigModel提供获取和保存操作。

## 架构设计

### 核心组件

1. **BookConfigModel** - 阅读配置数据模型
2. **BookSettingHelper** - 配置管理类，集成自定义UserDefaults逻辑

### 数据流

```
BookSettingHelper -> NSUserDefaults (自定义suite)
```

## 主要功能

### 简洁的配置管理
- ✅ 获取当前配置
- ✅ 保存配置

## 使用方法

### 基本使用

```objc
#import "BookSettingHelper.h"

// 获取单例实例
BookSettingHelper *helper = [BookSettingHelper sharedInstance];

// 获取当前配置
BookConfigModel *currentConfig = [helper getCurrentConfig];

// 修改配置
currentConfig.fontSize = 18.0;
currentConfig.lineSpace = 10.0;
currentConfig.justifiedAlignment = YES;

// 保存配置
[helper saveConfig:currentConfig];
```

## 配置属性

BookConfigModel包含以下配置属性：

| 属性 | 类型 | 说明 |
|------|------|------|
| fontSize | CGFloat | 字体大小 |
| lineSpace | CGFloat | 行间距 |
| paragraphSpace | CGFloat | 段落间距 |
| firstLineHeadIndent | CGFloat | 首行缩进 |
| margin | CGFloat | 左右间距 |
| topMargin | CGFloat | 上边距 |
| bottomMargin | CGFloat | 下边距 |
| justifiedAlignment | BOOL | 两端对齐 |
| oneHandMode | BOOL | 单手模式 |
| selectedLanguage | LanguageType | 简繁体切换 |
| updateTime | NSString | 更新时间 |

## 数据持久化

### 存储方式
- 使用自定义NSUserDefaults suite: `com.ppbrowser.bookconfig`
- 数据以JSON格式存储
- 自动更新时间戳

### 存储键值
- `book_config_data`: 存储配置数据

## 默认配置

首次使用时会自动创建默认配置：
- 字体大小: 16.0
- 行间距: 8.0
- 段落间距: 12.0
- 首行缩进: 2.0
- 左右间距: 20.0
- 上下边距: 50.0
- 两端对齐: 开启
- 单手模式: 关闭
- 简繁体: 自动

## 注意事项

1. **简洁设计**: 只提供最核心的获取和保存功能
2. **自动管理**: 自动处理默认配置和时间戳更新
3. **数据安全**: 使用独立的UserDefaults suite避免冲突
4. **向后兼容**: 支持从字典恢复配置数据
