//
//  BookSettingCell.m
//  Reader
//
//  Created by qingbin on 2023/8/9.
//

#import "BookSettingCell.h"

#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "UIView+FrameHelper.h"
#import "NSObject+Helper.h"

#import "ThemeProtocol.h"
#import "PPNotifications.h"
#import "BrowserUtils.h"

/// 字间距、行间距、段落间距、文本对齐方式（左对齐，中间对齐，右对齐，两端对齐）、首行缩进、左手模式？、左右间距？、暗黑模式自动切换黑色背景？
@interface BookSettingCell ()<ThemeProtocol>

@property (nonatomic, strong) UIView* backView;

@property (nonatomic, strong) UILabel* titleLabel;

@property (nonatomic, strong) UIView* line;

@property (nonatomic, assign) BOOL bShowLine;

@property (nonatomic, strong) UIStackView* stackView;

@property (nonatomic, strong) UISwitch* switchView;

@property (nonatomic, strong) UIStepper* stepper;

@property (nonatomic, strong) UILabel* contentLabel;

@property (nonatomic, strong) UISegmentedControl* segment;

@property (nonatomic, strong) UIImageView* arrow;

@property (nonatomic, strong) BookSettingModel *model;

@end

@implementation BookSettingCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        
        [self addSubviews];
        [self defineLayout];
        [self setupObservers];
        
        [self applyTheme];
    }
    
    return self;
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.titleLabel.textColor = UIColor.whiteColor;
        
        self.contentLabel.textColor = UIColor.whiteColor;
        
        self.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.backView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        self.line.backgroundColor = [UIColor colorWithHexString:kDarkThemeColorLine];
        
        self.arrow.tintColor = UIColor.whiteColor;
    } else {
        self.titleLabel.textColor = [UIColor colorWithHexString:@"#333333"];
        
        self.contentLabel.textColor = [UIColor colorWithHexString:@"#333333"];
        
        self.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.backView.backgroundColor = UIColor.whiteColor;
        self.line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        
        self.arrow.tintColor = [UIColor colorWithHexString:@"#333333"];
    }
}

- (void)updateWithModel:(BookSettingModel *)model
{
    self.model = model;
    
    self.titleLabel.text = model.title;
    
    self.switchView.hidden = YES;
    self.arrow.hidden = YES;
    self.stepper.hidden = YES;
    self.contentLabel.hidden = YES;
    self.segment.hidden = YES;
    
    if(model.type == BookSettingTypeStepper) {
        self.contentLabel.text = [NSString stringWithFormat:@"%.1f", model.value];
        self.stepper.maximumValue = model.maxValue;
        self.stepper.minimumValue = model.minValue;
        self.stepper.value = model.value;
        self.stepper.stepValue = model.stepValue;
        
        self.stepper.hidden = NO;
        self.contentLabel.hidden = NO;
    } else if(model.type == BookSettingTypeSwitch) {
        self.switchView.on = model.isOn;
        
        self.switchView.hidden = NO;
    } else if(model.type == BookSettingTypeSegment) {
        self.segment.hidden = NO;
        [self.segment setSelectedSegmentIndex:model.value];
    } else if(model.type == BookSettingTypeArrow) {
        self.arrow.hidden = NO;
    }
    
    [self updateCornerRadius];
}

- (void)setupObservers
{
    @weakify(self)
    [[self.stepper rac_signalForControlEvents:UIControlEventValueChanged]
    subscribeNext:^(id x) {
        @strongify(self)
        double value = self.stepper.value;
        self.contentLabel.text = [NSString stringWithFormat:@"%.1f", value];
        
        if(self.model.valueChangedAction) {
            self.model.valueChangedAction(value);
        }
    }];
    
    [[self.switchView rac_newOnChannel] subscribeNext:^(id x) {
        @strongify(self)
        if(self.model.newOnChannelAction) {
            self.model.newOnChannelAction(self.switchView.on);
        }
    }];
    
    [self.segment addTarget:self action:@selector(selectedIndex:) forControlEvents:UIControlEventValueChanged];
    
    UITapGestureRecognizer* tap = [UITapGestureRecognizer new];
    [self.contentView addGestureRecognizer:tap];
    [tap.rac_gestureSignal subscribeNext:^(id x) {
        if (self.model.tapAction) {
            self.model.tapAction();
        }
    }];
}

- (void)selectedIndex:(id)sender
{
    if(self.model.selectIndexAction) {
        self.model.selectIndexAction((int)self.segment.selectedSegmentIndex);
    }
}

- (void)updateCornerRadius
{
    // 圆角
    self.line.hidden = NO;
    self.backView.layer.cornerRadius = 10;
    self.backView.layer.masksToBounds = YES;
    
    if(self.model.isFirstInSection && self.model.isLastInSection) {
        //只有一个
        self.backView.layer.masksToBounds = YES;
        self.backView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner | kCALayerMinXMaxYCorner | kCALayerMaxXMaxYCorner;
        self.line.hidden = YES;
    } else {
        if(self.model.isFirstInSection) {
            //添加上圆角
            self.backView.layer.masksToBounds = YES;
            self.backView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
        } else if(self.model.isLastInSection) {
            //添加下圆角
            self.backView.layer.masksToBounds = YES;
            self.backView.layer.maskedCorners = kCALayerMinXMaxYCorner | kCALayerMaxXMaxYCorner;
            self.line.hidden = YES;
        } else {
            self.backView.layer.masksToBounds = NO;
            self.backView.layer.cornerRadius = 0;
        }
    }
}

- (void)addSubviews
{
    [self.contentView addSubview:self.backView];
    [self.backView addSubview:self.titleLabel];
    [self.backView addSubview:self.stackView];
}

- (void)defineLayout
{
    float leftOffset = iPadValue(30, 15);
    [self.backView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(leftOffset);
        make.right.mas_offset(-leftOffset);
        make.top.bottom.equalTo(self.contentView);
        make.height.mas_equalTo(60);
    }];
 
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.backView);
        make.left.mas_offset(15);
    }];
    
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.backView).offset(-10);
        make.centerY.equalTo(self.backView);
    }];
    
    float size = iPadValue(15, 10);
    [self.arrow mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(size);
    }];
        
    UIView* line = [UIView new];
    [self.backView addSubview:line];
    [line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.backView);
        make.left.equalTo(self.backView);
        make.right.mas_offset(0);
        make.height.mas_equalTo(0.5);
    }];
    self.line = line;
}

- (UIStackView *)stackView
{
    if(!_stackView) {
        _stackView = [[UIStackView alloc]initWithArrangedSubviews:@[
            self.contentLabel,
            self.stepper,
            self.switchView,
            self.arrow,
            self.segment,
        ]];
        
        _stackView.axis = UILayoutConstraintAxisHorizontal;
        _stackView.spacing = 10;
        _stackView.alignment = UIStackViewAlignmentCenter;
    }
    
    return _stackView;
}

- (UILabel *)titleLabel
{
    if(!_titleLabel) {
        _titleLabel = [UIView createLabelWithTitle:@""
                                         textColor:[UIColor colorWithHexString:@"#333333"]
                                           bgColor:UIColor.clearColor
                                          fontSize:16
                                     textAlignment:NSTextAlignmentLeft
                                             bBold:NO];
    }
    
    return _titleLabel;
}

- (UIView *)backView
{
    if(!_backView) {
        _backView = [UIView new];
    }
    
    return _backView;
}

- (UILabel *)contentLabel
{
    if(!_contentLabel) {
        _contentLabel = [UIView createLabelWithTitle:nil
                                        textColor:[UIColor colorWithHexString:@"#333333"]
                                          bgColor:UIColor.clearColor
                                         fontSize:16
                                    textAlignment:NSTextAlignmentRight
                                            bBold:NO];
        _contentLabel.hidden = YES;
    }
    
    return _contentLabel;
}

- (UIStepper *)stepper
{
    if(!_stepper) {
        _stepper = [[UIStepper alloc]init];
        _stepper.hidden = YES;
    }
    
    return _stepper;
}

- (UISwitch *)switchView
{
    if(!_switchView) {
        _switchView = [UISwitch new];
        _switchView.on = YES;
        _switchView.hidden = YES;
    }
    
    return _switchView;
}

- (UISegmentedControl *)segment
{
    if(!_segment) {
        _segment = [[UISegmentedControl alloc]initWithItems:@[@"自动", @"简体", @"繁体"]];
        _segment.selectedSegmentIndex = 0;
        _segment.hidden = YES;
    }
    
    return _segment;
}

- (UIImageView *)arrow
{
    if(!_arrow) {
        _arrow = [UIImageView new];
        
        UIImage* image = [UIImage imageNamed:@"standard_right_arrow"];
        image = [image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        
        _arrow.image = image;
        _arrow.hidden = YES;
    }
    
    return _arrow;
}

@end
