//
//  BookSettingViewController.m
//  Reader
//
//  Created by qingbin on 2023/8/9.
//

#import "BookSettingViewController.h"

#import "BookSettingCell.h"
#import "BookSettingModel.h"

#import "MaizyHeader.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"

#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "NSObject+Helper.h"
#import "NSString+Helper.h"
#import "UIView+FrameHelper.h"

#import "BookConfigModel.h"
#import "BookSettingHelper.h"

#import "PPNotifications.h"
#import "ReadingThemeViewController.h"
#import "BookReplaceController.h"
#import "UIImage+Extension.h"

@interface BookSettingViewController ()<UITableViewDelegate,UITableViewDataSource,ThemeProtocol>

@property (nonatomic, strong) UITableView* tableView;
@property (nonatomic, strong) NSMutableArray* model;
//
@property (nonatomic, weak) Tab *tab;
//
@property (nonatomic, strong) UIImageView* rightImageView;
//
@property (nonatomic, assign) BookSettingPageType pageType;

@end

@implementation BookSettingViewController

- (instancetype)initWithTab:(Tab *)tab
                   pageType:(BookSettingPageType)pageType
{
    self = [super init];
    if (self) {
        self.tab = tab;
        self.pageType = pageType;
    }
    
    return self;
}

- (void)viewDidLoad
{
    [super viewDidLoad];
    
    self.title = @"阅读模式设置";
    
    [self addSubviews];
    [self defineLayout];
    [self setupObservers];
    
    [self createCustomLeftBarButtonItem];
    [self _createCustomRightBarButtonItem];
    
    [self updateWithModel];
    
    [self applyTheme];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.rightImageView.tintColor = UIColor.whiteColor;
    } else {
        self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.rightImageView.tintColor = [UIColor colorWithHexString:@"#222222"];
    }
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    //只有当前的controller会触发一次, 其它已存在的controller走通知
    if(needReload) {
        //修改暗黑模式
        [self applyTheme];
        
        [self.tableView reloadData];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}

#pragma mark -- 导航栏
- (void)_createCustomRightBarButtonItem
{
    UIButton* rightButton = [[UIButton alloc] initWithFrame:CGRectMake(0.0, 0.0f, 44.0f, 44.0)];
    [rightButton setBackgroundColor:[UIColor clearColor]];

    UIImageView* imageView = [UIImageView new];
    
    UIImage* image = [UIImage ext_systemImageNamed:@"power"
                                         pointSize:20
                                        renderMode:UIImageRenderingModeAlwaysTemplate];
    imageView.image = image;
    
    self.rightImageView = imageView;
    
    [rightButton addSubview:imageView];
    [imageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(rightButton);
    }];
    
    [rightButton addTarget:self action:@selector(rightBarbuttonClick:) forControlEvents:UIControlEventTouchUpInside];
    UIBarButtonItem* barItem = [[UIBarButtonItem alloc] initWithCustomView:rightButton];

    UIBarButtonItem *spacer = [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemFixedSpace target:nil action:nil];
    spacer.width = -16.0f; // for example shift right bar button to the right

    self.navigationItem.rightBarButtonItems = @[spacer, barItem];
}

- (void)rightBarbuttonClick:(id)sender
{
    [self.tab reload];
    [super leftBarbuttonClick];
}

- (void)updateWithModel
{
    /// 字间距、行高、段落间距、文本对齐方式（左对齐，中间对齐，右对齐，两端对齐）、首行缩进、左手模式？、左右间距？
    [self.model removeAllObjects];
    
    BookSettingHelper* configHelper = [BookSettingHelper sharedInstance];
    NSMutableArray* array = [NSMutableArray array];

    @weakify(self)
    if (self.pageType == BookSettingPageTypeHome) {
        //首页
        
        //简繁体切换
//        BookSettingModel* fontValueItem = [BookSettingModel new];
//        fontValueItem.type = BookSettingTypeSegment;
//        fontValueItem.value = configHelper.config.selectedLanguage;
//        fontValueItem.title = @"简繁体";
//
//        [fontValueItem setSelectIndexAction:^(int index) {
//            @strongify(self)
//            //赋值
//            configHelper.config.selectedLanguage = index;
//            //保存配置
//            [configHelper saveConfig];
//            //同步到html
//            [BookSettingHelper applyStyleSettingsToReader:self.tab];
//            //刷新列表
//            [self updateWithModel];
//        }];
//        [array addObject:fontValueItem];
        
        //两端对齐
        BookSettingModel* justifiedAlignment = [BookSettingModel new];
        justifiedAlignment.type = BookSettingTypeSwitch;
        justifiedAlignment.isOn = configHelper.config.justifiedAlignment;
        justifiedAlignment.title = @"两端对齐";
        [justifiedAlignment setNewOnChannelAction:^(BOOL isOn) {
            @strongify(self)
            //赋值
            configHelper.config.justifiedAlignment = isOn;
            //保存配置
            [configHelper saveConfig];
            //同步到html
            [BookSettingHelper applyStyleSettingsToReader:self.tab];
            //刷新列表
            [self updateWithModel];
        }];
        [array addObject:justifiedAlignment];
        
        //轻点翻页
        BookSettingModel* oneHandMode = [BookSettingModel new];
        oneHandMode.type = BookSettingTypeSwitch;
        oneHandMode.isOn = configHelper.config.tapPageEnabled;
        oneHandMode.title = @"轻点翻页";
        [oneHandMode setNewOnChannelAction:^(BOOL isOn) {
            @strongify(self)
            //赋值
            configHelper.config.tapPageEnabled = isOn;
            //保存配置
            [configHelper saveConfig];
            //同步到html
            [BookSettingHelper applyTapPageEnabledToReader:isOn tab:self.tab];
            //刷新列表
            [self updateWithModel];
        }];
        [array addObject:oneHandMode];
        
        //字体间距调整
        BookSettingModel* fontAndMarginItem = [BookSettingModel new];
        fontAndMarginItem.type = BookSettingTypeArrow;
        fontAndMarginItem.title = @"字体和间距";

        [fontAndMarginItem setTapAction:^() {
            @strongify(self)
            BookSettingViewController* vc = [[BookSettingViewController alloc] initWithTab:self.tab pageType:BookSettingPageTypeTextStyle];
            [self.navigationController pushViewController:vc animated:YES];
        }];
        [array addObject:fontAndMarginItem];
        
        //背景主题
        BookSettingModel* themeItem = [BookSettingModel new];
        themeItem.type = BookSettingTypeArrow;
        themeItem.title = @"背景主题";

        [themeItem setTapAction:^() {
            @strongify(self)
            ReadingThemeViewController* vc = [[ReadingThemeViewController alloc] initWithTab:self.tab];
            [self.navigationController pushViewController:vc animated:YES];
        }];
        [array addObject:themeItem];
        
        //文本过滤
        BookSettingModel* filterItem = [BookSettingModel new];
        filterItem.type = BookSettingTypeArrow;
        filterItem.title = @"文本过滤";

        [filterItem setTapAction:^() {
            @strongify(self)
            BookReplaceController* vc = [[BookReplaceController alloc] initWithTab:self.tab];
            [self.navigationController pushViewController:vc animated:YES];
        }];
        [array addObject:filterItem];
    } else if (self.pageType == BookSettingPageTypeTextStyle) {
        //字体、间距等调整页
        //字体大小
        BookSettingModel* fontSize = [BookSettingModel new];
        fontSize.type = BookSettingTypeStepper;
        fontSize.minValue = 2;
        fontSize.maxValue = 50;
        fontSize.value = configHelper.config.fontSize;
        fontSize.title = @"字体大小";
        [fontSize setValueChangedAction:^(float value) {
            @strongify(self)
            //赋值
            configHelper.config.fontSize = value;
            //保存配置
            [configHelper saveConfig];
            //同步到html
            [BookSettingHelper applyStyleSettingsToReader:self.tab];
            //刷新列表
            [self updateWithModel];
        }];
        [array addObject:fontSize];
        
        //标题字体大小
        BookSettingModel* titleFontSize = [BookSettingModel new];
        titleFontSize.type = BookSettingTypeStepper;
        titleFontSize.minValue = 2;
        titleFontSize.maxValue = 50;
        titleFontSize.value = configHelper.config.titleFontSize;
        titleFontSize.title = @"标题字体大小";
        [titleFontSize setValueChangedAction:^(float value) {
            @strongify(self)
            //赋值
            configHelper.config.titleFontSize = value;
            //保存配置
            [configHelper saveConfig];
            //同步到html
            [BookSettingHelper applyStyleSettingsToReader:self.tab];
            //刷新列表
            [self updateWithModel];
        }];
        [array addObject:titleFontSize];
        
        //行高
        BookSettingModel* lineHeight = [BookSettingModel new];
        lineHeight.type = BookSettingTypeStepper;
        lineHeight.minValue = 1;
        lineHeight.maxValue = 100;
        lineHeight.stepValue = 0.1;
        lineHeight.value = configHelper.config.lineHeight;
        lineHeight.title = @"行高";
        [lineHeight setValueChangedAction:^(float value) {
            @strongify(self)
            //赋值
            configHelper.config.lineHeight = value;
            //保存配置
            [configHelper saveConfig];
            //同步到html
            [BookSettingHelper applyStyleSettingsToReader:self.tab];
            //刷新列表
            [self updateWithModel];
        }];
        [array addObject:lineHeight];
        
        //首行缩进
        BookSettingModel* firstLineHeadIndent = [BookSettingModel new];
        firstLineHeadIndent.type = BookSettingTypeStepper;
        firstLineHeadIndent.minValue = 0;
        firstLineHeadIndent.maxValue = 10;
        firstLineHeadIndent.value = configHelper.config.firstLineHeadIndent;
        firstLineHeadIndent.title = @"首行缩进";
        [firstLineHeadIndent setValueChangedAction:^(float value) {
            @strongify(self)
            //赋值
            configHelper.config.firstLineHeadIndent = value;
            //保存配置
            [configHelper saveConfig];
            //同步到html
            [BookSettingHelper applyStyleSettingsToReader:self.tab];
            //刷新列表
            [self updateWithModel];
        }];
        [array addObject:firstLineHeadIndent];
        
        //左右间距
        BookSettingModel* margin = [BookSettingModel new];
        margin.type = BookSettingTypeStepper;
        margin.minValue = 0;
        margin.maxValue = 200;
        margin.value = configHelper.config.horizontalMargin;
        margin.title = @"左右间距";
        [margin setValueChangedAction:^(float value) {
            @strongify(self)
            //赋值
            configHelper.config.horizontalMargin = value;
            //保存配置
            [configHelper saveConfig];
            //同步到html
            [BookSettingHelper applyStyleSettingsToReader:self.tab];
            //刷新列表
            [self updateWithModel];
        }];
        [array addObject:margin];
        
        //上间距
        BookSettingModel* topMargin = [BookSettingModel new];
        topMargin.type = BookSettingTypeStepper;
        topMargin.minValue = 0;
        topMargin.maxValue = 200;
        topMargin.value = configHelper.config.topMargin;
        topMargin.title = @"上间距";
        [topMargin setValueChangedAction:^(float value) {
            @strongify(self)
            //赋值
            configHelper.config.topMargin = value;
            //保存配置
            [configHelper saveConfig];
            //同步到html
            [BookSettingHelper applyStyleSettingsToReader:self.tab];
            //刷新列表
            [self updateWithModel];
        }];
        [array addObject:topMargin];
        
        //下间距
        BookSettingModel* bottomMargin = [BookSettingModel new];
        bottomMargin.type = BookSettingTypeStepper;
        bottomMargin.minValue = 0;
        bottomMargin.maxValue = 200;
        bottomMargin.value = configHelper.config.bottomMargin;
        bottomMargin.title = @"下间距";
        [bottomMargin setValueChangedAction:^(float value) {
            @strongify(self)
            //赋值
            configHelper.config.bottomMargin = value;
            //保存配置
            [configHelper saveConfig];
            //同步到html
            [BookSettingHelper applyStyleSettingsToReader:self.tab];
            //刷新列表
            [self updateWithModel];
        }];
        [array addObject:bottomMargin];
    }
    
    [self.model addObject:array];
    
    BookSettingModel* first = array.firstObject;
    first.isFirstInSection = YES;
    
    BookSettingModel* last = array.lastObject;
    last.isLastInSection = YES;
    
    [self.tableView reloadData];
}

- (void)setupObservers
{}

#pragma mark - Public Method

#pragma mark -- layout
- (void)addSubviews
{
    [self.view addSubview:self.tableView];
}

- (void)defineLayout
{
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
    }];
}

#pragma mark - UITableViewDataSource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    NSArray* array = self.model[section];
    return array.count;
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return self.model.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    NSArray* array = self.model[indexPath.section];
    BookSettingModel* model = array[indexPath.row];
    BookSettingCell* cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass(BookSettingCell.class)];

    [cell updateWithModel:model];
    
    return cell;
}

- (UITableView *)tableView
{
    if(!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
        
        _tableView.separatorStyle  = UITableViewCellSeparatorStyleNone;
        _tableView.showsHorizontalScrollIndicator = NO;
        _tableView.showsVerticalScrollIndicator   = NO;
        _tableView.delegate   = self;
        _tableView.dataSource = self;
        _tableView.rowHeight = UITableViewAutomaticDimension;
        _tableView.backgroundColor = UIColor.clearColor;
        
        [_tableView registerClass:[BookSettingCell class] forCellReuseIdentifier:NSStringFromClass([BookSettingCell class])];
        
        _tableView.tableFooterView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, 50)];
        _tableView.tableHeaderView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, iPadValue(30, 15))];
    }
    
    return _tableView;
}

- (NSMutableArray *)model
{
    if(!_model) {
        _model = [NSMutableArray array];
    }
    
    return _model;
}


@end
