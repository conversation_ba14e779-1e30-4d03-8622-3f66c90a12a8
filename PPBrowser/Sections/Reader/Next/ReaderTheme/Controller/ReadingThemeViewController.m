//
//  ReadingThemeViewController.m
//  PPBrowser
//
//  Created by qingbin on 2024/12/19.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import "ReadingThemeViewController.h"
#import "ReadingThemeCell.h"
#import "ReadingThemeModel.h"

#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "UIView+FrameHelper.h"
#import "NSObject+Helper.h"

#import "ThemeProtocol.h"
#import "PreferenceManager.h"
#import "BrowserUtils.h"

#import "BookSettingHelper.h"
#import "UIImage+Extension.h"

@interface ReadingThemeViewController ()<UITableViewDataSource, UITableViewDelegate, ThemeProtocol>

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) NSMutableArray<ReadingThemeModel *> *themeModels;
//
@property (nonatomic, strong) UIImageView* rightImageView;
//
@property (nonatomic, weak) Tab *tab;

@end

@implementation ReadingThemeViewController

- (instancetype)initWithTab:(Tab *)tab
{
    self = [super init];
    if (self) {
        self.tab = tab;
    }
    
    return self;
}

- (void)viewDidLoad
{
    [super viewDidLoad];
    
    self.title = @"选择背景主题";
    [self createCustomLeftBarButtonItem];
    [self _createCustomRightBarButtonItem];
    
    [self addSubviews];
    [self defineLayout];
    [self updateWithModel];
    
    [self applyTheme];
}

#pragma mark -- 导航栏
- (void)_createCustomRightBarButtonItem
{
    UIButton* rightButton = [[UIButton alloc] initWithFrame:CGRectMake(0.0, 0.0f, 44.0f, 44.0)];
    [rightButton setBackgroundColor:[UIColor clearColor]];

    UIImageView* imageView = [UIImageView new];
    
    UIImage* image = [UIImage ext_systemImageNamed:@"power"
                                         pointSize:20
                                        renderMode:UIImageRenderingModeAlwaysTemplate];
    imageView.image = image;
    
    self.rightImageView = imageView;
    
    [rightButton addSubview:imageView];
    [imageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(rightButton);
    }];
    
    [rightButton addTarget:self action:@selector(rightBarbuttonClick:) forControlEvents:UIControlEventTouchUpInside];
    UIBarButtonItem* barItem = [[UIBarButtonItem alloc] initWithCustomView:rightButton];

    UIBarButtonItem *spacer = [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemFixedSpace target:nil action:nil];
    spacer.width = -16.0f; // for example shift right bar button to the right

    self.navigationItem.rightBarButtonItems = @[spacer, barItem];
}

- (void)rightBarbuttonClick:(id)sender
{
    [self.tab reload];
    [super leftBarbuttonClick];
}

#pragma mark - 私有方法

- (void)updateWithModel
{
    [self.themeModels removeAllObjects];
    [self.themeModels addObjectsFromArray:[ReadingThemeModel defaultThemeArray]];
    
    BookConfigModel* config = [[BookSettingHelper sharedInstance] getCurrentConfig];
    for (ReadingThemeModel* item in self.themeModels) {
        if (item.theme == config.theme) {
            item.isSelected = true;
        } else {
            item.isSelected = false;
        }
    }
    
    ReadingThemeModel* item = self.themeModels.firstObject;
    item.isFirstInSection = YES;
    item = self.themeModels.lastObject;
    item.isLastInSection = YES;
    
    [self.tableView reloadData];
}

#pragma mark - layout

- (void)addSubviews
{
    [self.view addSubview:self.tableView];
}

- (void)defineLayout
{
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(0);
        make.right.mas_offset(-0);
        make.top.equalTo(self.view).offset(0);
        make.bottom.equalTo(self.view);
    }];
}

#pragma mark - UITableViewDataSource

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return self.themeModels.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    ReadingThemeCell *cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass(ReadingThemeCell.class) forIndexPath:indexPath];
    
    if (indexPath.row < self.themeModels.count) {
        ReadingThemeModel *model = self.themeModels[indexPath.row];
        [cell updateWithModel:model];
    }
    
    return cell;
}

#pragma mark - UITableViewDelegate

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    
    if (indexPath.row < self.themeModels.count) {
        ReadingThemeModel *model = self.themeModels[indexPath.row];
        
        for (ReadingThemeModel* item in self.themeModels) {
            item.isSelected = false;
        }
        
        model.isSelected = true;
        [self.tableView reloadData];
        
        [BookSettingHelper sharedInstance].config.theme = model.theme;
        [[BookSettingHelper sharedInstance] saveConfig];
        
        //同步到html
        [BookSettingHelper applyStyleSettingsToReader:self.tab];
    }
}

#pragma mark - ThemeProtocol

- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if (isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.rightImageView.tintColor = UIColor.whiteColor;
    } else {
        self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.rightImageView.tintColor = [UIColor colorWithHexString:@"#222222"];
    }
    
    [self.tableView reloadData];
}

#pragma mark - Getter

- (NSMutableArray<ReadingThemeModel *> *)themeModels
{
    if (!_themeModels) {
        _themeModels = [NSMutableArray array];
    }
    return _themeModels;
}

- (UITableView *)tableView
{
    if(!_tableView)
    {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStyleGrouped];

        _tableView.separatorStyle  = UITableViewCellSeparatorStyleNone;
        _tableView.showsHorizontalScrollIndicator = NO;
        _tableView.showsVerticalScrollIndicator   = NO;
        _tableView.delegate   = self;
        _tableView.dataSource = self;
        _tableView.rowHeight = iPadValue(88, 60);
        
        [_tableView registerClass:[ReadingThemeCell class] forCellReuseIdentifier:NSStringFromClass([ReadingThemeCell class])];
        
        //不能用CGFLOAT_MIN,否则iPhone8的iOS14.7会有一大块空白,系统BUG
        float height = iPadValue(10, 0.5);
        UIView* view = [[UIView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, height)];
        view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        _tableView.tableHeaderView = view;
        
        _tableView.tableFooterView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, 50)];
        _tableView.tableHeaderView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, iPadValue(30, 15))];
        
        _tableView.sectionFooterHeight = 0.0;
        _tableView.sectionHeaderHeight = 0.0f;
    }
    
    return _tableView;
}


@end
