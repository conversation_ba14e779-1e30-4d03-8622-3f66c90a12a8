//
//  ReadingThemeCell.m
//  PPBrowser
//
//  Created by qingbin on 2024/12/19.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import "ReadingThemeCell.h"

#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "UIView+FrameHelper.h"
#import "NSObject+Helper.h"

#import "ThemeProtocol.h"
#import "PreferenceManager.h"
#import "BrowserUtils.h"

@interface ReadingThemeCell ()<ThemeProtocol>

@property(nonatomic, strong) UIView *backView;
@property(nonatomic, strong) UIView *colorPreview;
@property(nonatomic, strong) UILabel *titleLabel;
@property(nonatomic, strong) UIImageView *selectedLogo;
@property(nonatomic, strong) UIView *line;

@property (nonatomic, strong) ReadingThemeModel *model;

@end

@implementation ReadingThemeCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        self.backgroundColor = UIColor.clearColor;
        [self addSubviews];
        [self defineLayout];
    }
    
    return self;
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.backView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        self.titleLabel.textColor = UIColor.whiteColor;
        self.line.backgroundColor = [UIColor colorWithHexString:kDarkThemeColorLine];
    } else {
        self.titleLabel.textColor = [UIColor colorWithHexString:@"#434343"];
        
        if(self.model.isSelected) {
            self.backView.backgroundColor = [UIColor colorWithHexString:@"#EEF7FF"];
        } else {
            self.backView.backgroundColor = UIColor.whiteColor;
        }
        
        self.line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
}

- (void)updateWithModel:(ReadingThemeModel *)model;
{
    self.model = model;
    
    // 更新标题
    self.titleLabel.text = model.title;
    
    // 更新颜色预览
    self.colorPreview.backgroundColor = [UIColor colorWithHexString:model.backgroundColor];
    
    // 更新选中状态
    if(model.isSelected) {
        self.backView.backgroundColor = [UIColor colorWithHexString:@"#EEF7FF"];
        self.selectedLogo.hidden = NO;
    } else {
        self.backView.backgroundColor = UIColor.whiteColor;
        self.selectedLogo.hidden = YES;
    }
    
    [self updateCornerRadius];
    [self applyTheme];
}

- (void)updateCornerRadius
{
    // 圆角
    self.line.hidden = NO;
    self.backView.layer.cornerRadius = iPadValue(20, 10);
    self.backView.layer.masksToBounds = YES;
    
    if(self.model.isFirstInSection && self.model.isLastInSection) {
        //只有一个
        self.backView.layer.masksToBounds = YES;
        self.backView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner | kCALayerMinXMaxYCorner | kCALayerMaxXMaxYCorner;
        self.line.hidden = YES;
    } else {
        if(self.model.isFirstInSection) {
            //添加上圆角
            self.backView.layer.masksToBounds = YES;
            self.backView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
        } else if(self.model.isLastInSection) {
            //添加下圆角
            self.backView.layer.masksToBounds = YES;
            self.backView.layer.maskedCorners = kCALayerMinXMaxYCorner | kCALayerMaxXMaxYCorner;
            self.line.hidden = YES;
        } else {
            self.backView.layer.masksToBounds = NO;
            self.backView.layer.cornerRadius = 0;
        }
    }
}

- (void)addSubviews
{
    [self.contentView addSubview:self.backView];
    [self.backView addSubview:self.colorPreview];
    [self.backView addSubview:self.titleLabel];
    [self.backView addSubview:self.selectedLogo];
}

- (void)defineLayout
{
    float offset = iPadValue(30, 15);
    [self.backView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(offset);
        make.right.mas_offset(-offset);
        make.top.bottom.equalTo(self.contentView);
    }];
    
    float size = iPadValue(40, 28);
    [self.colorPreview mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.left.mas_offset(iPadValue(30, 15));
        make.size.mas_equalTo(size);
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.left.equalTo(self.colorPreview.mas_right).offset(20);
        make.right.mas_offset(-(20));
    }];
    
    [self.selectedLogo mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.right.mas_offset(-20);
        make.size.mas_equalTo(iPadValue(35, 25));
    }];
    
    UIView* line = [UIView new];
    line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    [self.backView addSubview:line];
    [line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.contentView);
        make.left.mas_offset(0);
        make.right.mas_offset(-0);
        make.height.mas_equalTo(0.5);
    }];
    self.line = line;
}

- (UIView *)backView
{
    if(!_backView) {
        _backView = [UIView new];
    }
    
    return _backView;
}

- (UILabel *)titleLabel
{
    if(!_titleLabel) {
        _titleLabel = [UIView createLabelWithTitle:@""
                                         textColor:[UIColor colorWithHexString:@"#333333"]
                                           bgColor:UIColor.clearColor
                                          fontSize:iPadValue(20, 16)
                                     textAlignment:NSTextAlignmentLeft
                                             bBold:NO];
    }
    
    return _titleLabel;
}

- (UIView *)colorPreview
{
    if(!_colorPreview) {
        _colorPreview = [UIView new];
        _colorPreview.layer.cornerRadius = iPadValue(12, 6);
        _colorPreview.layer.masksToBounds = YES;
        _colorPreview.layer.borderWidth = 1.0f;
        _colorPreview.layer.borderColor = [UIColor colorWithHexString:@"#e0e0e0"].CGColor;
        
        // 添加阴影效果
        _colorPreview.layer.shadowColor = [UIColor blackColor].CGColor;
        _colorPreview.layer.shadowOffset = CGSizeMake(0, 1);
        _colorPreview.layer.shadowOpacity = 0.1f;
        _colorPreview.layer.shadowRadius = 2.0f;
    }
    
    return _colorPreview;
}

- (UIImageView *)selectedLogo
{
    if(!_selectedLogo) {
        _selectedLogo = [UIImageView new];
        _selectedLogo.image = [UIImage imageNamed:@"common_check_icon"];
    }
    
    return _selectedLogo;
}

@end
