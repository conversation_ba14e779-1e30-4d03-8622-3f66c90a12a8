//
//  ReadingThemeModel.m
//  PPBrowser
//
//  Created by qingbin on 2024/12/19.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import "ReadingThemeModel.h"

@implementation ReadingThemeModel

+ (instancetype)whiteTheme {
    ReadingThemeModel *model = [[ReadingThemeModel alloc] init];
    model.theme = ReadingModeThemeTypeWhite;
    model.backgroundColor = @"#ffffff";
    model.textColor = @"#333333";
    model.title = NSLocalizedString(@"reader.white.theme", nil);
    return model;
}

+ (instancetype)beigeTheme {
    ReadingThemeModel *model = [[ReadingThemeModel alloc] init];
    model.theme = ReadingModeThemeTypeBeige;
    model.backgroundColor = @"#f5f5dc";
    model.textColor = @"#333333";
    model.title = NSLocalizedString(@"reader.beige.theme", nil);
    return model;
}

+ (instancetype)darkTheme {
    ReadingThemeModel *model = [[ReadingThemeModel alloc] init];
    model.theme = ReadingModeThemeTypeDark;
    model.backgroundColor = @"#2d3748";
    model.textColor = @"#e2e8f0";
    model.title = NSLocalizedString(@"reader.dark.theme", nil);
    return model;
}

+ (instancetype)greenTheme {
    ReadingThemeModel *model = [[ReadingThemeModel alloc] init];
    model.theme = ReadingModeThemeTypeGreen;
    model.backgroundColor = @"#c6f6d5";
    model.textColor = @"#333333";
    model.title = NSLocalizedString(@"reader.green.theme", nil);
    return model;
}

+ (instancetype)blueTheme {
    ReadingThemeModel *model = [[ReadingThemeModel alloc] init];
    model.theme = ReadingModeThemeTypeBlue;
    model.backgroundColor = @"#e6f3ff";
    model.textColor = @"#333333";
    model.title = NSLocalizedString(@"reader.blue.theme", nil);
    return model;
}

+ (instancetype)pinkTheme {
    ReadingThemeModel *model = [[ReadingThemeModel alloc] init];
    model.theme = ReadingModeThemeTypePink;
    model.backgroundColor = @"#fce7f3";
    model.textColor = @"#333333";
    model.title = NSLocalizedString(@"reader.pink.theme", nil);
    return model;
}

+ (instancetype)yellowTheme {
    ReadingThemeModel *model = [[ReadingThemeModel alloc] init];
    model.theme = ReadingModeThemeTypeYellow;
    model.backgroundColor = @"#fef3c7";
    model.textColor = @"#333333";
    model.title = NSLocalizedString(@"reader.yellow.theme", nil);
    return model;
}

+ (instancetype)grayTheme {
    ReadingThemeModel *model = [[ReadingThemeModel alloc] init];
    model.theme = ReadingModeThemeTypeGray;
    model.backgroundColor = @"#f3f4f6";
    model.textColor = @"#333333";
    model.title = NSLocalizedString(@"reader.gray.theme", nil);
    return model;
}

+ (instancetype)purpleTheme {
    ReadingThemeModel *model = [[ReadingThemeModel alloc] init];
    model.theme = ReadingModeThemeTypePurple;
    model.backgroundColor = @"#ede9fe";
    model.textColor = @"#333333";
    model.title = NSLocalizedString(@"reader.purple.theme", nil);
    return model;
}

// 默认的排序列表
+ (NSArray<ReadingThemeModel *> *)defaultThemeArray
{
    return @[
        [self whiteTheme],
        [self beigeTheme],
        [self darkTheme],
        [self greenTheme],
        [self blueTheme],
        [self pinkTheme],
        [self yellowTheme],
        [self grayTheme],
        [self purpleTheme],
    ];
}

// 根据theme返回model
+ (ReadingThemeModel *)modelFromTheme:(ReadingModeThemeType)theme
{
    switch (theme) {
        case ReadingModeThemeTypeWhite:
            return [self whiteTheme];
            break;
        case ReadingModeThemeTypeBeige:
            return [self beigeTheme];
            break;
        case ReadingModeThemeTypeDark:
            return [self darkTheme];
            break;
        case ReadingModeThemeTypeGreen:
            return [self greenTheme];
            break;
        case ReadingModeThemeTypeBlue:
            return [self blueTheme];
            break;
        case ReadingModeThemeTypePink:
            return [self pinkTheme];
            break;
        case ReadingModeThemeTypeYellow:
            return [self yellowTheme];
            break;
        case ReadingModeThemeTypeGray:
            return [self grayTheme];
            break;
        case ReadingModeThemeTypePurple:
            return [self purpleTheme];
            break;
            
        default:
            break;
    }
    
    return [self whiteTheme];
}

@end
