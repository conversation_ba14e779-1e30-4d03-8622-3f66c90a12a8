//
//  ReadingThemeModel.h
//  PPBrowser
//
//  Created by qingbin on 2024/12/19.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "PPEnums.h"

NS_ASSUME_NONNULL_BEGIN

/**
 * 阅读模式主题选择模型
 */
@interface ReadingThemeModel : NSObject

// 背景主题
@property (nonatomic, assign) ReadingModeThemeType theme;
// 文字描述
@property (nonatomic, strong) NSString *title;
// 文字颜色
@property (nonatomic, strong) NSString *textColor;
// 背景颜色
@property (nonatomic, strong) NSString *backgroundColor;

// 辅助字段
// 是否为选中状态
@property (nonatomic, assign) BOOL isSelected;
// 是否为分组中的第一个
@property (nonatomic, assign) BOOL isFirstInSection;
// 是否为分组中的最后一个
@property (nonatomic, assign) BOOL isLastInSection;

// builder模式

// 白色
+ (instancetype)whiteTheme;
// 米色
+ (instancetype)beigeTheme;
// 深色
+ (instancetype)darkTheme;
// 护眼绿
+ (instancetype)greenTheme;
// 护眼蓝
+ (instancetype)blueTheme;
// 粉色
+ (instancetype)pinkTheme;
// 暖黄
+ (instancetype)yellowTheme;
// 灰色
+ (instancetype)grayTheme;
// 紫色
+ (instancetype)purpleTheme;

// 默认的排序列表
+ (NSArray<ReadingThemeModel *> *)defaultThemeArray;
// 根据theme返回model
+ (ReadingThemeModel *)modelFromTheme:(ReadingModeThemeType)theme;

@end

NS_ASSUME_NONNULL_END
