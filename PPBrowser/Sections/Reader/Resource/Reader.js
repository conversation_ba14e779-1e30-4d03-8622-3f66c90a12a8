!function() {
    // 实例变量
    let instance = {
        enabled: true,
        isLoading: false,
        currentPage: 1,
        totalPages: 1,
        updateAddress: true, //滑动时更新导航栏地址
        updateTitle: true, //滑动时更新导航栏标题
        hasMoreData: true,   //是否还有下一页
        debugEnabled: false, //调试模式
        lastPerformTime: 0,  //上次调用的时间，防止刷新过于频繁
        // 需要设置的变量
        action: 'next', // next 或 click
        append: 'element', // page, iframe, element, ajax, none
        nextLink: '[rel="next"]', // 默认next link选择器
        pageElement: '', // 页面元素选择器
        clickElement: '', // 点击元素选择器
        readerPageElement: '', //阅读模式页面的pageElement，用来定位插入数据的位置
        initalNextUrl: '', //初始化时的next url
        initalCurrentUrl: '', //初始化时的当前url
        // 阅读模式相关变量
        readerMode: false,
        shadowRoot: null,
        styleSettings: {
            fontSize: '20px',
            titleFontSize: '25px',
            fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Arial, sans-serif',
            lineHeight: '1.75',
            textColor: '#333333',
            backgroundColor: '#ffffff',
            secondaryBackgroundColor: '#f9f9f9',
            accentColor: '#4a90e2'
        },
        // 轻点翻页相关变量
        tapPageEnabled: false, // 是否启用轻点翻页
        tapPageHandler: null  // 轻点事件处理器
    };

    // 自动翻页开始

    function autoPageFunc() {
        let pages = [];
        let document_ = document;
        let insert_; // 插入点元素
        let loading;
        let intersectionObserver;
        let iframe_;
        // 添加新的DOM观察器函数
        let domObserver = null;

        /**
         * 启动自动翻页
         */
        function start() {
            if(!instance.started) {
                instance.started = true;
                prepareFirstPage();
            }
        }
        //直接开始
        start();

        /**
         * 添加滚动检测
         */
        function addScrollDetect() {
            // 1. 使用 Intersection Observer API 检测
            intersectionObserver = new IntersectionObserver(
                (entries) => {
                    entries.forEach(entry => {
                        if(entry.isIntersecting) {
                            scrollDetect(entry.target);
                        }
                    });
                },
                {
                    root: null,
                    rootMargin: '0px 0px 2%',
                    threshold: 0
                }
            );

            // 观察所有页面
            pages.forEach(page => {
                if(page.point) {
                    intersectionObserver.observe(page.point);
                }
            });

            // 2. 添加滚动事件监听
            window.addEventListener('scroll', () => {
                checkScrollPosition();
            });
        }

        /**
         * 检查滚动位置
         */
        function checkScrollPosition() {
            if (!instance.enabled || instance.isLoading || !instance.hasMoreData) return;

            const scrollHeight = Math.max(
                document.documentElement.scrollHeight,
                document.body.scrollHeight
            );
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const clientHeight = window.innerHeight || document.documentElement.clientHeight;

            // 当滚动到距离底部 12% 的位置时触发
            const scrollThreshold = scrollHeight * 0.12;
            const remainingScroll = scrollHeight - (scrollTop + clientHeight);

            if (remainingScroll <= scrollThreshold) {
                //到达底部，取pages最后一个元素
                if (pages && pages.length > 0) {
                    scrollDetect(pages[pages.length - 1].point);
                }
            }
        }

        /**
         * 滚动检测处理
         */
        function scrollDetect(element) {
            const previousPage = instance.currentPage;
            detectCurrentPage(element);

            // 只在以下情况触发加载：
            // 1. 向下滚动到新页面
            // 2. 当前页是最后一页（pages.length，因为currentPage从1开始）
            // 3. 检查是否需要追加新内容
            if (instance.currentPage >= previousPage
                && instance.currentPage === pages.length
                && shouldAppend()
                && instance.hasMoreData) {
                performAction();
            }
        }

        /**
         * 执行动作(Next Link或Click Element)
         */
        async function performAction() {
            // 如果距离上次执行不足500ms,直接返回
            if (instance.lastPerformTime && Date.now() - instance.lastPerformTime < 500) {
                return;
            }

            instance.lastPerformTime = Date.now();

            setLoading(true);

            try {
                await _performAction();
            } catch(error) {
                console.error('performAction error:', error);
            } finally {
                setLoading(false);
            }
        }

        async function _performAction() {
            if(instance.action === 'next') {
                //获取下一页链接
                let nextURL = findNextURL();

                //等待nextLink加载
                if(!nextURL) {
                    await waitForNextLink()
                    nextURL = findNextURL();
                }

                let _shouldAppend = nextURL!=null;
                if (instance.append === 'none') {
                    //https://apkpure.com/tw/game_adventure
                    //这里用的是第一个，专门适配apkpure
                    let nextElement = DOMHelper.getElement(instance.nextLink, true, document_);
                    _shouldAppend = DOMHelper.isElementHidden(nextElement)===false;
                }

                if(_shouldAppend) {
                    append(nextURL);
                } else {
                    setLoading(false);
                    //没有更多数据
                    instance.hasMoreData = false;

                    //监听后面是否会重新激活下一页逻辑
                    startDOMObserverAfterNoMoreData(document_);
                }
            } else if(instance.action === 'click') {
                // 1. 创建并加载iframe
                let url = findCurrentWebUrl();

                let iframe = await Iframe.createIframe(url, document_);
                if (iframe_) {
                    iframe_.remove();
                }
                iframe_ = iframe;

                if(iframe) {
                    try {
                        //检测待加载元素
                        await waitForLoadElement(iframe.contentDocument, 2000);

                        // 滑动iframe，触发懒加载的元素，主要是要点击下一页，因此这里的时间可以少一点，设置为2秒
                        await Iframe.scrollIframe(iframe, instance.clickElement);

                        // 2. 在iframe中找到并点击目标元素
                        const clickElement = await waitForElements(() => {
                            // 点击目标元素
                            const targetElement = DOMHelper.getElement(instance.clickElement, true, iframe.contentDocument);
                            return targetElement;
                        }, iframe.contentDocument, 1000);

                        if(clickElement) {
                            clickElement.click();

                            // 等待iframe.contentDocument的加载
                            try {
                                await Iframe.loadIframe(iframe);
                            } catch(e) {
                                console.error('Iframe load error:', e);
                            }

                            // 检测待加载元素
                            await waitForLoadElement(iframe.contentDocument, 2000);

                            // 滑动iframe，触发懒加载的元素
                            await Iframe.scrollIframe(iframe);

                            // 4. 获取新内容
                            const elements = await waitForPageElements(instance.pageElement, iframe.contentDocument);

                            // 更新链接
                            // 方法1: 检查URL变化
                            let newUrl = Iframe.getIframeUrl(iframe, instance.url);
                            if (newUrl) {
                                instance.url = newUrl;
                            }

                            // 获取标题
                            const title = extractArticleTitle(iframe.contentDocument);
                            // 4. 筛选真正的正文内容
                            const filteredElements = elements ? filterMainContentElements(elements, iframe.contentDocument) : null;
                            
                            if (filteredElements == null) {
                                filteredElements = elements;
                            }
                            
                            // 5. 追加新内容
                            if(filteredElements && filteredElements.length) {
                                _appendElements(filteredElements, title);
                            } else {
                                //没有更多数据了，不显示加载loading
                                instance.hasMoreData = false;

                                //监听后面是否会重新激活下一页逻辑
                                startDOMObserverAfterNoMoreData(iframe.contentDocument);
                            }
                        } else {
                            //没有更多数据了，不显示加载loading
                            instance.hasMoreData = false;

                            //click模式下，只有clickElement为空才加入监听，因为检测iframe过于麻烦
                            //监听后面是否会重新激活下一页逻辑
                            startDOMObserverAfterNoMoreData(iframe.contentDocument);
                        }
                    } catch(error) {
                        console.error('Click action error:', error);
                    } finally {
                        // 6. 清理iframe
                        // 不能移除ifame，否则会导致图片没有加载完成，只有空白图，例如pixiv
                        // if (!instance.debugEnabled) {
                        //    iframe_.remove();
                        // }

                        // 重置加载中状态
                        setLoading(false);
                    }
                } else {
                    //没有更多数据了，不显示加载loading
                    instance.hasMoreData = false;

                    setLoading(false);
                }
            }
        }

        /**
         * 获取最新网页的url
         */
        function findCurrentWebUrl() {
            if (pages && pages.length > 0) {
                let obj = pages[pages.length - 1];
                return obj.url || window.location.href;
            }

            if (instance.url) {
                return instance.url;
            }

            return window.location.href;
        }

        /**
         * 查找下一页URL
         */
        function findNextURL() {
            if (instance.append === 'none') {
                //none模式不需要关心这个逻辑, none模式下, 例如https://apkpure.com/tw/game_adventure
                return window.location.href;
            } else {
                //doc取document还是document_，取决于分页导航在哪里
                let link = DOMHelper.getElement(instance.nextLink, true, document_);
                if (!link) {
                    link = DOMHelper.getElement(instance.nextLink, true, document);
                }

                let nextUrl = link ? link.href : null;

                if (!nextUrl) {
                    nextUrl = findInputNextURL(link);
                }

                if (nextUrl) {
                    nextUrl = new URL(nextUrl, window.location.origin).href;

                    //https://www.macat.vip/%e5%ad%97%e4%bd%93
                    //会返回重复的下一页链接，排除这种情况
                    if (pages && pages.length > 0) {
                        let lastPage = pages[pages.length - 1];
                        if (lastPage && lastPage.url && lastPage.url === nextUrl) {
                            nextUrl = null;
                        }
                    }
                }

                return nextUrl;
            }
        }

        /**
         * 获取input元素的url，例如v2ex.com
         */
        function findInputNextURL(el) {
            // 1. 判断是否是 <input>
            if (!el || el.tagName !== "INPUT") {
                return null;
            }

            let url = null;

            // 2. 检查 onclick 事件
            const onclickAttr = el.getAttribute("onclick");
            if (onclickAttr && onclickAttr.includes("location.href")) {
                const match = onclickAttr.match(/location\.href\s+=\s+['"]([^'"]+)['"]/);
                if (match) url = match[1];
            }

            // 3. 检查 data-url 属性
            if (!url) url = el.getAttribute("data-url");

            // 4. 检查是否是 <form> 的提交按钮，并获取 form 的 action
            if (!url && el.type === "submit") {
                const form = el.closest("form");
                if (form) url = form.action;
            }

            // 5. 输出结果
            if (url) {
                const absoluteUrl = new URL(url, window.location.origin).href;
                return absoluteUrl;
            }

            return url;
        }

        /**
         * 监听后面是否会重新激活下一页逻辑,例如bing.com搜索
         * 注意click+ajax模式下，监听下一页的按钮在iframe.contentDocument中
         */
        function startDOMObserverAfterNoMoreData(doc = document_) {
            if (instance.hasMoreData) return;

            // 如果已经有观察器在运行，先停止它
            if (domObserver) {
                domObserver.disconnect();
            }

            // 创建新的观察器
            domObserver = new MutationObserver(async (mutations) => {
                //检查是否又有更多数据
                _checkHasMoreData(doc);
            });

            // 配置观察选项
            const config = {
                childList: true,    // 观察子节点变化
                subtree: true,      // 观察所有后代节点
                attributes: true,   // 观察属性变化
                attributeFilter: ['style', 'class', 'hidden'] // 只观察这些属性的变化
            };

            // 开始观察
            domObserver.observe(doc.body, config);

            // 2. 添加滚动事件监听
            window.addEventListener('scroll', () => {
                _checkScrollPosition(doc);
            });
        }

        /**
         * 检查滚动位置
         */
        function _checkScrollPosition(doc = document_) {
            if (!instance.enabled || instance.isLoading || instance.hasMoreData) return;

            const scrollHeight = Math.max(
                  doc.documentElement.scrollHeight,
                  doc.body.scrollHeight
            );
            const scrollTop = window.pageYOffset || doc.documentElement.scrollTop;
            const clientHeight = window.innerHeight || doc.documentElement.clientHeight;

            // 当滚动到距离底部 12% 的位置时触发
            const scrollThreshold = scrollHeight * 0.12;
            const remainingScroll = scrollHeight - (scrollTop + clientHeight);

            if (remainingScroll <= scrollThreshold) {
                //检查是否又有更多数据
                _checkHasMoreData(doc);
            }
        }


        //检查是否又有更多数据
        function _checkHasMoreData(doc = document_) {
            if (!instance.enabled || instance.isLoading || instance.hasMoreData) return;

            try {
                // 检查是否有新的"下一页"元素
                if (instance.action === 'next') {
                    let nextURL = findNextURL();

                    let _shouldAppend = nextURL != null;
                    if (instance.append === 'none') {
                        let nextElement = DOMHelper.getElement(instance.nextLink, true, doc);
                        _shouldAppend = DOMHelper.isElementHidden(nextElement) === false;
                    }

                    if (_shouldAppend) {
                        domObserver.disconnect();
                        domObserver = null;

                        _restartAction();
                    }
                } else if (instance.action === 'click') {
                    //click模式下，只监听clickElement为空的情况，iframe加载失败的情况不好判断
                    //v2.6.6, 注意click+ajax模式下，监听下一页的按钮在iframe.contentDocument中
                    let element = DOMHelper.getElement(instance.clickElement, false, doc);

                    if (element && domObserver) {
                        domObserver.disconnect();
                        domObserver = null;

                        _restartAction();
                    }
                }
            } catch (error) {
                console.error('DOM observer error:', error);
            }
        }

        function _restartAction() {
            //延时触发，留一点页面加载的时间
            setTimeout(async () => {
                // 找到新的下一页元素，重新启用自动拼页
                instance.hasMoreData = true;

                // 获取当前页面的元素
                const elements = await waitForPageElements(document_);
                if (elements && elements.length > 0) {
                    // 更新插入点
                    insert_ = ElementHelper.getInsertionPoint(elements, document_);
                }

                // 直接加载下一页
                performAction();
            }, 250);
        }

        /**
         * 添加新页面
         */
        async function append(url) {
            //更新url
            instance.url = url;

            switch(instance.append) {
                case 'element':
                    // 目前而言，next模式下的，只有element这种append方式是生效的
                    appendElement();
                    break;
            }
        }

        /**
         * 设置加载状态
         * @param {boolean} isLoading 是否正在加载
         */
        function setLoading(isLoading) {
            if (instance.isLoading === isLoading) return;

            instance.isLoading = isLoading;
        }

        /**
         * Element模式添加
         */
        async function appendElement() {
            try {
                setLoading(true);

                // 1. 获取下一页的文档
                const nextDocument = await getNextDocument();

                // 2. 等待待加载元素
                await waitForLoadElement(nextDocument);

                // 3. 获取下一页中符合pageElement选择器的所有元素
                const elements = await waitForPageElements(instance.pageElement, nextDocument);

                // 4. 筛选真正的正文内容
                const filteredElements = elements ? filterMainContentElements(elements, nextDocument) : null;
                
                if (filteredElements == null) {
                    filteredElements = elements;
                }
                
                // 5. 获取标题
                const title = extractArticleTitle(nextDocument);

                // 6. 追加新内容
                if(filteredElements && filteredElements.length) {
                    _appendElements(filteredElements, title);
                } else {
                    //没有更多数据了，不显示加载loading
                    instance.hasMoreData = false;

                    //监听后面是否会重新激活下一页逻辑
                    startDOMObserverAfterNoMoreData(document_);
                }

            } catch(error) {
                console.error('appendElement error:', error);
            } finally {
                setLoading(false);
            }
        }

        function _appendElements(elements, title) {
            // 2. 创建文档片段用于临时存储元素
            const fragment = document.createDocumentFragment();

            // 4. 将找到的元素添加到文档片段中
            elements.forEach(element => {
                // 使用adoptNode而不是cloneNode以保持事件监听器
                const adoptedNode = document.adoptNode(element);
                fragment.appendChild(adoptedNode);
            });

            // 5. 将文档片段插入到插入点之前
//            if(insert_ && insert_.parentNode) {
//                insert_.parentNode.insertBefore(fragment, insert_);
//            }
            let mainElement = DOMHelper.getElement("#id-page", true, document);

            // 添加标题
            if (title && title.length > 0) {
                const titleElement = document.createElement('h3');
                titleElement.className = 'reader-title';
                titleElement.textContent = title;
                mainElement.appendChild(titleElement);
            }

            // 添加内容
            const contentElement = document.createElement('div');
            contentElement.className = 'reader-content';
            contentElement.append(fragment);
            mainElement.appendChild(contentElement);

            // 6. 处理页面观察点
            const pageElement = getPageElementInList(elements);
            if(!pageElement) {
                const span = document.createElement('span');
                insert_.parentNode.insertBefore(span, insert_);
                elements.unshift(span);
            }

            // 7. 更新插入点位置
            insert_ = ElementHelper.getInsertionPoint(elements, document_);

            // 8. 完成添加
            appendFinally('element', pageElement, title);
        }

        /**
         * 筛选真正的正文内容元素
         * 参考 Readability.js 中的 adoptableArticle 方法原理
         * @param {Array} elements - 待筛选的元素数组
         * @param {Document} doc - 文档对象
         * @returns {Array} 筛选后的正文元素数组
         */
        function filterMainContentElements(elements, doc) {
            if (!elements || !elements.length) {
                return elements;
            }

            try {
                // 使用 ReaderArticleFinder 来检测和筛选内容
                const readerFinder = new ReaderArticleFinder(doc);

                // 获取文章节点作为参考
                const articleNode = readerFinder.articleNode();

                if (!articleNode) {
                    // 如果无法检测到文章节点，使用基础筛选
                    return filterElementsByBasicRules(elements, doc);
                }

                // 筛选出真正属于正文内容的元素
                const filteredElements = elements.filter(element => {
                    return isElementMainContent(element, articleNode, doc);
                });

                // 如果筛选后没有元素，返回原始元素（避免过度筛选）
                if (filteredElements.length === 0) {
                    return filterElementsByBasicRules(elements, doc);
                }

                // 清理每个筛选出的元素内部的非正文内容
                const cleanedElements = filteredElements.map(element => {
                    return cleanElementContent(element, doc);
                }).filter(Boolean); // 过滤掉清理后为空的元素

                return cleanedElements.length > 0 ? cleanedElements : filteredElements;

            } catch (error) {
                // 出错时使用基础筛选作为后备
                return filterElementsByBasicRules(elements, doc);
            }
        }

        /**
         * 判断元素是否为正文内容
         * @param {Element} element - 待判断的元素
         * @param {Element} articleNode - 文章节点
         * @param {Document} doc - 文档对象
         * @returns {boolean} 是否为正文内容
         */
        function isElementMainContent(element, articleNode, doc) {
            if (!element || !articleNode) {
                return false;
            }

            // 1. 检查元素是否在文章节点内部或与文章节点重叠
            if (articleNode.contains(element) || element.contains(articleNode)) {
                return true;
            }

            // 2. 检查元素是否与文章节点在同一层级且具有相似特征
            if (element.parentNode === articleNode.parentNode) {
                // 检查是否具有正文特征
                if (hasArticleCharacteristics(element)) {
                    return true;
                }
            }

            // 3. 计算元素的内容质量分数
            const contentScore = calculateContentScore(element);

            // 4. 如果分数足够高，认为是正文内容
            return contentScore >= 50; // 阈值可以根据需要调整
        }

        /**
         * 检查元素是否具有文章特征
         * @param {Element} element - 待检查的元素
         * @returns {boolean} 是否具有文章特征
         */
        function hasArticleCharacteristics(element) {
            if (!element) return false;

            // 正面特征的正则表达式
            const positiveRegex = /article|body|content|entry|hentry|page|pagination|post|text|main|primary/i;

            // 负面特征的正则表达式
            const negativeRegex = /advertisement|breadcrumb|combx|comment|contact|disqus|footer|link|meta|mod-conversations|promo|related|scroll|share|shoutbox|sidebar|social|sponsor|subscribe|tags|toolbox|widget|nav|header|menu|ad|banner/i;

            // 检查 class 和 id 属性
            const className = element.className || '';
            const id = element.getAttribute('id') || '';

            // 如果有明显的负面特征，排除
            if (negativeRegex.test(className) || negativeRegex.test(id)) {
                return false;
            }

            // 如果有正面特征，包含
            if (positiveRegex.test(className) || positiveRegex.test(id)) {
                return true;
            }

            // 检查标签名
            const tagName = element.tagName.toLowerCase();
            if (['article', 'main', 'section'].includes(tagName)) {
                return true;
            }

            return false;
        }

        /**
         * 计算元素的内容质量分数
         * @param {Element} element - 待计算的元素
         * @returns {number} 内容质量分数
         */
        function calculateContentScore(element) {
            if (!element) return 0;

            let score = 0;

            // 1. 文本内容长度分数
            const textContent = element.textContent || '';
            const textLength = textContent.trim().length;
            score += Math.min(textLength / 100, 50); // 最多50分

            // 2. 段落数量分数
            const paragraphs = element.querySelectorAll('p');
            score += Math.min(paragraphs.length * 5, 25); // 最多25分

            // 3. 链接密度惩罚
            const links = element.querySelectorAll('a');
            const linkTextLength = Array.from(links).reduce((sum, link) => {
                return sum + (link.textContent || '').length;
            }, 0);

            if (textLength > 0) {
                const linkDensity = linkTextLength / textLength;
                score -= linkDensity * 30; // 链接密度越高，扣分越多
            }

            // 4. 图片内容加分
            const images = element.querySelectorAll('img');
            score += Math.min(images.length * 3, 15); // 最多15分

            // 5. 标签特征分数
            if (hasArticleCharacteristics(element)) {
                score += 20;
            }

            // 6. 元素大小分数
            const rect = element.getBoundingClientRect();
            if (rect.height > 200) {
                score += 10;
            }

            return Math.max(0, score);
        }

        /**
         * 清理元素内容，移除非正文元素
         * 参考 Readability.js 的 cleanArticleNode 方法
         * @param {Element} element - 待清理的元素
         * @param {Document} doc - 文档对象
         * @returns {Element|null} 清理后的元素
         */
        function cleanElementContent(element, doc) {
            if (!element || !doc) {
                return null;
            }

            try {
                // 克隆元素以避免修改原始元素
                const cleanedElement = element.cloneNode(true);

                // 需要完全移除的标签
                const tagsToRemove = {
                    'SCRIPT': 1, 'STYLE': 1, 'LINK': 1, 'META': 1,
                    'NOSCRIPT': 1, 'FORM': 1, 'INPUT': 1, 'BUTTON': 1,
                    'SELECT': 1, 'TEXTAREA': 1, 'OPTION': 1
                };

                // 可能需要移除的容器标签
                const containerTags = {
                    'DIV': 1, 'SECTION': 1, 'ASIDE': 1, 'NAV': 1,
                    'HEADER': 1, 'FOOTER': 1, 'UL': 1, 'OL': 1
                };

                // 需要移除的属性（保留重要的结构属性）
                const attributesToRemove = /^(on\w+|data-\w+|aria-\w+)$/i;

                // 遍历所有子元素进行清理
                const walker = doc.createTreeWalker(
                    cleanedElement,
                    NodeFilter.SHOW_ELEMENT,
                    {
                        acceptNode: function(node) {
                            return NodeFilter.FILTER_ACCEPT;
                        }
                    }
                );

                const elementsToRemove = [];
                let currentNode;

                while (currentNode = walker.nextNode()) {
                    const tagName = currentNode.tagName;

                    // 1. 移除脚本、样式等标签
                    if (tagsToRemove[tagName]) {
                        elementsToRemove.push(currentNode);
                        continue;
                    }

                    // 2. 检查是否应该移除此元素
                    if (shouldRemoveElement(currentNode, cleanedElement)) {
                        elementsToRemove.push(currentNode);
                        continue;
                    }

                    // 3. 清理属性
                    cleanElementAttributes(currentNode, attributesToRemove);

                    // 4. 处理链接
                    if (tagName === 'A') {
                        cleanLinkElement(currentNode);
                    }

                    // 5. 处理图片
                    if (tagName === 'IMG') {
                        cleanImageElement(currentNode);
                    }

                    // 6. 处理容器元素
                    if (containerTags[tagName]) {
                        if (shouldRemoveContainer(currentNode)) {
                            elementsToRemove.push(currentNode);
                        }
                    }
                }

                // 移除标记的元素
                elementsToRemove.forEach(el => {
                    if (el.parentNode) {
                        el.parentNode.removeChild(el);
                    }
                });

                // 清理空的容器元素
                cleanEmptyContainers(cleanedElement);

                // 检查清理后是否还有有效内容
                if (!hasValidContent(cleanedElement)) {
                    return null;
                }

                return cleanedElement;

            } catch (error) {
                console.error('cleanElementContent error:', error);
                return element; // 出错时返回原始元素
            }
        }

        /**
         * 判断是否应该移除元素
         * @param {Element} element - 待判断的元素
         * @param {Element} rootElement - 根元素
         * @returns {boolean} 是否应该移除
         */
        function shouldRemoveElement(element, rootElement) {
            const tagName = element.tagName;
            const className = element.className || '';
            const id = element.getAttribute('id') || '';

            // 0. 保护重要的格式化元素，绝对不移除
            const protectedTags = {
                'P': 1, 'BR': 1,
                'STRONG': 1, 'B': 1, 'EM': 1, 'I': 1, 'U': 1, 'SPAN': 1,
                'BLOCKQUOTE': 1, 'PRE': 1, 'CODE': 1,
                'IMG': 1, 'VIDEO': 1, 'AUDIO': 1,
                'A': 1 // 链接需要特殊处理，但不在这里直接移除
            };

            if (protectedTags[tagName]) {
                return false;
            }

            // 1. 检查负面特征
            const negativeRegex = /advertisement|breadcrumb|combx|comment|contact|disqus|footer|link|meta|mod-conversations|promo|related|scroll|share|shoutbox|sidebar|social|sponsor|subscribe|tags|toolbox|widget|nav|header|menu|ad|banner|popup|modal/i;

            if (negativeRegex.test(className) || negativeRegex.test(id)) {
                return true;
            }

            // 2. 检查元素是否可见
            if (!isElementVisible(element)) {
                return true;
            }

            // 3. 对于容器元素，检查链接密度
            const containerTags = {'DIV': 1, 'SECTION': 1, 'ASIDE': 1, 'NAV': 1, 'HEADER': 1, 'FOOTER': 1};
            if (containerTags[tagName] && hasHighLinkDensity(element)) {
                return true;
            }

            // 4. 检查是否是空的容器
            if (isEmptyContainer(element)) {
                return true;
            }

            // 5. 检查特定标签的特殊规则
            if (tagName === 'UL' || tagName === 'OL') {
                return shouldRemoveList(element);
            }

            if (tagName === 'TABLE') {
                return shouldRemoveTable(element);
            }

            return false;
        }

        /**
         * 检查元素是否可见
         * @param {Element} element
         * @returns {boolean}
         */
        function isElementVisible(element) {
            if (!element || !element.offsetParent) {
                return false;
            }

            const style = getComputedStyle(element);
            return style.display !== 'none' &&
                   style.visibility !== 'hidden' &&
                   style.opacity !== '0';
        }

        /**
         * 检查元素是否有高链接密度
         * @param {Element} element
         * @returns {boolean}
         */
        function hasHighLinkDensity(element) {
            const textContent = element.textContent || '';
            const textLength = textContent.trim().length;

            if (textLength < 50) {
                return false;
            }

            const links = element.querySelectorAll('a');
            const linkTextLength = Array.from(links).reduce((sum, link) => {
                return sum + (link.textContent || '').length;
            }, 0);

            const linkDensity = linkTextLength / textLength;
            return linkDensity > 0.5; // 链接密度超过50%
        }

        /**
         * 检查是否是空容器
         * @param {Element} element
         * @returns {boolean}
         */
        function isEmptyContainer(element) {
            const textContent = (element.textContent || '').trim();
            if (textContent.length === 0) {
                // 检查是否有图片或其他媒体内容
                const mediaElements = element.querySelectorAll('img, video, audio, canvas, svg');
                return mediaElements.length === 0;
            }
            return false;
        }

        /**
         * 判断是否应该移除列表
         * @param {Element} listElement
         * @returns {boolean}
         */
        function shouldRemoveList(listElement) {
            const items = listElement.querySelectorAll('li');
            if (items.length === 0) {
                return true;
            }

            // 检查列表项中的链接密度
            let totalText = 0;
            let totalLinks = 0;

            items.forEach(item => {
                totalText += (item.textContent || '').length;
                const links = item.querySelectorAll('a');
                links.forEach(link => {
                    totalLinks += (link.textContent || '').length;
                });
            });

            if (totalText > 0 && totalLinks / totalText > 0.6) {
                return true; // 链接密度过高的列表
            }

            return false;
        }

        /**
         * 判断是否应该移除表格
         * @param {Element} tableElement
         * @returns {boolean}
         */
        function shouldRemoveTable(tableElement) {
            const cells = tableElement.querySelectorAll('td, th');
            if (cells.length === 0) {
                return true;
            }

            // 检查表格内容
            const totalText = (tableElement.textContent || '').trim().length;
            if (totalText < 50) {
                return true; // 内容太少的表格
            }

            return false;
        }

        /**
         * 清理元素属性
         * @param {Element} element
         * @param {RegExp} attributesToRemove
         */
        function cleanElementAttributes(element, attributesToRemove) {
            const attributes = Array.from(element.attributes);

            // 需要保留的重要属性
            const keepAttributes = {
                // 基本结构属性
                'href': 1, 'src': 1, 'alt': 1, 'title': 1,
                // 表格属性
                'colspan': 1, 'rowspan': 1,
                // 列表属性
                'type': 1, 'start': 1,
                // 媒体属性
                'width': 1, 'height': 1, 'controls': 1,
                // 语义属性
                'lang': 1, 'dir': 1
            };

            attributes.forEach(attr => {
                const attrName = attr.name.toLowerCase();

                // 保留重要属性
                if (keepAttributes[attrName]) {
                    return;
                }

                // 移除匹配的属性
                if (attributesToRemove.test(attr.name)) {
                    element.removeAttribute(attr.name);
                }

                // 移除特定的样式相关属性（但保留基本的class和id用于可能的样式）
                if (/^(style|onclick|onload|onmouseover|onmouseout)$/i.test(attrName)) {
                    element.removeAttribute(attr.name);
                }
            });
        }

        /**
         * 清理链接元素
         * @param {Element} linkElement
         */
        function cleanLinkElement(linkElement) {
            const href = linkElement.getAttribute('href');

            // 移除无效链接
            if (!href || href.startsWith('#') || href.startsWith('javascript:')) {
                // 将链接转换为span
                const span = linkElement.ownerDocument.createElement('span');
                span.innerHTML = linkElement.innerHTML;
                if (linkElement.parentNode) {
                    linkElement.parentNode.replaceChild(span, linkElement);
                }
            }
        }

        /**
         * 清理图片元素
         * @param {Element} imgElement
         */
        function cleanImageElement(imgElement) {
            // 移除装饰性属性
            const decorativeAttrs = ['border', 'hspace', 'vspace', 'align'];
            decorativeAttrs.forEach(attr => {
                imgElement.removeAttribute(attr);
            });

            // 检查图片尺寸，移除过小的图片
            const rect = imgElement.getBoundingClientRect();
            if (rect.width < 50 && rect.height < 50) {
                if (imgElement.parentNode) {
                    imgElement.parentNode.removeChild(imgElement);
                }
            }
        }

        /**
         * 判断是否应该移除容器
         * @param {Element} container
         * @returns {boolean}
         */
        function shouldRemoveContainer(container) {
            // 检查容器是否主要包含导航或广告内容
            const className = container.className || '';
            const id = container.getAttribute('id') || '';

            const navigationRegex = /nav|menu|breadcrumb|pagination|sidebar|widget|ad|banner|promo/i;

            if (navigationRegex.test(className) || navigationRegex.test(id)) {
                return true;
            }

            // 检查容器内容质量
            const textContent = (container.textContent || '').trim();
            if (textContent.length < 30) {
                return true;
            }

            return false;
        }

        /**
         * 清理空的容器元素
         * @param {Element} rootElement
         */
        function cleanEmptyContainers(rootElement) {
            const containers = rootElement.querySelectorAll('div, section, aside, header, footer, nav');

            // 从内到外清理空容器
            for (let i = containers.length - 1; i >= 0; i--) {
                const container = containers[i];

                // 检查容器是否真的为空（不包含任何有意义的内容）
                if (isTrulyEmptyContainer(container) && container.parentNode) {
                    container.parentNode.removeChild(container);
                }
            }
        }

        /**
         * 检查容器是否真的为空
         * @param {Element} container
         * @returns {boolean}
         */
        function isTrulyEmptyContainer(container) {
            // 检查文本内容
            const textContent = (container.textContent || '').trim();
            if (textContent.length > 0) {
                return false;
            }

            // 检查是否包含重要的子元素
            const importantElements = container.querySelectorAll('img, video, audio, canvas, svg, iframe, object, embed');
            if (importantElements.length > 0) {
                return false;
            }

            // 检查是否包含格式化元素（即使没有文本，也可能有结构意义）
            const formatElements = container.querySelectorAll('br, hr');
            if (formatElements.length > 0) {
                return false;
            }

            return true;
        }

        /**
         * 检查元素是否有有效内容
         * @param {Element} element
         * @returns {boolean}
         */
        function hasValidContent(element) {
            const textContent = (element.textContent || '').trim();
            if (textContent.length > 20) {
                return true;
            }

            // 检查是否有媒体内容
            const mediaElements = element.querySelectorAll('img, video, audio, canvas, svg');
            return mediaElements.length > 0;
        }

        /**
         * 基础规则筛选（后备方案）
         * @param {Array} elements - 待筛选的元素数组
         * @param {Document} doc - 文档对象
         * @returns {Array} 筛选后的元素数组
         */
        function filterElementsByBasicRules(elements, doc) {
            if (!elements || !elements.length) {
                return elements;
            }

            return elements.filter(element => {
                // 排除明显的非内容元素
                const negativeRegex = /nav|header|footer|sidebar|menu|banner|comment|social|share|related|promo/i;
                const className = element.className || '';
                const id = element.getAttribute('id') || '';

                if (negativeRegex.test(className) || negativeRegex.test(id)) {
                    return false;
                }

                // 排除太小的元素
                // next+element的方式，是没有大小的
//                const rect = element.getBoundingClientRect();
//                if (rect.height < 50 || rect.width < 100) {
//                    return false;
//                }

                // 排除文本内容太少的元素
                const textContent = (element.textContent || '').trim();
                if (textContent.length < 20) {
                    return false;
                }

                return true;
            });
        }

        /**
         * 从文档中提取文章标题
         * @param {Document} doc - 要提取标题的文档对象
         * @returns {string} 提取的标题
         */
        function extractArticleTitle(doc) {
            if (!doc) return '';

            try {
                // 1. 尝试从meta标签获取标题
                const getMetaTitle = (selector) => {
                    const metaEl = doc.querySelector(selector);
                    if (metaEl && metaEl.content) {
                        return metaEl.content.trim();
                    }
                    return null;
                };

                // 2. 获取各种可能的标题来源
                const docTitle = doc.title;
                const ogTitle = getMetaTitle("head meta[property='og:title']");
                const twitterTitle = getMetaTitle("head meta[name='twitter:title']");

                // 3. 定义不同优先级的选择器
                // 3.1 精确匹配选择器 - 最高优先级
                const exactSelectors = [
                    // 精确匹配class="title"的元素
                    "[class='title']",
                    "[id='title']",
                    "p[class='title']",
                    ".chapter-title",
                    ".article-title",
                    ".post-title",
                    ".entry-title"
                ].join(", ");
                
                // 3.2 高优先级选择器 - 次高优先级
                const highPrioritySelectors = [
                    "h2, h3, h4, h5, h6",
                    "[class='headline']",
                    "[class='article_title']",
                    "[class='chapter_title']",
                    "[class='chapterTitle']"
                ].join(", ");
                
                // 3.3 模糊匹配选择器 - 低优先级
                const fuzzySelectors = [
                    "[class*='title']:not([class*='sub']):not([class*='sub-']):not([class*='subtitle'])",
                    "[id*='title']:not([id*='sub']):not([id*='sub-']):not([id*='subtitle'])"
                ].join(", ");

                const h1Selectors = ["h1"];
                
                // 4. 按优先级查找元素
                const exactElements = doc.querySelectorAll(exactSelectors);
                const highPriorityElements = doc.querySelectorAll(highPrioritySelectors);
                const fuzzyElements = doc.querySelectorAll(fuzzySelectors);
                const h1Elements = doc.querySelectorAll(h1Selectors);
                
                // 5. 创建候选标题数组，包含元素和权重
                const candidates = [];
                
                // 添加精确匹配元素 (权重300)
                [...exactElements].forEach(el => {
                    if ( el.textContent.trim()) {
                        candidates.push({
                            element: el,
                            text: el.textContent.trim(),
                            weight: 300,
                        });
                    }
                });
                
                // 添加高优先级元素 (权重100)，并根据标签类型调整权重
                [...highPriorityElements].forEach(el => {
                    if (el.textContent.trim()) {
                        let tagWeight = 100;
                        
                        // 根据标签类型调整权重，h2优先于h1
                        const tagName = el.tagName.toLowerCase();
                        const className = el.className || '';
                        
                        if (tagName === 'h2') tagWeight += 10;
                        else if (tagName === 'h3') tagWeight += 15;
                        else if (tagName === 'h4') tagWeight += 20;
                        else if (tagName === 'h5') tagWeight += 25;
                        else if (tagName === 'h6' ||
                                 className === 'headline' ||
                                 className === 'article_title' ||
                                 className === 'chapter_title' ||
                                 className === 'chapterTitle') tagWeight += 30;
//                        else if (tagName === 'h1') tagWeight += 5;
                        
                        candidates.push({
                            element: el,
                            text: el.textContent.trim(),
                            weight: tagWeight
                        });
                    }
                });
                
                // 添加模糊匹配元素 (权重50)
                [...fuzzyElements].forEach(el => {
                    // 排除已经添加过的元素
                    const alreadyAdded = candidates.some(c => c.element === el);
                    if (!alreadyAdded && el.textContent.trim()) {
                        candidates.push({
                            element: el,
                            text: el.textContent.trim(),
                            weight: 50,
                        });
                    }
                });
                
                // h1，优先级最低
                [...h1Elements].forEach(el => {
                    // 排除已经添加过的元素
                    const alreadyAdded = candidates.some(c => c.element === el);
                    if (!alreadyAdded && el.textContent.trim()) {
                        candidates.push({
                            element: el,
                            text: el.textContent.trim(),
                            weight: 40,
                        });
                    }
                });
                
                // 8. 计算总分并排序
                candidates.forEach(candidate => {
                    candidate.totalScore = candidate.weight;
                });
                
                candidates.sort((a, b) => b.totalScore - a.totalScore);
                
                // 9. 返回得分最高的标题
                if (candidates.length > 0) {
                    return candidates[0].text;
                }
                
                // 10. 如果没有找到合适的元素，返回meta标题或文档标题
                if (ogTitle) return ogTitle;
                if (twitterTitle) return twitterTitle;
                if (docTitle) return docTitle;

                return '';
            } catch (e) {
                return doc.title || '';
            }
        }

        /**
         * 等待待加载页面元素加载
         * @returns {Promise} 返回找到的元素
         */
        async function waitForLoadElement(doc = document, timeout = 5000) {
            return new Promise(async (resolve, reject) => {
                if (instance.loadElement == null
                    || instance.loadElement.length == 0) {
                    resolve(null);
                    return;
                }

                let result = await waitForElements(() => {
                    const loadElement = DOMHelper.getElement(instance.loadElement, true, doc);
                    return loadElement;
                }, doc, timeout);

                resolve(result);
            });
        }

        /**
         * 等待nextLink页面元素加载
         * @returns {Promise} 返回找到的元素
         */
        async function waitForNextLink(doc = document, timeout = 1000) {
            return new Promise(async (resolve, reject) => {
                if (instance.nextLink == null
                    || instance.nextLink.length == 0) {
                    resolve(null);
                    return;
                }

                let result = await waitForElements(() => {
                    const nextURL = findNextURL();
                    return nextURL ? nextURL : null;
                }, doc, timeout);

                resolve(result);
            });
        }

        /**
         * 等待clickElement页面元素加载
         * @returns {Promise} 返回找到的元素
         */
        async function waitForClickElement(doc = document, timeout = 1000) {
            return new Promise(async (resolve, reject) => {
                if (instance.clickElement == null
                    || instance.clickElement.length == 0) {
                    resolve(null);
                    return;
                }

                let result = await waitForElements(() => {
                    const clickElement = DOMHelper.getElement(instance.clickElement, true, doc);
                    return clickElement;
                }, doc, timeout);

                resolve(result);
            });
        }

        /**
         * 等待页面元素加载
         * @returns {Promise} 返回找到的元素数组
         */
        async function waitForPageElements(pageElement, doc = document) {
            return new Promise(async (resolve, reject) => {
                let result = await waitForElements(() => {
                    const existingElements = ElementHelper.getPageElements(pageElement, doc);
                    return existingElements;
                }, doc, 1500);

                resolve(result);
            });
        }

        /**
         * 获取下一个文档
         */
        async function getNextDocument() {
            const response = await fetch(instance.url);
            const html = await response.text();
            const parser = new DOMParser();
            let nextDocument = parser.parseFromString(html, 'text/html');
            document_ = nextDocument.cloneNode(true);

            return nextDocument;
        }

        /**
         * 判断是否应该加载下一页
         */
        function shouldAppend() {
            return instance.enabled && !instance.isLoading;
        }

        /**
         * 添加完成后的处理
         * @param mode 添加模式(page/iframe/element/ajax/none)
         * @param element 新添加的页面元素
         */
        function appendFinally(mode, element, title) {
            console.log(`appendFinally - mode: ${mode}, element: ${element}`);

            // 如果没有成功添加元素,直接返回
            if(!element) {
                console.error('appendFinally - 添加页面失败: 没有有效的页面元素');
                setLoading(false);
                return;
            }

            // 创建新页面对象
            const newPage = {
                number: instance.totalPages + 1,
                url: instance.url,
                point: element,
                title: title
            };

            // 将新页面添加到pages数组
            pages.push(newPage);

            // 将新页面添加到IntersectionObserver监听
            if(intersectionObserver && element) {
                intersectionObserver.observe(element);
            }

            // 更新页面计数
            instance.totalPages++;

            // 使用setTimeout延迟重置loading状态
            setLoading(false);
        }

        /**
         * 准备第一页
         * 这个函数在start()时调用,用于初始化第一页的状态
         */
        async function prepareFirstPage() {
            console.log('prepareFirstPage');

            //防止performAction加载过快，例如google搜索刷新的时候，会跳到第2页
//            instance.lastPerformTime = Date.now();

            // 根据不同的append模式准备第一页
            switch(instance.append) {
                case 'element': {
                    // 检测loadElement
                    await waitForLoadElement(document_);

                    // 获取当前页面的元素
                    const elements = await waitForPageElements(instance.readerPageElement, document);

                    if(elements && elements.length > 0) {
                        // 设置插入点
                        insert_ = ElementHelper.getInsertionPoint(elements, document_);

                        // 获取页面元素作为观察点
                        const firstPageElement = getPageElementInList(elements);
                        const title = extractArticleTitle(document_);
                        if(firstPageElement) {
                            pages.push({
                                number: 1,
                                url: window.location.href,
                                point: firstPageElement,
                                title: title
                            });
                        }
                    }
                    break;
                }

                case 'ajax': {
                    // 检测loadElement
                    await waitForLoadElement(document_);
                    // 等待clickElement加载
                    await waitForClickElement(document_);

                    // AJAX模式下的第一页准备
                    let ajaxElements = await waitForPageElements(instance.readerPageElement, document);
                    if (!ajaxElements) {
                        //没有数据，有些网站加载时间特别长，需要等请加载完毕才行
                        //例如https://www.fshare.vn/folder/H7DXADP8PMOA?token=1739665894
                        //等待5秒
                        //等待clickElement加载
                        await waitForClickElement(document_, 2000);

                        //再次检测
                        ajaxElements = await waitForPageElements(instance.readerPageElement, document);
                    }

                    if(ajaxElements && ajaxElements.length > 0) {
                        insert_ = ElementHelper.getInsertionPoint(ajaxElements, document_);

                        const firstAjaxElement = getPageElementInList(ajaxElements);
                        const title = extractArticleTitle(document_);
                        if(firstAjaxElement) {
                            pages.push({
                                number: 1,
                                url: window.location.href,
                                point: firstAjaxElement,
                                title: title
                            });
                        }
                    }
                    break;
                }
            }

            // 添加一些底部边距,便于检测
            const marginBottom = document.createElement('div');
            marginBottom.style.marginBottom = '2rem';
            document.body.appendChild(marginBottom);

            addScrollDetect();
        }

        /**
         * 获取页面元素
         * 从elements数组中获取第一个合适的元素作为页面观察点
         */
        function getPageElementInList(elements) {
            if(!elements || elements.length === 0) return null;
            if(elements.length == 1) return elements[0];

            // 过滤出元素节点
            const elementNodes = elements.filter(e => e && e.nodeType === Node.ELEMENT_NODE);

            // 过滤出高度大于0的元素
            const heightNodes = elementNodes.filter(e => {
                const height = Math.max(
                                        e.clientHeight || 0,
                                        e.offsetHeight || 0,
                                        e.scrollHeight || 0
                                        );
                return height > 0;
            });

            // 过滤出position为static的元素
            const positionNode = heightNodes.filter(e => {
                const style = window.getComputedStyle(e);
                return style.position === 'static';
            })[0];

            // 优先返回position为static的元素,其次是有高度的元素,最后是第一个元素节点
            return positionNode || heightNodes[0] || elementNodes[0];
        }

        /**
         * 新建插入点元素
         */
        function createNewInsertElement() {
            // 如果没有下一个兄弟节点,创建一个文本节点作为插入点
            let insertElement = document.createTextNode(' ');
            return insertElement;
        }

        /**
         * 检测当前页面
         * 用于确定用户当前查看的是哪一页
         * @param element 当前intersecting的元素(仅在使用IntersectionObserver时提供)
         */
        function detectCurrentPage(element) {
            for(const page of pages) {
                // 两种检测方式:
                // 1. 如果提供了element(IntersectionObserver模式),检查是否是当前页的观察点
                // 2. 如果没提供element(其他模式),检查页面元素是否在视图中
                if(page && page.point && (element ? element === page.point : isScrolledIntoView(page.point))) {
                    if(instance.currentPage !== page.number) {
                        instance.currentPage = page.number;
                        console.log(`detectCurrentPage - 当前页面: ${page.number}, URL: ${page.url}`);

                        // 更新地址栏URL(可选)
                        if(instance.updateAddress && page.url && window.location.href !== page.url) {
                            window.history.replaceState(undefined, undefined, page.url);
                            //发送通知到native,更新记录
                            // 发送消息到readerHelper获取规则
                            window.webkit.messageHandlers.readerHelper.postMessage({
                                action: 'updateUrl'
                            });
                        }

                        // 更新页面标题(可选)
                        if(instance.updateTitle && page.title && document.title !== page.title) {
                            document.title = page.title;
                        }
                    }
                    break;
                }
            }
        }

        /**
         * 判断元素是否在视图中
         * @param element 要检查的元素
         * @returns {boolean} 是否在视图中
         */
        function isScrolledIntoView(element) {
            if(!element) return false;

            const rect = element.getBoundingClientRect();

            // 如果元素高度为0,忽略它
            if(rect.height <= 0) return false;

            // 根据元素高度判断可见性:
            // 1. 如果元素高度大于等于视窗高度,检查是否部分可见
            // 2. 如果元素高度小于视窗高度,检查是否完全可见
            if(Math.round(rect.height) >= window.innerHeight) {
                // 元素顶部在视图内且底部也在视图内
                return rect.top <= 1 && rect.bottom >= 0;
            } else {
                // 元素完全在视图内
                return rect.top >= -1 && rect.bottom <= window.innerHeight;
            }
        }
    }

    /**
     * 等待指定元素加载
     * @returns {Promise} 返回找到的结果，由fn决定返回的结果
     */
    async function waitForElements(fn, doc = document, timeout = 2500) {
        return new Promise((resolve, reject) => {
            // 首先检查是否已经存在元素
            let result = fn();
            if (result) {
                resolve(result);
                return;
            }

            // 设置超时
            const timer = setTimeout(() => {
                observer.disconnect();
                resolve(null);
            }, timeout);

            // 创建 MutationObserver 实例
            const observer = new MutationObserver((mutations, obs) => {
                let result = fn();
                if (result) {
                    clearTimeout(timer);
                    obs.disconnect();
                    resolve(result);
                }
            });

            // 配置观察选项
            const config = {
                childList: true,    // 观察目标子节点的变化
                subtree: true,      // 观察所有后代节点
                attributes: false,   // 不观察属性变化
                characterData: false // 不观察文本内容变化
            };

            // 开始观察
            observer.observe(doc.body, config);
        });
    }


    /**
     * 通用工具类
     */
    class UtilHelper {
        /**
         * 获取文档总高度
         */
        static getTotalHeight(doc) {
            //入参可能是iframe.contentDocument，和document不一样
            const html = doc.documentElement;
            const body = doc.body;

            let bodyClientHeight = 0;
            let bodyScrollHeight = 0;
            let bodyOffsetHeight = 0;

            try {
                bodyClientHeight = body.clientHeight;
                bodyScrollHeight = body.scrollHeight;
                bodyOffsetHeight = body.offsetHeight;
            } catch(e) {
                console.error(e);
            }

            return Math.max(
                            html.clientHeight,
                            html.scrollHeight,
                            html.offsetHeight,
                            bodyClientHeight,
                            bodyScrollHeight,
                            bodyOffsetHeight
                        );
        }
    }

    /**
     * 处理click action的逻辑，通过hidden iframe来获取数据
     */
    class Iframe {
        static IFRAME_STYLE = `
            position: fixed !important;
            top: 0 !important;
            right: 0 !important;
            width: 20% !important;
            height: 250px !important;
            display:block !important;
            visibility: hidden !important;
            opacity: 0 !important;
        `;

        // v2.7.4, opacity: 0 !important;修复在bing中也显示iframe的问题
        // 加载iframe
        static loadIframe(iframe, timeout = 2000) {
            return new Promise((resolve, reject) => {
                // 设置3秒超时
                const timer = setTimeout(() => {
                    //并不是所有的网站都需要重新刷新iframe
                    //包子漫画twmanga.com会刷新，
                    //https://anime1.me/不会刷新
                    resolve();
                }, timeout);

                iframe.onload = () => {
                    clearTimeout(timer);
                    resolve();
                };

                iframe.onerror = () => {
                    clearTimeout(timer);
                    reject('Iframe load failed');
                };
            });
        }

        // 创建并加载iframe
        static async createIframe(url, doc = document) {
            // 创建iframe元素
            const iframe = document.createElement('iframe');
            iframe.src = url;

            if (instance.debugEnabled) {
                iframe.style = IFRAME_DEBUG_STYLE;
            } else {
                iframe.style = this.IFRAME_STYLE;
            }

            iframe.scrolling = 'no';
            iframe.frameBorder = '0';

            // 插入iframe
            (doc.documentElement || doc.body).appendChild(iframe);

            // 等待iframe加载
            try {
                await this.loadIframe(iframe);
            } catch(e) {
                console.error('Iframe load error:', e);
            }

            // 检查iframe是否加载成功
            if(!iframe.contentDocument) {
                await new Promise(r => setTimeout(r, 1000));
                if(!iframe.contentDocument) {
                    console.error('Iframe content not available');
                    return null;
                }
            }

            return iframe;
        }

        // 获取当前iframe的url
        static getIframeUrl(iframe, currentUrl) {
            // 方法1: 检查URL变化
            let newUrl = iframe.contentWindow.location.href;

            // 方法2: 检查History API变化
            let stateUrl = null;
            const state = iframe.contentWindow.history.state;
            if(state && state.url) {
                stateUrl = state.url;
            }

            // 方法3: 检查新的a标签
            const currentLink = iframe.contentDocument.querySelector(
                                                                     'link[rel="canonical"], a.current-page'
                                                                     );
            let currentLinkUrl = null;
            if (currentLink && currentLink.href) {
                currentLinkUrl = currentLink.href;
            }

            if (newUrl && newUrl !== currentUrl) {
                return newUrl;
            } else if (stateUrl && stateUrl !== currentUrl) {
                return stateUrl;
            } else if (currentLinkUrl && currentLinkUrl !== currentUrl) {
                return currentLinkUrl;
            }

            return null;
        }

        /**
         * 如果是寻找clickElement模式，那么再每次停顿后都检查一次，加快速度
         * @param {HTMLIFrameElement} iframe iframe元素
         * @param {clickElement} 等待的元素，主要用来寻找点击的元素，寻找到了则直接返回
         */
        static checkClickElement(iframe, clickElement = undefined) {
            if (!clickElement) return false;

            let element = DOMHelper.getElement(clickElement, true, iframe.contentDocument);
            if (element) {
                instance.scrollIframe = false;
                console.log("scrollIframe, 找到点击节点clickElement")
                return true;
            }

            return false;
        }

        /**
         * 滚动iframe并等待内容加载
         * @param {HTMLIFrameElement} iframe iframe元素
         * @param {clickElement} 等待的元素，主要用来寻找点击的元素，寻找到了则直接返回
         * @param {number} timeout 超时时间，默认15s
         */
        static async scrollIframe(iframe, clickElement = undefined, timeout = 15000) {
            if (!iframe || !iframe.contentDocument) return;

            instance.scrollIframe = true;

            try {
                // 1.快速检查模式 - 如果有clickElement直接查找
                if (this.checkClickElement(iframe, clickElement)) {
                    return;
                }

                // 设置iframe样式
                iframe.style.setProperty("height", (Math.min(document.documentElement?.clientHeight, window.innerHeight) + "" || "500") + "px", "important");
                if (instance.debugEnabled) {
                    iframe.style.setProperty("visibility", "visible", "important");
                } else {
                    iframe.style.setProperty("visibility", "hidden", "important");
                }

                // 适配包子漫画
                // 等待500毫秒，如果有大图，先让大图加载，从而判断是否存在大图的情况
                await new Promise(r => setTimeout(r, 200));

                //v2.6.4 快速检查模式 - 如果有clickElement直接查找
                if (this.checkClickElement(iframe, clickElement)) {
                    return;
                }

                // 清除超时
                clearTimeout(instance.scrollIframeTimeout);
                instance.scrollIframeTimeout = setTimeout(() => {
                    instance.scrollIframe = false;
                }, timeout);

                // 快速模式 - 简化的滚动逻辑
                await this._scrollQuickMode(iframe, clickElement);
            } catch (e) {
                console.error('Error during iframe scroll:', e);
            } finally {
                // 重置iframe样式
                instance.scrollIframe = false;
            }
        }

        /**
         * 快速滚动模式 - 用于小图或无图场景
         */
        static async _scrollQuickMode(iframe, clickElement) {
            // 简化的滚动逻辑
            const doc = iframe.contentDocument;
            const win = iframe.contentWindow;

            // 只进行3次快速滚动
            const scrollPositions = [0, 0.5, 1].map(ratio =>
                Math.floor(UtilHelper.getTotalHeight(doc) * ratio)
            );

            for (const position of scrollPositions) {
                win.scrollTo({
                    top: position,
                    behavior: 'instant'
                });

                // 短暂等待以触发可能的懒加载
                await new Promise(r => setTimeout(r, 100));

                // 快速检查模式 - 如果有clickElement直接查找
                if (this.checkClickElement(iframe, clickElement)) {
                    return;
                }
            }
        }

        /**
         * 滚动iframe内容并等待加载
         * @param {mode} full或者element，滑动的模式
         */
        static async scrollIframeContent(iframe, mode) {
            await new Promise(r => setTimeout(r, 1));

            if (!iframe?.contentDocument || !iframe?.contentWindow) return;

            const doc = iframe.contentDocument;

            // 这种是处理漫画网站的逻辑
            // 添加滚动暂停时间,让页面有充足时间触发懒加载
            let SCROLL_PAUSE = Math.floor(Math.random() * (50 - 40 + 1)) + 40;  // 每次滚动后暂停50ms, [40,50]之间的整数
            let SCROLL_STEP = 100;   // 每次滚动100px,减小步长使滚动更平滑

            if (mode === 'full') {
                // 全页面滚动
                const height = UtilHelper.getTotalHeight(iframe.contentDocument);
                for(let i = 0; i < height && instance.scrollIframe; i += SCROLL_STEP) {
                    iframe.contentWindow.scrollTo({
                        top: i,
                        left: 0,
                        behavior: 'instant'
                    });

                    // 每次滚动后暂停50ms, [40,50]之间的整数
                    SCROLL_PAUSE = Math.floor(Math.random() * (50 - 40 + 1)) + 40;

                    // 每次滚动后暂停一下,给懒加载充足时间
                    await new Promise(r => setTimeout(r, SCROLL_PAUSE));

                    // 检查新出现的图片
                    const visibleImages = Array.from(doc.querySelectorAll('img')).filter(img => {
                        const rect = img.getBoundingClientRect();
                        return rect.top >= 0 && rect.bottom <= window.innerHeight;
                    });

                    // 如果当前视口中有未加载的图片,多等待一会
                    if(visibleImages.some(img => !img.complete)) {
                        await new Promise(r => setTimeout(r, SCROLL_PAUSE));
                    }
                }

                // 最后再滚动到底部确保触发所有懒加载
                iframe.contentWindow.scrollTo({
                    top: height,
                    left: 0,
                    behavior: 'instant'
                });
                await new Promise(r => setTimeout(r, SCROLL_PAUSE));

            } else {
                // 按元素滚动
                let elements = ElementHelper.getPageElements(instance.pageElement, doc);
                if (!elements?.length) {
                    elements = doc.querySelectorAll('body > *');
                }

                //v2.6.4如果是element模式，可以让加载时间减少
                SCROLL_PAUSE = 10; //减少到10毫秒

                // 如果只有一个包装元素且有子元素,则使用其子元素
                const targetElements = (elements.length === 1 && elements[0]?.children) ?
                                     elements[0].children : elements;

                for (const element of targetElements) {
                    if (!instance.scrollIframe) break;

                    if (element.ownerDocument === doc) {
                        // 滚动元素到视图中
                        // 不能使用scrollIntoView，因为它不在视图中，会强制document滑动，影响到用户的滑动行为
                        // 例如包子漫画
                        // 修改：使用 scrollTo 替代 scrollIntoView，只在 iframe 内部滚动
                        const elementRect = element.getBoundingClientRect();
                        const scrollTop = elementRect.top + doc.defaultView.scrollY;

                        doc.defaultView.scrollTo({
                            top: scrollTop,
                            left: 0,
                            behavior: 'instant'
                        });

                        // 等待元素中的图片加载
                        const elementImages = element.getElementsByTagName('img');
                        if(elementImages.length > 0) {
                            await new Promise(r => setTimeout(r, SCROLL_PAUSE));

                            // 如果元素中有未加载的图片,多等待一会
                            if(Array.from(elementImages).some(img => !img.complete)) {
                                await new Promise(r => setTimeout(r, SCROLL_PAUSE));
                            }
                        } else {
                            await new Promise(r => setTimeout(r, SCROLL_PAUSE));
                        }
                    }
                }
            }
        }
    }

    /**
     * Helper class for DOM node operations
     */
    class DOMHelper {
        /**
         * 区分xpath和selector
         */
        static detectSelectorType(selectorString, doc = document) {
            // XPath 的特征标识
            const xpathIndicators = [
                '//',           // 双斜杠（表示任意层级）
                '/',            // 单斜杠（表示直接子元素）
                '@',            // 属性选择
                '::',           // 轴标识符
                'contains(',    // contains 函数
                'text()',       // text 函数
                'id(',          // id 函数
                '[position()',  // position 函数
                '|',            // xpath 的或运算符
                '..',           // 父节点
                'descendant::', // 后代轴
                'ancestor::',   // 祖先轴
                'following::'   // 后续轴
            ];

            // CSS 选择器的特征标识
            const cssIndicators = [
                '#',            // id 选择器
                '.',            // class 选择器
                '>',            // 直接子元素
                '+',            // 相邻兄弟
                '~',            // 通用兄弟
                //':',            // 伪类（但不是::）
                '[src=',        // 属性等于
                '[href^=',      // 属性开头
                '[class*=',     // 属性包含
                '[id$=',        // 属性结尾
                ',',            // css 的或运算符
                'nth-child',    // nth-child 选择器
                ':not(',        // 否定伪类
                ':hover',       // 状态伪类
                ':first-child'  // 位置伪类
            ];

            // 检查 XPath 特征
            const hasXPathFeatures = xpathIndicators.some(indicator =>
                                                          selectorString.includes(indicator)
                                                          );

            // 检查 CSS 特征
            const hasCSSFeatures = cssIndicators.some(indicator =>
                                                      selectorString.includes(indicator)
                                                      );

            // 验证CSS选择器是否有效
            function _isValidCSSSelector(str) {
                try {
                    doc.querySelector(str);
                    return true;
                } catch (e) {
                    return false;
                }
            }

            if (hasXPathFeatures && !hasCSSFeatures) {
                return 'xpath';
            } else if (!hasXPathFeatures && hasCSSFeatures) {
                return 'selector';
            } else if (hasXPathFeatures && hasCSSFeatures) {
                // 如果同时包含两种特征，优先判断为更可能的类型
                return _isValidCSSSelector(selectorString) ? 'selector' : 'xpath';
            } else {
                // 如果是简单的元素选择器，默认认为是 CSS
                return _isValidCSSSelector(selectorString) ? 'selector' : 'xpath';
            }
        }

        /**
         * 判断元素是否隐藏
         * @param element, 元素节点
         */
        static isElementHidden(element) {
            if (!element) return true; // 防止 `null` 传入
            const style = getComputedStyle(element);

            return (
              element.hidden ||
              style.display === 'none' ||
              style.visibility === 'hidden' ||
              style.opacity === '0'
            );
        }


        /**
         * Gets a single element based on path
         * @param isFirst, 取第一个元素
         */
        static getElement(path, isFirst = true, doc = document) {
            if (!path || path.length === 0 || !doc) {
                return null;
            }

            let element;
            let isShadowRoot = doc instanceof ShadowRoot;

            try {
                // 检测是否为XPath
                const isXPath = DOMHelper.detectSelectorType(path, doc) === 'xpath';

                try {
                    // 先在当前document中查找
                    if (isXPath) {
                        if (!isFirst) {
                            const result = doc.evaluate(path, doc, null, XPathResult.ORDERED_NODE_SNAPSHOT_TYPE, null);
                            element = result?.snapshotLength > 0 ?
                            result.snapshotItem(result.snapshotLength - 1) : undefined;
                        } else {
                            element = doc.evaluate(path, doc, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null)
                            .singleNodeValue;
                        }
                    } else {
                        if (!isFirst) {
                            const elements = doc.querySelectorAll(path);
                            element = elements?.length > 0 ? elements[elements.length - 1] : undefined;
                        } else {
                            element = doc.querySelector(path);
                        }
                    }
                } catch (e) {
                    console.error(e);
                }

                if (!isShadowRoot) {
                    // 如果没找到,递归查找iframe和shadow DOM
                    if (!element) {
                        try {
                            // 查找所有iframe
                            const iframes = Array.from(doc.getElementsByTagName('iframe'));
                            for (const iframe of iframes) {
                                try {
                                    const iframeDoc = iframe.contentDocument;
                                    if (iframeDoc) {
                                        const result = this.getElement(path, isFirst, iframeDoc);
                                        if (result) {
                                            element = result;
                                            break;
                                        }
                                    }
                                } catch (e) {
                                    console.error(e);
                                }
                            }
                        } catch (e) {
                            console.error(e);
                        }

                        try {
                            // 查找所有shadow roots
                            // shadow不支持xpath，因为它的DOM是隔离的
                            if (!element && !isXPath) {
                                // 修改: 如果doc是shadowRoot,使用querySelectorAll获取元素
                                const allElements = doc.getElementsByTagName('*');
                                for (const el of allElements) {
                                    const shadowRoot = el.shadowRoot;
                                    if (shadowRoot) {
                                        const result = this.getElement(path, isFirst, shadowRoot);
                                        if (result) {
                                            element = result;
                                            break;
                                        }
                                    }
                                }
                            }
                        } catch (e) {
                            console.error(e);
                        }
                    }
                }

            } catch (e) {
                console.error(e);
            }

            return element;
        }

        /**
         * Gets multiple elements based on path
         */
        static getElements(path, doc = document) {
            if (!path || path.length === 0 || !doc) {
                return null;
            }

            let elements = [];
            let isShadowRoot = doc instanceof ShadowRoot;

            try {
                // 检测是否为XPath
                const isXPath = DOMHelper.detectSelectorType(path, doc) === 'xpath';

                try {
                    // 先在当前document中查找
                    if (isXPath) {
                        const result = doc.evaluate(path, doc, null, XPathResult.ORDERED_NODE_SNAPSHOT_TYPE, null);
                        if (result?.snapshotLength > 0) {
                            for (let i = 0; i < result.snapshotLength; i++) {
                                elements.push(result.snapshotItem(i));
                            }
                        }
                    } else {
                        elements = Array.from(doc.querySelectorAll(path));
                    }
                } catch (e) {
                    console.error(e);
                }

                if (!isShadowRoot) {
                    try {
                        // 递归查找iframe和shadow DOM中的元素
                        const iframes = Array.from(doc.getElementsByTagName('iframe'));
                        for (const iframe of iframes) {
                            try {
                                const iframeDoc = iframe.contentDocument;
                                if (iframeDoc) {
                                    const result = this.getElements(path, iframeDoc);
                                    if (result && result.length) {
                                        elements.push(...result);
                                    }
                                }
                            } catch (e) {
                                console.error(e);
                            }
                        }
                    } catch (e) {
                        console.error(e);
                    }

                    try {
                        // shadow不支持xpath，因为它的DOM是隔离的
                        if (!isXPath) {
                            // 修改: 如果doc是shadowRoot,使用querySelectorAll获取元素
                            const allElements = doc.getElementsByTagName('*');
                            for (const el of allElements) {
                                const shadowRoot = el.shadowRoot;
                                if (shadowRoot) {
                                    const result = this.getElements(path, shadowRoot);
                                    if (result && result.length) {
                                        elements.push(...result);
                                    }
                                }
                            }
                        }
                    } catch (e) {
                        console.error(e);
                    }
                }
            } catch (e) {
                console.error(e);
            }

            return elements && elements.length>0 ? elements : null;
        }

        /**
         * Gets parent element of a node
         */
        static getParentElement(node) {
            const elementClass = node.ownerDocument?.defaultView?.Element || Element;

            if (node.parentElement instanceof elementClass) {
                return node.parentElement;
            }

            const shadowRoot = DOMHelper.getParentShadowRoot(node);
            if (shadowRoot?.host) {
                return shadowRoot.host;
            }

            const iframe = DOMHelper.getParentIframe(node);
            return iframe || undefined;
        }

        /**
         * Gets first child element of a node
         */
        static getChildElement(node) {
            const elementClass = node.ownerDocument?.defaultView?.Element || Element;

            if (node.firstElementChild instanceof elementClass) {
                return node.firstElementChild;
            }

            const shadowRoot = node.shadowRoot;
            if (shadowRoot?.firstElementChild instanceof elementClass) {
                return shadowRoot.firstElementChild;
            }

            const iframeDoc = DOMHelper.getIframeDocument(node);
            return iframeDoc?.firstElementChild instanceof (iframeDoc?.defaultView?.Element || Element) ?
            iframeDoc.firstElementChild : undefined;
        }

        /**
         * Gets parent shadow root of a node
         */
        static getParentShadowRoot(node) {
            const root = node?.getRootNode();
            if (root && root instanceof node?.ownerDocument?.defaultView?.ShadowRoot) {
                return root;
            }
        }

        /**
         * Gets iframe document
         */
        static getIframeDocument(element) {
            let doc;
            if (element &&
                element.nodeName?.toUpperCase() === 'IFRAME' &&
                element.contentDocument &&
                element.contentDocument instanceof element.contentDocument.defaultView?.Document) {
                doc = element.contentDocument;
            }
            return doc;
        }

        /**
         * Gets parent iframe of a node
         */
        static getParentIframe(node) {
            return node?.ownerDocument?.defaultView?.frameElement;
        }

        /**
         * Gets ancestor contexts of a node
         */
        static getAncestorContexts(node) {
            const contexts = [];
            for (let i = 0; i < 10 && (node && node !== document); i++) {
                let parent, context;
                const shadowRoot = DOMHelper.getParentShadowRoot(node);

                if (shadowRoot) {
                    parent = shadowRoot.host;
                    context = 'shadow';
                } else {
                    const iframe = DOMHelper.getParentIframe(node);
                    if (iframe) {
                        parent = iframe;
                        context = 'iframe';
                    } else {
                        parent = document;
                        context = 'document';
                    }
                }

                contexts.unshift({
                    node: node,
                    context: context
                });
                node = parent;
            }
            return contexts;
        }

        /**
         * Gets nodes using TreeWalker
         */
        static getNodesByTreeWalker(root, nodeType) {
            const nodes = [];
            try {
                const walker = document.createTreeWalker(root, nodeType);
                while (walker.nextNode()) {
                    nodes.push(walker.currentNode);
                }
            } catch (e) {}
            return nodes;
        }

        /**
         * Gets element position
         */
        static getElementPosition(element) {
            let position = {
                top: 0,
                bottom: 0,
                left: 0,
                right: 0,
                rect: null
            };

            try {
                if (typeof element?.getBoundingClientRect === 'function') {
                    position.rect = element.getBoundingClientRect();
                    const scrollY = window.scrollY || document.documentElement.scrollTop;
                    position.top = position.rect.top + scrollY;
                    position.bottom = position.rect.bottom + scrollY;
                }
            } catch (e) {}

            return position;
        }
    }


    /**
     * Helper class for DOM element operations
     */
    class ElementHelper {
        /**
         * Gets the insertion point for new elements
         */
        static getInsertionPoint(elements, doc = document) {
            let insertionPoint;
            if (elements && elements.length > 0) {
                try {
                    const lastElement = elements[elements.length - 1];
                    if (lastElement.nextSibling) {
                        insertionPoint = lastElement.nextSibling;
                    } else {
                        insertionPoint = document.createTextNode(' ')
                        lastElement.parentNode.appendChild(insertionPoint);
                    }
                } catch (error) {
                    console.error(error);
                }
            }

            return insertionPoint;
        }

        /**
         * Gets page elements based on path and type
         */
        static getPageElements(path, doc) {
            if (!path) return null;

            return DOMHelper.getElements(path, doc);
        }

        /**
         * Removes elements matching the specified path
         */
        static removeElements(path, doc) {
            if (!doc || !path) {
                return;
            }

            const elements = ElementHelper.getPageElements(path, doc);
            for (const element of elements) {
                if (typeof element?.remove === 'function') {
                    element.remove();
                }
            }
        }

    }


    // 自动翻页结束

    /**
     * ReaderArticleFinder - 文章内容检测器
     * 参考 Readability.js 的实现，用于检测和筛选网页中的主要文章内容
     */
    class ReaderArticleFinder {
        constructor(contentDocument) {
            this.contentDocument = contentDocument;
            this.article = null;
            this.didSearchForArticleNode = false;
            this._cachedScrollY = 0;
            this._cachedScrollX = 0;
            this._elementsWithCachedBoundingRects = [];
        }

        /**
         * 获取文章节点
         * @param {boolean} fallback - 是否使用后备检测
         * @returns {Element|null} 文章节点
         */
        articleNode(fallback = false) {
            if (this.didSearchForArticleNode) {
                return this.article ? this.article.element : null;
            }

            // 1. 首先尝试白名单检测
            this.article = this.findArticleBySearchingWhitelist();

            // 2. 如果白名单失败，尝试全元素搜索
            if (!this.article) {
                this.article = this.findArticleBySearchingAllElements();
            }

            // 3. 如果仍然失败，尝试视觉检测
            if (!this.article) {
                this.article = this.findArticleByVisualExamination();
            }

            // 4. 如果需要后备方案且前面都失败了
            if (!this.article && fallback) {
                this.article = this.findArticleBySearchingAllElements(true);
            }

            this.didSearchForArticleNode = true;
            return this.article ? this.article.element : null;
        }

        /**
         * 通过白名单搜索文章
         * @returns {CandidateElement|null}
         */
        findArticleBySearchingWhitelist() {
            // 常见的文章容器选择器
            const trustedSelectors = [
                'article',
                '[role="main"]',
                '.article-content',
                '.post-content',
                '.entry-content',
                '.content-body',
                '.article-body',
                '.post-body',
                'main'
            ];

            for (const selector of trustedSelectors) {
                try {
                    const elements = this.contentDocument.querySelectorAll(selector);
                    if (elements.length === 1) {
                        return new CandidateElement(elements[0], this.contentDocument);
                    }
                } catch (e) {
                    continue;
                }
            }

            return null;
        }

        /**
         * 通过搜索所有元素来查找文章
         * @param {boolean} aggressive - 是否使用激进模式
         * @returns {CandidateElement|null}
         */
        findArticleBySearchingAllElements(aggressive = false) {
            const candidates = this.findCandidateElements();
            if (!candidates || !candidates.length) {
                return null;
            }

            // 找到得分最高的候选元素
            const bestCandidate = this.highestScoringCandidateFromCandidates(candidates);

            if (bestCandidate) {
                const score = bestCandidate.finalScore();
                const element = bestCandidate.element;

                // 降低分数阈值，特别是对于文章容器
                const className = element.className || '';
                const id = element.getAttribute('id') || '';
                const isArticleContainer = /article|content|body|post|entry|main|text/i.test(className) ||
                                         /article|content|body|post|entry|main|text/i.test(id);

                let threshold = aggressive ? 400 : 800; // 降低默认阈值

                // 对于明显的文章容器，进一步降低阈值
                if (isArticleContainer) {
                    threshold = aggressive ? 200 : 400;
                }

                if (score >= threshold) {
                    return bestCandidate;
                }
            }

            return null;
        }

        /**
         * 查找候选元素
         * @returns {Array<CandidateElement>}
         */
        findCandidateElements() {
            const candidates = [];
            const elements = this.contentDocument.getElementsByTagName('*');
            const maxTime = Date.now() + 1500; // 最多处理1.5秒

            // 忽略的标签
            const ignoredTags = {
                'A': 1, 'EMBED': 1, 'FORM': 1, 'HTML': 1, 'IFRAME': 1,
                'OBJECT': 1, 'OL': 1, 'OPTION': 1, 'SCRIPT': 1, 'STYLE': 1,
                'SVG': 1, 'UL': 1, 'LINK': 1, 'META': 1, 'HEAD': 1
            };

            for (let i = 0; i < elements.length; i++) {
                const element = elements[i];

                // 超时保护
                if (Date.now() > maxTime) {
                    break;
                }

                if (!ignoredTags[element.tagName]) {
                    const candidate = CandidateElement.candidateIfElementIsViable(element, this.contentDocument);
                    if (candidate) {
                        candidates.push(candidate);
                    }
                }
            }

            return candidates;
        }

        /**
         * 从候选元素中找到得分最高的
         * @param {Array<CandidateElement>} candidates
         * @returns {CandidateElement|null}
         */
        highestScoringCandidateFromCandidates(candidates) {
            if (!candidates || !candidates.length) {
                return null;
            }

            let bestCandidate = null;
            let bestScore = 0;

            // 记录所有候选元素的分数信息
            const candidateScores = [];

            for (const candidate of candidates) {
                const score = candidate.finalScore();
                const element = candidate.element;

                candidateScores.push({
                    tagName: element.tagName,
                    className: element.className,
                    id: element.id,
                    rawScore: candidate.rawScore,
                    multiplier: candidate.tagNameAndAttributesScoreMultiplier,
                    finalScore: score,
                    textLength: (element.textContent || '').trim().length
                });

                if (score > bestScore) {
                    bestScore = score;
                    bestCandidate = candidate;
                }
            }

            // 按分数排序并显示前5个
            candidateScores.sort((a, b) => b.finalScore - a.finalScore);

            return bestCandidate;
        }

        /**
         * 通过视觉检测查找文章
         * @returns {CandidateElement|null}
         */
        findArticleByVisualExamination() {
            // 测试点位置（相对于视窗）
            const testPoints = [
                [window.innerWidth / 2, 800],
                [window.innerWidth / 2, 600],
                [window.innerWidth / 4, 800],
                [window.innerWidth / 2, 400],
                [window.innerWidth / 2 - 128, 1100]
            ];

            for (const point of testPoints) {
                const elements = this.elementsFromPoint(point[0], point[1]);

                for (const element of elements) {
                    // 检查是否有明显的正面特征
                    if (this.hasVeryPositiveCharacteristics(element)) {
                        return new CandidateElement(element, this.contentDocument);
                    }

                    // 检查元素是否足够大且包含足够的文本
                    if (this.elementLooksLikeArticle(element)) {
                        const candidate = new CandidateElement(element, this.contentDocument);
                        if (candidate.finalScore() > 1000) {
                            return candidate;
                        }
                    }
                }
            }

            return null;
        }

        /**
         * 获取指定点位置的所有元素
         * @param {number} x
         * @param {number} y
         * @returns {Array<Element>}
         */
        elementsFromPoint(x, y) {
            const elements = [];
            let element = this.contentDocument.elementFromPoint(x, y);

            while (element && elements.indexOf(element) === -1) {
                elements.push(element);
                element.style.pointerEvents = 'none';
                element = this.contentDocument.elementFromPoint(x, y);
            }

            // 恢复 pointer-events
            elements.forEach(el => {
                el.style.pointerEvents = '';
            });

            return elements;
        }

        /**
         * 检查元素是否有明显的正面特征
         * @param {Element} element
         * @returns {boolean}
         */
        hasVeryPositiveCharacteristics(element) {
            const className = element.className || '';
            const id = element.getAttribute('id') || '';

            // 非常明确的正面特征
            const veryPositiveRegex = /instapaper_body|article-content|post-content|entry-content/i;

            return veryPositiveRegex.test(className) || veryPositiveRegex.test(id);
        }

        /**
         * 检查元素是否看起来像文章
         * @param {Element} element
         * @returns {boolean}
         */
        elementLooksLikeArticle(element) {
            const rect = element.getBoundingClientRect();

            // 尺寸检查
            if (rect.width < 280 || rect.height < 295 || rect.width * rect.height < 170000) {
                return false;
            }

            // 文本内容检查
            const textContent = element.textContent || '';
            if (textContent.trim().length < 500) {
                return false;
            }

            // 段落检查
            const paragraphs = element.querySelectorAll('p');
            if (paragraphs.length < 3) {
                return false;
            }

            return true;
        }
    }

    /**
     * CandidateElement - 候选元素类
     * 用于评估和计算元素的文章内容分数
     */
    class CandidateElement {
        constructor(element, contentDocument) {
            this.element = element;
            this.contentDocument = contentDocument;
            this.textNodes = this.usableTextNodesInElement(element);
            this.rawScore = this.calculateRawScore();
            this.tagNameAndAttributesScoreMultiplier = this.calculateElementTagNameAndAttributesScoreMultiplier();
            this.languageScoreMultiplier = 1; // 简化版本，固定为1
        }

        /**
         * 检查元素是否可行
         * @param {Element} element
         * @param {Document} contentDocument
         * @returns {CandidateElement|null}
         */
        static candidateIfElementIsViable(element, contentDocument) {
            // 首先检查元素是否可见
            if (!CandidateElement.isElementActuallyVisible(element)) {
                return null;
            }

            const rect = element.getBoundingClientRect();

            // 如果 getBoundingClientRect 返回的尺寸为 0，尝试使用其他方法获取尺寸
            let width = rect.width;
            let height = rect.height;

            // 添加调试信息
            const elementInfo = {
                tagName: element.tagName,
                className: element.className,
                id: element.id,
                rectWidth: rect.width,
                rectHeight: rect.height,
                offsetWidth: element.offsetWidth,
                offsetHeight: element.offsetHeight,
                scrollWidth: element.scrollWidth,
                scrollHeight: element.scrollHeight,
                textLength: (element.textContent || '').trim().length
            };

            if (width === 0 || height === 0) {
                console.log('PPBrowser Reader: 元素尺寸为0，尝试其他方法获取尺寸', elementInfo);

                // 尝试使用 offsetWidth/offsetHeight
                width = element.offsetWidth || 0;
                height = element.offsetHeight || 0;

                // 如果还是 0，尝试使用 scrollWidth/scrollHeight
                if (width === 0 || height === 0) {
                    width = element.scrollWidth || 0;
                    height = element.scrollHeight || 0;
                }

                // 如果还是 0，尝试计算内容尺寸
                if (width === 0 || height === 0) {
                    const computedStyle = getComputedStyle(element);
                    const textContent = element.textContent || '';

                    // 基于文本内容估算尺寸
                    if (textContent.trim().length > 100) {
                        // 估算文本内容的尺寸
                        const estimatedWidth = Math.min(window.innerWidth * 0.8, 800);
                        const estimatedHeight = Math.max(textContent.length / 50 * 20, 300);

                        width = width || estimatedWidth;
                        height = height || estimatedHeight;
                    }
                }
            }

            // 基本尺寸检查 - 降低最小要求以适应更多情况
            if (width < 200 || height < 200) {
                // 对于包含大量文本的元素，放宽高度要求
                const textContent = (element.textContent || '').trim();
                if (textContent.length < 500 || width < 200) {
                    return null;
                }
            }

            // 面积检查 - 降低最小面积要求
            if (width * height < 100000) {
                // 对于文本丰富的元素，放宽面积要求
                const textContent = (element.textContent || '').trim();
                if (textContent.length < 1000) {
                    return null;
                }
            }

            // 位置检查（不要太靠上）- 使用实际的 rect.top
            if (rect.top > 1300) {
                return null;
            }

            return new CandidateElement(element, contentDocument);
        }

        /**
         * 检查元素是否实际可见
         * @param {Element} element
         * @returns {boolean}
         */
        static isElementActuallyVisible(element) {
            if (!element) return false;

            // 检查元素本身的可见性
            const style = getComputedStyle(element);
            if (style.display === 'none' ||
                style.visibility === 'hidden' ||
                style.opacity === '0') {
                return false;
            }

            // 检查父元素的可见性
            let parent = element.parentElement;
            while (parent && parent !== document.body) {
                const parentStyle = getComputedStyle(parent);
                if (parentStyle.display === 'none' ||
                    parentStyle.visibility === 'hidden' ||
                    parentStyle.opacity === '0') {
                    return false;
                }
                parent = parent.parentElement;
            }

            // 检查元素是否有实际内容
            const textContent = (element.textContent || '').trim();
            const hasImages = element.getElementsByTagName('img').length > 0;
            const hasVideos = element.getElementsByTagName('video').length > 0;

            // 特殊处理：对于明显的文章内容容器，即使尺寸为0也认为是可见的
            const className = element.className || '';
            const id = element.getAttribute('id') || '';
            const isArticleContainer = /article|content|body|post|entry|main|text/i.test(className) ||
                                     /article|content|body|post|entry|main|text/i.test(id);

            if (isArticleContainer && textContent.length > 100) {
                return true;
            }

            return textContent.length > 20 || hasImages || hasVideos;
        }

        /**
         * 计算原始分数
         * @returns {number}
         */
        calculateRawScore() {
            let score = 0;
            for (const textNode of this.textNodes) {
                score += this.rawScoreForTextNode(textNode);
            }
            return score;
        }

        /**
         * 计算文本节点的原始分数
         * @param {Text} textNode
         * @returns {number}
         */
        rawScoreForTextNode(textNode) {
            if (!textNode || !textNode.nodeValue) {
                return 0;
            }

            const length = textNode.nodeValue.trim().length;
            if (length < 20) {
                return 0;
            }

            // 检查父元素是否可见 - 使用更宽松的检查
            const parent = textNode.parentElement;
            if (!parent || !this.isElementVisibleForScoring(parent)) {
                return 0;
            }

            // 计算深度惩罚
            let depthPenalty = 1;
            let currentElement = parent;
            while (currentElement && currentElement !== this.element) {
                depthPenalty -= 0.1;
                currentElement = currentElement.parentElement;
            }

            return Math.pow(length * Math.max(depthPenalty, 0.1), 1.25);
        }

        /**
         * 检查元素是否可见（用于评分，更宽松的检查）
         * @param {Element} element
         * @returns {boolean}
         */
        isElementVisibleForScoring(element) {
            if (!element) return false;

            // 只检查基本的CSS可见性，不检查尺寸
            const style = getComputedStyle(element);
            if (style.display === 'none' ||
                style.visibility === 'hidden' ||
                style.opacity === '0') {
                return false;
            }

            // 对于文章容器，即使尺寸为0也认为是可见的
            const className = element.className || '';
            const id = element.getAttribute('id') || '';
            const isArticleContainer = /article|content|body|post|entry|main|text/i.test(className) ||
                                     /article|content|body|post|entry|main|text/i.test(id);

            if (isArticleContainer) {
                return true;
            }

            // 检查元素是否有实际的尺寸
            const rect = element.getBoundingClientRect();
            if (rect.width === 0 && rect.height === 0) {
                // 尝试使用其他方法检查尺寸
                const hasSize = element.offsetWidth > 0 ||
                               element.offsetHeight > 0 ||
                               element.scrollWidth > 0 ||
                               element.scrollHeight > 0;

                // 如果没有尺寸但有文本内容，也认为是可见的
                if (!hasSize) {
                    const textContent = (element.textContent || '').trim();
                    return textContent.length > 50; // 有足够的文本内容就认为可见
                }
            }

            return true;
        }

        /**
         * 检查元素是否可见
         * @param {Element} element
         * @returns {boolean}
         */
        isElementVisible(element) {
            if (!element) return false;

            const style = getComputedStyle(element);
            if (style.display === 'none' ||
                style.visibility === 'hidden' ||
                style.opacity === '0') {
                return false;
            }

            // 检查元素是否有实际的尺寸
            const rect = element.getBoundingClientRect();
            if (rect.width === 0 && rect.height === 0) {
                // 尝试使用其他方法检查尺寸
                const hasSize = element.offsetWidth > 0 ||
                               element.offsetHeight > 0 ||
                               element.scrollWidth > 0 ||
                               element.scrollHeight > 0;
                if (!hasSize) {
                    return false;
                }
            }

            return true;
        }

        /**
         * 获取元素中可用的文本节点
         * @param {Element} element
         * @returns {Array<Text>}
         */
        usableTextNodesInElement(element) {
            const textNodes = [];
            const walker = this.contentDocument.createTreeWalker(
                element,
                NodeFilter.SHOW_TEXT,
                {
                    acceptNode: (node) => {
                        // 跳过空白文本节点
                        if (!/\S/.test(node.nodeValue)) {
                            return NodeFilter.FILTER_REJECT;
                        }

                        // 跳过脚本和样式中的文本
                        const parent = node.parentElement;
                        if (parent && ['SCRIPT', 'STYLE', 'NOSCRIPT'].includes(parent.tagName)) {
                            return NodeFilter.FILTER_REJECT;
                        }

                        return NodeFilter.FILTER_ACCEPT;
                    }
                }
            );

            let node;
            while (node = walker.nextNode()) {
                textNodes.push(node);
            }

            return textNodes;
        }

        /**
         * 计算元素标签名和属性的分数倍数
         * @returns {number}
         */
        calculateElementTagNameAndAttributesScoreMultiplier() {
            let multiplier = 1;

            // 检查元素及其父元素的特征
            let currentElement = this.element;
            let isRootElement = true; // 标记是否是根元素

            while (currentElement) {
                const id = currentElement.getAttribute('id') || '';
                const className = currentElement.className || '';

                // 正面特征 - 为根元素提供更高的加分
                const positiveBonus = isRootElement ? 1.0 : 0.5;
                if (/article|body|content|entry|hentry|page|pagination|post|text/i.test(id) ||
                    /article|body|content|entry|hentry|page|pagination|post|text/i.test(className)) {
                    multiplier += positiveBonus;

                    // 特别为 articlebody 这样的类名提供额外加分
                    if (/articlebody|article-body|post-body|content-body/i.test(className)) {
                        multiplier += isRootElement ? 1.5 : 0.75;
                    }
                }

                // 负面特征
                if (/comment|meta|footer|footnote|sidebar|nav|menu|ad|banner/i.test(id) ||
                    /comment|meta|footer|footnote|sidebar|nav|menu|ad|banner/i.test(className)) {
                    multiplier -= 0.75;
                }

                // 文章标签
                if (currentElement.tagName === 'ARTICLE') {
                    multiplier += isRootElement ? 1.0 : 0.5;
                }

                // 主要内容标签
                if (currentElement.tagName === 'MAIN') {
                    multiplier += isRootElement ? 0.8 : 0.4;
                }

                // 检查文本内容丰富度（仅对根元素）
                if (isRootElement) {
                    const textContent = (currentElement.textContent || '').trim();
                    if (textContent.length > 1000) {
                        multiplier += 0.5; // 文本内容丰富的元素额外加分
                    }
                    if (textContent.length > 3000) {
                        multiplier += 0.5; // 非常丰富的文本内容再加分
                    }
                }

                currentElement = currentElement.parentElement;
                isRootElement = false;

                // 只检查几层父元素
                if (multiplier < 0 || !currentElement || currentElement === this.contentDocument.body) {
                    break;
                }
            }

            // 确保最小倍数，避免分数为0
            return Math.max(0.5, multiplier);
        }

        /**
         * 计算基础分数
         * @returns {number}
         */
        basicScore() {
            return this.rawScore * this.tagNameAndAttributesScoreMultiplier;
        }

        /**
         * 计算最终分数
         * @returns {number}
         */
        finalScore() {
            return this.basicScore() * this.languageScoreMultiplier;
        }
    }

    // 工具函数：Base64解码为UTF-8文本
    function decodeBase64ToUTF8(base64String) {
        try {
            const binaryString = atob(base64String);
            const bytes = new Uint8Array(binaryString.length);
            for (let i = 0; i < binaryString.length; i++) {
                bytes[i] = binaryString.charCodeAt(i);
            }
            return new TextDecoder('utf-8').decode(bytes);
        } catch (e) {
            console.error('Base64 decoding error:', e);
            return '';
        }
    }

    // 创建Shadow DOM并设置样式
    function createReaderModeShadowDOM(content, title) {
        // 创建宿主元素
        const hostElement = document.createElement('div');
        hostElement.id = 'reader-mode-host';
        hostElement.style.position = 'fixed';
        hostElement.style.top = '0';
        hostElement.style.left = '0';
        hostElement.style.right = '0';
        hostElement.style.bottom = '0';
        hostElement.style.width = '100vw'; // 使用视口宽度单位
        hostElement.style.height = '100vh'; // 使用视口高度单位
        hostElement.style.zIndex = '2147483647';
        hostElement.style.backgroundColor = instance.styleSettings.backgroundColor;
        hostElement.style.overflow = 'auto';

        // 重要：添加以下样式以防止继承原页面的缩放和边距
        hostElement.style.transform = 'none';
        hostElement.style.transformOrigin = '0 0';
        hostElement.style.zoom = '1';
        hostElement.style.maxWidth = 'none';
        hostElement.style.minWidth = '0';
        hostElement.style.margin = '0';
        hostElement.style.padding = '0';
        hostElement.style.border = 'none';
        hostElement.style.outline = 'none';
        hostElement.style.boxSizing = 'border-box';

        // 添加到body
        document.body.appendChild(hostElement);

        // 创建Shadow DOM
        instance.shadowRoot = hostElement.attachShadow({ mode: 'open' });

        // 添加重置样式，确保阅读模式内容不受原页面样式影响
        const resetStyle = document.createElement('style');
        resetStyle.id = 'reader-reset-style'; // 添加ID
        resetStyle.textContent = `
            :host {
                all: initial;
                display: block !important;
                width: 100vw !important;
                height: 100vh !important;
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                right: 0 !important;
                bottom: 0 !important;
                margin: 0 !important;
                padding: 0 !important;
                border: none !important;
                outline: none !important;
                box-sizing: border-box !important;
                font-size: 20px !important; /* 基础字体大小 */
                line-height: 1.75 !important;
                color: ${instance.styleSettings.textColor} !important;
                background-color: ${instance.styleSettings.backgroundColor} !important;
                transform: none !important;
                zoom: 1 !important;
                overflow: auto !important;
            }

            /* 重置所有元素的基本样式 */
            * {
                box-sizing: border-box !important;
                margin: 0 !important;
                padding: 0 !important;
                font-family: ${instance.styleSettings.fontFamily} !important;
                max-width: none !important;
                min-width: 0 !important;
                transform: none !important;
                zoom: 1 !important;
                border: none !important;
                outline: none !important;
            }
        `;
        instance.shadowRoot.appendChild(resetStyle);

        // 添加主要样式
        const style = document.createElement('style');
        style.id = 'reader-main-style'; // 添加ID
        style.textContent = generateReaderModeCSS();
        instance.shadowRoot.appendChild(style);

        // 创建内容容器
        const container = document.createElement('div');
        container.id = 'id-page';
        container.className = 'reader-container';

        // 添加标题
        const titleElement = document.createElement('h3');
        titleElement.className = 'reader-title';
        titleElement.textContent = title || document.title;
        container.appendChild(titleElement);

        // 添加内容
        const contentElement = document.createElement('div');
        contentElement.className = 'reader-content';
        contentElement.innerHTML = content;
        container.appendChild(contentElement);

        // 添加到Shadow DOM
        instance.shadowRoot.appendChild(container);

        // 隐藏原始页面内容
        document.documentElement.classList.add('reader-mode-html');
        document.body.classList.add('reader-mode-body');

        // 设置阅读模式状态
        instance.readerMode = true;

        // 初始化轻点翻页
        initTapPageTurning();
        
        // 强制确保页面完全铺满屏幕
        forceFullScreenLayout();

        // 通知原生应用阅读模式已激活
        notifyNative('readerModeActivated', {});

        // 添加视口元标签以防止缩放
        ensureViewportMeta();
    }

    // 生成阅读模式的CSS
    function generateReaderModeCSS() {
        // 计算最佳内容宽度 - 使用屏幕宽度减去一些边距
        const screenWidth = window.innerWidth;
//        const contentWidth = screenWidth > 768 ? '90%' : '95%';
        const contentWidth = '100%';

        return `
            :host {
                --font-size: ${instance.styleSettings.fontSize};
                --title-font-size: ${instance.styleSettings.titleFontSize};
                --font-family: ${instance.styleSettings.fontFamily};
                --line-height: ${instance.styleSettings.lineHeight};
                --first-line-head-indent: ${instance.styleSettings.firstLineHeadIndent || '0em'};
                --horizontalMargin: ${instance.styleSettings.horizontalMargin || '20px'};
                --top-margin: ${instance.styleSettings.topMargin || '30px'};
                --bottom-margin: ${instance.styleSettings.bottomMargin || '20px'};
                --justified-alignment: ${instance.styleSettings.justifiedAlignment || 'left'};
                --text-color: ${instance.styleSettings.textColor};
                --background-color: ${instance.styleSettings.backgroundColor};
                --secondary-background: ${instance.styleSettings.secondaryBackgroundColor};
                --accent-color: ${instance.styleSettings.accentColor};
                --content-width: ${contentWidth};
                --spacing-unit: 1rem;
                --title-height: 60px; /* 标题区域的大概高度，简化后更紧凑 */

                /* iOS 安全区域变量 */
                --safe-area-inset-top: env(safe-area-inset-top, 0px);
                --safe-area-inset-right: env(safe-area-inset-right, 0px);
                --safe-area-inset-bottom: env(safe-area-inset-bottom, 0px);
                --safe-area-inset-left: env(safe-area-inset-left, 0px);

                /* 防止继承原页面的缩放 */
                font-size: ${instance.styleSettings.fontSize} !important;
                transform: none !important;
                zoom: 1 !important;
            }

            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
                max-width: none;
                min-width: 0;
            }

            .reader-container {
                font-family: var(--font-family);
                font-size: var(--font-size);
                line-height: var(--line-height);
                color: var(--text-color);
                background-color: var(--background-color);

                /* 使用安全区域来确保完全铺满屏幕 */
                padding-top: var(--safe-area-inset-top);
                padding-right: var(--safe-area-inset-right);
                padding-bottom: var(--safe-area-inset-bottom);
                padding-left: var(--safe-area-inset-left);

                width: 100vw; /* 使用视口宽度 */
                height: 100vh; /* 使用视口高度 */
                max-width: none; /* 移除最大宽度限制 */
                min-height: 100vh; /* 确保容器至少占满整个视口高度 */
                margin: 0; /* 移除边距 */
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
                text-rendering: optimizeLegibility;
                overflow-y: auto; /* 允许垂直滚动 */
                overflow-x: hidden; /* 隐藏水平滚动 */

                /* 确保不受原页面样式影响 */
                transform: none !important;
                zoom: 1 !important;
                box-sizing: border-box !important;
                position: relative !important;
            }

            .reader-title {
                font-size: var(--title-font-size);
                font-weight: 600;
                line-height: 1.3;
                color: var(--text-color);
                letter-spacing: -0.01em;
                width: 100vw !important;

                /* 使用配置的边距 */
                padding-top: var(--top-margin) !important;
                padding-left: var(--horizontalMargin) !important;
                padding-right: var(--horizontalMargin) !important;
                padding-bottom: 1rem !important;

                margin: 0 !important; /* 移除边距 */
                box-sizing: border-box !important;
                border-bottom: 1px solid rgba(0, 0, 0, 0.05);
                background-color: var(--background-color) !important;
                top: 0 !important;
                z-index: 1 !important;
            }

            .reader-content {
                width: 100vw !important; /* 使用视口宽度 */
                min-height: calc(100vh - var(--title-height, 80px) - var(--safe-area-inset-bottom)) !important; /* 减去标题高度和底部安全区域后铺满剩余高度 */

                /* 使用配置的边距，考虑安全区域 */
                padding-top: 1rem !important;
                padding-right: calc(var(--horizontalMargin) + var(--safe-area-inset-right)) !important;
                padding-bottom: calc(var(--bottom-margin) + var(--safe-area-inset-bottom)) !important;
                padding-left: calc(var(--horizontalMargin) + var(--safe-area-inset-left)) !important;

                margin: 0 !important; /* 移除边距 */
                box-sizing: border-box !important;

                /* 确保内容不受原页面样式影响 */
                font-size: var(--font-size) !important;
                line-height: var(--line-height) !important;
                background-color: var(--background-color) !important;
                color: var(--text-color) !important;
                overflow-wrap: break-word !important;
                word-wrap: break-word !important;

                /* 应用文本对齐方式 */
                text-align: var(--justified-alignment) !important;
            }

            .reader-content h1, .reader-content h2, .reader-content h3,
            .reader-content h4, .reader-content h5, .reader-content h6 {
                margin-top: calc(var(--spacing-unit) * 2);
                margin-bottom: var(--spacing-unit);
                line-height: 1.3;
                color: var(--text-color);
            }

            .reader-content h1 { font-size: 1.6rem !important; }
            .reader-content h2 { font-size: 1.4rem !important; }
            .reader-content h3 { font-size: 1.3rem !important; }
            .reader-content h4 { font-size: 1.2rem !important; }
            .reader-content h5 { font-size: 1.1rem !important; }
            .reader-content h6 { font-size: 1rem !important; }

            .reader-content a {
                color: var(--accent-color);
                text-decoration: none;
            }

            .reader-content a:hover {
                text-decoration: underline;
            }

            .reader-content img {
                max-width: 80%;
                height: auto;
                margin: calc(var(--spacing-unit) * 1.5) 0;
                border-radius: 4px;
            }

            .reader-content ul, .reader-content ol {
                margin: var(--spacing-unit) 0 calc(var(--spacing-unit) * 1.5) calc(var(--spacing-unit) * 2);
            }

            .reader-content li {
                margin-bottom: calc(var(--spacing-unit) * 0.5);
                font-size: var(--font-size) !important;
            }

            .reader-content blockquote {
                border-left: 4px solid var(--accent-color);
                padding-left: var(--spacing-unit);
                margin: calc(var(--spacing-unit) * 1.5) 0;
                color: rgba(var(--text-color-rgb, 51, 51, 51), 0.8);
            }

            .reader-content pre, .reader-content code {
                font-family: "SF Mono", Menlo, Monaco, Consolas, monospace;
                font-size: 0.9em;
                background-color: var(--secondary-background);
                border-radius: 4px;
            }

            .reader-content pre {
                padding: var(--spacing-unit);
                overflow-x: auto;
                margin: calc(var(--spacing-unit) * 1.5) 0;
            }

            .reader-content code {
                padding: 0.2em 0.4em;
            }

            /* 添加媒体查询以确保在不同设备上的一致性 */
            @media screen and (max-width: 768px) {
                .reader-title {
                    /* 移动设备上使用配置的边距，但稍微减少 */
                    padding-top: calc(var(--top-margin) * 0.8) !important;
                    padding-left: calc(var(--horizontalMargin) * 0.8) !important;
                    padding-right: calc(var(--horizontalMargin) * 0.8) !important;
                    padding-bottom: 1rem !important;
                }

                .reader-content {
                    /* 移动设备上使用配置的边距，但稍微减少，保持安全区域 */
                    padding-top: 1rem !important;
                    padding-right: calc(var(--horizontalMargin) * 0.8 + var(--safe-area-inset-right)) !important;
                    padding-bottom: calc(var(--bottom-margin) * 0.8 + var(--safe-area-inset-bottom)) !important;
                    padding-left: calc(var(--horizontalMargin) * 0.8 + var(--safe-area-inset-left)) !important;
                }
            }

            @media screen and (max-width: 480px) {
                .reader-title {
                    /* 小屏设备上进一步减少内边距 */
                    padding-top: calc(var(--top-margin) * 0.6) !important;
                    padding-left: calc(var(--horizontalMargin) * 0.6) !important;
                    padding-right: calc(var(--horizontalMargin) * 0.6) !important;
                    padding-bottom: 0.8rem !important;
                }

                .reader-content {
                    /* 小屏设备上进一步减少内边距，但保持安全区域 */
                    padding-top: 0.8rem !important;
                    padding-right: calc(var(--horizontalMargin) * 0.6 + var(--safe-area-inset-right)) !important;
                    padding-bottom: calc(var(--bottom-margin) * 0.6 + var(--safe-area-inset-bottom)) !important;
                    padding-left: calc(var(--horizontalMargin) * 0.6 + var(--safe-area-inset-left)) !important;
                }
            }
        `;
    }

    // 退出阅读模式
    function exitReaderMode() {
        if (!instance.readerMode) return;

        // 移除Shadow DOM宿主元素
        const hostElement = document.getElementById('reader-mode-host');
        if (hostElement) {
            document.body.removeChild(hostElement);
        }

        // 恢复原始页面内容
        document.documentElement.classList.remove('reader-mode-html');
        document.body.classList.remove('reader-mode-body');

        // 重置阅读模式状态
        instance.readerMode = false;
        instance.shadowRoot = null;

        // 通知原生应用阅读模式已退出
        notifyNative('readerModeDeactivated', {});
    }

    // 在Shadow DOM中通过ID查找元素的辅助函数
    function getElementByIdInShadow(shadowRoot, id) {
        if (!shadowRoot) return null;

        // 方法1：使用querySelector
        return shadowRoot.querySelector(`#${id}`);

        // 方法2：如果方法1不起作用，可以尝试遍历所有元素
        // const elements = shadowRoot.querySelectorAll('*');
        // for (const element of elements) {
        //     if (element.id === id) {
        //         return element;
        //     }
        // }
        // return null;
    }

    // 更新阅读模式样式
    function updateReaderModeStyle(styleSettings) {
        if (!instance.readerMode || !instance.shadowRoot) {
            return;
        }

        // 更新样式设置
        for (const key in styleSettings) {
            instance.styleSettings[key] = styleSettings[key];
        }

        // 获取新的CSS
        const newCSS = generateReaderModeCSS();

        try {
            // 通过ID查找并更新主样式元素
            const mainStyle = getElementByIdInShadow(instance.shadowRoot, 'reader-main-style');
            if (mainStyle) {
                mainStyle.textContent = newCSS;
            } else {
                // 回退方法：查找任何样式元素
                const styles = instance.shadowRoot.querySelectorAll('style');
                if (styles.length > 1) {
                    styles[1].textContent = newCSS;
                } else if (styles.length > 0) {
                    styles[0].textContent = newCSS;
                } else {
                    // 创建新的样式元素
                    const newStyle = document.createElement('style');
                    newStyle.id = 'reader-main-style';
                    newStyle.textContent = newCSS;
                    instance.shadowRoot.appendChild(newStyle);
                }
            }

            // 更新重置样式
            const resetStyle = getElementByIdInShadow(instance.shadowRoot, 'reader-reset-style');
            if (resetStyle) {
                resetStyle.textContent = `
                    :host {
                        all: initial;
                        display: block !important;
                        width: 100vw !important;
                        height: 100vh !important;
                        position: fixed !important;
                        top: 0 !important;
                        left: 0 !important;
                        right: 0 !important;
                        bottom: 0 !important;
                        margin: 0 !important;
                        padding: 0 !important;
                        border: none !important;
                        outline: none !important;
                        box-sizing: border-box !important;
                        font-size: 20px !important;
                        line-height: 1.75 !important;
                        color: ${instance.styleSettings.textColor} !important;
                        background-color: ${instance.styleSettings.backgroundColor} !important;
                        transform: none !important;
                        zoom: 1 !important;
                        overflow: auto !important;
                    }

                    /* 重置所有元素的基本样式 */
                    * {
                        box-sizing: border-box !important;
                        margin: 0 !important;
                        padding: 0 !important;
                        font-family: ${instance.styleSettings.fontFamily} !important;
                        max-width: none !important;
                        min-width: 0 !important;
                        transform: none !important;
                        zoom: 1 !important;
                        border: none !important;
                        outline: none !important;
                    }
                `;
                console.log('Updated reset style element');
            }

            // 直接更新宿主元素的背景色
            const hostElement = document.getElementById('reader-mode-host');
            if (hostElement) {
                hostElement.style.backgroundColor = instance.styleSettings.backgroundColor;
            }

            // 不能调用下面这段代码，否则会直接跳回最开始的位置
            // 强制重新渲染
//            const container = instance.shadowRoot.querySelector('.reader-container');
//            if (container) {
//                // 触发重排
//                container.style.display = 'none';
//                // 强制浏览器重新计算布局
//                void container.offsetHeight;
//                container.style.display = 'block';
//                console.log('Forced container reflow');
//            }
        } catch (error) {
            console.error('Error updating styles:', error);
        }

        // 通知原生应用样式已更新
        notifyNative('readerModeStyleUpdated', instance.styleSettings);
    }

    // 强制确保页面完全铺满屏幕
    function forceFullScreenLayout() {
        // 强制设置 html 和 body 的样式
        const html = document.documentElement;
        const body = document.body;

        // 直接设置内联样式，确保优先级最高
        html.style.setProperty('margin', '0', 'important');
        html.style.setProperty('padding', '0', 'important');
        html.style.setProperty('width', '100vw', 'important');
        html.style.setProperty('height', '100vh', 'important');
        html.style.setProperty('overflow', 'hidden', 'important');
        html.style.setProperty('position', 'fixed', 'important');
        html.style.setProperty('top', '0', 'important');
        html.style.setProperty('left', '0', 'important');
        html.style.setProperty('right', '0', 'important');
        html.style.setProperty('bottom', '0', 'important');

        body.style.setProperty('margin', '0', 'important');
        body.style.setProperty('padding', '0', 'important');
        body.style.setProperty('width', '100vw', 'important');
        body.style.setProperty('height', '100vh', 'important');
        body.style.setProperty('overflow', 'hidden', 'important');
        body.style.setProperty('position', 'fixed', 'important');
        body.style.setProperty('top', '0', 'important');
        body.style.setProperty('left', '0', 'important');
        body.style.setProperty('right', '0', 'important');
        body.style.setProperty('bottom', '0', 'important');

        // 确保 reader-mode-host 元素的样式
        const hostElement = document.getElementById('reader-mode-host');
        if (hostElement) {
            hostElement.style.setProperty('position', 'fixed', 'important');
            hostElement.style.setProperty('top', '0', 'important');
            hostElement.style.setProperty('left', '0', 'important');
            hostElement.style.setProperty('right', '0', 'important');
            hostElement.style.setProperty('bottom', '0', 'important');
            hostElement.style.setProperty('width', '100vw', 'important');
            hostElement.style.setProperty('height', '100vh', 'important');
            hostElement.style.setProperty('margin', '0', 'important');
            hostElement.style.setProperty('padding', '0', 'important');
            hostElement.style.setProperty('border', 'none', 'important');
            hostElement.style.setProperty('outline', 'none', 'important');
            hostElement.style.setProperty('box-sizing', 'border-box', 'important');
        }

        // 延迟执行，确保样式生效
        setTimeout(() => {
            if (hostElement) {
                hostElement.style.setProperty('width', '100vw', 'important');
                hostElement.style.setProperty('height', '100vh', 'important');
            }
        }, 100);
    }

    // 确保有正确的viewport meta标签
    function ensureViewportMeta() {
        let viewportMeta = document.querySelector('meta[name="viewport"]');

        if (!viewportMeta) {
            viewportMeta = document.createElement('meta');
            viewportMeta.name = 'viewport';
            document.head.appendChild(viewportMeta);
        }

        // 设置viewport以防止缩放，并支持安全区域
        viewportMeta.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover';
    }

    // 通知原生应用
    function notifyNative(type, data) {
        if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.readerHelper) {
            window.webkit.messageHandlers.readerHelper.postMessage({
                type: type,
                data: data
            });
        }
    }
    
    /**
     * 初始化轻点翻页功能
     */
    function initTapPageTurning() {
        // 移除可能存在的旧事件处理器
        if (instance.tapPageHandler) {
            if (instance.readerMode && instance.shadowRoot) {
                // 从 Shadow DOM 中移除事件监听
                const container = instance.shadowRoot.querySelector('.reader-container');
                if (container) {
                    container.removeEventListener('click', instance.tapPageHandler);
                }
            } else {
                // 从主文档中移除事件监听
                document.removeEventListener('click', instance.tapPageHandler);
            }
            instance.tapPageHandler = null;
        }
        
        // 如果启用了轻点翻页，添加事件监听
        if (instance.tapPageEnabled) {
            instance.tapPageHandler = function(event) {
                // 忽略表单元素、链接等可点击元素的点击
                if (event.target.tagName === 'A' ||
                    event.target.tagName === 'BUTTON' ||
                    event.target.tagName === 'INPUT' ||
                    event.target.tagName === 'TEXTAREA' ||
                    event.target.tagName === 'SELECT' ||
                    event.target.closest('a') ||
                    event.target.closest('button')) {
                    return;
                }
                
                // 获取点击位置
                const width = window.innerWidth;
                const clickX = event.clientX;
                
                // 获取正确的滚动容器
                let scrollContainer;
                if (instance.readerMode && instance.shadowRoot) {
                    // 在阅读模式下，使用 Shadow DOM 中的容器
                    scrollContainer = instance.shadowRoot.querySelector('.reader-container');
                    if (!scrollContainer) return;
                } else {
                    // 在普通模式下，使用 window
                    scrollContainer = window;
                }
                
                let scale = 0.98;
                // 点击屏幕左侧1/2区域向上翻页，右侧1/2区域向下翻页
                if (clickX < width / 2) {
                    // 向上翻页 - 滚动一屏高度
                    if (scrollContainer === window) {
                        scrollContainer.scrollBy({
                            top: -window.innerHeight * scale,
                            behavior: 'smooth'
                        });
                    } else {
                        // Shadow DOM 容器滚动
                        scrollContainer.scrollTop -= window.innerHeight * scale;
                    }
                } else {
                    // 向下翻页 - 滚动一屏高度
                    if (scrollContainer === window) {
                        scrollContainer.scrollBy({
                            top: window.innerHeight * scale,
                            behavior: 'smooth'
                        });
                    } else {
                        // Shadow DOM 容器滚动
                        scrollContainer.scrollTop += window.innerHeight * scale;
                    }
                }
                
                // 阻止事件冒泡和默认行为
                event.preventDefault();
                event.stopPropagation();
            };
            
            // 添加点击事件监听到正确的元素
            if (instance.readerMode && instance.shadowRoot) {
                // 在阅读模式下，添加到 Shadow DOM 容器
                const container = instance.shadowRoot.querySelector('.reader-container');
                if (container) {
                    container.addEventListener('click', instance.tapPageHandler);
                }
            } else {
                // 在普通模式下，添加到文档
                document.addEventListener('click', instance.tapPageHandler);
            }
        }
    }

    /**
     * 更新轻点翻页设置
     */
    function updateTapPageSetting(enabled) {
        instance.tapPageEnabled = enabled;
        initTapPageTurning();
    }

    // 从base64编码的字符串接收ReaderModel并激活阅读模式
    function updateReaderModelFromBase64(base64String) {
        if (!base64String) return;

        try {
            // 1. 解码base64字符串为UTF-8文本
            const decodedString = decodeBase64ToUTF8(base64String);

            // 2. 解析JSON字符串为对象
            const model = JSON.parse(decodedString);

            if (!model || !model.isReaderModeAvailable) return;

            instance.pageElement = model.pageElementXPath;
            //滑动返回第一页时，监听事件
            instance.readerPageElement = '.reader-content';
            instance.initalNextUrl = model.nextUrl;
            instance.initalCurrentUrl = model.currentUrl;

            // 更新样式设置
            Object.assign(instance.styleSettings, model.styleSettings);
            // 轻点翻页
            instance.tapPageEnabled = model.styleSettings.tapPageEnabled;
            
            // 4. 激活阅读模式
            createReaderModeShadowDOM(model.content, model.title);

            // 5. 自动拉取下一页数据
            detectActionMode(model);

            // 初始化轻点翻页
            initTapPageTurning();
            
            return;
        } catch (error) {
            console.error('Failed to parse base64 encoded ReaderModel:', error);
            return;
        }
    }

    /**
     * 决定当前的action+append模式
     */
    async function detectActionMode(model) {
        // 3. 设置自动翻页配置
        //69书吧特别适配
        let is69shuba = false;
        if (window.location.hostname.includes('69shuba.com')) {
            is69shuba = true;
        }
        
        if (!is69shuba && model.nextUrl && model.nextUrl.length > 0) {
            //有url，检查是否是next+element的模式
            const response = await fetch(model.nextUrl);
            const html = await response.text();
            const parser = new DOMParser();
            let nextDocument = parser.parseFromString(html, 'text/html');

            const pageElement = await waitForElements(() => {
                // 点击目标元素
                const targetElement = DOMHelper.getElement(model.pageElementXPath, true, nextDocument);
                return targetElement;
            }, nextDocument, 1000);

            if (pageElement) {
                //有值，next+element
                instance.action = 'next';
                instance.append = 'element';
                instance.nextLink = model.nextXPath;
                instance.initalNextUrl = model.nextUrl;
            } else {
                //没值，click+ajax
                instance.action = 'click';
                instance.append = 'ajax';
                instance.clickElement = model.nextXPath;
            }
        } else if (model.nextXPath) {
            //没有url，那么则是click+ajax的模式
            instance.action = 'click';
            instance.append = 'ajax';
            instance.clickElement = model.nextXPath;
        }

        // 5. 自动拉取下一页数据
        autoPageFunc();
    }

    // 更新阅读模式样式设置
    function updateReaderStyleSettings(base64String) {
        if (!base64String) return;

        try {
            // 解码base64字符串为UTF-8文本
            const decodedString = decodeBase64ToUTF8(base64String);

            // 解析JSON字符串为对象
            const styleSettings = JSON.parse(decodedString);

            // 更新样式
            updateReaderModeStyle(styleSettings);

            return;
        } catch (error) {
            console.error('Failed to parse base64 encoded style settings:', error);
            return;
        }
    }


    // 添加全局样式
    function addGlobalStyles() {
        const style = document.createElement('style');
        style.textContent = `
            /* 完全重置 html 和 body 元素，确保铺满整个屏幕 */
            html.reader-mode-html, body.reader-mode-body {
                overflow: hidden !important;
                margin: 0 !important;
                padding: 0 !important;
                width: 100vw !important;
                height: 100vh !important;
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                right: 0 !important;
                bottom: 0 !important;
                transform: none !important;
                zoom: 1 !important;
                max-width: none !important;
                min-width: 0 !important;
                box-sizing: border-box !important;
            }

            /* 隐藏原页面的所有直接子元素，除了 reader-mode-host */
            body.reader-mode-body > *:not(#reader-mode-host) {
                display: none !important;
                visibility: hidden !important;
            }

            #reader-mode-host {
                width: 100vw !important;
                height: 100vh !important;
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                right: 0 !important;
                bottom: 0 !important;
                z-index: 2147483647 !important;
                transform: none !important;
                zoom: 1 !important;
                max-width: none !important;
                min-width: 0 !important;
                box-sizing: border-box !important;
                margin: 0 !important;
                padding: 0 !important;
                border: none !important;
                outline: none !important;

                /* 确保覆盖安全区域 */
                padding-top: env(safe-area-inset-top, 0px) !important;
                padding-right: env(safe-area-inset-right, 0px) !important;
                padding-bottom: env(safe-area-inset-bottom, 0px) !important;
                padding-left: env(safe-area-inset-left, 0px) !important;
            }
        `;
        document.head.appendChild(style);
    }

    // 初始化
    function initialize() {
        // 添加全局样式
        addGlobalStyles();

        // 检查属性是否已存在
        if (!window.__firefox__.hasOwnProperty('readerHelper')) {
            Object.defineProperty(window.__firefox__, 'readerHelper', {
                enumerable: false,
                configurable: false,
                writable: false,
                value: Object.freeze({
                    updateReaderModelFromBase64: updateReaderModelFromBase64,
                    updateReaderStyleSettings: updateReaderStyleSettings,
                    exitReaderMode: exitReaderMode,
                    updateTapPageSetting: updateTapPageSetting
                })
            });
        }
    }

    // 初始化
    initialize();
} ();
