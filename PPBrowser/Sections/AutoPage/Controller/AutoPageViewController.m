//
//  AutoPageViewController.m
//  PPBrowser
//
//  Created by qingbin on 2025/2/20.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "AutoPageViewController.h"

#import "Masonry.h"
#import "MaizyHeader.h"
#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "ReactiveCocoa.h"
#import "UIView+FrameHelper.h"
#import "UIViewController+Helper.h"
#import "NSObject+Helper.h"

#import "SettingSwitchView.h"
#import "SettingArrowView.h"
#import "SettingTextView.h"
#import "SettingSegmentView.h"
#import "SettingSwitchAndTextView.h"
#import "PaddingNewLabel.h"

#import "PPNotifications.h"
#import "VIPController.h"
#import "BaseNavigationController.h"
#import "BrowserUtils.h"

#import "BrowserHelper.h"
#import "AppDelegate.h"

#import "PaymentManager.h"
#import "PPEnums.h"
#import "AutoPageBlackListModel.h"
#import "DatabaseUnit+AutoPageBlackList.h"

#import "Tab.h"
#import "URIFixup.h"
#import "InternalURL.h"

#import "AutoPageManager.h"
#import "AutoPageBlackListModel.h"

#import "AutoPageSettingViewController.h"
#import "AutoPageManualMarkViewController.h"
#import "CommonDataManager.h"

@interface AutoPageViewController () <ThemeProtocol>
//
@property (nonatomic, strong) UIStackView* stackView;
//开启自动翻页
@property (nonatomic, strong) SettingSegmentView *autoPageSegment;
//加入黑名单
@property (nonatomic, strong) SettingSwitchAndTextView *blacklistSwitch;
//手动标记
@property (nonatomic, strong) SettingArrowView *manualMarkArrow;
//相关信息按钮
@property (nonatomic, strong) UIButton *infoButton;
//设置按钮
@property (nonatomic, strong) UIButton *settingsButton;

// 添加分组视图
@property (nonatomic, strong) UIView *groupView;
//当前对应的tab
@property (nonatomic, weak) Tab *tab;
//当前对应的黑名单model
@property (nonatomic, strong) AutoPageBlackListModel *blackListModel;

@end

@implementation AutoPageViewController

- (instancetype)initWithTab:(Tab*)tab
{
    self = [super init];
    if(self) {
        self.tab = tab;
    }
    
    return self;
}

- (void)viewDidLoad
{
    [super viewDidLoad];
    
    self.title = NSLocalizedString(@"autopage.title", nil);
//    self.view.backgroundColor = [UIColor systemGroupedBackgroundColor];
    [self applyTheme];
    
    [self setupNavigationBar];
    [self addSubviews];
    [self defineLayout];
    [self bindData];
    
    [self createCustomLeftBarButtonItem];
}

- (void)setupNavigationBar {
//    self.navigationItem.leftBarButtonItem = [[UIBarButtonItem alloc] initWithCustomView:self.infoButton];
    self.navigationItem.rightBarButtonItem = [[UIBarButtonItem alloc] initWithCustomView:self.settingsButton];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if (isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.stackView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColorLine];
        self.groupView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
//        self.infoButton.tintColor = [UIColor colorWithHexString:@"#999999"];
//        self.settingsButton.tintColor = [UIColor colorWithHexString:@"#999999"];
    } else {
        self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.stackView.backgroundColor = [UIColor colorWithHexString:@"#eeeeee"];
        self.groupView.backgroundColor = UIColor.whiteColor;
//        self.infoButton.tintColor = [UIColor systemGrayColor];
//        self.settingsButton.tintColor = [UIColor systemGrayColor];
    }
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    //只有当前的controller会触发一次, 其它已存在的controller走通知
    if(needReload) {
        //修改暗黑模式
        [self applyTheme];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}

#pragma mark - 数据绑定

- (void)bindData
{
    // 绑定自动翻页开关事件
    @weakify(self);
    [self.autoPageSegment setSelectIndexBlock:^(int index) {
        @strongify(self)
        [self handleAutoPageStatusChange:index];
    }];
    
    // 绑定黑名单开关事件
    [self.blacklistSwitch setDidSwithAction:^(BOOL isOn) {
        @strongify(self)
        [self handleBlacklistStatusChange:isOn];
    }];
    
    // 绑定手动标记点击事件
    [self.manualMarkArrow setDidAction:^{
        @strongify(self)
        [self handleManualMarkTap];
    }];
    
    //设置页
    [[self.settingsButton rac_signalForControlEvents:UIControlEventTouchUpInside]
        subscribeNext:^(id x) {
        @strongify(self)
        [self handleOpenSettings];
    }];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(darkThemeDidChangeNotification:)
                                                 name:kDarkThemeDidChangeNotification
                                               object:nil];
                                               
    // 初始化开关状态
    [self updateSwitchStatus];
}

#pragma mark - Actions

- (void)handleOpenSettings
{
    AutoPageSettingViewController* vc = [AutoPageSettingViewController new];
    BaseNavigationController* navc = [[BaseNavigationController alloc]initWithRootViewController:vc];
//    if ([BrowserUtils isiPad]) {
//        // iPad
//        navc.modalPresentationStyle = UIModalPresentationFormSheet;
//        vc.preferredContentSize = CGSizeMake(kScreenWidth-90, kScreenHeight-90);
//        
//        [self presentViewController:navc animated:YES completion:nil];
//    } else {
//        // iPhone
//        [self presentViewController:navc animated:YES completion:nil];
//    }
    //v2.6.8,统一present
    [self presentCustomToViewController:navc];
}

- (void)updateSwitchStatus {
    // 更新自动翻页开关状态
    BOOL isAutoPage = [[PreferenceManager shareInstance].items.isAutoPage boolValue];
    [self.autoPageSegment updateWithSelectIndex:isAutoPage ? 0 : 1];
    
    // 更新黑名单开关状态
    NSString *currentUrl = self.tab.webView.URL.absoluteString;
    if (currentUrl.length > 0) {
        AutoPageBlackListModel *blackListModel = [[AutoPageManager sharedInstance] isURLInBlackList:currentUrl];
        [self.blacklistSwitch updateWithIsOn:blackListModel!=nil];
        
        self.blackListModel = blackListModel;
    }
}

- (void)handleAutoPageStatusChange:(int)index {
    BOOL isOn = (index == 0);
    [PreferenceManager shareInstance].items.isAutoPage = @(isOn);
    [[PreferenceManager shareInstance] encode];
    
    //不刷新网页，而是通过enabled来控制，这样用户交互好一点
    //逻辑过于复杂，还是通过刷新网页来控制开关吧
    [[NSNotificationCenter defaultCenter] postNotificationName:kReloadUserScriptNotification object:nil];
}

- (void)handleBlacklistStatusChange:(BOOL)isOn {
    //会员检测
    BOOL isVip = [VIPController checkIsVipWithMessage:NSLocalizedString(@"vip.alert.autopage.text", nil) controller:self];
    if(!isVip) {
        [self.blacklistSwitch updateWithIsOn:NO];
        return;
    }
    
    NSString *currentUrl = self.tab.webView.URL.absoluteString;
    if (currentUrl.length == 0) {
        [self.blacklistSwitch updateWithIsOn:NO];
        return;
    }
    
    if ([InternalURL isValid:[NSURL URLWithString:currentUrl]]) {
        [UIView showToast:NSLocalizedString(@"autopage.disable.site", nil)];
        [self.blacklistSwitch updateWithIsOn:NO];
        return;
    }
    
    @weakify(self)
    if (isOn) {
        // 添加到黑名单
        AutoPageBlackListModel *model = [[AutoPageBlackListModel alloc] init];
        model.uuid = [NSUUID UUID].UUIDString;
        model.sourceUrl = currentUrl;
        model.patternUrl = [AutoPageManager generatePatternUrl:currentUrl];
        
        self.blackListModel = model;
        
        DatabaseUnit* unit = [DatabaseUnit insertOrUpdateBlackList:model];
        [unit setCompleteBlock:^(id result, BOOL success) {
            @strongify(self)
            if (success) {
                // 禁用当前页面的自动翻页
                [self disableAutoPageForCurrentPage];
            }
        }];
        DB_EXEC(unit);
    } else {
        // 从黑名单移除
        DatabaseUnit* removeUnit = [DatabaseUnit removeBlackListWithId:self.blackListModel.uuid];
        [removeUnit setCompleteBlock:^(id result, BOOL success) {
            if (success) {
                //刷新网页
                [[NSNotificationCenter defaultCenter] postNotificationName:kReloadUserScriptNotification object:nil];
            }
        }];
        DB_EXEC(removeUnit);
    }
}

#pragma mark - Helper Methods

- (void)disableAutoPageForCurrentPage {
    // 通过JS禁用当前页面的自动翻页
    NSMutableDictionary *instance = [NSMutableDictionary dictionary];
    instance[@"enabled"] = @(false);
    
    // 使用UTF-8编码进行JSON序列化
    NSError *error;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:instance options:0 error:&error];
    NSString *jsonString = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
    
    NSString *script = [NSString stringWithFormat:@"window.updateAutoPageEnabledFunc && window.updateAutoPageEnabledFunc('%@');", jsonString];
    [self.tab.webView evaluateJavaScript:script completionHandler:nil];
}

#pragma mark - Event Handlers

- (void)handleManualMarkTap {
    // 处理手动标记点击
    AutoPageManualMarkViewController* vc = [[AutoPageManualMarkViewController alloc]initWithTab:self.tab];
    [self.navigationController pushViewController:vc animated:YES];
    
    [CommonDataManager shareInstance].markAutoPageViewController = vc;
}

#pragma mark - Layout

- (void)addSubviews {
    [self.view addSubview:self.groupView];
    [self.groupView addSubview:self.stackView];
}

- (void)defineLayout {
    float margin = iPadValue(30, 15);
    float topOffset = iPadValue(30, 20);
    
    [self.groupView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view.mas_safeAreaLayoutGuideTop).offset(topOffset);
        make.left.equalTo(self.view).offset(margin);
        make.right.equalTo(self.view).offset(-margin);
    }];
    
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.groupView);
        make.left.right.bottom.equalTo(self.groupView);
    }];
    
    [self.autoPageSegment mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([SettingSegmentView height]);
    }];
    
    [self.manualMarkArrow mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([SettingArrowView height]);
    }];
}

#pragma mark - getters

- (UIStackView *)stackView
{
    if(!_stackView) {
        _stackView = [[UIStackView alloc]initWithArrangedSubviews:@[
            self.autoPageSegment,
            self.blacklistSwitch,
            self.manualMarkArrow
        ]];
        
        _stackView.axis = UILayoutConstraintAxisVertical;
        _stackView.spacing = 0; // 移除spacing
    }
    
    return _stackView;
}

- (UIView *)groupView {
    if (!_groupView) {
        _groupView = [[UIView alloc] init];
//        _groupView.backgroundColor = [UIColor secondarySystemGroupedBackgroundColor];
        
        // 设置圆角和阴影
        _groupView.layer.cornerRadius = 10;
        _groupView.layer.masksToBounds = YES;
        _groupView.layer.shadowColor = [UIColor blackColor].CGColor;
        _groupView.layer.shadowOffset = CGSizeMake(0, 2);
        _groupView.layer.shadowOpacity = 0.1;
        _groupView.layer.shadowRadius = 4;
    }
    return _groupView;
}

- (SettingSegmentView *)autoPageSegment
{
    if(!_autoPageSegment) {
        _autoPageSegment = [[SettingSegmentView alloc]initWithTitle:NSLocalizedString(@"autopage.title", nil) showLine:YES segments:@[
            NSLocalizedString(@"autopage.enable", nil),
            NSLocalizedString(@"autopage.disable", nil)
        ]];
    }
    
    return _autoPageSegment;
}

- (SettingSwitchAndTextView *)blacklistSwitch {
    if (!_blacklistSwitch) {
        _blacklistSwitch = [[SettingSwitchAndTextView alloc] initWithShowLine:YES];
        [_blacklistSwitch updateWithTitle:NSLocalizedString(@"autopage.add.blacklist", nil)
                                   detail:NSLocalizedString(@"autopage.blacklist.detail", nil)
                                     isOn:NO];
    }
    return _blacklistSwitch;
}

- (SettingArrowView *)manualMarkArrow {
    if (!_manualMarkArrow) {        
        _manualMarkArrow = [[SettingArrowView alloc] initWithShowLine:NO];
        [_manualMarkArrow updateWithTitle:NSLocalizedString(@"autopage.tagit", nil)];
    }
    return _manualMarkArrow;
}

- (UIButton *)infoButton {
    if (!_infoButton) {
        _infoButton = [UIButton buttonWithType:UIButtonTypeInfoLight];
        _infoButton.tintColor = [UIColor labelColor];
    }
    return _infoButton;
}

- (UIButton *)settingsButton {
    if (!_settingsButton) {
        _settingsButton = [UIButton buttonWithType:UIButtonTypeSystem];
        if (@available(iOS 13.0, *)) {
            [_settingsButton setImage:[UIImage systemImageNamed:@"gearshape"] forState:UIControlStateNormal];
        } else {
            [_settingsButton setTitle:NSLocalizedString(@"preference.setting", nil) forState:UIControlStateNormal];
        }
        _settingsButton.tintColor = [UIColor labelColor];
    }
    return _settingsButton;
}

@end


