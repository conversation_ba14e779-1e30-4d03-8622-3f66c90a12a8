//
//  AutoPageManager.h
//  PPBrowser
//
//  Created by qingbin on 2025/2/5.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>

@class AutoPageBlackListModel;
@class AutoPageModel;

/**
 东方永页机规则:
 https://ghfast.top/https://raw.githubusercontent.com/hoothin/UserScripts/master/Pagetual/pagetualRules.json
  
 https://github.com/sixcious/infy-scroll/wiki/Databases
 AutoPagerize规则：
 http://wedata.net/databases/AutoPagerize/items_all.json
 InfyScroll规则：
 http://wedata.net/databases/InfyScroll/items_all.json
 
 检查json格式是否正确的网站:
 https://jsonlint.com/
 
 自动翻页有3种模式，分别是append/ajax/click，append模式和ajax模式有点类似，通过加载"下一页"标记对应的网站，然后将得到的数据截取"正文区域"标记的区域，然后拼接在一起，如果图片加载不出来可以考虑用ajax模式，否则也可直接考虑用append模式。click模式和其他2种模式区别有点大，部分网站是自己内部拼接数据的，只需要点击"下一页"标记即可触发，因此如果是这种情况可以用click模式。
 
 // 测试网站：
 https://www.quanben.io/amp/n/buyiguandao/10.html
 
  //删除了百度规则，导致搜索分成不正常
 {
     "name": "百度 手机/iPad版",
     "nextLink": "//a[contains(@class, 'new-nextpage-only') or contains(@class, 'new-nextpage')] | //*[@id=\"page\"]/div/a[last()]",
     "pageElement": "//*[@id=\"results\"] | //*[@id=\"content_left\"]/*",
     "append": "element",
     "action": "next",
     "url": "^https?://(m|www|wap)\\.baidu\\.com/",
     "comment": "www.baidu.com会自适应不同设备，连续请求太快会失败。"
 }
 */

@interface AutoPageManager : NSObject

+ (instancetype)sharedInstance;

/**
 * 检查URL是否匹配规则，返回匹配结果给JS
 */
- (void)asyncCheckRulesForURL:(NSString *)url
                 withCallback:(void(^)(BOOL isBlackList, NSArray<NSDictionary *> *config))callback;

/**
 * 生成URL的匹配模式
 * @param url 原始URL
 * @return 生成的正则表达式模式
 */
+ (NSString *)generatePatternUrl:(NSString *)url;

/**
 * 检查URL是否匹配指定的模式
 * @param url 要检查的URL
 * @param pattern 匹配模式(正则表达式)
 * @return 是否匹配
 */
- (BOOL)url:(NSString *)url matchesPattern:(NSString *)pattern;

/**
 * 检查URL是否在黑名单中
 * @param url 要检查的URL
 * @return 黑名单model
 */
- (AutoPageBlackListModel *)isURLInBlackList:(NSString *)url;

/**
 * 刷新黑名单缓存
 */
- (void)reloadBlackList;

/**
 * 刷新用户标记
 */
- (void)reloadUserMarkRules;

@end
