//
//  AutoPageHelper.m
//  PPBrowser
//
//  Created by qing<PERSON> on 2025/2/5.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "AutoPageHelper.h"

#import "Tab.h"
#import "PPNotifications.h"
#import "BaseNavigationController.h"

#import "UIView+Helper.h"
#import "NSObject+Helper.h"

#import "NSURL+Extension.h"
#import "NSString+Helper.h"

#import "AutoPageManager.h"
#import "InternalURL.h"

@interface AutoPageHelper ()

@property(nonatomic,weak) Tab *tab;

@end

@implementation AutoPageHelper

- (instancetype)initWithTab:(Tab*)tab
{
    self = [super init];
    if(self) {
        self.tab = tab;
    }
    
    return self;
}

#pragma mark -- TabContentScript

- (NSString *)name
{
    return @"AutoPageHelper";
}

- (NSString*)scriptMessageHandlerName
{
    return @"AutoPageHelper";
}

- (void)userContentController:(WKUserContentController*)userContentController didReceiveScriptMessage:(WKScriptMessage*)message
{
    if(!self.tab || !self.tab.webView) return;
    
    NSString *url = [self.tab.webView.URL absoluteString];
    
    if ([message.body isKindOfClass:[NSDictionary class]]) {
        NSDictionary *body = message.body;
        if ([body[@"action"] isEqualToString:@"getRules"]) {
            NSMutableDictionary* result = [NSMutableDictionary dictionary];
            if([InternalURL isValid:[NSURL URLWithString:url]]) {
                // 内部网络
                result[@"isValid"] = @(NO);
                NSString *jsParam = [NSString base64EncodeWithDictionary:result];
                NSString *js = [NSString stringWithFormat:@"window.callAutoPageCallback('%@');", jsParam];
                [message.webView evaluateJavaScript:js completionHandler:nil];
                return;
            } else {
                result[@"isValid"] = @(YES);
                
                //进行 base64 编码时，中文字符需要正确处理 UTF-8 编码
                [[AutoPageManager sharedInstance] asyncCheckRulesForURL:url withCallback:^(BOOL isBlackList, NSArray<NSDictionary *> *config) {
                    // 将结果发送回JS
                    if (isBlackList) {
                        //已加入黑名单
                        result[@"isValid"] = @(NO);
                        NSString *jsParam = [NSString base64EncodeWithDictionary:result];
                        NSString *js = [NSString stringWithFormat:@"window.callAutoPageCallback('%@');", jsParam];
                        [message.webView evaluateJavaScript:js completionHandler:nil];
                        return;
                    } else if (config && config.count > 0) {
                        // 在转换为JSON之前确保所有字符串都是UTF-8编码
                        NSMutableArray *encodedConfig = [NSMutableArray array];
                        for (NSDictionary *rule in config) {
                            NSMutableDictionary *encodedRule = [NSMutableDictionary dictionary];
                            [rule enumerateKeysAndObjectsUsingBlock:^(id key, id obj, BOOL *stop) {
                                if ([obj isKindOfClass:[NSString class]]) {
                                    // 确保字符串是UTF-8编码
                                    NSString *encodedString = [obj stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
                                    encodedRule[key] = encodedString;
                                } else {
                                    encodedRule[key] = obj;
                                }
                            }];
                            [encodedConfig addObject:encodedRule];
                        }
                        result[@"dataList"] = encodedConfig;
                    } else {
                        result[@"dataList"] = [NSNull null];
                    }
                    
                    // 分割线开关
                    BOOL showSeparator = [[PreferenceManager shareInstance].items.isShowSeparatorLine boolValue];
                    result[@"showSeparatorLine"] = @(showSeparator);
                    
                    //2.6.3 智能拼页，开启智能检测
                    BOOL isEnabledAutoPageAutoDetect = [[PreferenceManager shareInstance].items.isEnabledAutoPageAutoDetect boolValue];
                    result[@"enabledAutoPageAutoDetect"] = @(isEnabledAutoPageAutoDetect);
                    
                    //国际化适配
                    NSInteger localLanguage = [NSLocalizedString(@"opensearch.value", nil) intValue];
                    result[@"localLanguage"] = @(localLanguage);
                    
                    // 使用UTF-8编码进行JSON序列化
                    NSError *error;
                    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:result options:0 error:&error];
                    NSString *jsonString = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
                    
                    // Base64编码
                    NSString *base64String = [[jsonString dataUsingEncoding:NSUTF8StringEncoding] base64EncodedStringWithOptions:0];
                    
                    NSString *js = [NSString stringWithFormat:@"window.callAutoPageCallback('%@');", base64String];
                    [message.webView evaluateJavaScript:js completionHandler:nil];
                }];
            }
        } else if ([body[@"action"] isEqualToString:@"updateUrl"]) {
            //更新历史url
            //2.6.4,发送通知到TabManager，并且进行saveTab操作
            [[NSNotificationCenter defaultCenter] postNotificationName:kUpdateAutoPageUrlNotification object:nil];
        }
    }
}

@end
