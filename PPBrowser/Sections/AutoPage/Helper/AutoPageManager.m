//
//  AutoPageManager.m
//  PPBrowser
//
//  Created by qingbin on 2025/2/5.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "AutoPageManager.h"
#import "AutoPageBlackListModel.h"
#import "ReactiveCocoa.h"
#import "DatabaseUnit+AutoPageBlackList.h"
#import "DatabaseUnit+AutoPage.h"
#import "NSURL+Extension.h"

@interface AutoPageManager ()

@property (nonatomic, strong) NSArray *autoPageRules;
@property (nonatomic, strong) NSArray<AutoPageModel *> *userMarkRules;
@property (nonatomic, strong) NSArray<AutoPageBlackListModel *> *blackList;
@property (nonatomic, strong) dispatch_queue_t ruleQueue;

@end

@implementation AutoPageManager

+ (instancetype)sharedInstance {
    static AutoPageManager *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[self alloc] init];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        self.ruleQueue = dispatch_queue_create("com.focus.autorule.queue", DISPATCH_QUEUE_SERIAL);
        [self _loadRulesAsync];
        [self reloadUserMarkRules];
        [self reloadBlackList];
    }
    return self;
}

- (void)_loadRulesAsync
{
    dispatch_async(self.ruleQueue, ^{
        [self _loadRules];
    });
}

- (void)_loadRules {
    // 加载AutoPage规则
    NSString *autoPagePath = [[NSBundle mainBundle] pathForResource:@"AutoPage" ofType:@"json"];
    NSData *autoPageData = [NSData dataWithContentsOfFile:autoPagePath];
    self.autoPageRules = [NSJSONSerialization JSONObjectWithData:autoPageData options:0 error:nil];
}

- (void)reloadBlackList {
    @weakify(self)
    DatabaseUnit* unit = [DatabaseUnit queryAllBlackLists];
    [unit setCompleteBlock:^(NSArray<AutoPageBlackListModel *> *result, BOOL success) {
        @strongify(self)
        if (success) {
            self.blackList = result;
        }
    }];
    DB_EXEC(unit);
}

- (void)reloadUserMarkRules {
    @weakify(self)
    DatabaseUnit* unit = [DatabaseUnit queryAllAutoPages];
    [unit setCompleteBlock:^(NSArray<AutoPageModel *> *result, BOOL success) {
        @strongify(self)
        if (success) {
            self.userMarkRules = result;
        }
    }];
    DB_EXEC(unit);
}

- (AutoPageBlackListModel *)isURLInBlackList:(NSString *)url {
    if (!url || url.length == 0) return nil;
    
    for (AutoPageBlackListModel *model in self.blackList) {
        if ([self url:url matchesPattern:model.patternUrl]) {
            return model;
        }
    }
    return nil;
}

- (void)asyncCheckRulesForURL:(NSString *)url withCallback:(void(^)(BOOL isBlackList, NSArray<NSDictionary *> *config))callback
{
    //判空
    NSURL* URL = [NSURL URLWithString:url];
    if (url.length == 0 || !URL) {
        if (callback) {
            callback(NO, nil);
        }
        
        return;
    }
    
    //v2.7.3，百度搜索分成，禁止自动翻页
    //v2.7.6，去他娘的百度
//    NSString* host = [URL normalizedHost];
//    if ([host.lowercaseString rangeOfString:@"baidu.com"].location != NSNotFound) {
//        if (callback) {
//            //相当于在黑名单中
//            callback(YES, nil);
//        }
//        
//        return;
//    }
    
    dispatch_async(self.ruleQueue, ^{
        [self checkRulesForURL:url withCallback:^(BOOL isBlackList, NSArray<NSDictionary *> *config) {
            dispatch_async(dispatch_get_main_queue(), ^{
                if (callback) {
                    callback(isBlackList, config);
                }
            });
        }];
    });
}

- (void)checkRulesForURL:(NSString *)url withCallback:(void(^)(BOOL isBlackList, NSArray<NSDictionary *> *))callback {
    // 先检查黑名单
    if ([self isURLInBlackList:url]) {
        callback(YES, nil);
        return;
    }
    
    //检查用户标记规则
    NSArray<NSDictionary *> *userMarkMatch = [self matchUserMarkRules:url];
    
    // 检查AutoPage规则
    NSArray<NSDictionary *> *autoPageMatch = [self matchAutoPageRules:url];
    
    NSMutableArray* result = [NSMutableArray array];
    [result addObjectsFromArray:userMarkMatch];
    [result addObjectsFromArray:autoPageMatch];
    
    if (result && result.count > 0) {
        callback(NO, result);
        return;
    }

    // 没有匹配规则，返回null
    callback(NO, nil);
}

- (NSArray<NSDictionary *> *)matchUserMarkRules:(NSString *)url {
    NSMutableArray* result = [NSMutableArray array];    
    for (AutoPageModel *obj in self.userMarkRules) {
        NSString* patternUrl = obj.patternUrl;
        if (patternUrl.length == 0) continue;
        if ([self url:url matchesPattern:patternUrl]) {
            NSMutableDictionary* mutableRule = [NSMutableDictionary dictionary];
            if (obj.type == AutoPageMarkModeAppend) {
                mutableRule[@"action"] = @"next";
                mutableRule[@"append"] = @"element";
                mutableRule[@"nextLink"] = obj.nextXPath ?: @"";
                mutableRule[@"pageElement"] = obj.pageElementXPath ?: @"";
            } else if (obj.type == AutoPageMarkModeAjax) {
                mutableRule[@"action"] = @"click";
                mutableRule[@"append"] = @"ajax";
                mutableRule[@"clickElement"] = obj.nextXPath ?: @"";
                mutableRule[@"pageElement"] = obj.pageElementXPath ?: @"";
            } else if (obj.type == AutoPageMarkModeClick) {
                mutableRule[@"action"] = @"next";
                mutableRule[@"append"] = @"none";
                mutableRule[@"nextLink"] = obj.nextXPath ?: @"";
            }
            
            [result addObject:mutableRule];
        }
    }
    
    // 如果没有匹配到结果，则检查URL的host是否与任何规则的sourceUrl的host一致
    if (result.count == 0) {
        NSURL *nsurl = [NSURL URLWithString:url];
        if (nsurl && nsurl.host) {
            NSString *urlHost = nsurl.host;
            
            for (AutoPageModel *obj in self.userMarkRules) {
                if (obj.sourceUrl.length == 0) continue;
                
                NSURL *sourceURL = [NSURL URLWithString:obj.sourceUrl];
                if (sourceURL && sourceURL.host && [urlHost isEqualToString:sourceURL.host]) {
                    NSMutableDictionary* mutableRule = [NSMutableDictionary dictionary];
                    if (obj.type == AutoPageMarkModeAppend) {
                        mutableRule[@"action"] = @"next";
                        mutableRule[@"append"] = @"element";
                        mutableRule[@"nextLink"] = obj.nextXPath ?: @"";
                        mutableRule[@"pageElement"] = obj.pageElementXPath ?: @"";
                    } else if (obj.type == AutoPageMarkModeAjax) {
                        mutableRule[@"action"] = @"click";
                        mutableRule[@"append"] = @"ajax";
                        mutableRule[@"clickElement"] = obj.nextXPath ?: @"";
                        mutableRule[@"pageElement"] = obj.pageElementXPath ?: @"";
                    } else if (obj.type == AutoPageMarkModeClick) {
                        mutableRule[@"action"] = @"next";
                        mutableRule[@"append"] = @"none";
                        mutableRule[@"nextLink"] = obj.nextXPath ?: @"";
                    }
                    
                    [result addObject:mutableRule];
                }
            }
        }
    }
    
    return result;
}

- (NSArray<NSDictionary *> *)matchAutoPageRules:(NSString *)url {
    NSMutableArray* result = [NSMutableArray array];
    for (NSDictionary *rule in self.autoPageRules) {
        NSString* patternUrl = rule[@"url"];
        if (patternUrl.length == 0) continue;
        if ([self url:url matchesPattern:patternUrl]) {
            //url取当前webview的url，这里的url是正则表达式，和AutoPage.js中的url(表示当前url)不是同一个意思
            //防止AutoPage算法误判
            NSMutableDictionary* mutableRule = [NSMutableDictionary dictionaryWithDictionary:rule];
            mutableRule[@"url"] = url?:@"";
            
            [result addObject:mutableRule];
        }
    }
    return result;
}

// 检查URL是否匹配指定的模式
- (BOOL)url:(NSString *)url matchesPattern:(NSString *)pattern {
    NSError *error = nil;
    NSRegularExpression *regex = [NSRegularExpression regularExpressionWithPattern:pattern
                                                                         options:NSRegularExpressionCaseInsensitive
                                                                           error:&error];
    if (error) return NO;
    
    NSRange range = [regex rangeOfFirstMatchInString:url
                                             options:0
                                               range:NSMakeRange(0, url.length)];
    
    return range.location != NSNotFound;
}

#pragma mark - URL Pattern Generation

+ (NSString *)generatePatternUrl:(NSString *)url {
    if (url.length == 0) return @"";
    
    NSURL *nsurl = [NSURL URLWithString:url];
    if (!nsurl) return @"";
    
    // 1. 提取主要组成部分
    NSString *scheme = nsurl.scheme;
    NSString *host = nsurl.host;
    NSString *path = nsurl.path;
    
    // 2. 处理路径部分
    NSMutableString *pattern = [NSMutableString string];
    
    // 添加协议和域名
    [pattern appendFormat:@"^%@://%@", scheme, [host stringByReplacingOccurrencesOfString:@"." withString:@"\\."]];
    
    // 3. 处理路径部分
    if (path.length > 0) {
        NSArray *pathComponents = [path componentsSeparatedByString:@"/"];
        NSMutableArray *processedComponents = [NSMutableArray array];
        
        for (NSString *component in pathComponents) {
            if (component.length == 0) continue;
            
            // 检查是否是数字页码
            if ([self isPageNumber:component]) {
                [processedComponents addObject:@"\\d+"];
            }
            // 检查是否包含日期格式
            else if ([self isDateComponent:component]) {
                [processedComponents addObject:@"\\d{4}(?:-\\d{1,2}){0,2}"];
            }
            // 检查是否是ID格式
            else if ([self isIDComponent:component]) {
                [processedComponents addObject:@"[a-zA-Z0-9_-]+"];
            }
            else {
                [processedComponents addObject:[self escapeRegexString:component]];
            }
        }
        
        if (processedComponents.count > 0) {
            [pattern appendString:@"/"];
            [pattern appendString:[processedComponents componentsJoinedByString:@"/"]];
        }
    }
    
    // 4. 处理查询参数
    NSString *query = nsurl.query;
    if (query) {
        // 将查询参数部分替换为可选模式
        [pattern appendString:@"(?:\\?[^#]*)?"];
    }
    
    // 5. 处理锚点
    NSString *fragment = nsurl.fragment;
    if (fragment) {
        // 将锚点部分替换为可选模式
        [pattern appendString:@"(?:#.*)?"];
    }
    
    return pattern;
}

#pragma mark - URL Pattern Helpers

+ (BOOL)isPageNumber:(NSString *)component {
    // 匹配纯数字、p123、page123等常见页码格式
    NSString *pattern = @"^(?:p(?:age)?[_-]?)?\\d+$|^\\d+$";
    NSPredicate *predicate = [NSPredicate predicateWithFormat:@"SELF MATCHES %@", pattern];
    return [predicate evaluateWithObject:component];
}

+ (BOOL)isDateComponent:(NSString *)component {
    // 匹配2024、2024-02、2024-02-23等日期格式
    NSString *pattern = @"^\\d{4}(?:-\\d{1,2}){0,2}$";
    NSPredicate *predicate = [NSPredicate predicateWithFormat:@"SELF MATCHES %@", pattern];
    return [predicate evaluateWithObject:component];
}

+ (BOOL)isIDComponent:(NSString *)component {
    // 匹配常见的ID格式：字母数字下划线连字符
    NSString *pattern = @"^[a-zA-Z0-9][a-zA-Z0-9_-]*$";
    NSPredicate *predicate = [NSPredicate predicateWithFormat:@"SELF MATCHES %@", pattern];
    return [predicate evaluateWithObject:component];
}

+ (NSString *)escapeRegexString:(NSString *)string {
    // 转义正则表达式特殊字符
    NSString *escapedString = [string stringByReplacingOccurrencesOfString:@"[.+?^${}()|[\\]\\\\]" 
                                                               withString:@"\\\\$0" 
                                                                  options:NSRegularExpressionSearch 
                                                                    range:NSMakeRange(0, string.length)];
    return escapedString;
}

@end
