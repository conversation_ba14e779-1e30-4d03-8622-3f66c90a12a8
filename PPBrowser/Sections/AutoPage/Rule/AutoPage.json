[{"name": "百度 手机/iPad版", "nextLink": "//a[contains(@class, 'new-nextpage-only') or contains(@class, 'new-nextpage')] | //*[@id=\"page\"]/div/a[last()]", "pageElement": "//*[@id=\"results\"] | //*[@id=\"content_left\"]/*", "append": "element", "action": "next", "url": "^https?://(m|www|wap)\\.baidu\\.com/", "comment": "www.baidu.com会自适应不同设备，连续请求太快会失败。"}, {"name": "Google 手机搜索", "action": "next", "append": "none", "nextLink": "div[role='progressbar']~a[role='button']", "url": "^https://(?:www\\.)?google\\..*/search", "exampleUrl": "https://www.google.com/search?q=hello"}, {"name": "Google Search", "action": "next", "append": "element", "nextLink": "id('pnnext')|id('navbar navcnt nav')//td[span]/following-sibling::td[1]/a|id('nn')/parent::a", "pageElement": "id('rso')|id('center_col')/style[contains(.,'relative')][id('rso')]", "url": "^https?://[^./]+\\.google(?:\\.[^./]{2,3}){1,2}/(?:c(?:se|ustom)|search|webhp|m|#)", "exampleUrl": "https://www.google.com/search?q=hello"}, {"name": "<PERSON>", "action": "click", "append": "ajax", "clickElement": "a[title='Next page'],a.sb_pagN,a.sb_halfnext,a.sb_fullnpl", "pageElement": "id(\"b_results\")/li[@class=\"b_algo\"]|id(\"results\")", "url": "^https?://.*\\.bing\\.com/(?:[^/]+/)*?(?:results\\.aspx|search)", "exampleUrl": "https://www.bing.com/search?q=Edge"}, {"name": "Duckduckgo", "url": "^https://duckduckgo\\.com/\\?", "action": "next", "append": "none", "nextLink": "#more-results,.result--more>a"}, {"name": "头条搜索手机版", "pageElement": "#results", "append": "element", "action": "next", "url": "^https?://(so|m)\\.toutiao\\.com/search", "nextLink": "a[class^=container_],a[class^=containerRight]"}, {"name": "360搜索手机版", "action": "click", "append": "ajax", "pageElement": "#main > div.r-results", "clickElement": "#load-more", "url": "^https://m\\.so\\.com/s\\?"}, {"name": "yandex搜索", "action": "next", "append": "none", "nextLink": ".main__container button.Pager-More", "url": "^https://yandex\\.com/search"}, {"name": "Pixiv手机版", "clickElement": "//div[@class='pager']/a[contains(@class, 'next')][./div[@class='arrow']]", "pageElement": "//div[contains(@class, 'works-grid-list')]", "append": "ajax", "action": "click", "url": "^https://www\\.pixiv\\.net/", "exampleUrl": "https://www.pixiv.net/tags/オリジナル/artworks"}, {"name": "Pixiv iPad版", "clickElement": "//nav/button[@aria-current='true']/following-sibling::a[not(@hidden)]", "pageElement": "//section/div/ul | //section/div/div/ul | //section/div/div/div/ul", "loadElement": "//section//div/ul//figure", "append": "ajax", "action": "click", "url": "^https://www\\.pixiv\\.net/", "exampleUrl": "https://www.pixiv.net/tags/オリジナル/artworks"}, {"name": "coccoc 手机版", "clickElement": "div.moreResultMobile-WWClT", "pageElement": "div.searchResultsMain-HyzAT", "append": "ajax", "action": "click", "url": "^https://coccoc\\.com/search", "exampleUrl": "https://coccoc.com/search?query=Th%E1%BB%9Di+ti%E1%BA%BFt+ng%C3%A0y+mai"}, {"name": "coccoc iPad版", "clickElement": "//div[@class='searchPagination-fN0W1 searchPagination-VM8NV']", "pageElement": "//div[@class='searchResultsMain-HyzAT']/*", "append": "ajax", "action": "click", "url": "^https://coccoc\\.com/search", "exampleUrl": "https://coccoc.com/search?query=Th%E1%BB%9Di+ti%E1%BA%BFt+ng%C3%A0y+mai"}, {"name": "v2ex", "action": "next", "append": "element", "pageElement": "#Wrapper .content .box .cell.item", "nextLink": "input[value*=\"下一页\"],#mobile-topic-link-more", "url": "^https://(www\\.)?v2ex\\.com/", "exampleUrl": "https://v2ex.com/recent"}, {"name": "v2ex 帖子评论", "action": "next", "append": "element", "pageElement": "#Wrapper .content .box .cell", "nextLink": "span.page_current+a", "url": "^https://(www\\.)?v2ex\\.com/t", "exampleUrl": "https://v2ex.com/t/1112853"}, {"name": "包子漫画 - twmanga.com", "action": "click", "append": "ajax", "pageElement": ".comic-contain > *", "clickElement": "#next-chapter", "url": "^https://www\\.twmanga\\.com/comic/chapter/", "exampleUrl": "https://www.twmanga.com/comic/chapter/"}, {"name": "mh5漫画", "action": "click", "append": "ajax", "pageElement": ".ptview>img:not([style])", "clickElement": "//div[contains(@class, 'setnmh-pagedos')]//div[contains(@class, 'setnmh-nextpage')][last()]/a", "url": "^https://mh5\\.tw/", "exampleUrl": "https://mh5.tw/series-wuncyov-626757-2-%E6%AD%A6%E9%80%86", "comment": "clickElement要A标签才生效"}, {"name": "dm5漫画", "action": "click", "append": "ajax", "pageElement": "#cp_img", "clickElement": ".view-bottom-bar li:nth-last-of-type(2)>a", "url": "^https?://(www\\.manhuaren|m\\.dm5|m\\.1kkk)\\.com/", "exampleUrl": "https://m.dm5.com/m426475-p15/"}, {"name": "言耽社 - 閱讀", "action": "click", "append": "ajax", "pageElement": "title,.article-content", "clickElement": "span.current+a", "url": "^https?://yandanshe\\.com/\\d+", "exampleUrl": "https://yandanshe.com/19327/3/"}, {"name": "MINI4K", "action": "click", "append": "ajax", "pageElement": "div[class*='-item-list']>ul>.column", "clickElement": "a.pager__item--next", "url": "^https?://www\\.mini4k\\.com/movies", "exampleUrl": "https://www.mini4k.com/movies"}, {"name": "1337x", "action": "next", "append": "element", "pageElement": "table.table-list>tbody", "nextLink": ".pagination li.active+li>a", "url": "^https?://www\\.1377x\\.", "exampleUrl": "https://www.1377x.to/search/hello/2/"}, {"name": "萌番组 lite", "action": "click", "append": "ajax", "pageElement": ".torrents-ul", "clickElement": "//section/a[span[contains(@class, 'next')]]", "exampleUrl": "https://bangumi.moe/lite/", "url": "^https?://bangumi\\.moe/lite/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "action": "next", "append": "element", "nextLink": "a.disabled+a:not(.disabled)", "pageElement": ".full2, .full2+script", "exampleUrl": "https://www.anirena.com/", "url": "^https?://www\\.anirena\\.com/"}, {"name": "ACG.RIP", "action": "next", "append": "element", "nextLink": "li.next>a", "pageElement": "table.post-index>tbody", "exampleUrl": "https://acg.rip/", "url": "^https?://acg\\.rip/"}, {"name": "anime1.me", "action": "click", "append": "ajax", "clickElement": "#table-list_next", "pageElement": "#table-list>tbody>tr", "loadElement": "#table-list>tbody>tr.odd", "exampleUrl": "https://anime1.me/", "url": "^https?://anime1\\.me/"}, {"name": "怡萱动漫", "action": "next", "append": "element", "url": "^https?://www\\.iyxdm\\.me/", "nextLink": "//a[@class='nextPage' or text()='下一页']", "pageElement": ".dhnew>ul", "exampleUrl": "https://www.iyxdm.me/resource/"}, {"name": "LIBVIO", "action": "click", "append": "ajax", "url": "^https?://.*\\.libviohd\\.*/type/", "clickElement": "li.active+li>a", "pageElement": ".stui-vodlist", "exampleUrl": "https://www.libvio.fun/type/1-2.html"}, {"name": "筆趣影視", "action": "click", "append": "ajax", "url": "^https?://(www\\.)?biquysw.com/(acg|zongyi|mov|tv)/", "clickElement": "//a[@class='a1' and text()='下一页']", "pageElement": ".index-tj", "exampleUrl": "http://biquysw.com/acg/y2022/"}, {"name": "游研社", "action": "click", "append": "ajax", "url": "^https?://www\\.yystv\\.cn/", "clickElement": "li.next-page", "pageElement": ".list-container>li,.video-list,.img-mode", "exampleUrl": "https://www.yystv.cn/docs"}, {"name": "马克喵", "action": "next", "append": "element", "url": "^https?://www\\.macat\\.vip/", "nextLink": ".next", "pageElement": ".posts-wrapper>div", "exampleUrl": "https://www.macat.vip/%e5%ad%97%e4%bd%93"}, {"name": "小众软件", "action": "next", "append": "element", "url": "^https?://www\\.appinn\\.com/", "nextLink": "a.next,.next>a", "pageElement": "article,.amp-post-title,.amp-featured-image,.cntn-wrp", "exampleUrl": "https://www.appinn.com/category/system/page/6/"}, {"name": "scloud.ws", "action": "next", "append": "element", "url": "^https?://scloud\\.ws/", "nextLink": ".js-ls-pagination-next", "pageElement": ".topic-card-wrapper", "exampleUrl": "https://scloud.ws/index/page4/"}, {"name": "w3school", "action": "click", "append": "ajax", "url": "^https?://www\\.w3school\\.com\\.cn/", "clickElement": "li.next>a", "pageElement": "#maincontent>*:not([class*='prenextnav']):not(#bpn):not(#tpn)", "exampleUrl": "https://www.w3school.com.cn/js/js_htmldom_html.asp"}, {"name": "菜鸟教程 - 分类页", "action": "next", "append": "element", "url": "^https?://www\\.runoob\\.com/w3cnote", "nextLink": "li.next-page>a", "pageElement": ".archive-list>.archive-list-item", "exampleUrl": "https://www.runoob.com/w3cnote"}, {"name": "菜鸟教程 - 文章页", "action": "click", "append": "ajax", "url": "^https?://www\\.runoob\\.com/[a-z/-]+\\.html", "clickElement": ".next-design-link a[rel=\"next\"]", "pageElement": ".article-heading, .article-body", "exampleUrl": "https://www.runoob.com/js/js-tutorial.html"}, {"name": "果核剥壳", "action": "click", "append": "ajax", "url": "^https?://www\\.ghxi\\.com/", "clickElement": "a.next", "pageElement": "ul.post-loop>.item", "exampleUrl": "https://www.ghxi.com/category/all/pcsoft"}, {"name": "億破姐&電腦系統吧", "action": "click", "append": "ajax", "url": "^https?://www\\.(ypojie|dnxitong)\\.com/", "clickElement": ".next-page>a", "pageElement": "article[class^='excerpt excerpt-']", "exampleUrl": "https://www.ypojie.com/pc，http://www.dnxitong.com/soft/"}, {"name": "FC Portables", "action": "next", "append": "element", "url": "^https?://www\\.fcportables\\.com/", "nextLink": "a.next", "pageElement": "article[class^='post']", "exampleUrl": "https://www.fcportables.com/"}, {"name": "KaranPc", "action": "next", "append": "element", "url": "^https?://karanpc\\.com/", "nextLink": "a.next", "pageElement": "article[class^='post']", "exampleUrl": "https://karanpc.com/windows/download-managers/"}, {"name": "阿榮福利味", "action": "next", "append": "element", "url": "^https?://www\\.azofreeware\\.com/", "nextLink": ".blog-pager-older-link", "pageElement": ".blog-posts.hfeed", "exampleUrl": "https://www.azofreeware.com/"}, {"name": "Apkpure", "action": "next", "append": "none", "url": "^https?://apkpure\\.com/", "nextLink": "a.show-more", "exampleUrl": "https://apkpure.com/tw/game_adventure"}, {"name": "BOOK☆WALKER TW", "action": "click", "append": "ajax", "exampleUrl": "https://www.bookwalker.com.tw/block/1", "url": "^https?://www\\.bookwalker\\.com\\.tw/", "clickElement": "//li[span/@class='active']/following-sibling::li[1]/a", "pageElement": ".book_package,.bookdesc2.clearfix,.row.listbox.clearfix+.row.mar0pad0"}, {"name": "BOOK☆WALKER JP - 手機版", "action": "click", "append": "ajax", "exampleUrl": "https://bookwalker.jp/new/?qsto=st2", "url": "^https?://bookwalker\\.jp/(category|search|new)/", "clickElement": "li.o-select-box+li>a,.o-pager-next>a", "pageElement": ".o-tile-list>.o-tile,.o-contents-section__body .m-tile"}, {"name": "www.shoujixs.net 小说阅读", "action": "click", "append": "ajax", "exampleUrl": "https://wap.shoujixs.net/shoujixs_161450_40736193.html,https://www.shoujixs.net/shoujixs_161450_40736191.html", "url": "^https?://(www.wap)\\.shoujixs\\.net/", "clickElement": "//div[contains(@class, 'Readpage')]/a[last()] | //a[normalize-space(text())='下一章']", "pageElement": "#articlecon,#zjny"}, {"name": "69书吧", "action": "click", "append": "ajax", "url": "^https?://(?:www\\.)?69shuba\\.[^/]+/txt/\\d+/\\d+", "exampleUrl": "https://69shuba.cx/txt/38161/26369110", "clickElement": "//a[normalize-space(text())='下一章']", "pageElement": ".txtnav"}, {"name": "次元姬小说 - 閱讀", "action": "click", "append": "ajax", "url": "^https?://(?:www\\.)?ciyuanji\\.com/chapter/\\w+\\.htm", "exampleUrl": "https://www.ciyuanji.com/chapter/19595_3723178.html", "clickElement": "//button[normalize-space(text())='下一章']", "pageElement": "[class^=\"chapter_title\"],[class^=\"chapter_list\"],[class^=\"chapter_article\"]"}, {"name": "稷下書院 - 閱讀", "action": "next", "append": "element", "pageElement": "h1,.content", "nextLink": "//a[normalize-space(text())='下一章' and not(contains(@href,'end'))]", "url": "^https://(?:www\\.)?\\.twmanga\\.com/comic/chapter/", "exampleUrl": "https://kan.timotxt.com/c/1004298677_6.html"}, {"name": "小說狂人 - 閱讀", "action": "next", "append": "element", "pageElement": ".name,.content", "nextLink": "//a[normalize-space(text())='下一章']", "url": "^https?://czbooks\\.net/n/\\w+/\\w+", "exampleUrl": "https://czbooks.net/n/u19ba/ui0g9"}, {"name": "小說狂人 - 書籍分類", "action": "next", "append": "element", "pageElement": ".novel-list>.novel-item-wrapper", "nextLink": ".nav.paginate>li.active+li>a", "url": "^https?://czbooks\\.net/c/\\w+", "exampleUrl": "https://czbooks.net/c/xuanhuan/3"}, {"name": "無限小說 - 書籍分類", "action": "next", "append": "element", "pageElement": ".row>div.p-2", "nextLink": "li[onclick]+li>a", "url": "^https?://(www\\.)?8book\\.com/booklists/(update|newrelease|finish|list|\\d+)/", "exampleUrl": "https://www.8book.com/booklists/newrelease/"}, {"name": "ftopx.com", "action": "next", "append": "element", "pageElement": "//div[div[@class='thumbnail']]", "nextLink": "li.active+li>a", "url": "^https?://ftopx\\.com/", "exampleUrl": "https://ftopx.com/"}, {"name": "Kemono - posts", "action": "click", "append": "ajax", "pageElement": ".card-list.card-list--legacy", "clickElement": "a.pagination-button-after-current", "url": "^https?://kemono\\.su/posts", "exampleUrl": "https://kemono.su/posts"}, {"name": "看妹图", "action": "click", "append": "ajax", "pageElement": "#list", "clickElement": "//a[@class='nextPage' or text()='下一页']", "url": "^https?://kanmeitu\\d\\.cc/p/(index_\\d+.html)?", "exampleUrl": "https://kanmeitu1.cc/p/"}, {"name": "ACG漫画网 - 阅读", "action": "next", "append": "element", "pageElement": ".manga-page img", "nextLink": "#pages span+a", "url": "^https?://www\\.acgxmh\\.com/", "exampleUrl": "https://www.acgxmh.com/h/314618.html"}, {"name": "ACG漫画网 - 分类页", "action": "next", "append": "element", "pageElement": ".manga-page", "nextLink": "#pages span+a", "url": "^https?://www\\.acgxmh\\.com/", "exampleUrl": "https://www.acgxmh.com/hentai/"}, {"name": "18x78", "action": "next", "append": "element", "pageElement": ".posts-wrapper", "nextLink": "a.next", "url": "^https?://18x78\\.com/", "exampleUrl": "https://18x78.com/"}, {"name": "绅士漫画 - 閱讀", "action": "click", "append": "ajax", "pageElement": "#picarea", "clickElement": "//a[text()='下一頁']", "url": "^https?://(www\\.)?(wnacg|htcomic|ssmh\\d+)\\.[^/]+/photos-view", "exampleUrl": "https://wnacg.com/photos-view-id-15403544.html"}, {"name": "绅士漫画", "action": "next", "append": "element", "pageElement": "li.gallary_item", "nextLink": "span.thispage+a", "url": "^https?://(www\\.)?(wnacg|htcomic|ssmh\\d+)\\.[^/]+/(albums|search|photos-index)", "exampleUrl": "https://wnacg.com/albums-index-cate-9.html"}, {"name": "松鼠症倉庫 - 閱讀", "action": "click", "append": "ajax", "pageElement": "#show_image_area", "clickElement": "//a[text()='下一頁']", "url": "^https?://ahri8\\.top/readOnline2\\.php", "exampleUrl": "https://ahri8.top/readOnline2.php?ID=97432&host_id=0"}, {"name": "The Hentai World", "action": "next", "append": "element", "pageElement": "#thumbContainer", "nextLink": "a.next, .current+.minithumb>a", "url": "^https?://thehentaiworld\\.com/", "exampleUrl": "https://thehentaiworld.com/?new"}, {"name": "Hanime1 - 漫画", "action": "next", "append": "element", "pageElement": "img#current-page-image", "nextLink": "a.comic-show-content-nav-item-wrapper.arrow-right", "url": "^https?://hanime1\\.me/comic\\/\\d+\\/\\d", "exampleUrl": "https://hanime1.me/comic/80357/1"}, {"name": "177漫画", "action": "next", "append": "element", "pageElement": "#main", "nextLink": ".next.page-numbers", "url": "^https?://177picyy\\.com", "exampleUrl": "www.177picyy.com"}, {"name": "xxiav图片", "action": "next", "append": "element", "pageElement": "#main", "nextLink": ".next.page-numbers", "url": "^https?://xxiav\\.com", "exampleUrl": "www.xxiav.com"}, {"name": "91xx", "action": "click", "append": "ajax", "pageElement": "#wrapper .row:first-of-type", "clickElement": ".pagingnav+a", "url": "^https?://91porn\\.com/v.php", "exampleUrl": ""}, {"name": "jablexx", "action": "click", "append": "ajax", "pageElement": "#list_videos_common_videos_list .container:last-of-type", "clickElement": "//ul[contains(@class, \"pagination\")]//span[contains(@class, \"active\")]/ancestor::li/following-sibling::li[1]/a", "url": "^https?://jable\\.tv", "exampleUrl": ""}]