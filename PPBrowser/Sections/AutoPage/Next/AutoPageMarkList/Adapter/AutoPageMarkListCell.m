//
//  AutoPageMarkListCell.m
//  PPBrowser
//
//  Created by qingbin on 2025/2/23.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "AutoPageMarkListCell.h"

#import "Masonry.h"
#import "BrowserUtils.h"
#import "DateHelper.h"
#import "UIColor+Helper.h"
#import "ThemeProtocol.h"
#import "UIView+Helper.h"

@interface AutoPageMarkListCell()

@property (nonatomic, strong) UIView *backView;
@property (nonatomic, strong) UILabel *urlLabel;
@property (nonatomic, strong) UILabel *timeLabel;
@property (nonatomic, strong) UIView *line;
@property (nonatomic, strong) AutoPageModel *model;

@end

@implementation AutoPageMarkListCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        self.backgroundColor = UIColor.clearColor;
        [self addSubviews];
        [self defineLayout];
    }
    return self;
}

- (void)addSubviews {
    [self.contentView addSubview:self.backView];
    [self.backView addSubview:self.urlLabel];
    [self.backView addSubview:self.timeLabel];
    [self.backView addSubview:self.line];
}

- (void)defineLayout {
    float margin = iPadValue(30, 15);
    float topOffset = iPadValue(20, 15);
    
    [self.backView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.contentView);
    }];
    
    [self.urlLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.backView).offset(margin);
        make.right.equalTo(self.backView).offset(-margin);
        make.top.equalTo(self.backView).offset(topOffset);
    }];
    
    [self.timeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.urlLabel);
        make.top.equalTo(self.urlLabel.mas_bottom).offset(8);
        make.bottom.equalTo(self.backView).offset(-topOffset);
    }];
    
    [self.line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.backView).offset(margin);
        make.right.equalTo(self.backView).offset(-margin);
        make.bottom.equalTo(self.backView);
        make.height.mas_equalTo(0.5);
    }];
}

- (void)updateWithModel:(AutoPageModel *)model {
    _model = model;
    
    // 获取模式文本和颜色
    NSString *modeText;
    UIColor *modeColor;
    switch (model.type) {
        case AutoPageMarkModeAppend:
            modeText = @"Append";
            modeColor = [UIColor systemGreenColor];
            break;
        case AutoPageMarkModeAjax:
            modeText = @"Ajax";
            modeColor = [UIColor systemBlueColor];
            break;
        case AutoPageMarkModeClick:
            modeText = @"Click";
            modeColor = [UIColor systemOrangeColor];
            break;
    }
    
    // 创建富文本
    NSMutableAttributedString *attributedString = [[NSMutableAttributedString alloc] init];
    
    // 添加模式文本
    NSAttributedString *modeAttr = [[NSAttributedString alloc] 
        initWithString:[NSString stringWithFormat:@"%@: ", modeText]
        attributes:@{
            NSFontAttributeName: [UIFont boldSystemFontOfSize:iPadValue(16, 14)],
            NSForegroundColorAttributeName: modeColor
        }];
    [attributedString appendAttributedString:modeAttr];
    
    // 添加URL文本
    NSAttributedString *urlAttr = [[NSAttributedString alloc] 
        initWithString:model.sourceUrl ?: @""
        attributes:@{
            NSFontAttributeName: [UIFont systemFontOfSize:iPadValue(16, 14)],
            NSForegroundColorAttributeName: [ThemeProtocol isDarkTheme] ? 
                UIColor.whiteColor : [UIColor colorWithHexString:@"#333333"]
        }];
    [attributedString appendAttributedString:urlAttr];
    
    // 设置富文本
    self.urlLabel.attributedText = attributedString;
    
    // 设置时间
    NSTimeInterval timeInterval = [model.ctime doubleValue];
    self.timeLabel.text = [DateHelper timeWithDate:timeInterval formatter:NormalDateFormatter];
    
    [self updateCornerRadius];
    [self applyTheme];
}

- (void)updateCornerRadius {
    float cornerRadius = iPadValue(20, 10);
    
    // 圆角
    self.line.hidden = NO;
    self.backView.layer.cornerRadius = cornerRadius;
    self.backView.layer.masksToBounds = YES;
    
    if (self.isFirstInSection && self.isLastInSection) {
        // 只有一个
        self.backView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner | kCALayerMinXMaxYCorner | kCALayerMaxXMaxYCorner;
        self.line.hidden = YES;
    } else {
        if (self.isFirstInSection) {
            // 添加上圆角
            self.backView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
        } else if (self.isLastInSection) {
            // 添加下圆角
            self.backView.layer.maskedCorners = kCALayerMinXMaxYCorner | kCALayerMaxXMaxYCorner;
            self.line.hidden = YES;
        } else {
            self.backView.layer.masksToBounds = NO;
            self.backView.layer.cornerRadius = 0;
        }
    }
}

- (void)applyTheme {
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    
    if (isDarkTheme) {
        self.backView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        // 更新富文本中URL部分的颜色
        [self updateUrlTextColorForDarkTheme:YES];
        self.timeLabel.textColor = [UIColor colorWithHexString:@"#999999"];
        self.line.backgroundColor = [UIColor colorWithHexString:kDarkThemeColorLine];
    } else {
        self.backView.backgroundColor = UIColor.whiteColor;
        // 更新富文本中URL部分的颜色
        [self updateUrlTextColorForDarkTheme:NO];
        self.timeLabel.textColor = [UIColor colorWithHexString:@"#666666"];
        self.line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
}

// 辅助方法：更新富文本中URL部分的颜色
- (void)updateUrlTextColorForDarkTheme:(BOOL)isDark {
    if (!self.urlLabel.attributedText) return;
    
    NSMutableAttributedString *attributedString = [[NSMutableAttributedString alloc] 
        initWithAttributedString:self.urlLabel.attributedText];
    
    // 获取模式文本的长度
    NSRange colonRange = [attributedString.string rangeOfString:@": "];
    if (colonRange.location == NSNotFound) return;
    
    // 更新URL部分的颜色
    NSRange urlRange = NSMakeRange(colonRange.location + colonRange.length, 
                                  attributedString.length - (colonRange.location + colonRange.length));
    
    [attributedString addAttribute:NSForegroundColorAttributeName 
                           value:isDark ? UIColor.whiteColor : [UIColor colorWithHexString:@"#333333"]
                           range:urlRange];
    
    self.urlLabel.attributedText = attributedString;
}

#pragma mark - Lazy Load

- (UIView *)backView {
    if (!_backView) {
        _backView = [[UIView alloc] init];
    }
    return _backView;
}

- (UILabel *)urlLabel {
    if (!_urlLabel) {
        _urlLabel = [UIView createLabelWithTitle:@""
                                     textColor:[UIColor colorWithHexString:@"#333333"]
                                       bgColor:UIColor.clearColor
                                      fontSize:iPadValue(18, 16)
                                 textAlignment:NSTextAlignmentLeft
                                         bBold:NO];
        _urlLabel.numberOfLines = 2;
    }
    return _urlLabel;
}

- (UILabel *)timeLabel {
    if (!_timeLabel) {
        _timeLabel = [UIView createLabelWithTitle:@""
                                      textColor:[UIColor colorWithHexString:@"#666666"]
                                        bgColor:UIColor.clearColor
                                       fontSize:iPadValue(15, 13)
                                  textAlignment:NSTextAlignmentLeft
                                          bBold:NO];
    }
    return _timeLabel;
}

- (UIView *)line {
    if (!_line) {
        _line = [[UIView alloc] init];
        _line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
    return _line;
}

@end
