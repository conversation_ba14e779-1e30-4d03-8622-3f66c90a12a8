//
//  AutoPageBlackListViewController.m
//  PPBrowser
//
//  Created by qingbin on 2025/2/22.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "AutoPageBlackListViewController.h"
#import "AutoPageBlackListCell.h"
#import "DatabaseUnit+AutoPageBlackList.h"

#import "Masonry.h"
#import "MaizyHeader.h"
#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "ReactiveCocoa.h"
#import "UIView+FrameHelper.h"
#import "UIViewController+Helper.h"
#import "NSObject+Helper.h"

#import "PaddingNewLabel.h"

#import "PPNotifications.h"
#import "VIPController.h"
#import "BaseNavigationController.h"
#import "BrowserUtils.h"

#import "BrowserHelper.h"

#import "PPEnums.h"
#import "UITableView+HintMessage.h"

@interface AutoPageBlackListViewController () <UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) NSMutableArray<AutoPageBlackListModel *> *model;

@end

@implementation AutoPageBlackListViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.title = NSLocalizedString(@"autopage.blacklist", nil);
    [self addSubviews];
    [self defineLayout];
    [self applyTheme];
    [self createCustomLeftBarButtonItem];
    
    [self loadData];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if (isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
    } else {
        self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
    
    self.tableView.backgroundColor = self.view.backgroundColor;
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    //只有当前的controller会触发一次, 其它已存在的controller走通知
    if(needReload) {
        //修改暗黑模式
        [self applyTheme];
        
        [self.tableView reloadData];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}

#pragma mark - layout

- (void)addSubviews
{
    [self.view addSubview:self.tableView];
}

- (void)defineLayout {
    float margin = iPadValue(30, 15);
    float topOffset = iPadValue(30, 20);
    
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(topOffset);
        make.left.mas_offset(margin);
        make.right.mas_offset(-margin);
        make.bottom.equalTo(self.view);
    }];
}

- (void)loadData {
    @weakify(self)
    DatabaseUnit* unit = [DatabaseUnit queryAllBlackLists];
    [unit setCompleteBlock:^(id result, BOOL success) {
        @strongify(self)
        if (success) {
            self.model = [NSMutableArray arrayWithArray:result];
            [self.tableView reloadData];
            [self showHintMessageIfNeed];
        }
    }];
    DB_EXEC(unit);
}

- (void)showHintMessageIfNeed
{
    if(self.model.count == 0) {
        UIImage* image = [UIImage imageNamed:@"empty_data_logo"];
        [self.tableView showHintMessage:NSLocalizedString(@"tableview.emptyTips", nil)
                                  image:image
                          sectionMargin:iPadValue(120, 80)];
    } else {
        [self.tableView hideHintMessage];
    }
}

#pragma mark - UITableViewDataSource & UITableViewDelegate

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.model.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    AutoPageBlackListCell *cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass(AutoPageBlackListCell.class)];
    
    // 设置cell位置信息
    cell.isFirstInSection = (indexPath.row == 0);
    cell.isLastInSection = (indexPath.row == self.model.count - 1);
    
    [cell updateWithModel:self.model[indexPath.row]];
    return cell;
}

- (UISwipeActionsConfiguration *)tableView:(UITableView *)tableView trailingSwipeActionsConfigurationForRowAtIndexPath:(NSIndexPath *)indexPath {
    @weakify(self)
    UIContextualAction *deleteAction = [UIContextualAction contextualActionWithStyle:UIContextualActionStyleDestructive
                                                                               title:NSLocalizedString(@"common.delete", nil)
                                                                           handler:^(UIContextualAction * _Nonnull action,
                                                                                   __kindof UIView * _Nonnull sourceView,
                                                                                   void (^ _Nonnull completionHandler)(BOOL)) {
        @strongify(self)
        AutoPageBlackListModel *model = self.model[indexPath.row];
        DatabaseUnit* unit = [DatabaseUnit removeBlackListWithId:model.uuid];
        [unit setCompleteBlock:^(id result, BOOL success) {
            if (success) {
                [self.model removeObjectAtIndex:indexPath.row];
                [self.tableView deleteRowsAtIndexPaths:@[indexPath] withRowAnimation:UITableViewRowAnimationFade];
                [self showHintMessageIfNeed];
                
                //刷新网页
                [[NSNotificationCenter defaultCenter] postNotificationName:kReloadUserScriptNotification object:nil];
            }
            
            completionHandler(YES);
        }];
        DB_EXEC(unit);
    }];
    
    return [UISwipeActionsConfiguration configurationWithActions:@[deleteAction]];
}

#pragma mark - Lazy Load

- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
        _tableView.delegate = self;
        _tableView.dataSource = self;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.showsVerticalScrollIndicator = NO;
        _tableView.estimatedRowHeight = UITableViewAutomaticDimension;
        _tableView.estimatedSectionHeaderHeight = 0;
        _tableView.estimatedSectionFooterHeight = 0;
        
        [_tableView registerClass:[AutoPageBlackListCell class] forCellReuseIdentifier:NSStringFromClass(AutoPageBlackListCell.class)];
    }
    return _tableView;
}

- (NSMutableArray *)model {
    if (!_model) {
        _model = [NSMutableArray array];
    }
    return _model;
}

@end
