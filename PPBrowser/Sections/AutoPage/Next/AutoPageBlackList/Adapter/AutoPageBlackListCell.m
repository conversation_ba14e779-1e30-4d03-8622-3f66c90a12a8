//
//  AutoPageBlackListCell.m
//  PPBrowser
//
//  Created by qingbin on 2025/2/22.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "AutoPageBlackListCell.h"
#import "Masonry.h"
#import "BrowserUtils.h"
#import "DateHelper.h"
#import "UIColor+Helper.h"
#import "ThemeProtocol.h"
#import "UIView+Helper.h"

@interface AutoPageBlackListCell()

@property (nonatomic, strong) UIView *backView;
@property (nonatomic, strong) UILabel *urlLabel;
@property (nonatomic, strong) UILabel *timeLabel;
@property (nonatomic, strong) UIView *line;
@property (nonatomic, strong) AutoPageBlackListModel *model;

@end

@implementation AutoPageBlackListCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        self.backgroundColor = UIColor.clearColor;
        [self addSubviews];
        [self defineLayout];
    }
    return self;
}

- (void)addSubviews {
    [self.contentView addSubview:self.backView];
    [self.backView addSubview:self.urlLabel];
    [self.backView addSubview:self.timeLabel];
    [self.backView addSubview:self.line];
}

- (void)defineLayout {
    float margin = iPadValue(30, 15);
    float topOffset = iPadValue(20, 15);
    
    [self.backView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.contentView);
    }];
    
    [self.urlLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.backView).offset(margin);
        make.right.equalTo(self.backView).offset(-margin);
        make.top.equalTo(self.backView).offset(topOffset);
    }];
    
    [self.timeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.urlLabel);
        make.top.equalTo(self.urlLabel.mas_bottom).offset(8);
        make.bottom.equalTo(self.backView).offset(-topOffset);
    }];
    
    [self.line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.backView).offset(margin);
        make.right.equalTo(self.backView).offset(-margin);
        make.bottom.equalTo(self.backView);
        make.height.mas_equalTo(0.5);
    }];
}

- (void)updateWithModel:(AutoPageBlackListModel *)model {
    _model = model;
    
    self.urlLabel.text = model.sourceUrl;
    
    NSTimeInterval timeInterval = [model.ctime doubleValue];
    self.timeLabel.text = [DateHelper timeWithDate:timeInterval formatter:NormalDateFormatter];
    
    [self updateCornerRadius];
    [self applyTheme];
}

- (void)updateCornerRadius {
    float cornerRadius = iPadValue(20, 10);
    
    // 圆角
    self.line.hidden = NO;
    self.backView.layer.cornerRadius = cornerRadius;
    self.backView.layer.masksToBounds = YES;
    
    if (self.isFirstInSection && self.isLastInSection) {
        // 只有一个
        self.backView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner | kCALayerMinXMaxYCorner | kCALayerMaxXMaxYCorner;
        self.line.hidden = YES;
    } else {
        if (self.isFirstInSection) {
            // 添加上圆角
            self.backView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
        } else if (self.isLastInSection) {
            // 添加下圆角
            self.backView.layer.maskedCorners = kCALayerMinXMaxYCorner | kCALayerMaxXMaxYCorner;
            self.line.hidden = YES;
        } else {
            self.backView.layer.masksToBounds = NO;
            self.backView.layer.cornerRadius = 0;
        }
    }
}

- (void)applyTheme {
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    
    if (isDarkTheme) {
        self.backView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        self.urlLabel.textColor = UIColor.whiteColor;
        self.timeLabel.textColor = [UIColor colorWithHexString:@"#999999"];
        self.line.backgroundColor = [UIColor colorWithHexString:kDarkThemeColorLine];
    } else {
        self.backView.backgroundColor = UIColor.whiteColor;
        self.urlLabel.textColor = [UIColor colorWithHexString:@"#333333"];
        self.timeLabel.textColor = [UIColor colorWithHexString:@"#666666"];
        self.line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
}

#pragma mark - Lazy Load

- (UIView *)backView {
    if (!_backView) {
        _backView = [[UIView alloc] init];
    }
    return _backView;
}

- (UILabel *)urlLabel {
    if (!_urlLabel) {
        _urlLabel = [UIView createLabelWithTitle:@""
                                     textColor:[UIColor colorWithHexString:@"#333333"]
                                       bgColor:UIColor.clearColor
                                      fontSize:iPadValue(18, 16)
                                 textAlignment:NSTextAlignmentLeft
                                         bBold:NO];
        _urlLabel.numberOfLines = 2;
    }
    return _urlLabel;
}

- (UILabel *)timeLabel {
    if (!_timeLabel) {
        _timeLabel = [UIView createLabelWithTitle:@""
                                      textColor:[UIColor colorWithHexString:@"#666666"]
                                        bgColor:UIColor.clearColor
                                       fontSize:iPadValue(15, 13)
                                  textAlignment:NSTextAlignmentLeft
                                          bBold:NO];
    }
    return _timeLabel;
}

- (UIView *)line {
    if (!_line) {
        _line = [[UIView alloc] init];
        _line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
    return _line;
}

@end
