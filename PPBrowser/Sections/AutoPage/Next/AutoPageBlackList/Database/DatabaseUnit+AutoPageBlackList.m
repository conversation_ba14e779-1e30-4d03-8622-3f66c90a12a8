//
//  DatabaseUnit+AutoPageBlackList.m
//  PPBrowser
//
//  Created by qing<PERSON> on 2025/2/22.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "DatabaseUnit+AutoPageBlackList.h"
#import "AutoPageBlackListModel.h"
#import "ReactiveCocoa.h"
#import "FMDatabaseQueue.h"
#import "FMDatabase.h"
#import "AutoPageManager.h"

@implementation DatabaseUnit (AutoPageBlackList)

#pragma mark - 增加和更新

// 插入或更新黑名单规则
+ (DatabaseUnit *)insertOrUpdateBlackList:(AutoPageBlackListModel *)model {
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        
        NSString* ctime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        model.ctime = ctime;
        model.updateTime = ctime;
        
        NSString* command = @"REPLACE INTO t_autopage_blacklist(uuid, sourceUrl, patternUrl, updateTime, ctime) VALUES (?,?,?,?,?)";
        
        BOOL result = [db executeUpdate:command,
                      model.uuid,
                      model.sourceUrl ?: @"",
                      model.patternUrl ?: @"",
                      model.updateTime,
                      model.ctime];
        
        //刷新黑名单缓存
        [[AutoPageManager sharedInstance] reloadBlackList];
         
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
        
    return unit;
}

#pragma mark - 删除

// 删除一个黑名单规则
+ (DatabaseUnit *)removeBlackListWithId:(NSString *)uuid {
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        
        NSString* command = @"DELETE FROM t_autopage_blacklist WHERE uuid=?;";
        BOOL result = [db executeUpdate:command, uuid];
        
        //刷新黑名单缓存
        [[AutoPageManager sharedInstance] reloadBlackList];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

#pragma mark - 查询

// 查询所有黑名单规则
+ (DatabaseUnit *)queryAllBlackLists {
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        
        NSString* command = @"SELECT * FROM t_autopage_blacklist ORDER BY ctime DESC;";
        FMResultSet* set = [db executeQuery:command];
        
        NSMutableArray *array = [[NSMutableArray alloc] init];
        while([set next]) {
            AutoPageBlackListModel* model = [[AutoPageBlackListModel alloc] initWithDictionary:[set resultDictionary] error:nil];
            [array addObject:model];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(array, YES);
            }
        });
    };
    
    return unit;
}

// 检查URL是否在黑名单中
+ (DatabaseUnit *)checkURLInBlackList:(NSString *)url {
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        
        NSString* command = @"SELECT COUNT(*) as count FROM t_autopage_blacklist WHERE patternUrl LIKE ?;";
        FMResultSet* set = [db executeQuery:command, [NSString stringWithFormat:@"%%%@%%", url]];
        
        BOOL exists = NO;
        if([set next]) {
            exists = [set intForColumn:@"count"] > 0;
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(@(exists), YES);
            }
        });
    };
    
    return unit;
}

@end
