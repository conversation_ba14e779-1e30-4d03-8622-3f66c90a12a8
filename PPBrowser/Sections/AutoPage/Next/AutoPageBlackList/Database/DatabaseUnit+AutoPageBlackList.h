//
//  DatabaseUnit+AutoPageBlackList.h
//  PPBrowser
//
//  Created by qingbin on 2025/2/22.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "DatabaseUnit.h"

@class AutoPageBlackListModel;

NS_ASSUME_NONNULL_BEGIN

@interface DatabaseUnit (AutoPageBlackList)

/**
 * 插入或更新黑名单规则
 * @param model 黑名单规则模型
 * @return DatabaseUnit实例
 */
+ (DatabaseUnit *)insertOrUpdateBlackList:(AutoPageBlackListModel *)model;

/**
 * 删除一个黑名单规则
 * @param uuid 规则ID
 * @return DatabaseUnit实例
 */
+ (DatabaseUnit *)removeBlackListWithId:(NSString *)uuid;

/**
 * 查询所有黑名单规则
 * @return DatabaseUnit实例，completeBlock返回AutoPageBlackListModel数组
 */
+ (DatabaseUnit *)queryAllBlackLists;

/**
 * 检查URL是否在黑名单中
 * @param url 要检查的URL
 * @return DatabaseUnit实例，completeBlock返回BOOL值，表示是否在黑名单中
 */
+ (DatabaseUnit *)checkURLInBlackList:(NSString *)url;

@end

NS_ASSUME_NONNULL_END
