//
//  AutoPageBlackListModel.h
//  PPBrowser
//
//  Created by qing<PERSON> on 2025/2/22.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "BaseModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface AutoPageBlackListModel : BaseModel
//id
@property (nonatomic, strong) NSString *uuid;
//标记的网站
@property (nonatomic, strong) NSString *sourceUrl;
//pattern url, 匹配url的正则表达式
@property (nonatomic, strong) NSString *patternUrl;
//创建时间
@property (nonatomic, strong) NSString *ctime;
// 更新时间，和iCloud的对比
@property (nonatomic, strong) NSString *updateTime;

@end

NS_ASSUME_NONNULL_END
