//
//  AutoPageManualMarkViewModel.m
//  PPBrowser
//
//  Created by qing<PERSON> on 2025/2/22.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "AutoPageManualMarkViewModel.h"

@implementation AutoPageManualMarkViewModel

//根据标记模式获取说明文案
- (NSString *)getDetailWithAutoPageMarkMode:(AutoPageMarkMode)mode
{
    if (mode == AutoPageMarkModeAppend) {
        return NSLocalizedString(@"autopage.append.detail", nil);
    } else if (mode == AutoPageMarkModeAjax) {
        return NSLocalizedString(@"autopage.ajax.detail", nil);
    } else if (mode == AutoPageMarkModeClick) {
        return NSLocalizedString(@"autopage.click.detail", nil);
    }
    
    return @"";
}

@end
