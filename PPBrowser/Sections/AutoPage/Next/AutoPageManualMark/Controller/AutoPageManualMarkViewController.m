//
//  AutoPageManualMarkViewController.m
//  PPBrowser
//
//  Created by qingbin on 2025/2/22.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "AutoPageManualMarkViewController.h"

#import "Masonry.h"
#import "MaizyHeader.h"
#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "ReactiveCocoa.h"
#import "UIView+FrameHelper.h"
#import "UIViewController+Helper.h"
#import "NSObject+Helper.h"

#import "SettingSegmentAndTextView.h"
#import "SettingTextAndArrowView.h"
#import "PPNotifications.h"
#import "BrowserUtils.h"
#import "PPEnums.h"

#import "AutoPageTagitView.h"
#import "AutoPageSettingViewController.h"
#import "BaseNavigationController.h"
#import "BrowserViewController.h"
#import "CommonDataManager.h"
#import "AutoPageModel.h"
#import "AutoPageManager.h"
#import "DatabaseUnit+AutoPage.h"
#import "VIPController.h"

@interface AutoPageManualMarkViewController ()<ThemeProtocol>

@property (nonatomic, weak) Tab *tab;
@property (nonatomic, strong) UIScrollView *scrollView;
@property (nonatomic, strong) UIStackView *stackView;
// 模式选择
@property (nonatomic, strong) SettingSegmentAndTextView *modeSegmentView;
// 标记正文区域
@property (nonatomic, strong) SettingTextAndArrowView *contentAreaView;
// 标记下一页
@property (nonatomic, strong) SettingTextAndArrowView *nextPageView;
// 确定按钮
@property (nonatomic, strong) UIButton *confirmButton;
// 设置按钮
@property (nonatomic, strong) UIButton *settingsButton;
// ViewModel
@property (nonatomic, strong) AutoPageManualMarkViewModel *viewModel;
// 当前正在标记的model
@property (nonatomic, strong) AutoPageModel *currentMarkModel;

@end

@implementation AutoPageManualMarkViewController

- (instancetype)initWithTab:(Tab*)tab {
    self = [super init];
    if(self) {
        self.tab = tab;
        
        self.currentMarkModel = [AutoPageModel new];
        NSString* url = [tab.webView.URL absoluteString];
        self.currentMarkModel.sourceUrl = url;
        self.currentMarkModel.patternUrl = [AutoPageManager generatePatternUrl:url];
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.title = NSLocalizedString(@"autopage.tagit", nil);
    [self addSubviews];
    [self defineLayout];
    [self setupObservers];
    [self updateWithModel];
    [self applyTheme];
    [self createCustomLeftBarButtonItem];
    [self setupNavigationBar];
}

- (void)setupNavigationBar {
//    self.navigationItem.leftBarButtonItem = [[UIBarButtonItem alloc] initWithCustomView:self.infoButton];
    self.navigationItem.rightBarButtonItem = [[UIBarButtonItem alloc] initWithCustomView:self.settingsButton];
}

#pragma mark - Theme Support

- (void)applyTheme {
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if (isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.scrollView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.confirmButton.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
    } else {
        self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.scrollView.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.confirmButton.backgroundColor = UIColor.whiteColor;
    }
}

- (void)themeDidChangeHandler:(BOOL)needReload {
    if(needReload) {
        [self applyTheme];
    }
}

#pragma mark - UI Setup

- (void)addSubviews {
    [self.view addSubview:self.scrollView];
    [self.scrollView addSubview:self.stackView];
    [self.view addSubview:self.confirmButton];
    
    // 将所有视图添加到stackView
    [self.stackView addArrangedSubview:self.modeSegmentView];
    [self.stackView addArrangedSubview:self.contentAreaView];
    [self.stackView addArrangedSubview:self.nextPageView];
}

- (void)defineLayout {
    float margin = iPadValue(30, 15);
    float topOffset = iPadValue(30, 20);
    float buttonHeight = iPadValue(60, 44);
    float buttonTopOffset = iPadValue(30, 20); // 按钮与上方内容的间距
    
    [self.scrollView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.right.equalTo(self.view);
        make.bottom.equalTo(self.view);  // 让scrollView占满整个视图
    }];
    
    // stackView不再设置bottom约束，让它自然撑开
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.scrollView).offset(topOffset);
        make.left.equalTo(self.view).offset(margin);
        make.right.equalTo(self.view).offset(-margin);
        make.width.equalTo(self.scrollView).offset(-2 * margin);
    }];
    
    [self.contentAreaView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([SettingTextAndArrowView height]);
    }];
    
    [self.nextPageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([SettingTextAndArrowView height]);
    }];
    
    // 确定按钮跟随stackView布局
    [self.confirmButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.stackView.mas_bottom).offset(buttonTopOffset);
        make.left.equalTo(self.stackView);
        make.right.equalTo(self.stackView);
        make.height.mas_equalTo(buttonHeight);
        make.bottom.lessThanOrEqualTo(self.scrollView).offset(-topOffset); // 设置scrollView的内容边界
    }];
    
    // 设置圆角
    self.stackView.layer.cornerRadius = 10;
    self.stackView.layer.masksToBounds = YES;
    
    self.confirmButton.layer.cornerRadius = 10;
    self.confirmButton.layer.masksToBounds = YES;
}

- (void)updateWithModel {
    // 模式选择
    [self.modeSegmentView updateWithTitle:NSLocalizedString(@"autopage.mode.title", nil)
                                 detail:@""
                           selectIndex:0];
    
    // 默认显示append模式UI
    [self updateViewsForMode:0];
}

- (void)updateViewsForMode:(AutoPageMarkMode)mode {
    self.currentMarkModel.type = mode;
    
    if (mode == 2) { // click模式
        self.contentAreaView.hidden = YES;
        self.nextPageView.hidden = NO;
    } else { // append或ajax模式
        self.contentAreaView.hidden = NO;
        self.nextPageView.hidden = NO;
    }
    
    //更新说明文案
    [self.modeSegmentView updateWithDetail:[self.viewModel getDetailWithAutoPageMarkMode:mode]];
}

#pragma mark - Event Handlers

- (void)setupObservers {
    @weakify(self)
    self.modeSegmentView.selectIndexBlock = ^(int index) {
        @strongify(self)
        [self updateViewsForMode:index];
    };
    
    [self.contentAreaView setDidAction:^{
        @strongify(self)
        // 处理标记正文区域
        [self handleMarkContentArea];
    }];
    
    [self.nextPageView setDidAction:^{
        @strongify(self)
        // 处理标记下一页
        [self handleMarkNextPage];
    }];
    
    [[self.confirmButton rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(id x) {
        @strongify(self)
        [self handleConfirmAction];
    }];
    
    //设置页
    [[self.settingsButton rac_signalForControlEvents:UIControlEventTouchUpInside]
        subscribeNext:^(id x) {
        @strongify(self)
        [self handleOpenSettings];
    }];
}

#pragma mark - Actions

- (void)handleOpenSettings
{
    AutoPageSettingViewController* vc = [AutoPageSettingViewController new];
    BaseNavigationController* navc = [[BaseNavigationController alloc]initWithRootViewController:vc];
    
//    if ([BrowserUtils isiPad]) {
//        // iPad
//        navc.modalPresentationStyle = UIModalPresentationFormSheet;
//        vc.preferredContentSize = CGSizeMake(kScreenWidth-90, kScreenHeight-90);
//        
//        [self presentViewController:navc animated:YES completion:nil];
//    } else {
//        // iPhone
//        [self presentViewController:navc animated:YES completion:nil];
//    }
    //v2.6.8,统一present
    [self presentCustomToViewController:navc];
}

- (void)handleMarkContentArea {
    //标记正文
    @weakify(self)
    [AutoPageTagitView showWithConfirmHandler:^(NSArray<NSString *> * _Nonnull xpathList) {
        @strongify(self)
        //v2.6.7,正文区域只取第一个
        self.currentMarkModel.pageElementXPath = xpathList.firstObject;
        [self.contentAreaView updateWithContent:NSLocalizedString(@"autopage.mark", nil)];
    }];
    [self dismissAllRelatedViewControllers];
}

- (void)handleMarkNextPage {
    //标记下一页按钮
    [AutoPageTagitView showWithConfirmHandler:^(NSArray<NSString *> * _Nonnull xpathList) {
        //v2.6.7,下一页按钮可以取数组值，通过 | 来组合在一起
        self.currentMarkModel.nextXPath = [xpathList componentsJoinedByString:@" | "];
        [self.nextPageView updateWithContent:NSLocalizedString(@"autopage.mark", nil)];
    }];
    [self dismissAllRelatedViewControllers];
}

// 为了避免代码重复，我们可以抽取一个公共方法
- (void)dismissAllRelatedViewControllers {
    // 查找导航控制器中是否有BrowserViewController
    for (UIViewController *vc in self.navigationController.viewControllers) {
        if ([vc isKindOfClass:[BrowserViewController class]]) {
            [self.navigationController popToViewController:vc animated:YES];
            return;
        }
    }
    
    // 如果没找到BrowserViewController，直接关闭当前页面
    [self dismissViewControllerAnimated:YES completion:nil];
}

- (void)handleConfirmAction {
    // 验证必填字段
    NSString *errorMsg = nil;
    
    switch (self.currentMarkModel.type) {
        case AutoPageMarkModeAppend:
        case AutoPageMarkModeAjax: {
            // Append和Ajax模式需要标记正文区域
            if (self.currentMarkModel.pageElementXPath.length == 0) {
                errorMsg = NSLocalizedString(@"autopage.mark.content", nil);
            } else if (self.currentMarkModel.nextXPath.length == 0) {
                // Append和Ajax模式需要标记下一页按钮
                errorMsg = NSLocalizedString(@"autopage.mark.next", nil);
            }
            break;
        }
        case AutoPageMarkModeClick: {
            // Click模式需要标记下一页按钮
            if (self.currentMarkModel.nextXPath.length == 0) {
                errorMsg = NSLocalizedString(@"autopage.mark.next", nil);
            }
            break;
        }
    }
    
    if (errorMsg) {
        [UIView showToast:errorMsg];
        return;
    }
    
    //会员检测
    BOOL isVip = [VIPController checkIsVipWithMessage:NSLocalizedString(@"vip.alert.autopage.text", nil) controller:self];
    if(!isVip) {
        return;
    }
        
    // 保存到数据库
    DatabaseUnit* unit = [DatabaseUnit insertOrUpdateAutoPage:self.currentMarkModel];
    [unit setCompleteBlock:^(id result, BOOL success) {
        if (success) {
            // 刷新浏览器
            [[NSNotificationCenter defaultCenter] postNotificationName:kReloadUserScriptNotification object:nil];
            
            // 关闭当前页面
            [self dismissAllRelatedViewControllers];
        }
    }];
    DB_EXEC(unit);
}

#pragma mark - Lazy Load

- (UIScrollView *)scrollView {
    if (!_scrollView) {
        _scrollView = [[UIScrollView alloc] init];
        _scrollView.showsVerticalScrollIndicator = NO;
        _scrollView.backgroundColor = UIColor.clearColor;
    }
    return _scrollView;
}

- (UIStackView *)stackView {
    if (!_stackView) {
        _stackView = [[UIStackView alloc] init];
        _stackView.axis = UILayoutConstraintAxisVertical;
        _stackView.spacing = 0;
        _stackView.backgroundColor = UIColor.clearColor;
    }
    return _stackView;
}

- (SettingSegmentAndTextView *)modeSegmentView {
    if (!_modeSegmentView) {
        _modeSegmentView = [[SettingSegmentAndTextView alloc] initWithTitle:NSLocalizedString(@"autopage.mode.title", nil)
                                                                  showLine:YES
                                                                 segments:@[@"Append", @"Ajax", @"Click"]];
        _modeSegmentView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
        _modeSegmentView.layer.cornerRadius = 10;
    }
    return _modeSegmentView;
}

- (SettingTextAndArrowView *)contentAreaView {
    if (!_contentAreaView) {
        _contentAreaView = [[SettingTextAndArrowView alloc] initWithShowLine:YES];
        [_contentAreaView updateWithTitle:NSLocalizedString(@"autopage.mark.content", nil) content:NSLocalizedString(@"autopage.unmark", nil)];
    }
    return _contentAreaView;
}

- (SettingTextAndArrowView *)nextPageView {
    if (!_nextPageView) {
        _nextPageView = [[SettingTextAndArrowView alloc] initWithShowLine:NO];
        [_nextPageView updateWithTitle:NSLocalizedString(@"autopage.mark.next", nil) content:NSLocalizedString(@"autopage.unmark", nil)];
        _nextPageView.layer.maskedCorners = kCALayerMinXMaxYCorner | kCALayerMaxXMaxYCorner;
        _nextPageView.layer.cornerRadius = 10;
    }
    return _nextPageView;
}

- (UIButton *)confirmButton {
    if (!_confirmButton) {
        _confirmButton = [UIButton buttonWithType:UIButtonTypeSystem];
        [_confirmButton setTitle:NSLocalizedString(@"alert.confirm", nil) forState:UIControlStateNormal];
        [_confirmButton setTitleColor:[UIColor systemBlueColor] forState:UIControlStateNormal];
        _confirmButton.titleLabel.font = [UIFont systemFontOfSize:iPadValue(18, 16)];
        _confirmButton.backgroundColor = UIColor.whiteColor;
    }
    return _confirmButton;
}

- (AutoPageManualMarkViewModel *)viewModel
{
    if (!_viewModel) {
        _viewModel = [AutoPageManualMarkViewModel new];
    }
    
    return _viewModel;
}

- (UIButton *)settingsButton {
    if (!_settingsButton) {
        _settingsButton = [UIButton buttonWithType:UIButtonTypeSystem];
        if (@available(iOS 13.0, *)) {
            [_settingsButton setImage:[UIImage systemImageNamed:@"gearshape"] forState:UIControlStateNormal];
        } else {
            [_settingsButton setTitle:NSLocalizedString(@"preference.setting", nil) forState:UIControlStateNormal];
        }
        _settingsButton.tintColor = [UIColor labelColor];
    }
    return _settingsButton;
}

@end
