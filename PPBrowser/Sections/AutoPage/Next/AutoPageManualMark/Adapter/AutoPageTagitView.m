//
//  AutoPageTagitView.m
//  PPBrowser
//
//  Created by qingbin on 2025/2/23.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "AutoPageTagitView.h"

#import "Masonry.h"
#import "ReactiveCocoa.h"

#import "UIColor+Helper.h"
#import "NSObject+Helper.h"
#import "UIView+FrameHelper.h"
#import "MaizyHeader.h"
#import "PPNotifications.h"
#import "BrowserViewController.h"

#import "CustomButton.h"
#import "BrowserUtils.h"
#import "AppDelegate.h"

#import "MarkHelper.h"
#import "NSURL+Extension.h"
#import "CommonDataManager.h"

@interface AutoPageTagitView()

@property(nonatomic,strong) UIStackView* stackView;
//放大
@property(nonatomic,strong) UIButton* expandButton;
//缩小
@property(nonatomic,strong) UIButton* reduceButton;
//确认按钮
@property(nonatomic,strong) UIButton* confirmButton;
//取消按钮
@property(nonatomic,strong) UIButton* cancelButton;
//关闭按钮
@property(nonatomic,strong) UIButton* closeButton;
//
@property(nonatomic,strong) UITapGestureRecognizer* tapGesture;
//
@property(nonatomic,weak) PandaWebView* webView;

@property (nonatomic, assign) CGFloat originY; // 记录原始Y坐标
@property (nonatomic, strong) MASConstraint *bottomConstraint; // 保存底部约束
//确认按钮回调
@property (nonatomic, copy) void (^confirmHandler)(NSArray<NSString *> *xpathList);
@end

@implementation AutoPageTagitView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if(self) {
        [self addSubviews];
        [self defineLayout];
        [self setupObservers];
        
        self.backgroundColor = [UIColor.blackColor colorWithAlphaComponent:0.3];
        if ([BrowserUtils isiPad]) {
            self.layer.cornerRadius = 10;
            self.layer.masksToBounds = YES;
        }
    }
    
    return self;
}

+ (instancetype)showWithConfirmHandler:(void(^)(NSArray<NSString *> *))confirmHandler
{
    AppDelegate *appDelegate = (AppDelegate*) [UIApplication sharedApplication].delegate;
    BrowserViewController* browser = appDelegate.browser;
    if(!browser) return nil;
    
    AutoPageTagitView* view = [AutoPageTagitView new];
    view.confirmHandler = confirmHandler;
    [browser.view addSubview:view];
    
    float height = 50 + 36 + 20;
    float maxWidth = iPadValue(500, kScreenWidth);
    
    [view mas_makeConstraints:^(MASConstraintMaker *make) {
        if ([BrowserUtils isiPad]) {
            make.width.mas_equalTo(maxWidth);
            make.centerX.equalTo(browser.view);
        } else {
            make.left.right.equalTo(browser.view);
        }
        // 保存bottom约束以便后续更新
        view.bottomConstraint = make.bottom.equalTo(browser.view.mas_safeAreaLayoutGuideBottom);
        make.height.mas_equalTo(height);
    }];
        
    return view;
}

- (void)close
{
    AppDelegate *appDelegate = (AppDelegate*) [UIApplication sharedApplication].delegate;
    BrowserViewController* browser = appDelegate.browser;
    if(!browser) return;
    
    [MarkHelper cancelHideElementWithWebView:self.webView];
    
//    self.webView.userInteractionEnabled = YES;
    //通过UI结构分析,webview包着一层WKScrollView, WKScrollView包着一层WKContentView
    //所以只需要禁止WKContentView的响应事件即可
    for(UIView* view in self.webView.scrollView.subviews) {
        //重置
        NSLog(@"class = %@", NSStringFromClass(view.class));
        view.userInteractionEnabled = YES;
    }
    
    [browser.view removeGestureRecognizer:self.tapGesture];
    
    [self removeFromSuperview];
    
    //回到手动标记页面
    [self _showMarkViewController];
}

- (void)setupObservers
{
    AppDelegate *appDelegate = (AppDelegate*) [UIApplication sharedApplication].delegate;
    BrowserViewController* _vc = appDelegate.browser;
    if(!_vc) return;
    
    PandaWebView* webView = _vc.tabManager.selectedTab.webView;
//    webView.userInteractionEnabled = NO;
    self.webView = webView;
    
    //通过UI结构分析,webview包着一层WKScrollView, WKScrollView包着一层WKContentView
    //所以只需要禁止WKContentView的响应事件即可
    for(UIView* view in webView.scrollView.subviews) {
        NSLog(@"class = %@", NSStringFromClass(view.class));
        view.userInteractionEnabled = NO;
    }
    
    @weakify(self)
    [[self.closeButton rac_signalForControlEvents:UIControlEventTouchUpInside]
    subscribeNext:^(id x) {
        @strongify(self)
        [self close];
    }];
    
    [[self.expandButton rac_signalForControlEvents:UIControlEventTouchUpInside]
    subscribeNext:^(id x) {
        //扩大区域
        [MarkHelper showParentElementMaskWithWebView:webView];
    }];
    
    [[self.reduceButton rac_signalForControlEvents:UIControlEventTouchUpInside]
    subscribeNext:^(id x) {
        //缩小区域
        [MarkHelper showTheChildElementMaskWithWebView:webView];
    }];
    
    [[self.confirmButton rac_signalForControlEvents:UIControlEventTouchUpInside]
    subscribeNext:^(id x) {
        @strongify(self)
        //确定按钮
        [self _handleConfirm];
    }];
    
    [[self.cancelButton rac_signalForControlEvents:UIControlEventTouchUpInside]
    subscribeNext:^(id x) {
        @strongify(self)
        //取消按钮
        [self _handleCancel];
    }];
    
    UITapGestureRecognizer* tap = [UITapGestureRecognizer new];
    [_vc.view addGestureRecognizer:tap];
    [tap.rac_gestureSignal subscribeNext:^(id x) {
        CGPoint pt = [tap locationInView:webView];
        [MarkHelper showElementMaskWithWebView:webView point:pt];
    }];
    self.tapGesture = tap;
    
    // 添加拖动手势
    UIPanGestureRecognizer *pan = [[UIPanGestureRecognizer alloc] initWithTarget:self action:@selector(handlePan:)];
    [self addGestureRecognizer:pan];
    
    //拦截事件点击
    tap = [UITapGestureRecognizer new];
    [self addGestureRecognizer:tap];
}

- (void)handlePan:(UIPanGestureRecognizer *)pan {
    AppDelegate *appDelegate = (AppDelegate*) [UIApplication sharedApplication].delegate;
    BrowserViewController* browser = appDelegate.browser;
    if(!browser) return;
    
    CGPoint translation = [pan translationInView:browser.view];
    
    switch (pan.state) {
        case UIGestureRecognizerStateBegan: {
            // 记录开始拖动时的位置
            self.originY = CGRectGetMinY(self.frame);
            break;
        }
        case UIGestureRecognizerStateChanged: {
            // 计算新的位置
            CGFloat newY = self.originY + translation.y;
            CGFloat maxY = CGRectGetHeight(browser.view.frame) - CGRectGetHeight(self.frame);
            CGFloat minY = browser.view.safeAreaInsets.top;
            
            // 限制移动范围
            newY = MAX(minY, MIN(newY, maxY));
            
            // 更新约束
            [self mas_updateConstraints:^(MASConstraintMaker *make) {
                // 使用bottom约束，这样在键盘弹出时也能正常工作
                CGFloat bottomOffset = CGRectGetHeight(browser.view.frame) - newY - CGRectGetHeight(self.frame);
                self.bottomConstraint.offset(-bottomOffset);
            }];
            
            // 立即更新布局
            [self.superview layoutIfNeeded];
            break;
        }
        default:
            break;
    }
}

//确定
- (void)_handleConfirm
{
    //获取当前标记的元素
    if (self.confirmHandler) {
        self.confirmHandler([CommonDataManager shareInstance].markXPathList);
    }
    
    //关闭标记view
    [self close];
}

//取消
- (void)_handleCancel
{
    //关闭标记view
    [self close];
}

//回到手动标记页面
- (void)_showMarkViewController
{
    //回到手动标记页面
    AppDelegate *appDelegate = (AppDelegate*) [UIApplication sharedApplication].delegate;
    BrowserViewController* browser = appDelegate.browser;
    if(!browser) return;
    
    AutoPageManualMarkViewController* vc = [CommonDataManager shareInstance].markAutoPageViewController;
    BaseNavigationController* navc = [[BaseNavigationController alloc]initWithRootViewController:vc];
    
    if ([BrowserUtils isiPhone] && ![[BrowserUtils shareInstance] isLandscape]) {
        // iPhone竖屏 - 使用半模态弹窗
        if (@available(iOS 15.0, *)) {
            UISheetPresentationController* sheet = navc.sheetPresentationController;
            sheet.detents = @[
                UISheetPresentationControllerDetent.mediumDetent,
                UISheetPresentationControllerDetent.largeDetent
            ];
            sheet.preferredCornerRadius = 10;
        }
        [browser presentViewController:navc animated:YES completion:nil];
        
    } else if ([BrowserUtils isiPad]) {
        // iPad
//        [browser presentViewController:navc animated:YES completion:nil];
        [browser presentCustomToViewController:navc];
    } else {
        // iPhone横屏 - 使用普通Push
        [browser.navigationController pushViewController:vc animated:YES];
    }
}

#pragma mark - layout

- (void)addSubviews
{
    [self addSubview:self.stackView];
    [self addSubview:self.closeButton];
}

- (void)defineLayout
{
    [self.closeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.right.mas_offset(0);
        make.size.mas_equalTo(44);
    }];
    
    float spacing = iPadValue(30, 15);
    float margin = iPadValue(40, 20);
    float maxWidth = iPadValue(500, kScreenWidth);
    float buttonWidth = (MIN(maxWidth, kScreenWidth)-spacing*3-margin*2) / 4.0;
    float buttonHeight = 36;
    
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_offset(50);
        make.centerX.mas_offset(0);
    }];
    
    NSArray* buttons = @[self.expandButton, self.reduceButton, self.confirmButton, self.cancelButton];
    for(UIButton* button in buttons) {
        [button mas_makeConstraints:^(MASConstraintMaker *make) {
            make.size.mas_equalTo(CGSizeMake(buttonWidth, buttonHeight));
        }];
    }
}

- (UIStackView *)stackView
{
    if(!_stackView) {
        _stackView = [[UIStackView alloc]initWithArrangedSubviews:@[
            self.expandButton,
            self.reduceButton,
            self.confirmButton,
            self.cancelButton
        ]];
        
        _stackView.backgroundColor = UIColor.clearColor;
        _stackView.axis = UILayoutConstraintAxisHorizontal;
        _stackView.spacing = iPadValue(30, 15);
        _stackView.distribution = UIStackViewDistributionEqualCentering;
    }
    
    return _stackView;
}

- (UIButton *)closeButton
{
    if(!_closeButton) {
        _closeButton = [UIButton new];
        
        UIImage* image = [UIImage imageNamed:@"tagit_close_icon"];
        image = [image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        [_closeButton setImage:image forState:UIControlStateNormal];
        _closeButton.tintColor = UIColor.whiteColor;
    }
    
    return _closeButton;
}

- (UIButton *)createButtonWithTitle:(NSString *)title
{
    UIButton* button = [UIButton new];
    [button setTitle:title forState:UIControlStateNormal];
    [button setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
    button.titleLabel.font = [UIFont systemFontOfSize:iPadValue(18, 16)];
    
    button.backgroundColor = [UIColor.blackColor colorWithAlphaComponent:0.6];
    
//    button.layer.cornerRadius = 36/2.0;
    button.layer.cornerRadius = 8.0;
    button.layer.masksToBounds = YES;
    
    return button;
}

- (UIButton *)expandButton
{
    if(!_expandButton) {
        _expandButton = [self createButtonWithTitle:NSLocalizedString(@"tagit.expand", nil)];
    }
    
    return _expandButton;
}

- (UIButton *)reduceButton
{
    if(!_reduceButton) {
        _reduceButton = [self createButtonWithTitle:NSLocalizedString(@"tagit.reduce", nil)];
    }
    
    return _reduceButton;
}

- (UIButton *)confirmButton
{
    if(!_confirmButton) {
        _confirmButton = [self createButtonWithTitle:NSLocalizedString(@"alert.confirm", nil)];
    }
    
    return _confirmButton;
}

- (UIButton *)cancelButton
{
    if(!_cancelButton) {
        _cancelButton = [self createButtonWithTitle:NSLocalizedString(@"alert.cancel", nil)];
    }
    
    return _cancelButton;
}

@end
