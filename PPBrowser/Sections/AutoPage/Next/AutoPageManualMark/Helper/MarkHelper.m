//
//  MarkHelper.m
//  PPBrowser
//
//  Created by qingbin on 2025/2/23.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "MarkHelper.h"

#import "Tab.h"
#import "PPNotifications.h"
#import "BaseNavigationController.h"

#import "UIView+Helper.h"
#import "NSObject+Helper.h"

#import "TagitManager.h"
#import "CopyrightHelper.h"

#import "NSURL+Extension.h"
#import "NSString+Helper.h"

#import "PreferenceManager.h"
#import "CommonDataManager.h"

@interface MarkHelper ()

@property (nonatomic, weak) Tab *tab;

@end

@implementation MarkHelper

- (instancetype)initWithTab:(Tab*)tab
{
    self = [super init];
    if(self) {
        self.tab = tab;
    }
    
    return self;
}

+ (void)showElementMaskWithWebView:(WKWebView*)webView point:(CGPoint)point
{
    NSString* js = [NSString stringWithFormat:@"window.__firefox__.markHelper.showElementMaskWithPoint(\"%f\",\"%f\")",point.x, point.y];
    [webView evaluateJavaScript:js completionHandler:^(id _Nullable obj, NSError * _Nullable error) {
        if(error) {
            NSLog(@"error = %@", error.localizedDescription);
        }
    }];
}

+ (void)showParentElementMaskWithWebView:(WKWebView*)webView
{
    NSString* js = [NSString stringWithFormat:@"window.__firefox__.markHelper.showParentElementMask()"];
    [webView evaluateJavaScript:js completionHandler:^(id _Nullable obj, NSError * _Nullable error) {
        if(error) {
            NSLog(@"error = %@", error.localizedDescription);
        }
    }];
}

+ (void)showTheChildElementMaskWithWebView:(WKWebView*)webView
{
    NSString* js = [NSString stringWithFormat:@"window.__firefox__.markHelper.showTheChildElementMask()"];
    [webView evaluateJavaScript:js completionHandler:^(id _Nullable obj, NSError * _Nullable error) {
        if(error) {
            NSLog(@"error = %@", error.localizedDescription);
        }
    }];
}

+ (void)cancelHideElementWithWebView:(WKWebView*)webView
{
    NSString* js = [NSString stringWithFormat:@"window.__firefox__.markHelper.cancelElementMask()"];
    [webView evaluateJavaScript:js completionHandler:^(id _Nullable obj, NSError * _Nullable error) {
        if(error) {
            NSLog(@"error = %@", error.localizedDescription);
        }
    }];
}

#pragma mark - TabContentScript

- (NSString*)name
{
    return @"markHelper";
}

- (NSString*)scriptMessageHandlerName
{
    return @"markHelper";
}

- (void)userContentController:(WKUserContentController*)userContentController didReceiveScriptMessage:(WKScriptMessage*)message
{
    if(!self.tab || !self.tab.webView) return;
    PandaWebView* webView = self.tab.webView;
    
    NSDictionary* params = message.body;
    int type = [params[@"type"] intValue];
    NSArray<NSString *>* markXPathList = params[@"xpath"];
    if (markXPathList.count > 0) {
        [CommonDataManager shareInstance].markXPathList = markXPathList;
    }
    
    NSLog(@"MarkHelper ...... %d, %s, param = %@",__LINE__, __func__, params);
}

@end
