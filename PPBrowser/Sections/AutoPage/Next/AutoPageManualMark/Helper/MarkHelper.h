//
//  MarkHelper.h
//  PPBrowser
//
//  Created by qing<PERSON> on 2025/2/23.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>

#import "TabContentScript.h"
@class Tab;

NS_ASSUME_NONNULL_BEGIN
//通用类，专门用来处理选择Html中的元素
@interface MarkHelper : NSObject<TabContentScript>

- (instancetype)initWithTab:(Tab*)tab;

+ (void)showElementMaskWithWebView:(WKWebView*)webView point:(CGPoint)point;
+ (void)showParentElementMaskWithWebView:(WKWebView*)webView;
+ (void)showTheChildElementMaskWithWebView:(WKWebView*)webView;
+ (void)cancelHideElementWithWebView:(WKWebView*)webView;

@end

NS_ASSUME_NONNULL_END
