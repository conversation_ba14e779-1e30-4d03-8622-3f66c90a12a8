//
//  AutoPageModel.h
//  PPBrowser
//
//  Created by qing<PERSON> on 2025/2/20.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "BaseModel.h"
#import "PPEnums.h"

@interface AutoPageModel : BaseModel
//id
@property (nonatomic, strong) NSString *uuid;
//标记的网站
@property (nonatomic, strong) NSString *sourceUrl;
//pattern url, 匹配url的正则表达式
@property (nonatomic, strong) NSString *patternUrl;
//类型,分为append/ajax/click, 对应next+element,click+ajax和next+none
@property (nonatomic, assign) AutoPageMarkMode type;
//pageElement,xpath
@property (nonatomic, strong) NSString *pageElementXPath;
//next,xpath, 分别对应nextLink或者clickElemen, 完全以路径生成的xpath
// v.2.6.7, 包括2种情况，一个是纯路径，一个是文本的形式，(数组，以逗号分割)
@property (nonatomic, strong) NSString *nextXPath;
//是否激活，默认true
@property (nonatomic, assign) BOOL isActive;

//创建时间
@property (nonatomic, strong) NSString *ctime;
// 更新时间，和iCloud的对比
@property (nonatomic, strong) NSString *updateTime;

@end

