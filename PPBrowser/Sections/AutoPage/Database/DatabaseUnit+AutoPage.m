//
//  DatabaseUnit+AutoPage.m
//  PPBrowser
//
//  Created by qingbin on 2025/2/20.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "DatabaseUnit+AutoPage.h"
#import "ReactiveCocoa.h"
#import "FMDatabaseQueue.h"
#import "FMDatabase.h"
#import "AutoPageManager.h"

@implementation DatabaseUnit (AutoPage)

// 插入或更新自动翻页规则
+ (DatabaseUnit *)insertOrUpdateAutoPage:(AutoPageModel *)item
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        
        NSString* ctime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        if (item.ctime.length == 0) {
            item.ctime = ctime;
        }
        item.updateTime = ctime;
        
        NSString* command = @"REPLACE INTO t_autopage_list(uuid, sourceUrl, patternUrl, type, isActive, pageElementXPath, nextXPath, updateTime, ctime) VALUES (?,?,?,?,?,?,?,?,?)";
        
        BOOL result = [db executeUpdate:command,
                      item.uuid,
                      item.sourceUrl ?: @"",
                      item.patternUrl ?: @"",
                      @(item.type),
                      @(item.isActive),
                      item.pageElementXPath ?: @"",
                      item.nextXPath ?: @"",
                      item.updateTime,
                      item.ctime];
        
        //刷新缓存列表
        [[AutoPageManager sharedInstance] reloadUserMarkRules];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    

    
    return unit;
}

// 删除一个自动翻页规则
+ (DatabaseUnit *)removeAutoPageWithId:(NSString *)uuid
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        
        NSString* command = [NSString stringWithFormat:@"DELETE FROM t_autopage_list WHERE uuid=?;"];
        BOOL result = [db executeUpdate:command, uuid];
        
        //刷新缓存列表
        [[AutoPageManager sharedInstance] reloadUserMarkRules];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

// 查询所有自动翻页规则
+ (DatabaseUnit*)queryAllAutoPages
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = [NSString stringWithFormat:@"SELECT * FROM t_autopage_list ORDER BY ctime DESC"];
        FMResultSet* set = [db executeQuery:command];
        
        NSMutableArray *array = [[NSMutableArray alloc] init];
        while([set next]) {
            AutoPageModel* model = [[AutoPageModel alloc]initWithDictionary:[set resultDictionary] error:nil];
            [array addObject:model];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(array, YES);
            }
        });
    };
    
    return unit;
}

// 更新自动翻页规则
+ (DatabaseUnit *)updateAutoPageWithItem:(AutoPageModel *)item
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        
        NSString* command = @"UPDATE t_autopage_list SET sourceUrl=?, patternUrl=?, type=?, isActive=?, pageElementXPath=?, next=?, updateTime=? WHERE uuid=?;";
        item.updateTime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        
        BOOL result = [db executeUpdate:command,
                      item.sourceUrl ?: @"",
                      item.patternUrl ?: @"",
                      @(item.type),
                      @(item.isActive),
                      item.pageElementXPath ?: @"",
                      item.nextXPath ?: @"",
                      item.updateTime,
                      item.uuid];
        
        //刷新缓存列表
        [[AutoPageManager sharedInstance] reloadUserMarkRules];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}


@end 
