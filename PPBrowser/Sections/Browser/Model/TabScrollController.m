//
//  TabScrollController.m
//  PPBrowser
//
//  Created by qingbin on 2022/4/24.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "TabScrollController.h"

#import "MaizyHeader.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"

#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "NSObject+Helper.h"

#import "TopToolbar.h"
#import "Tab.h"
#import "BottomToolbar.h"

#import "PreferenceManager.h"
#import "PreferenceItemModel.h"

#import "BrowserUtils.h"
#import "TopToolbarForiPad.h"

#import "CommonDataManager.h"

typedef NS_ENUM(NSInteger,ScrollDirection) {
    ScrollDirectionUp = 1,
    ScrollDirectionDown = 2,
};

typedef NS_ENUM(NSInteger,ToolbarState) {
    ToolbarStateCollapsed = 1,
    ToolbarStateVisible = 2,
    ToolbarStateAnimating = 3,
};

@interface TabScrollController ()<UIScrollViewDelegate,UIGestureRecognizerDelegate>

@property (nonatomic, assign) ToolbarState toolbarState;

@property (nonatomic, assign) ScrollDirection scrollDirection;
// topToolbar离顶部safearea's top的距离
@property (nonatomic, assign) float headerTopOffset;
//
@property (nonatomic, assign) float footerBottomOffset;

@property (nonatomic, assign) float lastContentOffset;

@property (nonatomic, weak) UIScrollView *scrollView;

// topToolbar的高度 44 (固定值)
@property (nonatomic, assign) float topScrollHeight;
// bottomToolbar顶部离手机底部的高度 84 (固定值)
@property (nonatomic, assign) float bottomScrollHeight;

@end

@implementation TabScrollController

- (instancetype)init
{
    self = [super init];
    if(self) {
        if([BrowserUtils isiPad]) {
            self.topScrollHeight = [TopToolbarForiPad topScrollHeight];
        } else {
            self.topScrollHeight = [TopToolbar topScrollHeight];
        }
        
        self.bottomScrollHeight = [BottomToolbar toolbarHeight];
        
        ///锁定全屏相关逻辑
        [CommonDataManager shareInstance].scrollController = self;
    }
    
    return self;
}

- (void)setTab:(Tab *)tab
{
    _tab = tab;
    
    [self setupObservers];
}

- (void)setupObservers
{
    self.scrollView.delegate = nil;
    [self.scrollView.panGestureRecognizer removeTarget:self action:nil];
    
    self.scrollView.delegate = self;
    [self.scrollView.panGestureRecognizer addTarget:self action:@selector(handlePan:)];
}

- (BOOL)tabIsLoading
{
    return self.tab.webView.isLoading;
}

- (void)handlePan:(UIPanGestureRecognizer*)gesture
{
    ///锁定全屏相关逻辑
    if([CommonDataManager shareInstance].isLockFullScreen) return;
    
    if([self tabIsLoading]) return;
    
    UIView* containerView = self.scrollView.superview;
    CGPoint translation = [gesture translationInView:containerView];
    float delta = self.lastContentOffset - translation.y;
    
    if(delta > 0) {
        self.scrollDirection = ScrollDirectionDown;
    } else if (delta < 0){
        self.scrollDirection = ScrollDirectionUp;
    }
    
    self.lastContentOffset = translation.y;
    if([self checkRubberbandingForDelta:delta] && [self checkScrollHeightIsLargeEnoughForScrolling]) {
        BOOL bottomIsNotRubberbanding = self.scrollView.contentOffset.y + self.scrollView.frame.size.height < self.scrollView.contentSize.height;
        BOOL topIsRubberbanding = self.scrollView.contentOffset.y <= 0;
        if(bottomIsNotRubberbanding && (self.toolbarState != ToolbarStateCollapsed || topIsRubberbanding)) {
            [self scrollWithDelta:delta];
        }
        
        if(self.headerTopOffset == -self.topScrollHeight && self.footerBottomOffset == self.bottomScrollHeight) {
            self.toolbarState = ToolbarStateCollapsed;
        } else if(self.headerTopOffset == 0) {
            self.toolbarState = ToolbarStateVisible;
        } else {
            self.toolbarState = ToolbarStateAnimating;
        }
    }
    
    if(gesture.state == UIGestureRecognizerStateEnded || gesture.state == UIGestureRecognizerStateCancelled) {
        self.lastContentOffset = 0;
    }
}

- (void)showToolBars:(BOOL)animated
{
    //v2.6.2 修复锁定全屏失败的BUG
//    if(self.toolbarState == ToolbarStateVisible) {
//        return;
//    }
    
    self.toolbarState = ToolbarStateVisible;
    float durationRatio = fabsf(self.headerTopOffset/self.topScrollHeight);
    float actualDuration = 0.2*durationRatio;
    [self animateToolbarsWithOffsets:actualDuration
                        headerOffset:0
                        footerOffset:0
                               alpha:1];
}

- (void)hideToolbars
{
    //v2.6.2 修复锁定全屏失败的BUG
//    if(self.toolbarState == ToolbarStateCollapsed) {
//        return;
//    }
    
    self.toolbarState = ToolbarStateCollapsed;
    float durationRatio = fabsf((self.headerTopOffset+self.topScrollHeight)/self.topScrollHeight);
    float actualDuration = 0.2*durationRatio;
    [self animateToolbarsWithOffsets:actualDuration
                        headerOffset:-self.topScrollHeight
                        footerOffset:self.bottomScrollHeight
                               alpha:0];
}

- (void)animateToolbarsWithOffsets:(float)duration
                      headerOffset:(float)headerOffset
                      footerOffset:(float)footerOffset
                             alpha:(float)alpha
{
    self.headerTopOffset = headerOffset;
    self.footerBottomOffset = footerOffset;
        
    if(alpha >= 0.99) {
        [self.topToolbar showSearchBar];
    } else {
        [self.topToolbar hideSearchBar];
    }
}

- (BOOL)isBouncingAtBottom
{
    return self.scrollView.contentOffset.y > (self.scrollView.contentSize.height - self.scrollView.frame.size.height) && self.scrollView.contentSize.height > self.scrollView.frame.size.height;
}

- (BOOL)checkRubberbandingForDelta:(float)delta
{
    return !((delta < 0 && self.scrollView.contentOffset.y + self.scrollView.frame.size.height > self.scrollView.contentSize.height &&
              self.scrollView.frame.size.height < self.scrollView.contentSize.height) ||
             self.scrollView.contentOffset.y < delta);
}

- (void)scrollWithDelta:(float)delta
{
    if(self.scrollView.frame.size.height > self.scrollView.contentSize.height) return;
    
    float updatedOffset = self.headerTopOffset - delta;
    self.headerTopOffset = [self clamp:updatedOffset min:-self.topScrollHeight max:0];
    if([self isHeaderDisplayedForGivenOffset:updatedOffset]) {
        CGPoint offset = CGPointMake(self.scrollView.contentOffset.x, self.scrollView.contentOffset.y - delta);
        self.scrollView.contentOffset = offset;
    }
    
    updatedOffset = self.footerBottomOffset + delta;
    self.footerBottomOffset = [self clamp:updatedOffset min:0 max:self.bottomScrollHeight];
    
    float alpha = 1 - fabsf(self.headerTopOffset/self.topScrollHeight);
//    self.topToolbar.alpha = alpha;
    
    if(alpha >= 0.99) {
        [self.topToolbar showSearchBar];
    } else {
        [self.topToolbar hideSearchBar];
    }
}

- (BOOL)checkScrollHeightIsLargeEnoughForScrolling
{
    // 主要是为了适配iPhone横屏启动, 导致宽高错乱的问题
    float screenHeight = [[BrowserUtils shareInstance] transitionToSize].height;
    
    return (screenHeight + 2*44) < self.scrollView.contentSize.height;
}

- (float)isHeaderDisplayedForGivenOffset:(float)offset
{
    return offset > -self.topScrollHeight && offset < 0;
}

- (float)clamp:(float)y min:(float)min max:(float)max
{
    if(y >= max) {
        return max;
    } else if(y <= min){
        return min;
    }
    return y;
}

- (void)setHeaderTopOffset:(float)headerTopOffset
{
    _headerTopOffset = headerTopOffset;
    
//    CGRect frame = self.topToolbar.frame;
//    CGPoint origin = frame.origin;
//    origin.y = headerTopOffset;
//    frame.origin = origin;
//    
//    self.topToolbar.frame = frame;
    [self.topToolbar mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.mas_offset(headerTopOffset).priorityHigh();
    }];
    [self.topToolbar layoutIfNeeded];
}

- (void)setFooterBottomOffset:(float)footerBottomOffset
{
    _footerBottomOffset = footerBottomOffset;
    
    //iPad不走这个逻辑
    if([BrowserUtils isiPad]) return;
    
    //iPhone横竖屏切换逻辑，如果是横屏，那么也不走这个逻辑
    if([[BrowserUtils shareInstance] isLandscape]) {
        return;
    }
//    if([BrowserUtils isiPhone]) {
//        if(kScreenWidth > kScreenHeight) return;
//    }
    
    //非全屏也不走这个逻辑(锁定全屏操作，会自动开启全屏模式)
    BOOL isFullScreen = [[PreferenceManager shareInstance].items.isFullScreen boolValue];
    if(!isFullScreen) return;
    
    // 主要是为了适配iPhone横屏启动, 导致宽高错乱的问题
//    float screenHeight = [[BrowserUtils shareInstance] transitionToSize].height;
//    
//    float height = [BottomToolbar toolbarHeight];
//    float offsetY = screenHeight - height;
    
//    CGRect frame = self.bottomToolbar.frame;
//    CGPoint origin = frame.origin;
//    origin.y = offsetY + footerBottomOffset;
//    frame.origin = origin;
//    
//    self.bottomToolbar.frame = frame;
    [self.bottomToolbar mas_updateConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_offset(footerBottomOffset).priorityHigh();
    }];
    [self.bottomToolbar layoutIfNeeded];
}

- (UIScrollView *)scrollView
{
    return self.tab.webView.scrollView;
}

#pragma mark -- UIGestureRecognizerDelegate
- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldRecognizeSimultaneouslyWithGestureRecognizer:(UIGestureRecognizer *)otherGestureRecognizer
{
    return YES;
}

#pragma mark - UIScrollViewDelegate

- (void)scrollViewDidEndDragging:(UIScrollView *)scrollView willDecelerate:(BOOL)decelerate
{
    ///锁定全屏相关逻辑
    if([CommonDataManager shareInstance].isLockFullScreen) return;
    
    if([self tabIsLoading] || [self isBouncingAtBottom]) {
        return;
    }
    
    if((decelerate || (self.toolbarState == ToolbarStateAnimating && !decelerate)) && [self checkScrollHeightIsLargeEnoughForScrolling]) {
        if(self.scrollDirection == ScrollDirectionUp) {
            [self showToolBars:YES];
        } else if(self.scrollDirection == ScrollDirectionDown) {
            [self hideToolbars];
        }
    }
}

- (BOOL)scrollViewShouldScrollToTop:(UIScrollView *)scrollView
{
    ///点击顶部退出全屏模式
    ///锁定全屏相关逻辑
    if([CommonDataManager shareInstance].isLockFullScreen
       && self.toolbarState == ToolbarStateCollapsed) {
        //退出锁定全屏逻辑
        [CommonDataManager shareInstance].isLockFullScreen = NO;
        return NO;
    }
    
    if(self.toolbarState == ToolbarStateCollapsed) {
        [self showToolBars:YES];
        return NO;
    }
    
    return YES;
}

@end
