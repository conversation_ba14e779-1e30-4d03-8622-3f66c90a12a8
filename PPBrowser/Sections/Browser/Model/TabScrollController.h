//
//  TabScrollController.h
//  PPBrowser
//
//  Created by qingbin on 2022/4/24.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>
@class Tab;
@class TopToolbar;
@class BottomToolbar;

NS_ASSUME_NONNULL_BEGIN

@interface TabScrollController : NSObject

@property (nonatomic, strong) TopToolbar *topToolbar;

@property (nonatomic, strong) BottomToolbar *bottomToolbar;

@property (nonatomic, strong) Tab* tab;

- (void)hideToolbars;

- (void)showToolBars:(BOOL)animated;

@end

NS_ASSUME_NONNULL_END
