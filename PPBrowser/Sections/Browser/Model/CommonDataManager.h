//
//  CommonDataManager.h
//  PPBrowser
//
//  Created by qingbin on 2023/8/6.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

#import "TabScrollController.h"
#import "PlayerView.h"
#import "PPEnums.h"

#import "BookMarkModel.h"
#import "CustomTagModel.h"
#import "AutoPageManualMarkViewController.h"

@interface CommonDataManager : NSObject

+ (instancetype)shareInstance;

/// 锁定全屏
@property (nonatomic, assign) BOOL isLockFullScreen;

@property (nonatomic, weak) TabScrollController* scrollController;

//截图保存失败(图片太大了)，用来压缩图片
@property (nonatomic, strong) UIImage* screenshotImage;

//复制链接后的PlayModel
@property (nonatomic, strong) PlayModel* currentPlayModel;

//是否正在播放视频，用来控制是否跟随屏幕旋转
@property (nonatomic, assign) BOOL isPlayVideo;
//添加了播放器
- (void)addPlayer:(PlayerView *)player;
//播放器释放
- (void)endPlayer:(PlayerView *)player;

//当前翻译状态
@property (nonatomic, assign) TranslateStatus translateStatus;

//浏览器首页标签所有model,用于判断当前网页是否加入了首页标签
@property (nonatomic, strong) NSArray *customTagModels;
//当前网页对应的favorModel
@property (nonatomic, strong) CustomTagModel *currentCustomTagModel;
//重新加载首页model
- (void)reloadCustomTagModels;

//浏览器所有书签model,用于判断前网页是否加入了书签
@property (nonatomic, strong) NSArray *bookMarks;
//当前网页对应的书签
@property (nonatomic, strong) BookMarkModel *currentBookMarkModel;
//重新加载书签
- (void)reloadBookMarks;

//自动翻页当前标记的viewcontroller
@property (nonatomic, strong) AutoPageManualMarkViewController *markAutoPageViewController;
@property (nonatomic, strong) NSArray *markXPathList;

//2.6.9 屏幕常亮,默认关闭
@property (nonatomic, assign) BOOL isIdleTimerEnabled;

//切换到全屏
+ (void)lockFullScreen;

@end
