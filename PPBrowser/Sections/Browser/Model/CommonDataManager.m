//
//  CommonDataManager.m
//  PPBrowser
//
//  Created by qingbin on 2023/8/6.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import "CommonDataManager.h"

#import "ReactiveCocoa.h"
#import "Masonry.h"

#import "NSObject+Helper.h"
#import "AppDelegate.h"
#import "BrowserViewController.h"

#import "DatabaseUnit+BookMark.h"
#import "DatabaseUnit+CustomTag.h"

@interface CommonDataManager ()

@property (nonatomic, strong) NSMutableArray* playerArray;
//退出锁屏,底部安全区域
@property (nonatomic, weak) UIView* bottomLockFullScreenView;

@end

@implementation CommonDataManager

+ (instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    static CommonDataManager* obj;
    dispatch_once(&onceToken, ^{
        obj = [CommonDataManager new];
    });
    
    return obj;
}

- (instancetype)init
{
    self = [super init];
    if(self) {
        self.playerArray = [NSMutableArray array];
    }
    
    return self;
}

- (void)setIsLockFullScreen:(BOOL)isLockFullScreen
{
    if(isLockFullScreen) {
        [self.scrollController hideToolbars];
        [self setupLockScreenView];
    } else {
        [self.scrollController showToolBars:YES];
        
        [self _removeLockFullScreenView];
    }
    
    _isLockFullScreen = isLockFullScreen;
}

//切换到全屏
+ (void)lockFullScreen
{
    //切换到全屏
    [PreferenceManager shareInstance].items.isFullScreen = @(YES);
    [[PreferenceManager shareInstance] encode];
    
    //锁定全屏
    [CommonDataManager shareInstance].isLockFullScreen = YES;
}

#pragma mark - 添加底部退出全屏手势view

- (void)setupLockScreenView
{
    [self _removeLockFullScreenView];
    
    UIView* lockFullScreenView = [UIView new];
    self.bottomLockFullScreenView = lockFullScreenView;
    
    UIWindow* window = [NSObject normalWindow];
    [window addSubview:lockFullScreenView];
    
    float height = window.safeAreaInsets.bottom;
    if(height <= 0.0f) height = 20;
    
    [lockFullScreenView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.bottom.equalTo(window);
        make.height.mas_equalTo(height);
    }];
    
    UITapGestureRecognizer* tap = [UITapGestureRecognizer new];
    [lockFullScreenView addGestureRecognizer:tap];
    @weakify(self)
    [tap.rac_gestureSignal subscribeNext:^(id x) {
        @strongify(self)
        self.isLockFullScreen = NO;
        
        [self _removeLockFullScreenView];
    }];
}

- (void)_removeLockFullScreenView
{
    if(self.bottomLockFullScreenView) {
        [self.bottomLockFullScreenView removeFromSuperview];
        self.bottomLockFullScreenView = nil;
    }
}

//添加了播放器
- (void)addPlayer:(PlayerView *)player
{
    //修复全屏模式下，循环播放的BUG，如果强制刷新旋转，会有问题
    if([self.playerArray containsObject:player]) {
        [self.playerArray removeObject:player];
        [self.playerArray insertObject:player atIndex:0];
        return;
    }
    
    [self.playerArray insertObject:player atIndex:0];
    //更新状态
    self.isPlayVideo = self.playerArray.count>0;
    
    AppDelegate *appDelegate = (AppDelegate*) [UIApplication sharedApplication].delegate;
    UIWindow* window = appDelegate.window;
    
    //强制刷新旋转
    [window.rootViewController setNeedsStatusBarAppearanceUpdate];
    [UIViewController attemptRotationToDeviceOrientation];
}

//播放器释放
- (void)endPlayer:(PlayerView *)player
{
    [self.playerArray removeObject:player];
    //更新状态
    self.isPlayVideo = self.playerArray.count>0;
    
    AppDelegate *appDelegate = (AppDelegate*) [UIApplication sharedApplication].delegate;
    UIWindow* window = appDelegate.window;
    
    //强制刷新旋转
    [window.rootViewController setNeedsStatusBarAppearanceUpdate];
    [UIViewController attemptRotationToDeviceOrientation];
}

- (void)reloadCustomTagModels
{
    DatabaseUnit* unit = [DatabaseUnit queryAllCustomTags];
    @weakify(self)
    [unit setCompleteBlock:^(id result, BOOL success) {
        @strongify(self)
        if(success) {
            self.customTagModels = result;
        }
    }];
    DB_EXEC(unit);
}

- (void)reloadBookMarks
{
    DatabaseUnit* unit = [DatabaseUnit queryAllBookMarks];
    @weakify(self)
    [unit setCompleteBlock:^(id result, BOOL success) {
        @strongify(self)
        if(success) {
            self.bookMarks = result;
        }
    }];
    
    DB_EXEC(unit);
}

@end
