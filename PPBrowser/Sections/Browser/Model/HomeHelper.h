//
//  HomeHelper.h
//  PPBrowser
//
//  Created by qingbin on 2024/10/21.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>

#import "TabModel.h"

// 首页管理类
@interface HomeHelper : NSObject

//当url不是完整的链接时，例如少了个http，那么在SessionRestore中new URL就会报错，从而导致逻辑中断
//自定义主页
+ (void)handleCustomUrl:(NSString *)url completion:(void (^)(BOOL succ))completion;

//获取自定义首页url,如果为默认值,则返回[InternalURL homeUrl]
+ (NSString *)getCustomUrl;

//判断当前用户是否自定义了首页url
+ (BOOL)isCustomUrl;

//新开一页，判断获取默认/自定义的TabModel
+ (TabModel *)getHomeTabModel;

@end
