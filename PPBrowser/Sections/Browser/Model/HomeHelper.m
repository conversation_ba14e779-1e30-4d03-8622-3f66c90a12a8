//
//  HomeHelper.m
//  PPBrowser
//
//  Created by qing<PERSON> on 2024/10/21.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import "HomeHelper.h"

#import "MaizyHeader.h"

#import "ReactiveCocoa.h"
#import "NSObject+Helper.h"

#import "InternalURL.h"
#import "TabModel.h"
#import "TabManager.h"

#import "DatabaseUnit+Helper.h"
#import "PreferenceManager.h"
#import "CommonDataManager.h"

#import "AppDelegate.h"
#import "BrowserViewController.h"

#import "PPNotifications.h"

@implementation HomeHelper

//自定义主页
+ (void)handleCustomUrl:(NSString *)url completion:(void (^)(BOOL succ))completion;
{
    NSString* customUrl = [url stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceCharacterSet]];
    
    BOOL isCustomUrl = NO;
    if(customUrl.length == 0) {
        //默认值
        isCustomUrl = NO;
        customUrl = [InternalURL homeUrl];
    } else {
        //自定义值
        NSURL* URL = [NSURL URLWithString:customUrl];
        if(!URL) {
            if(completion) {
                completion(NO);
            }
            
            return;
        }
        
        isCustomUrl = YES;
    }
    
    //1、更新TabModel中的urlHistorySnapshot字段,将数组第一个设置为首页
    DatabaseUnit* unit = [DatabaseUnit selectAllTabs];
    [unit setCompleteBlock:^(NSArray* result, BOOL success) {
        if(success && result.count > 0) {
            [self _handleUpdateCustomUrl:customUrl tabs:result completion:^(BOOL succ) {
                if(succ) {
                    if(isCustomUrl) {
                        [PreferenceManager shareInstance].items.customUrl = customUrl;
                    } else {
                        [PreferenceManager shareInstance].items.customUrl = @"";
                    }
                    
                    [[PreferenceManager shareInstance] encode];
                }
                
                if(completion) {
                    completion(succ);
                }
            }];
        } else {
            if(completion) {
                completion(NO);
            }
        }
    }];
    DB_EXEC(unit);
    
    //2、将已加载的标签页, 重设它们的tabModel的urlHistorySnapshot
    //3、将已加载的标签页，重新设置它们的history堆栈
}

//1、更新数据库中tabModel的urlHistorySnapshot
+ (void)_handleUpdateCustomUrl:(NSString *)customUrl
                          tabs:(NSArray<TabModel *> *)tabArray
                    completion:(void (^)(BOOL succ))completion
{
    NSMutableArray* tabIdArray = [NSMutableArray array];
    NSMutableArray* urlHistorySnapshotArray = [NSMutableArray array];
    
    for(TabModel* tabModel in tabArray) {
        NSMutableArray* urls = [[tabModel.urlHistorySnapshot componentsSeparatedByString:@","] mutableCopy];
        if(urls.count > 0) {
            //设置首页
            urls[0] = customUrl;
        } else {
            //没有历史数据，直接插入
            [urls addObject:customUrl];
        }
        
        NSString* urlHistorySnapshot = [urls componentsJoinedByString:@","];
        tabModel.urlHistorySnapshot = urlHistorySnapshot;
        
        [tabIdArray addObject:tabModel.tabId];
        [urlHistorySnapshotArray addObject:tabModel.urlHistorySnapshot];
    }
    
    DatabaseUnit* unit = [DatabaseUnit updateTabWithTabIdArray:tabIdArray urlHistorySnapshotArray:urlHistorySnapshotArray];
    [unit setCompleteBlock:^(id result, BOOL success) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [self handleReloadCustomUrl:completion];
        });
    }];
    DB_EXEC(unit);
}

+ (void)handleReloadCustomUrl:(void (^)(BOOL succ))completion
{
    AppDelegate *appDelegate = (AppDelegate*) [UIApplication sharedApplication].delegate;
    BrowserViewController* browser = appDelegate.browser;
    if(!browser) {
        if(completion) {
            completion(NO);
        }
        return;
    }
    
    //(第2/3步太复杂，直接删除所有已加载的tab，然后重新加载web)
    TabManager* tabManager = browser.tabManager;
    for(Tab* tab in tabManager.allTabs) {
        //处理已经加载到内存中的tab
        tab.tabDelegate = nil;
        tab.webView.navigationDelegate = nil;
        [tab removeWebView];
    }
    [tabManager.allTabs removeAllObjects];
    
    [[NSNotificationCenter defaultCenter] postNotificationName:kChangeCustomUrlNotification object:nil];
    
    if(completion) {
        completion(YES);
    }
}

//获取自定义首页url,如果为默认值,则返回空字符串
+ (NSString *)getCustomUrl
{
    NSString* customUrl = [PreferenceManager shareInstance].items.customUrl;
    if(customUrl.length == 0 || [InternalURL isAboutHomeURL:customUrl]) {
        return [InternalURL homeUrl];
    }
    
    return customUrl;
}

//判断当前用户是否自定义了首页url
+ (BOOL)isCustomUrl
{
    NSString* customUrl = [PreferenceManager shareInstance].items.customUrl;
    if(customUrl.length == 0 || [InternalURL isAboutHomeURL:customUrl]) {
        return NO;
    }
    
    return YES;
}

//新开一页，判断获取默认/自定义的TabModel
+ (TabModel *)getHomeTabModel
{
    TabModel* tabModel;
    if([HomeHelper isCustomUrl]) {
        //自定义首页url
        NSString* customUrl = [HomeHelper getCustomUrl];
        tabModel = [TabModel tabModelWithUrl:customUrl];
    } else {
        //默认
        tabModel = [TabModel tabModelForNewHomePanel];
    }
    
    return tabModel;
}

@end
