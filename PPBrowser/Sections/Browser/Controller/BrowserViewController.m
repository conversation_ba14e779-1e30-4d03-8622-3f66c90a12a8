//
//  BrowserViewController.m
//  PandaBrowser
//
//  Created by qingbin on 2022/3/2.
//

#import "BrowserViewController.h"
#import "Tab.h"
#import "TabManager.h"

#import "BrowserViewController+TabManagerDelegate.h"
#import "BrowserViewController+TabDelegate.h"
#import "BrowserViewController+TopToolbarDelegate.h"
#import "BrowserViewController+BottomToolbarDelegate.h"
#import "BrowserViewController+WKUIDelegate.h"
#import "BrowserViewController+FindInPageBarDelegate.h"
#import "BrowserViewController+PreferenceViewDelegate.h"

#import "DatabaseUnit+Helper.h"

#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "UIView+FrameHelper.h"
#import "NSObject+Helper.h"

#import "TabModel.h"
#import "InternalURL.h"

#import "WebViewNavDelegate.h"

#import "ScreenshotHelper.h"

#import "TabTrayViewController.h"

#import "FindInPageBar.h"

#import "PPNotifications.h"

#import "NewTabPageView.h"
#import "PreferenceView.h"

#import "PhotoBrowserController.h"
#import "DatabaseUnit+History.h"
#import "HistoryModel.h"

#import "UserScript.h"
#import "Tampermonkey.H"
#import "SystemScriptManager.h"
#import "UserComandModel.h"

#import "CustomTagModel.h"

#import "ThemeProtocol.h"
#import "PreferenceManager.h"

#import "PlayModel.h"
#import "ContextMenuModel.h"

#import "SDWebImageDownloader.h"
#import "PlayerView.h"

#import "GuidelineController.h"
#import "CopyrightHelper.h"

#import "TagitManager.h"
#import "BrowserHelper.h"
#import "DataClearHelper.h"

#import "PlayerView.h"

#import <StoreKit/StoreKit.h>

#import "URIFixup.h"

#import "WebBlacklistHelper.h"
#import "CommonDataManager.h"

#import "AppDelegate.h"
#import "PreferencePopUpController.h"
#import "SnifferHelper.h"

#import "TranslateToolBar.h"
#import "TranslateSettingController.h"
#import "ImageTypeChecker.h"
#import "UserAgentManager.h"

#import "HomeHelper.h"
#import "PaymentManager.h"
#import "UserScriptUpdateManager.h"
#import "AlertJumpHelper.h"

#import "AutoPageManager.h"
#import "SwipeNavigationManager.h"
#import "UIAlertController+SafePresentation.h"
#import "MitaAIInputDialog.h"
#import "SearchManager.h"
#import "SDImageCache.h"
#import "BookReplaceManager.h"

@interface BrowserViewController ()<ThemeProtocol,SwipeNavigationManagerDelegate,MitaAIInputDialogDelegate>

@property (nonatomic, strong) TabManager *tabManager;

@property (nonatomic, strong) TopToolbar* topToolbar;
@property (nonatomic, strong) BottomToolbar* bottomToolbar;
//翻译工具栏
@property (nonatomic, strong) TranslateToolBar *translateToolBar;

@property (nonatomic, strong) PreferenceView* preferenceView;

@property (nonatomic, strong) FindInPageBar* findInPageBar;

@property (nonatomic, strong) NewTabPageView *ntpView;
// 滑动导航管理器
@property (nonatomic, strong) SwipeNavigationManager *swipeNavigationManager;

@end

@implementation BrowserViewController

- (void)viewDidLoad
{
    [super viewDidLoad];

    self.view.backgroundColor = UIColor.whiteColor;
    self.toolBarScrollController = [TabScrollController new];
        
    [[WebViewNavDelegate shareInstance] insert:self];
    
    [self addSubviews];
    [self defineLayout];
    [self setupObservers];
        
    //加载智能拼页规则
    [AutoPageManager sharedInstance];
    
    //打开历史记录
    [self showLastViewClosedTab];
    
    //统一初始化
    [self commonInit];
    
    [self applyTheme];
    
    //重签名检测
    [BrowserHelper checkResignApp];
    
#ifdef DEBUG
#else
    NSInteger reviewCount = [[PreferenceManager shareInstance].items.reviewCount integerValue];
    if(reviewCount >= 1) {
        //一句话实现在App内直接评论了。然而需要注意的是：打开次数一年不能多于3次。（开发期间可以无限制弹出，方便测试）
        [SKStoreReviewController requestReview];
    } else {
        reviewCount++;
        if(reviewCount > 3) {
            //防止溢出
            reviewCount = 3;
        } else {
            //大于10之后就不再需要保存了
            [PreferenceManager shareInstance].items.reviewCount = @(reviewCount);
            [[PreferenceManager shareInstance] encode];
        }
    }
#endif
}

- (void)viewDidAppear:(BOOL)animated
{
    [super viewDidAppear:animated];
    
    //防止和侧滑手势返回冲突，但是效果不明显
    self.tabManager.selectedTab.webView.allowsBackForwardNavigationGestures = YES;
}

- (void)viewDidDisappear:(BOOL)animated
{
    [super viewDidDisappear:animated];
    
    //防止和侧滑手势返回冲突，但是效果不明显
    self.tabManager.selectedTab.webView.allowsBackForwardNavigationGestures = NO;
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    
    AppDelegate *appDelegate = (AppDelegate*) [UIApplication sharedApplication].delegate;
    
    if(isDarkTheme) {
        self.navigationController.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        //夜间模式
        [UIApplication sharedApplication].statusBarStyle = UIStatusBarStyleLightContent;
        //window
        appDelegate.window.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
    } else {
        self.navigationController.view.backgroundColor = UIColor.whiteColor;
        self.view.backgroundColor = UIColor.whiteColor;
        
        if (@available(iOS 13.0, *)) {
            [UIApplication sharedApplication].statusBarStyle = UIStatusBarStyleDarkContent;
        } else {
            [UIApplication sharedApplication].statusBarStyle = UIStatusBarStyleDefault;
        }
        //window
        appDelegate.window.backgroundColor = [UIColor whiteColor];
    }
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    //只有当前的controller会触发一次, 其它已存在的controller走通知
    if(needReload) {
        //修改暗黑模式
        [self applyTheme];
        //重新加载脚本
        [self _reloadUserScript:nil];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    //修改暗黑模式
    [self applyTheme];
    //重新加载脚本
    [self _reloadUserScript:nil];
}

- (void)commonInit
{
    AppDelegate* app = (AppDelegate*)[UIApplication sharedApplication].delegate;
    //添加分享过来的脚本
    if(app.needAddUserScriptURL) {
        [self handleAddUserScriptWithURL:app.needAddUserScriptURL];
        app.needAddUserScriptURL = nil;
    } else if(app.needAddUserScriptJsContent.length > 0) {
        [self handleAddUserScriptWithJsContent:app.needAddUserScriptJsContent];
        app.needAddUserScriptJsContent = nil;
    }
    
    //优化打开速度，现在打开有点慢
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self _commonInit];
    });
}

- (void)_commonInit
{
    //处理添加书签/首页相关逻辑
    [[CommonDataManager shareInstance] reloadCustomTagModels];
    [[CommonDataManager shareInstance] reloadBookMarks];
        
    //查询广告过滤xpath
    [[TagitManager shareInstance] reloadData];
        
    BOOL isVip = [[PaymentManager shareInstance] isVip];
    if(isVip) {
        //自动更新脚本
        dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
            [[UserScriptUpdateManager shareInstance] updateUserScripts];
        });
    }
    
    //更新屏蔽APP跳转
    [[AlertJumpHelper shareInstance] reloadData];
    
    //网页黑名单初始化
    [[WebBlacklistHelper shareInstance] reloadData];
    
    // 初始化滑动导航管理器
    self.swipeNavigationManager = [[SwipeNavigationManager alloc] initWithHostView:self.view delegate:self];
    
    // v2.7.8，阅读模式，加载文本过滤规则
    [[BookReplaceManager shareInstance] reloadData];
}

#pragma mark -- layout
- (void)addSubviews
{
    [self.view addSubview:self.topToolbar];
    [self.view addSubview:self.bottomToolbar];
}

- (void)defineLayout
{
    float height = 0;
    if([BrowserUtils isiPad]) {
        //iPad
        height = [TopToolbarForiPad toolbarHeight];
    } else {
        //iPhone
        height = [TopToolbar toolbarHeight];
    }
    
    [self.topToolbar mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.view);
        make.height.mas_equalTo(height).priorityHigh();
        make.top.mas_offset(-height).priorityHigh();
    }];
    
    [self.bottomToolbar mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.view);
        make.height.mas_equalTo([BottomToolbar toolbarHeight]).priorityHigh();
        make.bottom.mas_offset(0).priorityHigh();
    }];
        
    //如果是iPad,先隐藏,如果判断需要显示
    //那么再显示
    if([BrowserUtils isiPad]) {
        [self.bottomToolbar dismiss:NO];
    }
}

- (void)setupObservers
{
    //findInBar的键盘事件
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(_keyboardWillShown:)
                                                 name:UIKeyboardWillShowNotification
                                               object:nil];

    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(_keyboardWillHidden:)
                                                 name:UIKeyboardWillHideNotification
                                               object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(_handlerImageModeHelper:)
                                                 name:kGetAllImageHelperNotification
                                               object:nil];
    
    //添加脚本
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(_addUserScript:)
                                                 name:kAddUserScriptNotification
                                               object:nil];
    
    //重新加载脚本
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(_reloadUserScript:)
                                                 name:kReloadUserScriptNotification
                                               object:nil];
    //脚本命令打开新标签页
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(_openInTabByUserScript:)
                                                 name:kOpenInTabByUserScriptNotification
                                               object:nil];
    
    //打开书签
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(_openBookMark:)
                                                 name:kOpenBookMarkNotification
                                               object:nil];
    
    //打开网页
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(_openWebView:)
                                                 name:kOpenWebViewNotification
                                               object:nil];
    
    //刷新当前Web页
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(_reloadCurrentWebView:)
                                                 name:kReloadCurrentWebViewNotification
                                               object:nil];
    
    //长按弹窗
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(_openContextMenu:)
                                                 name:kContextMenuNotification
                                               object:nil];
    
    //更换了首页
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(handleChangeCustomUrl)
                                                 name:kChangeCustomUrlNotification
                                               object:nil];
}

#pragma mark -- 翻译相关

//显示翻译工具栏
- (void)showTranslateToolbar
{
    [self hideTranslateToolbar];
    
    self.translateToolBar = [TranslateToolBar new];
    [self.view addSubview:self.translateToolBar];
    
    [self.translateToolBar mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([TranslateToolBar height]);
        make.centerX.mas_equalTo(self.view);
        make.bottom.equalTo(self.bottomToolbar.mas_top).offset(-iPadValue(20, 10));
    }];
    
    @weakify(self)
    [self.translateToolBar setCloseAction:^{
        @strongify(self)
        [self hideTranslateToolbar];
    }];
    
    [self.translateToolBar setSettingAction:^{
        @strongify(self)
        TranslateSettingController* vc = [TranslateSettingController new];
        BaseNavigationController* navc = [[BaseNavigationController alloc]initWithRootViewController:vc];
        
//        if ([BrowserUtils isiPad]) {
//            // iPad
//            navc.modalPresentationStyle = UIModalPresentationFormSheet;
//            vc.preferredContentSize = CGSizeMake(kScreenWidth-90, kScreenHeight-90);
//            
//            [self presentViewController:navc animated:YES completion:nil];
//        } else {
//            // iPhone
//            [self presentViewController:navc animated:YES completion:nil];
//        }
        //v2.6.8,统一present
        [self presentCustomToViewController:navc];
    }];
    
    [self.translateToolBar setTranslateAction:^{
        @strongify(self)
        [self startTranslatePage];
    }];
    
    [self.translateToolBar setRestoreAction:^{
        @strongify(self)
        [self restoreTranslate];
    }];
    
    //如果是初始化或者默认状态，那么点击翻译则直接开启翻译
    [self.translateToolBar startTranslateIfNeed];
}

// 隐藏翻译工具栏
- (void)hideTranslateToolbar
{
    [self.translateToolBar removeFromSuperview];
    self.translateToolBar = nil;
}

//翻译网页
- (void)startTranslatePage
{
    WKWebView* webView = self.tabManager.selectedTab.webView;
    
    [TranslateManager startTranslatePageWithWebView:webView];
}

//显示原文
- (void)restoreTranslate
{
    WKWebView* webView = self.tabManager.selectedTab.webView;

    [TranslateManager restoreTranslateWithWebView:webView];
}

#pragma mark -- SafeArea发生变化
- (void)viewSafeAreaInsetsDidChange
{
    [super viewSafeAreaInsetsDidChange];
    
    [[BrowserUtils shareInstance] updateWithSafeArea:self.view.safeAreaInsets];
}

#pragma mark -- 屏幕旋转,iPad适配
- (void)willTransitionToTraitCollection:(UITraitCollection *)newCollection
              withTransitionCoordinator:(id<UIViewControllerTransitionCoordinator>)coordinator
{
    // During split screen launching on iPad, this callback gets fired before viewDidLoad gets a chance to
    // set things up. Make sure to only update the toolbar state if the view is ready for it.
    if([self isViewLoaded]) {
        //收起所有的工具栏
        [self hideUserPreferenceView];
    }
}

- (void)viewWillTransitionToSize:(CGSize)size
       withTransitionCoordinator:(id<UIViewControllerTransitionCoordinator>)coordinator
{
    [BrowserUtils shareInstance].transitionToSize = size;
    //收起所有的工具栏
    [self hideUserPreferenceView];
    
    //屏幕旋转
    [self.ntpView updateForTransition];
    if([self isViewLoaded]) {
        [self updateToolBarLayoutsToSize:size];
    }
    
    //屏幕发生旋转通知
    [[NSNotificationCenter defaultCenter] postNotificationName:kOrientationDidChangeNotification object:@(size)];
    
    //发生旋转时,size才是最后的值, [UIScreen mainScreen].bounds.size还没更新
//    NSLog(@"newSize = %@,  currentSize = %@",NSStringFromCGSize(size), NSStringFromCGSize([UIScreen mainScreen].bounds.size));
}

#pragma mark -- 工具栏屏幕旋转适配
- (void)updateToolBarLayoutsToSize:(CGSize)size
{
    //顶部、底部工具栏适配屏幕旋转
    if([BrowserUtils isiPad]) {
        float height = [TopToolbarForiPad toolbarHeight];
        self.topToolbar.frame = CGRectMake(0, 0, size.width, height);
        [((TopToolbarForiPad*)self.topToolbar) updateToolBarLayoutsToSize:size];
        
        [self.bottomToolbar updateToolBarLayoutsToSize:size];
    } else if([BrowserUtils isiPhone]) {
        //iPhone
        //iPhone横竖屏切换逻辑
        BOOL isLandscape = size.width>size.height;
        if(!isLandscape) {
            //竖屏
            [self.bottomToolbar show:NO isHomePage:[self isHomePage]];
        } else {
            //横屏
            //隐藏底部工具栏
            NSURL* URL = self.tabManager.selectedTab.webView.URL;
            if(!URL) return;
            if([InternalURL isAboutHomeURL:URL.absoluteString]) {
                //首页，显示底部工具栏
                [self.bottomToolbar show:NO isHomePage:[self isHomePage]];
            } else {
                //非首页，隐藏底部工具栏
                [self.bottomToolbar dismiss:NO];
            }
        }
    }
}

- (BOOL)isHomePage
{
    NSURL* URL = self.tabManager.selectedTab.webView.URL;
    if(!URL) return NO;
    
    return [InternalURL isAboutHomeURL:URL.absoluteString];
}

#pragma mark -- 播放器相关设置
- (UIStatusBarStyle)preferredStatusBarStyle {
    return UIStatusBarStyleDefault;
}

- (BOOL)prefersStatusBarHidden {
    return NO;
}

- (UIStatusBarAnimation)preferredStatusBarUpdateAnimation {
    return UIStatusBarAnimationNone;
}
//如果不添加这两个旋转相关函数,那么播放器全屏后,无法响应相关事件
- (BOOL)shouldAutorotate {
    //qingbin,v2.0
    if([BrowserUtils isiPad]) {
        //只有iPad才支持旋转
        return YES;
    } else {
        //iPhone默认竖屏
        //v2.1.6必须允许它旋转,否则横屏打开的时候,没法自动切换回到竖屏
        return YES;
    }
}
//如果不添加这两个旋转相关函数,那么播放器全屏后,无法响应相关事件
- (UIInterfaceOrientationMask)supportedInterfaceOrientations {    
    //qingbin,v2.0
    if([BrowserUtils isiPad]) {
        //只有iPad才支持旋转
        return UIInterfaceOrientationMaskAllButUpsideDown;
    } else {
        //iPhone
        //如果正在播放视频，为了防止和播放器的旋转逻辑冲突，因此默认不旋转
        if([CommonDataManager shareInstance].isPlayVideo) {
            //正在播放视频
            return UIInterfaceOrientationMaskPortrait;
        } else {
            //如果不是正在播放视频，则默认可以跟随旋转
            return UIInterfaceOrientationMaskAllButUpsideDown;
        }
    }
}

//https://blog.csdn.net/han_laomo/article/details/84833277
//全屏的时候自动隐藏iPhone X底部白条
- (BOOL)prefersHomeIndicatorAutoHidden
{
    return YES;
}

// 需要拉两下才能下拉系统框
//- (UIRectEdge)preferredScreenEdgesDeferringSystemGestures
//{
//    return UIRectEdgeAll;
//}

#pragma mark -- 添加webView
- (void)addWebView:(UIView*)webView
{
    if(webView.superview) return;
    
    [self.view addSubview:webView];
    [self.view sendSubviewToBack:webView];
    
    [webView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.topToolbar.mas_bottom);
        make.left.right.equalTo(self.view);
        make.bottom.equalTo(self.bottomToolbar.mas_top);
    }];
}

#pragma mark -- 打开上一次浏览历史
- (void)showLastViewClosedTab
{    
    @weakify(self)
    void (^openNewTabBlock)(void) = ^{
        @strongify(self)
        //如果有url scheme, 冷启动
        AppDelegate* app = (AppDelegate*)[UIApplication sharedApplication].delegate;
        if(app.needJumpUrl.length > 0) {
            NSURL* fixupURL = [URIFixup getCompletionURL:app.needJumpUrl];
            [self handleOpenInURLScheme:fixupURL];
            app.needJumpUrl = nil;
            return;
        }
        
        //更新tab数量
        [self.bottomToolbar updateTabCount:1];
        [self.topToolbar updateTabCount:1];
                
        //新开一页
        TabModel* tabModel = [HomeHelper getHomeTabModel];
        [self.tabManager addTabAndSelect:tabModel];
    };
        
    BOOL openLastWindow = [[PreferenceManager shareInstance].items.openLastWindow boolValue];
    if(openLastWindow) {
        //启动时恢复窗口
        DatabaseUnit* unit = [DatabaseUnit selectAllTabs];
        unit.completeBlock = ^(NSArray<TabModel*>* result, BOOL success) {
            @strongify(self)
            if(success && result.count > 0) {
                //更新tab数量
                [self.bottomToolbar updateTabCount:(int)result.count];
                [self.topToolbar updateTabCount:(int)result.count];
                                
                //如果有url scheme, 冷启动
                AppDelegate* app = (AppDelegate*)[UIApplication sharedApplication].delegate;
                if(app.needJumpUrl.length > 0) {
                    [self handleOpenInNewTabAction:[NSURL URLWithString:app.needJumpUrl]];
                    app.needJumpUrl = nil;
                    return;
                }
                
                TabModel* selectItem;
                for(TabModel* item in result) {
                    if(item.isSelected) {
                        selectItem = item;
                        break;
                    }
                }
                
                if(!selectItem) {
                    //如果不存在,有bug,会导致崩溃
                    selectItem = result.lastObject;
                    selectItem.isSelected = YES;
                }
                
                //打开历史记录
                [self.tabManager restoreTab:selectItem];
            } else {
                if(openNewTabBlock) {
                    openNewTabBlock();
                }
            }
        };
        
        DB_EXEC(unit);
    } else {
        //不需要恢复窗口
        //删除数据库中所有的tab
        DatabaseUnit* unit = [DatabaseUnit deleteAllTabs];
        DB_EXEC(unit);
        
        if(openNewTabBlock) {
            openNewTabBlock();
        }
    }
}

#pragma mark -- 更换了首页
- (void)handleChangeCustomUrl
{
    DatabaseUnit* unit = [DatabaseUnit selectAllTabs];
    @weakify(self)
    unit.completeBlock = ^(NSArray* result, BOOL success) {
        @strongify(self)
        if(success && result.count > 0) {
            //更新tab数量
            [self.bottomToolbar updateTabCount:(int)result.count];
            [self.topToolbar updateTabCount:(int)result.count];
                                        
            TabModel* selectItem;
            for(TabModel* item in result) {
                if(item.isSelected) {
                    selectItem = item;
                    break;
                }
            }
            
            if(!selectItem) {
                //如果不存在,有bug,会导致崩溃
                selectItem = result.lastObject;
                selectItem.isSelected = YES;
            }
            
            //打开历史记录
            [self.tabManager restoreTab:selectItem];
        }
    };
    
    DB_EXEC(unit);
}

#pragma mark -- 显示标签页
- (void)showTabTray
{
//    PandaWebView* webView = self.tabManager.selectedTab.webView;
//    if(![webView isLoading]) {
//        //正在加载中则不截图
//        [[ScreenshotHelper shareInstance] takeScreenshot:self.tabManager.selectedTab];
//    } else if([webView estimatedProgress] > 0.85) {
//        //如果是正在加载中, 那么必须加载进度完成了85%才能截图
//        [[ScreenshotHelper shareInstance] takeScreenshot:self.tabManager.selectedTab];
//    }
    
    [[ScreenshotHelper shareInstance] takeScreenshot:self.tabManager.selectedTab];
    
    TabTrayViewController* vc = [[TabTrayViewController alloc]initWithTabManager:self.tabManager];
    UINavigationController* navc = [[UINavigationController alloc]initWithRootViewController:vc];
    navc.modalPresentationStyle = UIModalPresentationFullScreen;
    navc.transitioningDelegate = vc;
    //这句代码很重要,动画切换过程中防止看到底部
    navc.view.backgroundColor = [UIColor colorWithHexString:@"#ffffff"];
    
    @weakify(self)
    [vc loadingData:^{
        @strongify(self)
        [self presentViewController:navc animated:YES completion:nil];
    }];
}

#pragma mark -- 更新历史记录
- (void)navigateInTab:(Tab*)tab
{
    if(!tab || !tab.webView) return;
    
    NSString* url = tab.webView.URL.absoluteString;
    //内部url，直接返回
    if([InternalURL isValid:tab.webView.URL]) return;
    
    HistoryModel* item = tab.currentHistoryModel;
    if(!item) {
        //这种属于WebView中点击事件,因此记录为url方式
        item = [HistoryModel new];
        item.historyId = [[NSUUID UUID] UUIDString];
        item.searchType = HistorySearchTypeUrl;
        
        tab.currentHistoryModel = item;
    }
    item.title = tab.webView.title;
    item.url = url;
    
    //保存到历史表中
    DatabaseUnit* unit = [DatabaseUnit addHistoryWithItem:item];
    DB_EXEC(unit);
}

#pragma mark -- 更新页面状态
- (void)updatePageStatusForTab:(Tab*)tab
{
    //updatePageStatusForTab有可能多个tab同时进入这里
    //因此要保证下面的所有状态都要是公用的, 这样就只有selectTab才能真正影响到
    
    //要判断tab是不是当前选中的tab
    if(tab != self.tabManager.selectedTab) return;
    
//    LOG_DEBUG(@"title = %@", tab.webView.title);
    
    //这里保险一点,取WebView的URL作为参考
    NSString* urlString = [tab.webView.URL absoluteString];
    if([InternalURL isAboutHomeURL:urlString]) {
        [self showNewTabPageController];

        //隐藏toptoolbar
        [self.topToolbar dismiss:NO];
    } else {
        //webView, 隐藏自定义首页
        [self hideActiveNewPageController];
        
        //如果还是包住的历史记录,先不处理
        //因为会有一个解包的过程,如果是非首页,那么始终会走这里的逻辑
        //历史记录页不处理相关逻辑, 但是首页和错误页需要处理
        if(urlString.length > 0 && ![InternalURL isSessionRestore:urlString]) {
            //显示toptoolbar
            if([CommonDataManager shareInstance].isLockFullScreen) {
                //如果现在的状态是锁定全屏，那么则不需要显示工具栏
            } else {
                [self.topToolbar show:NO];
                [self.bottomToolbar show:NO isHomePage:[self isHomePage]];
            }
            
            //更新toptoolbar进度条
            float progress = tab.webView.estimatedProgress;
            if(progress >= 1.0 || progress < 0) {
                [self.topToolbar hideProgressBar];
            } else {
                [self.topToolbar updateProgressBar:progress];
            }
            
            //更新toptoolbar的刷新和关闭刷新按钮
            BOOL isLoading = tab.webView.loading;
            [self.topToolbar updateLoadingStatus:isLoading];
            
            //更新url
            //如果是错误页,显示错误页中的errorUrl
            if([InternalURL isErrorPage:urlString]) {
                NSRange range = [urlString rangeOfString:@"errorUrl="];
                NSInteger location = range.location+range.length;
                NSInteger length = urlString.length - (range.location+range.length);
                NSString* errorUrl = [urlString substringWithRange:NSMakeRange(location, length)];
                [self.topToolbar updateCurrentURL:errorUrl];
            } else {
                //internal开头的scheme都不显示出来
                if(![InternalURL isValid:[NSURL URLWithString:urlString]]) {
                    [self.topToolbar updateCurrentURL:urlString];
                }
            }
            
            //更新标题title
            AddressBarDisplayMode displayMode = [[PreferenceManager shareInstance].items.toptoolDisplayMode intValue];
            if (displayMode == AddressBarDisplayModeTitle) {
                NSString *title = tab.webView.title;
                if (title.length > 0) {
                    [self.topToolbar updateCurrentTitle:title];
                }
            }
        }
    }
    
    //更新是否能前进后退
    //前进后退的判断分两块逻辑: 1、从历史页加载Webview,那么则从didCommit中进行判断
    //2、从tabs中选择, 如果Webview已存在,那么从didSelectedTab代理中更新
    [self.bottomToolbar updateGoBackStatus:tab.webView.canGoBack];
    [self.bottomToolbar updateGoForwardStatus:tab.webView.canGoForward];
    //壁纸相关
    NSString* url = tab.webView.URL.absoluteString;
    if(url.length <= 0) {
        url = tab.model.url;
    }
    
    if(url.length > 0) {
        BOOL isSessionRestore = [InternalURL isSessionRestore:url];
        BOOL isHomePage = [InternalURL isAboutHomeURL:url];
        
        //如果是历史记录还在转换中的url,那么先不处理
        if(isHomePage) {
            [self.bottomToolbar updateIsHomePage:isHomePage];
        } else if(!isSessionRestore) {
            [self.bottomToolbar updateIsHomePage:isHomePage];
        }
    }
    
    [self.topToolbar updateGoBackStatus:tab.webView.canGoBack];
    [self.topToolbar updateGoForwardStatus:tab.webView.canGoForward];
}

#pragma mark -- 添加新首页(homepanel)
- (void)showNewTabPageController
{
    Tab* selectedTab = self.tabManager.selectedTab;
    if(!selectedTab) return;
    
    if(self.ntpView == nil) {
        self.ntpView = [[NewTabPageView alloc] init];
    }
    
    [self.ntpView removeFromSuperview];
    
    [self.ntpView updateWithTab:selectedTab];
    
    selectedTab.ntpView = self.ntpView;
    
    if(self.ntpView.superview == nil) {
        [self.view addSubview:self.ntpView];
        [self.ntpView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.equalTo(self.view);
        }];
        
        [self.view bringSubviewToFront:self.bottomToolbar];
        
        //将播放视频的view提前
        for(UIView* view in self.view.subviews) {
            if([view isMemberOfClass:PlayerView.class]) {
                [self.view bringSubviewToFront:view];
            }
        }
    
        // v2.1.6版本去掉alpha动画,效果更好(感觉像闪动的效果)
        // We have to run this animation, even if the view is already showing because there may be a hide animation running
        // and we want to be sure to override its results.
//        [UIView animateWithDuration:0.25 animations:^{
//            self.ntpView.alpha = 1;
//        } completion:^(BOOL finished) {
//        }];
    }
}

#pragma mark -- 隐藏新首页(homepanel)
- (void)hideActiveNewPageController
{
    NewTabPageView* ntpView = self.ntpView;
    if(!ntpView) return;

    [ntpView removeFromSuperview];
    // v2.1.6版本去掉alpha动画,效果更好(感觉像闪动的效果)
//    [UIView animateWithDuration:0.25 animations:^{
//            ntpView.alpha = 0;
//        } completion:^(BOOL finished) {
//            [ntpView removeFromSuperview];
//    }];
}

#pragma mark -- 打开个人设置页
- (void)openUserPreferenceView:(BOOL)isTriggerByBottomBar
{
    //iPhone
    if([BrowserUtils isiPhone] && ![[BrowserUtils shareInstance] isLandscape]){
        //竖屏iPhone
        if(!self.preferenceView.superview) {
            UIView* window = YBIBNormalWindow();
            if(!window) {
                window = self.view;
            }

            [window addSubview:self.preferenceView];
            self.preferenceView.preferenceView.delegate = self;

            [self.preferenceView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.edges.mas_offset(0);
            }];
        }

        [self.preferenceView.superview bringSubviewToFront:self.preferenceView];
        [self.preferenceView show];
    } else {
        //iPad
        //横屏iPhone,需要确定箭头的位置
        CGRect sourceRect = CGRectZero;
        if(isTriggerByBottomBar) {
            //底部工具栏触发
            UIView* button = self.bottomToolbar.menuButton;
            sourceRect = [button.superview convertRect:button.frame toView:self.view];
        } else {
            //顶部工具栏触发
            UIView* button = self.topToolbar.appMenuButton;
            sourceRect = [button.superview convertRect:button.frame toView:self.view];
        }
        
        if(self.popUpPreferenceController) {
            [self.popUpPreferenceController showAt:self sourceRect:sourceRect];
        } else {
            self.popUpPreferenceController = [PreferencePopUpController showAt:self sourceRect:sourceRect];
        }
    }
}

- (void)hideUserPreferenceView
{
    if(_preferenceView) {
        [self.preferenceView hide];
    }
    
    if(self.popUpPreferenceController) {
        [self.popUpPreferenceController dismissViewControllerAnimated:YES completion:nil];
    }
}

#pragma mark -- 隐藏获取打开查找bar
- (void)updateFindInPageVisibility:(BOOL)visible text:(NSString*)text
{
    if(visible) {
        if(self.findInPageBar) {
            [self findInPageDidPressClose:self.findInPageBar];
            [self.findInPageBar removeFromSuperview];
            self.findInPageBar = nil;
        }
        
        self.findInPageBar = [[FindInPageBar alloc]initWithTab:self.tabManager.selectedTab];
        self.findInPageBar.delegate = self;
        
        [self.view addSubview:self.findInPageBar];
        float height = iPadValue(60, 44);
        [self.findInPageBar mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.view.mas_safeAreaLayoutGuideLeft);
            make.right.equalTo(self.view.mas_safeAreaLayoutGuideRight);
            make.bottom.equalTo(self.bottomToolbar.mas_top);
            make.height.mas_equalTo(height);
        }];
        
        if(text.length > 0) {
            [self.findInPageBar updateWithText:text];
        }
        
        [self.findInPageBar becomeFirstResponder];
    } else {
        if(self.findInPageBar) {
            [self findInPageDidPressClose:self.findInPageBar];
            [self.findInPageBar removeFromSuperview];
            self.findInPageBar = nil;
        }
    }
}

// 清空阅读模式和播放模式
- (void)clearWebViewReaderAndPlayer:(PandaWebView*)webView
{
    [webView clearWebViewReaderAndPlayer];
}

#pragma mark -- 键盘弹起事件
- (void)_keyboardWillShown:(NSNotification *)notification
{
    NSDictionary *info = [notification userInfo];
    CGRect keyboardRect = [[info objectForKey:UIKeyboardFrameEndUserInfoKey] CGRectValue];
    if (CGRectIsEmpty(keyboardRect)) {
        return;
    }
    
    float duration = [[info objectForKey:UIKeyboardAnimationDurationUserInfoKey] floatValue];
    float deltaY = keyboardRect.size.height - [BottomToolbar toolbarHeight];
    
    UIViewAnimationCurve options = [[info objectForKey:UIKeyboardAnimationCurveUserInfoKey] integerValue];
    [UIView setAnimationCurve:options];
    [UIView animateWithDuration:duration animations:^{
        self.findInPageBar.transform = CGAffineTransformMakeTranslation(0, -deltaY);
    }];
}

#pragma mark -- 键盘收起事件
- (void)_keyboardWillHidden:(NSNotification *)notification
{
    NSDictionary *userInfo = notification.userInfo;
//    CGRect frame = [userInfo[UIKeyboardFrameEndUserInfoKey] CGRectValue];//键盘动画结束时的frame
    NSTimeInterval timeInterval = [userInfo[UIKeyboardAnimationDurationUserInfoKey] doubleValue];//动画持续时间
    UIViewAnimationCurve curve = [userInfo[UIKeyboardAnimationCurveUserInfoKey] integerValue];//动画曲线类型

    [UIView setAnimationCurve:curve];
    [UIView animateWithDuration:timeInterval animations:^{
        //code
        self.findInPageBar.transform = CGAffineTransformIdentity;
    }];
}

#pragma mark -- 看图模式
- (void)_handlerImageModeHelper:(NSNotification*)notification
{
    NSArray* imageUrls = notification.object;
    
    if(imageUrls.count == 0) {
        //弹toast
        [UIView showToast:NSLocalizedString(@"tips.image.notfound", nil)];
        return;
    }
    
    WKWebView* webView = self.tabManager.selectedTab.webView;
    if(!webView) return;
    
    NSURL* URL = webView.URL;
    //pixiv.net会根据referer来判断请求
    NSString* referer = URL.absoluteString;
    
    NSString* userAgent = [[UserAgentManager shareInstance] getCurrentUserAgent];
        
    NSArray *cookieModelArray = [[NSHTTPCookieStorage sharedHTTPCookieStorage] cookiesForURL:URL];
    
    NSMutableDictionary* cookies = [NSMutableDictionary dictionary];
    for(NSHTTPCookie* obj in cookieModelArray) {
        if(obj.name.length>0 && obj.value.length>0) {
            [cookies setValue:obj.value forKey:obj.name];
        }
    }
    
    PhotoBrowserController* vc = [[PhotoBrowserController alloc]initWithCookie:cookies referer:referer userAgent:userAgent];
    UINavigationController* navc = [[UINavigationController alloc]initWithRootViewController:vc];
    navc.navigationBar.backgroundColor = UIColor.whiteColor;
    
    NSMutableArray* array = [NSMutableArray array];
    for(NSString* url in imageUrls) {
        //过滤svg
        if([[ImageTypeChecker checkImageTypeFromUrl:url] isEqualToString:@"svg"]) continue;
        
        PhotoBrowserModel* item = [PhotoBrowserModel new];
        item.url = url;
        item.referer = referer;
        item.userAgent = userAgent;
        item.cookies = cookies;
        
        [array addObject:item];
    }
    
    [vc updateWithModel:array];
    
//    if([BrowserUtils isiPad]) {
//      //iPad
//      navc.modalPresentationStyle = UIModalPresentationFormSheet;
//      vc.preferredContentSize = CGSizeMake(kScreenWidth-90, kScreenHeight-90);
//    } else {
//        //iPhone
//    }
//    
//    [self presentViewController:navc animated:YES completion:nil];
    //v2.6.8,统一present
    [self presentCustomToViewController:navc];
}

#pragma mark -- 添加了一个脚本
- (void)_addUserScript:(NSNotification *)notification
{
    UserScript* userScript = notification.object;
        
    NSString* sourceCode = userScript.executorJs;
    
    if(sourceCode.length == 0) {
        LOG_ERROR(@"添加脚本出错了....");
        return;
    }
    
    WKUserScript* script = [[WKUserScript alloc] initWithSource:sourceCode injectionTime:WKUserScriptInjectionTimeAtDocumentStart forMainFrameOnly:YES];

    //给所有在内存中的webView都添加脚本
    //如果是新开的webView, 会在初始化的时候添加上
    for(Tab* tab in self.tabManager.allTabs) {
        PandaWebView* webView = tab.webView;
        if(!webView) continue;
        
        [webView.configuration.userContentController addUserScript:script];
        //要刷新之后才有效
        [tab reload];
    }
}

#pragma mark -- 重新加载脚本
- (void)_reloadUserScript:(NSNotification *)notification
{
    //给所有在内存中的webView都添加脚本
    //如果是新开的webView, 会在初始化的时候添加上
    for(Tab* tab in self.tabManager.allTabs) {
        PandaWebView* webView = tab.webView;
        if(!webView) continue;
        
        @weakify(tab)
        [[SystemScriptManager shareInstance] injectUserSciptsToWebView:webView completion:^{
            @strongify(tab)
            //要刷新之后才有效
            [tab reload];
        }];
    }
}

#pragma mark -- 打开新标签页
- (void)_openInTabByUserScript:(NSNotification *)notification
{
    UserComandModel* item = notification.object;
    
    if(self.tabManager.selectedTab) {
        [[ScreenshotHelper shareInstance] takeScreenshot:self.tabManager.selectedTab];
    }
        
    [self handleOpenInNewTabAction:[NSURL URLWithString:item.openUrl]];
}

#pragma mark -- 打开书签
- (void)_openBookMark:(NSNotification *)notification
{
    //初始化
    BookMarkController* vc = [[BookMarkController alloc] initWithTab:self.tabManager.selectedTab];
    
    @weakify(self)
    vc.loadRequestBlock = ^(NSURL *url) {
        @strongify(self)
        [self.tabManager.selectedTab loadRequest:[NSURLRequest requestWithURL:url]];
    };
    
    vc.handleOpenAction = ^(NSURL *url) {
        @strongify(self)
        [self handleOpenAction:url];
    };

    vc.handleOpenInNewTabAction = ^(NSURL *url) {
        @strongify(self)
        [self handleOpenInNewTabAction:url];
    };
    
    vc.handleOpenInBackendTabAction = ^(NSURL *url) {
        @strongify(self)
        [self handleOpenInBackendTabAction:url];
    };
    
    vc.handleCopyLinkContextMenu = ^(NSURL *url) {
        @strongify(self)
        [self handleCopyLinkContextMenu:url];
    };
    
    self.bookMarkController = vc;

    [self.navigationController pushViewController:vc animated:YES];
}

#pragma mark -- 打开网页
- (void)_openWebView:(NSNotification *)notification
{
    CustomTagModel* model = notification.object;
    
    if(model.type == CustomTagTypeGuideline) {
        //用户指南
        GuidelineController* vc = [GuidelineController new];
        [self.navigationController pushViewController:vc animated:YES];
    } else if(model.type == CustomTagTypeMetaso) {
        //秘塔AI，打开输入弹窗
        MitaAIInputDialog *dialog = [[MitaAIInputDialog alloc] init];
        dialog.delegate = self;
        // 在当前视图中显示弹窗
        [dialog showInView:self.view];
    } else {
        NSURL* URL = [NSURL URLWithString:model.targetUrl];
        [self.tabManager.selectedTab loadRequest:[NSURLRequest requestWithURL:URL]];
    }
}

#pragma mark - MitaAIInputDialogDelegate

// 用户点击提交按钮时的回调，返回用户输入的问题
- (void)aiInputDialogDidSubmitWithQuestion:(NSString *)question
{
    NSURL* URL = [[SearchManager shareInstance] searchURLForQuery:question searchType:SearchEngineTypeMetaso];
    [self.tabManager.selectedTab loadRequest:[NSURLRequest requestWithURL:URL]];
}

// 用户点击取消按钮时的回调
- (void)aiInputDialogDidCancel
{
    
}

#pragma mark -- 刷新当前web页
- (void)_reloadCurrentWebView:(NSNotification *)notification
{
    Tab* tab = self.tabManager.selectedTab;
    PandaWebView* webView = tab.webView;
    if(!webView) return;
    
    @weakify(tab)
    [[SystemScriptManager shareInstance] injectUserSciptsToWebView:webView completion:^{
        @strongify(tab)
        //要刷新之后才有效
        [tab reload];
    }];
}

#pragma mark -- 长按弹窗
- (void)_openContextMenu:(NSNotification *)notification
{
    ContextMenuModel* model = notification.object;
    
    LongPressElementType elementType = [model elementType];
    if(elementType == LongPressElementTypeDefault) return;
    
    NSString* url = model.src;
    //链接太长看不到设置封面选项，截取一下
    NSString* title = @"";
    if(url.length > 0) {
        NSInteger maxLength = MIN(150, url.length);
        title = [url substringToIndex:maxLength];
        if(maxLength < url.length) {
            title = [NSString stringWithFormat:@"%@...", title];
        }
    }
    
    if(elementType == LongPressElementTypeVideo
       || elementType == LongPressElementTypeAudio) {
        //关闭了长按识别视频/音频
        BOOL enabledLongPressPlayer = [[PreferenceManager shareInstance].items.enabledLongPressPlayer boolValue];
        if(!enabledLongPressPlayer) return;
        
        //对油管进行特别的处理
        WKWebView* webView = self.tabManager.selectedTab.webView;
        NSString* url = webView.URL.absoluteString;
        BOOL isYoutube = [self _isYoutube:url];
        
        if(isYoutube) {
            title = nil;
        }
        
        //视频和音频
        UIAlertController* alertController = [UIAlertController alertControllerWithTitle:title message:nil preferredStyle:UIAlertControllerStyleActionSheet];
//        if([BrowserUtils isiPad]) {
//            //适配iPad
//            UIPopoverPresentationController* popover = alertController.popoverPresentationController;
//            if(popover) {
//                popover.sourceView = self.tabManager.selectedTab.webView;
//                popover.sourceRect = CGRectMake(model.x, model.y, 0, 0);
//                popover.permittedArrowDirections = UIPopoverArrowDirectionAny;
//            }
//        }
        
        //播放
        UIAlertAction* action = [UIAlertAction actionWithTitle:NSLocalizedString(@"tips.play", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            //伪造成PlaylistDetector
            Tab* tab = self.tabManager.selectedTab;
            PlayModel* model = [[PlayModel alloc]init];
            if(tab.webView.title.length > 0) {
                //探测的标题不准确,如果webview有标题,优先采用
                model.pageTitle = tab.webView.title;
            }
            
            //保存网址的url
            model.originUrl = [tab.webView.URL absoluteString];
            model.src = url;
            model.status = PlayModelItemStatusContextMenuDetector;
            
            [tab.webView enterPlayViewWithModel:model];
        }];
        [alertController addAction:action];
        
        if(isYoutube == NO) {
            action = [UIAlertAction actionWithTitle:NSLocalizedString(@"tips.copyvideolink.title", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                //复制到剪贴板
                if (url.length > 0) {
                    UIPasteboard *pasteboard = [UIPasteboard generalPasteboard];
                    pasteboard.string = url;
                    
                    [UIView showSucceed:NSLocalizedString(@"tips.pasteboard.success", nil)];
                }

                Tab* tab = self.tabManager.selectedTab;
                PlayModel* model = [[PlayModel alloc]init];
                if(tab.webView.title.length > 0) {
                    //探测的标题不准确,如果webview有标题,优先采用
                    model.pageTitle = tab.webView.title;
                }
                
                //保存网址的url
                model.originUrl = [tab.webView.URL absoluteString];
                model.src = url;
                model.status = PlayModelItemStatusContextMenuDetector;
                
                [CommonDataManager shareInstance].currentPlayModel = model;
            }];
            [alertController addAction:action];
        }

        action = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.cancel", nil) style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
            [alertController dismissViewControllerAnimated:YES completion:nil];
        }];
        [alertController addAction:action];
        
//        [self presentViewController:alertController animated:YES completion:nil];
        //v2.6.8, 统一present方法，防止崩溃
        [alertController presentSafelyFromViewController:self
                                              sourceView:self.tabManager.selectedTab.webView
                                              sourceRect:CGRectMake(model.x, model.y, 0, 0)];
    } else if(elementType == LongPressElementTypeImage) {
        //图片
        //长按设置图标
        BOOL isLongPressSetIcon = self.tabManager.selectedTab.longPressSetIconAction != nil;
        //关闭了长按识别图片
        BOOL enabledDetectPhoto = [[PreferenceManager shareInstance].items.enabledDetectPhoto boolValue];
        if(!enabledDetectPhoto && !isLongPressSetIcon) return;
        
        UIAlertController* alertController = [UIAlertController alertControllerWithTitle:title message:nil preferredStyle:UIAlertControllerStyleActionSheet];
//        if([BrowserUtils isiPad]) {
//            //适配iPad
//            UIPopoverPresentationController* popover = alertController.popoverPresentationController;
//            if(popover) {
//                popover.sourceView = self.tabManager.selectedTab.webView;
//                popover.sourceRect = CGRectMake(model.x, model.y, 0, 0);
//                popover.permittedArrowDirections = UIPopoverArrowDirectionAny;
//            }
//        }
        
        UIAlertAction* action = nil;
        
        //打开了长按识别图片
        if(enabledDetectPhoto) {
            action = [UIAlertAction actionWithTitle:NSLocalizedString(@"tips.saveToPhotos", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                @weakify(self)
                [[SDWebImageDownloader sharedDownloader] downloadImageWithURL:[NSURL URLWithString:url] options:SDWebImageDownloaderHighPriority progress:nil completed:^(UIImage *image, NSData *data, NSError *error, BOOL finished) {
                    @strongify(self)
                    UIImageWriteToSavedPhotosAlbum(image, self, @selector(saveImage2:didFinishSavingWithError:contextInfo:), nil);
                }];
            }];
            [alertController addAction:action];
            
            action = [UIAlertAction actionWithTitle:NSLocalizedString(@"contextmenu.copyLink", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                //复制到剪贴板
                if (url.length > 0) {
                    UIPasteboard *pasteboard = [UIPasteboard generalPasteboard];
                    pasteboard.string = url;
                    
                    [UIView showSucceed:NSLocalizedString(@"tips.pasteboard.success", nil)];
                }
            }];
            [alertController addAction:action];
        }

        //长按设置图标
        if(isLongPressSetIcon) {
            @weakify(self)
            action = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.set.icon", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                @strongify(self)
                if(self.tabManager.selectedTab.longPressSetIconAction) {
                    self.tabManager.selectedTab.longPressSetIconAction(url);
                }
                //返回首页
                [self bottomToolbarDidClickHome];
            }];
            [alertController addAction:action];
        }
        
        action = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.cancel", nil) style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
            [alertController dismissViewControllerAnimated:YES completion:nil];
        }];
        [alertController addAction:action];
        
//        [self presentViewController:alertController animated:YES completion:nil];
        //v2.6.8, 统一present方法，防止崩溃
        [alertController presentSafelyFromViewController:self
                                              sourceView:self.tabManager.selectedTab.webView
                                              sourceRect:CGRectMake(model.x, model.y, 0, 0)];
    } else if(url.length > 0) {
        NSURL* URL = [NSURL URLWithString:url];
        //普通的链接
        UIAlertControllerStyle style = UIAlertControllerStyleActionSheet;
        if([BrowserUtils isiPad]) {
            //iPad
            style = UIAlertControllerStyleAlert;
        }
        UIAlertController *alertController = [UIAlertController alertControllerWithTitle:nil message:nil preferredStyle:style];
        [alertController addAction:([UIAlertAction actionWithTitle:NSLocalizedString(@"alert.cancel", nil) style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
            
        }])];
        [alertController addAction:([UIAlertAction actionWithTitle:NSLocalizedString(@"contextmenu.openInNewTab", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            [self handleOpenInNewTabAction:URL];
        }])];
        
        [alertController addAction:([UIAlertAction actionWithTitle:NSLocalizedString(@"contextmenu.openDirectly", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            [self handleOpenAction:URL];
        }])];
        
        [alertController addAction:([UIAlertAction actionWithTitle:NSLocalizedString(@"popupwindow.setting.openInInBackWindow", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            [self handleOpenInBackendTabAction:URL];
        }])];
        
        [alertController addAction:([UIAlertAction actionWithTitle:NSLocalizedString(@"contextmenu.openInSafari", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            if([[UIApplication sharedApplication] canOpenURL:URL]) {
                [[UIApplication sharedApplication] openURL:URL options:@{} completionHandler:nil];
            }
        }])];
        
        [alertController addAction:([UIAlertAction actionWithTitle:NSLocalizedString(@"contextmenu.copyLink", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            [self handleCopyLinkContextMenu:URL];
        }])];
    
//        [self presentViewController:alertController animated:YES completion:nil];
        //v2.6.8, 统一present方法，防止崩溃
        [alertController presentSafelyFromViewController:self];
    }
}

- (void)saveImage2:(UIImage*)image didFinishSavingWithError:(NSError*)error contextInfo:(id)contextInfo
{
    if(!error) {
        [UIView showSucceed:NSLocalizedString(@"tips.savesuccess", nil)];
    }
}

//判断是否是油管
- (BOOL)_isYoutube:(NSString *)url
{
    if(url.length == 0) return NO;
    
    NSString* domain = [[NSURL URLWithString:url] normalizedHost];
    if([domain.lowercaseString rangeOfString:@"youtube.com"].location != NSNotFound) {
        return YES;
    }
    
    return NO;
}

#pragma mark -- 隐藏或者打开toolbar
- (void)showOrHideToolbar:(BOOL)isShow
{
    if([CommonDataManager shareInstance].isLockFullScreen) {
        //如果现在的状态是锁定全屏，那么则不需要显示工具栏
        return;
    }
    
    if(!isShow) {
        [self.topToolbar dismiss:YES];
        [self.bottomToolbar dismiss:YES];
    } else {
        [self.topToolbar show:YES];
        
        if([BrowserUtils isiPad]) {
            //iPad
        } else {
            //iPhone
            [self.bottomToolbar show:YES isHomePage:[self isHomePage]];
        }
    }
}

#pragma mark -- 隐藏导航栏
- (BaseNavigationBarStyle)preferredNavigationBarStyle
{
    return BaseNavigationBarStyleNoneWithDefaultContent;
}

#pragma mark -- SwipeNavigationManagerDelegate

- (BOOL)swipeNavigationCanGoBack:(SwipeNavigationManager *)manager {
    Tab* tab = self.tabManager.selectedTab;
    return [tab canGoBack];
}

- (BOOL)swipeNavigationCanGoForward:(SwipeNavigationManager *)manager {
    Tab* tab = self.tabManager.selectedTab;
    return [tab canGoForward];
}

- (void)swipeNavigationGoBack:(SwipeNavigationManager *)manager {
    Tab* tab = self.tabManager.selectedTab;
    [tab goBack];
}

- (void)swipeNavigationGoForward:(SwipeNavigationManager *)manager {
    Tab* tab = self.tabManager.selectedTab;
    [tab goForward];
}

// 返回当前webView
- (PandaWebView *)swipeNavigationGetCurrentWebView
{
    NSString* urlString = [self.tabManager.selectedTab.webView.URL absoluteString];
    if([InternalURL isAboutHomeURL:urlString]) {
        //首页
        return nil;
    } else {
        //webView
        return self.tabManager.selectedTab.webView;
    }
    
    return nil;
}

#pragma mark -- lazy init
- (TabManager *)tabManager
{
    if(!_tabManager) {
        _tabManager = [TabManager new];
        [_tabManager setBrowser:self];
        [_tabManager addDelegate:self];
        
        [[WebViewNavDelegate shareInstance] insert:(id<WKNavigationDelegate>)_tabManager];
        [WebViewNavDelegate shareInstance].tabManager = _tabManager;
        
        [[ScreenshotHelper shareInstance] setTabManager:_tabManager];
    }
    
    return _tabManager;
}

- (TopToolbar *)topToolbar
{
    if(!_topToolbar) {
        if([BrowserUtils isiPad]) {
            //iPad
#warning -- 这里要注意,强制TopToolbarForiPad赋值给TopToolbar，因为它们的属性和方法大部分都是一样的
            _topToolbar = [[TopToolbarForiPad alloc]init];
        } else {
            //iPhone
            _topToolbar = [[TopToolbar alloc]init];
        }
    
        _topToolbar.delegate = self;
        //设置滑动控制器
        self.toolBarScrollController.topToolbar = _topToolbar;
    }
    
    return _topToolbar;
}

- (BottomToolbar *)bottomToolbar
{
    if(!_bottomToolbar) {
        _bottomToolbar = [[BottomToolbar alloc]init];
        _bottomToolbar.delegate = self;
        
        self.toolBarScrollController.bottomToolbar = _bottomToolbar;
    }
    
    return _bottomToolbar;
}

- (PreferenceView *)preferenceView
{
    if(!_preferenceView) {
        _preferenceView = [PreferenceView new];
    }
    
    return _preferenceView;
}

@end
