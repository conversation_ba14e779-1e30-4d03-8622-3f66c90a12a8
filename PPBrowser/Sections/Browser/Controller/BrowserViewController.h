//
//  BrowserViewController.h
//  PandaBrowser
//
//  Created by qingbin on 2022/3/2.
//

#import "BaseViewController.h"

#import "TopToolbar.h"
#import "BottomToolbar.h"
#import "TopToolbarForiPad.h"

#import "TabManager.h"
#import "ScreenshotHelper.h"
#import "TabScrollController.h"
#import "BookMarkController.h"
#import "BaseNavigationController.h"
#import "ThemeProtocol.h"

#import "BrowserUtils.h"
#import "PreferencePopUpController.h"

@protocol TabDelegate;

@interface BrowserViewController : BaseViewController<ThemeProtocol>

@property (readonly) TopToolbar* topToolbar;
@property (readonly) BottomToolbar* bottomToolbar;
@property (readonly) TabManager* tabManager;
@property (readonly) NewTabPageView *ntpView;

@property (nonatomic, strong) TabScrollController* toolBarScrollController;

//保存当前书签controller
@property (nonatomic, strong) BookMarkController *bookMarkController;

//QQ登录的URL
@property (nonatomic, strong) NSURL* qqLoginURL;

// Controller must be retained otherwise `AirDrop` and other sharing options will fail!
@property (nonatomic, strong) UIDocumentInteractionController* documentInteractionController;
/// 横屏iPhone或者iPad
@property (nonatomic, strong) PreferencePopUpController* popUpPreferenceController;

- (void)updatePageStatusForTab:(Tab*)tab;

// 添加webView
- (void)addWebView:(UIView*)webView;

// 显示标签页
- (void)showTabTray;

// 打开个人设置页
- (void)openUserPreferenceView:(BOOL)isTriggerByBottomBar;

//关闭个人设置页
- (void)hideUserPreferenceView;

// 隐藏或者打开toolbar
- (void)showOrHideToolbar:(BOOL)isShow;

// 隐藏获取打开查找bar
- (void)updateFindInPageVisibility:(BOOL)visible text:(NSString*)text;

// 清空阅读模式和播放模式
- (void)clearWebViewReaderAndPlayer:(PandaWebView*)webView;

// 更新历史记录
- (void)navigateInTab:(Tab*)tab;

// 显示翻译工具栏
- (void)showTranslateToolbar;
// 隐藏翻译工具栏
- (void)hideTranslateToolbar;

@end

