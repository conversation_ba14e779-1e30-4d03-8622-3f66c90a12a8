//
//  NSURL+Extension.h
//  PPBrowser
//
//  Created by qingbin on 2022/3/20.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <UIKit/UIKit.h>

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface NSURL (Extension)

- (NSString*)schemelessAbsoluteString;

- (BOOL)schemeIsValid;

/**
 * Returns the second level domain (SLD) of a url. It removes any subdomain/TLD
 *
 * E.g., https://m.foo.com/bar/baz?noo=abc#123  => foo
 **/
//- (NSString*)hostSLD;

- (NSURL*)withoutWWW;

/**
 * Returns just the domain, but with the same scheme.
 *
 * E.g., https://m.foo.com/bar/baz?noo=abc#123  => https://foo.com
 *
 * Any failure? Return this URL.
 */
- (NSURL*)domainURL;

/**
 * Returns just the domain, and without the same scheme.
 *
 * E.g., https://m.foo.com/bar/baz?noo=abc#123  => foo.com
 *
 * Any failure? Return this URL.
 */
- (NSString*)normalizedHost;

@end

NS_ASSUME_NONNULL_END
