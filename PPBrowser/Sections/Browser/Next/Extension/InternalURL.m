//
//  InternalURL.m
//  PandaBrowser
//
//  Created by q<PERSON><PERSON> on 2022/3/3.
//

#import "InternalURL.h"

@interface InternalURL ()

@end

@implementation InternalURL

//internal://local/about/home?uuidkey=710413BF-6CDB-4BF3-B2E3-88F270B6CCD6#panel=0

/*
 internal://local/sessionrestore?history={
   "currentPage" : 0,
   "history" : [
     "internal:\/\/local\/about\/home#panel=0",
     "https:\/\/www.wikipedia.org\/"
   ]
 }&uuidkey=9E043546-C3EC-402A-A662-E80618D46C33
 */

+ (BOOL)isValid:(NSURL*)url
{
    return [[self scheme] isEqualToString:url.scheme];
}

+ (NSString *)scheme
{
    return @"internal";
}

+ (NSString *)baseUrl
{
    return [NSString stringWithFormat:@"%@://local", [InternalURL scheme]];
}

//home
+ (NSString*)homeUrlBaseUrl
{
    return [NSString stringWithFormat:@"%@/about/home",[self baseUrl]];
}

+ (NSString*)homeUrl
{
    NSString* url = [NSString stringWithFormat:@"%@",[self homeUrlBaseUrl]];
    return url;
}

+ (BOOL)isAboutHomeURL:(NSString*)url
{
    return [url containsString:[self homeUrlBaseUrl]];
}

//sessionrestore
+ (NSString*)sessionRestoreHistoryItemBaseUrl
{
    return [NSString stringWithFormat:@"%@/sessionrestore",[self baseUrl]];
}

+ (BOOL)isSessionRestore:(NSString*)url
{
    return [url containsString:[self sessionRestoreHistoryItemBaseUrl]];
}

+ (NSString*)sessionRestoreWrapperUrl:(NSString*)historyJson
{
    NSString* url = [NSString stringWithFormat:@"%@?history=%@",[self sessionRestoreHistoryItemBaseUrl], historyJson];
    return url;
}

//error
+ (NSString*)errorPageWithUrl:(NSString*)url
{
    //会有多次执行的情况,如果已经是错误页,那么直接返回
    if([InternalURL isErrorPage:url]) return url;
    
    return [NSString stringWithFormat:@"%@/error?errorUrl=%@",[self baseUrl], url];
}

+ (NSString*)errorPageBaseUrl
{
    return [NSString stringWithFormat:@"%@/error",[self baseUrl]];
}

+ (BOOL)isErrorPage:(NSString*)url
{
    return [url containsString:[self errorPageBaseUrl]];
}

//reader
//+ (NSString *)readerPageWithUrl:(NSString *)url
//{
//    if (!url || url.length == 0) return nil;
//    
//    //会有多次执行的情况,如果已经是阅读模式,那么直接返回
//    if([InternalURL isReaderPage:url]) return url;
//    
//    NSString *encodedUrl = [url stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
//    return [NSString stringWithFormat:@"%@/reader?url=%@",[self baseUrl], encodedUrl];
//}
//
//+ (NSString *)readerPageBaseUrl
//{
//    return [NSString stringWithFormat:@"%@/reader",[self baseUrl]];
//}
//
//+ (BOOL)isReaderPage:(NSString *)url
//{
//    NSString* readerUrl = [NSString stringWithFormat:@"%@?url=", [self readerPageBaseUrl]];
//    return [url containsString:readerUrl];
//}

// v2.7.6, 使用NSURLComponents解析URL，提取errorUrl的值
+ (NSString *)extractErrorUrlFromUrlString:(NSString *)urlString {
    // 创建NSURL对象
    NSURL *url = [NSURL URLWithString:urlString];
    if (!url) {
        return nil;
    }
    
    // 使用NSURLComponents解析URL
    NSURLComponents *components = [NSURLComponents componentsWithURL:url resolvingAgainstBaseURL:NO];
    
    // 遍历所有查询参数
    for (NSURLQueryItem *queryItem in components.queryItems) {
        if ([queryItem.name isEqualToString:@"errorUrl"]) {
            return queryItem.value;
        }
    }
    
    return nil;
}

// v2.7.6, 使用NSURLComponents解析URL，提取url的值
+ (NSString *)extractReaderUrlFromUrlString:(NSString *)urlString
{
    // 创建NSURL对象
    NSURL *url = [NSURL URLWithString:urlString];
    if (!url) {
        return nil;
    }
    
    // 使用NSURLComponents解析URL
    NSURLComponents *components = [NSURLComponents componentsWithURL:url resolvingAgainstBaseURL:NO];
    
    // 遍历所有查询参数
    for (NSURLQueryItem *queryItem in components.queryItems) {
        if ([queryItem.name isEqualToString:@"url"]) {
            return queryItem.value;
        }
    }
    
    return nil;
}

//解析真正的url
//+ (NSString *)parseSessionRestoreURL:(NSString *)urlString
//{
//    NSURLComponents *components = [NSURLComponents componentsWithString:urlString];
//    if (!components) return nil;
//    
//    NSString *historyValue = nil;
//    for (NSURLQueryItem *item in components.queryItems) {
//        if ([item.name isEqualToString:@"history"]) {
//            historyValue = item.value;
//            break;
//        }
//    }
//    if (!historyValue) return nil;
//    
//    NSString *jsonString = [historyValue stringByRemovingPercentEncoding];
//    NSData *jsonData = [jsonString dataUsingEncoding:NSUTF8StringEncoding];
//    if (!jsonData) return nil;
//    
//    NSError *error;
//    NSDictionary *historyDict = [NSJSONSerialization JSONObjectWithData:jsonData options:0 error:&error];
//    if (error || ![historyDict isKindOfClass:[NSDictionary class]]) return nil;
//    
//    NSNumber *currentPage = historyDict[@"currentPage"];
//    NSArray *history = historyDict[@"history"];
//    
//    if (![currentPage isKindOfClass:[NSNumber class]] || ![history isKindOfClass:[NSArray class]]) return nil;
//    if (currentPage.integerValue >= history.count) return nil;
//    
//    id currentURL = history[currentPage.integerValue];
//    
//    return [self resolveNestedHistory:currentURL];
//}
//
//+ (NSString *)resolveNestedHistory:(id)historyEntry
//{
//    if ([historyEntry isKindOfClass:[NSString class]]) {
//        return historyEntry;
//    } else if ([historyEntry isKindOfClass:[NSDictionary class]]) {
//        NSNumber *currentPage = historyEntry[@"currentPage"];
//        NSArray *history = historyEntry[@"history"];
//        
//        if (![currentPage isKindOfClass:[NSNumber class]] || ![history isKindOfClass:[NSArray class]]) return nil;
//        if (currentPage.integerValue >= history.count) return nil;
//        
//        return [self parseSessionRestoreURL:history[currentPage.integerValue]];
//    }
//    
//    return nil;
//}

@end
