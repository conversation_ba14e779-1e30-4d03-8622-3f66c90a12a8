//
//  InternalURL.h
//  PandaBrowser
//
//  Created by q<PERSON><PERSON> on 2022/3/3.
//

#import <Foundation/Foundation.h>

@interface InternalURL : NSObject

//是否是internal:开头的scheme
+ (BOOL)isValid:(NSURL*)url;

//首页
+ (NSString*)homeUrl;
+ (BOOL)isAboutHomeURL:(NSString*)url;

//session
+ (NSString*)sessionRestoreHistoryItemBaseUrl;
+ (BOOL)isSessionRestore:(NSString*)url;
+ (NSString*)sessionRestoreWrapperUrl:(NSString*)historyJson;

//error
+ (NSString*)errorPageWithUrl:(NSString*)url;
+ (BOOL)isErrorPage:(NSString*)url;
// v2.7.6, 使用NSURLComponents解析URL，提取errorUrl的值
+ (NSString *)extractErrorUrlFromUrlString:(NSString *)urlString;

//reader
//+ (NSString *)readerPageWithUrl:(NSString *)url;
//+ (BOOL)isReaderPage:(NSString *)url;
//+ (NSString *)readerPageBaseUrl;
// v2.7.6, 使用NSURLComponents解析URL，提取url的值
+ (NSString *)extractReaderUrlFromUrlString:(NSString *)urlString;

//+ (NSString*)uuid;
+ (NSString *)scheme;
+ (NSString *)baseUrl;

//解析真正的url
//+ (NSString *)parseSessionRestoreURL:(NSString *)urlString;

@end

