//
//  Tab.m
//  PandaBrowser
//
//  Created by qingbin on 2022/3/2.
//

#import "Tab.h"
#import "InternalURL.h"
#import "InternalSchemeHandler.h"
#import "SystemScriptManager.h"
#import "PreferenceManager.h"
#import "ReactiveCocoa.h"
#import "UserAgentManager.h"
#import "UserCommandManager.h"

#import "MaizyHeader.h"

#import "NSURL+Extension.h"
#import "WebDataStoreManager.h"
#import "PPNotifications.h"

@interface Tab ()

@property (nonatomic, strong) WKWebViewConfiguration *configuration;

@end

@implementation Tab

- (instancetype)initWithConfiguration:(WKWebViewConfiguration*)configuration
{
    self = [super init];
    if(self) {
        self.configuration = configuration;
        self.commandManager = [[UserCommandManager alloc]initWithTab:self];
    }
    
    return self;
}

- (void)dealloc
{
    [self removeWebView];
}

- (void)removeWebView
{
    if(self.webView) {
        if(self.tabDelegate && [self.tabDelegate respondsToSelector:@selector(tab:didDeleteWebView:)]) {
            [self.tabDelegate tab:self didDeleteWebView:self.webView];
        }
        
        [self.webView removeFromSuperview];
        self.webView = nil;
    }
}

- (void)createWebViewWithCanCustomWebstore:(BOOL)canCustomWebstore
{
    if(!self.webView) {
        //这一步很重要,在处理about:blank这种情况时,如果不重新new userContentController,
        //那么因为about:blank传过来的configuration是同一个,就会导致重复添加js call oc的代码,
        //从而导致程序崩溃。
        //因此将configuration的设置放到createWebView中,从而保证每次新建webView都是新的userContentController/preferences。
        self.configuration.userContentController = [WKUserContentController new];
        self.configuration.preferences = [WKPreferences new];
        self.configuration.preferences.javaScriptCanOpenWindowsAutomatically = false;
        self.configuration.allowsInlineMediaPlayback = YES;
        // Enables Zoom in website by ignoring their javascript based viewport Scale limits.
        self.configuration.ignoresViewportScaleLimits = YES;
        
        // 不可重复注册
        if(![self.configuration urlSchemeHandlerForURLScheme:[InternalURL scheme]]) {
            InternalSchemeHandler *handler = [InternalSchemeHandler new];
            [self.configuration setURLSchemeHandler:handler forURLScheme:[InternalURL scheme]];
        }
        
        //v2.7.4，隐私模式逻辑修复
        if (canCustomWebstore) {
            //v2.7.4，如果是通过window.open或者_target=blank触发的创建tab，不能设置WKWebsiteDataStore，否则会导致应用程序崩溃。例如missav。
            //因此需要区分是用户手动打开的，还是由window.open或者_target=blank触发的。
            BOOL isPrivate = [[PreferenceManager shareInstance].items.isPrivate boolValue];
            if (isPrivate) {
                // 隐私模式
                // 判断是否需要共享状态(例如登录状态)
                BOOL sharedSession = [[PreferenceManager shareInstance].items.sharedSession boolValue];
                if (sharedSession) {
                    //共享登录状态
                    WKWebsiteDataStore* datastore = [[WebDataStoreManager sharedManager] getDataStoreForMode:WebDataStoreModeSharedPrivate identifierName:nil];
                    self.configuration.websiteDataStore = datastore;
                } else {
                    //不共享登录状态
                    WKWebsiteDataStore* datastore = [[WebDataStoreManager sharedManager] getDataStoreForMode:WebDataStoreModeIsolatedPrivate identifierName:nil];
                    self.configuration.websiteDataStore = datastore;
                }
            } else {
                // 正常模式
                if(!self.configuration.websiteDataStore) {
                    //v2.7.5, 正常模式下，不能强制赋值websiteDataStore，否则已登录状态需要重新刷新一下才会更新
                    WKWebsiteDataStore* datastore = [[WebDataStoreManager sharedManager] getDataStoreForMode:WebDataStoreModeDefault identifierName:nil];
                    self.configuration.websiteDataStore = datastore;
                }
            }
        }

        self.webView = [[PandaWebView alloc] initWithConfiguration:self.configuration];
        self.webView.allowsBackForwardNavigationGestures = YES;
        self.webView.allowsLinkPreview = YES;
        
        //iOS16.4之后，需要设置该属性才能调试web页
        //https://blog.csdn.net/siwen1990/article/details/130363477
    #if DEBUG
        if(@available(iOS 16.4, *)) {
        #if __IPHONE_OS_VERSION_MAX_ALLOWED >= 160400
            // 在 iOS 16.4 或更高版本下会执行这里的代码
            self.webView.inspectable = YES;
        #endif
        }
    #endif
        
        // Turning off masking allows the web content to flow outside of the scrollView's frame
        // which allows the content appear beneath the toolbars in the BrowserViewController
        self.webView.scrollView.layer.masksToBounds = NO;
        
        self.webView.tab = self;
        
        [self loadUserScripts];
        
        if(self.tabDelegate && [self.tabDelegate respondsToSelector:@selector(tab:didCreateWebView:)]) {
            [self.tabDelegate tab:self didCreateWebView:self.webView];
        }
        
        @weakify(self)        
        self.webView.didLoadRequestBlock = ^(NSURLRequest *request) {
            @strongify(self)
            [self loadRequest:request];
        };
        
        self.webView.didFindInBarBlock = ^(NSString *text) {
            @strongify(self)
            if(self.tabDelegate && [self.tabDelegate respondsToSelector:@selector(tab:didFindInBar:text:)]) {
                [self.tabDelegate tab:self didFindInBar:self.webView text:text];
            }
        };
    }
}

- (void)loadRequest:(NSURLRequest *)request
{
    self.model.url = [request.URL absoluteString];
    
    NSURL* url = request.URL;
    if(url.isFileURL) {
        [self.webView loadFileURL:url allowingReadAccessToURL:url];
        return;
    }

    [self.webView loadRequest:request];
}

// The web view can go gray if it was zombified due to memory pressure.
// When this happens, the URL is nil, so try restoring the page upon selection.
- (void)reload
{
    // Clear the user agent before further navigation.
    // Proper User Agent setting happens in BVC's WKNavigationDelegate.
    // This prevents a bug with back-forward list, going back or forward and reloading the tab
    // loaded wrong user agent.
    self.webView.customUserAgent = nil;
    
    NSURL* url = self.webView.URL;
    NSString* urlString = [url absoluteString];
    if([InternalURL isSessionRestore:urlString]) {
        return;
    }
    
    //错误页判断
    if([InternalURL isErrorPage:urlString]) {
        NSRange range = [urlString rangeOfString:@"errorUrl="];
        NSInteger location = range.location+range.length;
        NSInteger length = urlString.length - (range.location+range.length);
        NSString* errorUrl = [urlString substringWithRange:NSMakeRange(location, length)];
        
        NSString* js = [NSString stringWithFormat:@"window.location.replace(\'%@\')",errorUrl];
        [self.webView evaluateJavaScript:js completionHandler:^(id _Nullable obj, NSError * _Nullable error) {
            if(error) {
                LOG_ERROR(@"error = %@", error.localizedDescription);
            }
        }];
        
        return;
    }
    
    //退出阅读模式
    [[NSNotificationCenter defaultCenter] postNotificationName:kExitReaderNotification object:self.webView];
    self.webView.isInReader = NO;
    
    //清空视频模式缓存数据
    [self.webView clearWebViewReaderAndPlayer];
    
    //reloaded zombified tab from origin
    [self.webView reloadFromOrigin];
}

#pragma mark -- 加载UserScripts
- (void)loadUserScripts
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    // For WKWebView background color to take effect, isOpaque must be false,
    // which is counter-intuitive. Default is true. The color is previously
    // set to black in the WKWebView init.
    self.webView.opaque = !isDarkTheme;
    
    [[SystemScriptManager shareInstance] injectUserSciptsToWebView:self.webView completion:nil];
}

- (void)goBack
{
    [self.webView goBack];
}

- (void)goForward
{
    [self.webView goForward];
}

- (BOOL)canGoBack
{
    return [self.webView canGoBack];
}

- (BOOL)canGoForward
{
    return [self.webView canGoForward];
}

- (void)stop
{
    [self.webView stopLoading];
}

- (void)updateUserAgentWithURL:(NSURL *)URL
{
    NSString* UA = [[UserAgentManager shareInstance] getCurrentUserAgent];
    self.webView.customUserAgent = UA;
}

- (NSArray<NSString*>*)historyList
{
    NSMutableArray* urls = [NSMutableArray array];
    for(WKBackForwardListItem* item in self.webView.backForwardList.backList) {
        [urls addObject:item.URL.absoluteString];
    }
    
    return urls;
}

- (NSArray<NSString*>*)forwardList
{
    NSMutableArray* urls = [NSMutableArray array];
    for(WKBackForwardListItem* item in self.webView.backForwardList.forwardList) {
        [urls addObject:item.URL.absoluteString];
    }
    
    return urls;
}

- (int)hasHomeUrl
{
    int (^handleBlock)(NSArray *) = ^int(NSArray *urls){
        for(int i=0;i<urls.count;i++) {
            __block NSString* url = urls[i];
            while([InternalURL isSessionRestore:url]) {
                //解包
                NSURLComponents* components = [[NSURLComponents alloc]initWithString:url];
                [components.queryItems enumerateObjectsUsingBlock:^(NSURLQueryItem * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
                    NSString* key = obj.name;
                    NSString* value = obj.value;
                    if([key isEqualToString:@"url"]) {
                        url = value;
                        *stop = YES;
                    }
                }];
            }
            
            NSURL* URL = [NSURL URLWithString:url];
            url = URL.absoluteString;
            
            if([InternalURL isAboutHomeURL:url]) {
                return i;
            }
        }
        
        return -1;
    };
    
    NSArray* historyList = [self historyList];
    
    int res = handleBlock(historyList);
    if(res != -1) {
        return res;
    }
    
    NSArray* forwardList = [self forwardList];
    res = handleBlock(forwardList);
    if(res != -1) {
        res = res + 1 + (int)historyList.count;
        return res;
    }
    
    return -1;
}

- (TabModel *)model
{
    if(!_model) {
        _model = [TabModel new];
    }
    
    return _model;
}

@end
