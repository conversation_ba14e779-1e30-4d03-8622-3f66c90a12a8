//
//  TabManager+WKNavigationDelegate.m
//  PandaBrowser
//
//  Created by qingbin on 2022/3/9.
//

#import "TabManager+WKNavigationDelegate.h"
#import "InternalURL.h"

@implementation TabManager (WKNavigationDelegate)

/// Called when the WKWebView's content process has gone away. If this happens for the currently selected tab
/// then we immediately reload it.
- (void)webViewWebContentProcessDidTerminate:(WKWebView *)webView
{
    // 进程被终止时调用
    if(self.selectedTab.webView == webView) {
        [webView reload];
    }
}

// 2 页面开始加载时调用
- (void)webView:(WKWebView *)webView didStartProvisionalNavigation:(WKNavigation *)navigation
{
    //加载时调用一次
    [self _saveTabInWebView:webView];
}

// 5 页面加载完成之后调用
- (void)webView:(WKWebView *)webView didFinishNavigation:(null_unspecified WKNavigation *)navigation
{
    //加载结束再调用一次
    [self _saveTabInWebView:webView];
}

- (void)_saveTabInWebView:(WKWebView *)webView
{
    // only store changes if this is not an error page
    // as we current handle tab restore as error page redirects then this ensures that we don't
    // call storeChanges unnecessarily on startup
    
    NSURL* URL = webView.URL;
    if(!URL) return;
    
    // tab restore uses internal pages,
    // so don't call storeChanges unnecessarily on startup
    // 不保存sessionRestore开头的堆栈URL,只保存真实的URL
    // 备注: 此处逻辑主要用于关掉浏览器之后,重新打开浏览器能够实现保存历史的功能
    // 在sessionRestore.html和sessionRestoreHandler中, 通过js实现了将历史表中的url转换为
    // internal://local/sessionrestore的格式, 此时会发生三次请求, 第一次是location.go()
    // 第二次是location.replace(), 前两次都是sessionrestore格式, 主要处理保存历史相关功能,
    // 第三次才是真正的URL请求。
    if([InternalURL isSessionRestore:URL.absoluteString]) return;
    
    Tab* tab = [self tabForWebView:webView];
    if(tab) {
        [self saveTab:tab];
    }
}

@end
