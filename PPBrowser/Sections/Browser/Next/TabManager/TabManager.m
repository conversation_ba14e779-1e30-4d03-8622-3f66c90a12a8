//
//  TabManager.m
//  PandaBrowser
//
//  Created by qingbin on 2022/3/2.
//

#import "TabManager.h"
#import "InternalURL.h"
#import "InternalSchemeHandler.h"

#import "DatabaseUnit+Helper.h"

#import "TabModel.h"
#import "ReactiveCocoa.h"
#import "BrowserViewController.h"
#import "MaizyHeader.h"

#import "HomeHelper.h"
#import "PPNotifications.h"
#import "ScreenshotHelper.h"

@interface TabManager ()

@property (nonatomic, strong) NSPointerArray* delegates;
@property (nonatomic, assign) bool isRestoring;
@end

@implementation TabManager

- (instancetype)init
{
    self = [super init];
    if (self) {
        [self bindData];
    }
    
    return self;
}

#pragma mark - 数据绑定

- (void)bindData
{
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(_updateAutoPageUrl:) name:kUpdateAutoPageUrlNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(closeAllTabsAndReloadSelectedTab) name:kIncognitoStatusChangedNotification object:nil];
}

- (void)_updateAutoPageUrl:(NSNotification *)notification
{
    //自动翻页，更新url
    //webView.backForwardList.currentItem.URL即对应输入框最新的url，非常棒
    Tab* tab = self.selectedTab;
    [self saveTab:tab];
}

#pragma mark - Helpers

- (Tab*)generateTabWithModel:(TabModel *)tabModel configuration:(WKWebViewConfiguration*)configuration
{
    if(!tabModel) return nil;
    
    Tab* tab = [[Tab alloc] initWithConfiguration:configuration];
    tab.model = tabModel;
    tab.tabDelegate = (id<TabDelegate>)self.browser;
    
    //v2.7.4，如果是通过window.open或者_target=blank触发的创建tab，不能设置WKWebsiteDataStore，否则会导致应用程序崩溃。例如missav。
    //因此需要区分是用户手动打开的，还是由window.open或者_target=blank触发的。
    [tab createWebViewWithCanCustomWebstore:NO];
        
    return tab;
}

- (Tab*)generateTabWithModel:(TabModel*)tabModel
{
    if(!tabModel) return nil;
    WKWebViewConfiguration* configuration = [self makeWebViewConfiguration];
    Tab* tab = [[Tab alloc] initWithConfiguration:configuration];
    tab.model = tabModel;
    tab.tabDelegate = (id<TabDelegate>)self.browser;
    
    //v2.7.4，如果是通过window.open或者_target=blank触发的创建tab，不能设置WKWebsiteDataStore，否则会导致应用程序崩溃。例如missav。
    //因此需要区分是用户手动打开的，还是由window.open或者_target=blank触发的。
    [tab createWebViewWithCanCustomWebstore:YES];
        
    return tab;
}

- (Tab*)addTab:(Tab*)tab
{
    [self.allTabs addObject:tab];
    
    //保证插入顺序, 在didFinish中调用saveTab才是真正的保存数据
    NSString* url = tab.model.url;
    if(url.length == 0) url = @"";
    
    //由于alltabs不是表示所有数据,因此采用以下策略:
    //新开的页面order都是取最大值,已存在的页面由于只更新url不改变order
    //所以不会影响到历史tab。
    //因此同样是最大值的order,可以根据插入的时间来判断顺序
    //然后打开标签页时,再重新修改成正确的order。
    DatabaseUnit* unit = [DatabaseUnit insertIntoTabWithTabId:tab.model.tabId
                                                          url:url
                                                        order:INT_MAX];
    DB_EXEC(unit);
    
    [self notifyDelegateDidAddTab:tab];
    
    if(tab.model.url.length > 0) {
        NSURLRequest* request = [[NSURLRequest alloc]initWithURL:[NSURL URLWithString:tab.model.url]];
        [tab loadRequest:request];
    }
    
    return tab;
}

- (void)selectTab:(Tab*)tab
{
    Tab* previous = self.selectedTab;
    if(previous) {
        previous.model.isSelected = NO;
    }
    
    _selectIndex = [self indexOfTab:tab];
    tab.model.isSelected = YES;
    
    DatabaseUnit* unit = [DatabaseUnit updateSelectedTabWithTabId:tab.model.tabId];
    DB_EXEC(unit);
    
    [self notifyDelegateWithSelectedTab:tab previous:previous];
}

- (Tab*)addTabAndSelect:(TabModel*)tabModel
{
    Tab* tab = [self generateTabWithModel:tabModel];
    [self addTab:tab];
    [self selectTab:tab];
    
    return tab;
}

#pragma mark -- 保存tab
- (void)saveTab:(Tab *)tab
{
    // 只保存真正的url
    // 第三次才是真正的URL请求。
    NSString* url = tab.webView.URL.absoluteString;
    if (url.length == 0) return;
    if([InternalURL isSessionRestore:url]) return;
    
    [self updateTabModelWhenFinishNavigation:tab];
    
    TabModel* model = tab.model;
        
    DatabaseUnit* unit = [DatabaseUnit insertIntoTabWithTitle:model.title
                                                          url:model.url
                                                        tabId:model.tabId
                                           urlHistorySnapshot:model.urlHistorySnapshot
                                       urlHistoryCurrentIndex:model.urlHistoryCurrentIndex
                                                   isSelected:model.isSelected];
    DB_EXEC(unit);
}

- (Tab *)restoreTab:(TabModel*)tabModel
{//internal://local/about/home
    
    tabModel = [self updateRestoreTabModel:tabModel];
    return [self addTabAndSelect:tabModel];
}

//后台打开
- (Tab *)restoreTabWithoutSelect:(TabModel *)tabModel
{
    tabModel = [self updateRestoreTabModel:tabModel];
    Tab* tab = [self generateTabWithModel:tabModel];
    return [self addTab:tab];
}

//根据urlHistorySnapshot更新TabModel
- (TabModel *)updateRestoreTabModel:(TabModel *)tabModel
{
    NSArray* urls = [tabModel.urlHistorySnapshot componentsSeparatedByString:@","];
    int currentPage = tabModel.urlHistoryCurrentIndex;
    
    //v2.7.6，阅读模式url逻辑
    //1、如果最后一项是阅读模式url，那么删除internal://local/reader?url= 前缀，提取真实的url
    //2、交互体验优化，如果上一页的url和阅读模式的url是一样的，那么删除重复项，只保留一项
//    urls = [self _processURLsForReaderMode:urls];
    
    //3、阅读模式处理url，也提取真正的url
//    if ([InternalURL isReaderPage:tabModel.url]) {
//        // 提取真实URL
//        tabModel.url = [InternalURL extractReaderUrlFromUrlString:tabModel.url];
//    }
    
    //这里有一个BUG,如果由于崩溃等原因,导致没有记录到urls,页面打开会变成空白
    //交互非常差,iPad版还会导致没法打开APP,因此如果没有记录到urls,那么则返回首页
    TabSessionRestoreModel* obj = [TabSessionRestoreModel new];
    
    if(urls.count > 0) {
        obj.history = urls;
    } else {
//        obj.history = @[@"internal://local/about/home"];
        obj.history = @[[InternalURL homeUrl]];
    }
    
    //可能会出现丢失首页的BUG，如果丢失了首页，那么则插入首页
    if (![HomeHelper isCustomUrl]) {
        //默认主页
        
        //如果是内部链接，先解码出真正的链接
        BOOL hasHomeUrl = YES;
        NSString* homeHistoryUrl = obj.history.firstObject;
        
        if ([InternalURL isValid:[NSURL URLWithString:homeHistoryUrl]]) {
            NSURLComponents* components = [NSURLComponents componentsWithString:homeHistoryUrl];
            NSArray* queryItems = components.queryItems;
            for(NSURLQueryItem* item in queryItems) {
                if([item.name isEqualToString:@"url"]) {
                    homeHistoryUrl = item.value;
                }
            }
        }
        
        //自定义主页，因为用户可能不是输入全量路径的，导致判断容易出错，因此不处理自定义主页的情况
        if (![InternalURL isAboutHomeURL:homeHistoryUrl]) {
            hasHomeUrl = NO;
        }
        
        if (!hasHomeUrl) {
            //没有首页，插入首页
            NSString* customUrl = [HomeHelper getCustomUrl];
            
            NSMutableArray* history = [NSMutableArray array];
            [history addObject:customUrl];
            [history addObjectsFromArray:obj.history];
            
            obj.history = [history copy];
            currentPage += 1;
        }
    }
    
    obj.currentPage = currentPage;
    NSString* json = [obj toJSONString];
    
    //URLQueryAllowedCharacterSet和stringByRemovingPercentEncoding之间的相互转换
    //如果没有URLQueryAllowedCharacterSet,那么loadRequest无效;
    //而webview中的url则需要stringByRemovingPercentEncoding转换,否则无法访问
    NSString* restoreURLString = [InternalURL sessionRestoreWrapperUrl:json];
    restoreURLString = [restoreURLString stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
    tabModel.url = restoreURLString;
    
    return tabModel;
}

/**
 * v2.7.6
 * 处理URL数组，特别处理阅读模式URL
 * 如果最后一项是阅读模式URL(internal://local/reader?url=xxx)，提取真实URL
 * 如果提取的URL与数组中已有URL重复，则删除重复项
 * @param urls URL字符串数组
 * @return 处理后的URL数组
 */
//- (NSArray<NSString *> *)_processURLsForReaderMode:(NSArray<NSString *> *)urls {
//    // 检查数组是否为空
//    if (urls.count == 0) {
//        return urls;
//    }
//    
//    // 获取最后一个URL
//    NSString *lastUrl = [urls lastObject];
//    
//    if ([InternalURL isReaderPage:lastUrl]) {
//        // 提取真实URL
//        NSString *actualUrl = [InternalURL extractReaderUrlFromUrlString:lastUrl];
//        
//        // 如果成功提取了真实URL
//        if (actualUrl) {
//            // 创建可变数组用于修改
//            NSMutableArray *processedURLs = [urls mutableCopy];
//            [processedURLs removeLastObject]; // 移除阅读模式URL
//            
//            // 检查真实URL是否已经存在于数组中
//            NSUInteger existingIndex = [processedURLs indexOfObject:actualUrl];
//            if (existingIndex != NSNotFound) {
//                // 如果已存在，则不添加
//                return [processedURLs copy];
//            } else {
//                // 如果不存在，则添加真实URL
//                [processedURLs addObject:actualUrl];
//                return [processedURLs copy];
//            }
//        }
//    }
//    
//    // 如果最后一项不是阅读模式URL或提取失败，返回原数组
//    return [urls copy];
//}

- (void)updateTabModelWhenFinishNavigation:(Tab *)tab
{
    PandaWebView* webView = tab.webView;
    if(!webView) return;
    
    int order = [self indexOfWebView:webView];
    int currentPage = 0;
    
    NSArray* backList = [tab historyList];
    NSArray* forwardList = [tab forwardList];
        
    NSString* currentItemUrl = webView.backForwardList.currentItem.URL.absoluteString;
    //qq音乐点播放会崩溃, currentItemUrl为空, 加一个判断
    if(currentItemUrl.length == 0) {
        //得到的是about:blank
        currentItemUrl = tab.webView.URL.absoluteString;
    }
    
    NSMutableArray* urls = [NSMutableArray array];
    [urls addObjectsFromArray:backList];
    [urls addObject:currentItemUrl];
    [urls addObjectsFromArray:forwardList];
    currentPage = (int)-forwardList.count;
    
    BOOL isSelected = (self.selectedTab == tab);
    
    tab.model.ppOrder = order;
    tab.model.urlHistoryCurrentIndex = currentPage;
    tab.model.isSelected = isSelected;
    
    [tab.model updateTitleWithWebViewTitle:tab.webView.title url:tab.webView.URL toDisk:NO];
    
    tab.model.urlHistorySnapshot = [urls componentsJoinedByString:@","];
    
//    NSLog(@"url stack = %@", tab.model.urlHistorySnapshot);
}

#pragma mark -- 标签页中的删除操作
- (BOOL)removeTabWithModel:(TabModel*)item allTabModels:(NSMutableArray*)allTabModels
{
    //需要同步维护allTabModels, 保证标签页从内存中的操作
    
    Tab* tab = [self tabForTabModel:item];
    if(!tab) {
        //删除的是历史数据,还没生成tab
    } else {
        [self.allTabs removeObject:tab];
        tab.tabDelegate = nil;
        tab.webView.navigationDelegate = nil;
        [tab removeWebView];
        
        //v2.7.2,清理本地截图
        [ScreenshotHelper removeScreenshot:tab.model.tabId];
    }
    
    DatabaseUnit* unit = [DatabaseUnit deleteTabWithTabId:item.tabId];
    DB_EXEC(unit);

    for(TabModel* obj in allTabModels) {
        if([obj.tabId isEqualToString:item.tabId]) {
            [allTabModels removeObject:obj];
            break;
        }
    }
    
    // 如果全部删除了,那么打开新的首页
    BOOL openNewTab = NO;
    if(allTabModels.count == 0) {
        TabModel* tabModel = [HomeHelper getHomeTabModel];
        Tab* tab = [self addTabAndSelect:tabModel];
        [[ScreenshotHelper shareInstance] takeScreenshot:tab];

        [allTabModels addObject:tabModel];
        
        openNewTab = YES;
    }

    if(item.isSelected) {
        //移除已选中的tab, 找到最后的一个tab,并且将其状态置为选中
        TabModel* tabModel = allTabModels.lastObject;
        tabModel.isSelected = YES;
        
        //已经加载的Tab
        Tab* tab = [self tabForTabModel:tabModel];
        if (!tab) {
            tab = [self addTabAndSelect:tabModel];
            //v2.7.3，这里不能截图，否则会导致倒数第二张Tab白屏
//            [[ScreenshotHelper shareInstance] takeScreenshot:tab];
        } else {
            [self selectTab:tab];
        }
    } else {
        //移除非选中的tab
    }

    return openNewTab;
}

#pragma mark -- 标签页中进行删除全部标签页操作
- (void)removeAllTabsFromTabTray
{
    [self.browser.bottomToolbar updateTabCount:1];
    [self.browser.topToolbar updateTabCount:1];
    
    for(Tab* tab in self.allTabs) {
        //处理已经加载到内存中的tab
        
        tab.tabDelegate = nil;
        tab.webView.navigationDelegate = nil;
        [tab removeWebView];
    }
    [self.allTabs removeAllObjects];
    
    //查询所有id，并且清理截图
    DatabaseUnit* unit = [DatabaseUnit selectAllTabs];
    [unit setCompleteBlock:^(NSArray<TabModel *>* result, BOOL success) {
        if (success && result.count>0) {
            for (TabModel* item in result) {
                //v2.7.2,清理本地截图
                [ScreenshotHelper removeScreenshot:item.tabId];
            }
        }
    }];
    DB_EXEC(unit);
    
    //删除数据库中所有的tab
    unit = [DatabaseUnit deleteAllTabs];
    DB_EXEC(unit);
    
    TabModel* tabModel = [HomeHelper getHomeTabModel];
    Tab* tab = [self addTabAndSelect:tabModel];
    [[ScreenshotHelper shareInstance] takeScreenshot:tab];
}

#pragma mark -- 关闭当前标签
- (void)closeCurrentTab:(NSArray *)allTabModelArray
{
    TabModel* currentTabModel = self.selectedTab.model;
    
    BOOL openNewTab = [self removeTabWithModel:currentTabModel allTabModels:[allTabModelArray mutableCopy]];
    
    if(openNewTab) {
        //更新标签数量
        [self.browser.bottomToolbar updateTabCount:1];
        [self.browser.topToolbar updateTabCount:1];
    } else {
        //更新标签数量
        int allTabsCount = [BrowserUtils shareInstance].allTabsCount;
        allTabsCount--;
        if(allTabsCount <= 0) {
            allTabsCount = 1;
        }
        [self.browser.bottomToolbar updateTabCount:allTabsCount];
        [self.browser.topToolbar updateTabCount:allTabsCount];
        
        //更新order
        NSMutableArray* items = [NSMutableArray array];
        for(int i=0;i<allTabModelArray.count;i++) {
            TabModel* item = allTabModelArray[i];
            item.ppOrder = i;
                        
            [items addObject:item];
        }
        DatabaseUnit* unit = [DatabaseUnit updateAllTabsOrder:items];
        DB_EXEC(unit);
    }
}

#pragma mark -- 关闭其他标签
- (void)closeOtherTabs:(NSArray *)allTabModelArray
{
    if(allTabModelArray.count <= 1) return;
        
    NSMutableArray* removeIds = [NSMutableArray array];
    NSMutableArray* removeTabs = [NSMutableArray array];
    for(TabModel* item in allTabModelArray) {
        if(item.isSelected) continue;
        
        Tab* tab = [self tabForTabModel:item];
        if(!tab) {
            //删除的是历史数据,还没生成tab
        } else {
            [self.allTabs removeObject:tab];
            tab.tabDelegate = nil;
            tab.webView.navigationDelegate = nil;
            [tab removeWebView];
        
            [removeTabs addObject:tab];
        }
        
        [removeIds addObject:item.tabId];
        //v2.7.2,清理本地截图
        [ScreenshotHelper removeScreenshot:item.tabId];
    }
    
    DatabaseUnit* unit = [DatabaseUnit deleteTabArrayWithTabIds:removeIds];
    DB_EXEC(unit);
        
    [self.browser.bottomToolbar updateTabCount:1];
    [self.browser.topToolbar updateTabCount:1];
}

#pragma mark - 关闭所有webview，重新加载
//v2.7.4，由于触发了无痕模式的变更，因此关闭所有webview，再重新加载已选的webview
- (void)closeAllTabsAndReloadSelectedTab
{
    //先找到已选的tab
    TabModel* selectItem;
    for(Tab* tab in self.allTabs) {
        if(tab.model.isSelected) {
            selectItem = tab.model;
            break;
        }
    }
    
    if(!selectItem) {
        //如果不存在,有bug,会导致崩溃
        selectItem = self.allTabs.lastObject;
        selectItem.isSelected = YES;
    }
    
    if(!selectItem) {
        //新开一页
        selectItem = [HomeHelper getHomeTabModel];
    }
    
    for(Tab* tab in self.allTabs) {
        //处理已经加载到内存中的tab
        tab.tabDelegate = nil;
        tab.webView.navigationDelegate = nil;
        [tab removeWebView];
    }
    [self.allTabs removeAllObjects];
    
    //打开历史记录
    [self restoreTab:selectItem];
}

- (int)indexOfTab:(Tab*)tab
{
    int count = 0;
    for(id obj in self.allTabs) {
        if(obj == tab) {
            return count;
        }
        count++;
    }
    
    return -1;
}

- (Tab*)tabForWebView:(WKWebView*)webView
{
    for(Tab* tab in self.allTabs) {
        if(tab.webView == webView) return tab;
    }
    
    return nil;
}

- (Tab*)tabForTabModel:(TabModel*)tabModel
{
    //注意，这个是获取已加载的tab的model
    for(Tab* tab in self.allTabs) {
        if([tab.model.tabId isEqualToString:tabModel.tabId]) return tab;
    }
    
    return nil;
}

- (int)indexOfWebView:(WKWebView*)webView
{
    int count = 0;
    @synchronized (self) {
        for(Tab* obj in self.allTabs) {
            if(obj.webView == webView) {
                return count;
            }
            count++;
        }
    }
    
    return count;
}

- (WKWebViewConfiguration*)makeWebViewConfiguration
{
    WKWebViewConfiguration* configuration = [WKWebViewConfiguration new];
//    configuration.userContentController = [WKUserContentController new];
    configuration.processPool = [WKProcessPool new];
    configuration.preferences.javaScriptCanOpenWindowsAutomatically = false;
    configuration.preferences.javaScriptEnabled = true;
    
//    configuration.allowsInlineMediaPlayback = YES;
    // Enables Zoom in website by ignoring their javascript based viewport Scale limits.
//    configuration.ignoresViewportScaleLimits = YES;
    
//    WKDataDetectorTypes types = WKDataDetectorTypePhoneNumber | WKDataDetectorTypeAddress | WKDataDetectorTypeCalendarEvent
//    | WKDataDetectorTypeTrackingNumber | WKDataDetectorTypeFlightNumber | WKDataDetectorTypeLookupSuggestion;
//    [configuration setDataDetectorTypes:types];

//    if(![configuration urlSchemeHandlerForURLScheme:[InternalURL scheme]]) {
//        InternalSchemeHandler *handler = [InternalSchemeHandler new];
//        [configuration setURLSchemeHandler:handler forURLScheme:[InternalURL scheme]];
//    }

    return configuration;
}

- (Tab *)selectedTab
{
    if(self.selectIndex >= self.allTabs.count || self.selectIndex < 0) return nil;
    
    return self.allTabs[self.selectIndex];
}

- (void)addDelegate:(id<TabManagerDelegate>)delegate
{
    [self.delegates addPointer:(__bridge  void*)delegate];
}

- (void)removeDelegate:(id<TabManagerDelegate>)delegate
{
    int count = 0;
    for(id obj in self.delegates) {
        if(obj == delegate) {
            [self.delegates removePointerAtIndex:count];
            break;
        }
        
        count++;
    }
}

- (void)notifyDelegateWithSelectedTab:(Tab*)selected previous:(Tab*)previous
{
    for(id item in self.delegates) {
        if([item respondsToSelector:@selector(tabManager:didSelectedTabChange:previous:)]) {
            [item tabManager:self didSelectedTabChange:selected previous:previous];
        }
    }
}

- (void)notifyDelegateDidAddTab:(Tab*)tab
{
    for(id item in self.delegates) {
        if([item respondsToSelector:@selector(tabManager:didAddTab:)]) {
            [item tabManager:self didAddTab:tab];
        }
    }
}

- (NSPointerArray *)delegates {
    if(!_delegates) {
        _delegates = [NSPointerArray weakObjectsPointerArray];
    }
    return _delegates;
}

- (NSMutableArray *)allTabs
{
    if(!_allTabs) {
        _allTabs = [NSMutableArray array];
    }
    
    return _allTabs;
}

@end
