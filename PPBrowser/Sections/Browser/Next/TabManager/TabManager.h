//
//  TabManager.h
//  PandaBrowser
//
//  Created by qingbin on 2022/3/2.
//

#import <Foundation/Foundation.h>
#import <WebKit/WebKit.h>
#import <UIKit/UIKit.h>

#import "Tab.h"
#import "TabModel.h"

@class BrowserViewController;

@class TabManager;

@protocol TabManagerDelegate <NSObject>

@optional
- (void)tabManager:(TabManager*)tabManager didSelectedTabChange:(Tab*)selected previous:(Tab*)previous;
- (void)tabManager:(TabManager*)tabManager didAddTab:(Tab*)tab;

@end

@interface TabManager : NSObject

//allTabs不代表数据库中所有的tab,而是表示当前打开的tab
@property (nonatomic, strong) NSMutableArray *allTabs;
@property (nonatomic, assign) int selectIndex;
@property (nonatomic, strong) Tab* selectedTab;

@property (nonatomic, weak) BrowserViewController* browser;
@property (nonatomic, weak) id<TabManagerDelegate> tabManagerDelegate;

- (void)addDelegate:(id<TabManagerDelegate>)delegate;
- (void)removeDelegate:(id<TabManagerDelegate>)delegate;

- (Tab*)generateTabWithModel:(TabModel*)tabModel;

- (Tab*)addTab:(Tab*)tab;

- (void)selectTab:(Tab*)tab;

- (void)saveTab:(Tab*)tab;

- (Tab*)addTabAndSelect:(TabModel*)tabModel;

- (Tab*)restoreTab:(TabModel*)tabModel;

//后台打开(不选中)
- (Tab *)restoreTabWithoutSelect:(TabModel *)tabModel;

- (void)updateTabModelWhenFinishNavigation:(Tab*)tab;

- (Tab*)tabForWebView:(WKWebView*)webView;

- (Tab*)tabForTabModel:(TabModel*)tabModel;

- (BOOL)removeTabWithModel:(TabModel*)item allTabModels:(NSMutableArray*)allTabModels;

- (Tab*)generateTabWithModel:(TabModel *)tabModel configuration:(WKWebViewConfiguration*)configuration;

// 标签页中进行删除全部标签页操作
- (void)removeAllTabsFromTabTray;

// 关闭当前标签
- (void)closeCurrentTab:(NSArray *)allTabModelArray;

// 关闭其他标签
- (void)closeOtherTabs:(NSArray *)allTabModelArray;

//根据urlHistorySnapshot更新TabModel
- (TabModel *)updateRestoreTabModel:(TabModel *)tabModel;

//v2.7.4，由于触发了无痕模式的变更，因此关闭所有webview，再重新加载已选的webview
- (void)closeAllTabsAndReloadSelectedTab;

@end


