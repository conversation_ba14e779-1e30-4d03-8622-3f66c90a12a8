//
//  WebViewNavDelegate.h
//  PandaBrowser
//
//  Created by qingbin on 2022/3/9.
//

#import <Foundation/Foundation.h>
#import <WebKit/WebKit.h>
#import "PandaWebView.h"

@class TabManager;
@class Tab;

@interface WebViewNavDelegate : NSObject<WKNavigationDelegate>

+ (instancetype)shareInstance;

//这种方式只能添加单例的监听对象,用来监听所有WebView的事件,如tabManager/BrowserViewController
- (void)insert:(id<WKNavigationDelegate>)delegate;

//这种方式只监听对应webView的事件(如阅读模式)
- (void)addObserver:(id<WKNavigationDelegate>)delegate forKey:(PandaWebView*)webView;
- (void)removeObserverForKey:(PandaWebView*)webView;

@property(nonatomic,weak) TabManager *tabManager;

@end


