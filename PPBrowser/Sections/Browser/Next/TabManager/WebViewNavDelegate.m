//
//  WebViewNavDelegate.m
//  PandaBrowser
//
//  Created by qingbin on 2022/3/9.
//

#import "WebViewNavDelegate.h"

#import "Tab.h"
#import "TabManager.h"

@interface WebViewNavDelegate ()

@property (nonatomic, strong) NSPointerArray* delegates;
@property (nonatomic, strong) NSMapTable* keyValueDelegates;

@end

@implementation WebViewNavDelegate

+ (instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    static WebViewNavDelegate* obj;
    dispatch_once(&onceToken, ^{
        obj = [WebViewNavDelegate new];
    });
    
    return obj;
}

// 1 在发送请求之前，决定是否跳转
- (void)webView:(WKWebView *)webView decidePolicyForNavigationAction:(WKNavigationAction *)navigationAction preferences:(WKWebpagePreferences *)preferences decisionHandler:(void (^)(WKNavigationActionPolicy, WKWebpagePreferences * _Nonnull))decisionHandler
{
    //https://www.jianshu.com/p/29c610b8c3f2
    //https://github.com/WebKit/WebKit/blob/main/Source/WebKit/UIProcess/API/Cocoa/WKNavigationDelegatePrivate.h#L62
    //_WKNavigationActionPolicyAllowWithoutTryingAppLink
    //禁止Universal Links跳转, 如果不做处理, 那么就会提示跳转到其它APP, 没办法在浏览器中查看相关内容
    __block WKNavigationActionPolicy res = WKNavigationActionPolicyAllow + 2;

    for(id<WKNavigationDelegate> obj in self.delegates) {
        if(![obj respondsToSelector:@selector(webView:decidePolicyForNavigationAction:preferences:decisionHandler:)]) continue;
        [obj webView:webView decidePolicyForNavigationAction:navigationAction preferences:preferences decisionHandler:^(WKNavigationActionPolicy policy, WKWebpagePreferences * _Nonnull preferences) {
            res = policy;
        }];
    }

#warning -- 腾讯视频跳转走的是Universal Links
//    if(res != WKNavigationActionPolicyCancel) {
//        res = allowDecision;
//    }

    decisionHandler(res, preferences);
}

// 2 页面开始加载时调用
- (void)webView:(WKWebView *)webView didStartProvisionalNavigation:(null_unspecified WKNavigation *)navigation
{
    for(id<WKNavigationDelegate> obj in self.delegates) {
        if(![obj respondsToSelector:@selector(webView:didStartProvisionalNavigation:)]) continue;

        [obj webView:webView didStartProvisionalNavigation:navigation];
    }
}

// 3 在收到响应后，决定是否跳转
- (void)webView:(WKWebView *)webView decidePolicyForNavigationResponse:(WKNavigationResponse *)navigationResponse decisionHandler:(void (^)(WKNavigationResponsePolicy))decisionHandler
{
    __block WKNavigationResponsePolicy res = WKNavigationResponsePolicyAllow;

    for(id<WKNavigationDelegate> obj in self.delegates) {

        if(![obj respondsToSelector:@selector(webView:decidePolicyForNavigationResponse:decisionHandler:)]) continue;

        [obj webView:webView decidePolicyForNavigationResponse:navigationResponse decisionHandler:^(WKNavigationResponsePolicy policy) {
            if(policy == WKNavigationResponsePolicyCancel) {
                res = policy;
            }
        }];
    }

    //更新mimeType
    if(res == WKNavigationResponsePolicyAllow) {
        Tab* tab = [self.tabManager tabForWebView:webView];
        tab.model.mimeType = navigationResponse.response.MIMEType;
    }

    decisionHandler(res);
}

// 4 当内容开始返回时调用
- (void)webView:(WKWebView *)webView didCommitNavigation:(null_unspecified WKNavigation *)navigation
{
    for(id<WKNavigationDelegate> obj in self.delegates) {
        if(![obj respondsToSelector:@selector(webView:didCommitNavigation:)]) continue;
        [obj webView:webView didCommitNavigation:navigation];
    }

    //针对特定的webView
    id<WKNavigationDelegate> obj = [self.keyValueDelegates objectForKey:webView];
    if(obj && [obj respondsToSelector:@selector(webView:didCommitNavigation:)]) {
        [obj webView:webView didCommitNavigation:navigation];
    }
}

// 5 页面加载完成之后调用
- (void)webView:(WKWebView *)webView didFinishNavigation:(null_unspecified WKNavigation *)navigation
{
    for(id<WKNavigationDelegate> obj in self.delegates) {
        if(![obj respondsToSelector:@selector(webView:didFinishNavigation:)]) continue;
        [obj webView:webView didFinishNavigation:navigation];
    }

    //针对特定的webView
    id<WKNavigationDelegate> obj = [self.keyValueDelegates objectForKey:webView];
    if(obj && [obj respondsToSelector:@selector(webView:didFinishNavigation:)]) {
        [obj webView:webView didFinishNavigation:navigation];
    }
}

// 6 页面加载失败时调用
- (void)webView:(WKWebView *)webView didFailProvisionalNavigation:(null_unspecified WKNavigation *)navigation withError:(NSError *)error
{
    for(id<WKNavigationDelegate> obj in self.delegates) {
        if(![obj respondsToSelector:@selector(webView:didFailProvisionalNavigation:withError:)]) continue;
        [obj webView:webView didFailProvisionalNavigation:navigation withError:error];
    }

    //针对特定的webView
    id<WKNavigationDelegate> obj = [self.keyValueDelegates objectForKey:webView];
    if(obj && [obj respondsToSelector:@selector(webView:didFailProvisionalNavigation:withError:)]) {
        [obj webView:webView didFailProvisionalNavigation:navigation withError:error];
    }
}

// 6 页面加载失败时调用
- (void)webView:(WKWebView *)webView didFailNavigation:(null_unspecified WKNavigation *)navigation withError:(NSError *)error
{
    for(id<WKNavigationDelegate> obj in self.delegates) {
        if(![obj respondsToSelector:@selector(webView:didFailNavigation:withError:)]) continue;
        [obj webView:webView didFailNavigation:navigation withError:error];
    }

    //针对特定的webView
    id<WKNavigationDelegate> obj = [self.keyValueDelegates objectForKey:webView];
    if(obj && [obj respondsToSelector:@selector(webView:didFailNavigation:withError:)]) {
        [obj webView:webView didFailNavigation:navigation withError:error];
    }
}

// 接收到服务器跳转请求之后调用
- (void)webView:(WKWebView *)webView didReceiveServerRedirectForProvisionalNavigation:(null_unspecified WKNavigation *)navigation
{
    for(id<WKNavigationDelegate> obj in self.delegates) {

        if(![obj respondsToSelector:@selector(webView:didReceiveServerRedirectForProvisionalNavigation:)]) continue;

        [obj webView:webView didReceiveServerRedirectForProvisionalNavigation:navigation];
    }
}

// 需要响应身份验证时调用 同样在block中需要传入用户身份凭证
- (void)webView:(WKWebView *)webView didReceiveAuthenticationChallenge:(NSURLAuthenticationChallenge *)challenge completionHandler:(void (^)(NSURLSessionAuthChallengeDisposition disposition, NSURLCredential * _Nullable credential))completionHandler
{
    NSMutableArray* authenticatingDelegates = [NSMutableArray array];
    for(id<WKNavigationDelegate> obj in self.delegates) {

        if(![obj respondsToSelector:@selector(webView:didReceiveAuthenticationChallenge:completionHandler:)]) continue;

        if([obj respondsToSelector:@selector(webView:didReceiveAuthenticationChallenge:completionHandler:)]) {
            [authenticatingDelegates addObject:obj];
        }
    }

    if(authenticatingDelegates.count <= 0) {
        completionHandler(NSURLSessionAuthChallengePerformDefaultHandling, nil);
        return;
    }

    id<WKNavigationDelegate> firstAuthenticatingDelegate = authenticatingDelegates[0];
    [firstAuthenticatingDelegate webView:webView didReceiveAuthenticationChallenge:challenge completionHandler:^(NSURLSessionAuthChallengeDisposition disposition, NSURLCredential * _Nullable credential) {
        dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
            completionHandler(disposition, credential);
        });
    }];
}

// 进程被终止时调用
- (void)webViewWebContentProcessDidTerminate:(WKWebView *)webView
{
    for(id<WKNavigationDelegate> obj in self.delegates) {
        if(![obj respondsToSelector:@selector(webViewWebContentProcessDidTerminate:)]) continue;
        [obj webViewWebContentProcessDidTerminate:webView];
    }
}

- (void)insert:(id<WKNavigationDelegate>)delegate
{
    [self.delegates addPointer:(__bridge  void*)delegate];
}

- (void)remove:(id<WKNavigationDelegate>)delegate
{
    int count = 0;
    for(id obj in self.delegates) {
        if(obj == delegate) {
            [self.delegates removePointerAtIndex:count];
            break;
        }
        
        count++;
    }
}

- (void)addObserver:(id<WKNavigationDelegate>)delegate forKey:(PandaWebView*)webView
{
    if(!webView || !delegate) return;
    [self.keyValueDelegates setObject:delegate forKey:webView];
}

- (void)removeObserverForKey:(PandaWebView*)webView
{
    if(!webView) return;
    [self.keyValueDelegates removeObjectForKey:webView];
}

- (NSPointerArray *)delegates {
    if(!_delegates) {
        _delegates = [NSPointerArray weakObjectsPointerArray];
    }
    return _delegates;
}

- (NSMapTable *)keyValueDelegates
{
    if(!_keyValueDelegates) {
        _keyValueDelegates = [NSMapTable strongToWeakObjectsMapTable];
    }
    
    return _keyValueDelegates;
}

@end
