//
//  Tab.h
//  PandaBrowser
//
//  Created by q<PERSON><PERSON> on 2022/3/2.
//

#import <Foundation/Foundation.h>
#import <WebKit/WebKit.h>

#import "TabModel.h"
#import "PandaWebView.h"
#import "NewTabPageView.h"

#import "HistoryModel.h"

@class UserCommandManager;
@class Tab;

@protocol TabDelegate <NSObject>

- (void)tab:(Tab*)tab didCreateWebView:(WKWebView*)webView;
- (void)tab:(Tab*)tab didDeleteWebView:(WKWebView*)webView;
- (void)tab:(Tab*)tab didFindInBar:(WKWebView*)webView text:(NSString*)text;

@end

@interface Tab : NSObject

- (instancetype)initWithConfiguration:(WKWebViewConfiguration*)configuration;

//v2.7.4，如果是通过window.open或者_target=blank触发的创建tab，不能设置WKWebsiteDataStore，否则会导致应用程序崩溃。例如missav。
//因此需要区分是用户手动打开的，还是由window.open或者_target=blank触发的。
- (void)createWebViewWithCanCustomWebstore:(BOOL)canCustomWebstore;

- (void)removeWebView;
- (void)loadRequest:(NSURLRequest*)request;

// The web view can go gray if it was zombified due to memory pressure.
// When this happens, the URL is nil, so try restoring the page upon selection.
- (void)reload;

- (void)goBack;
- (void)goForward;
- (BOOL)canGoBack;
- (BOOL)canGoForward;
- (void)stop;

- (void)updateUserAgentWithURL:(NSURL *)url;

//web页
@property (nonatomic, strong) PandaWebView *webView;
//
@property (nonatomic, strong) TabModel* model;
//首页
@property (nonatomic, weak) NewTabPageView *ntpView;
//用户脚本命令管理器
@property (nonatomic, strong) UserCommandManager* commandManager;
//用来记录历史
@property (nonatomic, strong) HistoryModel *currentHistoryModel;
//用来标记是否是长按设置图标
@property (nonatomic, copy) void (^longPressSetIconAction)(NSString* iconUrl);

@property(nonatomic,weak) id<TabDelegate> tabDelegate;

- (NSArray<NSString*>*)historyList;
- (NSArray<NSString*>*)forwardList;

//是否存在首页的URL
//返回首页的下标, 如果不存在，那么返回-1
- (int)hasHomeUrl;

@end

