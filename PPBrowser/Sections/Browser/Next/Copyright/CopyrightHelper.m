//
//  CopyrightHelper.m
//  PPBrowser
//
//  Created by qingbin on 2022/6/16.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "CopyrightHelper.h"
#import "NSURL+Extension.h"

@interface CopyrightHelper ()
/// 广告过滤白名单
@property (nonatomic, strong) NSMutableDictionary *adblockUrls;
//阅读模式黑名单
@property (nonatomic, strong) NSMutableDictionary *readerUrls;
//视频模式黑名单
@property (nonatomic, strong) NSMutableDictionary *playUrls;
//
@property (nonatomic, strong) NSMutableDictionary *popupUrls;
// 油猴脚本黑名单
@property (nonatomic, strong) NSMutableDictionary *userscriptUrls;

@end

@implementation CopyrightHelper

+ (instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    static CopyrightHelper* obj;
    dispatch_once(&onceToken, ^{
        obj = [CopyrightHelper new];
    });
    
    return obj;
}

- (instancetype)init
{
    self = [super init];
    if(self) {
        self.readerUrls = [NSMutableDictionary dictionary];
        self.playUrls = [NSMutableDictionary dictionary];
        self.adblockUrls = [NSMutableDictionary dictionary];
        self.popupUrls = [NSMutableDictionary dictionary];
        self.userscriptUrls = [NSMutableDictionary dictionary];
        
        [self addReaderCopyrights];
        [self addPlayerCopyrights];
        [self addAdBlockCopyrights];
        [self addPopupCopyrights];
        [self addUserScriptBlackList];
    }
    
    return self;
}

#pragma mark - 添加油猴脚本黑名单

- (void)addUserScriptBlackList
{
    self.userscriptUrls[@"youku.com"] = @"youku.com";
}

#pragma mark -- 添加阅读模式版权控制
- (void)addReaderCopyrights
{
    /*
     参考:
     "baidu.com", "qidian.com", "jjwxc.net", "faloo.com", "zongheng.com", "hongxiu.com", "xs8.cn", "xxsy.net", "tadu.com", "17k.com", "hongshu.com", "readnovel.com", "zhulang.com", "qdmm.com", "shuhai.com", "fbook.net", "motie.com", "heiyan.com"
     */
    //greasyfork
    self.readerUrls[@"greasyfork.org"] = @"greasyfork.org";
    
    //知乎
    self.readerUrls[@"zhihu.com"] = @"zhihu.com";
    
    //百度
    self.readerUrls[@"baidu.com"] = @"baidu.com";
    
    //起点中文网
    self.readerUrls[@"qidian.com"] = @"qidian.com";
    
    //起点女生网
    self.readerUrls[@"qdmm.com"] = @"qdmm.com";
    
    //天下书盟
    self.readerUrls[@"fbook.net"] = @"fbook.net";
    
    //香江文学
    self.readerUrls[@"jjwxc.net"] = @"jjwxc.net";
    
    //书海
    self.readerUrls[@"shuhai.com"] = @"shuhai.com";
    
    //纵横中文网
    self.readerUrls[@"zongheng.com"] = @"zongheng.com";
    
    //塔读文学
    self.readerUrls[@"tadu.com"] = @"tadu.com";
    
    //17k
    self.readerUrls[@"17k.com"] = @"17k.com";
    
    //逐浪中文网
    self.readerUrls[@"zhulang.com"] = @"zhulang.com";
}

#pragma mark -- 添加广告版权控制
- (void)addAdBlockCopyrights
{
    //以下网址不能屏蔽广告
//    self.adblockUrls[@"baidu.com"]    = @"baidu.com"; //因为这里也不能使用标记模式，这个不是我们所希望的
    self.adblockUrls[@"youku.com"]    = @"youku.com";
    self.adblockUrls[@"tudou.com"]    = @"tudou.com";
    self.adblockUrls[@"iqiyi.com"]    = @"iqiyi.com";
    self.adblockUrls[@"v.qq.com"]     = @"v.qq.com";
//    self.adblockUrls[@"bilibili.com"] = @"bilibili.com";
    self.adblockUrls[@"mgtv.com"]     = @"mgtv.com";
    self.adblockUrls[@"sogou.com"]    = @"sogou.com";
    self.adblockUrls[@"sogoucdn.com"] = @"sogoucdn.com";
    self.adblockUrls[@"m.sogou.com"]  = @"m.sogou.com";
    self.adblockUrls[@"wap.sogou.com"] = @"wap.sogou.com";
    self.adblockUrls[@"m.so.com"]      = @"m.so.com";
    self.adblockUrls[@"m.toutiao.com"] = @"m.toutiao.com";
    self.adblockUrls[@"weibo.cn"]      = @"weibo.cn";
    
    //小说网站
    self.adblockUrls[@"qidian.com"] = @"qidian.com";
    self.adblockUrls[@"qdmm.com"] = @"qdmm.com";
    self.adblockUrls[@"fbook.net"] = @"fbook.net";
    self.adblockUrls[@"jjwxc.net"] = @"jjwxc.net";
    self.adblockUrls[@"zongheng.com"] = @"zongheng.com";
    self.adblockUrls[@"tadu.com"] = @"tadu.com";
    self.adblockUrls[@"17k.com"] = @"17k.com";
    self.adblockUrls[@"zhulang.com"] = @"zhulang.com";
    
    //百度贴吧跳转失败，是因为广告屏蔽+UA导致的
    self.adblockUrls[@"bdimg.com"] = @"bdimg.com";
    
    //i.y.qq.com -- qq音乐
    self.adblockUrls[@"qq.com"] = @"qq.com";
    //网易云音乐
    self.adblockUrls[@"music.163.com"] = @"music.163.com";
}

#pragma mark -- 添加视频模式版权控制
- (void)addPlayerCopyrights
{
    //以下网址不显示视频模式
//    self.playUrls[@"baidu.com"]    = @"baidu.com";
    self.playUrls[@"youku.com"]    = @"youku.com";
    self.playUrls[@"tudou.com"]    = @"tudou.com";
    self.playUrls[@"iqiyi.com"]    = @"iqiyi.com";
    self.playUrls[@"v.qq.com"]     = @"v.qq.com";
//    self.playUrls[@"bilibili.com"] = @"bilibili.com";
    self.playUrls[@"mgtv.com"]     = @"mgtv.com";
    self.playUrls[@"sogou.com"]   = @"sogou.com";
    
    //2.6.2版本更新
//    self.playUrls[@"youtube.com"]   = @"youtube.com";
//    self.playUrls[@"twitter.com"]   = @"twitter.com";
//    self.playUrls[@"facebook.com"]  = @"facebook.com";
//    self.playUrls[@"instagram.com"] = @"instagram.com";
    
    self.playUrls[@"qq.com"] = @"qq.com";
    self.playUrls[@"music.163.com"] = @"music.163.com";
}

#pragma mark -- 屏蔽弹窗
- (void)addPopupCopyrights
{
//txvideo://v.qq.com/HomeActivity?from=10124
//iqiyi://mobile/register_business/qyclient?pluginParams=
//imgotv://home?type=23&content=imgotv
//com.baidu.tieba://unidispatch/frs?query=%E7%
    
    self.popupUrls[@"txvideo://"] = @"txvideo://";
//    self.popupUrls[@"iqiyi://"] = @"iqiyi://";
//    self.popupUrls[@"imgotv://"] = @"imgotv://";
//    self.popupUrls[@"com.baidu.tieba://"] = @"com.baidu.tieba://";
}

- (BOOL)validPlayUrl:(NSURL *)URL
{
    NSString* host = [URL normalizedHost];
    if(host.length == 0) return YES;
    
    __block BOOL isValid = YES;
    NSString* url = URL.absoluteString;
    [self.playUrls enumerateKeysAndObjectsUsingBlock:^(NSString*  _Nonnull key, NSString*  _Nonnull obj, BOOL * _Nonnull stop) {
        if([url rangeOfString:key].location != NSNotFound) {
            isValid = NO;
            *stop = YES;
        }
    }];
    
    return isValid;
}

// 判断是否是百度, v2.6.7，百度搜索分成不能屏蔽广告
- (BOOL)isBaidu:(NSURL *)URL
{
    if (!URL) return NO;
    NSString* host = [URL normalizedHost];
    if(host.length == 0) return NO;
    
    if ([host.lowercaseString rangeOfString:@"baidu.com"].location != NSNotFound) {
        return YES;
    }
    
    return NO;
}

// 广告过滤规则
- (BOOL)validContentRuleText:(NSString *)ruleText
{
    if(ruleText.length == 0) return NO;
    //是排除规则,那么不检测
    if([ruleText rangeOfString:@"@@"].location != NSNotFound) return YES;
    
    __block BOOL isValid = YES;
    [self.adblockUrls enumerateKeysAndObjectsUsingBlock:^(NSString*  _Nonnull key, NSString*  _Nonnull obj, BOOL * _Nonnull stop) {
        if([ruleText rangeOfString:key].location != NSNotFound) {
            isValid = NO;
            *stop = YES;
        }
    }];
    
    return isValid;
}

//判断是否能缓存
- (BOOL)validTaskUrl:(NSURL *)URL
{
    NSString* host = [URL normalizedHost];
    if(host.length == 0) return YES;
    
    NSMutableDictionary* playUrls = [NSMutableDictionary dictionary];
    [playUrls addEntriesFromDictionary:self.playUrls];
    //y只能播放，不能缓存
    playUrls[@"youtube.com"]  = @"youtube.com";
    
    __block BOOL isValid = YES;
    NSString* url = URL.absoluteString;
    [playUrls enumerateKeysAndObjectsUsingBlock:^(NSString*  _Nonnull key, NSString*  _Nonnull obj, BOOL * _Nonnull stop) {
        if([url rangeOfString:key].location != NSNotFound) {
            isValid = NO;
            *stop = YES;
        }
    }];
    
    return isValid;
}

- (BOOL)validAdBlockUrl:(NSURL *)URL
{
    NSString* host = [URL normalizedHost];
    if(host.length == 0) return YES;
    
    __block BOOL isValid = YES;
    NSString* url = URL.absoluteString;
    [self.adblockUrls enumerateKeysAndObjectsUsingBlock:^(NSString*  _Nonnull key, NSString*  _Nonnull obj, BOOL * _Nonnull stop) {
        if([url rangeOfString:key].location != NSNotFound) {
            isValid = NO;
            *stop = YES;
        }
    }];
    
    return isValid;
}

// 屏蔽弹窗
- (BOOL)validPopupUrl:(NSURL *)URL
{
    NSString* host = URL.absoluteString;
    if(host.length == 0) return YES;
    
    __block BOOL isValid = YES;
    NSString* url = URL.absoluteString;
    [self.popupUrls enumerateKeysAndObjectsUsingBlock:^(NSString*  _Nonnull key, NSString*  _Nonnull obj, BOOL * _Nonnull stop) {
        if([url rangeOfString:key].location != NSNotFound) {
            isValid = NO;
            *stop = YES;
        }
    }];
    
    return isValid;
}

#pragma mark - 油猴脚本黑名单(禁止运行，例如优酷)
- (BOOL)isInUserScriptBlackList:(NSURL *)URL
{
    NSString* host = [URL normalizedHost];
    if(host.length == 0) return NO;
    
    __block BOOL isBlackList = NO;
    NSString* url = URL.absoluteString;
    [self.userscriptUrls enumerateKeysAndObjectsUsingBlock:^(NSString*  _Nonnull key, NSString*  _Nonnull obj, BOOL * _Nonnull stop) {
        if([url rangeOfString:key].location != NSNotFound) {
            isBlackList = YES;
            *stop = YES;
        }
    }];
    
    return isBlackList;
}

@end
