//
//  CopyrightHelper.h
//  PPBrowser
//
//  Created by qing<PERSON> on 2022/6/16.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>

/// 版权保护
@interface CopyrightHelper : NSObject

+ (instancetype)shareInstance;

- (BOOL)validPlayUrl:(NSURL *)URL;

// 广告过滤规则
- (BOOL)validContentRuleText:(NSString *)ruleText;
- (BOOL)validAdBlockUrl:(NSURL *)URL;

//判断是否能缓存
- (BOOL)validTaskUrl:(NSURL *)URL;

// 屏蔽弹窗
- (BOOL)validPopupUrl:(NSURL *)URL;

// 判断是否是百度, v2.6.7，百度搜索分成不能屏蔽广告
- (BOOL)isBaidu:(NSURL *)URL;

// 油猴脚本黑名单(禁止运行，例如优酷)
- (BOOL)isInUserScriptBlackList:(NSURL *)URL;

@end

