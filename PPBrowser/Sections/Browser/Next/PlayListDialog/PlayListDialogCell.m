//
//  PlayListDialogCell.m
//  PPBrowser
//
//  Created by qingbin on 2024/10/27.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import "PlayListDialogCell.h"

#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "UIView+FrameHelper.h"
#import "NSObject+Helper.h"
#import "BrowserUtils.h"
#import "ThemeProtocol.h"

#import "PPNotifications.h"

@interface PlayListDialogCell ()

@property (nonatomic, strong) UIView* backView;

@property (nonatomic, strong) UILabel* titleLabel;

@property (nonatomic, strong) UIButton* playButton;

@property (nonatomic, strong) UIView* line;

@property (nonatomic, assign) BOOL bShowLine;

@property (nonatomic, strong) PlayModel* model;

@end

@implementation PlayListDialogCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        self.backgroundColor = UIColor.clearColor;
        
        [self addSubviews];
        [self defineLayout];
        [self setupObservers];
        
        [self applyTheme];
    }
    
    return self;
}

- (void)updateWithModel:(PlayModel *)model
{
    self.model = model;
    
//    NSURLComponents *components = [NSURLComponents componentsWithURL:[NSURL URLWithString:model.src] resolvingAgainstBaseURL:NO];
//    components.query = nil;
    
    self.titleLabel.text = [model.src lastPathComponent];
    
    [self updateCornerRadius];
    [self applyTheme];
}

- (void)updateCornerRadius
{
    // 圆角
    self.line.hidden = NO;

    if(self.model.isFirstInSection && self.model.isLastInSection) {
        //只有一个
        self.backView.layer.masksToBounds = YES;
        self.backView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner | kCALayerMinXMaxYCorner | kCALayerMaxXMaxYCorner;
        self.line.hidden = YES;
    } else {
        if(self.model.isFirstInSection) {
            //添加上圆角
            self.backView.layer.masksToBounds = YES;
            self.backView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
        } else if(self.model.isLastInSection) {
            //添加下圆角
            self.backView.layer.masksToBounds = YES;
            self.backView.layer.maskedCorners = kCALayerMinXMaxYCorner | kCALayerMaxXMaxYCorner;
            self.line.hidden = YES;
        } else {
            self.backView.layer.masksToBounds = NO;
            self.backView.layer.cornerRadius = 0;
        }
    }
}

- (void)dealloc
{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.backView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        self.titleLabel.textColor = [UIColor colorWithHexString:kDarkThemeColorText];
        self.line.backgroundColor = [UIColor colorWithHexString:kDarkThemeColorLine];
    } else {
        self.backView.backgroundColor = UIColor.whiteColor;
        self.titleLabel.textColor = [UIColor colorWithHexString:@"#333333"];
        self.line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
}

+ (float)height
{
    if([BrowserUtils isiPad]) {
        return 88;
    } else {
        return 60;
    }
}

- (void)setupObservers
{
    @weakify(self)
    [[self.playButton rac_signalForControlEvents:UIControlEventTouchUpInside]
    subscribeNext:^(id x) {
        @strongify(self)
        if(self.didTaskAction) {
            self.didTaskAction(self.model);
        }
    }];
}

- (void)addSubviews
{
    [self.contentView addSubview:self.backView];
    [self.backView addSubview:self.titleLabel];
    [self.backView addSubview:self.playButton];
}

- (void)defineLayout
{
    [self.backView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.contentView);
    }];
    
    float offset = iPadValue(30, 15);
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(offset);
        make.centerY.equalTo(self.contentView);
        make.right.equalTo(self.playButton.mas_left).offset(-10);
    }];
    
    [self.playButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self).offset(-15);
        make.size.mas_equalTo(CGSizeMake(60, 30));
        make.centerY.equalTo(self.contentView);
    }];
    
    UIView* line = [UIView new];
    line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    [self addSubview:line];
    [line mas_makeConstraints:^(MASConstraintMaker *make) {
       make.left.mas_offset(0);
       make.right.mas_offset(-0);
       make.height.mas_equalTo(0.5);
       make.bottom.mas_offset(0);
    }];
    self.line = line;
}

- (UILabel *)titleLabel
{
    if(!_titleLabel) {
        float font = iPadValue(20, 16);
        _titleLabel = [UIView createLabelWithTitle:nil
                                        textColor:[UIColor colorWithHexString:@"#333333"]
                                          bgColor:UIColor.clearColor
                                         fontSize:font
                                    textAlignment:NSTextAlignmentLeft
                                            bBold:NO];
        _titleLabel.lineBreakMode = NSLineBreakByTruncatingMiddle;
    }
    
    return _titleLabel;
}

- (UIButton *)playButton
{
    if(!_playButton) {
        _playButton = [UIButton new];
        [_playButton setTitle:NSLocalizedString(@"tips.play", nil) forState:UIControlStateNormal];
        _playButton.titleLabel.font = [UIFont systemFontOfSize:16];
        [_playButton setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
        _playButton.backgroundColor = [UIColor colorWithHexString:@"#2D7AFE"];
        _playButton.layer.cornerRadius = 15;
        _playButton.layer.masksToBounds = YES;
    }
    
    return _playButton;
}

- (UIView *)backView
{
    if(!_backView) {
        _backView = [UIView new];
        
        _backView.layer.cornerRadius = 10;
        _backView.layer.masksToBounds = YES;
    }
    
    return _backView;
}

@end
