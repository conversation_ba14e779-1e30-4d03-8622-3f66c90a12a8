//
//  PlayListDialogView.m
//  PPBrowser
//
//  Created by qingbin on 2024/10/27.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import "PlayListDialogView.h"

#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "UIView+FrameHelper.h"
#import "NSObject+Helper.h"

#import "ThemeProtocol.h"
#import "PreferenceManager.h"
#import "BrowserUtils.h"

#import "PPEnums.h"
#import "PPNotifications.h"

#import "PlayListDialogCell.h"

@interface PlayListDialogView ()<UITableViewDelegate,UITableViewDataSource>

@property (nonatomic, strong) UIView* dialogView;

@property (nonatomic, strong) UILabel *titleLabel;

@property (nonatomic, strong) UIButton *closeButton;

@property (nonatomic, strong) UIView *dialogBackgroundView;

@property (nonatomic, strong) UITableView* tableView;

@property (nonatomic, strong) NSMutableArray* model;

@end

@implementation PlayListDialogView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if(self) {
        [self addSubviews];
        [self defineLayout];
        [self setupObservers];
        
        [self applyTheme];
    }
    
    return self;
}

- (void)showWithModel:(NSArray *)model
{
    UIWindow* window = [NSObject normalWindow];
    [window endEditing:YES];
    [window addSubview:self];
   
    [self mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_offset(0);
    }];
    
    self.model = [model mutableCopy];
    for(PlayModel* obj in self.model) {
        obj.isFirstInSection = NO;
        obj.isLastInSection = NO;
    }
    
    PlayModel* first = self.model.firstObject;
    first.isFirstInSection = YES;
    
    PlayModel* last = self.model.lastObject;
    last.isLastInSection = YES;
    
    [self.tableView reloadData];
    
    self.backgroundColor = [UIColor colorWithRed:0 green:0 blue:0 alpha:0];
    self.dialogView.layer.opacity = 0.5f;
    
    [UIView animateWithDuration:0.25
                          delay:0.0
                        options:UIViewAnimationOptionCurveEaseInOut
                     animations:^{
            self.backgroundColor = [UIColor colorWithRed:0x00 / 255.0f green:0x00 / 255.0f blue:0x00 / 255.0f alpha:0.4f];
            self.dialogView.layer.opacity = 1.0f;
        }
                     completion:^(BOOL finished) {
        }
    ];
}

- (void)close
{
    self.dialogView.layer.opacity = 1.0f;
    
    [UIView animateWithDuration:0.25f delay:0.0 options:UIViewAnimationOptionCurveEaseInOut
        animations:^{
            self.backgroundColor = [UIColor colorWithRed:0.0f green:0.0f blue:0.0f alpha:0.0f];
            self.dialogView.layer.opacity = 0.0f;
        }
        completion:^(BOOL finished) {
            [self removeFromSuperview];
        }
    ];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.titleLabel.textColor = [UIColor colorWithHexString:@"#ffffff"];
        [self.closeButton setImage:[UIImage imageNamed:@"sheet_close_dark_button"] forState:UIControlStateNormal];
        self.dialogBackgroundView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.dialogView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
    } else {
        self.titleLabel.textColor = [UIColor colorWithHexString:@"#222222"];
        [self.closeButton setImage:[UIImage imageNamed:@"sheet_close_button"] forState:UIControlStateNormal];
        self.dialogBackgroundView.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.dialogView.backgroundColor = UIColor.whiteColor;
    }
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    //只有当前的controller会触发一次, 其它已存在的controller走通知
    if(needReload) {
        //修改暗黑模式
        [self applyTheme];
        
        [self.tableView reloadData];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}

- (void)setupObservers
{
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(darkThemeDidChangeNotification:)
                                                 name:kDarkThemeDidChangeNotification
                                               object:nil];
}

- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event
{
    UITouch *touch = [touches anyObject];
    CGPoint touchPoint = [touch locationInView:self];
    
    if (CGRectContainsPoint(self.dialogView.frame, touchPoint)) {
        // 点击在子视图范围内，不响应
        return;
    } else {
        [self close];
    }

    // 点击不在子视图范围内，执行点击事件
    [super touchesBegan:touches withEvent:event];
}

#pragma mark -- layout
- (void)addSubviews
{
    [self addSubview:self.dialogView];
    [self.dialogView addSubview:self.titleLabel];
    [self.dialogView addSubview:self.closeButton];
    [self.dialogView addSubview:self.dialogBackgroundView];
    [self.dialogBackgroundView addSubview:self.tableView];
}

- (void)defineLayout
{
    float offset = iPadValue(30, 10);
    [self.dialogView mas_makeConstraints:^(MASConstraintMaker *make) {
        if([BrowserUtils isiPhone]) {
            //iPhone
            make.center.mas_offset(0);
            make.height.mas_equalTo(350);
            make.left.mas_offset(offset);
            make.right.mas_offset(-offset);
        } else {
            float maxWidth = MIN(kScreenWidth-2*offset, 768);
            make.center.mas_offset(0);
            make.height.mas_equalTo(350);
            make.width.mas_equalTo(maxWidth);
        }
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_offset(15);
        make.centerX.mas_offset(0);
    }];
    
    [self.closeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_offset(10);
        make.right.mas_offset(-10);
        make.size.mas_equalTo(30);
    }];
    
    [self.dialogBackgroundView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_offset(50);
        make.left.mas_offset(10);
        make.right.mas_offset(-10);
        make.bottom.mas_offset(-10);
    }];
    
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_offset(10);
        make.left.mas_offset(10);
        make.right.mas_offset(-10);
        make.bottom.mas_offset(-10);
    }];
}

#pragma mark - UITableViewDataSource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return self.model.count;
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    PlayModel* model = self.model[indexPath.row];

    PlayListDialogCell* cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass(PlayListDialogCell.class)];
    [cell updateWithModel:model];
    
    @weakify(self)
    [cell setDidTaskAction:^(PlayModel *model) {
        @strongify(self)
        //点击播放
        BOOL shouldClose = YES;
        if(self.enterPlayViewAction) {
            shouldClose = self.enterPlayViewAction(model);
        }
        
        if(shouldClose) {
            [self close];
        }
    }];

    return cell;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    PlayModel* model = self.model[indexPath.row];
    
    //点击播放
    BOOL shouldClose = YES;
    if(self.enterPlayViewAction) {
        shouldClose = self.enterPlayViewAction(model);
    }
    
    if(shouldClose) {
        [self close];
    }
}

#pragma mark -- Getters

- (UILabel *)titleLabel
{
    if(!_titleLabel) {
        _titleLabel = [UIView createLabelWithTitle:NSLocalizedString(@"common.video.list.title", nil)
                                         textColor:[UIColor colorWithHexString:@"#222222"]
                                           bgColor:UIColor.clearColor
                                          fontSize:16
                                     textAlignment:NSTextAlignmentCenter
                                             bBold:NO];
    }
    
    return _titleLabel;
}

- (UIButton *)closeButton
{
    if(!_closeButton) {
        _closeButton = [UIButton new];
        [_closeButton setImage:[UIImage imageNamed:@"sheet_close_button"] forState:UIControlStateNormal];
        
        @weakify(self)
        [[_closeButton rac_signalForControlEvents:UIControlEventTouchUpInside]
            subscribeNext:^(id x) {
            @strongify(self)
            [self close];
        }];
    }
    
    return _closeButton;
}

- (UIView *)dialogView
{
    if(!_dialogView) {
        _dialogView = [UIView new];
        _dialogView.layer.cornerRadius = 10;
    
        // 设置阴影颜色
        _dialogView.layer.shadowColor = [UIColor colorWithRed:0 green:0 blue:0 alpha:0.4].CGColor;
        // 设置阴影的偏移量
        _dialogView.layer.shadowOffset = CGSizeMake(0, 5);
        // 设置阴影的不透明度
        _dialogView.layer.shadowOpacity = 1.0;
        // 设置阴影的半径
        _dialogView.layer.shadowRadius = 10;
    }
    
    return _dialogView;
}

- (UIView *)dialogBackgroundView
{
    if(!_dialogBackgroundView) {
        _dialogBackgroundView = [UIView new];
        _dialogBackgroundView.layer.cornerRadius = 10;
    }
    
    return _dialogBackgroundView;
}

- (UITableView *)tableView
{
    if(!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
        
        _tableView.separatorStyle  = UITableViewCellSeparatorStyleNone;
        _tableView.showsHorizontalScrollIndicator = NO;
        _tableView.showsVerticalScrollIndicator   = YES;
        _tableView.delegate   = self;
        _tableView.dataSource = self;
        _tableView.rowHeight = iPadValue(88, 60);
        _tableView.backgroundColor = UIColor.clearColor;
        
        [_tableView registerClass:[PlayListDialogCell class] forCellReuseIdentifier:NSStringFromClass([PlayListDialogCell class])];
        
        float height = iPadValue(30, 10);
        UIWindow* window = [NSObject normalWindow];
        _tableView.tableFooterView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, CGFLOAT_MIN, height+window.safeAreaInsets.bottom)];
        _tableView.tableHeaderView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, CGFLOAT_MIN, height)];
    }
    
    return _tableView;
}

- (NSMutableArray *)model
{
    if(!_model) {
        _model = [NSMutableArray array];
    }
    
    return _model;
}


@end
