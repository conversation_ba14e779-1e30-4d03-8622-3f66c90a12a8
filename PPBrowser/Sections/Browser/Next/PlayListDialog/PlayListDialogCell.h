//
//  PlayListDialogCell.h
//  PPBrowser
//
//  Created by qing<PERSON> on 2024/10/27.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import <UIKit/UIKit.h>

#import "PPEnums.h"
#import "PlayModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface PlayListDialogCell : UITableViewCell

- (void)updateWithModel:(PlayModel *)model;

@property (nonatomic, copy) void (^didTaskAction)(PlayModel *model);

@end

NS_ASSUME_NONNULL_END
