//
//  MenuHelper.h
//  PPBrowser
//
//  Created by qingbin on 2022/4/26.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>

@protocol MenuHelperDelegate <NSObject>

- (void)findInPageItem;

@end

NS_ASSUME_NONNULL_BEGIN

// https://www.jianshu.com/p/25db4c901adf?utm_campaign=maleskine&utm_content=note&utm_medium=seo_notes&utm_source=recommendation
// https://blog.csdn.net/qq_18683985/article/details/82960100
// 配合PandaWebView canPerformAction

// 修改Info中Localization native development region为China
// 如果不修改那么Menu则为英语
@interface MenuHelper : NSObject

+ (instancetype)shareInstance;

- (void)setItems;

@property (nonatomic, weak) id<MenuHelperDelegate> delegate;

@end

NS_ASSUME_NONNULL_END
