//
//  MenuHelper.m
//  PPBrowser
//
//  Created by qingbin on 2022/4/26.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "MenuHelper.h"
#import <UIKit/UIKit.h>

@implementation MenuHelper

+ (instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    static MenuHelper* obj;
    dispatch_once(&onceToken, ^{
        obj = [[MenuHelper alloc]init];
    });

    return obj;
}

- (void)setItems
{
    UIMenuItem* findInPageItem = [[UIMenuItem alloc]initWithTitle:NSLocalizedString(@"menuhelper.findInPage", nil) action:@selector(findInPageItem)];
    [[UIMenuController sharedMenuController] setMenuItems:@[findInPageItem]];
}

- (void)findInPageItem
{
    if(self.delegate && [self.delegate respondsToSelector:@selector(findInPageItem)]) {
        [self.delegate findInPageItem];
    }
}

@end
