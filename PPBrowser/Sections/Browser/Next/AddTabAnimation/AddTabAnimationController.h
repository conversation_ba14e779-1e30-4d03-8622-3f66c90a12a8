//
//  AddTabAnimationController.h
//  PPBrowser
//
//  Created by qingbin on 2022/6/14.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "BaseViewController.h"
#import "NewTabPageView.h"

@interface AddTabAnimationController : BaseViewController<UIViewControllerTransitioningDelegate,UIViewControllerAnimatedTransitioning>

//- (instancetype)initWithContentOffset:(CGPoint)offset;

@property (nonatomic, copy) void (^didAnimationCompletedBlock)(void);

//更新标签页的个数
- (void)updateTabCount:(int)count;

@end
