//
//  AddTabAnimationController.m
//  PPBrowser
//
//  Created by qingbin on 2022/6/14.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "AddTabAnimationController.h"

#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "UIView+FrameHelper.h"
#import "NSObject+Helper.h"

#import "NewTabPageView.h"
#import "BottomToolbar.h"

#import "ThemeProtocol.h"
#import "PreferenceManager.h"

@interface AddTabAnimationController ()<ThemeProtocol>

@property (nonatomic, strong) NewTabPageView *ntpView;

@property (nonatomic, strong) BottomToolbar *bottomToolbar;

@property (nonatomic, assign) CGPoint contentOffset;

@end

@implementation AddTabAnimationController

//- (instancetype)initWithContentOffset:(CGPoint)offset
//{
//    self = [super init];
//    if(self) {
//        self.contentOffset = offset;
//    }
//    
//    return self;
//}

- (void)viewDidLoad
{
    [super viewDidLoad];
    
    [self addSubviews];
    [self defineLayout];

    if(self.ntpView == nil) {
        self.ntpView = [[NewTabPageView alloc]init];
    }
    
    ToolbarOption option = [[PreferenceManager shareInstance].items.toolBarOption intValue];
    [self.bottomToolbar configureWithOption:option];
    [self.bottomToolbar updateIsHomePage:YES];
    
    [self applyTheme];
}

- (void)viewDidLayoutSubviews
{
    [super viewDidLayoutSubviews];
    
//    [self.ntpView updateContentOffset:self.contentOffset];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [self.ntpView applyTheme];
    [self.bottomToolbar applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
    } else {
        self.view.backgroundColor = UIColor.whiteColor;
    }
}

//更新标签页的个数
- (void)updateTabCount:(int)count
{
    [self.bottomToolbar updateTabCount:count];
}

- (void)addSubviews
{
    [self.view addSubview:self.ntpView];
    [self.view addSubview:self.bottomToolbar];
}

- (void)defineLayout
{
    [self.ntpView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
    }];
    
    [self.bottomToolbar mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.bottom.equalTo(self.view);
        make.height.mas_equalTo([BottomToolbar toolbarHeight]);
    }];
}

#pragma mark -- 隐藏导航栏
- (BaseNavigationBarStyle)preferredNavigationBarStyle
{
    return BaseNavigationBarStyleNoneWithDefaultContent;
}

#pragma mark -- UIViewControllerAnimatedTransitioning
- (NSTimeInterval)transitionDuration:(nullable id <UIViewControllerContextTransitioning>)transitionContext
{
    return 0.4;
}

- (void)animateTransition:(id <UIViewControllerContextTransitioning>)transitionContext
{
    [self animatePresentation:transitionContext];
}

#pragma mark -- UIViewControllerTransitioningDelegate
- (nullable id <UIViewControllerAnimatedTransitioning>)animationControllerForPresentedController:(UIViewController *)presented presentingController:(UIViewController *)presenting sourceController:(UIViewController *)source
{
    return self;
}

- (void)animatePresentation:(id<UIViewControllerContextTransitioning>)context
{
    UINavigationController* destinationController = [context viewControllerForKey:UITransitionContextToViewControllerKey];
    CGRect finalFrame = [context finalFrameForViewController:destinationController];
    UIView* toView = [context viewForKey:UITransitionContextToViewKey];
    toView.frame = finalFrame;
    
    dispatch_async(dispatch_get_main_queue(), ^{
        UIView* backgroundView = [UIView new];
        
        //修复夜间模式下的闪屏效果
        BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
        UIColor* backgroundColor = nil;
        if(isDarkTheme) {
            backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        } else {
            backgroundColor = UIColor.whiteColor;
        }
        
        backgroundView.backgroundColor = backgroundColor;
        backgroundView.frame = finalFrame;
        backgroundView.alpha = 1;
        
        [context.containerView addSubview:toView];
        [context.containerView addSubview:backgroundView];
        
        [toView setNeedsLayout];
        [toView layoutIfNeeded];
        
        self.view.transform = CGAffineTransformMakeScale(0.2, 0.2);
        self.view.alpha = 0.1;
        
        UIViewPropertyAnimator* animator = [[UIViewPropertyAnimator alloc]initWithDuration:0.4 dampingRatio:0.825 animations:^{
            self.view.transform = CGAffineTransformIdentity;
            self.view.alpha = 1.0;
            
            backgroundView.alpha = 0;
        }];
        
        [animator addCompletion:^(UIViewAnimatingPosition finalPosition) {
            [backgroundView removeFromSuperview];
            [context completeTransition:YES];
            
            //这里有两个问题
            //1、加载完会闪动一下(白色界面闪动一下)
            //2、AddTabAnimationController移除完毕,但是BrowserViewController的ntp还没加载出来,
            //会看到上一个页面的内容,也是闪动一下
            if(self.didAnimationCompletedBlock) {
                self.didAnimationCompletedBlock();
            }
            
            [self dismissViewControllerAnimated:NO completion:^{
            }];
        }];
        
        [animator startAnimation];
    });
}

- (BottomToolbar *)bottomToolbar
{
    if(!_bottomToolbar) {
        _bottomToolbar = [[BottomToolbar alloc]init];
    }
    
    return _bottomToolbar;
}

@end
