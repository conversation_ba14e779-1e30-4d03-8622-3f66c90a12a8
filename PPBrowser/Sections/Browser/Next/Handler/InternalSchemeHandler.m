//
//  InternalSchemeHandler.m
//  PandaBrowser
//
//  Created by qingbin on 2022/3/4.
//

#import "InternalSchemeHandler.h"
#import "InternalAboutHomeHandler.h"
#import "SessionRestoreHandler.h"
#import "ErrorPageHandler.h"

@interface InternalSchemeHandler ()

@property(nonatomic,strong) NSMutableDictionary *hanlders;

@property (nonatomic, strong) NSMutableDictionary* requestMap;

@end

@implementation InternalSchemeHandler

- (NSDictionary*)getQueryWithURL:(NSURL*)url
{
    NSMutableDictionary* results = [NSMutableDictionary dictionary];
    NSArray* keyValues = [url.query componentsSeparatedByString:@"&"];
    for(NSString* pair in keyValues) {
        NSArray* kv = [pair componentsSeparatedByString:@"="];
        if(kv.count > 1) {
            //stringByRemovingPercentEncoding - 将百分比编码字符重新转会普通字符
            //在iOS中通过WebView加载Url或者请求HTTP时，若是链接中包含中文、特殊符号&％或是空格等都需要预先进行一下转码才可正常访问。
            results[kv[0]] = [kv[1] stringByRemovingPercentEncoding];
        }
    }
    
    return results;
}

- (NSURLResponse*)defaultResponse:(NSURL*)url
{
    NSURLResponse* response = [[NSURLResponse alloc]initWithURL:url
                                                       MIMEType:@"text/html"
                                          expectedContentLength:-1
                                               textEncodingName:nil];
    return response;
}

- (NSData*)defaultResponseData:(NSURL*)url
{
    NSString* html = @"<!DOCTYPE html> \
    <html> \
      <body style='background-color:#ffffff'></body> \
    </html>";
    NSData* data = [html dataUsingEncoding:kCFStringEncodingUTF8];
    
    return data;
}

- (void)webView:(WKWebView *)webView startURLSchemeTask:(id <WKURLSchemeTask>)urlSchemeTask
{
    NSURL* url = urlSchemeTask.request.URL;
    if(!url) return;
    
    /*
     internal://local/about/home
     协议 (scheme)：internal
     主机 (host)：local
     路径 (path)：/about/home
     */
    
    NSString* path = url.path;
    
//    if ([path isEqualToString:@"/reader/proxy"]) {
//        // 处理阅读模式跨域问题的HTTP请求
//        // 检查是否是阅读模式的代理请求
//        
//        // 从查询参数中获取实际URL
//        NSDictionary* queryParams = [self getQueryWithURL:url];
//        NSString* targetUrl = queryParams[@"url"];
//        
//        if (targetUrl) {
//            // 创建实际的HTTP请求
//            NSURL* actualURL = [NSURL URLWithString:targetUrl];
//            NSMutableURLRequest* request = [NSMutableURLRequest requestWithURL:actualURL];
//            //必须强引用，否则没有回调
//            if (actualURL) {
//                self.requestMap[actualURL] = request;
//            }
//            
//            // 复制原始请求的头部
//            [request setAllHTTPHeaderFields:urlSchemeTask.request.allHTTPHeaderFields];
//            
//            // 发送实际请求
//            NSURLSession* session = [NSURLSession sharedSession];
//            NSURLSessionDataTask* task = [session dataTaskWithRequest:request
//                                                   completionHandler:^(NSData* data, NSURLResponse* response, NSError* error) {
//                //回调之后，则移除强引用
//                if (actualURL) {
//                    [self.requestMap removeObjectForKey:actualURL];
//                }
//                
//                if (error) {
//                    [urlSchemeTask didFailWithError:error];
//                    return;
//                }
//                
//                // 创建响应并返回数据
//                NSHTTPURLResponse* httpResponse = (NSHTTPURLResponse*)response;
//                NSMutableDictionary* headers = [NSMutableDictionary dictionaryWithDictionary:httpResponse.allHeaderFields];
//                
//                // 创建响应并返回数据
//                NSURLResponse* proxyResponse = [[NSHTTPURLResponse alloc] initWithURL:url
//                                                                           statusCode:((NSHTTPURLResponse*)response).statusCode
//                                                                          HTTPVersion:@"HTTP/1.1"
//                                                                         headerFields:headers];
//                
//                [urlSchemeTask didReceiveResponse:proxyResponse];
//                [urlSchemeTask didReceiveData:data];
//                [urlSchemeTask didFinish];
//            }];
//            
//            [task resume];
//            return;
//        }
//    }
    
    InternalSchemeHandler* handler = self.hanlders[path];

    NSData* data = [handler defaultResponseData:url];
    NSURLResponse* response = [handler defaultResponse:url];
    
    [urlSchemeTask didReceiveResponse:response];
    [urlSchemeTask didReceiveData:data];
    [urlSchemeTask didFinish];
}

- (void)webView:(WKWebView *)webView stopURLSchemeTask:(id <WKURLSchemeTask>)urlSchemeTask
{}

- (NSMutableDictionary *)hanlders
{
    if(!_hanlders) {
        _hanlders = [NSMutableDictionary dictionary];

        _hanlders[@"/about/home"] = [InternalAboutHomeHandler new];
        _hanlders[@"/sessionrestore"] = [SessionRestoreHandler new];
        _hanlders[@"/error"] = [ErrorPageHandler new];
    }
    
    return _hanlders;
}

#pragma mark - getters

- (NSMutableDictionary *)requestMap
{
    if (!_requestMap) {
        _requestMap = [NSMutableDictionary dictionary];
    }
    
    return _requestMap;
}

@end
