//
//  InternalSchemeHandler.h
//  PandaBrowser
//
//  Created by qing<PERSON> on 2022/3/4.
//

#import <Foundation/Foundation.h>
#import <WebKit/WebKit.h>

@interface InternalSchemeHandler : NSObject<WKURLSchemeHandler>

- (NSURLResponse*)defaultResponse:(NSURL*)url;
- (NSData*)defaultResponseData:(NSURL*)url;
- (NSDictionary*)getQueryWithURL:(NSURL*)url;

@property(nonatomic,strong) NSString *path;

@end

