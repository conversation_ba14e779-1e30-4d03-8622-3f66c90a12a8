//
//  PandaWebView.m
//  PandaBrowser
//
//  Created by qingbin on 2022/3/2.
//

#import "PandaWebView.h"
#import "UserAgentManager.h"

#import "PPNotifications.h"

#import "MaizyHeader.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"

#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "NSObject+Helper.h"
#import "PPEnums.h"

#import "PlayerView.h"
#import "PlayModel.h"
#import "MenuHelper.h"
#import "PreferenceManager.h"
#import "Tab.h"
#import "MessageItemView.h"
#import "CopyrightHelper.h"

#import "ThemeProtocol.h"
#import "PreferenceManager.h"

#import "BrowserUtils.h"

#import "SnifferHelper.h"
#import "PlaylistDetectorHelper.h"

#import "AppDelegate.h"
#import "BrowserViewController.h"

#import "BottomToolbar.h"
#import "TopToolbar.h"
#import "NSString+Helper.h"

#import "AdBlockManager.h"
#import "PlayListDialogView.h"
#import "SwipeIndicatorView.h"

#import "InternalURL.h"
#import "ReaderManager.h"

static WKWebsiteDataStore *_nonPersistentDataStore;

@interface PandaWebView ()<MenuHelperDelegate,UIScrollViewDelegate,ThemeProtocol>
//视频/音频播放view
@property (nonatomic, strong) PlayerView* playerView;
//视频模式入口
@property (nonatomic, strong) MessageItemView* playButton;
//阅读模式入口
@property (nonatomic, strong) MessageItemView* readerButton;
//判断是否在滑动中
@property (nonatomic, assign) BOOL isScrolling;
//下拉刷新控件
@property (nonatomic, strong) UIRefreshControl *refreshControl;
//两个约束，控制readerButton的位置
@property (nonatomic, strong) MASConstraint *bottomOffsetConstraint;
//
@property (nonatomic, strong) MASConstraint *abovePlayButtonOffsetConstraint;

@end

@implementation PandaWebView

- (instancetype)initWithConfiguration:(WKWebViewConfiguration*)configuration
{
    //设置scheme handler
//    if(![configuration urlSchemeHandlerForURLScheme:@"mediaheader"]) {
//        //不可以重复注册
//        MediaHeaderSchemeHandler* mediaHeaderSchemeHandler = [[MediaHeaderSchemeHandler alloc] init];
//        [configuration setURLSchemeHandler:mediaHeaderSchemeHandler forURLScheme:@"mediaheader"];
//    }

#warning -- UA的设置要在这里, 否则访问不了百度贴吧
/// 这里不能用applicationNameForUserAgent设置，
/// 1)否则会出现多重UA
/// 2)并且会经常跳转百度APP
//    NSString* useragent = [[UserAgentManager shareInstance] getCurrentUserAgent];
//    configuration.applicationNameForUserAgent = useragent; 
    
//    self.customUserAgent = [[UserAgentUtil shareInstance] getCurrentUserAgent];
    
    self = [super initWithFrame:CGRectZero configuration:configuration];
    
    [[AdBlockManager shareInstance] loadSavedRulesWithWebView:self];
    
    [self addSubviews];
    [self defineLayout];
    [self setupObservers];
    [self applyTheme];
    
    return self;
}

#pragma mark -- 夜间模式

- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
//        self.scrollView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
    } else {
        self.backgroundColor = UIColor.whiteColor;
//        self.scrollView.backgroundColor = UIColor.whiteColor;
    }
    
    // For WKWebView background color to take effect, isOpaque must be false,
    // which is counter-intuitive. Default is true. The color is previously
    // set to black in the WKWebView init.
    //如果不设置这个条件,那么暗黑模式下会有白色闪动
    //https://www.jianshu.com/p/b1af81aecad5
    self.opaque = !isDarkTheme;
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}

- (void)removeAllBrowsingData:(void(^)(void))completeHandler
{
    id dataTypes = [WKWebsiteDataStore allWebsiteDataTypes];
    [self.configuration.websiteDataStore removeDataOfTypes:dataTypes modifiedSince:[NSDate distantPast] completionHandler:^{
        if(completeHandler) {
            completeHandler();
        }
    }];
}

#pragma mark -- 广告过滤版权控制
- (void)updateRuleListIfNeedWithUrl:(NSURL *)URL
{
    BOOL enabledAdblock = [[PreferenceManager shareInstance].items.enabledAdblock boolValue];
    if(!enabledAdblock) {
        //没有开通广告过滤，直接移除所有广告规则
        [self.configuration.userContentController removeAllContentRuleLists];
        return;
    }
    
    BOOL isValid = [[CopyrightHelper shareInstance] validAdBlockUrl:URL];
    //v2.6.7，百度搜索分成不能屏蔽广告
    //v2.7.6，开启屏蔽，百度搜索分成没有了
//    BOOL isBaiDu = [[CopyrightHelper shareInstance] isBaidu:URL];
    if(!isValid) {
        //需要过滤
        [self.configuration.userContentController removeAllContentRuleLists];
        return;
    } else {
        [[AdBlockManager shareInstance] loadSavedRulesWithWebView:self];
    }
}

#pragma mark - v2.6.9 生成pdf
- (void)exportPDF {
    if (@available(iOS 14.0, *)) {
        [self createPDFWithConfiguration:nil completionHandler:^(NSData * _Nullable pdfData, NSError * _Nullable error) {
            if (error) {
                NSLog(@"生成 PDF 失败: %@", error.localizedDescription);
                return;
            }

            if (pdfData) {
                [self savePDFWithData:pdfData];
            }
        }];
    } else {
        NSLog(@"仅支持 iOS 14 及以上版本的 createPDF 接口");
    }
}

- (void)savePDFWithData:(NSData *)data {
    // 创建时间戳字符串
    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    [formatter setDateFormat:@"yyyyMMdd_HHmmss"];
    NSString *timestamp = [formatter stringFromDate:[NSDate date]];

    // 拼接文件名
    NSString *fileName = [NSString stringWithFormat:@"output_%@.pdf", timestamp];
    
    NSURL *docsURL = [[[NSFileManager defaultManager] URLsForDirectory:NSDocumentDirectory inDomains:NSUserDomainMask] firstObject];
    NSURL *fileURL = [docsURL URLByAppendingPathComponent:fileName];

    NSError *writeError = nil;
    BOOL success = [data writeToURL:fileURL options:NSDataWritingAtomic error:&writeError];

    if (success) {
        NSLog(@"PDF 已保存到: %@", fileURL.path);
        // 调用分享功能
        [self sharePDFAtURL:fileURL];
    } else {
        NSLog(@"保存 PDF 失败: %@", writeError.localizedDescription);
    }
}

- (void)sharePDFAtURL:(NSURL *)fileURL {
    dispatch_async(dispatch_get_main_queue(), ^{
        UIActivityViewController *activityVC = [[UIActivityViewController alloc] initWithActivityItems:@[fileURL] applicationActivities:nil];
        activityVC.completionWithItemsHandler = ^(UIActivityType __nullable activityType, BOOL completed, NSArray * __nullable returnedItems, NSError * __nullable activityError) {
            NSFileManager* fileManager = [NSFileManager defaultManager];
            if([fileManager fileExistsAtPath:fileURL.absoluteString]) {
                [fileManager removeItemAtPath:fileURL.absoluteString error:nil];
            }
            
            if(!activityError && completed) {
                [UIView showSucceed:NSLocalizedString(@"bookmark.export.succ", nil)];
            }
        };
        
        UIWindow* window = [NSObject normalWindow];
        // iPad 需要设置 popoverPresentationController
        if ([BrowserUtils isiPad]) {
            activityVC.popoverPresentationController.sourceView = self;
            activityVC.popoverPresentationController.sourceRect = CGRectMake(CGRectGetMidX(self.bounds),
                                                                              CGRectGetMidY(self.bounds),
                                                                              1, 1);
        }

        [window.rootViewController presentViewController:activityVC animated:YES completion:nil];
    });
}

#pragma mark - v2.6.9 生成p二维码
- (void)exportQRCode {
    NSString *urlString = self.URL.absoluteString;
    if (!urlString) {
        return;
    }

    UIImage *qrImage = [self generateQRCodeFromString:urlString];
    if (!qrImage) {
        return;
    }

    dispatch_async(dispatch_get_main_queue(), ^{
        UIActivityViewController *activityVC = [[UIActivityViewController alloc] initWithActivityItems:@[qrImage] applicationActivities:nil];
        activityVC.completionWithItemsHandler = ^(UIActivityType __nullable activityType, BOOL completed, NSArray * __nullable returnedItems, NSError * __nullable activityError) {
            if(!activityError && completed) {
                [UIView showSucceed:NSLocalizedString(@"bookmark.export.succ", nil)];
            }
        };
        
        UIWindow* window = [NSObject normalWindow];
        // iPad 需要设置 popoverPresentationController
        if ([BrowserUtils isiPad]) {
            activityVC.popoverPresentationController.sourceView = self;
            activityVC.popoverPresentationController.sourceRect = CGRectMake(CGRectGetMidX(self.bounds),
                                                                              CGRectGetMidY(self.bounds),
                                                                              1, 1);
        }

        [window.rootViewController presentViewController:activityVC animated:YES completion:nil];
    });
}

- (UIImage *)generateQRCodeFromString:(NSString *)string {
    NSData *data = [string dataUsingEncoding:NSUTF8StringEncoding];
    CIFilter *filter = [CIFilter filterWithName:@"CIQRCodeGenerator"];
    [filter setValue:data forKey:@"inputMessage"];
    [filter setValue:@"H" forKey:@"inputCorrectionLevel"]; // 容错级别 H = 30%

    CIImage *ciImage = filter.outputImage;

    // 放大二维码（否则会很模糊）
    CGFloat scale = 10.0;
    CGAffineTransform transform = CGAffineTransformMakeScale(scale, scale);
    CIImage *scaledImage = [ciImage imageByApplyingTransform:transform];

    return [UIImage imageWithCIImage:scaledImage];
}

#pragma mark -- 添加阅读模式监听
- (void)setupObservers
{
    @weakify(self)
    [self.playButton setClickBlock:^(NSArray<PlayModel *> *videoList, ReaderModel *readerModel) {
        @strongify(self)
        //进入播放模式
        [self _preprocessWithVideoList:videoList];
    }];
    
    [self.readerButton setClickBlock:^(NSArray<PlayModel *> *videoList, ReaderModel *readerModel) {
        @strongify(self)
        //进入阅读模式
        [self enterReaderWithModel:readerModel];
    }];
    
    UIPanGestureRecognizer* panGesture = [[UIPanGestureRecognizer alloc]initWithTarget:self action:@selector(viewDragged:)];
    [self.playButton addGestureRecognizer:panGesture];
    
    panGesture = [[UIPanGestureRecognizer alloc]initWithTarget:self action:@selector(viewDragged:)];
    [self.readerButton addGestureRecognizer:panGesture];
    
    [self _addPull2RefreshHeaderIfNeed];
    
    [[self.refreshControl rac_signalForControlEvents:UIControlEventValueChanged] subscribeNext:^(id x) {
        @strongify(self)
        //下拉刷新
        if(self.refreshControl.isRefreshing) {
            [self.refreshControl endRefreshing];
        }
        
        [self.tab reload];
    }];
    
    //视频模式
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(playlistDetectorNotification:) name:kPlaylistDetectorNotification object:nil];
    //阅读模式
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(readerDetectorNotification:) name:kReadabilityMessageHelperNotification object:nil];

    //暗黑模式
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(darkThemeDidChangeNotification:)
                                                 name:kDarkThemeDidChangeNotification
                                               object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(_addPull2RefreshHeaderIfNeed)
                                                 name:kupdatePullToRefreshNotification
                                               object:nil];
}

#pragma mark -- 下拉刷新

- (void)_addPull2RefreshHeaderIfNeed
{
    //下拉刷新
    BOOL enabledPullToRefesh =[[PreferenceManager shareInstance].items.enabledPullToRefesh boolValue];
    if(!enabledPullToRefesh) {
        self.scrollView.refreshControl = nil;
        return;
    } else {
        self.scrollView.refreshControl = self.refreshControl;
    }
}

- (void)viewDragged:(UIPanGestureRecognizer *)gestureRecognizer
{
    //该代码段由ChatGPT生成
    UIView *view = gestureRecognizer.view;
        
    // 获取拖动的偏移量
    CGPoint translation = [gestureRecognizer translationInView:view.superview];
    
    // 移动视图
    view.center = CGPointMake(view.center.x + translation.x, view.center.y + translation.y);
    
    // 最后停留的位置
    if (gestureRecognizer.state == UIGestureRecognizerStateEnded) {
        CGRect frame = view.frame;
        frame.origin.y += translation.y;
        
        float sizeHeight = [MessageItemView sizeHeight];
        float rightOffset = iPadValue(30, 20);
        
        float y = MAX(frame.origin.y, sizeHeight/2.0+[TopToolbar toolbarHeight]);
        y = MIN(y, self.frame.size.height-sizeHeight/2.0-[BottomToolbar toolbarHeight]);
        
        if(frame.origin.x >= kScreenWidth/2.0) {
            //右边
//            view.center = CGPointMake(kScreenWidth-sizeHeight/2-rightOffset, y);
            //最终需要通过autolayout更新，否则layoutIfNeed更新的时候，又会重新恢复原位
            [view mas_updateConstraints:^(MASConstraintMaker *make) {
                make.right.mas_offset(-rightOffset);
                make.bottom.mas_offset(-(CGRectGetHeight(self.frame)-y));
            }];
        } else {
            //左边
//            view.center = CGPointMake(rightOffset+sizeHeight/2.0, y);
            
            //最终需要通过autolayout更新，否则layoutIfNeed更新的时候，又会重新恢复原位
            [view mas_updateConstraints:^(MASConstraintMaker *make) {
                make.right.mas_offset(-(CGRectGetWidth(self.frame) - rightOffset - sizeHeight));
                make.bottom.mas_offset(-(CGRectGetHeight(self.frame)-y));
            }];
        }
    }
    
    // 重置拖动的偏移量
    [gestureRecognizer setTranslation:CGPointZero inView:view.superview];
}

// 清空播放模式
- (void)clearWebViewReaderAndPlayer
{
    [self.playButton clearData];
    [self.readerButton clearData];
}

#pragma mark -- layout
- (void)addSubviews
{
    [self addSubview:self.readerButton];
    [self addSubview:self.playButton];
}

- (void)defineLayout
{
    float sizeHeight = [MessageItemView sizeHeight];
    
    UIWindow* window = YBIBNormalWindow();
    float bottomOffset = window.safeAreaInsets.bottom;
    
    float rightOffset = iPadValue(40, 20);
    bottomOffset = iPadValue(kScreenHeight/6.0, 50) + bottomOffset;
    
    [self.playButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_offset(-rightOffset);
        make.bottom.mas_offset(-bottomOffset);
        make.size.mas_equalTo(sizeHeight);
    }];
    
    [self.readerButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_offset(-rightOffset);
        make.size.mas_equalTo(sizeHeight);
        self.bottomOffsetConstraint = make.bottom.mas_offset(-bottomOffset);
        self.abovePlayButtonOffsetConstraint = make.bottom.lessThanOrEqualTo(self.playButton.mas_top).offset(-20);
    }];
    
    [self.bottomOffsetConstraint deactivate];
    [self.abovePlayButtonOffsetConstraint activate];
}

#pragma mark - 阅读模式

- (void)readerDetectorNotification:(NSNotification *)notification
{
    BOOL enabledReader = [[PreferenceManager shareInstance].items.enabledReader boolValue];
    if(!enabledReader) {
        //关闭了阅读模式
        [self.readerButton endReader];
        return;
    }
    
    //检测是否是当前webView
    PandaWebView* webView = notification.userInfo[@"webView"];
    if(webView != self) return;
    
    ReaderModel* item = notification.object;
    
    if(item.isReaderModeAvailable) {
        //检测到阅读模式
        if(self.playButton.status == MessageItemStatusVideo) {
            //有视频模式提示按钮
            [self.bottomOffsetConstraint deactivate];
            [self.abovePlayButtonOffsetConstraint activate];
        } else {
            //没有视频模式提示按钮
            [self.bottomOffsetConstraint activate];
            [self.abovePlayButtonOffsetConstraint deactivate];
        }
        
        [self.readerButton showReader:item];
        //如果收到了通知,而且不是在滑动状态,那么显示入口
        if(!self.isScrolling) {
            //只有Playlist Detect才能处理按钮显示逻辑
            self.readerButton.alpha = 1;
        }
    } else {
        //没有检测到阅读模式
        [self.readerButton endReader];
    }
}

#pragma mark -- 视频/音频播放模式

- (void)playlistDetectorNotification:(NSNotification*)notification
{
    [self _handleVideoDetectWithNotification:notification];
}

- (void)_handleVideoDetectWithNotification:(NSNotification *)notification
{
    //音视频检测是有多次的
    BOOL enabledPlayer = [[PreferenceManager shareInstance].items.enabledPlayer boolValue];
    if(!enabledPlayer) {
        //关闭了视频模式
        [self.playButton endPlayer];
        return;
    }
    
    //检测是否是当前webView
    PandaWebView* webView = notification.userInfo[@"webView"];
    if(webView != self) return;
    
    //版权检测
    BOOL isValid = [[CopyrightHelper shareInstance] validPlayUrl:webView.URL];
    if(!isValid) {
        //关闭视频模式
        [self.playButton endPlayer];
        return;
    }
    
    PlayModel* item = notification.object;
    if(item.status == PlayModelItemStatusPlaylistDetector
       || item.status == PlayModelItemStatusBlobDetector) {
        //Playlist Detect
        //Blob Detect
        if([item.src isEqualToString:self.playButton.playModel.src]) {
            return;
        }
    }
    
    //加快打开速度
    if(item.src.length>0) {
        //加个延迟，等待url scheme的返回，如果url scheme没有触发，那么就触发当前预缓存的逻辑
        //不用加延迟，不能太慢，否则51吃瓜网会播放失败，防止MediaHeaderSchemeHandler失败，需要加快这边预检测的逻辑
        [[SnifferHelper shareInstance] snifferWithOriginUrl:item.originUrl videoUrl:item.src completion:nil];
    }
    
    [self.playButton enterPlayer:item];
    
    // 调整阅读模式提示按钮
    if(self.playButton.status == MessageItemStatusVideo) {
        //有视频模式提示按钮
        [self.bottomOffsetConstraint deactivate];
        [self.abovePlayButtonOffsetConstraint activate];
    } else {
        //没有视频模式提示按钮
        [self.bottomOffsetConstraint activate];
        [self.abovePlayButtonOffsetConstraint deactivate];
    }
    
    if(!self.playerView) {
        //初始化
        [self.playerView removeFromSuperview];
        self.playerView = nil;
        self.playerView = [[PlayerView alloc] init];
        
        @weakify(self)
        [self.playerView setClosePlayerBlock:^{
            @strongify(self)
            [self.playerView removeFromSuperview];
            self.playerView = nil;
        }];
    }
    
    //如果收到了通知,而且不是在滑动状态,那么显示入口
    if(!self.isScrolling) {
        //只有Playlist Detect才能处理按钮显示逻辑
        self.playButton.alpha = 1;
    }
}

#pragma mark -- 进入阅读模式

- (void)enterReaderWithModel:(ReaderModel *)readerModel
{
    if (readerModel.currentUrl.length == 0) return;
    
    self.isInReader = YES;
    
    // 使用ReaderManager保存当前阅读模式内容
    [ReaderManager sharedInstance].currentReaderModel = readerModel;
    
    // 进入阅读模式
    [[NSNotificationCenter defaultCenter] postNotificationName:kEnterReaderNotification object:self];
    
    // 隐藏阅读模式图标
    [self.readerButton endReader];
}

#pragma mark -- 视频模式预处理

- (void)_preprocessWithVideoList:(NSArray *)videoList
{
    if(videoList.count == 0) return;
    
    if(videoList.count == 1) {
        //直接进入视频模式
        [self enterPlayViewWithModel:videoList.firstObject];
        return;
    }
    
    PlayListDialogView* dialogView = [PlayListDialogView new];
    [dialogView showWithModel:videoList];
    
    @weakify(self)
    [dialogView setEnterPlayViewAction:^BOOL (PlayModel *model) {
        @strongify(self)
        return [self enterPlayViewWithModel:model];
    }];
}

#pragma mark -- 进入视频模式
- (BOOL)enterPlayViewWithModel:(PlayModel*)item
{
    if(item.src.length == 0) {
        [UIView showFailed:NSLocalizedString(@"tips.invalid.link", nil)];
        
        [self.playerView removeFromSuperview];
        self.playerView = nil;
        return NO;
    }
    
    //暂停网页正在播放的视频
    if(@available(iOS 14.5, *)) {
        [self pauseAllMediaPlayback:nil];
    }
    
    BOOL canHandleUrl = YES;    
    //加密链接播放
    if([item.src.lowercaseString hasPrefix:@"blob:http"]) {
        [UIView showToast:NSLocalizedString(@"tips.encrypt.link", nil)];
        return NO;
    }
    
    if(canHandleUrl) {
        [self _setupPlayerIfNeed];
        
        [self.playerView playWithModel:item];
    }
    
    return YES;
}

- (void)_setupPlayerIfNeed
{
    if(!self.playerView) {
        //初始化
        [self.playerView removeFromSuperview];
        self.playerView = nil;
        self.playerView = [[PlayerView alloc] init];
        
        @weakify(self)
        [self.playerView setClosePlayerBlock:^{
            @strongify(self)
            [self.playerView removeFromSuperview];
            self.playerView = nil;
        }];
    }
    
    if(!self.playerView.superview) {
        //添加在BrowserViewController的view上
        [self.superview addSubview:self.playerView];
        [self.playerView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.equalTo(self.superview);
        }];
    }
}

#pragma mark -- UIScrollViewDelegate
- (void)scrollViewWillEndDragging:(UIScrollView *)scrollView withVelocity:(CGPoint)velocity targetContentOffset:(inout CGPoint *)targetContentOffset
{
    if (self.playButton.status == MessageItemStatusVideo) {
        if (velocity.y > 0) {
            //往下滑动
            self.playButton.alpha = 0;
        } else if (velocity.y <= 0) {
            //往上滑动
            self.playButton.alpha = 1;
        }
    }
        
    if (self.readerButton.status == MessageItemStatusReader) {
        if (velocity.y > 0) {
            //往下滑动
            self.readerButton.alpha = 0;
        } else if (velocity.y <= 0) {
            //往上滑动
            self.readerButton.alpha = 1;
        }
    }
}

- (void)scrollViewWillBeginDragging:(UIScrollView *)scrollView
{
    self.isScrolling = YES;
}

- (void)scrollViewDidEndDragging:(UIScrollView *)scrollView willDecelerate:(BOOL)decelerate
{
    if(!decelerate) {
        self.isScrolling = NO;
        
        //停止滑动，显示播放按钮
        if(self.playButton.status == MessageItemStatusVideo) {
            self.playButton.alpha = 1;
        }
        
        if(self.readerButton.status == MessageItemStatusReader) {
            self.readerButton.alpha = 1;
        }
    }
}

- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView
{
    self.isScrolling = NO;
}

- (void)scrollViewDidEndScrollingAnimation:(UIScrollView *)scrollView
{
    self.isScrolling = NO;
}

#pragma mark -- 长按弹出menu
- (BOOL)canPerformAction:(SEL)action withSender:(id)sender
{
//    NSLog(@".................. = %@", NSStringFromSelector(action));

    NSString* lookup = [NSString stringWithFormat:@"_%@:", @"lookup"];
    SEL sel_lookup = NSSelectorFromString(lookup);
    
    NSString* translate = [NSString stringWithFormat:@"_%@:", @"translate"];
    SEL sel_translate = NSSelectorFromString(translate);
    
    //transliterateChinese: 简繁体切换
    //define: 查询
    //translate: 查询
    
    if (action == @selector(copy:)
        || action == @selector(paste:)
        || action == @selector(selectAll:)
        || action == @selector(define:)
        || action == @selector(translate:)
        || action == sel_translate
        || action == sel_lookup
        || action == @selector(findInPageItem)) {
            return YES;
        }
    
    return NO;
}

- (BOOL)canBecomeFirstResponder
{
    return YES;
}

#pragma mark -- MenuHelperDelegate
- (void)findInPageItem
{
    NSString* js = @"getSelection().toString()";
    [self evaluateJavaScript:js completionHandler:^(id _Nullable obj, NSError * _Nullable error) {
        if(error) {
            LOG_ERROR(@"error = %@", error.localizedDescription);
        } else {
            NSString* selection = obj;
            if(selection.length == 0) return;
            
            if(self.didFindInBarBlock) {
                self.didFindInBarBlock(selection);
            }
        }
    }];
}

#pragma mark -- lazy init

- (MessageItemView *)playButton
{
    if(!_playButton) {
        _playButton = [MessageItemView new];
    }
    
    return _playButton;
}

- (MessageItemView *)readerButton
{
    if(!_readerButton) {
        _readerButton = [MessageItemView new];
    }
    
    return _readerButton;
}

- (UIRefreshControl *)refreshControl
{
    if(!_refreshControl) {
        _refreshControl = [[UIRefreshControl alloc]init];
    }
    
    return _refreshControl;
}

- (void)dealloc
{
    BOOL isPrivate = [[PreferenceManager shareInstance].items.isPrivate boolValue];
    if(isPrivate) {
        //无痕模式，清理数据
        [self removeAllBrowsingData:nil];
    }
    
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

@end
