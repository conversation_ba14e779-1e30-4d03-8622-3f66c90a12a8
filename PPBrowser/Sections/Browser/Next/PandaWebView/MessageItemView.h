//
//  MessageItemView.h
//  PPBrowser
//
//  Created by qingbin on 2022/5/17.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "PPEnums.h"

#import "PlayModel.h"
#import "ReaderModel.h"

//图标来源:
//https://www.iconfont.cn/collections/detail?spm=a313x.7781069.1998910419.d9df05512&cid=40485
//https://www.iconfont.cn/collections/detail?spm=a313x.7781069.1998910419.dc64b3430&cid=33548
//https://www.iconfont.cn/collections/detail?spm=a313x.7781069.1998910419.d9df05512&cid=40558
@interface MessageItemView : UIView

@property (readonly) MessageItemStatus status;
@property (readonly) PlayModel* playModel;
// 点击事件
@property (nonatomic, copy) void (^clickBlock)(NSArray<PlayModel *>* videoList, ReaderModel* readerModel);

//进入播放器
- (void)enterPlayer:(PlayModel *)playModel;
//退出播放器
- (void)endPlayer;
//只更新数据，不处理显示逻辑
- (void)updateWithPlayModel:(PlayModel *)playModel;

//显示阅读模式
- (void)showReader:(ReaderModel *)readerModel;
//退出阅读模式
- (void)endReader;
//刷新网页后，清空数据
- (void)clearData;

//高度
+ (float)sizeHeight;

@end

