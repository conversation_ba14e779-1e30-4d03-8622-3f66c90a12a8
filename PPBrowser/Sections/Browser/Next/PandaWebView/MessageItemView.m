//
//  MessageItemView.m
//  PPBrowser
//
//  Created by qingbin on 2022/5/17.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "MessageItemView.h"

#import "PPNotifications.h"

#import "MaizyHeader.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"

#import "UIView+Helper.h"
#import "UIColor+Helper.h"

#import "YJBadgeLabel.h"

@interface MessageItemView ()

@property (nonatomic, strong) UIImageView *imageView;

@property (nonatomic, assign) MessageItemStatus status;
//视频模式model
@property (nonatomic, strong) PlayModel* playModel;
//阅读模式model
@property (nonatomic, strong) ReaderModel *readerModel;

@property (nonatomic, strong) YJBadgeLabel* badgeLabel;

@property (nonatomic, strong) NSMutableArray* videoList;

@end

@implementation MessageItemView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    
    if(self) {
        [self addSubviews];
        [self defineLayout];
        [self setupObservers];
        
        float raidus = [MessageItemView sizeHeight];
        self.backgroundColor = UIColor.whiteColor;
        self.layer.cornerRadius = raidus/2.0;

        self.hidden = YES;
        
        self.layer.shadowColor   = [UIColor colorWithHexString:@"#000000"].CGColor;
        self.layer.shadowOpacity = 0.2f;
        self.layer.shadowRadius  = 10.0f;
        self.layer.shadowOffset  = CGSizeMake(0.0f, 0);
        
        [self updateWithStatus:MessageItemStatusDefault];
    }
    
    return self;
}

+ (float)sizeHeight
{
    return 50;
}

- (void)updateWithStatus:(MessageItemStatus)status
{
    self.status = status;
    
    if(status == MessageItemStatusDefault) {
        self.hidden = YES;
    } else {
        if (status == MessageItemStatusVideo) {
            self.imageView.image = [UIImage imageNamed:@"player_detect_icon"];
        } else if (status == MessageItemStatusReader) {
            self.imageView.image = [UIImage imageNamed:@"book_detect_icon"];
        }
        
        self.hidden = NO;
    }
}

//进入播放器
- (void)enterPlayer:(PlayModel *)playModel
{
    //处理显示逻辑
    if(self.status == MessageItemStatusDefault) {
        [self updateWithStatus:MessageItemStatusVideo];
    }
    
    [self updateWithPlayModel:playModel];
}

//退出播放器
- (void)endPlayer
{
    self.playModel = nil;
    
    if(self.status == MessageItemStatusVideo) {
        [self updateWithStatus:MessageItemStatusDefault];
    }
    
    //更新角标
    [self.badgeLabel setValue:0];
    //清除缓存
    [self.videoList removeAllObjects];
    self.videoList = nil;
}

//只更新数据，不处理显示逻辑
- (void)updateWithPlayModel:(PlayModel *)playModel
{
    //角标逻辑
    NSString* originUrl = playModel.originUrl;
    if(originUrl.length <= 0) return;
    
    //先判断是否是同一个网址的
    BOOL isTheSameWebURL = YES;
    
    NSMutableDictionary* dict = [NSMutableDictionary dictionary];
    for(int i=0;i<self.videoList.count;i++) {
        PlayModel* item = self.videoList[i];
        NSString* key = [NSString stringWithFormat:@"%@-%@", item.originUrl, item.src];
        dict[key] = item;
        
        if(![item.originUrl isEqualToString:playModel.originUrl]) {
            isTheSameWebURL = NO;
        }
    }
    
    if(!isTheSameWebURL) {
        //不同的网址，那么清空之前的视频链接
        [self.videoList removeAllObjects];
    } else {
        BOOL isExist = NO;
        NSString* key = [NSString stringWithFormat:@"%@-%@", playModel.originUrl, playModel.src];
        isExist = dict[key];
        
        if(isExist) {
            return;
        } else {
            //探测到不同的视频链接
            [self.videoList addObject:playModel];
        }
    }
    
    self.playModel = playModel;
    
    [self.badgeLabel setValue:self.videoList.count];
}

//显示阅读模式
- (void)showReader:(ReaderModel *)readerModel
{
    //处理显示逻辑
    [self updateWithStatus:MessageItemStatusReader];
    self.readerModel = readerModel;
}

//退出阅读模式
- (void)endReader
{
    self.readerModel = nil;
    [self updateWithStatus:MessageItemStatusDefault];
}

//刷新网页后，清空数据
- (void)clearData
{
    //退出播放器
    [self endPlayer];
    //退出阅读模式
    [self endReader];
}

#pragma mark - handle events

- (void)setupObservers
{
    UITapGestureRecognizer* tap = [UITapGestureRecognizer new];
    [self addGestureRecognizer:tap];
    @weakify(self)
    [tap.rac_gestureSignal subscribeNext:^(id x) {
        @strongify(self)
        if (self.clickBlock) {
            self.clickBlock(self.videoList, self.readerModel);
        }
    }];
}

#pragma mark - layout

- (void)addSubviews
{
    [self addSubview:self.imageView];
    [self addSubview:self.badgeLabel];
}

- (void)defineLayout
{
    float offset = 10;
    [self.imageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self).insets(UIEdgeInsetsMake(offset, offset, offset, offset));
    }];
    
    [self.badgeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.imageView.mas_right).offset(2);
        make.centerY.equalTo(self.imageView.mas_top).offset(-2);
    }];
}

- (UIImageView *)imageView
{
    if(!_imageView) {
        _imageView = [UIImageView new];
    }
    
    return _imageView;
}

- (YJBadgeLabel *)badgeLabel
{
    if(!_badgeLabel) {
        _badgeLabel = [[YJBadgeLabel alloc]initWithFontSize:[UIFont systemFontOfSize:12]
                                                       type:BadgeTypeRedBackground];
        [_badgeLabel setValue:0];
    }
    
    return _badgeLabel;
}

- (NSMutableArray *)videoList
{
    if(!_videoList) {
        _videoList = [NSMutableArray array];
    }
    
    return _videoList;
}

@end
