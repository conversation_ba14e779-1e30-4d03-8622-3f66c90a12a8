//
//  WebDataStoreManager.m
//  PPBrowser
//
//  Created by qingbin on 2025/5/2.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "WebDataStoreManager.h"

@interface WebDataStoreManager ()
/// 持久化的共享数据存储
@property (nonatomic, strong) WKWebsiteDataStore *defaultDataStore;

/// 非持久化的共享数据存储
@property (nonatomic, strong) WKWebsiteDataStore *sharedPrivateDataStore;

/// 自定义持久化数据存储缓存
@property (nonatomic, strong) NSMutableDictionary<NSString *, WKWebsiteDataStore *> *customDataStores;

@end

@implementation WebDataStoreManager

#pragma mark - 单例实现

+ (instancetype)sharedManager {
    static WebDataStoreManager *sharedInstance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        sharedInstance = [[self alloc] init];
    });
    return sharedInstance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _defaultDataStore = [WKWebsiteDataStore defaultDataStore];
        _sharedPrivateDataStore = [WKWebsiteDataStore nonPersistentDataStore];
        _customDataStores = [NSMutableDictionary dictionary];
    }
    return self;
}

#pragma mark - 公共方法

- (WKWebsiteDataStore *)getDataStoreForMode:(WebDataStoreMode)mode identifierName:(nullable NSString *)identifierName {
    switch (mode) {
        case WebDataStoreModeDefault:
            return self.defaultDataStore;
            
        case WebDataStoreModeSharedPrivate:
            return self.sharedPrivateDataStore;
            
        case WebDataStoreModeIsolatedPrivate:
            // 每次返回一个全新的非持久化数据存储实例
            return [WKWebsiteDataStore nonPersistentDataStore];
            
        case WebDataStoreModeCustomPersistent:
            // 检查标识符是否存在
            if (!identifierName || identifierName.length == 0) {
                NSLog(@"警告：自定义数据存储模式必须提供有效的标识符名称，使用默认存储替代");
                return self.defaultDataStore;
            }
            
            // 根据标识符返回或创建自定义持久化数据存储
            WKWebsiteDataStore *existingStore = self.customDataStores[identifierName];
            if (existingStore) {
                return existingStore;
            } else {
                // 在实际情况下，这个功能需要依赖自定义的持久化方案
                // 因为 WKWebsiteDataStore 并不直接支持创建多个命名的持久化存储
                // 这里暂时返回一个非持久化存储作为替代
                WKWebsiteDataStore *newStore = [WKWebsiteDataStore nonPersistentDataStore];
                self.customDataStores[identifierName] = newStore;
                return newStore;
            }
    }
}

- (WKWebViewConfiguration *)createConfigurationWithMode:(WebDataStoreMode)mode identifierName:(nullable NSString *)identifierName {
    WKWebViewConfiguration *config = [[WKWebViewConfiguration alloc] init];
    config.websiteDataStore = [self getDataStoreForMode:mode identifierName:identifierName];
    return config;
}

- (void)clearDataForMode:(WebDataStoreMode)mode identifierName:(nullable NSString *)identifierName completion:(nullable void (^)(void))completion {
    WKWebsiteDataStore *dataStore = [self getDataStoreForMode:mode identifierName:identifierName];
    
    // 获取从1970年至今的时间范围
    NSDate *date = [NSDate dateWithTimeIntervalSince1970:0];
    
    // 获取所有可移除的数据类型
    NSSet *dataTypes = [WKWebsiteDataStore allWebsiteDataTypes];
    
    // 清除指定日期之后的所有数据
    [dataStore removeDataOfTypes:dataTypes modifiedSince:date completionHandler:^{
        if (completion) {
            completion();
        }
    }];
}

- (void)clearCookiesForMode:(WebDataStoreMode)mode identifierName:(nullable NSString *)identifierName completion:(nullable void (^)(void))completion {
    WKWebsiteDataStore *dataStore = [self getDataStoreForMode:mode identifierName:identifierName];
    
    [dataStore.httpCookieStore getAllCookies:^(NSArray<NSHTTPCookie *> * _Nonnull cookies) {
        if (cookies.count == 0) {
            if (completion) {
                completion();
            }
            return;
        }
        
        dispatch_group_t group = dispatch_group_create();
        
        for (NSHTTPCookie *cookie in cookies) {
            dispatch_group_enter(group);
            [dataStore.httpCookieStore deleteCookie:cookie completionHandler:^{
                dispatch_group_leave(group);
            }];
        }
        
        dispatch_group_notify(group, dispatch_get_main_queue(), ^{
            if (completion) {
                completion();
            }
        });
    }];
}

- (void)printCookiesForMode:(WebDataStoreMode)mode identifierName:(nullable NSString *)identifierName {
    WKWebsiteDataStore *dataStore = [self getDataStoreForMode:mode identifierName:identifierName];
    
    [dataStore.httpCookieStore getAllCookies:^(NSArray<NSHTTPCookie *> * _Nonnull cookies) {
        NSLog(@"数据存储模式: %ld 的Cookie数量: %lu", (long)mode, (unsigned long)cookies.count);
        
        for (NSUInteger i = 0; i < cookies.count; i++) {
            NSHTTPCookie *cookie = cookies[i];
            NSLog(@"[%lu] %@ = %@, 域名: %@", (unsigned long)i, cookie.name, cookie.value, cookie.domain);
        }
    }];
}

@end
