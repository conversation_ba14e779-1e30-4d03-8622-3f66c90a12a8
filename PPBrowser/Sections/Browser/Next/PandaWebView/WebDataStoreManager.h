//
//  WebDataStoreManager.h
//  PPBrowser
//
//  Created by qingbin on 2025/5/2.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <WebKit/WebKit.h>

/**
 * Web视图数据存储模式
 */
typedef NS_ENUM(NSInteger, WebDataStoreMode) {
    /// 默认数据存储模式 - 持久化存储，应用内共享登录状态
    WebDataStoreModeDefault,
    
    /// 共享隐私模式 - 非持久化存储，但同一个实例内共享登录状态
    WebDataStoreModeSharedPrivate,
    
    /// 独立隐私模式 - 非持久化存储，每个实例独立登录状态
    WebDataStoreModeIsolatedPrivate,
    
    /// 自定义名称的持久化存储模式 - 可用于创建多个独立的持久化存储
    /// 注意：自定义持久化模式需要单独指定identifierName参数
    WebDataStoreModeCustomPersistent
};

NS_ASSUME_NONNULL_BEGIN

/**
 * Web视图数据存储管理器
 */
@interface WebDataStoreManager : NSObject

/// 单例实例
+ (instancetype)sharedManager;

/// 获取指定模式的数据存储
/// @param mode 数据存储模式
/// @param identifierName 自定义标识符名称 (仅在 WebDataStoreModeCustomPersistent 模式下使用)
- (WKWebsiteDataStore *)getDataStoreForMode:(WebDataStoreMode)mode
                             identifierName:(nullable NSString *)identifierName;

/// 创建WebView配置，包含指定的数据存储模式
/// @param mode 数据存储模式
/// @param identifierName 自定义标识符名称 (仅在 WebDataStoreModeCustomPersistent 模式下使用)
- (WKWebViewConfiguration *)createConfigurationWithMode:(WebDataStoreMode)mode
                                         identifierName:(nullable NSString *)identifierName;

/// 清除指定数据存储的所有数据
/// @param mode 数据存储模式
/// @param identifierName 自定义标识符名称 (仅在 WebDataStoreModeCustomPersistent 模式下使用)
/// @param completion 完成回调
- (void)clearDataForMode:(WebDataStoreMode)mode
          identifierName:(nullable NSString *)identifierName
              completion:(nullable void (^)(void))completion;

/// 只清除指定数据存储的Cookie
/// @param mode 数据存储模式
/// @param identifierName 自定义标识符名称 (仅在 WebDataStoreModeCustomPersistent 模式下使用)
/// @param completion 完成回调
- (void)clearCookiesForMode:(WebDataStoreMode)mode
             identifierName:(nullable NSString *)identifierName
                 completion:(nullable void (^)(void))completion;

/// 打印指定数据存储的所有Cookie（调试用）
/// @param mode 数据存储模式
/// @param identifierName 自定义标识符名称 (仅在 WebDataStoreModeCustomPersistent 模式下使用)
- (void)printCookiesForMode:(WebDataStoreMode)mode
             identifierName:(nullable NSString *)identifierName;

@end


NS_ASSUME_NONNULL_END
