//
//  PandaWebView.h
//  PandaBrowser
//
//  Created by qingbin on 2022/3/2.
//

#import <UIKit/UIKit.h>
#import <WebKit/WebKit.h>

#import "PlayModel.h"
#import "WebDataStoreManager.h"

@class ReaderModel;
@class Tab;
@class PlayerView;
@class MessageItemView;

@interface PandaWebView : WKWebView

- (instancetype)initWithConfiguration:(WKWebViewConfiguration*)configuration;

//- (void)removeNonPersistentStore;

- (void)removeAllBrowsingData:(void(^)(void))completeHandler;

// 清空阅读模式和播放模式
- (void)clearWebViewReaderAndPlayer;

// 进入视频模式
- (BOOL)enterPlayViewWithModel:(PlayModel*)item;

// 广告过滤版权控制
- (void)updateRuleListIfNeedWithUrl:(NSURL *)URL;

// v2.6.9 生成pdf
- (void)exportPDF;
// v2.6.9 生成二维码
- (void)exportQRCode;

//调用Tab的loadRequest方法
@property (nonatomic, copy) void (^didLoadRequestBlock)(NSURLRequest* request);
//页面中查找
@property (nonatomic, copy) void (^didFindInBarBlock)(NSString* text);
// 是否需要检测目录列表
@property (nonatomic, assign) BOOL neededCheckBookMenu;
// 目录的url
@property (nonatomic, strong) NSString* bookMenuUrl;
//对应的Tab
@property (nonatomic, weak) Tab* tab;
//是否正处于阅读模式中
@property (nonatomic, assign) BOOL isInReader;

@end


