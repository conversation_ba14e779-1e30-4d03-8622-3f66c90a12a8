//
//  BaseActivity.m
//  PPBrowser
//
//  Created by qing<PERSON> on 2022/6/25.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "BaseActivity.h"

@implementation BaseActivity

//- (NSString *)activityType
//{}

- (NSString *)activityTitle
{
    return self.title;
}

//- (UIImage *)activityImage
//{}

- (void)performActivity
{
    [self activityDidFinish:YES];
    
    if(self.didAction) {
        self.didAction();
    }
}

- (BOOL)canPerformWithActivityItems:(NSArray *)activityItems
{
    if (activityItems.count > 0) {
        return YES;
    }
    
    return NO;
}

//
+ (instancetype)copy
{
    BaseActivity* item = [BaseActivity new];
    item.title = NSLocalizedString(@"activity.copy", nil);
    return item;
}

+ (instancetype)addBookMark
{
    BaseActivity* item = [BaseActivity new];
    item.title = NSLocalizedString(@"activity.addBookmark", nil);
    return item;
}

//+ (instancetype)addUserScript
//{
//    BaseActivity* item = [BaseActivity new];
//    item.title = NSLocalizedString(@"activity.addUserscript", nil);
//    return item;
//}

+ (instancetype)findInPage
{
    BaseActivity* item = [BaseActivity new];
    item.title = NSLocalizedString(@"activity.findInPage", nil);
    return item;
}

+ (instancetype)addToHomePanel
{
    BaseActivity* item = [BaseActivity new];
    item.title = NSLocalizedString(@"addCustomTag.title", nil);
    return item;
}

+ (instancetype)addPopupWindowOption
{
    BaseActivity* item = [BaseActivity new];
    item.title = NSLocalizedString(@"activity.addPopupWindowOption", nil);
    return item;
}

@end
