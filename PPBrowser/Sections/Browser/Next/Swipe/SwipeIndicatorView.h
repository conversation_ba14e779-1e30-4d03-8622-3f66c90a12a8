//
//  SwipeIndicatorView.h
//  PPBrowser
//
//  Created by qingbin on 2025/3/26.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface SwipeIndicatorView : UIView

@property (nonatomic, strong) UIImageView *arrowImageView;
@property (nonatomic, strong) UIView *backgroundView;
@property (nonatomic, assign) BOOL isBackward; // YES为返回，NO为前进

// 更新位置和缩放（带实际位移值）
- (void)updatePositionWithTranslation:(CGFloat)translation maxDistance:(CGFloat)maxDistance;

// 回到初始位置的动画
- (void)animateBackToOrigin;

- (instancetype)initWithFrame:(CGRect)frame isBackward:(BOOL)isBackward;

@end

NS_ASSUME_NONNULL_END
