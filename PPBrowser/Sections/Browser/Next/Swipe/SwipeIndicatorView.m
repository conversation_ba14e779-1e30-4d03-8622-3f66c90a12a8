//
//  SwipeIndicatorView.m
//  PPBrowser
//
//  Created by qingbin on 2025/3/26.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "SwipeIndicatorView.h"

#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "PPNotifications.h"
#import "ThemeProtocol.h"
#import "UIColor+Helper.h"

@interface SwipeIndicatorView()
// 毛玻璃效果视图 - 白天
@property (nonatomic, strong) UIVisualEffectView *blurEffectView;

@end

@implementation SwipeIndicatorView

- (instancetype)initWithFrame:(CGRect)frame isBackward:(BOOL)isBackward {
    self = [super initWithFrame:frame];
    if (self) {
        self.isBackward = isBackward;
        [self setupViews];
        [self handleEvents];
        [self applyTheme];
    }
    return self;
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.arrowImageView.tintColor = [UIColor whiteColor];
        
        self.blurEffectView.hidden = YES;
        self.backgroundView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        self.backgroundView.layer.borderColor = UIColor.clearColor.CGColor;
    } else {
        self.arrowImageView.tintColor = [UIColor blackColor];
        
        self.blurEffectView.hidden = NO;
        self.backgroundView.backgroundColor = UIColor.clearColor;
        self.backgroundView.layer.borderColor = [UIColor colorWithWhite:0.8 alpha:1.0].CGColor;
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}

- (void)handleEvents
{
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(darkThemeDidChangeNotification:)
                                                 name:kDarkThemeDidChangeNotification
                                               object:nil];
}

- (void)setupViews {
    // 背景视图（正方形带圆角）
    self.backgroundView = [[UIView alloc] init];
    self.backgroundView.backgroundColor = [UIColor clearColor]; // 透明背景，用于毛玻璃效果
    self.backgroundView.layer.cornerRadius = 8; // 正方形带圆角
    self.backgroundView.clipsToBounds = YES; // 确保毛玻璃效果不会超出圆角
    
    // 移除阴影，只保留边框
    self.backgroundView.layer.borderWidth = 0.5;
    self.backgroundView.layer.borderColor = [UIColor colorWithWhite:0.8 alpha:1.0].CGColor;
    
    [self addSubview:self.backgroundView];
    
    // 添加毛玻璃效果 - 白天
    UIBlurEffect *blurEffect = [UIBlurEffect effectWithStyle:UIBlurEffectStyleExtraLight];
    self.blurEffectView = [[UIVisualEffectView alloc] initWithEffect:blurEffect];
    [self.backgroundView addSubview:self.blurEffectView];
    
    // 箭头图标
    self.arrowImageView = [[UIImageView alloc] init];
    
    // 使用系统箭头符号
    UIImage *arrowImage = nil;
    NSString *symbolName = self.isBackward ? @"backward_icon" : @"forward_icon";
    arrowImage = [UIImage imageNamed:symbolName];
    arrowImage = [arrowImage imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
    
    self.arrowImageView.image = arrowImage;
    self.arrowImageView.tintColor = [UIColor blackColor]; // 箭头改为黑色，与白色背景搭配
    [self.backgroundView addSubview:self.arrowImageView];
    
    // 设置自动布局
    [self.backgroundView mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.center.equalTo(self);
//        make.size.mas_equalTo(CGSizeMake(50, 50));
        make.edges.equalTo(self);
    }];
    
    [self.blurEffectView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.backgroundView);
    }];
    
    [self.arrowImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.backgroundView);
        make.size.mas_equalTo(CGSizeMake(20, 20));
    }];
    
    // 初始位置在屏幕外
    CGFloat initialOffsetX = self.isBackward ? -60 : 60;
    self.backgroundView.transform = CGAffineTransformMakeTranslation(initialOffsetX, 0);
    
    self.alpha = 0;
}

- (void)updatePositionWithTranslation:(CGFloat)translation maxDistance:(CGFloat)maxDistance {
    // 计算滑动百分比
    CGFloat percentage = fabs(translation) / maxDistance;
    percentage = MIN(1.0, percentage);
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    float alpha = percentage;
    if (isDarkTheme) {
        alpha = MIN(alpha, 0.7);
    }
    // 更新透明度
    self.alpha = alpha;
    
    // 计算水平位移（从屏幕外平移到可见位置）
    CGFloat width = self.backgroundView.frame.size.width;
    CGFloat horizontalOffset = 0;
    
    if (self.isBackward) {
        // 后退指示器（右滑时）- 从左侧屏幕外滑入
        horizontalOffset = -width * (1 - percentage);
    } else {
        // 前进指示器（左滑时）- 从右侧屏幕外滑入
        horizontalOffset = width * (1 - percentage);
    }
    
    // 应用平移变换
    self.backgroundView.transform = CGAffineTransformMakeTranslation(horizontalOffset, 0);
}

- (void)animateBackToOrigin {
    [UIView animateWithDuration:0.3
                     animations:^{
        self.alpha = 0;
        // 回到屏幕外
        CGFloat finalOffsetX = self.isBackward ? -60 : 60;
        self.backgroundView.transform = CGAffineTransformMakeTranslation(finalOffsetX, 0);
    }];
}

@end
