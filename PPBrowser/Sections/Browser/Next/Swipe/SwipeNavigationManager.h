//
//  SwipeNavigationManager.h
//  PPBrowser
//
//  Created by qingbin on 2025/3/26.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "PandaWebView.h"

NS_ASSUME_NONNULL_BEGIN

@class SwipeNavigationManager;

// 滑动导航代理协议
@protocol SwipeNavigationManagerDelegate <NSObject>

@optional
// 是否可以返回上一页
- (BOOL)swipeNavigationCanGoBack:(SwipeNavigationManager *)manager;
// 是否可以前进下一页
- (BOOL)swipeNavigationCanGoForward:(SwipeNavigationManager *)manager;
// 执行返回操作
- (void)swipeNavigationGoBack:(SwipeNavigationManager *)manager;
// 执行前进操作
- (void)swipeNavigationGoForward:(SwipeNavigationManager *)manager;
// 返回当前webView
- (PandaWebView *)swipeNavigationGetCurrentWebView;

@end

// 滑动导航管理器
@interface SwipeNavigationManager : NSObject <UIGestureRecognizerDelegate>

// 初始化方法
- (instancetype)initWithHostView:(UIView *)hostView delegate:(id<SwipeNavigationManagerDelegate>)delegate;

// 清理资源
- (void)cleanup;

// 启用/禁用滑动手势
- (void)setSwipeGestureEnabled:(BOOL)enabled;

// 应用主题（深色/浅色模式）
- (void)applyTheme:(BOOL)isDarkTheme;

// 宿主视图
@property (nonatomic, weak) UIView *hostView;

// 代理
@property (nonatomic, weak) id<SwipeNavigationManagerDelegate> delegate;

// 是否启用滑动手势
@property (nonatomic, assign) BOOL swipeGestureEnabled;

// 最大滑动距离
@property (nonatomic, assign) CGFloat maxSwipeDistance;

@end

NS_ASSUME_NONNULL_END 
