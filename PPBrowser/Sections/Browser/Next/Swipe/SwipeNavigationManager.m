//
//  SwipeNavigationManager.m
//  PPBrowser
//
//  Created by qingbin on 2025/3/26.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "SwipeNavigationManager.h"
#import "SwipeIndicatorView.h"
#import "Masonry.h"
#import "ThemeProtocol.h"
#import "PreferenceManager.h"
#import "PlayerView.h"
#import "PPNotifications.h"
#import "BottomToolbar.h"
#import "TopToolbar.h"
#import "TopToolbarForiPad.h"
#import "FindInPageBar.h"
#import "SearchListView.h"

@interface SwipeNavigationManager()

// 滑动手势相关属性
@property (nonatomic, strong) UIPanGestureRecognizer *swipeGestureRecognizer;
@property (nonatomic, strong) SwipeIndicatorView *backwardIndicatorView; // 后退指示器
@property (nonatomic, strong) SwipeIndicatorView *forwardIndicatorView;  // 前进指示器
@property (nonatomic, assign) CGFloat lastTranslationX;  // 上一次的横向偏移量
@property (nonatomic, assign) BOOL isHorizontalSwipe;    // 是否是水平滑动
@property (nonatomic, assign) BOOL isHandling; //正在动画中，防止重复操作
@property (nonatomic, assign) CGPoint touchPoint; //点击的位置
@property (nonatomic, assign) BOOL isAtHomePage; //是否在首页
@property (nonatomic, weak) PandaWebView *webView;
@property (nonatomic, assign) BOOL canTriggerSwipeGesture; //是否当前元素是否允许触发滑动手势

@end

@implementation SwipeNavigationManager

#pragma mark - 初始化方法

- (instancetype)initWithHostView:(UIView *)hostView delegate:(id<SwipeNavigationManagerDelegate>)delegate {
    self = [super init];
    if (self) {
        _hostView = hostView;
        _delegate = delegate;
        _swipeGestureEnabled = YES;
        _isHandling = NO;
        _maxSwipeDistance = 60; // 默认最大滑动距离
        
        [self handleEvents];
        
        BOOL enableGestureHaptics = [[PreferenceManager shareInstance].items.enableGestureHaptics boolValue];
        if (enableGestureHaptics) {
            [self setupSwipeGesture];
        } else {
            [self cleanup];
        }
    }
    return self;
}

#pragma mark - 公共方法

- (void)cleanup {
    // 移除手势识别器
    if (_swipeGestureRecognizer) {
        [_hostView removeGestureRecognizer:_swipeGestureRecognizer];
        _swipeGestureRecognizer = nil;
    }
    
    // 移除滑块视图
    [_backwardIndicatorView removeFromSuperview];
    [_forwardIndicatorView removeFromSuperview];
    _backwardIndicatorView = nil;
    _forwardIndicatorView = nil;
}

- (void)setSwipeGestureEnabled:(BOOL)enabled {
    _swipeGestureEnabled = enabled;
    _swipeGestureRecognizer.enabled = enabled;
}

- (void)applyTheme:(BOOL)isDarkTheme {
    // 如果需要针对不同主题调整滑块样式，可以在这里实现
}

#pragma mark - 私有方法

- (void)handleEvents
{
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(handleUpdateSwipeGesutre) name:kUpdateSwipeGesutreNotification object:nil];
}

- (void)handleUpdateSwipeGesutre
{
    BOOL enableSwipeNavigation = [[PreferenceManager shareInstance].items.enableSwipeNavigation boolValue];
    if (enableSwipeNavigation) {
        [self setupSwipeGesture];
    } else {
        [self cleanup];
    }
}

- (void)setupSwipeGesture {
    float indicatorWidth = 60;
    
    // 创建滑动手势识别器
    self.swipeGestureRecognizer = [[UIPanGestureRecognizer alloc] initWithTarget:self action:@selector(handleSwipeGesture:)];
    self.swipeGestureRecognizer.delegate = self;
    [self.hostView addGestureRecognizer:self.swipeGestureRecognizer];
    
    // 创建后退指示器（左侧）
    self.backwardIndicatorView = [[SwipeIndicatorView alloc] initWithFrame:CGRectMake(0, 0, indicatorWidth, indicatorWidth) isBackward:YES];
    [self.hostView addSubview:self.backwardIndicatorView];
    
    // 创建前进指示器（右侧）
    self.forwardIndicatorView = [[SwipeIndicatorView alloc] initWithFrame:CGRectMake(0, 0, indicatorWidth, indicatorWidth) isBackward:NO];
    [self.hostView addSubview:self.forwardIndicatorView];
    
    // 设置指示器位置
    [self.backwardIndicatorView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.hostView); // 无偏移
        make.centerY.equalTo(self.hostView);
        make.size.mas_equalTo(CGSizeMake(indicatorWidth, indicatorWidth)); // 正方形大小
    }];
    
    [self.forwardIndicatorView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.hostView); // 无偏移
        make.centerY.equalTo(self.hostView);
        make.size.mas_equalTo(CGSizeMake(indicatorWidth, indicatorWidth)); // 正方形大小
    }];
    
    // 确保滑块初始状态不可见
    self.backwardIndicatorView.alpha = 0;
    self.forwardIndicatorView.alpha = 0;
}

- (void)handleSwipeGesture:(UIPanGestureRecognizer *)gestureRecognizer {
    if (!self.swipeGestureEnabled) return;
    
    // 获取当前的水平移动距离
    CGPoint translation = [gestureRecognizer translationInView:self.hostView];
    CGFloat currentTranslationX = translation.x;
    
    // 根据手势状态处理
    switch (gestureRecognizer.state) {
        case UIGestureRecognizerStateBegan: {
            //判断是否开启
            BOOL enableSwipeNavigation = [[PreferenceManager shareInstance].items.enableSwipeNavigation boolValue];
            if (!enableSwipeNavigation) return;
            
            // 记录初始状态
            self.lastTranslationX = 0;
            self.isHorizontalSwipe = NO;
            self.isHandling = NO;
            
            [self.hostView bringSubviewToFront:self.backwardIndicatorView];
            [self.hostView bringSubviewToFront:self.forwardIndicatorView];
            
            // 在手势开始时，判断是否是水平滑动
            CGPoint velocity = [gestureRecognizer velocityInView:self.hostView];
            if (fabs(velocity.x) > fabs(velocity.y)) {
                self.isHorizontalSwipe = YES;
            }
            
            if (self.isHorizontalSwipe) {
                if (self.isAtHomePage) {
                    //首页，不需要判断网页元素
                    self.canTriggerSwipeGesture = true;
                } else {
                    //网页，等待检测网页元素类型
                    NSString* js = [NSString stringWithFormat:@"window.__firefox__.swipeHelper.shouldIgnoreTouch('%f', '%f')", self.touchPoint.x, self.touchPoint.y];
                    @weakify(self)
                    [self.webView evaluateJavaScript:js completionHandler:^(id result, NSError *error) {
                        @strongify(self)
                        if (!error && [result isKindOfClass:[NSNumber class]]) {
                            self.canTriggerSwipeGesture = ![result boolValue];
                        }
                    }];
                }
            }
            
            break;
        }
            
        case UIGestureRecognizerStateChanged: {
            //判断是否开启
            BOOL enableSwipeNavigation = [[PreferenceManager shareInstance].items.enableSwipeNavigation boolValue];
            if (!enableSwipeNavigation) return;
            // 如果不是水平滑动，忽略后续操作
            if (!self.isHorizontalSwipe) return;
            // 等待判断网页元素
            if (!self.canTriggerSwipeGesture) return;
            
            // 计算移动百分比，用于检测是否刚达到最大距离
            CGFloat previousPercentage = fabs(self.lastTranslationX) / self.maxSwipeDistance;
            CGFloat currentPercentage = fabs(currentTranslationX) / self.maxSwipeDistance;
            
            if (currentTranslationX > 0) {  // 右滑 -> 返回上一页
                // 检查是否可以后退
                BOOL canGoBack = [self.delegate respondsToSelector:@selector(swipeNavigationCanGoBack:)] ? 
                                [self.delegate swipeNavigationCanGoBack:self] : NO;
                
                if (canGoBack) {
                    // 更新位置，传入实际的平移值
                    [self.backwardIndicatorView updatePositionWithTranslation:currentTranslationX maxDistance:self.maxSwipeDistance];
                    
                    // 检测是否刚刚达到阈值，如果是则触发震动反馈
                    if (previousPercentage < 1.0 && currentPercentage >= 1.0) {
                        [self triggerFeedback];
                    }
                }
                // 确保另一个指示器隐藏
                [self.forwardIndicatorView animateBackToOrigin];
            } else if (currentTranslationX < 0) {  // 左滑 -> 前进下一页
                // 检查是否可以前进
                BOOL canGoForward = [self.delegate respondsToSelector:@selector(swipeNavigationCanGoForward:)] ? 
                                   [self.delegate swipeNavigationCanGoForward:self] : NO;
                
                if (canGoForward) {
                    // 更新位置，传入实际的平移值
                    [self.forwardIndicatorView updatePositionWithTranslation:currentTranslationX maxDistance:self.maxSwipeDistance];
                    
                    // 检测是否刚刚达到阈值，如果是则触发震动反馈
                    if (previousPercentage < 1.0 && currentPercentage >= 1.0) {
                        [self triggerFeedback];
                    }
                }
                // 确保另一个指示器隐藏
                [self.backwardIndicatorView animateBackToOrigin];
            }
            
            self.lastTranslationX = currentTranslationX;
            break;
        }
            
        case UIGestureRecognizerStateEnded:
        case UIGestureRecognizerStateCancelled: {
            //判断是否开启
            BOOL enableSwipeNavigation = [[PreferenceManager shareInstance].items.enableSwipeNavigation boolValue];
            if (!enableSwipeNavigation) return;
            // 如果不是水平滑动，忽略后续操作
            if (!self.isHorizontalSwipe) return;
            // 等待判断网页元素
            if (!self.canTriggerSwipeGesture) return;
            
            // 计算移动百分比
            CGFloat translationPercentage = fabs(self.lastTranslationX) / self.maxSwipeDistance;
            
            // 如果滑动距离超过阈值，触发导航操作
            if (translationPercentage >= 1.0) {
                if (self.lastTranslationX > 0) {  // 右滑 -> 返回上一页
                    BOOL canGoBack = [self.delegate respondsToSelector:@selector(swipeNavigationCanGoBack:)] ? 
                                    [self.delegate swipeNavigationCanGoBack:self] : NO;
                    
                    if (canGoBack) {
                        if (self.isHandling) return;
                        self.isHandling = YES;
                        // 利用动画实现平滑过渡，然后执行返回操作
                        [UIView animateWithDuration:0.15 animations:^{
                            // 显示完全滑入的效果
                            BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
                            self.backwardIndicatorView.alpha = isDarkTheme ? 0.7 : 1.0;
                            self.backwardIndicatorView.backgroundView.transform = CGAffineTransformIdentity;
                        } completion:^(BOOL finished) {
                            // 完成后执行返回并隐藏滑块
                            if ([self.delegate respondsToSelector:@selector(swipeNavigationGoBack:)]) {
                                [self.delegate swipeNavigationGoBack:self];
                            }
                            [self.backwardIndicatorView animateBackToOrigin];
                        }];
                        
                        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                            //重置
                            self.isHandling = NO;
                        });
                    }
                } else if (self.lastTranslationX < 0) {  // 左滑 -> 前进下一页
                    BOOL canGoForward = [self.delegate respondsToSelector:@selector(swipeNavigationCanGoForward:)] ? 
                                       [self.delegate swipeNavigationCanGoForward:self] : NO;
                    
                    if (canGoForward) {
                        if (self.isHandling) return;
                        self.isHandling = YES;
                        // 利用动画实现平滑过渡，然后执行前进操作
                        [UIView animateWithDuration:0.15 animations:^{
                            // 显示完全滑入的效果
                            BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
                            self.forwardIndicatorView.alpha = isDarkTheme ? 0.7 : 1.0;
                            self.forwardIndicatorView.backgroundView.transform = CGAffineTransformIdentity;
                        } completion:^(BOOL finished) {
                            // 完成后执行前进并隐藏滑块
                            if ([self.delegate respondsToSelector:@selector(swipeNavigationGoForward:)]) {
                                [self.delegate swipeNavigationGoForward:self];
                            }
                            [self.forwardIndicatorView animateBackToOrigin];
                        }];
                        
                        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                            //重置
                            self.isHandling = NO;
                        });
                    }
                }
            } else {
                // 没有达到阈值，还原指示器
                [self.backwardIndicatorView animateBackToOrigin];
                [self.forwardIndicatorView animateBackToOrigin];
            }
            
            break;
        }
            
        default:
            break;
    }
}

// 触发震动反馈
- (void)triggerFeedback
{
    BOOL enableGestureHaptics = [[PreferenceManager shareInstance].items.enableGestureHaptics boolValue];
    if (!enableGestureHaptics) return;
    
    UIImpactFeedbackGenerator *feedbackGenerator = [[UIImpactFeedbackGenerator alloc] initWithStyle:UIImpactFeedbackStyleSoft];
    [feedbackGenerator prepare];
    [feedbackGenerator impactOccurred];
}

#pragma mark - UIGestureRecognizerDelegate

- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldReceiveTouch:(UITouch *)touch {
    //判断是否开启
    BOOL enableSwipeNavigation = [[PreferenceManager shareInstance].items.enableSwipeNavigation boolValue];
    // 如果没有开启，那么也不响应手势
    if (!enableSwipeNavigation) return NO;
    
    // 如果触摸发生在 PlayerView 上，不响应手势
    UIView *touchView = touch.view;
    while (touchView) {
        if ([touchView isKindOfClass:[PlayerView class]]
            || [touchView isKindOfClass:[BottomToolbar class]]
            || [touchView isKindOfClass:[TopToolbarForiPad class]]
            || [touchView isKindOfClass:[FindInPageBar class]]
            || [touchView isKindOfClass:[SearchListView class]]
            ) {
            return NO;
        }
        touchView = touchView.superview;
    }
    
    PandaWebView* webView = nil;
    if (self.delegate && [self.delegate respondsToSelector:@selector(swipeNavigationGetCurrentWebView)]) {
        webView = [self.delegate swipeNavigationGetCurrentWebView];
    }
    
    if (webView) {
        self.isAtHomePage = NO;
        self.touchPoint = [touch locationInView:webView];
    } else {
        self.isAtHomePage = YES;
    }
    
    self.webView = webView;
     
    return YES;
}

- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldRecognizeSimultaneouslyWithGestureRecognizer:(UIGestureRecognizer *)otherGestureRecognizer {
    // 允许与宿主视图的内部手势共存
    return YES;
}

- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldBeRequiredToFailByGestureRecognizer:(UIGestureRecognizer *)otherGestureRecognizer {
    // 确保我们的滑动手势不会干扰宿主视图的其他手势
    if (gestureRecognizer == self.swipeGestureRecognizer) {
        return NO;
    }
    return NO;
}

#pragma mark -- 从bundle中加载文件

- (NSString *)_loadJsFromBundleWithFileName:(NSString *)scriptFileName
{
    NSString* path = [[NSBundle mainBundle]pathForResource:scriptFileName ofType:@"js"];
    NSError* error = nil;
    NSString* source = [NSString stringWithContentsOfFile:path encoding:NSUTF8StringEncoding error:&error];
    if(error) {
        NSLog(@"error = %@", error.localizedDescription);
    }

    return source;
}

@end
