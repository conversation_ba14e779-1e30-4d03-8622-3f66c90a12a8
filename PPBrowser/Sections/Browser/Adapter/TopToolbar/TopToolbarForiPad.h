//
//  TopToolbarForiPad.h
//  PPBrowser
//
//  Created by qingbin on 2022/10/5.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "TopToolbar.h"
#import "BrowserUtils.h"

@class CustomTitleAndImageView;

// iPad对应的顶部工具栏
@interface TopToolbarForiPad : UIView<ThemeProtocol,TopToolBarProtocol>

@property(nonatomic,weak) id<TopToolbarDelegate> delegate;
@property(nonatomic,strong) NSString* currentURL;

@property (readonly) CustomTitleAndImageView* textFieldLeftView;

- (void)updateProgressBar:(float)progress;
- (void)hideProgressBar;

- (void)updateLoadingStatus:(BOOL)isLoading;

- (void)show:(BOOL)animated;
- (void)dismiss:(BOOL)animated;

- (void)showSearchBar;
- (void)hideSearchBar;

- (void)updateCurrentURL:(NSString *)url;
- (void)updateCurrentTitle:(NSString *)title;

// 工具栏屏幕旋转适配
- (void)updateToolBarLayoutsToSize:(CGSize)size;

//逻辑与BottomToolbar一致
- (void)updateGoBackStatus:(BOOL)canGoBack;
- (void)updateGoForwardStatus:(BOOL)canGoForward;

//更新标签页的个数
- (void)updateTabCount:(int)count;

// 2.7.6 更新阅读模式/脚本管理的状态
- (void)updateIsInReader:(BOOL)isInReader;

// 控件高度
+ (float)toolbarHeight;
// 35+5, 真正滑动的高度
+ (float)topScrollHeight;

@end
