//
//  TopToolbarForiPad.m
//  PPBrowser
//
//  Created by qingbin on 2022/10/5.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "TopToolbarForiPad.h"

#import "MaizyHeader.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"
#import "CustomTextField.h"

#import "UIColor+Helper.h"
#import "NSObject+Helper.h"

#import "GradientProgressBar.h"
#import "SearchSuggestionController.h"
#import "PPNotifications.h"
#import "TabsButton.h"
#import "CustomTitleAndImageView.h"

@interface TopToolbarForiPad ()<UITextFieldDelegate>

@property (nonatomic, strong) NSMutableArray* actionButtons;

@property (nonatomic, strong) UIView* contentView;

@property (nonatomic, strong) CustomTitleAndImageView* backButton;
@property (nonatomic, strong) CustomTitleAndImageView* forwardButton;
@property (nonatomic, strong) CustomTitleAndImageView* addNewTabButton;

@property (nonatomic, strong) CustomTextField* textField;
@property (nonatomic, strong) CustomTitleAndImageView* refreshButton;
@property (nonatomic, strong) CustomTitleAndImageView* textFieldLeftView;

@property (nonatomic, strong) CustomTitleAndImageView* appMenuButton;
@property (nonatomic, strong) TabsButton* tabsButton;
@property (nonatomic, strong) CustomTitleAndImageView* homeButton;

@property (nonatomic, weak) UIButton* scanBtn;

@property (nonatomic, strong) GradientProgressBar* progressBar;

@property (nonatomic, assign) BOOL isLoading;

@property (nonatomic, assign) BOOL canGoBack;

@property (nonatomic, assign) BOOL canGoForward;
//2.7.6 当前网页是否处于阅读模式
@property (nonatomic, assign) BOOL isInReader;

@end

@implementation TopToolbarForiPad

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if(self) {        
        [self addSubviews];
        [self defineLayout];
        [self handleEvents];
        
        self.backgroundColor = UIColor.whiteColor;
        self.isLoading = NO;
        
        //先隐藏搜索框
        [self hideSearchBar];
        
        [self applyTheme];
    }
    
    return self;
}


// 工具栏屏幕旋转适配
- (void)updateToolBarLayoutsToSize:(CGSize)size
{
    //更新编辑框右边视图约束
    [self updateWithTextField:self.textField width:size.width];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        
        self.textField.backgroundColor = [UIColor colorWithHexString:kDarkThemeColorTextFiledBackgroundColor];
        self.textField.textColor = UIColor.whiteColor;
        [self.textField updatePlaceHolder:NSLocalizedString(@"textfiled.placeholder", nil) color:[UIColor colorWithHexString:kDarkThemeColorTextFiledContent]];

        [self.textFieldLeftView updateWith:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            imageView.tintColor = UIColor.whiteColor;
        }];
        
        for(UIButton* button in self.actionButtons) {
            button.imageView.tintColor = [UIColor colorWithHexString:@"#ffffff"];
        }
        
        self.scanBtn.imageView.tintColor = [UIColor colorWithHexString:@"#ffffff"];
    } else {
        self.backgroundColor = UIColor.whiteColor;
        
        self.textField.textColor = [UIColor colorWithHexString:@"#333333"];
        
        [self.textFieldLeftView updateWith:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            imageView.tintColor = [UIColor colorWithHexString:@"#333333"];
        }];
        
        [self.textField setBackgroundColor:[UIColor colorWithHexString:@"#f0f0f4"]];
        [self.textField updatePlaceHolder:NSLocalizedString(@"textfiled.placeholder", nil) color:[UIColor colorWithHexString:@"#999999"]];
        
        for(UIButton* button in self.actionButtons) {
            button.imageView.tintColor = [UIColor colorWithHexString:@"#333333"];
        }
        
        self.scanBtn.imageView.tintColor = [UIColor colorWithHexString:@"#333333"];
    }
    
    [self.tabsButton applyTheme:isDarkTheme];
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}

- (void)updateCurrentURL:(NSString*)url
{
    self.currentURL = url;
    
    self.textField.text = [[[[NSURL URLWithString:url] withoutWWW] schemelessAbsoluteString] trim:@"/"];
}

- (void)updateCurrentTitle:(NSString *)title
{
    if (title.length > 0) {
        self.textField.text = title;
    }
}


- (void)updateLoadingStatus:(BOOL)isLoading
{
    self.isLoading = isLoading;
    if(!isLoading) {
        UIImage* image = [UIImage imageNamed:@"navc_refresh"];
        image = [image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
                
        [self.refreshButton updateWith:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            imageView.image = image;
        }];
    } else {
        UIImage* image = [UIImage imageNamed:@"navc_stop_refresh"];
        image = [image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        
        [self.refreshButton updateWith:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            imageView.image = image;
        }];
    }
}

- (void)updateGoBackStatus:(BOOL)canGoBack
{
    //只有没有设置壁纸的情况下才能使用暗黑模式
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    
    self.canGoBack = canGoBack;
//    self.backButton.userInteractionEnabled = canGoBack;
    
    if(canGoBack) {
        if(isDarkTheme) {
            self.backButton.imageView.tintColor = [UIColor colorWithHexString:@"#ffffff"];
        } else {
            self.backButton.imageView.tintColor = [UIColor colorWithHexString:@"#333333"];
        }
    } else {
        self.backButton.imageView.tintColor = [UIColor colorWithHexString:@"#999999"];
    }
}

- (void)updateGoForwardStatus:(BOOL)canGoForward
{
    //只有没有设置壁纸的情况下才能使用暗黑模式
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    
    self.canGoForward = canGoForward;
//    self.forwardButton.userInteractionEnabled = canGoForward;
    
    if(canGoForward) {
        if(isDarkTheme) {
            self.forwardButton.imageView.tintColor = [UIColor colorWithHexString:@"#ffffff"];
        } else {
            self.forwardButton.imageView.tintColor = [UIColor colorWithHexString:@"#333333"];
        }
    } else {
        self.forwardButton.imageView.tintColor = [UIColor colorWithHexString:@"#999999"];
    }
}

//更新标签页的个数
- (void)updateTabCount:(int)count
{
    count = MAX(0, count);
    [BrowserUtils shareInstance].allTabsCount = count;
    
    [self.tabsButton updateTabCount:count];
}

// 2.7.6 更新阅读模式/脚本管理的状态
- (void)updateIsInReader:(BOOL)isInReader
{
    self.isInReader = isInReader;
    
    if (isInReader) {
        //阅读模式
        self.textFieldLeftView.imageView.image = [[UIImage imageNamed:@"top_book_icon"] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
    } else {
        //正常模式
        self.textFieldLeftView.imageView.image = [[UIImage imageNamed:@"top_script_icon"] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
    }
}

- (void)show:(BOOL)animated
{
    if(animated) {
        [self mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.mas_offset(0).priorityHigh();
        }];
        
        [UIView animateWithDuration:0.3 animations:^{
            [self layoutIfNeeded];
        } completion:^(BOOL finished) {
            [self showSearchBar];
        }];
    } else {
        [self mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.mas_offset(0).priorityHigh();
        }];
        [self showSearchBar];
    }
}

- (void)dismiss:(BOOL)animated
{
    [self hideSearchBar];

    if(animated) {
        [self mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.mas_offset(-[TopToolbarForiPad toolbarHeight]).priorityHigh();
        }];
        
        [UIView animateWithDuration:0.3 animations:^{
            [self layoutIfNeeded];
        } completion:^(BOOL finished) {
        }];
    } else {
        [self mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.mas_offset(-[TopToolbarForiPad toolbarHeight]).priorityHigh();
        }];
    }
}

- (void)showSearchBar
{
    self.textField.hidden = NO;
    self.contentView.hidden = NO;
}

- (void)hideSearchBar
{
    self.textField.hidden = YES;
    self.contentView.hidden = YES;
}

#pragma mark -- UITextFieldDelegate
- (BOOL)textFieldShouldBeginEditing:(UITextField *)textField
{
    if(self.delegate && [self.delegate respondsToSelector:@selector(topToolbarDidSearch)]) {
        [self.delegate topToolbarDidSearch];
    }
    
    return NO;
}

#pragma mark -- 控件高度
+ (float)toolbarHeight
{
    float topOffset = [self topOffset];
    return 10/*状态栏高度*/ + 50 + topOffset;
}

+ (float)topOffset
{
    UIWindow* window = YBIBNormalWindow();
    float topOffset = window.safeAreaInsets.top;
    return topOffset;
}

+ (float)topScrollHeight
{
    return [self toolbarHeight] - [self topOffset];
}

- (void)updateProgressBar:(float)progress
{
    self.progressBar.alpha = 1;
    self.progressBar.hidden = NO;
    [self.progressBar setProgress:progress animated:YES];
}

- (void)hideProgressBar
{
    self.progressBar.hidden = YES;
    [self.progressBar setProgress:0 animated:YES];
}

- (void)addSubviews
{
    [self addSubview:self.contentView];
    [self.contentView addSubview:self.backButton];
    [self.contentView addSubview:self.forwardButton];
    [self.contentView addSubview:self.addNewTabButton];
    [self.contentView addSubview:self.textField];
    [self.contentView addSubview:self.homeButton];
    [self.contentView addSubview:self.tabsButton];
    [self.contentView addSubview:self.appMenuButton];
    [self addSubview:self.progressBar];
}

- (void)defineLayout
{
    float ButtonHeight = 50;
    float Padding = 15;
    float margin = 10;
    
    [self.contentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.equalTo(self);
        make.height.mas_equalTo(50);
        make.bottom.mas_offset(-5);
    }];
    
    [self.backButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.mas_safeAreaLayoutGuideLeading).offset(Padding);
        make.centerY.equalTo(self.contentView);
        make.size.mas_equalTo(ButtonHeight);
    }];
    
    [self.forwardButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.backButton.mas_trailing).offset(margin);
        make.centerY.equalTo(self.contentView);
        make.size.mas_equalTo(ButtonHeight);
    }];
    
    [self.addNewTabButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.forwardButton.mas_trailing).offset(margin);
        make.centerY.equalTo(self.contentView);
        make.size.mas_equalTo(ButtonHeight);
    }];
    
    [self.textField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.leading.equalTo(self.addNewTabButton.mas_trailing).offset(margin);
        make.height.mas_equalTo(40);
    }];
    
    [self.appMenuButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.textField.mas_trailing).offset(margin);
        make.centerY.equalTo(self.contentView);
        make.size.mas_equalTo(ButtonHeight);
    }];
    
    [self.tabsButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.appMenuButton.mas_trailing).offset(margin);
        make.centerY.equalTo(self.contentView);
        make.size.mas_equalTo(ButtonHeight);
    }];
        
    [self.homeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.tabsButton.mas_trailing).offset(margin);
        make.trailing.equalTo(self.mas_safeAreaLayoutGuideTrailing).offset(-Padding);
        make.centerY.equalTo(self.contentView);
        make.size.mas_equalTo(ButtonHeight);
    }];
    
    [self.progressBar mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self);
        make.bottom.equalTo(self).offset(1.5/2.0);
        make.height.mas_equalTo(1.5);
    }];
}

#pragma mark - handle events

- (void)handleEvents
{
    @weakify(self)
    [self.refreshButton setTapAction:^{
        @strongify(self)
        if(!self.isLoading) {
            if(self.delegate && [self.delegate respondsToSelector:@selector(topToolbarDidReload)]) {
                [self.delegate topToolbarDidReload];
            }
        } else {
            if(self.delegate && [self.delegate respondsToSelector:@selector(topToolbarDidCancelReload)]) {
                [self.delegate topToolbarDidCancelReload];
            }
        }
    }];
    
    //iPad
    [self.backButton setTapAction:^{
        @strongify(self)
        if (!self.canGoBack) return;
        if(self.delegate && [self.delegate respondsToSelector:@selector(topToolbarDidClickBack)]) {
            [self.delegate topToolbarDidClickBack];
        }
    }];
    
    // 添加返回按钮长按手势
    UILongPressGestureRecognizer* backLongPress = [[UILongPressGestureRecognizer alloc] init];
    [self.backButton addGestureRecognizer:backLongPress];
    [self.backButton.tapGesture requireGestureRecognizerToFail:backLongPress];
    [[backLongPress rac_gestureSignal] subscribeNext:^(UILongPressGestureRecognizer *gesture) {
        @strongify(self)
        if (gesture.state == UIGestureRecognizerStateBegan) {
            if(self.delegate && [self.delegate respondsToSelector:@selector(topToolbarDidLongPress:forToolbarGroup:)]) {
                [self.delegate topToolbarDidLongPress:self.backButton forToolbarGroup:ToolbarGroupBack];
            }
        }
    }];
    
    [self.forwardButton setTapAction:^{
        @strongify(self)
        if (!self.canGoForward) return;
        if(self.delegate && [self.delegate respondsToSelector:@selector(topToolbarDidClickForward)]) {
            [self.delegate topToolbarDidClickForward];
        }
    }];
    
    // 添加前进按钮长按手势
    UILongPressGestureRecognizer* forwardLongPress = [[UILongPressGestureRecognizer alloc] init];
    [self.forwardButton addGestureRecognizer:forwardLongPress];
    [self.forwardButton.tapGesture requireGestureRecognizerToFail:forwardLongPress];
    [[forwardLongPress rac_gestureSignal] subscribeNext:^(UILongPressGestureRecognizer *gesture) {
        @strongify(self)
        if (gesture.state == UIGestureRecognizerStateBegan) {
            if(self.delegate && [self.delegate respondsToSelector:@selector(topToolbarDidLongPress:forToolbarGroup:)]) {
                [self.delegate topToolbarDidLongPress:self.forwardButton forToolbarGroup:ToolbarGroupForward];
            }
        }
    }];
    
    [self.addNewTabButton setTapAction:^{
        @strongify(self)
        //增加一个tab
        [BrowserUtils shareInstance].allTabsCount++;
        [self updateTabCount:[BrowserUtils shareInstance].allTabsCount];
        
        if(self.delegate && [self.delegate respondsToSelector:@selector(topToolbarDidClickAddTab)]) {
            [self.delegate topToolbarDidClickAddTab];
        }
    }];
    
    // 添加新建按钮长按手势
    UILongPressGestureRecognizer* addTabLongPress = [[UILongPressGestureRecognizer alloc] init];
    [self.addNewTabButton addGestureRecognizer:addTabLongPress];
    [self.addNewTabButton.tapGesture requireGestureRecognizerToFail:addTabLongPress];
    [[addTabLongPress rac_gestureSignal] subscribeNext:^(UILongPressGestureRecognizer *gesture) {
        @strongify(self)
        if (gesture.state == UIGestureRecognizerStateBegan) {
            if(self.delegate && [self.delegate respondsToSelector:@selector(topToolbarDidLongPress:forToolbarGroup:)]) {
                [self.delegate topToolbarDidLongPress:self.addNewTabButton forToolbarGroup:ToolbarGroupNew];
            }
        }
    }];
    
//    [[self.tabsButton rac_signalForControlEvents:UIControlEventTouchUpInside]
//    subscribeNext:^(id x) {
//        @strongify(self)
//        if(self.delegate && [self.delegate respondsToSelector:@selector(topToolbarDidClickTabs)]) {
//            [self.delegate topToolbarDidClickTabs];
//        }
//    }];
    UILongPressGestureRecognizer* longPress = [UILongPressGestureRecognizer new];
    [self.tabsButton addGestureRecognizer:longPress];
    
    UITapGestureRecognizer* tap = [UITapGestureRecognizer new];
    [self.tabsButton addGestureRecognizer:tap];
    [tap requireGestureRecognizerToFail:longPress];
    
    [[longPress rac_gestureSignal] subscribeNext:^(UILongPressGestureRecognizer *gesture) {
        @strongify(self)
        if (gesture.state == UIGestureRecognizerStateBegan) {
            if(self.delegate && [self.delegate respondsToSelector:@selector(topToolbarDidLongPress:forToolbarGroup:)]) {
                [self.delegate topToolbarDidLongPress:self.tabsButton forToolbarGroup:ToolbarGroupTabs];
            }
        }
    }];
    
    [[tap rac_gestureSignal] subscribeNext:^(id x) {
        @strongify(self)
        if(self.delegate && [self.delegate respondsToSelector:@selector(topToolbarDidClickTabs)]) {
            [self.delegate topToolbarDidClickTabs];
        }
    }];
    
    [self.appMenuButton setTapAction:^{
        @strongify(self)
        if(self.delegate && [self.delegate respondsToSelector:@selector(topToolbarDidClickMenu)]) {
            [self.delegate topToolbarDidClickMenu];
        }
    }];
    
    // 添加功能按钮长按手势
    UILongPressGestureRecognizer* menuLongPress = [[UILongPressGestureRecognizer alloc] init];
    [self.appMenuButton addGestureRecognizer:menuLongPress];
    [self.appMenuButton.tapGesture requireGestureRecognizerToFail:menuLongPress];
    [[menuLongPress rac_gestureSignal] subscribeNext:^(UILongPressGestureRecognizer *gesture) {
        @strongify(self)
        if (gesture.state == UIGestureRecognizerStateBegan) {
            if(self.delegate && [self.delegate respondsToSelector:@selector(topToolbarDidLongPress:forToolbarGroup:)]) {
                [self.delegate topToolbarDidLongPress:self.appMenuButton forToolbarGroup:ToolbarGroupFeatures];
            }
        }
    }];

    [self.homeButton setTapAction:^{
        @strongify(self)
        if(self.delegate && [self.delegate respondsToSelector:@selector(topToolbarDidClickHome)]) {
            [self.delegate topToolbarDidClickHome];
        }
    }];
    
    // 添加主页按钮长按手势
    UILongPressGestureRecognizer* homeLongPress = [[UILongPressGestureRecognizer alloc] init];
    [self.homeButton addGestureRecognizer:homeLongPress];
    [self.homeButton.tapGesture requireGestureRecognizerToFail:homeLongPress];
    [[homeLongPress rac_gestureSignal] subscribeNext:^(UILongPressGestureRecognizer *gesture) {
        @strongify(self)
        if (gesture.state == UIGestureRecognizerStateBegan) {
            if(self.delegate && [self.delegate respondsToSelector:@selector(topToolbarDidLongPress:forToolbarGroup:)]) {
                [self.delegate topToolbarDidLongPress:self.homeButton forToolbarGroup:ToolbarGroupHome];
            }
        }
    }];
    
    // 进入阅读模式
    [[[NSNotificationCenter defaultCenter] rac_addObserverForName:kEnterReaderNotification object:nil]
        subscribeNext:^(id x) {
        @strongify(self)
        [self updateIsInReader:YES];
    }];
    
    // 退出阅读模式
    [[[NSNotificationCenter defaultCenter] rac_addObserverForName:kExitReaderNotification object:nil]
        subscribeNext:^(id x) {
        @strongify(self)
        [self updateIsInReader:NO];
    }];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(darkThemeDidChangeNotification:)
                                                 name:kDarkThemeDidChangeNotification
                                               object:nil];
}

- (UIView *)contentView
{
    if(!_contentView) {
        _contentView = [UIView new];
        _contentView.backgroundColor = UIColor.clearColor;
    }
    
    return _contentView;
}

- (CustomTextField *)textField
{
    if(!_textField) {
        float height = 40;
        CustomTextField* textField = [[CustomTextField alloc] init];
        textField.font = [UIFont systemFontOfSize:iPadValue(22, 18)];
        textField.layer.cornerRadius = 5;
        textField.layer.masksToBounds = YES;
        //2.6.8 阿拉伯语布局适配
        textField.textAlignment = NSTextAlignmentLeft;
        
        textField.leftViewMode = UITextFieldViewModeAlways;
        textField.textColor = [UIColor colorWithHexString:@"#333333"];
        textField.returnKeyType = UIReturnKeySearch;
        
        [textField setBackgroundColor:[UIColor colorWithHexString:@"#f0f0f4"]];
        [textField updatePlaceHolder:NSLocalizedString(@"textfiled.placeholder", nil) color:[UIColor colorWithHexString:@"#999999"]];
        [textField setClearButtonMode:UITextFieldViewModeNever];

        [textField setDelegate:self];
        
        //左边
        float offset = iPadValue(15, 10);
        float width = 30;
        CustomTitleAndImageView* leftView = [[CustomTitleAndImageView alloc]initWithLayout:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            [imageView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerY.equalTo(view);
                make.left.mas_offset(offset);
                make.size.mas_equalTo(width);
            }];
            
            imageView.image = [[UIImage imageNamed:@"top_script_icon"] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
            imageView.contentMode = UIViewContentModeScaleAspectFit;
        }];
        CGRect rect = CGRectMake(0.0, 0.0, 2*offset+width, height);
        textField.leftViewRect = rect;
        textField.leftView = leftView;
                
        self.textFieldLeftView = leftView;
        @weakify(self)
        [leftView setTapAction:^{
            @strongify(self)
            if (self.isInReader) {
                //阅读模式设置
                if(self.delegate && [self.delegate respondsToSelector:@selector(topToolbarDidClickReaderSetting)]) {
                    [self.delegate topToolbarDidClickReaderSetting];
                }
            } else {
                //脚本设置
                if (self.delegate && [self.delegate respondsToSelector:@selector(topToolbarDidClickJs)]) {
                    [self.delegate topToolbarDidClickJs];
                }
            }
        }];
        
        //右边
        [self updateWithTextField:textField width:kScreenWidth];
        
        UIView* rightView = [UIView new];
        [rightView addSubview:self.refreshButton];
        
        //刷新按钮
        [self.refreshButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.size.mas_equalTo(44);
            make.center.equalTo(rightView);
        }];
        
        textField.rightView = rightView;
        textField.rightViewMode = UITextFieldViewModeAlways;
        
        _textField = textField;
    }
    
    return _textField;
}

// 更新编辑框rightViewRect
- (void)updateWithTextField:(CustomTextField *)textField
                      width:(float)width
{
    //右边
    float margin = 10;
    float height = 40;
    float rightWidth = 5/*左边间距*/ + 44/*刷新按钮宽度*/ + 5/*右边间距*/;
    float rightOffset = width - 15*2/*Paddingx2*/ - margin*6/*marginx6*/ - 50*6 - rightWidth;
    textField.rightViewRect = CGRectMake(rightOffset, 0, rightWidth, height);
}

- (GradientProgressBar *)progressBar
{
    if(!_progressBar) {
        _progressBar = [[GradientProgressBar alloc]initWithProgressViewStyle:UIProgressViewStyleBar];
        
        _progressBar.hidden = YES;
        _progressBar.alpha = 0;
    }
    
    return _progressBar;
}

- (void)createButtonWithImageName:(NSString*)imageName
                        imageView:(UIImageView*)imageView
{
    [imageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.mas_offset(0);
        make.size.mas_equalTo(30);
    }];
    
    UIImage* image = [[UIImage imageNamed:imageName] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
    imageView.image = image;
    imageView.tintColor = [UIColor colorWithHexString:@"#999999"];
}

- (CustomTitleAndImageView *)backButton
{
    if(!_backButton) {
        @weakify(self)
        _backButton = [[CustomTitleAndImageView alloc]initWithLayout:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            @strongify(self)
            [self createButtonWithImageName:@"navc_back" imageView:imageView];
        }];
    }
    
    return _backButton;
}

- (CustomTitleAndImageView *)forwardButton
{
    if(!_forwardButton) {
        @weakify(self)
        _forwardButton = [[CustomTitleAndImageView alloc]initWithLayout:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            @strongify(self)
            [self createButtonWithImageName:@"navc_forward" imageView:imageView];
        }];
    }
    
    return _forwardButton;
}

- (CustomTitleAndImageView *)addNewTabButton
{
    if(!_addNewTabButton) {
        @weakify(self)
        _addNewTabButton = [[CustomTitleAndImageView alloc]initWithLayout:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            @strongify(self)
            [self createButtonWithImageName:@"navc_add" imageView:imageView];
        }];
    }
    
    return _addNewTabButton;
}

- (CustomTitleAndImageView *)refreshButton
{
    if(!_refreshButton) {
        @weakify(self)
        _refreshButton = [[CustomTitleAndImageView alloc]initWithLayout:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            UIImage* image = [UIImage imageNamed:@"navc_refresh"];
            image = [image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
            imageView.image = image;
            
            [imageView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.center.mas_offset(0);
                make.size.mas_equalTo(22);
            }];
        }];
    }
    
    return _refreshButton;
}

- (CustomTitleAndImageView *)homeButton
{
    if(!_homeButton) {
        @weakify(self)
        _homeButton = [[CustomTitleAndImageView alloc]initWithLayout:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            @strongify(self)
            [self createButtonWithImageName:@"navc_home" imageView:imageView];
        }];
    }
    
    return _homeButton;
}

- (TabsButton *)tabsButton
{
    if(!_tabsButton) {
        _tabsButton = [TabsButton new];        
        [_tabsButton updateTabCount:1];
    }
    
    return _tabsButton;
}

- (CustomTitleAndImageView *)appMenuButton
{
    if(!_appMenuButton) {
        @weakify(self)
        _appMenuButton = [[CustomTitleAndImageView alloc]initWithLayout:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            @strongify(self)
            [self createButtonWithImageName:@"navc_menu" imageView:imageView];
        }];
    }
    
    return _appMenuButton;
}

- (NSMutableArray *)actionButtons
{
    if(!_actionButtons) {
        _actionButtons = [NSMutableArray array];
        [_actionButtons addObjectsFromArray:@[
            self.backButton,
            self.forwardButton,
            self.addNewTabButton,
            self.refreshButton,
            self.appMenuButton,
            self.tabsButton,
            self.homeButton
        ]];
    }
    
    return _actionButtons;
}

@end
