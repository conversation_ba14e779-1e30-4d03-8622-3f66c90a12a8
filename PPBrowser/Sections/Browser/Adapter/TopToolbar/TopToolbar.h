//
//  TopToolbar.h
//  PandaBrowser
//
//  Created by q<PERSON><PERSON> on 2022/3/3.
//

#import <UIKit/UIKit.h>

#import "NSString+Helper.h"
#import "NSURL+Extension.h"

#import "ThemeProtocol.h"
#import "PreferenceManager.h"
#import "CustomTitleAndImageView.h"

/// 约束一下必须实现的方法
@protocol TopToolBarProtocol <NSObject>

@required
- (void)updateGoBackStatus:(BOOL)canGoBack;
- (void)updateGoForwardStatus:(BOOL)canGoForward;
//更新标签页的个数
- (void)updateTabCount:(int)count;
- (void)updateCurrentURL:(NSString*)url;
- (void)show:(BOOL)animated;
- (void)dismiss:(BOOL)animated;
- (void)updateProgressBar:(float)progress;
- (void)hideProgressBar;

- (void)updateLoadingStatus:(BOOL)isLoading;
// 控件高度
+ (float)toolbarHeight;
// 35+5, 真正滑动的高度
+ (float)topScrollHeight;

@end

@protocol TopToolbarDelegate <NSObject>
@optional
- (void)topToolbarDidSearch;
- (void)topToolbarDidReload;
- (void)topToolbarDidCancelReload;
- (void)topToolbarDidMenuAction:(UIView*)menuButton;

//iPad(或者横屏iPhone)
- (void)topToolbarDidClickBack;
- (void)topToolbarDidClickForward;
- (void)topToolbarDidClickAddTab;
- (void)topToolbarDidClickTabs;
- (void)topToolbarDidClickMenu;
- (void)topToolbarDidClickHome;
//长按tabs
- (void)topToolbarDidLongPressTabs:(UIButton*)tabsButton;
//点击了脚本
- (void)topToolbarDidClickJs;
// v2.6.8,工具栏长按手势
- (void)topToolbarDidLongPress:(UIView*)button forToolbarGroup:(ToolbarGroup)group;
// v2.7.6 阅读模式设置
- (void)topToolbarDidClickReaderSetting;

@end

// iPhone对应的顶部工具栏
@interface TopToolbar : UIView<ThemeProtocol,TopToolBarProtocol>

@property (nonatomic, weak) id<TopToolbarDelegate> delegate;
@property (nonatomic, strong) NSString* currentURL;
@property (readonly) CustomTitleAndImageView* appMenuButton;
@property (readonly) CustomTitleAndImageView* textFieldLeftView;

- (void)updateProgressBar:(float)progress;
- (void)hideProgressBar;

- (void)updateLoadingStatus:(BOOL)isLoading;

- (void)show:(BOOL)animated;
- (void)dismiss:(BOOL)animated;

- (void)showSearchBar;
- (void)hideSearchBar;

- (void)updateCurrentURL:(NSString *)url;
- (void)updateCurrentTitle:(NSString *)title;

//逻辑与BottomToolbar一致
- (void)updateGoBackStatus:(BOOL)canGoBack;
- (void)updateGoForwardStatus:(BOOL)canGoForward;

//更新标签页的个数
- (void)updateTabCount:(int)count;

// 2.7.6 更新阅读模式/脚本管理的状态
- (void)updateIsInReader:(BOOL)isInReader;

// 控件高度
+ (float)toolbarHeight;
// 35+5, 真正滑动的高度
+ (float)topScrollHeight;

@end


