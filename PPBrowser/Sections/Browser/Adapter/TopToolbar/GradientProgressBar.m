//
//  GradientProgressBar.m
//  PPBrowser
//
//  Created by qingbin on 2022/4/3.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "GradientProgressBar.h"

#import "MaizyHeader.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"

#import "UIColor+Helper.h"

@interface GradientProgressBar ()
// Alpha mask for visible part of gradient.
@property(nonatomic,strong) CALayer* alphaMaskLayer;
// Gradient layer.
@property(nonatomic,strong) CAGradientLayer* gradientLayer;
@end

@implementation GradientProgressBar

- (instancetype)init
{
    self = [super init];
    if(self) {
        self.alphaMaskLayer = [CALayer layer];
        self.gradientLayer = [[CAGradientLayer alloc]init];
        
        [self setupProgressViewColors];
        [self setupAlphaMaskLayer];
//        [self setupGradientLayer];
        
        [self.layer insertSublayer:self.gradientLayer atIndex:0];
        [self updateAlphaMaskLayerWidthAnimated:NO];
    }
    
    return self;
}

- (void)setupProgressViewColors
{
    self.backgroundColor = [UIColor colorWithHexString:@"#363B40"];
    self.trackTintColor = UIColor.clearColor;
    self.progressTintColor = UIColor.clearColor;
}

- (void)setupAlphaMaskLayer
{
    self.alphaMaskLayer.frame = self.bounds;
    self.alphaMaskLayer.cornerRadius = 1.5;
    
    self.alphaMaskLayer.anchorPoint = CGPointMake(0, 0);
    self.alphaMaskLayer.position = CGPointMake(0, 0);
    
    self.alphaMaskLayer.backgroundColor = UIColor.whiteColor.CGColor;
}

//- (void)setupGradientLayer
//{
//    // Apply "alphaMaskLayer" as a mask to the gradient layer in order to show only parts of the current "progress"
//    self.gradientLayer.mask = self.alphaMaskLayer;
//    
//    self.gradientLayer.frame = CGRectMake(self.bounds.origin.x, self.bounds.origin.y, self.bounds.size.width*2, self.bounds.size.height);
//    
//    UIColor* firstColor = [UIColor colorWithHexString:@"#9059ff"];
//    UIColor* secondColor = [UIColor colorWithHexString:@"#ff4aa2"];
//    UIColor* thirdColor = [UIColor colorWithHexString:@"#ffbd4f"];
//    self.gradientLayer.colors = @[firstColor,secondColor,thirdColor];
//    self.gradientLayer.locations = @[@0.0, @0.2, @0.4, @0.6, @0.8, @1.0, @1.0];
//    self.gradientLayer.startPoint = CGPointMake(0, 0);
//    self.gradientLayer.endPoint = CGPointMake(1, 0);
//    self.gradientLayer.drawsAsynchronously = NO;
//}

- (void)hideProgressBar
{
    if(self.progress < 1.0) return;
    
    [CATransaction begin];
    CABasicAnimation* moveAnimation = [CABasicAnimation animationWithKeyPath:@"position"];
    moveAnimation.duration = 0.25;
    moveAnimation.fromValue = @(self.gradientLayer.position);
    moveAnimation.toValue = @(CGPointMake(self.gradientLayer.frame.size.width,  self.gradientLayer.position.y));
    moveAnimation.fillMode = kCAFillModeForwards;
    moveAnimation.removedOnCompletion = false;

    [CATransaction setCompletionBlock:^{
        [super setProgress:0];
        self.hidden = YES;
    }];

    [self.gradientLayer addAnimation:moveAnimation forKey:@"position"];

    [CATransaction commit];
}

- (void)layoutSubviews
{
    [super layoutSubviews];
    
    self.gradientLayer.frame = CGRectMake(self.bounds.origin.x-4, self.bounds.origin.y, self.bounds.size.width*2, self.bounds.size.height);
}

- (void)animateGradient
{
    CABasicAnimation* gradientChangeAnimation = [CABasicAnimation animationWithKeyPath:@"locations"];
    gradientChangeAnimation.duration = 0.2 * 2;
    gradientChangeAnimation.fromValue = @[@0.0, @0.0, @0.0, @0.2, @0.4, @0.6, @0.8];
    gradientChangeAnimation.toValue = @[@0.0, @0.2, @0.4, @0.6, @0.8, @1.0, @1.0];
    gradientChangeAnimation.fillMode = kCAFillModeForwards;
    gradientChangeAnimation.removedOnCompletion = NO;
    gradientChangeAnimation.repeatCount = INFINITY;
    [self.gradientLayer addAnimation:gradientChangeAnimation forKey:@"colorChange"];
}

- (void)updateAlphaMaskLayerWidthAnimated:(BOOL)animated
{
    [CATransaction begin];
    [CATransaction setAnimationDuration:animated?0.2:0];
    
    self.alphaMaskLayer.frame = CGRectMake(self.bounds.origin.x, self.bounds.origin.y, self.bounds.size.width*self.progress, self.bounds.size.height);
    if(self.progress >= 1.0) {
        [CATransaction setCompletionBlock:^{
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [self hideProgressBar];
            });
        }];
    }
    
    [CATransaction commit];
}

- (void)setProgress:(float)progress animated:(BOOL)animated
{    
    [self.gradientLayer removeAnimationForKey:@"position"];
    
    if(![self.gradientLayer.animationKeys containsObject:@"colorChange"]) {
        [self animateGradient];
    }
    
    [super setProgress:progress animated:animated];
    
    [self updateAlphaMaskLayerWidthAnimated:animated];
}

@end
