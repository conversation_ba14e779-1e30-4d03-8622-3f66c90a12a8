//
//  TopToolbar.m
//  PandaBrowser
//
//  Created by qingbin on 2022/3/3.
//

#import "TopToolbar.h"

#import "MaizyHeader.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"
#import "CustomTextField.h"

#import "UIColor+Helper.h"
#import "NSObject+Helper.h"

#import "GradientProgressBar.h"
#import "SearchSuggestionController.h"
#import "PPNotifications.h"

#import "BrowserUtils.h"
#import "TabsButton.h"

@interface TopToolbar ()<UITextFieldDelegate>
//iPhone横屏下的按钮
@property (nonatomic, strong) NSArray *horizontalButtons;

@property (nonatomic, strong) UIStackView *stackView;

@property (nonatomic, strong) CustomTitleAndImageView* backButton;
@property (nonatomic, strong) CustomTitleAndImageView* forwardButton;
@property (nonatomic, strong) CustomTitleAndImageView* addNewTabButton;

@property (nonatomic, strong) CustomTextField* textField;
@property (nonatomic, strong) CustomTitleAndImageView* textFieldLeftView;

@property (nonatomic, strong) CustomTitleAndImageView* appMenuButton;
@property (nonatomic, strong) TabsButton* tabsButton;
@property (nonatomic, strong) CustomTitleAndImageView* homeButton;

@property (nonatomic, strong) GradientProgressBar* progressBar;

/// 竖屏时搜索框右边显示的工具栏
@property (nonatomic, strong) UIView *textRightView;
@property (nonatomic, strong) CustomTitleAndImageView *textRefreshButton;
@property (nonatomic, strong) UIView* line;
@property (nonatomic, strong) CustomTitleAndImageView *textMenuButton;

@property (nonatomic, assign) BOOL isLoading;

@property (nonatomic, assign) BOOL canGoBack;

@property (nonatomic, assign) BOOL canGoForward;
//2.7.6 当前网页是否处于阅读模式
@property (nonatomic, assign) BOOL isInReader;

@end

@implementation TopToolbar

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if(self) {
        [self commonInit];
        
        [self addSubviews];
        [self defineLayout];
        [self handleEvents];
        
        self.isLoading = NO;
        
        //先隐藏搜索框
        [self hideSearchBar];
        
        [self applyTheme];
    }
    
    return self;
}

- (void)commonInit
{
    self.horizontalButtons = @[
        self.backButton,
        self.forwardButton,
        self.addNewTabButton,
        self.appMenuButton,
        self.tabsButton,
        self.homeButton
    ];
    
    for(UIButton* button in self.horizontalButtons) {
        button.hidden = YES;
    }
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        
        self.textField.backgroundColor = [UIColor colorWithHexString:kDarkThemeColorTextFiledBackgroundColor];
        self.textField.textColor = UIColor.whiteColor;
        [self.textField updatePlaceHolder:NSLocalizedString(@"textfiled.placeholder", nil) color:[UIColor colorWithHexString:kDarkThemeColorTextFiledContent]];
        self.line.backgroundColor = [UIColor colorWithHexString:kDarkThemeColorLine];
        
        [self.textFieldLeftView updateWith:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            imageView.tintColor = UIColor.whiteColor;
        }];
        
        [self.textRefreshButton updateWith:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            imageView.tintColor = UIColor.whiteColor;
        }];
        
        [self.textMenuButton updateWith:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            imageView.tintColor = UIColor.whiteColor;
        }];
                
        for(CustomTitleAndImageView* button in self.horizontalButtons) {
            if([button isKindOfClass:TabsButton.class]) {
                button.tintColor = UIColor.whiteColor;
            } else if([button isKindOfClass:CustomTitleAndImageView.class]) {
                [button updateWith:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
                    imageView.tintColor = UIColor.whiteColor;
                }];
            }
        }
    } else {
        self.backgroundColor = UIColor.whiteColor;
        
        self.textField.textColor = [UIColor colorWithHexString:@"#333333"];
        self.line.backgroundColor = [UIColor colorWithHexString:@"#333333"];
        [self.textField setBackgroundColor:[UIColor colorWithHexString:@"#f5f5f5"]];
        [self.textField updatePlaceHolder:NSLocalizedString(@"textfiled.placeholder", nil) color:[UIColor colorWithHexString:@"#999999"]];
        
        [self.textFieldLeftView updateWith:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            imageView.tintColor = [UIColor colorWithHexString:@"#333333"];
        }];
        
        [self.textRefreshButton updateWith:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            imageView.tintColor = [UIColor colorWithHexString:@"#333333"];
        }];
        
        [self.textMenuButton updateWith:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            imageView.tintColor = [UIColor colorWithHexString:@"#333333"];
        }];
        
        for(CustomTitleAndImageView* button in self.horizontalButtons) {
            if([button isKindOfClass:TabsButton.class]) {
                button.tintColor = [UIColor colorWithHexString:@"#333333"];
            } else if([button isKindOfClass:CustomTitleAndImageView.class]) {
                [button updateWith:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
                    imageView.tintColor = [UIColor colorWithHexString:@"#333333"];
                }];
            }
        }
    }
    
    [self.tabsButton applyTheme:isDarkTheme];
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}

- (void)updateCurrentURL:(NSString *)url
{
    self.currentURL = url;
    
    AddressBarDisplayMode displayMode = [[PreferenceManager shareInstance].items.toptoolDisplayMode intValue];
    if (displayMode == AddressBarDisplayModeURL || displayMode == AddressBarDisplayModeTitle) {
        //显示网址
        self.textField.text = [[[[NSURL URLWithString:url] withoutWWW] schemelessAbsoluteString] trim:@"/"];
    } else if (displayMode == AddressBarDisplayModeDomain) {
        //显示域名
        NSURL* URL = [NSURL URLWithString:url];
        self.textField.text = URL.host;
    }
}

- (void)updateCurrentTitle:(NSString *)title
{
    AddressBarDisplayMode displayMode = [[PreferenceManager shareInstance].items.toptoolDisplayMode intValue];
    if (displayMode != AddressBarDisplayModeTitle) return;
    
    if (title.length > 0) {
        self.textField.text = title;
    }
}

- (void)updateLoadingStatus:(BOOL)isLoading
{
    self.isLoading = isLoading;
    if(!isLoading) {
        UIImage* image = [UIImage imageNamed:@"top_refresh_icon"];
        image = [image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        
        [self.textRefreshButton updateWith:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            imageView.image = image;
        }];
    } else {
        UIImage* image = [UIImage imageNamed:@"top_close_icon"];
        image = [image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        
        [self.textRefreshButton updateWith:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            imageView.image = image;
        }];
    }
}

// 2.7.6 更新阅读模式/脚本管理的状态
- (void)updateIsInReader:(BOOL)isInReader
{
    self.isInReader = isInReader;
    
    if (isInReader) {
        //阅读模式
        self.textFieldLeftView.imageView.image = [[UIImage imageNamed:@"top_book_icon"] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
    } else {
        //正常模式
        self.textFieldLeftView.imageView.image = [[UIImage imageNamed:@"top_script_icon"] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
    }
}

- (void)show:(BOOL)animated
{
    if(self.superview == nil) return;
    
    if(animated) {
        [self mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.mas_offset(0).priorityHigh();
        }];
        
        [UIView animateWithDuration:0.3 animations:^{
            [self layoutIfNeeded];
        } completion:^(BOOL finished) {
            [self showSearchBar];
        }];
    } else {
        [self mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.mas_offset(0).priorityHigh();
        }];
        [self showSearchBar];
    }
}

- (void)dismiss:(BOOL)animated
{
    if(self.superview == nil) return;
    
    [self hideSearchBar];

    if(animated) {
        [self mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.mas_offset(-[TopToolbar toolbarHeight]).priorityHigh();
        }];
        
        [UIView animateWithDuration:0.3 animations:^{
            [self layoutIfNeeded];
        } completion:^(BOOL finished) {
        }];
    } else {
        [self mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.mas_offset(-[TopToolbar toolbarHeight]).priorityHigh();
        }];
    }
}

// iPhone横竖屏切换
- (void)viewDidSafeAreaDidChange
{
    if(![BrowserUtils isiPhone]) return;
    
    [self mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([TopToolbar toolbarHeight]).priorityHigh();
    }];
    
    for(CustomTitleAndImageView* button in self.horizontalButtons) {
        button.hidden = ![[BrowserUtils shareInstance] isLandscape];
    }
    
    if([[BrowserUtils shareInstance] isLandscape]) {
        //iPhone横屏
        self.textField.rightView = [UIView new];
        self.textField.rightViewRect = CGRectZero;
        
        self.textField.layer.cornerRadius = 5;
        self.textField.layer.masksToBounds = YES;
    } else {
        //iPhone竖屏
        self.textField.rightView = self.textRightView;
        self.textField.rightViewRect = [self textRightRect];
        
        self.textField.layer.cornerRadius = 0;
        self.textField.layer.masksToBounds = NO;
    }
}

- (void)showSearchBar
{
    self.textField.hidden = NO;
}

- (void)hideSearchBar
{
    self.textField.hidden = YES;
}

- (void)updateGoBackStatus:(BOOL)canGoBack
{
    //只有没有设置壁纸的情况下才能使用暗黑模式
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    
    self.canGoBack = canGoBack;
    self.backButton.userInteractionEnabled = canGoBack;
    
    [self.backButton updateWith:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
        if(canGoBack) {
            if(isDarkTheme) {
                imageView.tintColor = [UIColor colorWithHexString:@"#ffffff"];
            } else {
                imageView.tintColor = [UIColor colorWithHexString:@"#333333"];
            }
        } else {
            imageView.tintColor = [UIColor colorWithHexString:@"#999999"];
        }
    }];
}

- (void)updateGoForwardStatus:(BOOL)canGoForward
{
    //只有没有设置壁纸的情况下才能使用暗黑模式
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    
    self.canGoForward = canGoForward;
    self.forwardButton.userInteractionEnabled = canGoForward;
    
    [self.forwardButton updateWith:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
        if(canGoForward) {
            if(isDarkTheme) {
                imageView.tintColor = [UIColor colorWithHexString:@"#ffffff"];
            } else {
                imageView.tintColor = [UIColor colorWithHexString:@"#333333"];
            }
        } else {
            imageView.tintColor = [UIColor colorWithHexString:@"#999999"];
        }
    }];
}

//更新标签页的个数
- (void)updateTabCount:(int)count
{
    count = MAX(0, count);
    [BrowserUtils shareInstance].allTabsCount = count;
    
    [self.tabsButton updateTabCount:count];
}

#pragma mark -- UITextFieldDelegate
- (BOOL)textFieldShouldBeginEditing:(UITextField *)textField
{
    if(self.delegate && [self.delegate respondsToSelector:@selector(topToolbarDidSearch)]) {
        [self.delegate topToolbarDidSearch];
    }
    
    return NO;
}

#pragma mark -- 控件高度
+ (float)textFieldHeight
{
    return 35;
}

+ (float)toolbarHeight
{
    //限定最小高度是44
    float topOffset = [self topOffset];
    float height = [self textFieldHeight] + 3 + topOffset;
    
    if(height < 44) {
        height = 44;
    }
    
    return height;
}

+ (float)topOffset
{
    if([BrowserUtils isiPhone]) {
        //iPhone
        //iPhone横竖屏切换逻辑
        return [[BrowserUtils shareInstance] safeArea].top;
    }
    
    UIWindow* window = [NSObject normalWindow];
    float topOffset = window.safeAreaInsets.top;
    return topOffset;
}

+ (float)topScrollHeight
{
    return [TopToolbar toolbarHeight] - [TopToolbar topOffset];
}

- (void)updateProgressBar:(float)progress
{    
    self.progressBar.alpha = 1;
    self.progressBar.hidden = NO;
    [self.progressBar setProgress:progress animated:YES];
}

- (void)hideProgressBar
{
    self.progressBar.hidden = YES;
    [self.progressBar setProgress:0 animated:YES];
}

#pragma mark -- layout

- (void)addSubviews
{
    [self addSubview:self.stackView];
    [self addSubview:self.progressBar];
}

- (void)defineLayout
{
    float ButtonHeight = [TopToolbar textFieldHeight];
    
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.mas_safeAreaLayoutGuideLeft);
        make.right.equalTo(self.mas_safeAreaLayoutGuideRight);
        make.centerX.mas_offset(0);
        make.height.mas_equalTo(ButtonHeight);
        make.bottom.mas_offset(-3);
    }];
    
    [self.textField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([TopToolbar textFieldHeight]);
    }];
    [self.textField setContentCompressionResistancePriority:UILayoutPriorityDefaultLow forAxis:UILayoutConstraintAxisHorizontal];
    [self.textField setContentHuggingPriority:UILayoutPriorityDefaultLow forAxis:UILayoutConstraintAxisHorizontal];
    
    for(CustomTitleAndImageView* button in self.horizontalButtons) {
        [button setContentCompressionResistancePriority:UILayoutPriorityDefaultHigh forAxis:UILayoutConstraintAxisHorizontal];
        [button setContentHuggingPriority:UILayoutPriorityDefaultHigh forAxis:UILayoutConstraintAxisHorizontal];
    }
    
    [self.progressBar mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self);
        make.bottom.equalTo(self).offset(1.5/2.0);
        make.height.mas_equalTo(1.5);
    }];
    
    for(CustomTitleAndImageView* button in self.horizontalButtons) {
        [button mas_makeConstraints:^(MASConstraintMaker *make) {
            make.size.mas_equalTo(ButtonHeight);
        }];
    }
}

- (void)handleEvents
{
    @weakify(self)
    [self.textRefreshButton setTapAction:^{
        @strongify(self)
        if(!self.isLoading) {
            if(self.delegate && [self.delegate respondsToSelector:@selector(topToolbarDidReload)]) {
                [self.delegate topToolbarDidReload];
            }
        } else {
            if(self.delegate && [self.delegate respondsToSelector:@selector(topToolbarDidCancelReload)]) {
                [self.delegate topToolbarDidCancelReload];
            }
        }
    }];
    
    [self.textMenuButton setTapAction:^{
        @strongify(self)
        if(self.delegate && [self.delegate respondsToSelector:@selector(topToolbarDidMenuAction:)]) {
            [self.delegate topToolbarDidMenuAction:self.textMenuButton];
        }
    }];
    
    //横屏iPhone
    [self.backButton setTapAction:^{
        @strongify(self)
        if(self.delegate && [self.delegate respondsToSelector:@selector(topToolbarDidClickBack)]) {
            [self.delegate topToolbarDidClickBack];
        }
    }];
    
    // 添加返回按钮长按手势
    UILongPressGestureRecognizer* backLongPress = [[UILongPressGestureRecognizer alloc] init];
    [self.backButton addGestureRecognizer:backLongPress];
    [self.backButton.tapGesture requireGestureRecognizerToFail:backLongPress];
    [[backLongPress rac_gestureSignal] subscribeNext:^(UILongPressGestureRecognizer *gesture) {
        @strongify(self)
        if (gesture.state == UIGestureRecognizerStateBegan) {
            if(self.delegate && [self.delegate respondsToSelector:@selector(topToolbarDidLongPress:forToolbarGroup:)]) {
                [self.delegate topToolbarDidLongPress:self.backButton forToolbarGroup:ToolbarGroupBack];
            }
        }
    }];
    
    [self.forwardButton setTapAction:^{
        @strongify(self)
        if(self.delegate && [self.delegate respondsToSelector:@selector(topToolbarDidClickForward)]) {
            [self.delegate topToolbarDidClickForward];
        }
    }];
    
    // 添加前进按钮长按手势
    UILongPressGestureRecognizer* forwardLongPress = [[UILongPressGestureRecognizer alloc] init];
    [self.forwardButton addGestureRecognizer:forwardLongPress];
    [self.forwardButton.tapGesture requireGestureRecognizerToFail:forwardLongPress];
    [[forwardLongPress rac_gestureSignal] subscribeNext:^(UILongPressGestureRecognizer *gesture) {
        @strongify(self)
        if (gesture.state == UIGestureRecognizerStateBegan) {
            if(self.delegate && [self.delegate respondsToSelector:@selector(topToolbarDidLongPress:forToolbarGroup:)]) {
                [self.delegate topToolbarDidLongPress:self.forwardButton forToolbarGroup:ToolbarGroupForward];
            }
        }
    }];
    
    [self.addNewTabButton setTapAction:^{
        @strongify(self)
        //增加一个tab
        [BrowserUtils shareInstance].allTabsCount++;
        [self updateTabCount:[BrowserUtils shareInstance].allTabsCount];
        
        if(self.delegate && [self.delegate respondsToSelector:@selector(topToolbarDidClickAddTab)]) {
            [self.delegate topToolbarDidClickAddTab];
        }
    }];
    
    // 添加新建按钮长按手势
    UILongPressGestureRecognizer* addTabLongPress = [[UILongPressGestureRecognizer alloc] init];
    [self.addNewTabButton addGestureRecognizer:addTabLongPress];
    [self.addNewTabButton.tapGesture requireGestureRecognizerToFail:addTabLongPress];
    [[addTabLongPress rac_gestureSignal] subscribeNext:^(UILongPressGestureRecognizer *gesture) {
        @strongify(self)
        if (gesture.state == UIGestureRecognizerStateBegan) {
            if(self.delegate && [self.delegate respondsToSelector:@selector(topToolbarDidLongPress:forToolbarGroup:)]) {
                [self.delegate topToolbarDidLongPress:self.addNewTabButton forToolbarGroup:ToolbarGroupNew];
            }
        }
    }];
    
//    [[self.tabsButton rac_signalForControlEvents:UIControlEventTouchUpInside]
//    subscribeNext:^(id x) {
//        @strongify(self)
//        if(self.delegate && [self.delegate respondsToSelector:@selector(topToolbarDidClickTabs)]) {
//            [self.delegate topToolbarDidClickTabs];
//        }
//    }];
    UILongPressGestureRecognizer* longPress = [UILongPressGestureRecognizer new];
    [self.tabsButton addGestureRecognizer:longPress];
    
    UITapGestureRecognizer* tap = [UITapGestureRecognizer new];
    [self.tabsButton addGestureRecognizer:tap];
    [tap requireGestureRecognizerToFail:longPress];
    
    [[longPress rac_gestureSignal] subscribeNext:^(UILongPressGestureRecognizer *gesture) {
        @strongify(self)
        if (gesture.state == UIGestureRecognizerStateBegan) {
            if(self.delegate && [self.delegate respondsToSelector:@selector(topToolbarDidLongPress:forToolbarGroup:)]) {
                [self.delegate topToolbarDidLongPress:self.tabsButton forToolbarGroup:ToolbarGroupTabs];
            }
        }
    }];
    
    [[tap rac_gestureSignal] subscribeNext:^(id x) {
        @strongify(self)
        if(self.delegate && [self.delegate respondsToSelector:@selector(topToolbarDidClickTabs)]) {
            [self.delegate topToolbarDidClickTabs];
        }
    }];
    
    [self.appMenuButton setTapAction:^{
        @strongify(self)
        if(self.delegate && [self.delegate respondsToSelector:@selector(topToolbarDidClickMenu)]) {
            [self.delegate topToolbarDidClickMenu];
        }
    }];
    
    // 添加功能按钮长按手势
    UILongPressGestureRecognizer* menuLongPress = [[UILongPressGestureRecognizer alloc] init];
    [self.appMenuButton addGestureRecognizer:menuLongPress];
    [self.appMenuButton.tapGesture requireGestureRecognizerToFail:menuLongPress];
    [[menuLongPress rac_gestureSignal] subscribeNext:^(UILongPressGestureRecognizer *gesture) {
        @strongify(self)
        if (gesture.state == UIGestureRecognizerStateBegan) {
            if(self.delegate && [self.delegate respondsToSelector:@selector(topToolbarDidLongPress:forToolbarGroup:)]) {
                [self.delegate topToolbarDidLongPress:self.appMenuButton forToolbarGroup:ToolbarGroupFeatures];
            }
        }
    }];

    [self.homeButton setTapAction:^{
        @strongify(self)
        if(self.delegate && [self.delegate respondsToSelector:@selector(topToolbarDidClickHome)]) {
            [self.delegate topToolbarDidClickHome];
        }
    }];
    
    // 添加主页按钮长按手势
    UILongPressGestureRecognizer* homeLongPress = [[UILongPressGestureRecognizer alloc] init];
    [self.homeButton addGestureRecognizer:homeLongPress];
    [self.homeButton.tapGesture requireGestureRecognizerToFail:homeLongPress];
    [[homeLongPress rac_gestureSignal] subscribeNext:^(UILongPressGestureRecognizer *gesture) {
        @strongify(self)
        if (gesture.state == UIGestureRecognizerStateBegan) {
            if(self.delegate && [self.delegate respondsToSelector:@selector(topToolbarDidLongPress:forToolbarGroup:)]) {
                [self.delegate topToolbarDidLongPress:self.homeButton forToolbarGroup:ToolbarGroupHome];
            }
        }
    }];
    
    // 进入阅读模式
    [[[NSNotificationCenter defaultCenter] rac_addObserverForName:kEnterReaderNotification object:nil]
        subscribeNext:^(id x) {
        @strongify(self)
        [self updateIsInReader:YES];
    }];
    
    // 退出阅读模式
    [[[NSNotificationCenter defaultCenter] rac_addObserverForName:kExitReaderNotification object:nil]
        subscribeNext:^(id x) {
        @strongify(self)
        [self updateIsInReader:NO];
    }];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(darkThemeDidChangeNotification:)
                                                 name:kDarkThemeDidChangeNotification
                                               object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(viewDidSafeAreaDidChange)
                                                 name:kViewSafeAreaInsetsDidChangeNotification
                                               object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(viewDidSafeAreaDidChange)
                                                 name:kOrientationDidChangeNotification
                                               object:nil];
}

#pragma mark -- 设置搜索框
- (CGRect)textRightRect
{
    float height = [TopToolbar textFieldHeight];
    float rightWidth = 78/*宽度*/ + 5/*右边间距*/;
    float screenWidth = [[BrowserUtils shareInstance] transitionToSize].width;
    float rightOffset = screenWidth - rightWidth;
    CGRect frame = CGRectMake(rightOffset, 0, rightWidth, height);
    
    return frame;
}

- (void)_setupTextField:(CustomTextField *)textField
{
    float height = [TopToolbar textFieldHeight];
    textField.rightViewRect = [self textRightRect];
    
    self.textRightView.frame = [self textRightRect];
    
    [self.textRightView addSubview:self.textRefreshButton];
    [self.textRightView addSubview:self.line];
    [self.textRightView addSubview:self.textMenuButton];
    
    [self.line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_offset(-10);
        make.top.mas_offset(10);
        make.centerY.mas_offset(0);
        make.left.equalTo(self.textRefreshButton.mas_right).offset(3.5f);
        make.width.mas_equalTo(0.5);
    }];
    
    [self.textMenuButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.bottom.equalTo(self.textRightView);
        make.right.equalTo(self.textRightView).offset(-5.0f);
        make.width.mas_equalTo(height);
    }];

    [self.textRefreshButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.bottom.equalTo(self.textRightView);
        make.left.equalTo(self.textRightView);
        make.width.mas_equalTo(height);
    }];
    
    textField.rightView = self.textRightView;
    textField.rightViewMode = UITextFieldViewModeAlways;
}

#pragma mark -- Getters

- (CustomTextField *)textField
{
    if(!_textField) {
        float height = [TopToolbar textFieldHeight];
        CustomTextField* textField = [[CustomTextField alloc] init];
        textField.font = [UIFont systemFontOfSize:14.0];
        //2.6.8 阿拉伯语布局适配
        textField.textAlignment = NSTextAlignmentLeft;
        
        textField.leftViewMode = UITextFieldViewModeAlways;
        textField.textColor = [UIColor colorWithHexString:@"#333333"];
        textField.returnKeyType = UIReturnKeySearch;
        
        [textField setBackgroundColor:[UIColor colorWithHexString:@"#f5f5f5"]];
        [textField updatePlaceHolder:NSLocalizedString(@"textfiled.placeholder", nil) color:[UIColor colorWithHexString:@"#999999"]];
        [textField setClearButtonMode:UITextFieldViewModeNever];

        [textField setDelegate:self];
        
        //左边
        float offset = 15;
        CustomTitleAndImageView* leftView = [[CustomTitleAndImageView alloc]initWithLayout:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            [imageView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerY.equalTo(view);
                make.left.mas_offset(offset);
                make.size.mas_equalTo(22);
            }];
            
            imageView.image = [[UIImage imageNamed:@"top_script_icon"] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
            imageView.contentMode = UIViewContentModeScaleAspectFit;
        }];
        CGRect rect = CGRectMake(0.0, 0.0, offset+height, height);
        textField.leftViewRect = rect;
        textField.leftView = leftView;
        
        self.textFieldLeftView = leftView;
        @weakify(self)
        [leftView setTapAction:^{
            @strongify(self)
            if (self.isInReader) {
                //阅读模式设置
                if(self.delegate && [self.delegate respondsToSelector:@selector(topToolbarDidClickReaderSetting)]) {
                    [self.delegate topToolbarDidClickReaderSetting];
                }
            } else {
                //脚本设置
                if (self.delegate && [self.delegate respondsToSelector:@selector(topToolbarDidClickJs)]) {
                    [self.delegate topToolbarDidClickJs];
                }
            }
        }];
        
        [self _setupTextField:textField];
        
        _textField = textField;
    }
    
    return _textField;
}

- (UIView *)textRightView
{
    if(!_textRightView) {
        _textRightView = [UIView new];
    }
    
    return _textRightView;
}

- (CustomTitleAndImageView *)textRefreshButton
{
    if(!_textRefreshButton) {
        _textRefreshButton = [[CustomTitleAndImageView alloc]initWithLayout:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            UIImage* image = [UIImage imageNamed:@"top_refresh_icon"];
            image = [image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
            imageView.image = image;
            
            [imageView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.center.mas_offset(0);
                make.size.mas_equalTo(18);
            }];
        }];
    }
    
    return _textRefreshButton;
}

- (CustomTitleAndImageView *)textMenuButton
{
    if(!_textMenuButton) {
        _textMenuButton = [[CustomTitleAndImageView alloc]initWithLayout:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            UIImage* image = [UIImage imageNamed:@"top_menu_icon"];
            image = [image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
            imageView.image = image;
            
            [imageView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.center.mas_offset(0);
                make.size.mas_equalTo(20);
            }];
        }];
    }
    
    return _textMenuButton;
}

- (GradientProgressBar *)progressBar
{
    if(!_progressBar) {
        _progressBar = [[GradientProgressBar alloc]initWithProgressViewStyle:UIProgressViewStyleBar];
        
        _progressBar.hidden = YES;
        _progressBar.alpha = 0;
    }
    
    return _progressBar;
}

- (UIView *)line
{
    if(!_line) {
        _line = [UIView new];
    }
    
    return _line;
}

- (UIStackView *)stackView
{
    if(!_stackView) {
        _stackView = [[UIStackView alloc]initWithArrangedSubviews:@[
            self.backButton,
            self.forwardButton,
            self.addNewTabButton,
            self.textField,
            self.appMenuButton,
            self.tabsButton,
            self.homeButton
        ]];
        
        _stackView.spacing = 8.0f;
        _stackView.axis = UILayoutConstraintAxisHorizontal;
        _stackView.alignment = UIStackViewAlignmentCenter;
    }
    
    return _stackView;
}

- (void)createButtonWithImageName:(NSString*)imageName
                        imageView:(UIImageView*)imageView
{
    [imageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.mas_offset(0);
        make.size.mas_equalTo(27);
    }];
    
    UIImage* image = [[UIImage imageNamed:imageName] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
    imageView.image = image;
    imageView.tintColor = [UIColor colorWithHexString:@"#999999"];
}

- (CustomTitleAndImageView *)backButton
{
    if(!_backButton) {
        @weakify(self)
        _backButton = [[CustomTitleAndImageView alloc]initWithLayout:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            @strongify(self)
            [self createButtonWithImageName:@"navc_back" imageView:imageView];
        }];
    }
    
    return _backButton;
}

- (CustomTitleAndImageView *)forwardButton
{
    if(!_forwardButton) {
        @weakify(self)
        _forwardButton = [[CustomTitleAndImageView alloc]initWithLayout:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            @strongify(self)
            [self createButtonWithImageName:@"navc_forward" imageView:imageView];
        }];
    }
    
    return _forwardButton;
}

- (CustomTitleAndImageView *)addNewTabButton
{
    if(!_addNewTabButton) {
        @weakify(self)
        _addNewTabButton = [[CustomTitleAndImageView alloc]initWithLayout:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            @strongify(self)
            [self createButtonWithImageName:@"navc_add" imageView:imageView];
        }];
    }
    
    return _addNewTabButton;
}

- (CustomTitleAndImageView *)homeButton
{
    if(!_homeButton) {
        @weakify(self)
        _homeButton = [[CustomTitleAndImageView alloc]initWithLayout:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            @strongify(self)
            [self createButtonWithImageName:@"navc_home" imageView:imageView];
        }];
    }
    
    return _homeButton;
}

- (TabsButton *)tabsButton
{
    if(!_tabsButton) {
        _tabsButton = [TabsButton new];
        [_tabsButton updateTabCount:1];
    }
    
    return _tabsButton;
}

- (CustomTitleAndImageView *)appMenuButton
{
    if(!_appMenuButton) {
        @weakify(self)
        _appMenuButton = [[CustomTitleAndImageView alloc]initWithLayout:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            @strongify(self)
            [self createButtonWithImageName:@"navc_menu" imageView:imageView];
        }];
    }
    
    return _appMenuButton;
}

@end
