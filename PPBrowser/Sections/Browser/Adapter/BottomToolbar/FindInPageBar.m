//
//  FindInPageBar.m
//  PPBrowser
//
//  Created by qingbin on 2022/4/4.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "FindInPageBar.h"

#import "MaizyHeader.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"
#import "CustomTextField.h"

#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "PPNotifications.h"
#import "Tab.h"

#import "ThemeProtocol.h"
#import "PreferenceManager.h"

@interface FindInPageBar ()<UITextFieldDelegate,ThemeProtocol>

@property(nonatomic,strong) CustomTextField* textField;
@property(nonatomic,strong) UILabel* matchCountLabel;
@property(nonatomic,strong) UIButton* previousButton;
@property(nonatomic,strong) UIButton* nextButton;
@property(nonatomic,strong) UIButton* closeButton;

@property (nonatomic, assign) int currentResult;
@property (nonatomic, assign) int totalResults;

@end

@implementation FindInPageBar

- (instancetype)initWithTab:(Tab*)tab
{
    self = [super init];
    if(self) {
        self.tab = tab;
        
        [self addSubviews];
        [self defineLayout];
        [self setupObservers];
        
        self.backgroundColor = UIColor.whiteColor;
        
        [self applyTheme];
    }
    
    return self;
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        
        self.previousButton.tintColor = UIColor.whiteColor;
        self.nextButton.tintColor = UIColor.whiteColor;
        self.closeButton.tintColor = UIColor.whiteColor;
        
        self.matchCountLabel.textColor = UIColor.whiteColor;
        
        self.textField.textColor = [UIColor colorWithHexString:@"#ffffff"];
        self.textField.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
    } else {
        self.backgroundColor = UIColor.whiteColor;
        
        self.textField.textColor = [UIColor colorWithHexString:@"#333333"];
        self.textField.backgroundColor = UIColor.whiteColor;
        
        self.previousButton.tintColor = [UIColor colorWithHexString:@"#333333"];
        self.nextButton.tintColor = [UIColor colorWithHexString:@"#333333"];
        self.closeButton.tintColor = [UIColor colorWithHexString:@"#333333"];
        
        self.matchCountLabel.textColor = [UIColor colorWithHexString:@"#333333"];
    }
}

//修改暗黑模式
- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}

- (void)updateWithText:(NSString*)text
{
    self.textField.text = text;
    
    if(self.delegate && [self.delegate respondsToSelector:@selector(findInPageBar:didTextChange:)]) {
        [self.delegate findInPageBar:self didTextChange:text];
    }
}

- (BOOL)becomeFirstResponder
{
    [self.textField becomeFirstResponder];
    return [super becomeFirstResponder];
}

- (void)dealloc
{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

#pragma mark -- 通知
- (void)findInPageNotification:(NSNotification*)notification
{
    NSDictionary* data = notification.userInfo;
    
    if(data[@"currentResult"]) {
        self.currentResult = [data[@"currentResult"] intValue];
    }
    
    if(data[@"totalResults"]) {
        self.totalResults = [data[@"totalResults"] intValue];
    }
    
    self.previousButton.enabled = self.totalResults > 1;
    self.nextButton.enabled = self.previousButton.enabled;
    
    self.matchCountLabel.text = [NSString stringWithFormat:@"(%d/%d)",self.currentResult,self.totalResults];
}

#pragma mark -- 代理
- (void)didTextChange:(UITextField*)textField
{
    NSString* text = [textField.text stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceCharacterSet]];
    self.matchCountLabel.hidden = text.length == 0;
    
    if(self.delegate && [self.delegate respondsToSelector:@selector(findInPageBar:didTextChange:)]) {
        [self.delegate findInPageBar:self didTextChange:text];
    }
}

- (void)setupObservers
{
    @weakify(self)
    [[self.previousButton rac_signalForControlEvents:UIControlEventTouchUpInside]
     subscribeNext:^(id x) {
        @strongify(self)
        [self endEditing:YES];
        
        if(self.delegate && [self.delegate respondsToSelector:@selector(findInPageBar:didFindPreviousWithText:)]) {
            [self.delegate findInPageBar:self didFindPreviousWithText:self.textField.text];
        }
    }];
    
    [[self.nextButton rac_signalForControlEvents:UIControlEventTouchUpInside]
     subscribeNext:^(id x) {
        @strongify(self)
        [self endEditing:YES];
        
        if(self.delegate && [self.delegate respondsToSelector:@selector(findInPageBar:didFindNextWithText:)]) {
            [self.delegate findInPageBar:self didFindNextWithText:self.textField.text];
        }
    }];
    
    [[self.closeButton rac_signalForControlEvents:UIControlEventTouchUpInside]
     subscribeNext:^(id x) {
        @strongify(self)
        if(self.delegate && [self.delegate respondsToSelector:@selector(findInPageDidPressClose:)]) {
            [self.delegate findInPageDidPressClose:self];
        }
        
        [self removeFromSuperview];
    }];
    
    [[[self.textField.rac_textSignal distinctUntilChanged] throttle:0.5]
     subscribeNext:^(id x) {
        [self didTextChange:self.textField];
    }];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(findInPageNotification:)
                                                 name:kFindInPageHelperNotification
                                               object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(darkThemeDidChangeNotification:)
                                                 name:kDarkThemeDidChangeNotification
                                               object:nil];
}

#pragma mark -- layout
- (void)addSubviews
{
    [self addSubview:self.textField];
    [self addSubview:self.matchCountLabel];
    [self addSubview:self.previousButton];
    [self addSubview:self.nextButton];
    [self addSubview:self.closeButton];
}

- (void)defineLayout
{
//    UIView* line = [UIView new];
//    line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
//    [self addSubview:line];
//    [line mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.top.leading.trailing.equalTo(self);
//        make.height.mas_equalTo(0.5);
//    }];
    
    [self.textField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.bottom.equalTo(self).insets(UIEdgeInsetsMake(0, 8, 0, 0));
    }];
    [self.textField setContentHuggingPriority:UILayoutPriorityDefaultLow forAxis:UILayoutConstraintAxisHorizontal];
    [self.textField setContentCompressionResistancePriority:UILayoutPriorityDefaultLow forAxis:UILayoutConstraintAxisHorizontal];
    
    [self.matchCountLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.textField.mas_right);
        make.centerY.equalTo(self);
    }];
    [self.matchCountLabel setContentHuggingPriority:UILayoutPriorityDefaultHigh forAxis:UILayoutConstraintAxisHorizontal];
    [self.matchCountLabel setContentCompressionResistancePriority:UILayoutPriorityDefaultHigh forAxis:UILayoutConstraintAxisHorizontal];
    
    float size = iPadValue(60, 44);
    [self.previousButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.matchCountLabel.mas_right);
        make.centerY.equalTo(self);
        make.size.mas_equalTo(size);
    }];
    
    [self.nextButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.previousButton.mas_right);
        make.centerY.equalTo(self);
        make.size.mas_equalTo(size);
    }];
    
    [self.closeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.nextButton.mas_right);
        make.right.equalTo(self);
        make.centerY.equalTo(self);
        make.size.mas_equalTo(size);
    }];
}

- (CustomTextField *)textField
{
    if(!_textField)
    {
        CustomTextField* textField = [[CustomTextField alloc] init];
        //2.6.8 阿拉伯语布局适配
        textField.textAlignment = NSTextAlignmentLeft;
        
        float font = iPadValue(20, 16);
        textField.font = [UIFont systemFontOfSize:font];
        textField.textColor = [UIColor colorWithHexString:@"#333333"];
        textField.returnKeyType = UIReturnKeySearch;
        
        [textField setBackgroundColor:[UIColor colorWithHexString:@"#ffffff"]];
        [textField setDelegate:self];
        
        textField.layer.cornerRadius = 5;
        textField.layer.masksToBounds = YES;
        
//        textField.layer.borderWidth = 0.5;
//        textField.layer.borderColor = [UIColor colorWithHexString:@"#F5F5F5"].CGColor;
        
        _textField = textField;
    }
    
    return _textField;
}

- (UILabel *)matchCountLabel
{
    if(!_matchCountLabel) {
        float font = iPadValue(20, 15);
        _matchCountLabel = [UIView createLabelWithTitle:nil textColor:[UIColor colorWithHexString:@"#333333"] bgColor:UIColor.clearColor fontSize:font textAlignment:NSTextAlignmentLeft bBold:NO];
    }
    
    return _matchCountLabel;
}

- (UIButton *)nextButton
{
    if(!_nextButton) {
        _nextButton = [UIButton new];
        
        UIImage* image = [UIImage imageNamed:@"find_next_icon"];
        image = [image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        
        [_nextButton setImage:image forState:UIControlStateNormal];
    }
    
    return _nextButton;
}

- (UIButton *)previousButton
{
    if(!_previousButton) {
        _previousButton = [UIButton new];
        
        UIImage* image = [UIImage imageNamed:@"find_previous_icon"];
        image = [image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        
        [_previousButton setImage:image forState:UIControlStateNormal];
    }
    
    return _previousButton;
}

- (UIButton *)closeButton
{
    if(!_closeButton) {
        _closeButton = [UIButton new];
        
        UIImage* image = [UIImage imageNamed:@"find_close_icon"];
        image = [image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        
        [_closeButton setImage:image forState:UIControlStateNormal];
    }
    
    return _closeButton;
}

@end
