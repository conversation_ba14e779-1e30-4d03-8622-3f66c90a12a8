//
//  BottomToolbar.m
//  PandaBrowser
//
//  Created by qingbin on 2022/3/3.
//

#import "BottomToolbar.h"

#import "Masonry.h"
#import "ReactiveCocoa.h"

#import "UIColor+Helper.h"
#import "NSObject+Helper.h"
#import "MaizyHeader.h"
#import "PPNotifications.h"
#import "BrowserViewController.h"

#import "TabsButton.h"

#import "CustomButton.h"
#import "BrowserUtils.h"

#import "CommonDataManager.h"
#import "NSObject+Helper.h"
#import "SearchListView.h"

@interface BottomToolbar ()
//水平stackview
@property (nonatomic, strong) UIStackView *stackView;
//返回按钮
@property (nonatomic, strong) CustomTitleAndImageView* backButton;
//前进按钮
@property (nonatomic, strong) CustomTitleAndImageView* forwardButton;
//新增按钮
@property (nonatomic, strong) CustomTitleAndImageView* addTabButton;
//标签页按钮
@property (nonatomic, strong) TabsButton* tabsButton;
//设置页按钮
@property (nonatomic, strong) CustomTitleAndImageView* menuButton;
//主页按钮
@property (nonatomic, strong) CustomTitleAndImageView* homeButton;

@property (nonatomic, strong) UIView* line;

@property (nonatomic, assign) BOOL canGoBack;

@property (nonatomic, assign) BOOL canGoForward;

@property (nonatomic, assign) BOOL isHomePage;

@property (nonatomic, strong) NSMutableArray* optionViews;

//记录它的展开和隐藏状态,主要用于适配iPad屏幕旋转
@property (nonatomic, assign) BOOL isShow;

@end

@implementation BottomToolbar

- (instancetype)init
{
    self = [super init];
    if(self) {
        [self addSubviews];
        [self defineLayout];
        [self setupObserver];
        
        ToolbarOption selectOption = [[PreferenceManager shareInstance].items.toolBarOption intValue];
        [self configureWithOption:selectOption];
    }
    
    return self;
}

//根据option配置底部状态栏
- (void)configureWithOption:(ToolbarOption)option
{
    for(UIView* item in self.optionViews) {
        [self.stackView removeArrangedSubview:item];
    }
    
    [self.optionViews removeAllObjects];
    self.optionViews = nil;
    
//    ToolbarOptionDefault,       //默认样式, < > + 口 三
//    ToolbarOption1   = 1,       //样式1, < > 三 口 主
//    ToolbarOption2   = 2,       //样式2, < > 主 口 三
//    ToolbarOption3   = 3,       //样式3, < > 口 三 主
//    ToolbarOption3   = 4,       //样式4, < > +  三 口
    self.addTabButton.hidden = YES;
    self.homeButton.hidden = YES;
    
    if(option == ToolbarOptionDefault) {
        [self.optionViews addObjectsFromArray:@[
            self.backButton,
            self.forwardButton,
            self.addTabButton,
            self.tabsButton,
            self.menuButton
        ]];
    } else if(option == ToolbarOption1) {
        [self.optionViews addObjectsFromArray:@[
            self.backButton,
            self.forwardButton,
            self.menuButton,
            self.tabsButton,
            self.homeButton
        ]];
    } else if(option == ToolbarOption2) {
        [self.optionViews addObjectsFromArray:@[
            self.backButton,
            self.forwardButton,
            self.homeButton,
            self.tabsButton,
            self.menuButton
        ]];
    } else if(option == ToolbarOption3) {
        [self.optionViews addObjectsFromArray:@[
            self.backButton,
            self.forwardButton,
            self.tabsButton,
            self.menuButton,
            self.homeButton
        ]];
    } else if(option == ToolbarOption4) {
        [self.optionViews addObjectsFromArray:@[
            self.backButton,
            self.forwardButton,
            self.addTabButton,
            self.menuButton,
            self.tabsButton,
        ]];
    }
    
    for(UIView* item in self.optionViews) {
        if(item == self.addTabButton) {
            item.hidden = NO;
        } else if(item == self.homeButton){
            item.hidden = NO;
        }
        [self.stackView addArrangedSubview:item];
    }
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    //只有没有设置壁纸的情况下才能使用暗黑模式
    [self updateIsHomePage:self.isHomePage];
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}

- (void)show:(BOOL)animated isHomePage:(BOOL)isHomePage
{
    if(self.superview == nil) return;
    
    //iPhone横竖屏切换逻辑，如果是横屏，而且不是首页，那么则隐藏
    if([BrowserUtils isiPhone] && [[BrowserUtils shareInstance] isLandscape] && !isHomePage) {
        [self dismiss:NO];
        return;
    }
    
    if(animated) {
        [self mas_updateConstraints:^(MASConstraintMaker *make) {
            make.bottom.mas_offset(0).priorityHigh();
        }];
        
        [UIView animateWithDuration:0.3 animations:^{
            [self layoutIfNeeded];
        } completion:^(BOOL finished) {
        }];
    } else {
        [self mas_updateConstraints:^(MASConstraintMaker *make) {
            make.bottom.mas_offset(0).priorityHigh();
        }];
    }
}

- (void)dismiss:(BOOL)animated
{
    if(self.superview == nil) return;
    
    if(animated) {
        [self mas_updateConstraints:^(MASConstraintMaker *make) {
            make.bottom.mas_offset([BottomToolbar toolbarHeight]).priorityHigh();
        }];
        
        [UIView animateWithDuration:0.3 animations:^{
            [self layoutIfNeeded];
        } completion:^(BOOL finished) {
        }];
    } else {
        [self mas_updateConstraints:^(MASConstraintMaker *make) {
            make.bottom.mas_offset([BottomToolbar toolbarHeight]).priorityHigh();
        }];
    }
}

// iPhone横竖屏切换
- (void)viewDidSafeAreaDidChange
{
    if(![BrowserUtils isiPhone]) return;
    
    [self mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([BottomToolbar toolbarHeight]).priorityHigh();
    }];
}

#pragma mark -- 工具栏屏幕旋转适配
- (void)updateToolBarLayoutsToSize:(CGSize)size
{
    if([BrowserUtils isiPad]) {
        //iPad
        
        //bottomToolbar的高度偏移适配
        if(self.isShow) {
           //显示的
            float height = [BottomToolbar toolbarHeight];
            float offsetY = size.height - height;
            self.frame = CGRectMake(0, offsetY, size.width, height);
        } else {
            //隐藏的
            self.frame = CGRectMake(0, size.height, size.width, [BottomToolbar toolbarHeight]);
        }
    }
}

#pragma mark -- 控件高度
+ (float)toolbarHeight
{
    UIWindow* window = YBIBNormalWindow();
    float bottomOffset = window.safeAreaInsets.bottom;
    
    if([BrowserUtils isiPhone] && [[BrowserUtils shareInstance]isLandscape]) {
        //iPhone
        //iPhone横竖屏切换逻辑
        bottomOffset = 0;
    }
    
    float height = iPadValue(70, 50);
    
    return height+bottomOffset;
}

- (void)updateGoBackStatus:(BOOL)canGoBack
{
    //只有没有设置壁纸的情况下才能使用暗黑模式
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    
    self.canGoBack = canGoBack;
    //不能禁止，因为还有长按事件
//    self.backButton.userInteractionEnabled = canGoBack;
    
    if(canGoBack) {
        if(isDarkTheme) {
            self.backButton.imageView.tintColor = [UIColor colorWithHexString:@"#ffffff"];
        } else {
            self.backButton.imageView.tintColor = [UIColor colorWithHexString:@"#333333"];
        }
    } else {
        self.backButton.imageView.tintColor = [UIColor colorWithHexString:@"#999999"];
    }
}

- (void)updateGoForwardStatus:(BOOL)canGoForward
{
    //只有没有设置壁纸的情况下才能使用暗黑模式
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    
    self.canGoForward = canGoForward;
    //不能禁止，因为还有长按事件
//    self.forwardButton.userInteractionEnabled = canGoForward;
    
    if(canGoForward) {
        if(isDarkTheme) {
            self.forwardButton.imageView.tintColor = [UIColor colorWithHexString:@"#ffffff"];
        } else {
            self.forwardButton.imageView.tintColor = [UIColor colorWithHexString:@"#333333"];
        }
    } else {
        self.forwardButton.imageView.tintColor = [UIColor colorWithHexString:@"#999999"];
    }
}

//更新标签页的个数
- (void)updateTabCount:(int)count
{
    count = MAX(1, count);
    
    [BrowserUtils shareInstance].allTabsCount = count;
    [self.tabsButton updateTabCount:count];
}

//更新当前页面是否是首页,壁纸相关逻辑
- (void)updateIsHomePage:(BOOL)isHomePage
{
    //只有没有设置壁纸的情况下才能使用暗黑模式
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    
    self.isHomePage = isHomePage;
    
    if(isHomePage) {
        //进行壁纸相关设置
        WallpaperModel* model = [PreferenceManager shareInstance].wallpaperModel;
        [self updateWithWallpaper:model];
        
        //有可能通过左滑返回,从而导致全屏模式下没有显示底部工具栏
        [self show:YES isHomePage:isHomePage];
    } else {
        //重置
        [self updateGoBackStatus:self.canGoBack];
        [self updateGoForwardStatus:self.canGoForward];
        
        UIColor* highlightColor = [UIColor colorWithHexString:@"#333333"];
        UIColor* backgroundColor = UIColor.whiteColor;
        if(isDarkTheme) {
            highlightColor = UIColor.whiteColor;
            backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
            self.line.hidden = YES;
            
            [self.tabsButton applyTheme:YES];
        } else {
            self.line.hidden = NO;
            
            [self.tabsButton applyTheme:NO];
        }
        self.addTabButton.imageView.tintColor = highlightColor;
        self.menuButton.imageView.tintColor = highlightColor;
        self.homeButton.imageView.tintColor = highlightColor;
        
        self.backgroundColor = backgroundColor;
        
        if([BrowserUtils isiPad]) {
            //iPad,如果是非首页,那么隐藏底部工具栏
            [self dismiss:NO];
        } else if([CommonDataManager shareInstance].isLockFullScreen) {
            //如果是非首页，而且锁定全屏状态,那么隐藏底部工具栏
            [self dismiss:NO];
        }
    }
}

- (void)updateWithWallpaper:(WallpaperModel *)model
{
    //只有没有设置壁纸的情况下才能使用暗黑模式
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    
    //壁纸的效果只适用于首页
    if([model.wp_lightColor boolValue]) {
        //浅色图标,收到设置的，所以就不进行暗黑模式相关逻辑处理
        if(self.canGoBack) {
            self.backButton.imageView.tintColor = [UIColor colorWithHexString:@"#ffffff"];
        } else {
            self.backButton.imageView.tintColor = [[UIColor colorWithHexString:@"#ffffff"] colorWithAlphaComponent:0.6];
        }
        
        if(self.canGoForward) {
            self.forwardButton.imageView.tintColor = [UIColor colorWithHexString:@"#ffffff"];
        } else {
            self.forwardButton.imageView.tintColor = [[UIColor colorWithHexString:@"#ffffff"] colorWithAlphaComponent:0.6];
        }
        
        self.addTabButton.imageView.tintColor = [UIColor colorWithHexString:@"#ffffff"];
        self.menuButton.imageView.tintColor = [UIColor colorWithHexString:@"#ffffff"];
        self.homeButton.imageView.tintColor = [UIColor colorWithHexString:@"#ffffff"];
        
        [self.tabsButton applyTheme:YES];
    } else {
        //深色图标(默认图标)
        if([model.wp_hasWallpaper boolValue]) {
            //有壁纸
            if(self.canGoBack) {
                self.backButton.imageView.tintColor = [UIColor colorWithHexString:@"#333333"];
            } else {
                self.backButton.imageView.tintColor = [UIColor colorWithHexString:@"#999999"];
            }
            
            if(self.canGoForward) {
                self.forwardButton.imageView.tintColor = [UIColor colorWithHexString:@"#333333"];
            } else {
                self.forwardButton.imageView.tintColor = [UIColor colorWithHexString:@"#999999"];
            }

            self.addTabButton.imageView.tintColor = [UIColor colorWithHexString:@"#333333"];
            self.menuButton.imageView.tintColor = [UIColor colorWithHexString:@"#333333"];
            self.homeButton.imageView.tintColor = [UIColor colorWithHexString:@"#333333"];
            
            [self.tabsButton applyTheme:NO];
        } else {
            //没有设置壁纸
            UIColor* highlightColor = [UIColor colorWithHexString:@"#333333"];
            if(isDarkTheme) {
                highlightColor = UIColor.whiteColor;
            }
            
            if(self.canGoBack) {
                self.backButton.imageView.tintColor = highlightColor;
            } else {
                self.backButton.imageView.tintColor = [UIColor colorWithHexString:@"#999999"];
            }
            
            if(self.canGoForward) {
                self.forwardButton.imageView.tintColor = highlightColor;
            } else {
                self.forwardButton.imageView.tintColor = [UIColor colorWithHexString:@"#999999"];
            }

            self.addTabButton.imageView.tintColor = highlightColor;
            self.menuButton.imageView.tintColor = highlightColor;
            self.homeButton.imageView.tintColor = highlightColor;
            
            [self.tabsButton applyTheme:isDarkTheme];
        }
    }
    
    if([model.wp_hasWallpaper boolValue]) {
        //有壁纸
        self.backgroundColor = UIColor.clearColor;
        self.line.hidden = YES;
    } else {
        //没有壁纸
        if(isDarkTheme) {
            //暗黑模式
            self.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
            self.line.hidden = YES;
        } else {
            //非暗黑模式
            if(self.isHomePage) {
                //首页
                self.backgroundColor = UIColor.clearColor;
            } else {
                //非首页
                self.backgroundColor = UIColor.whiteColor;
            }
            
            self.line.hidden = NO;
        }
    }
}

#pragma mark -- 更新壁纸
- (void)_updateWallpaper
{
    if(!self.isHomePage) return;
    
    UIWindow* window = YBIBNormalWindow();
    UINavigationController* navc = (UINavigationController*)window.rootViewController;

    for(UIViewController* vc in navc.viewControllers) {
        if([vc isMemberOfClass:BrowserViewController.class]) {
            WallpaperModel* model = [PreferenceManager shareInstance].wallpaperModel;
            [self updateWithWallpaper:model];
            break;
        }
    }
}

#pragma mark -- 更新底部状态栏
- (void)_toolbarDidChange
{
    ToolbarOption option = [[PreferenceManager shareInstance].items.toolBarOption intValue];
    [self configureWithOption:option];
    
    //更新标签数量
    int allTabsCount = [BrowserUtils shareInstance].allTabsCount;
    [self updateTabCount:allTabsCount];
}

#pragma mark -- layout
- (void)addSubviews
{
    [self addSubview:self.stackView];
    [self addSubview:self.line];
}

- (void)defineLayout
{
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.top.equalTo(self);
        make.bottom.equalTo(self.mas_safeAreaLayoutGuideBottom);
    }];
    
//    [self.searchListView mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.left.top.right.equalTo(self);
//        make.height.mas_offset(44);
//    }];
//    
//    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.top.equalTo(self.searchListView.mas_bottom);
//        make.left.right.equalTo(self);
//        make.bottom.equalTo(self.verticalStackView);
//    }];
    
    [self.line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.top.equalTo(self);
        make.height.mas_equalTo(0.5);
    }];
}

- (void)setupObserver
{
    @weakify(self)
    [self.backButton setTapAction:^{
        @strongify(self)
        if (!self.canGoBack) return;
        if (self.delegate && [self.delegate respondsToSelector:@selector(bottomToolbarDidClickBack)]) {
            [self.delegate bottomToolbarDidClickBack];
        }
    }];
    
    // 添加返回按钮长按手势
    UILongPressGestureRecognizer* backLongPress = [[UILongPressGestureRecognizer alloc] init];
    [self.backButton addGestureRecognizer:backLongPress];
    [self.backButton.tapGesture requireGestureRecognizerToFail:backLongPress];
    [[backLongPress rac_gestureSignal] subscribeNext:^(UILongPressGestureRecognizer *gesture) {
        @strongify(self)
        if (gesture.state == UIGestureRecognizerStateBegan) {
            if(self.delegate && [self.delegate respondsToSelector:@selector(bottomToolbarDidLongPress:forToolbarGroup:)]) {
                [self.delegate bottomToolbarDidLongPress:self.backButton forToolbarGroup:ToolbarGroupBack];
            }
        }
    }];
        
    [self.forwardButton setTapAction:^{
        @strongify(self)
        if (!self.canGoForward) return;
        if (self.delegate && [self.delegate respondsToSelector:@selector(bottomToolbarDidClickForward)]) {
            [self.delegate bottomToolbarDidClickForward];
        }
    }];
    
    // 添加前进按钮长按手势
    UILongPressGestureRecognizer* forwardLongPress = [[UILongPressGestureRecognizer alloc] init];
    [self.forwardButton addGestureRecognizer:forwardLongPress];
    [self.forwardButton.tapGesture requireGestureRecognizerToFail:forwardLongPress];
    [[forwardLongPress rac_gestureSignal] subscribeNext:^(UILongPressGestureRecognizer *gesture) {
        @strongify(self)
        if (gesture.state == UIGestureRecognizerStateBegan) {
            if(self.delegate && [self.delegate respondsToSelector:@selector(bottomToolbarDidLongPress:forToolbarGroup:)]) {
                [self.delegate bottomToolbarDidLongPress:self.forwardButton forToolbarGroup:ToolbarGroupForward];
            }
        }
    }];
        
    [self.addTabButton setTapAction:^{
        @strongify(self)
        //增加一个tab
        [BrowserUtils shareInstance].allTabsCount++;
        [self updateTabCount:[BrowserUtils shareInstance].allTabsCount];
        
        if(self.delegate && [self.delegate respondsToSelector:@selector(bottomToolbarDidClickAddTab)]) {
            [self.delegate bottomToolbarDidClickAddTab];
        }
    }];
    
    // 添加新建按钮长按手势
    UILongPressGestureRecognizer* addTabLongPress = [[UILongPressGestureRecognizer alloc] init];
    [self.addTabButton addGestureRecognizer:addTabLongPress];
    [self.addTabButton.tapGesture requireGestureRecognizerToFail:addTabLongPress];
    [[addTabLongPress rac_gestureSignal] subscribeNext:^(UILongPressGestureRecognizer *gesture) {
        @strongify(self)
        if (gesture.state == UIGestureRecognizerStateBegan) {
            if(self.delegate && [self.delegate respondsToSelector:@selector(bottomToolbarDidLongPress:forToolbarGroup:)]) {
                [self.delegate bottomToolbarDidLongPress:self.addTabButton forToolbarGroup:ToolbarGroupNew];
            }
        }
    }];
    
    UILongPressGestureRecognizer* longPress = [UILongPressGestureRecognizer new];
    [self.tabsButton addGestureRecognizer:longPress];
    
    UITapGestureRecognizer* tap = [UITapGestureRecognizer new];
    [self.tabsButton addGestureRecognizer:tap];
    [tap requireGestureRecognizerToFail:longPress];
    
    [[longPress rac_gestureSignal] subscribeNext:^(UILongPressGestureRecognizer *gesture) {
        @strongify(self)
        if (gesture.state == UIGestureRecognizerStateBegan) {            
            if(self.delegate && [self.delegate respondsToSelector:@selector(bottomToolbarDidLongPress:forToolbarGroup:)]) {
                [self.delegate bottomToolbarDidLongPress:self.tabsButton forToolbarGroup:ToolbarGroupTabs];
            }
        }
    }];
    
    [[tap rac_gestureSignal] subscribeNext:^(id x) {
        @strongify(self)
        if(self.delegate && [self.delegate respondsToSelector:@selector(bottomToolbarDidClickTabs)]) {
            [self.delegate bottomToolbarDidClickTabs];
        }
    }];
    
    [self.menuButton setTapAction:^{
        @strongify(self)
        if(self.delegate && [self.delegate respondsToSelector:@selector(bottomToolbarDidClickMenu:)]) {
            [self.delegate bottomToolbarDidClickMenu:YES];
        }
    }];
    
    // 添加功能按钮长按手势
    UILongPressGestureRecognizer* menuLongPress = [[UILongPressGestureRecognizer alloc] init];
    [self.menuButton addGestureRecognizer:menuLongPress];
    [self.menuButton.tapGesture requireGestureRecognizerToFail:menuLongPress];
    [[menuLongPress rac_gestureSignal] subscribeNext:^(UILongPressGestureRecognizer *gesture) {
        @strongify(self)
        if (gesture.state == UIGestureRecognizerStateBegan) {
            if(self.delegate && [self.delegate respondsToSelector:@selector(bottomToolbarDidLongPress:forToolbarGroup:)]) {
                [self.delegate bottomToolbarDidLongPress:self.menuButton forToolbarGroup:ToolbarGroupFeatures];
            }
        }
    }];
    
    // 首页按钮
    [self.homeButton setTapAction:^{
        @strongify(self)
        if(self.delegate && [self.delegate respondsToSelector:@selector(bottomToolbarDidClickHome)]) {
            [self.delegate bottomToolbarDidClickHome];
        }
    }];
    
    // 添加主页按钮长按手势
    UILongPressGestureRecognizer* homeLongPress = [[UILongPressGestureRecognizer alloc] init];
    [self.homeButton addGestureRecognizer:homeLongPress];
    [self.homeButton.tapGesture requireGestureRecognizerToFail:homeLongPress];
    [[homeLongPress rac_gestureSignal] subscribeNext:^(UILongPressGestureRecognizer *gesture) {
        @strongify(self)
        if (gesture.state == UIGestureRecognizerStateBegan) {
            if(self.delegate && [self.delegate respondsToSelector:@selector(bottomToolbarDidLongPress:forToolbarGroup:)]) {
                [self.delegate bottomToolbarDidLongPress:self.homeButton forToolbarGroup:ToolbarGroupHome];
            }
        }
    }];
    
//    [RACObserve([PreferenceManager shareInstance].items, isShowSearchEngineBar) subscribeNext:^(id x) {
//        @strongify(self)
//        [self _updateShowSearchEngineBar];
//    }];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(_updateWallpaper)
                                                 name:kUpdateWallpaperNotification
                                               object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(_toolbarDidChange)
                                                 name:kToolbarDidChangeNotification
                                               object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(darkThemeDidChangeNotification:)
                                                 name:kDarkThemeDidChangeNotification
                                               object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(viewDidSafeAreaDidChange)
                                                 name:kViewSafeAreaInsetsDidChangeNotification
                                               object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(viewDidSafeAreaDidChange)
                                                 name:kOrientationDidChangeNotification
                                               object:nil];
}

#pragma mark - getters

- (UIStackView *)stackView
{
    if(!_stackView) {
        _stackView = [[UIStackView alloc]initWithArrangedSubviews:self.optionViews];
        
        _stackView.axis = UILayoutConstraintAxisHorizontal;
        _stackView.distribution = UIStackViewDistributionFillEqually;
        
        //2.6.8 阿拉伯语布局适配
        [NSObject rtlLayoutSupportWithViews:@[_stackView]];
    }
    
    return _stackView;
}

- (CustomTitleAndImageView *)createButtonForiPadWithImageName:(NSString *)imageName
{
    //iPad
    CustomTitleAndImageView* button = [[CustomTitleAndImageView alloc] initWithLayout:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
        [imageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.center.mas_offset(0);
            make.size.mas_equalTo(30);
        }];
        
        UIImage* image = [UIImage imageNamed:imageName];
        image = [image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        imageView.image = image;
    }];
        
    return button;
}

- (CustomTitleAndImageView *)createButtonWithImageName:(NSString *)imageName
                                                height:(float)height
{
    //iPhone
    CustomTitleAndImageView* button = [[CustomTitleAndImageView alloc] initWithLayout:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
        [imageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.center.mas_offset(0);
            //等高度，宽度自适应
            make.size.mas_equalTo(height);
        }];
        
        UIImage* image = [UIImage imageNamed:imageName];
        image = [image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        imageView.image = image;
        imageView.contentMode = UIViewContentModeScaleAspectFit;
    }];
    
    return button;
}

- (CustomTitleAndImageView *)backButton
{
    if(!_backButton) {
        if([BrowserUtils isiPad]) {
            _backButton = [self createButtonForiPadWithImageName:@"navc_back"];
        } else {
            _backButton = [self createButtonWithImageName:@"back" height:20];
        }
        _backButton.imageView.tintColor = [UIColor colorWithHexString:@"#999999"];
    }
    
    return _backButton;
}

- (CustomTitleAndImageView *)forwardButton
{
    if(!_forwardButton) {
        if([BrowserUtils isiPad]) {
            _forwardButton = [self createButtonForiPadWithImageName:@"navc_forward"];
        } else {
            _forwardButton = [self createButtonWithImageName:@"forward" height:20];
        }
        _forwardButton.imageView.tintColor = [UIColor colorWithHexString:@"#999999"];
        
        _forwardButton.imageView.tintColor = [UIColor colorWithHexString:@"#999999"];
    }
    
    return _forwardButton;
}

- (TabsButton *)tabsButton
{
    if(!_tabsButton) {
        _tabsButton = [TabsButton new];
    }
    
    return _tabsButton;
}

- (CustomTitleAndImageView *)addTabButton
{
    if(!_addTabButton) {
        if([BrowserUtils isiPad]) {
            _addTabButton = [self createButtonForiPadWithImageName:@"navc_add"];
        } else {
            //https://yesicon.app/uiw/plus
            _addTabButton = [self createButtonWithImageName:@"plus" height:20];
        }
        
        _addTabButton.imageView.tintColor = [UIColor colorWithHexString:@"#333333"];
    }
    
    return _addTabButton;
}

- (CustomTitleAndImageView *)menuButton
{
    if(!_menuButton) {
        if([BrowserUtils isiPad]) {
            _menuButton = [self createButtonForiPadWithImageName:@"navc_menu"];
        } else {
//            _menuButton = [self createButtonWithImageName:@"list-panel" height:18];
            //https://yesicon.app/streamline/interface-setting-menu-1-button-parallel-horizontal-lines-menu-navigation-three-hamburger
            _menuButton = [self createButtonWithImageName:@"menu" height:20];
        }
        
        _menuButton.imageView.tintColor = [UIColor colorWithHexString:@"#333333"];
    }
    
    return _menuButton;
}

- (CustomTitleAndImageView *)homeButton
{
    if(!_homeButton) {        
        //https://yesicon.app/akar-icons/home
        _homeButton = [self createButtonWithImageName:@"menu_home_icon" height:22];
        _homeButton.imageView.tintColor = [UIColor colorWithHexString:@"#333333"];
    }
    
    return _homeButton;
}

- (UIView *)line
{
    if(!_line) {
        _line = [UIView new];
        _line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        _line.hidden = YES;
    }
    
    return _line;
}

- (NSMutableArray *)optionViews
{
    if(!_optionViews) {
        _optionViews = [NSMutableArray array];
    }
    
    return _optionViews;
}

@end
