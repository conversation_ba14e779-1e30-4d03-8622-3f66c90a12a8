//
//  FindInPageBar.h
//  PPBrowser
//
//  Created by qing<PERSON> on 2022/4/4.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <UIKit/UIKit.h>

@class FindInPageBar;
@class Tab;

@protocol FindInPageBarDelegate <NSObject>

- (void)findInPageBar:(FindInPageBar*)findInPage didTextChange:(NSString*)text;

- (void)findInPageBar:(FindInPageBar*)findInPage didFindPreviousWithText:(NSString*)text;

- (void)findInPageBar:(FindInPageBar*)findInPage didFindNextWithText:(NSString*)text;

- (void)findInPageDidPressClose:(FindInPageBar*)findInPage;

@end

//https://www.jianshu.com/p/0dc4e1b05c60
@interface FindInPageBar : UIView

- (instancetype)initWithTab:(Tab*)tab;

- (void)updateWithText:(NSString*)text;
- (BOOL)becomeFirstResponder;

@property(nonatomic,weak) id<FindInPageBarDelegate> delegate;
@property(nonatomic,weak) Tab *tab;

@end

