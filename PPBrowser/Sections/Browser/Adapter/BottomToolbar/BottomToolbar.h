//
//  BottomToolbar.h
//  PandaBrowser
//
//  Created by qing<PERSON> on 2022/3/3.
//

#import <UIKit/UIKit.h>

#import "WallpaperModel.h"
#import "PreferenceManager.h"
#import "ThemeProtocol.h"
#import "PPEnums.h"
#import "CustomTitleAndImageView.h"

@protocol BottomToolbarDelegate <NSObject>

// 点击返回按钮
- (void)bottomToolbarDidClickBack;
// 点击前进按钮
- (void)bottomToolbarDidClickForward;
// 点击新建标签页
- (void)bottomToolbarDidClickAddTab;
// 点击了tabs
- (void)bottomToolbarDidClickTabs;
// 点击功能区
- (void)bottomToolbarDidClickMenu:(BOOL)isTriggerByBottomBar;
// 点击首页
- (void)bottomToolbarDidClickHome;
// v2.6.8,工具栏长按手势
- (void)bottomToolbarDidLongPress:(UIView*)button forToolbarGroup:(ToolbarGroup)group;

@end

@interface BottomToolbar : UIView<ThemeProtocol>

@property (nonatomic, weak) id<BottomToolbarDelegate> delegate;
@property (readonly) CustomTitleAndImageView* menuButton;

- (void)updateGoBackStatus:(BOOL)canGoBack;
- (void)updateGoForwardStatus:(BOOL)canGoForward;

//更新当前页面是否是首页,壁纸相关逻辑
- (void)updateIsHomePage:(BOOL)isHomePage;
- (void)updateWithWallpaper:(WallpaperModel *)model;

//更新标签页的个数
- (void)updateTabCount:(int)count;
//isHomePage:是否是首页
- (void)show:(BOOL)animated isHomePage:(BOOL)isHomePage;

- (void)dismiss:(BOOL)animated;

// 控件高度
+ (float)toolbarHeight;

//根据option配置底部状态栏
- (void)configureWithOption:(ToolbarOption)option;

// 工具栏屏幕旋转适配
- (void)updateToolBarLayoutsToSize:(CGSize)size;

@end

