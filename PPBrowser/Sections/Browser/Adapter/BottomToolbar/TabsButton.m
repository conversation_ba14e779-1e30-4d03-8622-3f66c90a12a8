//
//  TabsButton.m
//  PPBrowser
//
//  Created by qingbin on 2022/8/28.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "TabsButton.h"

#import "Masonry.h"
#import "ReactiveCocoa.h"

#import "UIColor+Helper.h"
#import "NSObject+Helper.h"
#import "MaizyHeader.h"

#import "UIView+Helper.h"
#import "BrowserUtils.h"

@interface TabsButton ()

@property (nonatomic, strong) UILabel* label;

@property (nonatomic, strong) UIView* borderView;

@property (nonatomic, strong) UIImageView* logo;

@end

@implementation TabsButton

- (instancetype)init
{
    self = [super init];
    if(self) {
        [self addSubviews];
        [self defineLayout];
    }
    
    return self;
}

- (void)applyTheme:(BOOL)isDarkTheme
{
    if(isDarkTheme) {
        //暗黑模式(是否显示暗黑模式由toolbar决定)
        self.logo.tintColor = [UIColor colorWithHexString:@"#ffffff"];
        self.label.textColor = UIColor.whiteColor;
        self.borderView.layer.borderColor = UIColor.whiteColor.CGColor;
    } else {
        //非暗黑模式
        self.logo.tintColor = [UIColor colorWithHexString:@"#333333"];
        self.label.textColor = [UIColor colorWithHexString:@"#333333"];
        self.borderView.layer.borderColor = [UIColor colorWithHexString:@"#333333"].CGColor;
    }
}

- (void)updateTabCount:(int)count
{
    int total = MAX(count, 0);
    
    if(total < 100) {
        self.logo.hidden = YES;
        
        self.borderView.hidden = NO;
        self.label.hidden = NO;
        self.label.text = [NSString stringWithFormat:@"%d",total];
    } else {
        self.logo.hidden = NO;
        
        self.borderView.hidden = YES;
        self.label.hidden = YES;
    }
}

- (void)addSubviews
{
    [self addSubview:self.borderView];
    [self addSubview:self.label];
    [self addSubview:self.logo];
}

- (void)defineLayout
{
    float size = iPadValue(25, 20);
    [self.borderView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self);
        make.size.mas_equalTo(size);
    }];
    
    [self.label mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self);
    }];
    
    [self.logo mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self);
    }];
}

- (UIView *)borderView
{
    if(!_borderView) {
        _borderView = [UIView new];
        _borderView.backgroundColor = UIColor.clearColor;
        _borderView.layer.borderWidth = 1.5;
        _borderView.layer.borderColor = [UIColor colorWithHexString:@"#333333"].CGColor;
        _borderView.layer.cornerRadius = 2;
        _borderView.layer.masksToBounds = YES;
        _borderView.userInteractionEnabled = NO;
    }
    
    return _borderView;
}

- (UILabel *)label
{
    if(!_label) {
        float font = iPadValue(15, 11);
        _label = [UIView createLabelWithTitle:@""
                                    textColor:[UIColor colorWithHexString:@"#333333"]
                                      bgColor:UIColor.clearColor
                                     fontSize:font
                                textAlignment:NSTextAlignmentCenter
                                        bBold:YES];
    }
    
    return _label;
}

- (UIImageView *)logo
{
    if(!_logo) {
        _logo = [UIImageView new];
        UIImage* image = [UIImage imageNamed:@"menu_tabtray_icon"];
        image = [image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        _logo.tintColor = [UIColor colorWithHexString:@"#333333"];//默认
        _logo.image = image;
        _logo.hidden = YES;
    }
    
    return _logo;
}

@end
