//
//  LinkPreviewController.m
//  PPBrowser
//
//  Created by qingbin on 2022/7/19.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "LinkPreviewController.h"

#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"

#import "InternalSchemeHandler.h"
#import "InternalURL.h"

#import <WebKit/WebKit.h>

@interface LinkPreviewController ()

@property (nonatomic, strong) NSURL *URL;

@end

@implementation LinkPreviewController

- (instancetype)initWithURL:(NSURL *)URL
{
    self = [super init];
    if(self) {
        self.URL = URL;
    }
    
    return self;
}

- (void)viewDidLoad
{
    [super viewDidLoad];

    WKWebViewConfiguration* configuration = [WKWebViewConfiguration new];
    configuration.processPool = [WKProcessPool new];
    configuration.preferences.javaScriptCanOpenWindowsAutomatically = false;
    
    if(![configuration urlSchemeHandlerForURLScheme:[InternalURL scheme]]) {
        InternalSchemeHandler *handler = [InternalSchemeHandler new];
        [configuration setURLSchemeHandler:handler forURLScheme:[InternalURL scheme]];
    }
    
    WKWebView* webView = [[WKWebView alloc]initWithFrame:self.view.frame configuration:configuration];
    [self.view addSubview:webView];
    
    [webView loadRequest:[NSURLRequest requestWithURL:self.URL]];
}

@end
