//
//  BrowserViewController+FindInPageBarDelegate.m
//  PPBrowser
//
//  Created by qingbin on 2022/4/4.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "BrowserViewController+FindInPageBarDelegate.h"
#import "FindInPageHelper.h"

@implementation BrowserViewController (FindInPageBarDelegate)

- (void)findInPageBar:(FindInPageBar*)findInPage didTextChange:(NSString*)text
{
    WKWebView* webView = findInPage.tab.webView;
    [FindInPageHelper findInPageInWebView:webView keyword:text];
}

- (void)findInPageBar:(FindInPageBar*)findInPage didFindPreviousWithText:(NSString*)text
{
    WKWebView* webView = findInPage.tab.webView;
    [FindInPageHelper findPreviousInWebView:webView keyword:text];
}

- (void)findInPageBar:(FindInPageBar*)findInPage didFindNextWithText:(NSString*)text
{
    WKWebView* webView = findInPage.tab.webView;
    [FindInPageHelper findNextInWebView:webView keyword:text];
}

- (void)findInPageDidPressClose:(FindInPageBar*)findInPage
{
    WKWebView* webView = findInPage.tab.webView;
    [FindInPageHelper findDoneWithWebView:webView];
}

@end
