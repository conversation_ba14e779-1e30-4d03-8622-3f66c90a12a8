//
//  BrowserViewController+PreferenceViewDelegate.m
//  PPBrowser
//
//  Created by qing<PERSON> on 2022/4/25.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "BrowserViewController+PreferenceViewDelegate.h"
#import "PreferenceItemModel.h"

#import "PPEnums.h"
#import "PPNotifications.h"

#import "Tab.h"
#import "PreferenceManager.h"

#import "FindInPageHelper.h"
#import "GetAllImageHelper.h"

#import "NSObject+Helper.h"
#import "UIView+Helper.h"
#import "PPBrowser-Swift.h"

#import "HistoryViewController.h"
#import "BookMarkController.h"
#import "BaseNavigationController.h"
#import "BookMarkMoveController.h"

#import "DatabaseUnit+BookMark.h"
#import "BookMarkModel.h"

#import "ReactiveCocoa.h"

#import "UserScriptController.h"
#import "Tampermonkey.h"
//#import "PopUpUserAgentSelectView.h"
#import "Masonry.h"
#import "UIColor+Helper.h"

#import "CustomTagModel.h"
#import "DatabaseUnit+CustomTag.h"
#import "AddCustomTagController.h"

#import "SettingViewController.h"

#import "MaizyHeader.h"

#import <MessageUI/MessageUI.h>
#import "OpenUDID.h"
#import "WebViewController.h"
#import "SystemScriptManager.h"

#import "BrowserUtils.h"
#import "UserAgentController.h"

#import "ContentBlockerHelper.h"
#import "ASDFilter.h"
#import "AESFilterConverter.h"
#import "AECFilterParser.h"

#import "TagitView.h"

#import "FontStepView.h"

#import "WebBlacklistHelper.h"
#import "CommonDataManager.h"

#import "InternalURL.h"
#import "CopyrightHelper.h"

#import "FavorEditController.h"
#import "AutoPageViewController.h"

#import "TrialManager.h"
#import "UIAlertController+SafePresentation.h"
#import "IncognitoViewController.h"
#import "NSFileManager+Helper.h"

@implementation BrowserViewController (PreferenceViewDelegate)

- (void)preferenceViewDidSelectItem:(PreferenceItemModel*)item
{
    [self hideUserPreferenceView];
    
    if(item.itemType == PreferenceItemTypeNoImage) {
        [self handleNoImageMode];
    } else if(item.itemType == PreferenceItemTypeUA) {
        [self handleUserAgent];
    } else if(item.itemType == PreferenceItemTypeFindInPage) {
        [self handleFindInPage];
    } else if(item.itemType == PreferenceItemTypeImageMode) {
        [self handleGetAllImageMode];
    } else if(item.itemType == PreferenceItemTypeScreenshot) {
        [self handleScreenshotHelper];
    } else if(item.itemType == PreferenceItemTypeHistory) {
        [self handleHistoryController];
    } else if(item.itemType == PreferenceItemTypeAddBookMark) {
        [self handleBookMarkAndTagAction];
    } else if(item.itemType == PreferenceItemTypeBookMark) {
        [self handleBookMarkController];
    } else if(item.itemType == PreferenceItemTypePrivacy) {
        [self handlePrivateMode];
    } else if(item.itemType == PreferenceItemTypeJS) {
        [self handleJS];
    } else if(item.itemType == PreferenceItemTypeRefresh) {
        [self handleRefresh];
    } else if(item.itemType == PreferenceItemTypeSetting) {
        [self handleSetting];
    } else if(item.itemType == PreferenceItemTypeFullScreen) {
        [self handleSwitchFullScreen];
    } else if(item.itemType == PreferenceItemTypeFeedback) {
        [self jumpToFeedback];
    } else if(item.itemType == PreferenceItemTypeNightMode) {
        [self handleDarkTheme];
    } else if(item.itemType == PreferenceItemTypeTagit) {
        [self handleTagit];
    } else if(item.itemType == PreferenceItemTypeShare) {
        [self handleShareToFriends];
    } else if(item.itemType == PreferenceItemTypeFont) {
        [self handleFontChange];
    } else if(item.itemType == PreferenceItemTypeToolBox) {
        [self handleToolBox];
    } else if(item.itemType == PreferenceItemTypeWebViewBlacklist) {
        [self handleWebViewBlacklist];
    } else if(item.itemType == PreferenceItemTypeTranslate) {
        [self handleTranslate];
    } else if(item.itemType == PreferenceItemTypeAutoPage) {
        [self handleSwitchAutoPage];
    } else if(item.itemType == PreferenceItemTypeDownload) {
        //v2.7.4，打开本地下载文件夹
        [self openDownload];
    }
}

//v2.7.4，打开本地下载文件夹
- (void)openDownload
{
    [NSFileManager openFocusDownloadsFolder];
}

//翻译
- (void)handleTranslate
{
//    NSString *js = [NSString stringWithFormat:@"window.startTranslate('auto', 'zh')"];
//    [webView evaluateJavaScript:js completionHandler:nil];
    
    //"<a i=0>Widespread riots have been taking place in the United Kingdom</a><a i=1>following the stabbings of three young girls</a><a i=2>at a</a><a i=3>Taylor Swift</a><a i=4>themed dance event in Southport on 29 July.</a>"
    
    //"<a i=4>7 月 29 日，在南港举行的<\/a><a i=2>一场<\/a><a i=3>泰勒·斯威夫特<\/a>主题舞蹈活动中，<a i=1>三名年轻女孩被刺伤，此后<\/a><a i=0>英国发生了大规模骚乱<\/a>。"
    
    //["", "", "", "主题舞蹈活动中，", "7 月 29 日，在南港举行的一场泰勒·斯威夫特三名年轻女孩被刺伤，此后英国发生了大规模骚乱。"]
    
    [self showTranslateToolbar];
}

// 分享链接
- (void)handleShareToFriends
{
    NSURL* urlToShare = self.tabManager.selectedTab.webView.URL;
    NSArray* items = @[urlToShare];
    
    UIActivityViewController *vc = [[UIActivityViewController alloc] initWithActivityItems:items applicationActivities:nil];
    vc.completionWithItemsHandler = ^(UIActivityType __nullable activityType, BOOL completed, NSArray * __nullable returnedItems, NSError * __nullable activityError) {
    };
    
    if([BrowserUtils isiPad]) {
        //iPad
        vc.popoverPresentationController.sourceView = self.view;
        vc.popoverPresentationController.sourceRect = CGRectMake(CGRectGetMidX(self.view.frame), CGRectGetMidY(self.view.frame), 0, 0);
    }
    
    [self presentViewController:vc animated:YES completion:nil];
}

// 无图模式
- (void)handleNoImageMode
{    
    Tab* tab = self.tabManager.selectedTab;
    WKWebView* webView = tab.webView;
    
    BOOL enabled = [[PreferenceManager shareInstance].items.isNoImage boolValue];
    enabled = !enabled;
    
    //无图模式不写入到plist
    [PreferenceManager shareInstance].items.isNoImage = @(enabled);
    
    [[ContentBlockerHelper shareInstance] getBlockImagesRule:^(NSArray<WKContentRuleList *> * _Nonnull rules) {
        if(rules.count <= 0) return;

        WKContentRuleList* rule = rules.firstObject;

        dispatch_async(dispatch_get_main_queue(), ^{
            if(enabled) {
                [webView.configuration.userContentController addContentRuleList:rule];
            } else {
                [webView.configuration.userContentController removeContentRuleList:rule];
            }

            NSString* js = [NSString stringWithFormat:@"window.__firefox__.NoImageMode.setEnabled(\"%@\")",@(enabled)];
            [webView evaluateJavaScript:js completionHandler:^(id _Nullable obj, NSError * _Nullable error) {
                if(error) {
                    LOG_ERROR(@"error = %@", error.localizedDescription);
                } else {
                    [webView reloadFromOrigin];
                }
            }];
        });
    }];
}

// UserAgent
- (void)handleUserAgent
{    
    UserAgentController* vc = [[UserAgentController alloc] init];
    BaseNavigationController* navc = [[BaseNavigationController alloc] initWithRootViewController:vc];
    
    if ([BrowserUtils isiPhone] && ![[BrowserUtils shareInstance] isLandscape]) {
        // iPhone竖屏 - 使用半模态弹窗
        if (@available(iOS 15.0, *)) {
            UISheetPresentationController* sheet = navc.sheetPresentationController;
            sheet.detents = @[
                UISheetPresentationControllerDetent.mediumDetent,
                UISheetPresentationControllerDetent.largeDetent
            ];
            //            sheet.prefersGrabberVisible = YES;
            sheet.preferredCornerRadius = 10;
        }
        [self presentViewController:navc animated:YES completion:nil];
        
    } else if ([BrowserUtils isiPad]) {
        // iPad
//        navc.modalPresentationStyle = UIModalPresentationFormSheet;
//        vc.preferredContentSize = CGSizeMake(kScreenWidth-90, kScreenHeight-90);
//        
//        [self presentViewController:navc animated:YES completion:nil];
        //v2.6.8,统一present
        [self presentCustomToViewController:navc];
    } else {
        // iPhone横屏/iPad - 使用普通Push
        [self.navigationController pushViewController:vc animated:YES];
    }
}

// 页面查找
- (void)handleFindInPage
{
    [self updateFindInPageVisibility:YES text:@""];
}

// 看图模式
- (void)handleGetAllImageMode
{
    Tab* tab = self.tabManager.selectedTab;
    [GetAllImageHelper getAllImageWithWebView:tab.webView];
}

- (void)_translateToLanguage:(NSString*)keyword
{
    Tab* tab = self.tabManager.selectedTab;
    
    NSString* js = [NSString stringWithFormat:@"window.__firefox__.TranslateHandler(\'%@\')",keyword];
    [tab.webView evaluateJavaScript:js completionHandler:^(id _Nullable obj, NSError * _Nullable error) {
        if(error) {
            LOG_ERROR(@"error = %@", error.localizedDescription);
        }
    }];
}

- (void)handleScreenshotHelper
{
    //https://juejin.cn/post/6844903680055967757
    
    WKWebView* webView = self.tabManager.selectedTab.webView;
    if(!webView) return;
    
    [webView.scrollView asyncTakeSnapshotOfFullContent:^(UIImage * image) {
        if(image) {
            [CommonDataManager shareInstance].screenshotImage = image;
            UIImageWriteToSavedPhotosAlbum(image, self, @selector(saveImage:didFinishSavingWithError:contextInfo:), nil);
        }
    }];
}

- (void)saveImage:(UIImage*)image didFinishSavingWithError:(NSError*)error contextInfo:(id)contextInfo
{
    if(error) {
        UIImage* screenshotImage = [CommonDataManager shareInstance].screenshotImage;
        [CommonDataManager shareInstance].screenshotImage = nil;
        if(screenshotImage) {
            //图片太大，改成发送文件
            NSString *tmpFilePath = [self saveImageToTmpDirectory:screenshotImage];
            
            if (tmpFilePath) {
                [self sendFileToDocumentApp:tmpFilePath];
            } else {
                NSLog(@"Failed to save image to tmp directory!");
            }
        } else {
            [UIView showFailed:NSLocalizedString(@"tips.savefail", nil)];
        }
    } else {
        //totast
        [UIView showSucceed:NSLocalizedString(@"tips.photo.savesuccess", nil)];
        
        [CommonDataManager shareInstance].screenshotImage = nil;
    }
}

// 保存UIImage到Library/tmp目录
- (NSString *)saveImageToTmpDirectory:(UIImage *)image
{
    NSString *tmpDirectory = NSTemporaryDirectory();
    NSString *filePath = [tmpDirectory stringByAppendingPathComponent:@"tempImage.png"];
    [self deleteTmpFile:filePath];
    
//    NSData *imageData = UIImageJPEGRepresentation(image, 1.0); // 使用最大质量保存图片
    NSData *imageData = UIImagePNGRepresentation(image);
    BOOL success = [imageData writeToFile:filePath atomically:YES];
    
    if (success) {
        return filePath;
    } else {
        return nil;
    }
}

// 发送文件到文件APP中
- (void)sendFileToDocumentApp:(NSString *)filePath
{
    NSURL *fileURL = [NSURL fileURLWithPath:filePath];
    NSArray *activityItems = @[fileURL];
    
    UIActivityViewController *activityViewController = [[UIActivityViewController alloc] initWithActivityItems:activityItems applicationActivities:nil];    
    if (UI_USER_INTERFACE_IDIOM() == UIUserInterfaceIdiomPad) {
        activityViewController.popoverPresentationController.sourceView = self.view;
    }
    
    @weakify(self)
    [activityViewController setCompletionWithItemsHandler:^(UIActivityType __nullable activityType, BOOL completed, NSArray * __nullable returnedItems, NSError * __nullable activityError) {
        @strongify(self)
        if(!activityError && completed) {
            [UIView showSucceed:NSLocalizedString(@"tips.savesuccess", nil)];
        } else {
            [UIView showFailed:NSLocalizedString(@"tips.savefail", nil)];
        }
        
        [self deleteTmpFile:filePath];
    }];
    
    [self presentViewController:activityViewController animated:YES completion:nil];
}

// 删除临时文件
- (void)deleteTmpFile:(NSString *)filePath
{
    NSError *error;
    if([[NSFileManager defaultManager] fileExistsAtPath:filePath]) {
        [[NSFileManager defaultManager] removeItemAtPath:filePath error:&error];
    }
    
    if (error) {
        NSLog(@"Error deleting tmp file: %@", error.localizedDescription);
    }
}

// 将3x图片缩放为1x图片
//UIImage *scaleImageTo1x(UIImage *image, float newScale) {
//    CGFloat scale = newScale / 3.0;
//    CGSize newSize = CGSizeMake(image.size.width * scale, image.size.height * scale);
//
//    UIGraphicsBeginImageContextWithOptions(newSize, NO, 1.0);
//    [image drawInRect:CGRectMake(0, 0, newSize.width, newSize.height)];
//    UIImage *scaledImage = UIGraphicsGetImageFromCurrentImageContext();
//    UIGraphicsEndImageContext();
//
//    return scaledImage;
//}

//查询历史
- (void)handleHistoryController
{
    HistoryViewController* vc = [[HistoryViewController alloc] initWithTab:self.tabManager.selectedTab];
    [self.navigationController pushViewController:vc animated:YES];
}

//判断是否需要添加书签还是首页标签相关逻辑
- (void)handleBookMarkAndTagAction
{
    //新版本,同时支持添加到首页标签和书签
    
    Tab* tab = self.tabManager.selectedTab;
    if(!tab) return;
    if([InternalURL isAboutHomeURL:tab.webView.URL.absoluteString]) {
        [UIView showToast:NSLocalizedString(@"tips.tap.home", nil)];
        return;
    }
    
    NSMutableArray* actions = [NSMutableArray array];
    UIAlertAction* action;
    
    CustomTagModel* model = [CommonDataManager shareInstance].currentCustomTagModel;
    if(!model) {
        //添加到首页
        action = [UIAlertAction actionWithTitle:NSLocalizedString(@"addCustomTag.title", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            [self toolViewAddHomeAction];
        }];
        [actions addObject:action];
    } else {
        //编辑首页标签
        action = [UIAlertAction actionWithTitle:NSLocalizedString(@"addCustomTag.edit.home", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            [self toolViewAddHomeAction];
        }];
        [actions addObject:action];
    }
    
    BookMarkModel* bookmarkModel = [CommonDataManager shareInstance].currentBookMarkModel;
    if(!bookmarkModel) {
        //添加到书签
        action = [UIAlertAction actionWithTitle:NSLocalizedString(@"addCustomTag.add.bookmark", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            [self toolViewAddBookMarkAction];
        }];
        [actions addObject:action];
    } else {
        //编辑书签
        action = [UIAlertAction actionWithTitle:NSLocalizedString(@"addCustomTag.bookmark.title", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            [self toolViewAddBookMarkAction];
        }];
        [actions addObject:action];
    }

    UIAlertController* alertController = [UIAlertController alertControllerWithTitle:nil message:nil preferredStyle:UIAlertControllerStyleActionSheet];
    action = [UIAlertAction actionWithTitle:NSLocalizedString(@"common.cancel", nil) style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
        [alertController dismissViewControllerAnimated:YES completion:nil];
    }];
    [actions addObject:action];
        
    for(UIAlertAction* action in actions) {
        [alertController addAction:action];
    }
    
    //iPhone
//    if([BrowserUtils isiPhone] && ![[BrowserUtils shareInstance] isLandscape]){
//        //竖屏iPhone
//        [self presentViewController:alertController animated:YES completion:nil];
//    } else {
//        //iPad
//        //横屏iPhone,需要确定箭头的位置
//        
//        CGRect sourceRect = CGRectZero;
//        //顶部工具栏触发
//        UIView* button = self.topToolbar.appMenuButton;
//        sourceRect = [button.superview convertRect:button.frame toView:self.view];
//        
//        UIViewController* controller = self;
//        alertController.modalPresentationStyle = UIModalPresentationPopover;
//        alertController.popoverPresentationController.sourceView = controller.view;
//        alertController.popoverPresentationController.sourceRect = sourceRect;
//        alertController.popoverPresentationController.permittedArrowDirections = UIPopoverArrowDirectionAny;
//    
//        [controller presentViewController:alertController animated:YES completion:nil];
//    }
    
    CGRect sourceRect = CGRectZero;
    //顶部工具栏触发
    UIView* button = self.topToolbar.appMenuButton;
    sourceRect = [button.superview convertRect:button.frame toView:self.view];
    //v2.6.8, 统一present方法，防止崩溃
    [alertController presentSafelyFromViewController:self sourceView:self.view sourceRect:sourceRect];
}

//添加首页
- (void)toolViewAddHomeAction
{
    Tab* tab = self.tabManager.selectedTab;
    if(!tab) return;
    if([InternalURL isAboutHomeURL:tab.webView.URL.absoluteString]) {
        [UIView showToast:NSLocalizedString(@"tips.tap.home", nil)];
        return;
    }
    
    CustomTagModel* model = [CommonDataManager shareInstance].currentCustomTagModel;
    if(!model) {
        model = [CustomTagModel new];
        model.uuid = [[NSUUID UUID] UUIDString];
        model.title = tab.webView.title;
        model.targetUrl = tab.webView.URL.absoluteString;
        model.type = 0;
        model.ppOrder = INT_MAX;
    }
    FavorEditController* vc = [[FavorEditController alloc]init];
    [vc updateWithFavorModel:model tab:self.tabManager.selectedTab];
    BaseNavigationController* navc = [[BaseNavigationController alloc]initWithRootViewController:vc];
    
    //15.0的开启sheet模式
    if(@available(iOS 15.0,*)) {
        UISheetPresentationController* sheet = navc.sheetPresentationController;
        sheet.detents = @[
            UISheetPresentationControllerDetent.mediumDetent,
            UISheetPresentationControllerDetent.largeDetent,
        ];
        sheet.preferredCornerRadius = 10;
    }

//    [self presentViewController:navc animated:YES completion:nil];
    //v2.6.8,统一present
    [self presentCustomToViewController:navc];
}

//添加书签
- (void)toolViewAddBookMarkAction
{
    Tab* tab = self.tabManager.selectedTab;
    if(!tab) return;
    if([InternalURL isAboutHomeURL:tab.webView.URL.absoluteString]) {
        [UIView showToast:NSLocalizedString(@"tips.tap.home", nil)];
        return;
    }

    BookMarkModel* model = [CommonDataManager shareInstance].currentBookMarkModel;
    if(!model) {
        model = [BookMarkModel new];
        model.bookmarkId = [[NSUUID UUID] UUIDString];
        model.title = tab.webView.title;
        model.url = tab.webView.URL.absoluteString;
        model.ppOrder = INT_MAX;
        model.fileType = BookMarkTypeFile;
        model.parentId = [BookMarkModel rootId];
    }
    FavorEditController* vc = [[FavorEditController alloc]init];
    [vc updateWithBookMarkModel:model tab:tab];
    BaseNavigationController* navc = [[BaseNavigationController alloc]initWithRootViewController:vc];
    
    //15.0的开启sheet模式
    if(@available(iOS 15.0,*)) {
        UISheetPresentationController* sheet = navc.sheetPresentationController;
        sheet.detents = @[
            UISheetPresentationControllerDetent.mediumDetent,
            UISheetPresentationControllerDetent.largeDetent,
        ];
        sheet.preferredCornerRadius = 10;
    }

//    [self presentViewController:navc animated:YES completion:nil];
    //v2.6.8,统一present
    [self presentCustomToViewController:navc];
}

// 添加书签
- (void)handleAddBookMark
{
    Tab* tab = self.tabManager.selectedTab;
    if(!tab || !tab.webView) return;
    
    BookMarkModel* item = [BookMarkModel new];
    item.bookmarkId = [[NSUUID UUID] UUIDString];
    item.url = [tab.webView.URL absoluteString];
    item.title = tab.webView.title;
    item.fileType = BookMarkTypeFile;
    item.parentId = [BookMarkModel rootId];
    
    BookMarkMoveController* vc = [BookMarkMoveController new];
    vc.items = @[item];
    
    [vc setFinishBlock:^(BookMarkModel *selectTarget) {
        item.parentId = selectTarget.bookmarkId;
        DatabaseUnit* unit = [DatabaseUnit addBookMarkWithItem:item];
        [unit setCompleteBlock:^(id result, BOOL success) {
            if(success) {
                [UIView showSucceed:NSLocalizedString(@"addCustomTag.addSucceed", nil)];
            }
        }];
        DB_EXEC(unit);
    }];
    
    BaseNavigationController* navc = [[BaseNavigationController alloc]initWithRootViewController:vc];
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        navc.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
    } else {
        navc.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
    [self presentViewController:navc animated:YES completion:nil];
}

//查看书签页
- (void)handleBookMarkController
{
    [[NSNotificationCenter defaultCenter] postNotificationName:kOpenBookMarkNotification object:nil];
}

//无痕模式
- (void)handlePrivateMode
{
//    BOOL enabled = [[PreferenceManager shareInstance].items.isPrivate boolValue];
//    enabled = !enabled;
//    [PreferenceManager shareInstance].items.isPrivate = @(enabled);
//        
//    [[PreferenceManager shareInstance] encode];
    
    //v2.7.4，无痕模式改为弹窗的形式
    IncognitoViewController* vc = [[IncognitoViewController alloc]init];
    BaseNavigationController* navc = [[BaseNavigationController alloc]initWithRootViewController:vc];
    navc.view.backgroundColor = UIColor.whiteColor;
    
    if ([BrowserUtils isiPhone] && ![[BrowserUtils shareInstance] isLandscape]) {
        // iPhone竖屏 - 使用半模态弹窗
        if (@available(iOS 15.0, *)) {
            UISheetPresentationController* sheet = navc.sheetPresentationController;
            sheet.detents = @[
                UISheetPresentationControllerDetent.mediumDetent,
                UISheetPresentationControllerDetent.largeDetent
            ];
//            sheet.prefersGrabberVisible = YES;
            sheet.preferredCornerRadius = 10;
        }
        [self presentViewController:navc animated:YES completion:nil];
        
    } else if ([BrowserUtils isiPad]) {
        // iPad
        //v2.6.8,统一present
        [self presentCustomToViewController:navc];
    } else {
        // iPhone横屏 - 使用普通Push
        [self.navigationController pushViewController:vc animated:YES];
    }
}

//刷新
- (void)handleRefresh
{    
    Tab* tab = self.tabManager.selectedTab;
    [tab reload];
}

//JS脚本管理
- (void)handleJS
{
    NSURL *URL = self.tabManager.selectedTab.webView.URL;
    //v2.6.7 黑名单判断
    if ([[CopyrightHelper shareInstance] isInUserScriptBlackList:URL]) {
        [self showUserScriptBlackListAlert];
        return;
    }
    
    UserScriptController* vc = [[UserScriptController alloc]initWithTab:self.tabManager.selectedTab];
    BaseNavigationController* navc = [[BaseNavigationController alloc]initWithRootViewController:vc];
    navc.view.backgroundColor = UIColor.whiteColor;
    
//    if([BrowserUtils isiPad]) {
//        //iPad
//        navc.modalPresentationStyle = UIModalPresentationFormSheet;
//        vc.preferredContentSize = CGSizeMake(kScreenWidth-90, kScreenHeight-90);
//        
//        [self presentViewController:navc animated:YES completion:nil];
//    } else {
//        //iPhone
//        [self presentViewController:navc animated:YES completion:nil];
//    }
    //v2.6.8,统一present
    [self presentCustomToViewController:navc];
}

//弹窗提示，脚本黑名单
- (void)showUserScriptBlackListAlert
{
    UIAlertController *alertController = [UIAlertController alertControllerWithTitle:@"温馨提示" message:@"由于不可抗力因素，当前网站不支持油猴脚本功能，敬请理解！" preferredStyle:UIAlertControllerStyleAlert];
    
    [alertController addAction:([UIAlertAction actionWithTitle:NSLocalizedString(@"alert.iknown", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
    }])];

//    [self presentViewController:alertController animated:YES completion:nil];
    //v2.6.8, 统一present方法，防止崩溃
    [alertController presentSafelyFromViewController:self];
}

//广告
- (void)handleAdBlock
{
    //生成广告规则,步骤如下:
    /*
     1、链接
     https://easylist-downloads.adblockplus.org/easylistchina.txt
     2、ContentBlockItem.m中删除百度搜索相关的规则, 从而让百度搜索首页正常显示
     3、AESFilterConverter中进行版权控制检测
     [[CopyrightHelper shareInstance] validContentRuleText:rule.ruleText]
     4、JSConverter.js中添加unless-domain
     以上3个步骤就可以获得排除版权风险的广告规则
     */

    /*
     英语版:
     https://easylist.to/easylist/easylist.txt
     */
    
//    NSString* file = [[NSBundle mainBundle] pathForResource:@"easylistchina" ofType:@"txt"];
////    NSString* file = [[NSBundle mainBundle] pathForResource:@"easylist" ofType:@"txt"];
//    NSURL* fileUrl = [NSURL fileURLWithPath:file];
//    NSData* data = [NSData dataWithContentsOfURL:fileUrl];
//
//    ASDFilter* filter = [AECFilterParser parseWithData:data];
//    [[AESFilterConverter shareInstance] jsonFromRules:filter.rules upTo:INT_MAX optimize:YES];
}

//设置
- (void)handleSetting
{            
    SettingViewController* vc = [SettingViewController new];
    [self.navigationController pushViewController:vc animated:YES];
}

// 添加到主页
- (void)handleAddWebToHomePannel
{
    //uuid, title, targetUrl, type, ppOrder, ctime
    PandaWebView* webView = self.tabManager.selectedTab.webView;
    NSString* title = webView.title;
    NSString* targetUrl = webView.URL.absoluteString;

    CustomTagModel* item = [CustomTagModel new];
    item.title = title;
    item.targetUrl = targetUrl;
    item.type = CustomTagTypeWeb;
    item.ppOrder = INT_MAX;
    item.iconUrl = @"";
    
    AddCustomTagController* vc = [[AddCustomTagController alloc] initWithCustomTag:item webView:webView];
    BaseNavigationController* navc = [[BaseNavigationController alloc]initWithRootViewController:vc];
    navc.view.backgroundColor = UIColor.whiteColor;
    
    if([BrowserUtils isiPad]) {
        navc.modalPresentationStyle = UIModalPresentationFormSheet;
        
        CGSize size = [UIScreen mainScreen].bounds.size;
        float width = size.width * (1-0.12*2);
        float height = size.height * (1-0.12*2);
        vc.preferredContentSize = CGSizeMake(width, height);
    } else {
        navc.modalPresentationStyle = UIModalPresentationFullScreen;
    }
    
    [self presentViewController:navc animated:YES completion:nil];
}

//全屏切换
- (void)handleSwitchFullScreen
{
    BOOL enabled = [[PreferenceManager shareInstance].items.isFullScreen boolValue];
    enabled = !enabled;
    [PreferenceManager shareInstance].items.isFullScreen = @(enabled);
        
    [[PreferenceManager shareInstance] encode];
}

//2.6.2 智能拼页
- (void)handleSwitchAutoPage
{
    AutoPageViewController* vc = [[AutoPageViewController alloc] initWithTab:self.tabManager.selectedTab];
    BaseNavigationController* navc = [[BaseNavigationController alloc] initWithRootViewController:vc];
    
    if ([BrowserUtils isiPhone] && ![[BrowserUtils shareInstance] isLandscape]) {
        // iPhone竖屏 - 使用半模态弹窗
        if (@available(iOS 15.0, *)) {
            UISheetPresentationController* sheet = navc.sheetPresentationController;
            sheet.detents = @[
                UISheetPresentationControllerDetent.mediumDetent,
                UISheetPresentationControllerDetent.largeDetent
            ];
//            sheet.prefersGrabberVisible = YES;
            sheet.preferredCornerRadius = 10;
        }
        [self presentViewController:navc animated:YES completion:nil];
        
    } else if ([BrowserUtils isiPad]) {
        // iPad
//        navc.modalPresentationStyle = UIModalPresentationFormSheet;
//        vc.preferredContentSize = CGSizeMake(kScreenWidth-90, kScreenHeight-90);
//        
//        [self presentViewController:navc animated:YES completion:nil];
        //v2.6.8,统一present
        [self presentCustomToViewController:navc];
    } else {
        // iPhone横屏 - 使用普通Push
        [self.navigationController pushViewController:vc animated:YES];
    }
}

//腾讯兔小巢反馈
- (void)jumpToFeedback
{    
    int localizeable = [NSLocalizedString(@"opensearch.value", nil) intValue];
    if(localizeable == 1 || localizeable == 0) {
        //中文
        [self _jumpToTxc];
    } else {
        //英语
        [self _jumpToEMail];
    }
}

- (void)_jumpToTxc
{
    //参考 https://txc.qq.com/dashboard/new-product-success
    // 用户ID
    NSString *open_id = [OpenUDID value];
    NSString *user_id = [open_id substringFromIndex:open_id.length-6];
    
    // 昵称
    NSString *nickname = [NSString stringWithFormat:@"游客(%@)", user_id];
   // 头像url地址
   NSString *avatar = @"https://txc.qq.com/static/desktop/img/products/def-product-logo.png";

   // 获得 webview url，请注意url单词是product而不是products，products是旧版本的参数，用错地址将不能成功提交
   // 把1221数字换成你的产品ID，否则会不成功
   NSString *appUrl = @"https://support.qq.com/product/426333";

   // 设置请求体
   NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:appUrl]];

   // 请求方式为POST请求
   [request setHTTPMethod:@"POST"];
   [request setValue:@"application/x-www-form-urlencoded" forHTTPHeaderField:@"Content-Type"];
   NSString *body = [NSString stringWithFormat:@"nickname=%@&avatar=%@&openid=%@", nickname, avatar, open_id];
   [request setHTTPBody:[body dataUsingEncoding:NSUTF8StringEncoding]];
    
    WebViewController* vc = [[WebViewController alloc]initWithWebviewType:WebViewTypePush Title:@"问题反馈"];
    vc.hidesBottomBarWhenPushed = YES;
    [vc loadRequest:request];
    [self.navigationController pushViewController:vc animated:YES];
}

- (void)_jumpToEMail
{
    //跳转到邮件
    // 用户ID
    NSString *open_id = [OpenUDID value];
    NSString *user_id = [open_id substringFromIndex:open_id.length-6];

    NSString *phoneVersion = [[UIDevice currentDevice] systemVersion];

    NSDictionary *infoDic = [[NSBundle mainBundle] infoDictionary];
    NSString *appVersion = [infoDic objectForKey:@"CFBundleShortVersionString"]; // 获取App的版本号
    NSString *appBuildVersion = [infoDic objectForKey:@"CFBundleVersion"]; // 获取App的build版本
    
    NSString* body = [NSString stringWithFormat:@"\n\n\n %@, iOS %@, Focus %@ (%@)", [self deviceModelName], phoneVersion, appVersion, appBuildVersion];
    
    NSString *email = @"<EMAIL>";
    NSString *subject = [NSString stringWithFormat:@"Focus Feedback - %@", user_id];
    NSString *mailString = [NSString stringWithFormat:@"mailto:?to=%@&subject=%@&body=%@", email, subject, body];
    NSURL *url = [NSURL URLWithString:mailString];
    [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
}

- (NSString*)deviceModelName
{
    struct utsname systemInfo;
    uname(&systemInfo);
    NSString *deviceModel = [NSString stringWithCString:systemInfo.machine encoding:NSUTF8StringEncoding];
    return deviceModel;
}

//夜间模式切换
- (void)handleDarkTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    isDarkTheme = !isDarkTheme;
    
    [ThemeProtocol toggleDarkThemeCustom];
        
    //全局切换夜间模式
    for(Tab* tab in self.tabManager.allTabs) {
        if(tab.webView) {
            // For WKWebView background color to take effect, isOpaque must be false,
            // which is counter-intuitive. Default is true. The color is previously
            // set to black in the WKWebView init.
            tab.webView.opaque = !isDarkTheme;
        }
    }
    
    //重新加载脚本
    [[NSNotificationCenter defaultCenter] postNotificationName:kReloadUserScriptNotification object:nil];
    //切换夜间模式
    [[NSNotificationCenter defaultCenter] postNotificationName:kDarkThemeDidChangeNotification object:nil];
}

//标记模式
- (void)handleTagit
{
    [TagitView show];
}

//字体大小调整
- (void)handleFontChange
{
    //参考下面链接:
    //https://juejin.cn/s/how%20to%20increase%20font%20size%20of%20wkwebview%20in%20ios%20swift
    [FontStepView show];
}

//工具箱
- (void)handleToolBox
{
    UIAlertControllerStyle style = UIAlertControllerStyleActionSheet;
    if([BrowserUtils isiPad]) {
        style = UIAlertControllerStyleAlert;
    }
    UIAlertController* alertController = [UIAlertController alertControllerWithTitle:nil message:nil preferredStyle:style];
    @weakify(self)
    UIAlertAction* action = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.lock.fullscreen", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        @strongify(self)
        [self lockFullScreen];
    }];
    [alertController addAction:action];
    
    action = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.font.setting", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        @strongify(self)
        [self handleFontChange];
    }];
    [alertController addAction:action];
    
    action = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.cookies", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        @strongify(self)
        [self _viewCookies];
    }];
    [alertController addAction:action];
    
    action = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.add.webblacklist", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        @strongify(self)
        [self _handleAddWebBlacklist];
    }];
    [alertController addAction:action];
    
    //v2.6.9 屏幕常亮
    BOOL isIdleTimerEnabled = [CommonDataManager shareInstance].isIdleTimerEnabled;
    if (!isIdleTimerEnabled) {
        //点击后开启
        action = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.idleTimer.enabled", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            @strongify(self)
            [self _handleToggleIdleTimerDisabled];
        }];
        [alertController addAction:action];
    } else {
        //点击后关闭
        action = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.idleTimer.disabled", nil) style:UIAlertActionStyleDestructive handler:^(UIAlertAction * _Nonnull action) {
            @strongify(self)
            [self _handleToggleIdleTimerDisabled];
        }];
        [alertController addAction:action];
    }

    //v2.6.9 生成PDF
    // 确保iOS版本支持此API (iOS 14+)
    if (@available(iOS 14.0, *)) {
        action = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.export.pdf", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            @strongify(self)
            [self _createPdf];
        }];
        [alertController addAction:action];
    }
    
    //v2.6.9 生成二维码
    action = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.export.qrcode", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        @strongify(self)
        [self _createQRCode];
    }];
    [alertController addAction:action];
    
    action = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.cancel", nil) style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
        [alertController dismissViewControllerAnimated:YES completion:nil];
    }];
    [alertController addAction:action];

//    [self presentViewController:alertController animated:YES completion:nil];
    //v2.6.8, 统一present方法，防止崩溃
    [alertController presentSafelyFromViewController:self];
}

//v2.6.9 屏幕常亮
- (void)_handleToggleIdleTimerDisabled
{
    BOOL isIdleTimerEnabled = [CommonDataManager shareInstance].isIdleTimerEnabled;
    isIdleTimerEnabled = !isIdleTimerEnabled;
    
    if (isIdleTimerEnabled) {
        //开启
        [[UIApplication sharedApplication] setIdleTimerDisabled:YES];
    } else {
        //关闭
        [[UIApplication sharedApplication] setIdleTimerDisabled:NO];
    }
    
    [CommonDataManager shareInstance].isIdleTimerEnabled = isIdleTimerEnabled;
}

//v2.6.9 生成pdf
- (void)_createPdf
{
    PandaWebView* webView = self.tabManager.selectedTab.webView;
    if (!webView) {
        return;
    }
    
    [webView exportPDF];
}

//v2.6.9 生成二维码
- (void)_createQRCode
{
    PandaWebView* webView = self.tabManager.selectedTab.webView;
    if (!webView) {
        return;
    }
    
    [webView exportQRCode];
}

- (void)_viewCookies
{
    //查看cookies
    if(!self.tabManager.selectedTab) return;
    
    NSURL* URL = self.tabManager.selectedTab.webView.URL;
    if(!URL) return;
    if([InternalURL isValid:URL]) return;
    NSString* host = [URL normalizedHost];
    
    NSArray *cookieModelArray = [[NSHTTPCookieStorage sharedHTTPCookieStorage] cookiesForURL:URL];
    
    NSMutableArray* cookies = [NSMutableArray array];
    for(NSHTTPCookie* obj in cookieModelArray) {
        [cookies addObject:[NSString stringWithFormat:@"%@=%@", obj.name, obj.value]];
    }

    NSString* text = [cookies componentsJoinedByString:@";"];
    
    UIAlertController* alertController = [UIAlertController alertControllerWithTitle:host message:text preferredStyle:UIAlertControllerStyleAlert];
    
    UIAlertAction* action = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.cancel", nil) style:UIAlertActionStyleDestructive handler:^(UIAlertAction * _Nonnull action) {
    }];
    [alertController addAction:action];
    
    action = [UIAlertAction actionWithTitle:NSLocalizedString(@"activity.copy", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        if (text.length > 0) {
            UIPasteboard *pasteboard = [UIPasteboard generalPasteboard];
            pasteboard.string = text;
            
            [UIView showToast:NSLocalizedString(@"common.copy.success", nil)];
        }
    }];
    [alertController addAction:action];
    
//    [self presentViewController:alertController animated:YES completion:nil];
    //v2.6.8, 统一present方法，防止崩溃
    [alertController presentSafelyFromViewController:self];
}

- (void)_handleAddWebBlacklist
{
    //加入黑名单
    if(!self.tabManager.selectedTab) return;
    
    NSURL* URL = self.tabManager.selectedTab.webView.URL;
    if(!URL) return;
    
    [[WebBlacklistHelper shareInstance] addBlockURL:URL];
}

// 锁定全屏
- (void)lockFullScreen
{
//    //切换到全屏
//    [PreferenceManager shareInstance].items.isFullScreen = @(YES);
//    [[PreferenceManager shareInstance] encode];
//    
//    //锁定全屏
//    [CommonDataManager shareInstance].isLockFullScreen = YES;
    
    [CommonDataManager lockFullScreen];
}

- (void)handleWebViewBlacklist
{
    //网页黑名单
    [self _handleAddWebBlacklist];
}


@end
