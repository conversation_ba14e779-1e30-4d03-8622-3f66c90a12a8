//
//  BrowserViewController+ToolbarGesture.m
//  PPBrowser
//
//  Created by qingbin on 2025/3/28.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "BrowserViewController+ToolbarGesture.h"
#import "DatabaseUnit+ToolbarGesture.h"
#import "BottomToolbar.h"
#import "ReactiveCocoa.h"
#import "UIColor+Helper.h"
#import "MaizyHeader.h"
#import "PreferenceManager.h"
#import "NSObject+Helper.h"
#import "ToolbarGestureModel.h"

#import "DatabaseUnit+Helper.h"
#import "HomeHelper.h"

#import "BrowserViewController+PreferenceViewDelegate.h"
#import "BrowserViewController+BottomToolbarDelegate.h"
#import "UIAlertController+SafePresentation.h"

@implementation BrowserViewController (ToolbarGesture)

#pragma mark - 处理工具栏长按手势

- (void)handleToolbarLongPress:(UIView *)button forToolbarGroup:(ToolbarGroup)group {
    // 小震动
    UIImpactFeedbackGenerator* feedback = [[UIImpactFeedbackGenerator alloc] initWithStyle:UIImpactFeedbackStyleMedium];
    [feedback prepare];
    [feedback impactOccurred];
    
    // 从数据库中查询长按手势列表
    DatabaseUnit* unit = [DatabaseUnit queryToolbarGestureListWithGroup:group];
    @weakify(self)
    unit.completeBlock = ^(NSArray* result, BOOL success) {
        @strongify(self)
        if (!success || result.count == 0) {
            // 如果查询失败或没有配置长按手势，直接返回
            return;
        }
        
        // 创建操作菜单
        UIAlertController* alertController = [UIAlertController alertControllerWithTitle:nil message:nil preferredStyle:UIAlertControllerStyleActionSheet];
        
        // 遍历查询结果，添加操作选项
        for (ToolbarGestureModel* gesture in result) {
            NSString* title = [ToolbarGestureModel titleForToolbarLongPressAction:gesture.type];
            UIAlertAction* action = [UIAlertAction actionWithTitle:title style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                [self executeToolbarLongPressAction:gesture.type];
            }];
            [alertController addAction:action];
        }
        
        // 添加取消按钮
        UIAlertAction* cancelAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.cancel", nil) style:UIAlertActionStyleCancel handler:nil];
        [alertController addAction:cancelAction];
        
        // 在iPad上需要设置弹出位置
//        if ([BrowserUtils isiPad]) {
//            UIPopoverPresentationController* popover = alertController.popoverPresentationController;
//            popover.sourceView = self.view;
//            CGRect sourceRect = [button.superview convertRect:button.frame toView:self.view];
//            popover.sourceRect = sourceRect;
//        }
        
//        [self presentViewController:alertController animated:YES completion:nil];
        //v2.6.8, 统一present方法，防止崩溃
        CGRect sourceRect = [button.superview convertRect:button.frame toView:self.view];
        [alertController presentSafelyFromViewController:self sourceView:self.view sourceRect:sourceRect];
    };
    
    // 执行数据库查询
    DB_EXEC(unit);
}

#pragma mark - 执行工具栏长按操作

- (void)executeToolbarLongPressAction:(ToolbarLongPressAction)action {
    switch (action) {
//        case ToolbarLongPressActionBackHistory:
//            [self _handleCheckBackHistory];
//            break;
//            
//        case ToolbarLongPressActionForwardHistory:
//            [self _handleCheckForwardHistory];
//            break;
            
        case ToolbarLongPressActionGoHome:
            [self _handleGoHome];
            break;
            
        case ToolbarLongPressActionNewTab:
            [self _handleNewTab];
            break;
            
        case ToolbarLongPressActionShare:
            [self _handleShare];
            break;
            
        case ToolbarLongPressActionAddBookmark:
            [self _handleAddBookmark];
            break;
            
        case ToolbarLongPressActionAddHome:
            [self _handleAddHome];
            break;
            
        case ToolbarLongPressActionOpenSettings:
            [self _handleOpenSettings];
            break;
            
        case ToolbarLongPressActionFindInPage:
            [self _handleFindInPage];
            break;
            
        case ToolbarLongPressActionImageMode:
            [self _handleImageMode];
            break;
            
        case ToolbarLongPressActionRefresh:
            [self _handleRefresh];
            break;
            
        case ToolbarLongPressActionScrollToTop:
            [self _handleScrollToTop];
            break;
            
        case ToolbarLongPressActionScrollToBottom:
            [self _handleScrollToBottom];
            break;
            
        case ToolbarLongPressActionOpenHistory:
            [self _handleOpenHistory];
            break;
            
        case ToolbarLongPressActionOpenBookmarks:
            [self _handleOpenBookmarks];
            break;
            
        case ToolbarLongPressActionCloseCurrentTab:
            [self _handleCloseCurrentTab];
            break;
            
        case ToolbarLongPressActionCloseOtherTabs:
            [self _handleCloseOtherTabs];
            break;
            
        case ToolbarLongPressActionCloseAllTabs:
            [self _handleCloseAllTabs];
            break;
            
        case ToolbarLongPressActionOpenTranslation:
            [self _handleOpenTranslation];
            break;
            
        case ToolbarLongPressActionLockFullscreen:
            [self _handleLockFullscreen];
            break;
            
        case ToolbarLongPressActionOpenUserScript:
            [self _handleOpenUserScript];
            break;
            
        case ToolbarLongPressActionOpenTagit:
            [self _handleOpenTagit];
            break;
            
        case ToolbarLongPressActionOpenUseragent:
            [self _handleOpenUseragent];
            break;
            
        default:
            break;
    }
}

#pragma mark - 工具栏长按操作具体实现

// 历史记录相关操作
//- (void)_handleCheckBackHistory {
//    // 返回历史记录的具体实现
//}
//
//- (void)_handleCheckForwardHistory {
//    // 前进历史记录的具体实现
//}

- (void)_handleOpenUserScript {
    // 打开脚本的具体实现
    [self handleJS];
}

- (void)_handleOpenTagit {
    // 打开标记模式的具体实现
    [self handleTagit];
}

- (void)_handleOpenUseragent {
    // 打开UA的具体实现
    [self handleUserAgent];
}

- (void)_handleOpenHistory {
    // 打开历史记录的具体实现
    [self handleHistoryController];
}

// 标签页管理操作
- (void)_handleNewTab {
    // 新建标签的具体实现
    //增加一个tab
    [BrowserUtils shareInstance].allTabsCount++;
    [self bottomToolbarDidClickAddTab];
}

- (void)_handleCloseCurrentTab {
    // 关闭当前标签的具体实现
    DatabaseUnit* unit = [DatabaseUnit selectAllTabs];
    @weakify(self)
    unit.completeBlock = ^(NSArray<TabModel*>* result, BOOL success) {
        @strongify(self)
        if(success && result.count>0) {
            [self.tabManager closeCurrentTab:result];
        }
    };
    
    DB_EXEC(unit);
}

- (void)_handleCloseOtherTabs {
    // 关闭其他标签的具体实现
    DatabaseUnit* unit = [DatabaseUnit selectAllTabs];
    @weakify(self)
    unit.completeBlock = ^(NSArray<TabModel*>* result, BOOL success) {
        @strongify(self)
        if(success && result.count>0) {
            [self.tabManager closeOtherTabs:result];
        }
    };
    
    DB_EXEC(unit);
}

- (void)_handleCloseAllTabs {
    // 关闭所有标签的具体实现
    [self.tabManager removeAllTabsFromTabTray];
}

// 页面操作
- (void)_handleGoHome {
    // 回到首页的具体实现
    [self bottomToolbarDidClickHome];
}

- (void)_handleShare {
    // 分享页面的具体实现
    [self handleShareToFriends];
}

- (void)_handleAddBookmark {
    // 添加书签的具体实现
    [self toolViewAddBookMarkAction];
}

- (void)_handleAddHome {
    // 添加到首页的具体实现
    [self toolViewAddHomeAction];
}

- (void)_handleFindInPage {
    // 页内查找的具体实现
    [self handleFindInPage];
}

- (void)_handleImageMode {
    // 看图模式的具体实现
    [self handleGetAllImageMode];
}

- (void)_handleRefresh {
    // 刷新页面的具体实现
    [self handleRefresh];
}

// 页面导航操作
- (void)_handleScrollToTop {
    // 滚动到顶部的具体实现
    PandaWebView* webView = self.tabManager.selectedTab.webView;
    [webView.scrollView setContentOffset:CGPointMake(0, 0) animated:YES];
}

- (void)_handleScrollToBottom {
    // 滚动到底部的具体实现
    // 确保内容尺寸已正确计算
    dispatch_async(dispatch_get_main_queue(), ^{
        PandaWebView* webView = self.tabManager.selectedTab.webView;
        [webView.scrollView setContentOffset:CGPointMake(0, webView.scrollView.contentSize.height - webView.scrollView.frame.size.height) animated:YES];
    });
}

// 功能入口操作
- (void)_handleOpenSettings {
    // 打开设置的具体实现
    [self handleSetting];
}

- (void)_handleOpenBookmarks {
    // 打开书签的具体实现
    [self handleBookMarkController];
}

- (void)_handleOpenTranslation {
    // 打开翻译的具体实现
    [self handleTranslate];
}

- (void)_handleLockFullscreen {
    // 锁定全屏的具体实现
    [self lockFullScreen];
}

@end
