//
//  BrowserViewController+TabManagerDelegate.m
//  PandaBrowser
//
//  Created by qingbin on 2022/3/4.
//

#import "BrowserViewController+TabManagerDelegate.h"
#import "PandaWebView.h"

#import "Masonry.h"
#import "WebViewNavDelegate.h"
#import "MenuHelper.h"

@implementation BrowserViewController (TabManagerDelegate)

- (void)tabManager:(TabManager*)tabManager didSelectedTabChange:(Tab*)selected previous:(Tab*)previous
{
    //参考Brave-ios源码相关实现, 有很多细节, 现在只是摘取部分实现
    
//    if(previous && previous.webView) {
//        [previous.webView endEditing:YES];
//        [previous.webView removeFromSuperview];
//    }
    
    //先将内存中所有的webview全部移除
    NSArray* allTabs = self.tabManager.allTabs;
    for(Tab* tab in allTabs) {
        if(tab && tab.webView && tab.webView.superview) {
            [tab.webView endEditing:YES];
            [tab.webView removeFromSuperview];
        }
    }
    
    [self addWebView:selected.webView];
    
    if(!selected.webView.URL) {
        // The web view can go gray if it was zombified due to memory pressure.
        // When this happens, the URL is nil, so try restoring the page upon selection.
        [selected reload];
    }
    
    self.toolBarScrollController.tab = selected;
    
    [self updatePageStatusForTab:selected];
    
    //设置弹出menu代理
    [[MenuHelper shareInstance] setDelegate:(id<MenuHelperDelegate>)selected.webView];
    
    //2.7.6 更新toptoolbar的阅读模式状态
    [self.topToolbar updateIsInReader:selected.webView.isInReader];
}

- (void)tabManager:(TabManager*)tabManager didAddTab:(Tab*)tab
{
    [self updatePageStatusForTab:tab];
}


@end
