//
//  BrowserViewController+PreferenceViewDelegate.h
//  PPBrowser
//
//  Created by qingbin on 2022/4/25.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "BrowserViewController.h"
#import "PreferenceContentView.h"

NS_ASSUME_NONNULL_BEGIN

@interface BrowserViewController (PreferenceViewDelegate)<PreferenceViewDelegate>

//查询历史
- (void)handleHistoryController;

// 添加书签
- (void)handleAddBookMark;

// 添加到主页
- (void)handleAddWebToHomePannel;

// 页面查找
- (void)handleFindInPage;

//JS脚本管理
- (void)handleJS;

//弹窗提示，脚本黑名单
- (void)showUserScriptBlackListAlert;

//分享
- (void)handleShareToFriends;

//查看书签页
- (void)handleBookMarkController;

//刷新
- (void)handleRefresh;

//设置
- (void)handleSetting;

//锁定全屏
- (void)lockFullScreen;

//打开翻译
- (void)handleTranslate;

// 看图模式
- (void)handleGetAllImageMode;

//判断是否需要添加书签还是首页标签相关逻辑
- (void)handleBookMarkAndTagAction;

//添加首页
- (void)toolViewAddHomeAction;

//添加书签
- (void)toolViewAddBookMarkAction;

// UserAgent
- (void)handleUserAgent;

//标记模式
- (void)handleTagit;

@end

NS_ASSUME_NONNULL_END
