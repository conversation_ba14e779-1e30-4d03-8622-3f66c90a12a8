//
//  BrowserViewController+ToolbarGesture.h
//  PPBrowser
//
//  Created by qingbin on 2025/3/28.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "BrowserViewController.h"
#import "ToolbarGestureModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface BrowserViewController (ToolbarGesture)

// 处理工具栏长按手势
- (void)handleToolbarLongPress:(UIView *)button forToolbarGroup:(ToolbarGroup)group;

// 执行对应的工具栏长按操作
- (void)executeToolbarLongPressAction:(ToolbarLongPressAction)action;

@end

NS_ASSUME_NONNULL_END
