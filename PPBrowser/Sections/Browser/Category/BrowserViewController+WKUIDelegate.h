//
//  BrowserViewController+WKUIDelegate.h
//  PPBrowser
//
//  Created by qingbin on 2022/3/26.
//  Copyright © 2022 qingbin. All rights reserved.
//


#import "BrowserViewController.h"
#import <WebKit/WebKit.h>
#import "Tab.h"
#import "TabManager.h"

NS_ASSUME_NONNULL_BEGIN

@interface BrowserViewController (WKUIDelegate)<WKUIDelegate>

// 在新标签页打开
- (void)handleOpenInNewTabAction:(NSURL *)URL;

// 直接打开
- (void)handleOpenAction:(NSURL *)URL;

// 后台打开
- (void)handleOpenInBackendTabAction:(NSURL *)URL;

// 复制链接
- (void)handleCopyLinkContextMenu:(NSURL *)URL;

// 通过URL Scheme打开
- (void)handleOpenInURLScheme:(NSURL *)URL;

@end

NS_ASSUME_NONNULL_END
