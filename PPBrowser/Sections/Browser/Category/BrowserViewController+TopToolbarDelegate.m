//
//  BrowserViewController+TopToolbarDelegate.m
//  PPBrowser
//
//  Created by qingbin on 2022/4/24.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "BrowserViewController+TopToolbarDelegate.h"
#import "SearchSuggestionController.h"
#import "BaseNavigationController.h"
#import "Tab.h"

#import "BaseActivity.h"

#import "UIView+Helper.h"
#import "ReactiveCocoa.h"
#import "BrowserViewController+PreferenceViewDelegate.h"

#import "Tampermonkey.h"
#import "UserScript.h"

#import "PPNotifications.h"
#import "Tampermonkey.h"
#import "DatabaseUnit+UserScript.h"
#import "UserScriptController.h"
#import "NSObject+Helper.h"

#import "BrowserViewController+BottomToolbarDelegate.h"
#import "UserScriptHelper.h"
#import "PopUpMenuController.h"

#import "UserScriptPopUpViewController.h"
#import "ExMatchURLHelper.h"
#import "InternalURL.h"
#import "CopyrightHelper.h"
#import "BrowserViewController+ToolbarGesture.h"
#import "UIAlertController+SafePresentation.h"
#import "BookSettingViewController.h"

@implementation BrowserViewController (TopToolbarDelegate)

- (void)topToolbarDidSearch
{
    SearchSuggestionController* vc = [[SearchSuggestionController alloc] initWithTab:self.tabManager.selectedTab];
//    vc.delegate = self;
    
    BaseNavigationController* navc = [[BaseNavigationController alloc]initWithRootViewController:vc];
    navc.view.backgroundColor = UIColor.whiteColor;
    navc.modalPresentationStyle = UIModalPresentationFullScreen;
    
    [self presentViewController:navc animated:YES completion:nil];
}

- (void)topToolbarDidReload
{
    Tab* tab = self.tabManager.selectedTab;
    if(!tab || !tab.webView) return;
    
    //https://stackoverflow.com/questions/31953156/reload-vs-reloadfromorigin-in-wkwebview
    [tab reload];
}

- (void)topToolbarDidCancelReload
{
    Tab* tab = self.tabManager.selectedTab;
    if(!tab || !tab.webView) return;
    
    [tab stop];
}

- (void)topToolbarDidMenuAction:(UIView*)menuButton
{
    CGRect sourceRect = [menuButton.superview convertRect:menuButton.frame toView:self.view];
    [PopUpMenuController showAt:self sourceRect:sourceRect];
}

//选择弹出式窗口打开方式
- (void)handlePopupWindowOpenOption
{
    UIAlertControllerStyle style = UIAlertControllerStyleActionSheet;
    if([BrowserUtils isiPad]) {
        //iPad
        style = UIAlertControllerStyleAlert;
    }
    UIAlertController *alertController = [UIAlertController alertControllerWithTitle:nil message:nil preferredStyle:style];
    [alertController addAction:([UIAlertAction actionWithTitle:NSLocalizedString(@"alert.cancel", nil) style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
        
    }])];
    [alertController addAction:([UIAlertAction actionWithTitle:NSLocalizedString(@"popupwindow.setting.default", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [PreferenceManager shareInstance].items.popupWindowOption = @(PopupWindowOptionDefault);
        [[PreferenceManager shareInstance] encode];
    }])];
    [alertController addAction:([UIAlertAction actionWithTitle:NSLocalizedString(@"popupwindow.setting.alwaysAsk", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [PreferenceManager shareInstance].items.popupWindowOption = @(PopupWindowOptionAlwaysAsk);
        [[PreferenceManager shareInstance] encode];
    }])];
    [alertController addAction:([UIAlertAction actionWithTitle:NSLocalizedString(@"popupwindow.setting.openInCurrent", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [PreferenceManager shareInstance].items.popupWindowOption = @(PopupWindowOptionOpenInCurrent);
        [[PreferenceManager shareInstance] encode];
    }])];
    [alertController addAction:([UIAlertAction actionWithTitle:NSLocalizedString(@"popupwindow.setting.openInInNewWindow", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [PreferenceManager shareInstance].items.popupWindowOption = @(PopupWindowOptionOpenInNewWindow);
        [[PreferenceManager shareInstance] encode];
    }])];
    [alertController addAction:([UIAlertAction actionWithTitle:NSLocalizedString(@"popupwindow.setting.openInInBackWindow", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [PreferenceManager shareInstance].items.popupWindowOption = @(PopupWindowOptionOpenInBackWindow);
        [[PreferenceManager shareInstance] encode];
    }])];
    
//    [self presentViewController:alertController animated:YES completion:nil];
    //v2.6.8, 统一present方法，防止崩溃
    [alertController presentSafelyFromViewController:self];
}

//通过分享过来的脚本文件(file://)
- (void)handleAddUserScriptWithURL:(NSURL *)URL
{    
    [UserScriptHelper installScriptFromFileWithURL:URL tab:self.tabManager.selectedTab];
}

//通过ActionExtension分享过来的脚本文件(file://)
- (void)handleAddUserScriptWithJsContent:(NSString *)jsContent
{
    [UserScriptHelper installScriptFromFileWithJsContent:jsContent tab:self.tabManager.selectedTab];
}

#pragma mark -- iPad
//iPad
// 添加工具栏长按手势代理方法实现
- (void)topToolbarDidLongPress:(UIView *)button forToolbarGroup:(ToolbarGroup)group {
    [self handleToolbarLongPress:button forToolbarGroup:group];
}

- (void)topToolbarDidClickBack
{
    [self bottomToolbarDidClickBack];
}

- (void)topToolbarDidClickForward
{
    [self bottomToolbarDidClickForward];
}

- (void)topToolbarDidClickAddTab
{
    [self bottomToolbarDidClickAddTab];
}

- (void)topToolbarDidClickTabs
{
    [self bottomToolbarDidClickTabs];
}

- (void)topToolbarDidClickMenu
{
    [self bottomToolbarDidClickMenu:NO];
}

- (void)topToolbarDidClickHome
{
    [self bottomToolbarDidClickHome];
}

//点击了顶部工具栏上的脚本按钮
- (void)topToolbarDidClickJs
{
    NSURL *URL = self.tabManager.selectedTab.webView.URL;
    //v2.6.7 黑名单判断
    if ([[CopyrightHelper shareInstance] isInUserScriptBlackList:URL]) {
        [self showUserScriptBlackListAlert];
        return;
    }
    
    UIView* button =  self.topToolbar.textFieldLeftView;
    CGRect sourceRect = [button.superview convertRect:button.frame toView:self.view];
    
    NSString* url = self.tabManager.selectedTab.webView.URL.absoluteString;
    NSMutableArray* currentRunScriptArray = [NSMutableArray array];
    
    Tab* tab = self.tabManager.selectedTab;
    
    DatabaseUnit* unit = [DatabaseUnit queryAllUserScripts];
    @weakify(self)
    [unit setCompleteBlock:^(NSArray* result, BOOL success) {
        @strongify(self)
        if(!self) return;
        if(success) {
            //判断当前脚本是否正在运行状态
            for(UserScript* item in result) {
                if(item.isActive) {
                    //已经激活,查看是否在当前网站运行
                    if([ExMatchURLHelper matchesUserScript:item url:url]) {
                        [currentRunScriptArray addObject:item];
                        
                        //检测已注册的菜单栏
                        item.commands = [tab.commandManager getMenusWithScriptId:item.uuid];
                    }
                }
            }
        }
        
        [UserScriptPopUpViewController showAt:self
                                   sourceRect:sourceRect 
                              userScriptArray:currentRunScriptArray
                           userCommandManager:tab.commandManager
                                  clickAction:^(UserScript *script, UserScriptPopUpSettingModel *settingModel, UIViewController* viewController) {
            @strongify(self)
            [viewController dismissViewControllerAnimated:NO completion:nil];
            
            [self _handleUserScriptPopUpClickActionWithScript:script settingModel:settingModel];
        }];
    }];
    
    DB_EXEC(unit);
}

- (void)_handleUserScriptPopUpClickActionWithScript:(UserScript *)script 
                                       settingModel:(UserScriptPopUpSettingModel *)settingModel
{
    if(script) {
        //点击脚本
        [self _showCommandMenu:script];
    } else if(settingModel) {
        //0-更多脚本, 1-脚本管理页
        if(settingModel.type == 0) {
            NSURL* URL = self.tabManager.selectedTab.webView.URL;
            if([InternalURL isValid:URL]) {
                [UIView showToast:NSLocalizedString(@"script.not.support.title", nil)];
                return;
            }
        
            NSString* host = [URL normalizedHost];
            NSString* url = [NSString stringWithFormat:@"https://greasyfork.org/zh-CN/scripts/by-site/%@",host];
            [self.tabManager.selectedTab loadRequest:[NSURLRequest requestWithURL:[NSURL URLWithString:url]]];
        } else if(settingModel.type == 1) {
            //打开脚本管理页
            [self handleJS];
        }
    }
}

//显示脚本菜单项
- (void)_showCommandMenu:(UserScript *)model
{
    //检测已注册的菜单栏
    Tab* tab = self.tabManager.selectedTab;
    model.commands = [tab.commandManager getMenusWithScriptId:model.uuid];
    
    if(model.commands.count == 0) {
        [UIView showToast:NSLocalizedString(@"script.no.menu.title", nil)];
        return;
    }
    
    UIAlertController* alertController = [UIAlertController alertControllerWithTitle:nil message:nil preferredStyle:UIAlertControllerStyleAlert];
    UIAlertAction* action = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.cancel", nil) style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
        [alertController dismissViewControllerAnimated:YES completion:nil];
    }];
    [alertController addAction:action];

    @weakify(self)
    for(UserComandModel* item in model.commands) {
        UIAlertAction* action = [UIAlertAction actionWithTitle:item.name style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            @strongify(self)
            NSLog(@"点击了: %@", item.name);
            tab.commandManager.triggerCommandAction(item);
            [self dismissViewControllerAnimated:NO completion:nil];
        }];
        [alertController addAction:action];
    }
    
//    [self presentViewController:alertController animated:YES completion:nil];
    //v2.6.8, 统一present方法，防止崩溃
    [alertController presentSafelyFromViewController:self];
}

// v2.7.6 阅读模式设置
- (void)topToolbarDidClickReaderSetting
{
    BookSettingViewController* vc = [[BookSettingViewController alloc] initWithTab:self.tabManager.selectedTab pageType:BookSettingPageTypeHome];
    BaseNavigationController* navc = [[BaseNavigationController alloc] initWithRootViewController:vc];
    
    if ([BrowserUtils isiPhone] && ![[BrowserUtils shareInstance] isLandscape]) {
        // iPhone竖屏 - 使用半模态弹窗
        if (@available(iOS 15.0, *)) {
            UISheetPresentationController* sheet = navc.sheetPresentationController;
            sheet.detents = @[
                UISheetPresentationControllerDetent.largeDetent,
                UISheetPresentationControllerDetent.mediumDetent,
            ];
            //            sheet.prefersGrabberVisible = YES;
            sheet.preferredCornerRadius = 10;
        }
        [self presentViewController:navc animated:YES completion:nil];
        
    } else if ([BrowserUtils isiPad]) {
        // iPad
        //v2.6.8,统一present
        [self presentCustomToViewController:navc];
    } else {
        // iPhone横屏/iPad - 使用普通Push
        [self.navigationController pushViewController:vc animated:YES];
    }
}

@end
