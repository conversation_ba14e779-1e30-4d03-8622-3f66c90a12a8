//
//  BrowserViewController+WKNavigationDelegate.m
//  PandaBrowser
//
//  Created by qing<PERSON> on 2022/3/8.
//

#import "BrowserViewController+WKNavigationDelegate.h"
#import "InternalURL.h"

#import "PreferenceManager.h"
#import "PreferenceModel.h"

#import "NSURL+Extension.h"
#import "MaizyHeader.h"

#import "ReactiveCocoa.h"
#import "CopyrightHelper.h"

#import "UserScriptHelper.h"
#import "UserComandModel.h"

#import "WebBlacklistHelper.h"
#import "SnifferHelper.h"
#import "UIView+Helper.h"

#import "AlertJumpHelper.h"
#import "UIAlertController+SafePresentation.h"
#import "FileHelper.h"
#import <objc/runtime.h>

static const void *kViewingURLsKey = &kViewingURLsKey;

@implementation BrowserViewController (WKNavigationDelegate)

// Recognize an Apple Maps URL. This will trigger the native app. But only if a search query is present. Otherwise
// it could just be a visit to a regular page on maps.apple.com.
- (BOOL)isAppleMapsURL:(NSURL*)url
{
    if([url.scheme isEqualToString:@"http"] || [url.scheme isEqualToString:@"https"]) {
        if([url.host isEqualToString:@"maps.apple.com"] && url.query.length > 0) {
            return YES;
        }
    }

    return NO;
}

// Recognize a iTunes Store URL. These all trigger the native apps. Note that appstore.com and phobos.apple.com
// used to be in this list. I have removed them because they now redirect to itunes.apple.com. If we special case
// them then iOS will actually first open Safari, which then redirects to the app store. This works but it will
// leave a 'Back to Safari' button in the status bar, which we do not want.
- (BOOL)isStoreURL:(NSURL*)url
{
    if([url.scheme isEqualToString:@"http"] || [url.scheme isEqualToString:@"https"]) {
        if([url.host isEqualToString:@"itunes.apple.com"]) {
            return YES;
        }
    }

    if([url.scheme isEqualToString:@"itms-appss"] || [url.scheme isEqualToString:@"itmss"]) {
        return YES;
    }

    return NO;
}

//第三方登录的scheme
//wtloginmqq:
//weixin:
- (BOOL)isLoginURL:(NSURL*)url
{
    //参考
    //http://zkforever.cn/2021/01/02/iOS-qq%E4%B8%80%E9%94%AE%E7%99%BB%E5%BD%95/
    if([url.scheme isEqualToString:@"wtloginmqq"]
       || [url.scheme isEqualToString:@"weixin"]) {
        if([url.scheme isEqualToString:@"wtloginmqq"]) {
            //处理QQ登录逻辑
            NSString *urlStr = [NSString stringWithFormat:@"%@&schemacallback=FocusBrowser://",url];
            NSURL *URL = [NSURL URLWithString:urlStr];
            self.qqLoginURL = URL;

            [[UIApplication sharedApplication] openURL:URL options:@{} completionHandler:nil];
        } else if([url.scheme isEqualToString:@"weixin"]) {
            //处理微信登录逻辑
        }

        return YES;
    }

    return NO;
}

// 1 在发送请求之前，决定是否跳转
// This is the place where we decide what to do with a new navigation action. There are a number of special schemes
// and http(s) urls that need to be handled in a different way. All the logic for that is inside this delegate
// method.
- (void)webView:(WKWebView *)webView decidePolicyForNavigationAction:(WKNavigationAction *)navigationAction preferences:(WKWebpagePreferences *)preferences decisionHandler:(void (^)(WKNavigationActionPolicy, WKWebpagePreferences * _Nonnull))decisionHandler
{
    NSString* urlString = navigationAction.request.URL.absoluteString;
    LOG_DEBUG(@"1、 %s, title = %@, url = %@, header = %@", __func__, webView.title, urlString, navigationAction.request.allHTTPHeaderFields);

//    NSDictionary* allHTTPHeaderFields = navigationAction.request.allHTTPHeaderFields;
//    [[SnifferHelper shareInstance] updateRequestHeader:allHTTPHeaderFields forUrl:urlString];

    //隐藏页面查找
    [self updateFindInPageVisibility:NO text:nil];

    NSURL* url = navigationAction.request.URL;
    if(!url) {
        decisionHandler(WKNavigationActionPolicyCancel, preferences);
        return;
    }

    //--customHeader

    //如果是内部链接: internal://
    if([InternalURL isValid:url]) {
        decisionHandler(WKNavigationActionPolicyAllow+2, preferences);
        return;
    }

    //网页黑名单，直接返回
    if([[WebBlacklistHelper shareInstance] isBlockURL:url]) {
        decisionHandler(WKNavigationActionPolicyCancel, preferences);
        return;
    }

    if([url.scheme isEqualToString:@"about"]) {
        decisionHandler(WKNavigationActionPolicyAllow+2, preferences);
        return;
    }

    //--safeBrowsing

    // First special case are some schemes that are about Calling. We prompt the user to confirm this action. This
    // gives us the exact same behaviour as Safari.
    if([url.scheme isEqualToString:@"tel"] || [url.scheme isEqualToString:@"facetime"] || [url.scheme isEqualToString:@"facetime-audio"]) {
        [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
        decisionHandler(WKNavigationActionPolicyCancel, preferences);
        return;
    }

    // Second special case are a set of URLs that look like regular http links, but should be handed over to iOS
    // instead of being loaded in the webview. Note that there is no point in calling canOpenURL() here, because
    // iOS will always say yes. TODO Is this the same as isWhitelisted?
    if([self isAppleMapsURL:url]) {
        [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
        decisionHandler(WKNavigationActionPolicyCancel, preferences);
        return;
    }
//http://itunes.apple.com/cn/app/id458318329?mt=8
//https://m.v.qq.com/activity/downapp_activity.html?not_auto_open=1
//http://itunes.apple.com/cn/app/id458318329?mt=8
    if([self isStoreURL:url]) {
        [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:^(BOOL success) {
        }];
        decisionHandler(WKNavigationActionPolicyCancel, preferences);
        return;
    }

    if([self isLoginURL:url]) {
        decisionHandler(WKNavigationActionPolicyCancel, preferences);
        return;
    }

    // Handles custom mailto URL schemes.
    if([url.scheme isEqualToString:@"mailto"]) {
        [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
        decisionHandler(WKNavigationActionPolicyCancel, preferences);
        return;
    }

    // This is the normal case, opening a http or https url, which we handle by loading them in this WKWebView. We
    // always allow this. Additionally, data URIs are also handled just like normal web pages.
    if([@[@"http", @"https", @"data", @"blob", @"file"] containsObject:url.scheme]) {
//        if([BrowserUtils isiPhone]) {
//            //iPhone
//            //这里iPhone百度不能设置，否则会跳百度APP
//            if([url.absoluteString rangeOfString:@"baidu.com"].location != NSNotFound) {
//                //百度相关页面不允许设置UA,否则访问不了
//            } else {
//                if(navigationAction.targetFrame.isMainFrame) {
//                    Tab* tab = [self.tabManager tabForWebView:webView];
//                    [tab updateUserAgentWithURL:url];
//                }
//            }
//        } else {
//            //iPad/iMac
//            /// 这里必须要设置，否则iPad端百度搜索APP的时候，会不断请求接口，停不下来
//            if(navigationAction.targetFrame.isMainFrame) {
//                Tab* tab = [self.tabManager tabForWebView:webView];
//                [tab updateUserAgentWithURL:url];
//            }
//        }

        //v2.6.0 允许百度设置，并没有发现跳转百度APP的问题，可能以前的UA格式不对导致的
        if(navigationAction.targetFrame.isMainFrame) {
            Tab* tab = [self.tabManager tabForWebView:webView];
            [tab updateUserAgentWithURL:url];
        }

        //v2.7.4，文件下载
//        [[FileHelper shareInstance].pendingRequests setObject:navigationAction.request forKey:url.absoluteString];

        // Adblock logic,
        // Only use main document URL, not the request URL
        // If an iFrame is loaded, shields depending on the main frame, not the iFrame request

        // Weird behavior here with `targetFrame` and `sourceFrame`, on refreshing page `sourceFrame` is not nil (it is non-optional)
        //  however, it is still an uninitialized object, making it an unreliable source to compare `isMainFrame` against.
        //  Rather than using `sourceFrame.isMainFrame` or even comparing `sourceFrame == targetFrame`, a simple URL check is used.
        // No adblocking logic is be used on session restore urls. It uses javascript to retrieve the
        // request then the page is reloaded with a proper url and adblocking rules are applied.
        //参考firefox-focus-ios-main,不在每次请求添加过滤处理, 而是在webView初始化的时候就添加过滤规则。
        NSURL* mainDocumentURL = navigationAction.request.mainDocumentURL;
        if([[mainDocumentURL schemelessAbsoluteString] isEqualToString:[url schemelessAbsoluteString]]
           && ![InternalURL isValid:url]
           && [self _checkIsMainFrame:navigationAction.sourceFrame]
           && [self _checkIsMainFrame:navigationAction.targetFrame]) {
            //https://www.jianshu.com/p/3a75d7348843
            //如果targetFrame的mainFrame属性为NO，表明这个WKNavigationAction将会新开一个页面。
            PandaWebView* currentWebView = (PandaWebView*)webView;
            //添加了版权控制, 过滤规则不可靠，必须手动控制，否则容易收到律师函
            [currentWebView updateRuleListIfNeedWithUrl:url];
        }

//https://v.qq.com/ulink_handler/HomeActivity?channelId=&channelTitle=&confid=20449&from=20449_safari&tabIndex=0
        if([url.host containsString:@"qq.com"]) {
            //腾讯视频律师函
            decisionHandler(WKNavigationActionPolicyAllow, preferences);
        } else {
            decisionHandler(WKNavigationActionPolicyAllow + 2, preferences);
        }

        return;
    }

    // Ignore JS navigated links, the intention is to match Safari and native WKWebView behaviour.
//    if(navigationAction.navigationType == WKNavigationTypeLinkActivated) {
//        [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
//    }

    //版权保护
    BOOL isValid = [[CopyrightHelper shareInstance] validPopupUrl:url];
    if(isValid) {
//        BOOL enabledFilterPopup = [[PreferenceManager shareInstance].items.enabledFilterPopup boolValue];
//        if(!enabledFilterPopup) {
//            // Standard schemes are handled in previous if-case.
//            // This check handles custom app schemes to open external apps.
//            [self openExternalUrl:url];
//        }

        //[[UIApplication sharedApplication] canOpenURL:url]
        //这个判断需要添加对应的scheme, 否则跳转不了, 例如跳转到telegram
        [self openExternalUrl:url];
    } else {
        [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
    }

    decisionHandler(WKNavigationActionPolicyCancel, preferences);
}

//- (void)openExternalUrl:(NSURL*)url
//{
//    UIAlertController* alertController = [UIAlertController alertControllerWithTitle:NSLocalizedString(@"alert.allow.open3party.title", nil) message:nil preferredStyle:UIAlertControllerStyleAlert];
//
//    UIAlertAction *action = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.cancel", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
//        [alertController dismissViewControllerAnimated:YES completion:nil];
//    }];
//    [alertController addAction:action];
//
//    action = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.allow", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
//        [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
//    }];
//    [alertController addAction:action];
//
//    [self presentViewController:alertController animated:YES completion:nil];
//}

- (void)openExternalUrl:(NSURL*)URL
{
    BOOL isBlockURL = [[AlertJumpHelper shareInstance] isBlockURL:URL];
    if(isBlockURL) {
        //已添加在屏蔽名单中，禁止弹窗
        return;
    }

    NSString* scheme = URL.scheme;
    NSString* message = @"";

    int localizeable = [NSLocalizedString(@"opensearch.value", nil) intValue];
    if(localizeable == 0) {
        //简体中文
        message = [NSString stringWithFormat:@"可以通过拦截APP跳转，从而可以拦截\"%@:\"的跳转", scheme];
    } else if(localizeable == 1) {
        //繁体中文
        message = [NSString stringWithFormat:@"可以通過攔截APP跳轉，從而攔截\"%@:\"的跳轉", scheme];
    } else {
        //英语
        message = [NSString stringWithFormat:@"By blocking APP redirection, you can block the redirection of \"%@:\"", scheme];
    }

    //弹窗询问
    UIAlertController* alertController = [UIAlertController alertControllerWithTitle:NSLocalizedString(@"alert.allow.open3party.title", nil) message:message preferredStyle:UIAlertControllerStyleAlert];

    UIAlertAction *action = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.block.app.redirect", nil) style:UIAlertActionStyleDestructive handler:^(UIAlertAction * _Nonnull action) {
        [[AlertJumpHelper shareInstance] addAlertJumpWithUrl:URL.absoluteString];
    }];
    [alertController addAction:action];

    action = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.allow", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [[UIApplication sharedApplication] openURL:URL options:@{} completionHandler:nil];
    }];
    [alertController addAction:action];

    action = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.cancel", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [alertController dismissViewControllerAnimated:YES completion:nil];
    }];
    [alertController addAction:action];

//    [self presentViewController:alertController animated:YES completion:nil];
    //v2.6.8, 统一present方法，防止崩溃
    [alertController presentSafelyFromViewController:self];
}

- (BOOL)_checkIsMainFrame:(WKFrameInfo*)iframe
{
    //从首页进入到webview, sourceFrame==nil
    if(!iframe) return YES;

    return iframe.isMainFrame;
}

// 2 页面开始加载时调用
- (void)webView:(WKWebView *)webView didStartProvisionalNavigation:(null_unspecified WKNavigation *)navigation
{
//    LOG_DEBUG(@"2、 %s, url = %@", __func__, webView.URL.absoluteString);
}

// 4.2 在收到响应后，决定是否跳转
- (void)webView:(WKWebView *)webView decidePolicyForNavigationResponse:(WKNavigationResponse *)navigationResponse decisionHandler:(void (^)(WKNavigationResponsePolicy))decisionHandler
{
    LOG_DEBUG(@"4.2、 %s, url = %@", __func__, webView.URL.absoluteString);

    NSURLResponse* response = navigationResponse.response;
    NSURL* responseURL = response.URL;

//    NSURLRequest* request;
//    if(responseURL && responseURL.absoluteString.length > 0) {
//        NSString* key = responseURL.absoluteString;
//        request = [[FileHelper shareInstance].pendingRequests objectForKey:key];
//        [[FileHelper shareInstance].pendingRequests removeObjectForKey:key];
//    }

    // We can only show this content in the web view if this web view is not pending
    // download via the context menu.
//    BOOL canShowInWebView = navigationResponse.canShowMIMEType;

    // Check if this response should be downloaded.
//    WKHTTPCookieStore* cookieStore = webView.configuration.websiteDataStore.httpCookieStore;

    NSString* responseUrl = responseURL.absoluteString;
    if(responseUrl && responseUrl.length > 0) {
        // 检查是否正在查看这个URL，如果是则直接允许加载
        if ([self isViewingURL:responseURL]) {
            [self clearViewingURL:responseURL];
            decisionHandler(WKNavigationResponsePolicyAllow);
            return;
        }

        FileHelper* helper = [[FileHelper alloc] initWithResponse:response url:responseUrl];
//        if([helper isAttachment] || !canShowInWebView) {
        //v2.7.4
        if ([self isDownloadableResponse:navigationResponse]) {
            //should be downloaded

            // v2.7.6 检查是否应该显示查看选项
            if ([self shouldShowViewOption:navigationResponse]) {
                // 显示包含查看选项的弹窗
                @weakify(self)
                [helper showSelectViewWithViewHandler:^{
                    // 用户选择查看，重新加载URL以在WebView中显示
                    @strongify(self)
                    if (self && responseURL) {
                        // 添加一个标记，避免重复弹窗
                        [self setViewingURL:responseURL];
                        NSURLRequest *request = [NSURLRequest requestWithURL:responseURL];
                        [webView loadRequest:request];
                    }
                }];

                decisionHandler(WKNavigationResponsePolicyCancel);
                return;
            } else {
                // 只显示下载选项
                [helper showSelectView];
                decisionHandler(WKNavigationResponsePolicyCancel);
                return;
            }
        }
    }

    decisionHandler(WKNavigationResponsePolicyAllow);
}

/**
 * 获取可下载的MIME类型数组
 * @return NSArray 可下载的MIME类型数组
 */
- (NSArray *)downloadableMimeTypes {
    return @[
        // 文档类型
        @"application/pdf",
        @"application/msword",
        @"application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        @"application/vnd.ms-excel",
        @"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        @"application/vnd.ms-powerpoint",
        @"application/vnd.openxmlformats-officedocument.presentationml.presentation",
        @"text/csv",
        @"application/rtf",
        @"text/plain",

        // 压缩文件
        @"application/zip",
        @"application/x-rar-compressed",
        @"application/x-7z-compressed",
        @"application/x-tar",
        @"application/gzip",

        // 可执行文件
        @"application/x-msdownload",
        @"application/x-apple-diskimage",
        @"application/vnd.android.package-archive",
    ];
}

/**
 * 判断响应是否为可下载文件
 * @param navigationResponse WKNavigationResponse对象
 * @return BOOL 是否可下载
 */
- (BOOL)isDownloadableResponse:(WKNavigationResponse *)navigationResponse {
    // 1. 首先检查WebView是否能显示该内容类型
    if (!navigationResponse.canShowMIMEType) {
        return YES; // WebView无法显示，可以下载
    }

    // 2. 获取MIME类型
    NSString *mimeType = navigationResponse.response.MIMEType;
    if (!mimeType) {
        return NO; // 没有MIME类型，默认不下载
    }

    // 3. 检查Content-Disposition头
    if ([navigationResponse.response isKindOfClass:[NSHTTPURLResponse class]]) {
        NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *)navigationResponse.response;
        NSString *contentDisposition = [httpResponse.allHeaderFields objectForKey:@"Content-Disposition"];
        if (contentDisposition && [contentDisposition containsString:@"attachment"]) {
            return YES; // 服务器指定为附件下载
        }
    }

    // 4. 检查是否是可下载的MIME类型
    NSArray *downloadableMimeTypes = [self downloadableMimeTypes];
    if ([downloadableMimeTypes containsObject:mimeType]) {
        return YES;
    }

    return NO;
}

/**
 * 判断响应是否应该显示查看选项
 * @param navigationResponse WKNavigationResponse对象
 * @return BOOL 是否应该显示查看选项
 */
- (BOOL)shouldShowViewOption:(WKNavigationResponse *)navigationResponse {
    // 1. 检查WebView是否能显示该内容类型
    if (!navigationResponse.canShowMIMEType) {
        return NO; // WebView无法显示，不显示查看选项
    }

    // 2. 获取MIME类型
    NSString *mimeType = navigationResponse.response.MIMEType;
    if (!mimeType) {
        return NO; // 没有MIME类型，不显示查看选项
    }

    // 3. 检查是否属于downloadableMimeTypes类型
    NSArray *downloadableMimeTypes = [self downloadableMimeTypes];
    if ([downloadableMimeTypes containsObject:mimeType]) {
        return YES; // 属于可下载类型且WebView能显示，显示查看选项
    }

    return NO;
}

// 5 当内容开始返回时调用
- (void)webView:(WKWebView *)webView didCommitNavigation:(null_unspecified WKNavigation *)navigation
{
//    LOG_DEBUG(@"5、 %s, url = %@, canGoBack = %d, canGoForward = %d", __func__, webView.URL.absoluteString, webView.canGoBack, webView.canGoForward);

    Tab* tab = [self.tabManager tabForWebView:webView];
    if(!tab) return;

    tab.model.url = webView.URL.absoluteString;

    if(self.tabManager.selectedTab == tab) {
        [self updatePageStatusForTab:tab];
    }
}

// 6 页面加载完成之后调用
- (void)webView:(WKWebView *)webView didFinishNavigation:(null_unspecified WKNavigation *)navigation
{
//    LOG_DEBUG(@"6、 %s, url = %@, title = %@", __func__, webView.URL.absoluteString, webView.title);

    Tab* tab = [self.tabManager tabForWebView:webView];
    [self navigateInTab:tab];

    //已经完成写入,清除记录
    tab.currentHistoryModel = nil;

    NSURL* URL = webView.URL;
    if([[URL pathExtension] isEqualToString:@"js"]) {
        //脚本检测
        UIAlertController* alertController = [UIAlertController alertControllerWithTitle:NSLocalizedString(@"alert.install.script", nil) message:@"" preferredStyle:UIAlertControllerStyleAlert];
        UIAlertAction* action = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.cancel", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            [alertController dismissViewControllerAnimated:YES completion:nil];
        }];
        [alertController addAction:action];

        action = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.install", nil) style:UIAlertActionStyleDestructive handler:^(UIAlertAction * _Nonnull action) {
            [UserScriptHelper installScriptWithURL:URL tab:self.tabManager.selectedTab];
        }];
        [alertController addAction:action];

//        [self presentViewController:alertController animated:YES completion:nil];
        //v2.6.8, 统一present方法，防止崩溃
        [alertController presentSafelyFromViewController:self];
    } else {
        //截屏
//        [[ScreenshotHelper shareInstance] takeScreenshot:tab];
    }
}

// 6 页面加载失败时调用
- (void)webView:(WKWebView *)webView didFailProvisionalNavigation:(null_unspecified WKNavigation *)navigation withError:(NSError *)error
{
//    LOG_DEBUG(@"99、 %s, url = %@", __func__, webView.URL.absoluteString);

    //清空历史记录
    Tab* tab = [self.tabManager tabForWebView:webView];
    if(tab) {
        tab.currentHistoryModel = nil;
    }

    // Ignore the "Frame load interrupted" error that is triggered when we cancel a request
    // to open an external application and hand it over to UIApplication.openURL(). The result
    // will be that we switch to the external app, for example the app store, while keeping the
    // original web page in the tab instead of replacing it with an error page.
    if([error.domain isEqualToString:@"WebKitErrorDomain"] && error.code == 102) return;

    //checkIfWebContentProcessHasCrashed
    if(error.code == WKErrorWebContentProcessTerminated && [error.domain isEqualToString:@"WebKitErrorDomain"]) {
        LOG_ERROR(@"WebContent process has crashed. Trying to reload to restart it.");
        [webView reload];
        return;
    }

    if(error.code == NSURLErrorCancelled) return;

    NSURL* errorUrl = error.userInfo[NSURLErrorFailingURLErrorKey];
    if(!errorUrl) return;

    //这里失败的话,不会返回失败的url, 而是返回失败前的URL, 逻辑不对
    NSString* lastUrl = tab.model.url;
    if(lastUrl.length == 0) {
        //lastUrl可能为空
        lastUrl = [errorUrl absoluteString];
    }
    NSString* url = [InternalURL errorPageWithUrl:lastUrl];
    NSURLRequest* request = [[NSURLRequest alloc]initWithURL:[NSURL URLWithString:url]];
    [tab loadRequest:request];
}

// 6 页面加载失败时调用
- (void)webView:(WKWebView *)webView didFailNavigation:(null_unspecified WKNavigation *)navigation withError:(NSError *)error
{
//    LOG_DEBUG(@"100、  %s, url = %@", __func__, webView.URL.absoluteString);

    //清空历史记录
    Tab* tab = [self.tabManager tabForWebView:webView];
    if(tab) {
        tab.currentHistoryModel = nil;
    }
}

//4.1、 接收到服务器跳转请求之后调用
- (void)webView:(WKWebView *)webView didReceiveServerRedirectForProvisionalNavigation:(null_unspecified WKNavigation *)navigation
{
//    LOG_DEBUG(@"3.2、 %s, url = %@", __func__,· webView.URL.absoluteString);
}

//3、 需要响应身份验证时调用 同样在block中需要传入用户身份凭证
- (void)webView:(WKWebView *)webView didReceiveAuthenticationChallenge:(NSURLAuthenticationChallenge *)challenge completionHandler:(void (^)(NSURLSessionAuthChallengeDisposition disposition, NSURLCredential * _Nullable credential))completionHandler
{
//    NSLog(@"3.1、 %d, %s, method = %@, title = %@", __LINE__, __func__, challenge.protectionSpace.authenticationMethod, webView.title);

    //2.3.5版本
    if(challenge.protectionSpace.authenticationMethod == NSURLAuthenticationMethodServerTrust) {
        SecTrustRef trust = challenge.protectionSpace.serverTrust;
        if(trust) {
            SecCertificateRef cert = SecTrustGetCertificateAtIndex(trust, 0);
            if(cert) {
                completionHandler(NSURLSessionAuthChallengeUseCredential, [[NSURLCredential alloc]initWithTrust:trust]);
                return;
            } else {
                //jingjiaoba.com
                NSURLCredential *credential = [[NSURLCredential alloc]initWithTrust:trust];
                completionHandler(NSURLSessionAuthChallengeUseCredential,credential);
            }
        }
    }

    //NSURLAuthenticationMethodHTTPBasic -- 需要一个用户名和密码
    //NSURLAuthenticationMethodHTTPDigest -- 需要一个用户名和密码
    //NSURLAuthenticationMethodNTLM NTLM认证
    if(challenge.protectionSpace.authenticationMethod == NSURLAuthenticationMethodHTTPBasic ||
       challenge.protectionSpace.authenticationMethod == NSURLAuthenticationMethodHTTPDigest ||
       challenge.protectionSpace.authenticationMethod == NSURLAuthenticationMethodNTLM) {
        //reject
    } else {
        completionHandler(NSURLSessionAuthChallengePerformDefaultHandling, nil);
        return;
    }

    completionHandler(NSURLSessionAuthChallengeRejectProtectionSpace, nil);

    //2.3.4版本
//    if(challenge.protectionSpace.authenticationMethod == NSURLAuthenticationMethodServerTrust) {
//        SecTrustRef trust = challenge.protectionSpace.serverTrust;
//        SecCertificateRef cert = SecTrustGetCertificateAtIndex(trust, 0);
//        if(cert) {
//            completionHandler(NSURLSessionAuthChallengeUseCredential, [[NSURLCredential alloc]initWithTrust:trust]);
//            return;
//        } else {
//            //jingjiaoba.com
//            NSURLCredential *credential = [[NSURLCredential alloc]initWithTrust:trust];
//            completionHandler(NSURLSessionAuthChallengeUseCredential,credential);
//        }
//    }
//
//    //NSURLAuthenticationMethodHTTPBasic -- 需要一个用户名和密码
//    //NSURLAuthenticationMethodHTTPDigest -- 需要一个用户名和密码
//    //NSURLAuthenticationMethodNTLM NTLM认证
//    if(challenge.protectionSpace.authenticationMethod == NSURLAuthenticationMethodHTTPBasic ||
//       challenge.protectionSpace.authenticationMethod == NSURLAuthenticationMethodHTTPDigest ||
//       challenge.protectionSpace.authenticationMethod == NSURLAuthenticationMethodNTLM) {
//        //reject
//    } else {
//        completionHandler(NSURLSessionAuthChallengePerformDefaultHandling, nil);
//        return;
//    }
//
//    completionHandler(NSURLSessionAuthChallengeRejectProtectionSpace, nil);
}

// 进程被终止时调用
- (void)webViewWebContentProcessDidTerminate:(WKWebView *)webView
{
//    LOG_DEBUG(@" %d, %s, url = %@", __LINE__, __func__, webView.URL.absoluteString);
}

#pragma mark - URL查看状态管理

- (NSMutableSet *)viewingURLs {
    NSMutableSet *viewingURLs = objc_getAssociatedObject(self, kViewingURLsKey);
    if (!viewingURLs) {
        viewingURLs = [NSMutableSet set];
        objc_setAssociatedObject(self, kViewingURLsKey, viewingURLs, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    }
    return viewingURLs;
}

- (void)setViewingURL:(NSURL *)url {
    if (url) {
        [[self viewingURLs] addObject:url.absoluteString];
    }
}

- (BOOL)isViewingURL:(NSURL *)url {
    if (!url) return NO;
    return [[self viewingURLs] containsObject:url.absoluteString];
}

- (void)clearViewingURL:(NSURL *)url {
    if (url) {
        [[self viewingURLs] removeObject:url.absoluteString];
    }
}

@end
