//
//  BrowserViewController+BottomToolbarDelegate.m
//  PandaBrowser
//
//  Created by qingbin on 2022/3/9.
//

#import "BrowserViewController+BottomToolbarDelegate.h"
#import "AddTabAnimationController.h"

#import "UIColor+Helper.h"
#import "ReactiveCocoa.h"

#import <StoreKit/StoreKit.h>
#import "PreferenceManager.h"
#import "MaizyHeader.h"

#import "InternalURL.h"

#import "PaymentManager.h"
#import "DatabaseUnit+Helper.h"

#import "HomeHelper.h"
#import "BrowserViewController+ToolbarGesture.h"
#import "UIAlertController+SafePresentation.h"

@implementation BrowserViewController (BottomToolbarDelegate)

// 点击返回按钮
- (void)bottomToolbarDidClickBack
{
    [self.tabManager.selectedTab goBack];
}

// 点击前进按钮
- (void)bottomToolbarDidClickForward
{
    [self.tabManager.selectedTab goForward];
}

// 点击新建标签页
- (void)bottomToolbarDidClickAddTab
{    
    if(self.tabManager.selectedTab) {
        [[ScreenshotHelper shareInstance] takeScreenshot:self.tabManager.selectedTab];
    }
    
    TabModel* tabModel = [HomeHelper getHomeTabModel];
    if([HomeHelper isCustomUrl]) {
        //自定义首页url
        [self.tabManager addTabAndSelect:tabModel];
    } else {
        //默认
//        CGPoint contentOffset = [self.ntpView contentOffset];
//        AddTabAnimationController* vc = [[AddTabAnimationController alloc]initWithContentOffset:contentOffset];
        AddTabAnimationController* vc = [[AddTabAnimationController alloc]init];
        UINavigationController* navc = [[UINavigationController alloc]initWithRootViewController:vc];
        navc.modalPresentationStyle = UIModalPresentationFullScreen;
        navc.transitioningDelegate = vc;
        //这句代码很重要,动画切换过程中防止看到底部
        //暗黑模式适配
        BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
        if(isDarkTheme) {
            navc.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        } else {
            navc.view.backgroundColor = UIColor.whiteColor;
        }

        int allTabsCount = [BrowserUtils shareInstance].allTabsCount;
        //更新标签个数
        [vc updateTabCount:allTabsCount];
        
        [self.bottomToolbar updateTabCount:allTabsCount];
        [self.topToolbar updateTabCount:allTabsCount];
            
        //先加载完,要不然会出现闪动问题
        self.view.hidden = YES;
        //新开一页
        [self.tabManager addTabAndSelect:tabModel];
        
        @weakify(self)
        [vc setDidAnimationCompletedBlock:^{
            @strongify(self)
            self.view.hidden = NO;
        }];
        
        [self presentViewController:navc animated:YES completion:nil];
    }
}

// 点击标签页
- (void)bottomToolbarDidClickTabs
{
    [self showTabTray];
}

// v2.6.8
// 长按标签页
//- (void)bottomToolbarDidLongPressTabs:(UIView*)tabsButton
//{
//    // 小震动
//    UIImpactFeedbackGenerator* feedback = [[UIImpactFeedbackGenerator alloc]initWithStyle:UIImpactFeedbackStyleMedium];
//    [feedback prepare];
//    [feedback impactOccurred];
//    
//    //长按事件
//    UIAlertController* alertController = [UIAlertController alertControllerWithTitle:nil message:nil preferredStyle:UIAlertControllerStyleActionSheet];
//    @weakify(self)
//    UIAlertAction* action = [UIAlertAction actionWithTitle:NSLocalizedString(@"tab.new.tab", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
//        @strongify(self)
//        [alertController dismissViewControllerAnimated:YES completion:nil];
//        //增加一个tab
//        [BrowserUtils shareInstance].allTabsCount++;
//        
//        [self bottomToolbarDidClickAddTab];
//    }];
//    [alertController addAction:action];
//    
//    action = [UIAlertAction actionWithTitle:NSLocalizedString(@"tab.close.tab", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
//        @strongify(self)
//        [alertController dismissViewControllerAnimated:YES completion:nil];
//        
//        [self _closeCurrentTab];
//    }];
//    [alertController addAction:action];
//    
//    action = [UIAlertAction actionWithTitle:NSLocalizedString(@"tab.close.other.tab", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
//        @strongify(self)
//        [alertController dismissViewControllerAnimated:YES completion:nil];
//        
//        [self _closeOtherTabs];
//    }];
//    [alertController addAction:action];
//    
//    action = [UIAlertAction actionWithTitle:NSLocalizedString(@"tab.close.all.tab", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
//        @strongify(self)
//        [alertController dismissViewControllerAnimated:YES completion:nil];
//        
//        [self _closeAllTabs];
//    }];
//    [alertController addAction:action];
//
//    action = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.cancel", nil) style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
//        [alertController dismissViewControllerAnimated:YES completion:nil];
//    }];
//    [alertController addAction:action];
//
//    if([BrowserUtils isiPad]) {
//        UIPopoverPresentationController* popover = alertController.popoverPresentationController;
//        popover.sourceView = self.view;
//        
//        CGRect sourceRect = [tabsButton.superview convertRect:tabsButton.frame toView:self.view];
//        popover.sourceRect = sourceRect;
//    }
//
//    [self presentViewController:alertController animated:YES completion:nil];
//}

//#pragma mark -- 关闭当前标签
//- (void)_closeCurrentTab
//{
//    DatabaseUnit* unit = [DatabaseUnit selectAllTabs];
//    @weakify(self)
//    unit.completeBlock = ^(NSArray<TabModel*>* result, BOOL success) {
//        @strongify(self)
//        if(success && result.count>0) {
//            [self.tabManager closeCurrentTab:result];
//        }
//    };
//    
//    DB_EXEC(unit);
//}

//#pragma mark -- 关闭其它标签
//- (void)_closeOtherTabs
//{
//    DatabaseUnit* unit = [DatabaseUnit selectAllTabs];
//    @weakify(self)
//    unit.completeBlock = ^(NSArray<TabModel*>* result, BOOL success) {
//        @strongify(self)
//        if(success && result.count>0) {
//            [self.tabManager closeOtherTabs:result];
//        }
//    };
//    
//    DB_EXEC(unit);
//}

//#pragma mark -- 关闭所有标签
//- (void)_closeAllTabs
//{
//    [self.tabManager removeAllTabsFromTabTray];
//}

// 点击功能按钮
- (void)bottomToolbarDidClickMenu:(BOOL)isTriggerByBottomBar
{
    //Bundleid判断, 防止重签名
    BOOL isVip = [[PaymentManager shareInstance] isVip];
    if(isVip) {
        NSString* bundleId = [[NSBundle mainBundle] objectForInfoDictionaryKey:@"CFBundleIdentifier"];
        if(![bundleId isEqualToString:@"com.qingbin.focusbrowser"]) {
            UIAlertController* alertController = [UIAlertController alertControllerWithTitle:@"请支持正版应用" message:nil preferredStyle:UIAlertControllerStyleAlert];
            UIAlertAction* action = [UIAlertAction actionWithTitle:@"我知道了" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                [PaymentManager shareInstance].isVip = NO;
            }];
            [alertController addAction:action];
            
//            [self presentViewController:alertController animated:YES completion:nil];
            //v2.6.8, 统一present方法，防止崩溃
            [alertController presentSafelyFromViewController:self];
            return;
        }
    }
    
    [self openUserPreferenceView:isTriggerByBottomBar];
}

// 点击首页
- (void)bottomToolbarDidClickHome
{
#ifdef DEBUG
#else
    NSInteger reviewCount = [[PreferenceManager shareInstance].items.reviewCount integerValue];
    if(reviewCount >= 5) {
        //一句话实现在App内直接评论了。然而需要注意的是：打开次数一年不能多于3次。（开发期间可以无限制弹出，方便测试）
        [SKStoreReviewController requestReview];
    } else {
        reviewCount++;
        if(reviewCount > 10) {
            //防止溢出
            reviewCount = 10;
        } else {
            //大于10之后就不再需要保存了
            [PreferenceManager shareInstance].items.reviewCount = @(reviewCount);
            [[PreferenceManager shareInstance] encode];
        }
    }
#endif
    
    //点击回到首页
    Tab* tab = self.tabManager.selectedTab;
    PandaWebView* webView = tab.webView;
    if(!webView) return;
    
    NSArray* backList = webView.backForwardList.backList;
    WKBackForwardListItem* homeItem;
    if(backList.count > 0) {
        homeItem = backList.firstObject;
        
        if(homeItem) {
            [webView goToBackForwardListItem:homeItem];
        }
    }
    
//    if([HomeHelper isCustomUrl]) {
//        //自定义首页url
//        NSArray* backList = webView.backForwardList.backList;
//        WKBackForwardListItem* homeItem;
//        if(backList.count > 0) {
//            homeItem = backList.firstObject;
//            
//            if(homeItem) {
//                [webView goToBackForwardListItem:homeItem];
//            }
//        }
//    } else {
//        //默认，原有逻辑
//        int homeIndex = [tab hasHomeUrl];
//        if(homeIndex != -1) {
//            //存在首页
//            
//            //使用系统的方法返回首页
//            NSArray* backList = webView.backForwardList.backList;
//            WKBackForwardListItem* homeItem;
//            if(backList.count > 0) {
//                homeItem = backList.firstObject;
//                
//                if(homeItem) {
//                    [webView goToBackForwardListItem:homeItem];
//                }
//            } else {
//                if([webView canGoBack]) {
//                    [webView goBack];
//                }
//            }
//        } else {
//            //不存在首页,说明是新开的一页,然后点击了首页按钮
//            //那么将首页入栈
//            NSURL* URL = [NSURL URLWithString:[InternalURL homeUrl]];
//            NSURLRequest* request = [NSURLRequest requestWithURL:URL];
//            [tab loadRequest:request];
//        }
//    }
}

// 添加工具栏长按手势代理方法实现
- (void)bottomToolbarDidLongPress:(UIView *)button forToolbarGroup:(ToolbarGroup)group {
    [self handleToolbarLongPress:button forToolbarGroup:group];
}

@end
