//
//  BrowserViewController+WKNavigationDelegate.h
//  PandaBrowser
//
//  Created by qingbin on 2022/3/8.
//

#import "BrowserViewController.h"
#import <WebKit/WebKit.h>
#import "Tab.h"
#import "TabManager.h"

//https://www.jianshu.com/p/b33f05dfff16

@interface BrowserViewController (WKNavigationDelegate)<WKNavigationDelegate>

// 设置正在查看的URL，避免重复弹窗
- (void)setViewingURL:(NSURL *)url;

// 检查URL是否正在查看中
- (BOOL)isViewingURL:(NSURL *)url;

// 清除查看状态
- (void)clearViewingURL:(NSURL *)url;

@end

