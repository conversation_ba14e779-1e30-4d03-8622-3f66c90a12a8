//
//  BrowserViewController+TabDelegate.m
//  PandaBrowser
//
//  Created by qingbin on 2022/3/8.
//

#import "BrowserViewController+TabDelegate.h"
#import "Masonry.h"
#import "WebViewNavDelegate.h"
#import "ReactiveCocoa.h"
#import "MaizyHeader.h"
#import "InternalURL.h"

#import "FindInPageHelper.h"
#import "PandaWebView.h"
#import "PlaylistDetectorHelper.h"
#import "MarkHelper.h"

#import "GetAllImageHelper.h"
#import "ErrorPageHelper.h"
#import "UserScriptHelper.h"
#import "MetadataHelper.h"
#import "ContextMenuHelper.h"
#import "TagitHelper.h"
#import "TranslateHelper.h"
#import "BlobHelper.h"
#import "AutoPageHelper.h"
#import "ReadabilityHelper.h"
#import "ReaderHelper.h"

@implementation BrowserViewController (TabDelegate)

- (void)tab:(Tab*)tab didCreateWebView:(WKWebView*)webView
{
    //添加监听
    NSKeyValueObservingOptions option = NSKeyValueObservingOptionNew | NSKeyValueObservingOptionOld;
    NSArray* observerKeys = [BrowserViewController observerKeys];
    for(NSString* key in observerKeys) {
        [webView addObserver:self forKeyPath:key options:option context:nil];
    }
    
    webView.UIDelegate = self;
    
//    [self addWebView:tab.webView];
    
    //添加js到oc的回调
    [self addContentScriptsInTab:tab];
    
    //将webView的导航代理给WebViewNavDelegate单列, 然后单列中再进行相对应的逻辑分发
    tab.webView.navigationDelegate = [WebViewNavDelegate shareInstance];
}

- (void)addContentScriptsInTab:(Tab*)tab
{
    //添加js到oc的回调
    
    //阅读模式
//    ReadabilityMessageHelper* readerModeHelper = [[ReadabilityMessageHelper alloc]initWithTab:tab];
//    NSString* scriptMessageHandlerName = [readerModeHelper scriptMessageHandlerName];
//    [tab.webView.configuration.userContentController addScriptMessageHandler:readerModeHelper name:scriptMessageHandlerName];
    
    //阅读模式/查找目录+上一章+下一章
//    GetBookDetailHelper* bookHelper = [[GetBookDetailHelper alloc]initWithTab:tab];
//    NSString* scriptMessageHandlerName = [bookHelper scriptMessageHandlerName];
//    [tab.webView.configuration.userContentController addScriptMessageHandler:bookHelper name:scriptMessageHandlerName];
    
    //网页内查找
    FindInPageHelper* findInPageHelper = [[FindInPageHelper alloc]initWithTab:tab];
    NSString* scriptMessageHandlerName = [findInPageHelper scriptMessageHandlerName];
    [tab.webView.configuration.userContentController addScriptMessageHandler:findInPageHelper name:scriptMessageHandlerName];
    
    //看图模式
    GetAllImageHelper* getAllImageHelper = [[GetAllImageHelper alloc]initWithTab:tab];
    scriptMessageHandlerName = [getAllImageHelper scriptMessageHandlerName];
    [tab.webView.configuration.userContentController addScriptMessageHandler:getAllImageHelper name:scriptMessageHandlerName];
    
    //错误页提示
    ErrorPageHelper* errorPageHelper = [[ErrorPageHelper alloc]initWithTab:tab];
    scriptMessageHandlerName = [errorPageHelper scriptMessageHandlerName];
    [tab.webView.configuration.userContentController addScriptMessageHandler:errorPageHelper name:scriptMessageHandlerName];
    
    //视频/音频播放
    PlaylistDetectorHelper* playerHelper = [[PlaylistDetectorHelper alloc]initWithTab:tab];
    scriptMessageHandlerName = [playerHelper scriptMessageHandlerName];
    [tab.webView.configuration.userContentController addScriptMessageHandler:playerHelper name:scriptMessageHandlerName];
    
    //长按弹窗
    ContextMenuHelper* contextMenuHelper = [[ContextMenuHelper alloc]initWithTab:tab];
    scriptMessageHandlerName = [contextMenuHelper scriptMessageHandlerName];
    [tab.webView.configuration.userContentController addScriptMessageHandler:contextMenuHelper name:scriptMessageHandlerName];
    
    //获取网页meta信息
    MetadataHelper* metadataHelper = [[MetadataHelper alloc]initWithTab:tab];
    scriptMessageHandlerName = [metadataHelper scriptMessageHandlerName];
    [tab.webView.configuration.userContentController addScriptMessageHandler:metadataHelper name:scriptMessageHandlerName];
    
    //用户脚本
    UserScriptHelper* userScriptHelper = [[UserScriptHelper alloc]initWithTab:tab];
    scriptMessageHandlerName = [userScriptHelper scriptMessageHandlerName];
    [tab.webView.configuration.userContentController addScriptMessageHandler:userScriptHelper name:scriptMessageHandlerName];
    
    //tagit
    TagitHelper* tagitHelper = [[TagitHelper alloc]initWithTab:tab];
    scriptMessageHandlerName = [tagitHelper scriptMessageHandlerName];
    [tab.webView.configuration.userContentController addScriptMessageHandler:tagitHelper name:scriptMessageHandlerName];
    
    //翻译脚本
    TranslateHelper* translateHelper = [[TranslateHelper alloc]initWithTab:tab];
    scriptMessageHandlerName = [translateHelper scriptMessageHandlerName];
    [tab.webView.configuration.userContentController addScriptMessageHandler:translateHelper name:scriptMessageHandlerName];
    
    //Blob加密链接解析
    BlobHelper* blobHelper = [[BlobHelper alloc]initWithTab:tab];
    scriptMessageHandlerName = [blobHelper scriptMessageHandlerName];
    [tab.webView.configuration.userContentController addScriptMessageHandler:blobHelper name:scriptMessageHandlerName];
    
    //v2.6.2 智能拼页
    AutoPageHelper* autoPageHelper = [[AutoPageHelper alloc]initWithTab:tab];
    scriptMessageHandlerName = [autoPageHelper scriptMessageHandlerName];
    [tab.webView.configuration.userContentController addScriptMessageHandler:autoPageHelper name:scriptMessageHandlerName];
    
    //v2.6.3标记html元素
    MarkHelper* markHelper = [[MarkHelper alloc]initWithTab:tab];
    scriptMessageHandlerName = [markHelper scriptMessageHandlerName];
    [tab.webView.configuration.userContentController addScriptMessageHandler:markHelper name:scriptMessageHandlerName];
    
    //v2.7.6,阅读模式
    ReadabilityHelper* readabilityHelper = [[ReadabilityHelper alloc]initWithTab:tab];
    scriptMessageHandlerName = [readabilityHelper scriptMessageHandlerName];
    [tab.webView.configuration.userContentController addScriptMessageHandler:readabilityHelper name:scriptMessageHandlerName];
    
    //v2.7.6,阅读模式
    ReaderHelper* readerHelper = [[ReaderHelper alloc]initWithTab:tab];
    scriptMessageHandlerName = [readerHelper scriptMessageHandlerName];
    [tab.webView.configuration.userContentController addScriptMessageHandler:readerHelper name:scriptMessageHandlerName];
}

- (void)tab:(Tab*)tab didDeleteWebView:(WKWebView*)webView
{
    NSArray* observerKeys = [BrowserViewController observerKeys];
    for(NSString* key in observerKeys) {
        [webView removeObserver:self forKeyPath:key];
    }
    
    [webView.configuration.userContentController removeAllUserScripts];
    webView.UIDelegate = nil;
    webView.scrollView.delegate = nil;
    [webView removeFromSuperview];
}

- (void)tab:(Tab*)tab didFindInBar:(WKWebView*)webView text:(NSString*)text
{
    //页面查找
    [self updateFindInPageVisibility:YES text:text];
}

- (void)observeValueForKeyPath:(NSString *)keyPath
                      ofObject:(id)object
                        change:(NSDictionary<NSKeyValueChangeKey,id> *)change
                       context:(void *)context
{
    if(![object isMemberOfClass:PandaWebView.class]) return;
    PandaWebView* webView = object;
    
    Tab* tab = [self.tabManager tabForWebView:object];
    if(!tab) return;
    //仅处理当前tab
    if(tab != self.tabManager.selectedTab) return;
    
    if([keyPath isEqualToString:kWebViewCanGoBack]) {
        //can go back
        BOOL canGoBack = [[change objectForKey:@"new"] boolValue];
        [self.bottomToolbar updateGoBackStatus:canGoBack];
        [self.topToolbar updateGoBackStatus:canGoBack];
    } else if([keyPath isEqualToString:kWebViewCanGoForward]) {
        //can go forward
        BOOL canGoForward = [[change objectForKey:@"new"] boolValue];
        [self.bottomToolbar updateGoForwardStatus:canGoForward];
        [self.topToolbar updateGoForwardStatus:canGoForward];
    } else if([keyPath isEqualToString:kWebViewEstimatedProgress]) {
        //estimatedProgress
        if([InternalURL isValid:webView.URL]) {
            [self.topToolbar hideProgressBar];
        } else {
            float progress = [[change objectForKey:@"new"] floatValue];
            
            if(progress >= 1.0 || progress < 0) {
                [self.topToolbar hideProgressBar];
            } else {
                [self.topToolbar updateProgressBar:progress];
            }
        }
    } else if([keyPath isEqualToString:kWebViewLoading]) {
        //loading
        BOOL isLoading = [[change objectForKey:@"new"] floatValue];
        if(isLoading) {
            if([InternalURL isValid:webView.URL]) {
                [self.topToolbar hideProgressBar];
            } else {
                float progress = [[change objectForKey:@"new"] floatValue];
                if(progress < 0.1) {
                    [self.topToolbar updateProgressBar:0.1];
                }
            }
        } else {
            [self.topToolbar hideProgressBar];
        }
        
        [self.topToolbar updateLoadingStatus:isLoading];
    } else if([keyPath isEqualToString:kWebViewURL]) {
        //URL
        if(webView.URL == nil) return;
        Tab* tab = [self.tabManager tabForWebView:webView];
        //url的变化需要同步更新工具栏+首页是否隐藏等逻辑,url的kvo比didCommit快
        [self updatePageStatusForTab:tab];
        //更新历史
        [self navigateInTab:tab];
        
        // 注意返回的时候, 不触发decidePolicyForNavigationAction
        // 切换了URL,那么清空webView的阅读模式和播放模式
        // 清空阅读模式和播放模式
        [self clearWebViewReaderAndPlayer:(PandaWebView*)webView];
    } else if([keyPath isEqualToString:kWebViewTitle]) {
        //title
        NSString* title = webView.title;
        if(title.length == 0) {
            NSString* urlString = [webView.URL absoluteString];
            if(![InternalURL isAboutHomeURL:urlString]) {
                title = urlString;
            }
        }

        Tab* tab = [self.tabManager tabForWebView:webView];
        //url的变化需要同步更新工具栏+首页是否隐藏等逻辑,url的kvo比didCommit快
        [self updatePageStatusForTab:tab];
        //更新历史
        [self navigateInTab:tab];
        
        [tab.model updateTitleWithWebViewTitle:webView.title url:webView.URL toDisk:YES];
        
    } else if([keyPath isEqualToString:kWebViewHasOnlySecureContent]) {
        //hasOnlySecureContent
        
    } else if([keyPath isEqualToString:kWebViewServerTrust]) {
        //serverTrust
        
    }
    
    //壁纸相关
    NSString* url = tab.webView.URL.absoluteString;
    if(url.length <= 0) {
        url = tab.model.url;
    }
    
    if(url.length > 0) {
        BOOL isSessionRestore = [InternalURL isSessionRestore:url];
        BOOL isHomePage = [InternalURL isAboutHomeURL:url];
        
        //如果是历史记录还在转换中的url,那么先不处理
        if(isHomePage) {
            [self.bottomToolbar updateIsHomePage:isHomePage];
        } else if(!isSessionRestore) {
            [self.bottomToolbar updateIsHomePage:isHomePage];
        }
    }
}

+ (NSArray<NSString*>*)observerKeys
{
    return @[kWebViewEstimatedProgress,kWebViewURL,kWebViewTitle,kWebViewCanGoBack,
             kWebViewCanGoForward,kWebViewLoading,kWebViewHasOnlySecureContent,kWebViewServerTrust];
}

@end
