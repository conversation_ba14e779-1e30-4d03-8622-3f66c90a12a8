//
//  BrowserViewController+WKUIDelegate.m
//  PPBrowser
//
//  Created by qingbin on 2022/3/26.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "BrowserViewController+WKUIDelegate.h"
#import "UserComandModel.h"
#import "UserCommandManager.h"
#import "Tab.h"
#import "TabModel.h"
#import "PPEnums.h"

#import "Tampermonkey.h"
#import "UserScript.h"
#import "LinkPreviewController.h"
#import "UIView+Helper.h"
#import "NSString+Helper.h"
#import "MaizyHeader.h"

#import <WebKit/WebKit.h>
#import "InternalURL.h"

#import "WebBlacklistHelper.h"

#import "HomeHelper.h"
#import "UIAlertController+SafePresentation.h"

@implementation BrowserViewController (WKUIDelegate)

- (nullable WKWebView *)webView:(WKWebView *)webView createWebViewWithConfiguration:(WKWebViewConfiguration *)configuration forNavigationAction:(WKNavigationAction *)navigationAction windowFeatures:(WKWindowFeatures *)windowFeatures
{
//    LOG_DEBUG(@"webView's URL = %@", webView.URL);
    
    //网页黑名单，直接返回
    if([[WebBlacklistHelper shareInstance] isBlockURL:webView.URL]) {
        return nil;
    }
    
    //是否允许自定义拦截跳转
    BOOL canCustomOpenUrl = [self _canCustomOpenUrlWithNavigationAction:navigationAction];
    if (!canCustomOpenUrl) {
        return [self _customOpenUrlWithConfiguration:configuration];
    }
    
    PopupWindowOption option = [[PreferenceManager shareInstance].items.popupWindowOption intValue];
    if(option == PopupWindowOptionDefault) {
        return [self _handleDefaultOpenWindowWithWebView:webView
                                           configuration:configuration
                                     forNavigationAction:navigationAction
                                                  option:option];
    } else {
        [self _handleOpenWindowWithWebView:webView forNavigationAction:navigationAction option:option];
    }
    
    return nil;
}

- (void)_handleOpenWindowWithWebView:(WKWebView *)webView
                 forNavigationAction:(WKNavigationAction *)navigationAction
                              option:(PopupWindowOption)option
{
    UIAlertControllerStyle style = UIAlertControllerStyleActionSheet;
    if([BrowserUtils isiPad]) {
        //iPad
        style = UIAlertControllerStyleAlert;
    }
    
//    Tab* currentTab = self.tabManager.selectedTab;
    if(option == PopupWindowOptionAlwaysAsk) {
        UIAlertController *alertController = [UIAlertController alertControllerWithTitle:NSLocalizedString(@"popupwindow.webview.title", nil) message:nil preferredStyle:style];
        [alertController addAction:([UIAlertAction actionWithTitle:NSLocalizedString(@"alert.cancel", nil) style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
            
        }])];
        [alertController addAction:([UIAlertAction actionWithTitle:NSLocalizedString(@"popupwindow.setting.openInCurrent", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            //当前窗口打开
            [self handleOpenAction:navigationAction.request.URL];
        }])];
        [alertController addAction:([UIAlertAction actionWithTitle:NSLocalizedString(@"popupwindow.setting.openInInNewWindow", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            //新窗口打开
            [self handleOpenInNewTabAction:navigationAction.request.URL];
        }])];
        [alertController addAction:([UIAlertAction actionWithTitle:NSLocalizedString(@"popupwindow.setting.openInInBackWindow", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            //后台窗口打开
            [self handleOpenInBackendTabAction:navigationAction.request.URL];
        }])];
        
//        [self presentViewController:alertController animated:YES completion:nil];
        //v2.6.8, 统一present方法，防止崩溃
        [alertController presentSafelyFromViewController:self];
    } else if(option == PopupWindowOptionOpenInCurrent) {
        //当前窗口打开
        [self handleOpenAction:navigationAction.request.URL];
    } else if(option == PopupWindowOptionOpenInNewWindow) {
        //新窗口打开
        [self handleOpenInNewTabAction:navigationAction.request.URL];
    } else if(option == PopupWindowOptionOpenInBackWindow) {
        //后台窗口打开
        [self handleOpenInBackendTabAction:navigationAction.request.URL];
    }
}

//打开窗口,默认行为
- (WKWebView *)_handleDefaultOpenWindowWithWebView:(WKWebView *)webView
                                     configuration:(WKWebViewConfiguration *)configuration
                               forNavigationAction:(WKNavigationAction *)navigationAction
                                            option:(PopupWindowOption)option
{
    //参考下面链接,在原网页中打开
    //https://www.jianshu.com/p/3a75d7348843
    //https://www.jianshu.com/p/561307f8aa9e

    Tab* currentTab = self.tabManager.selectedTab;

    //如果能在同一个Tab打开, 那么尽量在同一个打开
    //zhongmu.gov.cn - 链接点击
    ///<WKNavigationAction: 0x7fce96b5b640; navigationType = 0; syntheticClickType = 1; position x = 199.00 y = 1266.00 request = <NSMutableURLRequest: 0x600000574870> { URL: https://public.zhongmu.gov.cn/D0102X/6573730.jhtml }; sourceFrame = <WKFrameInfo: 0x7fce96b3d400; webView = 0x7fce96175200; isMainFrame = YES; request = <NSMutableURLRequest: 0x600000540000> { URL: https://www.zhongmu.gov.cn/ }>; targetFrame = (null)>
    ///<NSMutableURLRequest: 0x600000574870> { URL: https://public.zhongmu.gov.cn/D0102X/6573730.jhtml }

    //jable.tv - 刷新
    ///<WKNavigationAction: 0x7fce96b59bb0; navigationType = -1; syntheticClickType = 0; position x = 0.00 y = 0.00 request = <NSMutableURLRequest: 0x600000575820> { URL: https://jable.tv/videos/hmn-284/ }; sourceFrame = <WKFrameInfo: 0x7fce96b3aec0; webView = 0x7fce9791b400; isMainFrame = YES; request = <NSMutableURLRequest: 0x600000578370> { URL: https://jable.tv/videos/hmn-284/ }>; targetFrame = (null)>

    //必须新开一页
    ///<WKNavigationAction: 0x105f18a90; navigationType = -1; syntheticClickType = 0; position x = 0.00 y = 0.00 request = <NSMutableURLRequest: 0x280332f30> { URL: https://mbledeparatea.com/?cs=dGw3YVVGWwdYZkReDlNtQVgFVGc&abt=0&red=1&sm=16&k=&v=*********&sts=0&prn=0&emb=1&tid=908057&rxy=390_844&inc=0&u=1052182240944060&agec=1669700955&fs=1&mbkb=116.0092807424594&ref=https%3A%2F%2Fdood.re%2Fe%2F4yy0n98n88um2q6jkpbm7lr45b2ea0z&osr=h-ciyuan.com&jst=8&enr=0&lcua=mozilla%2F5.0%20(iphone%3B%20cpu%20iphone%20os%2015_0%20like%20mac%20os%20x)%20applewebkit%2F605.1.15%20(khtml%2C%20like%20gecko)%20version%2F15.0%20mobile%2F15e148%20safari%2F604.1&tzd=8&uloc=zh-CN&if=0&ct=3&ctc=9&_QK0B=1669701108366&utr1=00:00:12&utr2=1&utr3=1&utr4=0&utr5=0&utr6=0&utr7=0 }; sourceFrame = <WKFrameInfo: 0x105f19810; webView = 0x112013c00; isMainFrame = NO; request = <NSMutableURLRequest: 0x280332fd0> { URL: https://dood.re/e/4yy0n98n88um2q6jkpbm7lr45b2ea0z }>; targetFrame = (null)>
    
    //根据navigationType来判断是否是链接点击
    if ([self _canCustomOpenUrlWithNavigationAction:navigationAction]) {
        [currentTab loadRequest:navigationAction.request];
        return nil;
    }
    
    return [self _customOpenUrlWithConfiguration:configuration];
}

// 是否允许自定义拦截跳转
- (BOOL)_canCustomOpenUrlWithNavigationAction:(WKNavigationAction *)navigationAction
{
    //根据navigationType来判断是否是链接点击
    if(navigationAction.navigationType == WKNavigationTypeLinkActivated) {
        WKFrameInfo* frame = navigationAction.targetFrame;
        if(!frame || (frame && !frame.isMainFrame)) {
            if(navigationAction.request) {
                return YES;
            }
        }
    }
    
    return NO;
}

// 默认跳转行为(根据configuration初始化webview)
- (PandaWebView *)_customOpenUrlWithConfiguration:(WKWebViewConfiguration *)configuration
{
    Tab* currentTab = self.tabManager.selectedTab;
    
    [[ScreenshotHelper shareInstance] takeScreenshot:currentTab];

    // If the page uses `window.open()` or `[target="_blank"]`, open the page in a new tab.
    // IMPORTANT!!: WebKit will perform the `URLRequest` automatically!! Attempting to do
    // the request here manually leads to incorrect results!!
    TabModel* tabModel = [TabModel new];
    tabModel.tabId = [NSUUID UUID].UUIDString;
//    tabModel.url = [navigationAction.request.URL absoluteString];

    //bug -- http://m.jianlaixiaoshuo.com/jianlai/page/3

    Tab* tab = [self.tabManager generateTabWithModel:tabModel configuration:configuration];
    [self.tabManager addTab:tab];
    
    //更新标签数量
    [BrowserUtils shareInstance].allTabsCount++;
    int allTabsCount = [BrowserUtils shareInstance].allTabsCount;

    [self.bottomToolbar updateTabCount:allTabsCount];
    [self.topToolbar updateTabCount:allTabsCount];

    // Wait momentarily before selecting the new tab, otherwise the parent tab
    // may be unable to set `window.location` on the popup immediately after
    // calling `window.open("")`.
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self.tabManager selectTab:tab];
    });
    
    //没办法防止新开一页,否则交互不正常
    return tab.webView;
}

#pragma - mark 实现panel
/**
 出现情况可能是WKWebView 退出的时候，JS刚好执行了window.alert(), alert 框可能弹不出来，completionHandler 最后没有被执行，导致 crash；另一种情况是在 WKWebView 一打开，JS就执行window.alert()，这个时候由于 WKWebView 所在的 UIViewController 出现（push或present）的动画尚未结束，alert 框可能弹不出来，completionHandler 最后没有被执行，导致 crash。
 */
// alert
- (void)webView:(WKWebView *)webView runJavaScriptAlertPanelWithMessage:(NSString *)message initiatedByFrame:(WKFrameInfo *)frame completionHandler:(void (^)(void))completionHandler
{
    //参考 bug https://www.jianshu.com/p/da44e10db6b0
    if ([self.navigationController visibleViewController] != self) {
        if(completionHandler) {
            completionHandler();
        }
        return;
    }

    UIAlertController *alertController = [UIAlertController alertControllerWithTitle:NSLocalizedString(@"alert.tips", nil) message:message?:@"" preferredStyle:UIAlertControllerStyleAlert];

    [alertController addAction:([UIAlertAction actionWithTitle:NSLocalizedString(@"alert.confirm", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        if(completionHandler) {
            completionHandler();
        }
    }])];

    if ([self.navigationController visibleViewController] == self) {
//        [self presentViewController:alertController animated:YES completion:nil];
        //v2.6.8, 统一present方法，防止崩溃
        [alertController presentSafelyFromViewController:self];
    } else {
        if(completionHandler) {
            completionHandler();
        }
    }
}

// confirm
- (void)webView:(WKWebView *)webView runJavaScriptConfirmPanelWithMessage:(NSString *)message initiatedByFrame:(WKFrameInfo *)frame completionHandler:(void (^)(BOOL result))completionHandler
{
    //bug: 参考https://www.jianshu.com/p/da44e10db6b0
    if(self.navigationController.visibleViewController != self) {
        if(completionHandler) {
            completionHandler(NO);
        }

        return;
    }

    UIAlertController *alertController = [UIAlertController alertControllerWithTitle:NSLocalizedString(@"alert.tips", nil) message:message?:@"" preferredStyle:UIAlertControllerStyleAlert];
    [alertController addAction:([UIAlertAction actionWithTitle:NSLocalizedString(@"alert.cancel", nil) style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
        if(completionHandler) {
            completionHandler(NO);
        }
    }])];
    [alertController addAction:([UIAlertAction actionWithTitle:NSLocalizedString(@"alert.confirm", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        if(completionHandler) {
            completionHandler(YES);
        }
    }])];

    if ([self.navigationController visibleViewController] == self)    {
//        [self presentViewController:alertController animated:YES completion:nil];
        //v2.6.8, 统一present方法，防止崩溃
        [alertController presentSafelyFromViewController:self];
    } else {
        if(completionHandler) {
            completionHandler(NO);
        }

        return;
    }
}

// prompt
- (void)webView:(WKWebView *)webView runJavaScriptTextInputPanelWithPrompt:(NSString *)prompt defaultText:(NSString *)defaultText initiatedByFrame:(WKFrameInfo *)frame completionHandler:(void (^)(NSString * _Nullable))completionHandler {
    //参考
    //https://www.jianshu.com/p/5fc4c0c6fbdf
    //https://www.jianshu.com/p/b7400bbad563

    if(self.navigationController.visibleViewController != self) {
        if(completionHandler) {
            completionHandler(@"");
        }

        return;
    }
    
    UIAlertController *alertController = [UIAlertController alertControllerWithTitle:prompt message:@"" preferredStyle:UIAlertControllerStyleAlert];
    [alertController addTextFieldWithConfigurationHandler:^(UITextField * _Nonnull textField) {
        textField.text = defaultText;
    }];
    [alertController addAction:([UIAlertAction actionWithTitle:NSLocalizedString(@"alert.finish", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        completionHandler(alertController.textFields[0].text?:@"");
    }])];


//    [self presentViewController:alertController animated:YES completion:nil];
    //v2.6.8, 统一present方法，防止崩溃
    [alertController presentSafelyFromViewController:self];
}

//- (void)webView:(WKWebView *)webView requestMediaCapturePermissionForOrigin:(WKSecurityOrigin *)origin initiatedByFrame:(WKFrameInfo *)frame type:(WKMediaCaptureType)type decisionHandler:(void (^)(WKPermissionDecision))decisionHandler
//API_AVAILABLE(ios(15.0)){
//    //网页申请相机和麦克风权限
//    NSString *mediaType = (type == WKMediaCaptureTypeCamera) ? @"相机" : @"麦克风";
//    [self showPermissionAlertForPermission:mediaType completion:^(BOOL allowed) {
//        if (allowed) {
//            decisionHandler(WKPermissionDecisionGrant);
//        } else {
//            decisionHandler(WKPermissionDecisionDeny);
//        }
//    }];
//}
//
//- (void)showPermissionAlertForPermission:(NSString *)permission completion:(void (^)(BOOL allowed))completion 
//{
//    UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"权限设置"
//                                                                   message:[NSString stringWithFormat:@"是否允许访问%@?", permission]
//                                                            preferredStyle:UIAlertControllerStyleAlert];
//    
//    [alert addAction:[UIAlertAction actionWithTitle:@"询问" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
//        NSURL *settingsURL = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
//        if ([[UIApplication sharedApplication] canOpenURL:settingsURL]) {
//            [[UIApplication sharedApplication] openURL:settingsURL options:@{} completionHandler:nil];
//        }
//        completion(NO);
//    }]];
//    
//    [alert addAction:[UIAlertAction actionWithTitle:@"拒绝" style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
//        completion(NO);
//    }]];
//    
//    [alert addAction:[UIAlertAction actionWithTitle:@"允许" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
//        completion(YES);
//    }]];
//    
//    [self presentViewController:alert animated:YES completion:nil];
//}

- (void)webViewDidClose:(WKWebView *)webView
{
}

 - (void)webView:(WKWebView *)webView contextMenuConfigurationForElement:(WKContextMenuElementInfo *)elementInfo completionHandler:(void (^)(UIContextMenuConfiguration * _Nullable))completionHandler
{
    NSURL* URL = elementInfo.linkURL;
    if(!URL) {
        UIContextMenuConfiguration* config = [UIContextMenuConfiguration configurationWithIdentifier:nil previewProvider:nil actionProvider:nil];
        completionHandler(config);
        return;
    }

    //预览窗口
    UIContextMenuConfiguration* config = [UIContextMenuConfiguration configurationWithIdentifier:nil previewProvider:^UIViewController * _Nullable{
        BOOL enabledPreview = [[PreferenceManager shareInstance].items.enabledPreview boolValue];
        if(!enabledPreview) return nil;
        
        LinkPreviewController* vc = [[LinkPreviewController alloc]initWithURL:URL];
        return vc;
    } actionProvider:^UIMenu * _Nullable(NSArray<UIMenuElement *> * _Nonnull suggestedActions) {

        UIAction* openInNewTabAction = [UIAction actionWithTitle:NSLocalizedString(@"contextmenu.openInNewTab", nil) image:nil identifier:nil handler:^(__kindof UIAction * _Nonnull action) {
            [self handleOpenInNewTabAction:URL];
        }];

        UIAction* openAction = [UIAction actionWithTitle:NSLocalizedString(@"contextmenu.openDirectly", nil) image:nil identifier:nil handler:^(__kindof UIAction * _Nonnull action) {
            [self handleOpenAction:URL];
        }];
        
        UIAction* openInBackgroundAction = [UIAction actionWithTitle:NSLocalizedString(@"contextmenu.openInBackground", nil) image:nil identifier:nil handler:^(__kindof UIAction * _Nonnull action) {
            [self handleOpenInBackendTabAction:URL];
        }];
        
        UIAction* openInSafariAction = [UIAction actionWithTitle:NSLocalizedString(@"contextmenu.openInSafari", nil) image:nil identifier:nil handler:^(__kindof UIAction * _Nonnull action) {
            if([[UIApplication sharedApplication] canOpenURL:URL]) {
                [[UIApplication sharedApplication] openURL:URL options:@{} completionHandler:nil];
            }
        }];

        UIAction* copyAction = [UIAction actionWithTitle:NSLocalizedString(@"contextmenu.copyLink", nil) image:nil identifier:nil handler:^(__kindof UIAction * _Nonnull action) {
            [self handleCopyLinkContextMenu:URL];
        }];

        return [UIMenu menuWithTitle:@"" children:@[openInNewTabAction, openAction, openInBackgroundAction, openInSafariAction, copyAction]];
    }];

    completionHandler(config);
}

#pragma mark -- 通过URL Scheme打开
- (void)handleOpenInURLScheme:(NSURL *)URL
{
    if(!URL) return;
    
    Tab* selectedTab = self.tabManager.selectedTab;
    //在新标签页打开
    if(selectedTab) {
        //添加asyc的原因，url scheme从外部打开，需要一些缓冲时间，否则会截图失败
        Tab* tab = self.tabManager.selectedTab;
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.6 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [[ScreenshotHelper shareInstance] takeScreenshot:tab];
        });
    }
    
    if(selectedTab) {
        NSString* currentUrl = selectedTab.model.url;
        if([InternalURL isAboutHomeURL:currentUrl]) {
            //首页，直接打开
            [selectedTab loadRequest:[NSURLRequest requestWithURL:URL]];
            return;
        }
    }
    
    [self _openURL:URL];
}

#pragma mark -- 在新标签页打开
- (void)handleOpenInNewTabAction:(NSURL *)URL
{
    //在新标签页打开
    if(self.tabManager.selectedTab) {
        Tab* tab = self.tabManager.selectedTab;
        [[ScreenshotHelper shareInstance] takeScreenshot:tab];
    }

    [self _openURL:URL];
}

- (void)_openURL:(NSURL *)URL
{
    if(!URL || URL.absoluteString.length==0) return;
    
    //更新标签数量
    [BrowserUtils shareInstance].allTabsCount++;
    int allTabsCount = [BrowserUtils shareInstance].allTabsCount;

    [self.bottomToolbar updateTabCount:allTabsCount];
    [self.topToolbar updateTabCount:allTabsCount];

    //新开一页
    NSMutableArray* urlArray = [NSMutableArray new];
    NSString* homeUrl = [HomeHelper getCustomUrl];
    [urlArray addObject:homeUrl];
    [urlArray addObject:URL.absoluteString];
    
    TabModel* tabModel = [TabModel new];
    tabModel.tabId = [NSUUID UUID].UUIDString;
    tabModel.urlHistorySnapshot = [urlArray componentsJoinedByString:@","];
    tabModel.urlHistoryCurrentIndex = 0;
    
    [self.tabManager restoreTab:tabModel];
}

#pragma mark -- 直接打开
- (void)handleOpenAction:(NSURL *)URL
{
    Tab* tab = self.tabManager.selectedTab;
    [tab loadRequest:[NSURLRequest requestWithURL:URL]];
}

#pragma mark -- 后台打开
- (void)handleOpenInBackendTabAction:(NSURL *)URL
{        
    //更新标签数量
    [BrowserUtils shareInstance].allTabsCount++;
    int allTabsCount = [BrowserUtils shareInstance].allTabsCount;

    [self.bottomToolbar updateTabCount:allTabsCount];
    [self.topToolbar updateTabCount:allTabsCount];
        
    //新开一页
    NSMutableArray* urlArray = [NSMutableArray new];
    NSString* homeUrl = [HomeHelper getCustomUrl];
    [urlArray addObject:homeUrl];
    [urlArray addObject:URL.absoluteString];
    
    TabModel* tabModel = [TabModel new];
    tabModel.tabId = [NSUUID UUID].UUIDString;
    tabModel.urlHistorySnapshot = [urlArray componentsJoinedByString:@","];
    tabModel.urlHistoryCurrentIndex = 0;
    
    [self.tabManager restoreTabWithoutSelect:tabModel];
}

#pragma mark -- 复制链接
- (void)handleCopyLinkContextMenu:(NSURL *)URL
{
    //复制到剪贴板
    if (URL && [URL absoluteString].length > 0) {
        UIPasteboard *pasteboard = [UIPasteboard generalPasteboard];
        pasteboard.string = [URL absoluteString];

        [UIView showSucceed:NSLocalizedString(@"tips.pasteboard.success", nil)];
    }
}

- (void)webView:(WKWebView *)webView contextMenuForElement:(WKContextMenuElementInfo *)elementInfo willCommitWithAnimator:(id<UIContextMenuInteractionCommitAnimating>)animator
{
    NSURL* url = elementInfo.linkURL;
    if(!url) return;

    [webView loadRequest:[NSURLRequest requestWithURL:url]];
}

@end
