//
//  BrowserViewController+TabManagerDelegate.h
//  PandaBrowser
//
//  Created by qingbin on 2022/3/4.
//

#import "BrowserViewController.h"
#import "Tab.h"
#import "TabManager.h"

#import "BrowserViewController+TabDelegate.h"
#import "BrowserViewController+WKNavigationDelegate.h"

@interface BrowserViewController (TabManagerDelegate)<TabManagerDelegate>

- (void)tabManager:(TabManager*)tabManager didSelectedTabChange:(Tab*)selected previous:(Tab*)previous;
- (void)tabManager:(TabManager*)tabManager didAddTab:(Tab*)tab;

@end



