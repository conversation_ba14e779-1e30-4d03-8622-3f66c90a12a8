//
//  BrowserUtils.m
//  PPBrowser
//
//  Created by qingbin on 2022/9/26.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "BrowserUtils.h"
#import "PPNotifications.h"

@interface BrowserUtils ()

@property (nonatomic, assign) UIEdgeInsets safeArea;

@end

@implementation BrowserUtils

+ (BOOL)isiPhone
{
    return [UIDevice currentDevice].userInterfaceIdiom == UIUserInterfaceIdiomPhone;
}

+ (BOOL)isiPad
{
    return [UIDevice currentDevice].userInterfaceIdiom == UIUserInterfaceIdiomPad;
}

+ (instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    static BrowserUtils* obj;
    dispatch_once(&onceToken, ^{
        obj = [BrowserUtils new];
    });
    
    return obj;
}

- (CGSize)transitionToSize
{
    if(CGSizeEqualToSize(_transitionToSize, CGSizeZero)) {
        // 主要是为了适配iPhone横屏启动, 导致宽高错乱的问题
        _transitionToSize = CGSizeMake(kScreenWidth, kScreenHeight);
    }
    
    return _transitionToSize;
}

+ (CGFloat)valueForiPad:(CGFloat)ipad
               standard:(CGFloat)standard
{
    NSNumber *result = [self _objectForiPad:@(ipad) standard:@(standard)];
    return result.floatValue;
}

+ (CGSize)sizeForiPad:(CGSize)ipad
             standard:(CGSize)standard
{
    NSNumber *result = [self _objectForiPad:@(ipad) standard:@(standard)];
    return result.CGSizeValue;
}

+ (NSString*)stringForiPad:(NSString*)ipad
                  standard:(NSString*)standard
{
    NSString *result = [self _objectForiPad:ipad standard:standard];
    return result;
}

+ (id)_objectForiPad:(id)ipad standard:(id)standard
{
    if ([self isiPad]) {
        return ipad;
    } else {
        return standard;
    }
}

+ (LocalizableOption)localizableOption
{
    LocalizableOption option = [NSLocalizedString(@"opensearch.value", nil) intValue];
    return option;
}

#pragma mark -- iPhone横竖屏适配
- (void)updateWithSafeArea:(UIEdgeInsets)safeArea
{
    self.safeArea = safeArea;
        
    //只有iPhone才监听该通知
    if(![BrowserUtils isiPhone]) return;
    [[NSNotificationCenter defaultCenter] postNotificationName:kViewSafeAreaInsetsDidChangeNotification object:nil];
}

- (UIEdgeInsets)safeArea
{
    return _safeArea;
}

- (BOOL)isLandscape
{
    return _transitionToSize.width > _transitionToSize.height;
}



@end
