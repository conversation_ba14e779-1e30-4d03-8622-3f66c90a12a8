//
//  BrowserUtils.h
//  PPBrowser
//
//  Created by qing<PERSON> on 2022/9/26.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

#import "MaizyHeader.h"
#import "PPEnums.h"

#ifndef BrowserUtilsMacro_h
#define BrowserUtilsMacro_h

#define iPadValue(i,s) \
[BrowserUtils valueForiPad:i standard:s]

#define iPadSize(i,s) \
[BrowserUtils sizeForiPad:i standard:s]

#define iPadString(i,s) \
[BrowserUtils stringForiPad:i standard:s]

#endif

@interface BrowserUtils : NSObject

+ (instancetype)shareInstance;

+ (BOOL)isiPhone;

+ (BOOL)isiPad;

+ (CGFloat)valueForiPad:(CGFloat)ipad
               standard:(CGFloat)standard;

+ (CGSize)sizeForiPad:(CGSize)ipad
             standard:(CGSize)standard;

+ (NSString*)stringForiPad:(NSString*)ipad
                  standard:(NSString*)standard;

+ (LocalizableOption)localizableOption;

//将要旋转到的屏幕大小size
@property (nonatomic, assign) CGSize transitionToSize;

//标签数量
@property (nonatomic, assign) int allTabsCount;

//iPhone横竖屏适配
- (void)updateWithSafeArea:(UIEdgeInsets)safeArea;

- (UIEdgeInsets)safeArea;

- (BOOL)isLandscape;

@end

