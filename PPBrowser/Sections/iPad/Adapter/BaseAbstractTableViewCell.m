//
//  BaseAbstractTableViewCell.m
//  PPBrowser
//
//  Created by qingbin on 2022/9/26.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "BaseAbstractTableViewCell.h"

@implementation BaseAbstractTableViewCell

- (id)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        [self addSubviews];
        [self defineLayout];
    }
    return self;
}

#pragma mark - subviews
- (void)addSubviews
{
    
}

#pragma mark - layout
- (void)defineLayout
{
    if ([BrowserUtils isiPad]) {
        [self adapterDefineLayout];
    } else {
        [self standardDefineLayout];
    }
}

- (void)adapterDefineLayout
{
    
}

- (void)standardDefineLayout
{
    
}

@end
