//
//  BaseAbstractView.m
//  PPBrowser
//
//  Created by qingbin on 2022/9/26.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "BaseAbstractView.h"

@interface BaseAbstractView ()

@end

@implementation BaseAbstractView

- (id)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        [self addSubviews];
        [self defineLayout];
    }
    return self;
}

#pragma mark - subviews
- (void)addSubviews
{
    
}

#pragma mark - layout
- (void)defineLayout
{
    if ([BrowserUtils isiPad]) {
        [self adapterDefineLayout];
    } else {
        [self standardDefineLayout];
    }
}

- (void)adapterDefineLayout
{
    
}

- (void)standardDefineLayout
{
    
}

@end
