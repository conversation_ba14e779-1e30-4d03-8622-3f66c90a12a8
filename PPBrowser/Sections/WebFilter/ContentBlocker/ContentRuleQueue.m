//
//  ContentRuleQueue.m
//  PPBrowser
//
//  Created by qingbin on 2022/8/18.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "ContentRuleQueue.h"

#import "ReactiveCocoa.h"

#import "ContentBlockItem.h"
#import "PPEnums.h"

@interface ContentRuleQueue ()

@property (nonatomic, strong) NSCondition * condition;

@property (nonatomic, strong) NSArray *rulelists;

@property (nonatomic, assign) BOOL destoryToken;

@end

@implementation ContentRuleQueue

+ (instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    static ContentRuleQueue* obj;
    dispatch_once(&onceToken, ^{
        obj = [[ContentRuleQueue alloc]init];
    });

    return obj;
}

- (instancetype)init
{
    self = [super init];
    if(self) {
        self.condition = [[NSCondition alloc]init];
    }
    
    return self;
}

- (void)putRules:(NSArray *)rulelists
{
    if(!rulelists) return;
    [self.condition lock];
    
    if(self.destoryToken) {
        [self.condition unlock];
        return;
    }
    
    self.rulelists = rulelists;
    
    [self.condition broadcast];
    [self.condition unlock];
}

- (NSArray *)getRulesSync
{
    [self.condition lock];
    while (!self.rulelists) {
        if(self.destoryToken) {
            [self.condition unlock];
            return nil;
        }
        
        [self.condition wait];
    }
    
    [self.condition unlock];
    
    return self.rulelists;
}

- (NSArray *)getRulesAsync
{
    [self.condition lock];
    if(self.destoryToken || !self.rulelists) {
        [self.condition unlock];
        return nil;
    }
    
    [self.condition unlock];
    
    return self.rulelists;
}

- (void)flush
{
    [self.condition lock];
    self.rulelists = nil;
    [self.condition unlock];
}

- (void)destroy
{
    [self.condition lock];
    self.rulelists = nil;
    self.destoryToken = YES;
    [self.condition broadcast];
    [self.condition unlock];
}

- (void)dealloc
{
#if DEBUG
    NSLog(@"%@ release...",[self class]);
#endif
}

@end
