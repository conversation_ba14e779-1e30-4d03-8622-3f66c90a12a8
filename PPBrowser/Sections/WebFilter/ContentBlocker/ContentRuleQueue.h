//
//  ContentRuleQueue.h
//  PPBrowser
//
//  Created by qingbin on 2022/8/18.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <WebKit/WebKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface ContentRuleQueue : NSObject

+ (instancetype)new NS_UNAVAILABLE;
- (instancetype)init NS_UNAVAILABLE;

+ (instancetype)shareInstance;

- (void)putRules:(NSArray *)rulelists;

- (NSArray *)getRulesSync;

- (NSArray *)getRulesAsync;

- (void)flush;

- (void)destroy;

@end

NS_ASSUME_NONNULL_END
