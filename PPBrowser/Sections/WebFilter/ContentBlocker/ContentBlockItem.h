//
//  ContentBlockItem.h
//  PPBrowser
//
//  Created by qingbin on 2022/3/20.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <WebKit/WebKit.h>
#import "PPEnums.h"

@interface ContentBlockItem : NSObject

@property(nonatomic,strong) NSString* fileName;
@property(nonatomic,strong) NSString* jsonContent;

@property(nonatomic,assign) ContentBlockItemType type;

- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)new NS_UNAVAILABLE;

- (instancetype)initWithItemType:(ContentBlockItemType)type fileName:(NSString*)fileName;

@end

