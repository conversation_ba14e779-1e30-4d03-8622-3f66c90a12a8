//
//  ContentBlockerHelper.m
//  PPBrowser
//
//  Created by qingbin on 2022/3/20.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "ContentBlockerHelper.h"

#import "ReactiveCocoa.h"
#import "PreferenceManager.h"

#import "ContentBlockItem.h"
#import "PPEnums.h"
#import "ContentRuleQueue.h"

@interface ContentBlockerHelper ()

@property (nonatomic, strong) WKContentRuleList *blockImagesRule;

@property (nonatomic, strong) WKContentRuleListStore* listStore;

@end

@implementation ContentBlockerHelper

+ (instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    static ContentBlockerHelper* obj;
    dispatch_once(&onceToken, ^{
        obj = [[ContentBlockerHelper alloc]init];
    });

    return obj;
}

- (instancetype)init
{
    self = [super init];
    if(self) {
        self.listStore = [WKContentRuleListStore defaultStore];
    }
    
    return self;
}

- (void)_getBlockListsWithEnabledLists:(NSArray*)enableList callback:(ContentBlockerHandler)callback
{
    NSMutableArray* returnList = [NSMutableArray array];
    
//    WKContentRuleListStore* listStore = [WKContentRuleListStore defaultStore];
    dispatch_group_t group = dispatch_group_create();
    dispatch_queue_t queue = dispatch_queue_create("com.concurrent.ContentBlockerHelper", DISPATCH_QUEUE_CONCURRENT);
    
    for(ContentBlockItem* item in enableList) {
        dispatch_group_enter(group);
        [self.listStore lookUpContentRuleListForIdentifier:item.fileName completionHandler:^(WKContentRuleList *ruleList, NSError *error) {            
            if(ruleList) {
                dispatch_async(queue, ^{
                    [returnList addObject:ruleList];
                    dispatch_group_leave(group);
                });
            } else {
                dispatch_async(queue, ^{
                    [self compileItem:item callback:^(WKContentRuleList *ruleList) {
                        if(ruleList) {
                            [returnList addObject:ruleList];
                        }
                        dispatch_group_leave(group);
                    }];
                });
            }
        }];
    }
    
    dispatch_group_notify(group, queue, ^{
        if(callback) {
            callback(returnList);
        }
    });
}

- (void)getBlockImagesRule:(ContentBlockerHandler)callback
{
    if(self.blockImagesRule) {
        callback(@[self.blockImagesRule]);
        return;
    }
    
    ContentBlockItem* item = [[ContentBlockItem alloc]initWithItemType:ContentBlockItemTypeImage fileName:@"block-images"];
    NSArray* enableList = @[item];
    [self _getBlockListsWithEnabledLists:enableList callback:^(NSArray<WKContentRuleList *> * _Nonnull rules) {
        self.blockImagesRule = rules.firstObject;
        if(callback) {
            callback(rules);
        }
    }];
}

- (void)getBlockLists:(ContentBlockerHandler)callback
{
    NSArray* enableList = [self getEnabledLists];
    [self _getBlockListsWithEnabledLists:enableList callback:callback];
}

- (void)compileBlockLists
{    
    dispatch_queue_t queue = dispatch_queue_create("com.serial.compileBlockLists", DISPATCH_QUEUE_CONCURRENT);
    dispatch_async(queue, ^{
        NSArray* enableList = [self getEnabledLists];

        [self _getBlockListsWithEnabledLists:enableList callback:^(NSArray<WKContentRuleList *> * _Nonnull rules) {
            [[ContentRuleQueue shareInstance] putRules:rules];
        }];
    });
}

- (void)compileItem:(ContentBlockItem*)item callback:(void(^)(WKContentRuleList*))callback
{
    NSString* path = [[NSBundle mainBundle] pathForResource:item.fileName ofType:@"json"];
    NSString* source = [NSString stringWithContentsOfFile:path encoding:NSUTF8StringEncoding error:nil];
    item.jsonContent = source;

    WKContentRuleListStore* listStore = [WKContentRuleListStore defaultStore];
    [listStore compileContentRuleListForIdentifier:item.fileName encodedContentRuleList:source completionHandler:^(WKContentRuleList *rule, NSError *error) {
        if(callback) {
            callback(rule);
        }
    }];
}

- (NSArray*)getEnabledLists
{
    NSMutableArray* enableList = [NSMutableArray array];
        
    //屏蔽广告
//    BOOL enabledAdblock = [[PreferenceManager shareInstance].items.enabledAdblock boolValue];
//    if(!enabledAdblock) return enableList;
        
    //广告
    ContentBlockItem* item = [[ContentBlockItem alloc]initWithItemType:ContentBlockItemTypeAd fileName:NSLocalizedString(@"block.ads.fileUrl", nil)];
    [enableList addObject:item];
    
    //trackers
//    item = [[ContentBlockItem alloc]initWithItemType:ContentBlockItemTypeTrackers fileName:@"block-trackers"];
//    [enableList addObject:item];
    
    return enableList;
}

@end

