//
//  ContentBlockItem.m
//  PPBrowser
//
//  Created by qingbin on 2022/3/20.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "ContentBlockItem.h"

@implementation ContentBlockItem

- (instancetype)initWithItemType:(ContentBlockItemType)type fileName:(NSString*)fileName
{
    self = [super init];
    if(self) {
        self.fileName = fileName;
        self.type = type;
    }

    return self;
}

- (void)loadJsonFromBundle
{
    
}

@end

/*
 版权问题
 @@||sm.cn
 @@||t66y.com
 @@||iqiyi.com
 @@||mgtv.com
 @@||youku.com
 @@||v.qq.com
 @@||sogou.com
 @@||so.com
 @@||toutiao.com
 */

/*
 //还原百度首页广告的方法
 1) 找到easylistChina的下面两个, 然后删除其及其附近几条对应的和百度相关的规则(因为屏蔽那条广告的规则, 基本上都是同一时间写的思路)
 baidu.com##.ec_wise_ad
 baidu.com#?#.ec_wise_ad
 
 2) //还原"已了解安全风险,查看更多"，否则“下一页”就不见了
 //删除了整个!#BaiduPromotion
 ipv6.baidu.com,m.baidu.com,www.baidu.com,www1.baidu.com,xueshu.baidu.com###results > div[class]:not(.result):not(.result-op):not(.sp-rslt-bar):not(.s-group-result)
 
 3) 
 */


/*
 搜狗搜索
 sogou.com##.ad_result
 */

/*
 youtube广告屏蔽

 
 */
