//
//  ContentBlockerHelper.h
//  PPBrowser
//
//  Created by qingbin on 2022/3/20.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <WebKit/WebKit.h>

NS_ASSUME_NONNULL_BEGIN

typedef void (^ContentBlockerHandler)(NSArray<WKContentRuleList*>* rules);

@interface ContentBlockerHelper : NSObject

+ (instancetype)shareInstance;

//提前编译通用规则，加快首页显示时间
- (void)compileBlockLists;

// 获取通用block list
- (void)getBlockLists:(ContentBlockerHandler)callback;

// 获取no image list
- (void)getBlockImagesRule:(ContentBlockerHandler)callback;

@end

NS_ASSUME_NONNULL_END


/*
 AdGuard Filters:
 https://github.com/AdguardTeam/AdguardFilters

 https://easylist-downloads.adblockplus.org/easylistchina+easylistchina_compliance+easylist.txt
 
 目前采用的规则是:
 中国版:
 https://easylist-downloads.adblockplus.org/easylistchina.txt
 英语版:
 https://easylist.to/easylist/easylist.txt
 
 过滤规则:
 1) https://wsgzao.github.io/post/adguard/
 2) https://github.com/cjx82630/cjxlist
 3) AdGurad-iOS中的filters_test.json
 
 //记录
 1) 屏蔽了百度首页广告,不好:
 https://easylist-downloads.adblockplus.org/easylistchina.txt
 https://filters.adtidy.org/ios/filters/224_optimized.txt
 https://easylist-downloads.adblockplus.org/easylistchina+easylistchina_compliance+easylist.txt
 
 YouTube规则:
 https://easylist-downloads.adblockplus.org/easylist.txt
 
 记录BUG:
 if-domain和unless-domain不能同时出现在同一条规则中
 */

//https://dss0.bdstatic.com/-0U0bnSm1A5BphGlnYG/tam-ogel/823d56945b733cca92eb56090c0ffb77_1242_1249.jpg
//||bdstatic.com^*/tam-ogel/
//@@||bdstatic.com/??*,*,*,
