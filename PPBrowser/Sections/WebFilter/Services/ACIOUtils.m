//
//  ACIOUtils.m
//  PPBrowser
//
//  Created by qingbin on 2022/5/8.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "ACIOUtils.h"

@implementation ACIOUtils

+ (NSString *)readLine:(NSInputStream*)stream encoding:(NSStringEncoding)encoding
{
    @autoreleasepool {
        NSData *lineBytes = [self readLineBytes:stream];
        if (lineBytes.length == 0) {
            return nil;
        }
        return [ACIOUtils toStringRemovingTrailingCrLf:lineBytes encoding:encoding];
    }
}

+ (NSData *)readLineBytes:(NSInputStream*)stream
{
    @autoreleasepool {
        NSUInteger length = 0;
        NSMutableData *buffer = [NSMutableData dataWithLength:128];

        Byte byteRead;
        while ([stream read:&byteRead maxLength:1] > 0) {
            if (buffer.length <= length) {
                [buffer setLength:buffer.length + 128];
            }

            switch (byteRead) {
            case 10:
                ((Byte *)[buffer mutableBytes])[length] = byteRead;
                ++length;
                return [buffer subdataWithRange:NSMakeRange(0, length)];
                break;
            default:
                ((Byte *)[buffer mutableBytes])[length] = byteRead;
                ++length;
                break;
            }
        }

        return [buffer subdataWithRange:NSMakeRange(0, length)];
    }
}

+ (NSString *)toStringRemovingTrailingCrLf:(NSData *)data encoding:(NSStringEncoding)encoding
{
    NSString *string = [[NSString alloc] initWithData:data encoding:encoding];
    if (string == nil && data.length > 0) {
        // something wrong with encoding, trying ISOLatin1
        string =[[NSString alloc] initWithData:data encoding:NSISOLatin1StringEncoding];
    }
    return [string stringByTrimmingCharactersInSet:[NSCharacterSet newlineCharacterSet]];
}

@end
