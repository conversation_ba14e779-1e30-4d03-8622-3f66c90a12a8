//
//  AESFilterConverter.h
//  PPBrowser
//
//  Created by qingbin on 2022/5/8.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "ASDFilter.h"
#import "ASDFilterRule.h"

#import "BaseModel.h"

//NSString *AESFTotalConvertedCountKey = @"totalConvertedCount";
//NSString *AESFConvertedCountKey = @"convertedCount";
//NSString *AESFConvertedRulesKey = @"converted";
//NSString *AESErrorsCountKey = @"errorsCount";
//NSString *AESFCOverLimitKey = @"overLimit";
//NSString *AESFConvertedErrorKey = @"AESFConvertedErrorKey";

@interface AESConverterResult : BaseModel

@property (nonatomic, assign) NSInteger totalConvertedCount;
@property (nonatomic, assign) NSInteger convertedCount;
@property (nonatomic, strong) NSString *converted;
@property (nonatomic, assign) NSInteger errorsCount;
@property (nonatomic, assign) BOOL overLimit;

@end

/**
Converter from Adguard filter rules
to Apple content-blocking extension rules format.
*/
@interface AESFilterConverter : NSObject

+ (instancetype)shareInstance;

/**
 Converts array of the filter rules to JSON string.
 
 @param rules       Array of ASDFilterRule objects, each object represents filter rule.
 @param limit       Maximum count of the rules, which may be converted.
 @param optimize    If true - "wide" rules will be ignored
 
 @return Returns dictionary with results or nil if error occured.
 Dictionary contains keys: AESFConvertedCountKey, AESFConvertedRulesKey, AESFCOoverLimitKey
 */
- (NSDictionary *)jsonFromRules:(NSArray<ASDFilterRule*> *)rules upTo:(NSUInteger)limit optimize:(BOOL)optimize;

@end

