//
//  ASDFilter.h
//  PPBrowser
//
//  Created by qing<PERSON> on 2022/5/8.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "BaseModel.h"
#import "ASDFilterRule.h"

NS_ASSUME_NONNULL_BEGIN

@interface ASDFilter : BaseModel

@property (nonatomic, strong) NSNumber *filterId;
@property (nonatomic, strong) NSString *name;
@property (nonatomic, strong) NSString *version;
@property (nonatomic, strong) NSString *updateDateString;
@property (nonatomic, strong) NSDate *updateDate;

/// Array of ASDFilterRule objects.
@property (nonatomic, strong) NSMutableArray<ASDFilterRule> *rules;

@end

NS_ASSUME_NONNULL_END
