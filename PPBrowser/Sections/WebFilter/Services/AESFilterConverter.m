//
//  AESFilterConverter.m
//  PPBrowser
//
//  Created by qingbin on 2022/5/8.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "AESFilterConverter.h"
#include <JavaScriptCore/JavaScript.h>
#include <JavaScriptCore/JSContext.h>
#include <JavaScriptCore/JavaScriptCore.h>

#import "ASDFilterRule.h"
#import "ResourceHelper.h"
#import "CopyrightHelper.h"

NSString *AESFTotalConvertedCountKey = @"totalConvertedCount";
NSString *AESFConvertedCountKey = @"convertedCount";
NSString *AESFConvertedRulesKey = @"converted";
NSString *AESErrorsCountKey = @"errorsCount";
NSString *AESFCOverLimitKey = @"overLimit";
NSString *AESFConvertedErrorKey = @"AESFConvertedErrorKey";

@implementation AESConverterResult
@end

@interface AESFilterConverter () {
    JSContext *_context;
    JSValue *_converterFunc;
}

@end

@implementation AESFilterConverter

+ (instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    static AESFilterConverter* obj;
    dispatch_once(&onceToken, ^{
        obj = [[AESFilterConverter alloc]init];
    });

    return obj;
}

- (instancetype)init
{
    self = [super init];
    if(self) {
        [self _setupJsContext];
    }
    
    return self;
}

- (void)_setupJsContext
{
    _context = [[JSContext alloc]init];
    
    //目前还没用到在线转换广告规则的功能
    //因此就不再把JSConverter加入到bundle里了,也不进行加密
    NSURL* jsURL = [[NSBundle mainBundle] URLForResource:@"JSConverter" withExtension:@"js"];
    NSString* script = [NSString stringWithContentsOfURL:jsURL encoding:NSUTF8StringEncoding error:NULL];
//    NSString* script = [EncryptionHandler shareInstance].jsConverter;
    
    [_context evaluateScript:@"var console = {}"];
    
    _context[@"console"][@"log"] = ^(NSString *message) {
        NSLog(@"Javascript: %@",message);
    };
    _context[@"console"][@"warn"] = ^(NSString *message) {
        NSLog(@"Javascript Warn: %@",message);
    };
    _context[@"console"][@"info"] = ^(NSString *message) {
        NSLog(@"Javascript Info: %@",message);
    };
    _context[@"console"][@"error"] = ^(NSString *message) {
        NSLog(@"Javascript Error: %@",message);
    };

    _context[@"window"] = _context.globalObject;
    
    [_context evaluateScript:script];
    _converterFunc = _context[@"jsonFromFilters"];
    
    if (!_converterFunc || [_converterFunc isUndefined]) {
        NSLog(@"failed to init jsonFromFilters...");
    }
}


- (AESConverterResult *)jsonFromRules:(NSArray<ASDFilterRule*> *)rules
                                 upTo:(NSUInteger)limit
                             optimize:(BOOL)optimize
{
    if (!(rules.count && limit)) {
        return nil;
    }

    @autoreleasepool {
        NSMutableArray *_rules = [NSMutableArray arrayWithCapacity:rules.count];
        NSMutableSet *ruleTextSet = [NSMutableSet set];
        for (ASDFilterRule *rule in rules) {
            //版权控制检测
            if(![[CopyrightHelper shareInstance] validContentRuleText:rule.ruleText]) {
                continue;
            }
            
            // This should delete duplicates.
            if (![ruleTextSet containsObject:rule.ruleText]) {
                [ruleTextSet addObject:rule.ruleText];
                //--------------
                [_rules addObject:rule.ruleText];
            }
        }

        JSValue *result = [_converterFunc callWithArguments:@[_rules, @(limit), @(optimize)]];

        NSDictionary *dictResult = [result toDictionary];
        AESConverterResult* model = [[AESConverterResult alloc]initWithDictionary:dictResult error:nil];
        
        ///Users/<USER>/Desktop/
        ///Users/<USER>/Desktop
//        NSString *writePath = [NSHomeDirectory() stringByAppendingPathComponent:@"tmp/block-ads.json"];
        NSString* writePath = [NSString stringWithFormat:@"/Users/<USER>/Desktop/block-ads.json"];
        NSError *error;
        [model.converted writeToFile:writePath atomically:YES encoding:NSUTF8StringEncoding error:&error];
        if (error) {
            NSLog(@"导出失败");
        }else {
            NSLog(@"导出成功 : %@",writePath);
        }

        return model;
    }
}

@end
