//
//  AECFilterParser.m
//  PPBrowser
//
//  Created by qingbin on 2022/5/8.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "AECFilterParser.h"
#import "ASDFilter.h"
#import "ACIOUtils.h"

@implementation AECFilterParser

+ (ASDFilter*)parseWithData:(NSData *)data
{
    NSMutableArray *rules = [NSMutableArray array];
    NSUInteger ruleCounter = 0;
    NSString *line;
    
    NSInputStream *stream = [NSInputStream inputStreamWithData:data];
    [stream open];
    while ((line = [ACIOUtils readLine:stream
                              encoding:NSUTF8StringEncoding]))
    {
           ASDFilterRule *rule = [ASDFilterRule new];
//           rule.filterId = filter.filterId;
           rule.ruleId = @(++ruleCounter);
           rule.ruleText = line;
           rule.isEnabled = @(1);
           [rules addObject:rule];
    }
    
    [stream close];
    
    ASDFilter* filter = [ASDFilter new];
    [filter.rules addObjectsFromArray:rules];
    return filter;
}

@end
