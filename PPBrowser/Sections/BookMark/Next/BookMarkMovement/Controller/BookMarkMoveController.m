//
//  BookMarkMoveController.m
//  PPBrowser
//
//  Created by qingbin on 2024/2/26.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import "BookMarkMoveController.h"

#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "UIView+FrameHelper.h"
#import "NSObject+Helper.h"

#import "ThemeProtocol.h"
#import "PreferenceManager.h"
#import "BrowserUtils.h"

#import "DatabaseUnit+BookMark.h"

#import "UITableView+HintMessage.h"
#import "PPEnums.h"
#import "PPNotifications.h"

#import "BookMarkMoveCell.h"

@interface BookMarkMoveController ()<UITableViewDelegate,UITableViewDataSource>

@property (nonatomic, strong) UITableView* tableView;

@property (nonatomic, strong) NSMutableArray* model;

@property (nonatomic, strong) UIButton* rightButton;

@end

@implementation BookMarkMoveController

- (void)viewDidLoad
{
    [super viewDidLoad];

    [self addSubviews];
    [self defineLayout];
    
    [self setupObservers];
    [self createCustomLeftBarButtonItem];
    [self _createCustomRightBarButtonItem];
    
    [self reloadData];
    
    [self applyTheme];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
    } else {
        self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
    
    [self.tableView reloadData];
}

#pragma mark -- 导航栏
- (void)_createCustomRightBarButtonItem
{
    UIButton* rightButton = [[UIButton alloc] initWithFrame:CGRectMake(0.0, 0.0f, 44.0f, 44.0)];
    [rightButton setBackgroundColor:[UIColor clearColor]];

    [rightButton setTitle:NSLocalizedString(@"alert.confirm", nil) forState:UIControlStateNormal];
    [rightButton setTitleColor:[UIColor colorWithHexString:@"#2D7AFE"] forState:UIControlStateNormal];
    rightButton.titleLabel.font = [UIFont systemFontOfSize:16];
    
    [rightButton addTarget:self action:@selector(rightBarbuttonClick) forControlEvents:UIControlEventTouchUpInside];
    UIBarButtonItem* barItem = [[UIBarButtonItem alloc] initWithCustomView:rightButton];

    UIBarButtonItem *spacer = [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemFixedSpace target:nil action:nil];
    spacer.width = -16.0f; // for example shift right bar button to the right

    self.navigationItem.rightBarButtonItems = @[spacer, barItem];
    
    self.rightButton = rightButton;
}

- (void)rightBarbuttonClick
{
    //保存
    for(BookMarkModel* obj in self.model) {
        if(obj.isSelected) {
            if(self.finishBlock) {
                self.finishBlock(obj);
            }
            
            break;
        }
    }
    
    [self dismissViewControllerAnimated:YES completion:nil];
}

- (void)reloadData
{
    DatabaseUnit* unit = [DatabaseUnit queryBookMarkMoveNodes];
    @weakify(self)
    [unit setCompleteBlock:^(BookMarkModel* result, BOOL success) {
        @strongify(self)
        if(!self) return;
        if(success) {
            [self _handleModelTreeWithModel:result];
        }
    }];
    
    DB_EXEC(unit);
}

- (void)_handleModelTreeWithModel:(BookMarkModel *)model
{
    NSMutableArray* array = [NSMutableArray array];
    //根节点
    model.level = 0;
    [array addObject:model];
    
    NSArray* items = [self _treeWithModel:model level:0];
    [array addObjectsFromArray:items];
    
    [self.model removeAllObjects];
    self.model = array;
    
    BookMarkModel* firstObject = self.items.firstObject;
    for(BookMarkModel* obj in self.model) {
        if([obj.bookmarkId isEqualToString:firstObject.parentId]) {
            obj.isSelected = YES;
        } else {
            obj.isSelected = NO;
        }
    }
    
    [self.tableView reloadData];
}

- (NSArray *)_treeWithModel:(BookMarkModel *)model level:(int)level
{
    model.level = level + 1;
    
    NSMutableArray* array = [NSMutableArray array];
    for(BookMarkModel* item in model.children) {
        //当前要移动的文件夹及其子列表不出现在移动列表中
        if([self _isInItemsWithId:item.bookmarkId]) continue;
        
        [array addObject:item];
        
        NSArray* items = [self _treeWithModel:item level:model.level];
        if(items.count > 0) {
            [array addObjectsFromArray:items];
        }
    }
    
    return array;
}

- (BOOL)_isInItemsWithId:(NSString *)uuid
{
    for(BookMarkModel* obj in self.items) {
        if([uuid isEqualToString:obj.bookmarkId]) {
            return YES;
        }
    }
    
    return NO;
}

#pragma mark -- 无数据提醒
- (void)showOrHideHintMessage
{
    if(self.model.count > 0) {
        [self.tableView hideHintMessage];
    } else {
        UIImage* image = [UIImage imageNamed:@"empty_data_logo"];
        [self.tableView showHintMessage:NSLocalizedString(@"tableview.emptyTips", nil)
                                  image:image
                          sectionMargin:iPadValue(150, 100)];
    }
}


- (void)setupObservers
{
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(darkThemeDidChangeNotification:)
                                                 name:kDarkThemeDidChangeNotification
                                               object:nil];
}

- (void)dealloc
{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

#pragma mark -- layout
- (void)addSubviews
{
    [self.view addSubview:self.tableView];
}

- (void)defineLayout
{
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_offset(0);
    }];
}

#pragma mark - UITableViewDataSource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return self.model.count;
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    BookMarkModel* model = self.model[indexPath.row];

    BookMarkMoveCell* cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass(BookMarkMoveCell.class)];
    [cell updateWithModel:model];

    return cell;
}

#pragma mark - UITableViewDelegate
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    [tableView deselectRowAtIndexPath:indexPath animated:NO];
        
    BookMarkModel* model = self.model[indexPath.row];
    if(self.finishBlock) {
        self.finishBlock(model);
    }
    
    [self leftBarbuttonClick];
}

- (UITableView *)tableView
{
    if(!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
        
        _tableView.separatorStyle  = UITableViewCellSeparatorStyleNone;
        _tableView.showsHorizontalScrollIndicator = NO;
        _tableView.showsVerticalScrollIndicator   = YES;
        _tableView.delegate   = self;
        _tableView.dataSource = self;
        _tableView.rowHeight = iPadValue(88, 60);
        
        [_tableView registerClass:[BookMarkMoveCell class] forCellReuseIdentifier:NSStringFromClass([BookMarkMoveCell class])];
        
        UIWindow* window = [NSObject normalWindow];
        float height = iPadValue(30, 10);
        _tableView.tableFooterView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, CGFLOAT_MIN, height+window.safeAreaInsets.bottom)];
        _tableView.tableHeaderView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, CGFLOAT_MIN, height)];
        
        _tableView.backgroundColor = UIColor.clearColor;
    }
    
    return _tableView;
}

- (NSMutableArray *)model
{
    if(!_model) {
        _model = [NSMutableArray array];
    }
    
    return _model;
}



@end
