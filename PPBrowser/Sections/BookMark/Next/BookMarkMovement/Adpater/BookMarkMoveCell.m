//
//  BookMarkMoveCell.m
//  PPBrowser
//
//  Created by qingbin on 2024/2/26.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import "BookMarkMoveCell.h"

#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "UIView+FrameHelper.h"
#import "NSObject+Helper.h"

#import "ThemeProtocol.h"
#import "BrowserUtils.h"

#import "UIImage+Extension.h"

@interface BookMarkMoveCell ()

@property (nonatomic, strong) BookMarkModel* model;

@property (nonatomic, strong) UIImageView* logo;

@property (nonatomic, strong) UILabel* titleLabel;

@property (nonatomic, strong) UIImageView* selectedLogo;

@property (nonatomic, strong) UIView* line;

@end

@implementation BookMarkMoveCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        [self addSubviews];
        [self defineLayout];
        [self setupObservers];
        
        [self applyTheme];
    }
    
    return self;
}

#pragma mark -- 暗黑模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        self.titleLabel.textColor = [UIColor colorWithHexString:@"#ffffff"];
        self.line.backgroundColor = [UIColor colorWithHexString:kDarkThemeColorLine];
    } else {
        self.backgroundColor = [UIColor colorWithHexString:@"#ffffff"];
        self.titleLabel.textColor = [UIColor colorWithHexString:@"#333333"];
        self.line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
}

- (void)updateWithModel:(BookMarkModel *)model
{
    self.model = model;
    
    //文件夹
    self.logo.image = [UIImage imageNamed:@"server_folder_icon"];
    self.titleLabel.text = model.title;

    if(self.model.isSelected) {
        self.selectedLogo.hidden = NO;
    } else {
        self.selectedLogo.hidden = YES;
    }
    
    int level = model.level;
    [self.logo mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(iPadValue(40, 20)*level);
        make.centerY.mas_offset(0);
        make.size.mas_equalTo(iPadValue(45, 35));
    }];
    
    [self applyTheme];
}

- (void)setupObservers
{
    
}

#pragma mark -- layout
- (void)addSubviews
{
    [self.contentView addSubview:self.logo];
    [self.contentView addSubview:self.titleLabel];
    [self.contentView addSubview:self.selectedLogo];
    [self.contentView addSubview:self.line];
}

- (void)defineLayout
{
    [self.logo mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_offset(0);
        make.left.mas_offset(iPadValue(30, 15));
        make.size.mas_equalTo(iPadValue(45, 35));
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_offset(0);
        make.left.equalTo(self.logo.mas_right).offset(iPadValue(30, 15));
        make.right.lessThanOrEqualTo(self.selectedLogo).offset(-iPadValue(30, 15));
    }];
    
    [self.selectedLogo mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.right.mas_offset(-iPadValue(30, 15));
        make.size.mas_equalTo(iPadValue(35, 25));
    }];
    
    [self.logo setContentCompressionResistancePriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    [self.logo setContentHuggingPriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    
    [self.selectedLogo setContentCompressionResistancePriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    [self.selectedLogo setContentHuggingPriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    
    [self.titleLabel setContentCompressionResistancePriority:UILayoutPriorityRequired-1 forAxis:UILayoutConstraintAxisHorizontal];
    [self.titleLabel setContentHuggingPriority:UILayoutPriorityRequired-1 forAxis:UILayoutConstraintAxisHorizontal];
    
    [self.line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView);
        make.bottom.right.equalTo(self.contentView);
        make.height.mas_equalTo(0.5);
    }];
}

- (UIView *)line
{
    if(!_line) {
        _line = [UIView new];
        _line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
    
    return _line;
}

- (UILabel *)titleLabel
{
    if(!_titleLabel) {
        _titleLabel = [UIView createLabelWithTitle:@""
                                         textColor:[UIColor colorWithHexString:@"#333333"]
                                           bgColor:UIColor.clearColor
                                          fontSize:iPadValue(20, 15)
                                     textAlignment:NSTextAlignmentLeft
                                             bBold:NO];
        
        _titleLabel.lineBreakMode = NSLineBreakByTruncatingMiddle;
    }
    
    return _titleLabel;
}

- (UIImageView *)logo
{
    if(!_logo) {
        _logo = [UIImageView new];
        _logo.contentMode = UIViewContentModeScaleAspectFit;
    }
    
    return _logo;
}

- (UIImageView *)selectedLogo
{
    if(!_selectedLogo) {
        _selectedLogo = [UIImageView new];
        
        UIImage* image = [UIImage imageNamed:@"book_checkmark_icon"];
        image = [image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        
        _selectedLogo.image = image;
        _selectedLogo.tintColor = UIColor.systemBlueColor;
    }
    
    return _selectedLogo;
}

@end
