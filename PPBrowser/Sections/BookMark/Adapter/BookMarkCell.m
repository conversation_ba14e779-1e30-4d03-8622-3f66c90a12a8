//
//  BookMarkCell.m
//  PPBrowser
//
//  Created by qingbin on 2022/4/29.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "BookMarkCell.h"

#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "UIView+FrameHelper.h"
#import "NSObject+Helper.h"

#import "ThemeProtocol.h"
#import "PreferenceManager.h"
#import "BrowserUtils.h"
#import "NSURL+Extension.h"

@interface BookMarkCell ()<ThemeProtocol>

@property (nonatomic, strong) BookMarkModel* model;

@property (nonatomic, strong) UIView *tapView;
@property (nonatomic, strong) UIView* logoContainer;
@property (nonatomic, strong) UILabel* logoText;

@property (nonatomic, strong) UIStackView* stackView;
@property (nonatomic, strong) UILabel* titleLabel;
@property (nonatomic, strong) UILabel* urlLabel;
@property (nonatomic, strong) UILabel* itemCountLabel;
@property (nonatomic, strong) UIImageView *folderIcon;

@property (nonatomic, strong) UIButton* moreButton;
@property (nonatomic, strong) UIView* line;

//iOS15.0
@property (nonatomic, strong) UIImageView *selectLogo;

@end

@implementation BookMarkCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        self.backgroundColor = [UIColor colorWithHexString:@"#FFFFFF"];
        [self addSubviews];
        [self defineLayout];
        [self setupObservers];
        
        [self applyTheme];
    }
    
    return self;
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        self.titleLabel.textColor = UIColor.whiteColor;
        self.urlLabel.textColor = [UIColor colorWithHexString:@"#999999"];
        self.itemCountLabel.textColor = [UIColor colorWithHexString:@"#666666"];
        self.line.backgroundColor = [UIColor colorWithHexString:kDarkThemeColorLine];
        
        self.moreButton.tintColor = [UIColor colorWithHexString:@"#9CA3AF"];
    } else {
        self.backgroundColor = UIColor.whiteColor;
        self.titleLabel.textColor = [UIColor colorWithHexString:@"#333333"];
        self.urlLabel.textColor = [UIColor colorWithHexString:@"#999999"];
        self.itemCountLabel.textColor = [UIColor colorWithHexString:@"#999999"];
        self.line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        
        self.moreButton.tintColor = [UIColor colorWithHexString:@"#9CA3AF"];
    }
}

- (void)updateWithModel:(BookMarkModel*)model
         batchOperation:(BatchType)batchOperation
{
    self.model = model;
    
    if(model.title.length == 0) {
        model.title = NSLocalizedString(@"bookmark.noTitle", nil);
    }
    self.titleLabel.text = model.title;
    
    if(model.fileType == BookMarkTypeFile) {
        // 文件类型显示 URL
        self.logoContainer.layer.masksToBounds = YES;
        self.folderIcon.hidden = YES;
        
        NSURL* URL = [NSURL URLWithString:model.url];
        if (URL) {
            self.urlLabel.text = [URL normalizedHost];
        } else {
            self.urlLabel.text = model.url;
        }
        self.urlLabel.hidden = NO;
        self.itemCountLabel.hidden = YES;
        
        // 设置网站图标/首字母
        [self setupLogoForModel:model];
    } else {
        // 文件夹类型显示项目数
        self.itemCountLabel.text = [NSString stringWithFormat:@"%d %@", model.childrenCount, NSLocalizedString(@"bookmark.item", nil)];
        self.urlLabel.hidden = YES;
        self.itemCountLabel.hidden = NO;
        
        // 设置文件图标
        self.logoContainer.backgroundColor = UIColor.clearColor;
        self.logoContainer.layer.masksToBounds = NO;
        self.logoText.text = @"";
        self.folderIcon.hidden = NO;
    }
    
    if(model.isLastInSection) {
        self.line.hidden = YES;
    } else {
        self.line.hidden = NO;
    }

    if(batchOperation == BatchTypeDefault) {
        self.moreButton.hidden = NO;
        self.selectLogo.hidden = YES;
        self.tapView.userInteractionEnabled = YES;
    } else {
        self.moreButton.hidden = YES;
        self.selectLogo.hidden = NO;
        self.tapView.userInteractionEnabled = NO;
        
        if(!model.isSelected) {
            self.selectLogo.image = [UIImage imageNamed:@"standard_checkmark_UnSelected"];
        } else {
            self.selectLogo.image = [UIImage imageNamed:@"standard_checkmark_Selected"];
        }
    }
    
    [self applyTheme];
}

- (void)setupLogoForModel:(BookMarkModel *)model
{
    NSString *firstChar = @"";
    
    // 设置网站首字母
    if (model.title.length > 0) {
        firstChar = [model.title substringToIndex:1];
    }
    
    self.logoContainer.backgroundColor = [UIColor colorWithURL:model.url];
    self.logoText.text = firstChar;
}

#pragma mark - Handle events

- (void)setupObservers
{
    UITapGestureRecognizer* tap = [[UITapGestureRecognizer alloc]init];
    @weakify(self)
    [tap.rac_gestureSignal subscribeNext:^(id x) {
        @strongify(self)
        if(self.didTapAction) {
            self.didTapAction(self.model);
        }
    }];
    [self.contentView addGestureRecognizer:tap];
    
    tap = [[UITapGestureRecognizer alloc]init];
    [tap.rac_gestureSignal subscribeNext:^(id x) {
        @strongify(self)
        if(self.didMoreBtnTapAction) {
            self.didMoreBtnTapAction(self.model);
        }
    }];
    [self.tapView addGestureRecognizer:tap];
}

#pragma mark -- layout
- (void)addSubviews
{
    [self.contentView addSubview:self.logoContainer];
    [self.logoContainer addSubview:self.logoText];
    [self.logoContainer addSubview:self.folderIcon];
    
    [self.contentView addSubview:self.stackView];
    [self.contentView addSubview:self.moreButton];
    [self.contentView addSubview:self.selectLogo];
    [self.contentView addSubview:self.line];
    
    [self.contentView addSubview:self.tapView];
    
    //2.6.8 阿拉伯语布局适配
    [NSObject rtlLayoutSupportWithViews:@[self.stackView]];
}

- (void)defineLayout
{
    float offset = iPadValue(30, 15);
    float size = iPadValue(40, 32);
    [self.logoContainer mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(self.contentView);
        make.left.mas_equalTo(self.contentView).offset(offset);
        make.size.mas_equalTo(size);
    }];
    
    [self.logoText mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.mas_equalTo(self.logoContainer);
    }];
    
    [self.folderIcon mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.logoContainer);
        make.size.mas_equalTo(iPadValue(45, 35));
    }];
    
    //增大点击范围
    [self.tapView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.top.bottom.equalTo(self.contentView);
        make.left.equalTo(self.moreButton.mas_left).offset(-10);
    }];
    
    //决定高度
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.contentView).offset(iPadValue(15, 10));
        make.centerY.equalTo(self.contentView);
        make.left.mas_equalTo(self.logoContainer.mas_right).offset(iPadValue(15, 12));
        make.right.equalTo(self.moreButton.mas_left).offset(-8);
        make.bottom.mas_equalTo(self.contentView).offset(-iPadValue(15, 10));
    }];
    
    [self.moreButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.right.mas_equalTo(self.contentView).offset(-iPadValue(10, 8));
        //宽度自适应
        make.height.mas_equalTo(iPadValue(22, 18));
    }];
    
    [self.selectLogo mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_offset(0);
        make.right.mas_offset(-20);
        make.size.mas_equalTo(iPadValue(25,22));
    }];
    
    [self.line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.contentView);
        make.left.equalTo(self.logoContainer);
        make.right.mas_equalTo(self.contentView);
        make.height.mas_equalTo(0.5);
    }];
}

#pragma mark -- Getters

- (UIView *)tapView
{
    if (!_tapView) {
        _tapView = [UIView new];
    }
    
    return _tapView;
}

- (UIView *)logoContainer
{
    if (!_logoContainer) {
        _logoContainer = [[UIView alloc] init];
        _logoContainer.backgroundColor = [UIColor colorWithHexString:@"#4285F4"];
        _logoContainer.layer.cornerRadius = iPadValue(10, 8);
        _logoContainer.clipsToBounds = YES;
    }
    return _logoContainer;
}

- (UILabel *)logoText
{
    if (!_logoText) {
        _logoText = [[UILabel alloc] init];
        _logoText.textColor = [UIColor whiteColor];
        _logoText.font = [UIFont boldSystemFontOfSize:iPadValue(20, 16)];
        _logoText.textAlignment = NSTextAlignmentCenter;
    }
    return _logoText;
}

- (UIImageView *)folderIcon
{
    if (!_folderIcon) {
        _folderIcon = [[UIImageView alloc]initWithImage:[UIImage imageNamed:@"server_folder_icon"]];
    }
    return _folderIcon;
}

- (UIStackView *)stackView
{
    if(!_stackView) {
        _stackView = [[UIStackView alloc]initWithArrangedSubviews:@[
            self.titleLabel,
            self.urlLabel,
            self.itemCountLabel
        ]];
        
        _stackView.axis = UILayoutConstraintAxisVertical;
        _stackView.spacing = iPadValue(5, 3);
        _stackView.alignment = UIStackViewAlignmentLeading;
    }
    
    return _stackView;
}

- (UILabel *)titleLabel
{
    if(!_titleLabel) {
        _titleLabel = [UIView createLabelWithTitle:@""
                                         textColor:[UIColor colorWithHexString:@"#333333"]
                                           bgColor:UIColor.clearColor
                                          fontSize:iPadValue(18, 14)
                                     textAlignment:NSTextAlignmentLeft
                                             bBold:NO];
        _titleLabel.font = [UIFont systemFontOfSize:iPadValue(18, 14) weight:UIFontWeightMedium];
    }
    
    return _titleLabel;
}

- (UILabel *)urlLabel
{
    if(!_urlLabel) {
        float font = iPadValue(16, 12);
        _urlLabel = [UIView createLabelWithTitle:@""
                                         textColor:[UIColor colorWithHexString:@"#999999"]
                                           bgColor:UIColor.clearColor
                                          fontSize:font
                                     textAlignment:NSTextAlignmentLeft
                                             bBold:NO];
    }
    
    return _urlLabel;
}

- (UILabel *)itemCountLabel
{
    if(!_itemCountLabel) {
        float font = iPadValue(16, 12);
        _itemCountLabel = [UIView createLabelWithTitle:@""
                                         textColor:[UIColor colorWithHexString:@"#999999"]
                                           bgColor:UIColor.clearColor
                                          fontSize:font
                                     textAlignment:NSTextAlignmentLeft
                                             bBold:NO];
    }
    
    return _itemCountLabel;
}

- (UIButton *)moreButton
{
    if (!_moreButton) {
        _moreButton = [UIButton buttonWithType:UIButtonTypeSystem];
        [_moreButton setImage:[[UIImage imageNamed:@"list_more_icon"] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate] forState:UIControlStateNormal];
        _moreButton.imageView.contentMode = UIViewContentModeScaleAspectFit;
    }
    return _moreButton;
}

- (UIImageView *)selectLogo
{
    if(!_selectLogo) {
        _selectLogo = [UIImageView new];
        _selectLogo.image = [UIImage imageNamed:@"standard_checkmark_UnSelected"];
        _selectLogo.hidden = YES;
    }
    
    return _selectLogo;
}

- (UIView *)line
{
    if(!_line) {
        _line = [UIView new];
        _line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
    
    return _line;
}

@end
