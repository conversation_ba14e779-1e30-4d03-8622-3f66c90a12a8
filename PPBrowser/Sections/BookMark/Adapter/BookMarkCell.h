//
//  BookMarkCell.h
//  PPBrowser
//
//  Created by qing<PERSON> on 2022/4/29.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "BookMarkModel.h"

NS_ASSUME_NONNULL_BEGIN

//iPad适配指南
/**
 iPad适配:
 字体:
 18 - 22
 16 - 20
 15 - 20
 14 - 18
 12 - 16
 
 切图半径:
 10 -20
 
 左右上下偏移:
 10 - 30
 15 - 30
 
 cell高度:
 50 - 88或者80
 60 - 88
 75 - 100
 */
@interface BookMarkCell : UITableViewCell

- (void)updateWithModel:(BookMarkModel*)model batchOperation:(BatchType)batchOperation;

@property (nonatomic, copy) void (^didTapAction)(BookMarkModel *model); //直接点击
@property (nonatomic, copy) void (^didMoreBtnTapAction)(BookMarkModel *model); //点击更多按钮

@end

NS_ASSUME_NONNULL_END

