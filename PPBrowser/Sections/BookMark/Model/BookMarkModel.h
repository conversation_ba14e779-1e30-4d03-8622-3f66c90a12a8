//
//  BookMarkModel.h
//  PPBrowser
//
//  Created by qingbin on 2022/4/29.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "BaseModel.h"
#import "PPEnums.h"
#import <CloudKit/CloudKit.h>
#import "SyncProtocol.h"

@protocol BookMarkModel <NSObject>
@end

//多叉树模型
@interface BookMarkModel : BaseModel<SyncProtocol>
//id
@property (nonatomic, strong) NSString *bookmarkId;
//父类id
@property (nonatomic, strong) NSString *parentId;
//网页url
@property (nonatomic, strong) NSString *url;
//网页标题,如果是文件夹,那么表示文件夹名称
@property (nonatomic, strong) NSString *title;
//生成时间
@property (nonatomic, strong) NSString *ctime;
//类型
@property (nonatomic, assign) BookMarkType fileType;

@property(nonatomic,assign) int ppOrder;
// 更新时间，和iCloud的对比
@property (nonatomic, strong) NSString *updateTime;
// v2.6.4
@property (nonatomic, strong) NSString *iconUrl;

//辅助字段,主要是卡片化
@property (nonatomic, assign) BOOL isFirstInSection;
@property (nonatomic, assign) BOOL isLastInSection;
@property (nonatomic, strong) NSMutableArray<BookMarkModel *> *children;
//获取子节点的数量,主要用来显示
@property (nonatomic, assign) int childrenCount;
/// 选中效果
@property (nonatomic, assign) BOOL isSelected;

/// 移动
/// 层级0123
@property (nonatomic, assign) int level;

//树节点根节点id
+ (NSString*)rootId;


//CloudKit同步
//获取一个默认的CKRecord
- (CKRecord *)toDefaultCKRecord;
//转换成CKRecord
- (CKRecord *)toCKRecord;
//从CKRecord转换为UserScript
- (instancetype)initWithCKRecord:(CKRecord *)record;
//判断两个书签是否一致
- (BOOL)objectIsEqualTo:(id)obj;
//返回uuid
- (NSString*)getUuid;
//更新时间
- (NSString*)getUpdateTime;

@end

