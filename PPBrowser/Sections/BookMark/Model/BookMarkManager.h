//
//  BookMarkManager.h
//  PPBrowser
//
//  Created by qingbin on 2023/3/30.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "BookMarkModel.h"

/// 书签导入导出的管理类
@interface BookMarkManager : NSObject

//导出书签
- (void)exportBookMarks;

//导入书签
- (void)parseBookMarksFromHtml:(NSString *)html;

- (void)exportBookMarksWithArray:(NSArray<BookMarkModel*> *)array
                      completion:(void(^)(void))completion;

@end

