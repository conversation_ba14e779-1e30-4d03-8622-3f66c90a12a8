//
//  BrowserBookMarkManager.m
//  Reader
//
//  Created by qing<PERSON> on 2023/12/17.
//

#import "BookMarkManager.h"

#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "DatabaseUnit+BookMark.h"
#import "DatabaseUnit+CustomTag.h"

#import "BrowserUtils.h"
#import "NSObject+Helper.h"
#import "UIView+Helper.h"
#import "PPNotifications.h"

@interface BookMarkManager ()<NSXMLParserDelegate>
{
    NSString *_string;
    NSUInteger _index;
    BookMarkModel *_rootBookmark;
    NSMutableArray *_allBookmarks;
}

@property (nonatomic, strong) NSMutableArray *allItems;
/// 首页标签
@property (nonatomic, strong) NSMutableArray *homepageItems;

@end

@implementation BookMarkManager

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.allItems = [NSMutableArray array];
        self.homepageItems = [NSMutableArray array];
    }
    return self;
}

#pragma mark -- 导入书签
//导入书签
- (void)parseBookMarksFromHtml:(NSString *)html
{
    [self.homepageItems removeAllObjects];
    
    if(html.length == 0) {
        [UIView showFailed:NSLocalizedString(@"bookmark.import.fail", nil)];
        return;
    }
    
    _string = html;
    _index = 0;
    _rootBookmark = [BookMarkModel new];
    _allBookmarks = [NSMutableArray array];
    
    [self parse];
    
    if(self.homepageItems.count > 0) {
        //有首页标签
        @weakify(self)
        DatabaseUnit* unit = [DatabaseUnit queryAllCustomTags];
        [unit setCompleteBlock:^(NSArray<CustomTagModel *>* result, BOOL success) {
            @strongify(self)
            if (success) {
                [self _addCustomTagWithCurrentModel:result];
            }
        }];
        DB_EXEC(unit);
    }

    if(_allBookmarks.count > 0) {
        DatabaseUnit* unit = [DatabaseUnit addBookMarkArrayWithItem:_allBookmarks];
        [unit setCompleteBlock:^(id result, BOOL success) {
            if(success) {
                [UIView showSucceed:NSLocalizedString(@"common.import.success", nil)];
                
                [[NSNotificationCenter defaultCenter] postNotificationName:kBookMarkDidChangeNotification object:nil];
            } else {
                [UIView showFailed:NSLocalizedString(@"common.import.fail", nil)];
            }
        }];
        
        DB_EXEC(unit);
    }
}

- (void)_addCustomTagWithCurrentModel:(NSArray<CustomTagModel *> *)currentArray
{
    int maxOrder = 0;
    for(CustomTagModel* obj in currentArray) {
        maxOrder = MAX(obj.ppOrder, maxOrder);
    }
    
    NSMutableArray* array = [NSMutableArray array];
    for(int i=0;i<self.homepageItems.count;i++) {
        BookMarkModel* obj = self.homepageItems[i];
        CustomTagModel* item = [CustomTagModel new];
        item.uuid = [[NSUUID UUID] UUIDString];
        item.title = obj.title;
        item.targetUrl = obj.url;
        item.iconUrl = obj.iconUrl ?: @"";
        item.ctime = obj.ctime;
        //新插入的书签，排在现有书签后面
        item.ppOrder = maxOrder + 1 + i;
        
        [array addObject:item];
    }
    
    DatabaseUnit* unit = [DatabaseUnit addCustomTagArrayWithItem:array];
    [unit setCompleteBlock:^(id result, BOOL success) {
        [[NSNotificationCenter defaultCenter] postNotificationName:kAddHomeCustomTagNotification object:nil];
    }];
    
    DB_EXEC(unit);
}
                                                                                      

- (void)parse
{
    while (_index < _string.length) {
        NSString *line = [self nextLine];
        //去掉首尾两端的空格
        line = [line stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceCharacterSet]];
        if ([line hasPrefix:@"<!DOCTYPE"]) {
            continue;
        } else if ([line hasPrefix:@"<META"]) {
            continue;
        } else if ([line hasPrefix:@"<H1>"]) {
            continue;
        } else if ([line hasPrefix:@"<DT><H3"]) {
            NSString *title = [self extractH3TitleFromLine:line];
            //首页标签
            BOOL isHomepageItem = [self isHomepageItemWithLine:line];
            
            BookMarkModel* bookmark = [[BookMarkModel alloc] init];
            bookmark.bookmarkId = [[NSUUID UUID] UUIDString];
            bookmark.title = title;
            bookmark.parentId = [BookMarkModel rootId];
            bookmark.fileType = BookMarkTypeFolder;
            
            if(!isHomepageItem) {
                [_allBookmarks addObject:bookmark];
            }
            
            [self parseChildrenOfBookmark:bookmark];
            
            if(!isHomepageItem) {
                [_rootBookmark.children addObject:bookmark];
            }
        } else if ([line hasPrefix:@"<DT><A"]) {
            BookMarkModel *bookmark = [[BookMarkModel alloc] init];
            bookmark.bookmarkId = [[NSUUID UUID] UUIDString];
            bookmark.title = [self extractATitleFromLine:line];
            bookmark.url = [self extractUrlFromLine:line];
            bookmark.ctime = [self extractCTimeFromLine:line];
            bookmark.parentId = [BookMarkModel rootId];
            bookmark.fileType = BookMarkTypeFile;
            
            [_allBookmarks addObject:bookmark];
            [_rootBookmark.children addObject:bookmark];
        }
    }
}

- (void)parseChildrenOfBookmark:(BookMarkModel *)bookmark
{
    while (_index < _string.length) {
        NSString *line = [self nextLine];
        //去掉首尾两端的空格
        line = [line stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceCharacterSet]];
        if ([line hasPrefix:@"</DL><p>"] || [line hasPrefix:@"</DL>"]) {
            return;
        } else if ([line hasPrefix:@"<DT><H3"]) {
            //首页标签放在根节点的最前面，因此不考虑嵌套的情况
            NSString *title = [self extractH3TitleFromLine:line];
            BookMarkModel *childBookmark = [[BookMarkModel alloc] init];
            childBookmark.title = title;
            childBookmark.fileType = BookMarkTypeFolder;
            childBookmark.bookmarkId = [[NSUUID UUID] UUIDString];
            childBookmark.parentId = bookmark.bookmarkId;
            
            [bookmark.children addObject:childBookmark];
            [self parseChildrenOfBookmark:childBookmark];
            
            [_allBookmarks addObject:childBookmark];
        } else if ([line hasPrefix:@"<DT><A"]) {
            //首页标签
            BOOL isHomepageItem = [self isHomepageItemWithLine:line];
            
            BookMarkModel *childBookmark = [[BookMarkModel alloc] init];
            childBookmark.title = [self extractATitleFromLine:line];
            childBookmark.url = [self extractUrlFromLine:line];
            bookmark.ctime = [self extractCTimeFromLine:line];
            childBookmark.fileType = BookMarkTypeFile;
            childBookmark.bookmarkId = [[NSUUID UUID] UUIDString];
            childBookmark.parentId = bookmark.bookmarkId;
            
            if(isHomepageItem) {
                //首页标签
                childBookmark.iconUrl = [self extractIconUrlFromLine:line];
                childBookmark.fileType = BookMarkTypeFocus_Homepage;
                
                [self.homepageItems addObject:childBookmark];
            } else {
                //正常书签
                [bookmark.children addObject:childBookmark];
                [_allBookmarks addObject:childBookmark];
            }
        }
    }
}

- (NSString *)nextLine
{
    NSRange range = [_string rangeOfString:@"\n" options:0 range:NSMakeRange(_index, _string.length - _index)];
    NSString *line = [_string substringWithRange:NSMakeRange(_index, range.location - _index)];
    _index = range.location + 1;
    return line;
}

- (NSString *)extractH3TitleFromLine:(NSString *)line
{
    NSError *error = nil;
    NSRegularExpression *regex = [NSRegularExpression regularExpressionWithPattern:@"<H3.*?>(.*?)</H3>" options:NSRegularExpressionCaseInsensitive error:&error];
    NSTextCheckingResult *result = [regex firstMatchInString:line options:0 range:NSMakeRange(0, [line length])];
    if (result) {
        return [line substringWithRange:[result rangeAtIndex:1]];
    }
    return nil;
}

- (NSString *)extractATitleFromLine:(NSString *)line
{
    NSError *error = nil;
    NSRegularExpression *regex = [NSRegularExpression regularExpressionWithPattern:@"<A.*?>(.*?)</A>" options:NSRegularExpressionCaseInsensitive error:&error];
    NSTextCheckingResult *result = [regex firstMatchInString:line options:0 range:NSMakeRange(0, [line length])];
    if (result) {
        return [line substringWithRange:[result rangeAtIndex:1]];
    }
    return nil;
}

- (NSString *)extractUrlFromLine:(NSString *)line
{
    NSRegularExpression *regex = [NSRegularExpression regularExpressionWithPattern:@"HREF=\"([^\"]*)\"" options:0 error:nil];
    NSTextCheckingResult *match = [regex firstMatchInString:line options:0 range:NSMakeRange(0, line.length)];
    if (match) {
        return [line substringWithRange:[match rangeAtIndex:1]];
    } else {
        return nil;
    }
}

- (NSString *)extractCTimeFromLine:(NSString *)line
{
    NSRegularExpression *regex = [NSRegularExpression regularExpressionWithPattern:@"ADD_DATE=\"([^\"]*)\"" options:0 error:nil];
    NSTextCheckingResult *match = [regex firstMatchInString:line options:0 range:NSMakeRange(0, line.length)];
    if (match) {
        return [line substringWithRange:[match rangeAtIndex:1]];
    } else {
        //给一个默认值，否则点击排序时没有效果
        return [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
    }
}

- (NSString *)extractIconUrlFromLine:(NSString *)line
{
    NSRegularExpression *regex = [NSRegularExpression regularExpressionWithPattern:@"ICON_HREF=\"([^\"]*)\"" options:0 error:nil];
    NSTextCheckingResult *match = [regex firstMatchInString:line options:0 range:NSMakeRange(0, line.length)];
    if (match) {
        return [line substringWithRange:[match rangeAtIndex:1]];
    } else {
        return nil;
    }
}

- (BOOL)isHomepageItemWithLine:(NSString *)line
{
    return [line rangeOfString:@"FOCUS_HOMEPAGE"].location != NSNotFound;
}

#pragma mark -- 导出书签
- (void)exportBookMarks
{
    [self.homepageItems removeAllObjects];
    [self.allItems removeAllObjects];
    
    dispatch_group_t group = dispatch_group_create();
    
    __block NSArray* rootArray = nil;
    DatabaseUnit* unit = [DatabaseUnit queryBookMarkListWithId:[BookMarkModel rootId]];
    @weakify(self)
    dispatch_group_enter(group);
    [unit setCompleteBlock:^(NSArray<BookMarkModel*>* result, BOOL success) {
        rootArray = result;
        
        dispatch_group_leave(group);
    }];
    DB_EXEC(unit);
    
    unit = [DatabaseUnit queryAllBookMarks];
    dispatch_group_enter(group);
    [unit setCompleteBlock:^(id result, BOOL success) {
        @strongify(self)
        self.allItems = result;
        
        dispatch_group_leave(group);
    }];
    DB_EXEC(unit);
    
    //首页标签
    unit = [DatabaseUnit queryAllCustomTags];
    dispatch_group_enter(group);
    [unit setCompleteBlock:^(NSArray<CustomTagModel *>* result, BOOL success) {
        @strongify(self)
        //排除掉内置项
        NSMutableArray* filterResult = [NSMutableArray array];
        for (CustomTagModel* obj in result) {
            if (obj.type == CustomTagTypeAdd) {
                [filterResult addObject:obj];
            }
        }
        self.homepageItems = [filterResult mutableCopy];
        
        dispatch_group_leave(group);
    }];
    DB_EXEC(unit);
    
    dispatch_group_notify(group, dispatch_get_main_queue(), ^{
        @strongify(self)
        [self _handleExportBookMarks:rootArray completion:nil];
    });
}

/// 查询该节点的直接子节点
- (NSArray*)_queryBookMarkListWithId:(NSString*)bookMarkId
{
    if(bookMarkId.length == 0) return nil;
    
    NSMutableArray* items = [NSMutableArray array];
    for(BookMarkModel* item in self.allItems) {
        if([item.parentId isEqualToString:bookMarkId]) {
            [items addObject:item];
        }
    }
    
    return items;
}

- (void)_handleExportBookMarks:(NSArray *)bookMarks
                    completion:(void(^)(void))completion
{
    NSMutableArray* array = [NSMutableArray array];
    //首页标签放在根节点的最前面
    if(self.homepageItems.count > 0) {
        //将首页标签伪装成书签
        BookMarkModel* homepageModel = [BookMarkModel new];
        homepageModel.fileType = BookMarkTypeFocus_Homepage;
        [array addObject:homepageModel];
        
        for(CustomTagModel* obj in self.homepageItems) {
            BookMarkModel* item = [BookMarkModel new];
            item.url = obj.targetUrl;
            item.title = obj.title;
            item.iconUrl = obj.iconUrl;
            item.ctime = obj.ctime;
            
            [homepageModel.children addObject:item];
        }
    }
    [array addObjectsFromArray:bookMarks];
    
    [self _handleExportBookMarkArray:array completion:completion];
}

- (void)_handleExportBookMarkArray:(NSArray *)bookMarks
                        completion:(void(^)(void))completion
{
    NSMutableString *bookmarkFile = [NSMutableString string];

    // 添加书签文件头
    [bookmarkFile appendString:@"<!DOCTYPE NETSCAPE-Bookmark-file-1>\n"];
    [bookmarkFile appendString:@"<!-- This is an automatically generated file.\n"];
    [bookmarkFile appendString:@"     It will be read and overwritten.\n"];
    [bookmarkFile appendString:@"     DO NOT EDIT! -->\n"];
    [bookmarkFile appendString:@"<META HTTP-EQUIV=\"Content-Type\" CONTENT=\"text/html; charset=UTF-8\">\n"];
    [bookmarkFile appendString:@"<TITLE>Bookmarks</TITLE>\n"];
    [bookmarkFile appendString:@"<H1>Bookmarks</H1>\n"];

    // 递归生成书签项
    [bookmarkFile appendString:@"<DL><p>\n"];
    for (BookMarkModel *item in bookMarks) {
        [bookmarkFile appendString:[self bookmarkItemForModel:item]];
    }
    [bookmarkFile appendString:@"</DL><p>\n"];

    //导出html格式
    NSString* textToShare = @"Bookmarks.html";
    
    NSFileManager* fileManager = [NSFileManager defaultManager];
    NSString *tmpFileName = [NSString stringWithFormat:@"tmp/%@",textToShare];
    NSString *path = [NSHomeDirectory() stringByAppendingPathComponent:tmpFileName];
    if([fileManager fileExistsAtPath:path]) {
        [fileManager removeItemAtPath:path error:nil];
    }
    [bookmarkFile writeToFile:path atomically:YES encoding:NSUTF8StringEncoding error:nil];
    NSURL* urlToShare = [NSURL fileURLWithPath:path];
    
    NSArray* items = @[urlToShare];
    
    UIActivityViewController *vc = [[UIActivityViewController alloc] initWithActivityItems:items applicationActivities:nil];
    vc.completionWithItemsHandler = ^(UIActivityType __nullable activityType, BOOL completed, NSArray * __nullable returnedItems, NSError * __nullable activityError) {
        if([fileManager fileExistsAtPath:path]) {
            [fileManager removeItemAtPath:path error:nil];
        }
        
        if(!activityError && completed) {
            [UIView showSucceed:NSLocalizedString(@"bookmark.export.succ", nil)];
        }
        
        if(completion) {
            completion();
        }
    };
    
    [UIView hideHud:NO];
    
    UIWindow* window = [NSObject normalWindow];
    if([BrowserUtils isiPad]) {
        //iPad
        vc.popoverPresentationController.sourceView = window;
        vc.popoverPresentationController.sourceRect = CGRectMake(CGRectGetMidX(window.frame), CGRectGetMidY(window.frame), 0, 0);
    }
    
    [window.rootViewController presentViewController:vc animated:YES completion:nil];
}

- (NSString *)bookmarkItemForModel:(BookMarkModel *)bookmarkModel
{
    NSMutableString *bookmarkItem = [NSMutableString string];

    // 如果这是一个文件夹，则递归处理它的子项
    if (bookmarkModel.fileType == BookMarkTypeFolder) {
        NSLog(@"title = %@", bookmarkModel.title);
        
        [bookmarkItem appendFormat:@"<DT><H3>%@</H3>\n", bookmarkModel.title?:@""];
        [bookmarkItem appendString:@"<DL><p>\n"];
        
        NSArray* childs = [self _queryBookMarkListWithId:bookmarkModel.bookmarkId];
        for (BookMarkModel *child in childs) {
            [bookmarkItem appendString:[self bookmarkItemForModel:child]];
        }

        [bookmarkItem appendString:@"</DL><p>\n"];
    }
    // 如果这是一个书签项，则直接生成书签项
    else if (bookmarkModel.fileType == BookMarkTypeFile) {
        [bookmarkItem appendFormat:@"<DT><A HREF=\"%@\" ADD_DATE=\"%@\">%@</A>\n", bookmarkModel.url?:@"", bookmarkModel.ctime?:@"", bookmarkModel.title?:@""];
    }
    // 轻阅首页标签，将它当做一个特殊的书签项
    else if(bookmarkModel.fileType == BookMarkTypeFocus_Homepage) {
        // 首页标签不需要考虑树结构，直接是一个列表，简化逻辑
        if(bookmarkModel.children.count == 0) return bookmarkItem;
        
        [bookmarkItem appendFormat:@"<DT><H3 FOCUS_HOMEPAGE>%@</H3>\n", @"主页"];
        [bookmarkItem appendString:@"<DL><p>\n"];
        
        for(BookMarkModel* obj in bookmarkModel.children) {
            [bookmarkItem appendFormat:@"<DT><A HREF=\"%@\" ADD_DATE=\"%@\" ICON_HREF=\"%@\" FOCUS_HOMEPAGE>%@</A>\n", obj.url?:@"", obj.ctime?:@"", obj.iconUrl?:@"", obj.title?:@""];
        }
        
        [bookmarkItem appendString:@"</DL><p>\n"];
    }

    return bookmarkItem;
}

#pragma mark -- 选择性分享

- (void)exportBookMarksWithArray:(NSArray<BookMarkModel*> *)array
                      completion:(void(^)(void))completion
{
    if(array.count == 0) return;
    
    DatabaseUnit* unit = [DatabaseUnit queryAllBookMarks];
    @weakify(self)
    [unit setCompleteBlock:^(id result, BOOL success) {
        @strongify(self)
        self.allItems = result;
        
        [self _handleExportBookMarkArray:array completion:completion];
    }];
    DB_EXEC(unit);
}

@end
