//
//  BookMarkModel.m
//  PPBrowser
//
//  Created by qing<PERSON> on 2022/4/29.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "BookMarkModel.h"
#import "CloudKitHelper.h"
#import "OpenUDID.h"
#import "NSFileManager+Helper.h"

@implementation BookMarkModel

+ (NSString*)rootId
{
    return @"root";
}

//将指定的字段存储到iCloud中
//- (NSString *)toiCloudJsonString
//{
//    return [self toJSONStringWithKeys:@[@"bookmarkId", @"title", @"parentId", @"url", @"fileType"]];
//}

- (NSMutableArray *)children
{
    if(!_children) {
        _children = [NSMutableArray array];
    }
    
    return _children;
}

//返回uuid
- (NSString*)getUuid
{
    return self.bookmarkId;
}

//更新时间
- (NSString*)getUpdateTime
{
    return self.updateTime;
}

//CloudKit同步
//获取一个默认的CKRecord
- (CKRecord *)toDefaultCKRecord
{
    CKRecordID* recordID = [[CKRecordID alloc]initWithRecordName:self.bookmarkId zoneID:[CloudKitHelper focusZoneID]];
    CKRecord* record = [[CKRecord alloc]initWithRecordType:NSStringFromClass(BookMarkModel.class) recordID:recordID];
    
    return record;
}

//CloudKit同步
//转换成CKRecord
- (CKRecord *)toCKRecord
{
    CKRecord* record = [self toDefaultCKRecord];
    
    record[@"bookmarkId"] = self.bookmarkId;
    record[@"title"] = self.title?:@"";
    record[@"parentId"] = self.parentId?:@"";
    record[@"url"] = self.url?:@"";
    record[@"fileType"] = @(self.fileType);
    record[@"ppOrder"] = @(self.ppOrder);
    record[@"ctime"] = self.ctime;
    record[@"updateTime"] = self.updateTime?:@"1";
    
    //额外添加的字段，用于CloudKit
    //app版本号
    NSString* version = [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleShortVersionString"];
    record[@"appVersion"] = version?:@"";
    //上传到CloudKit创建时间
    record[@"appCtime"] = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
    //用户的uuid
    record[@"appUserID"] = [OpenUDID value]?:@"";
    //添加类型区分
    record[@"appType"] = @(CloudModelTypeBookMark);
        
    return record;
}
//从CKRecord转换为BookMark
- (instancetype)initWithCKRecord:(CKRecord *)record
{
    self = [super init];
    if(self) {
        self.bookmarkId = record[@"bookmarkId"];
        self.title = record[@"title"];
        self.parentId = record[@"parentId"];
        
        self.url = record[@"url"];
        self.fileType = [[record objectForKey:@"fileType"] intValue];
        self.ppOrder = [[record objectForKey:@"ppOrder"] intValue];
        self.ctime = record[@"ctime"];
        
        NSString* updateTimeText = record[@"updateTime"];
        NSInteger updateTime = [updateTimeText integerValue];
        if(updateTime == 0 || updateTime == 1) {
            //初始化
            updateTime = [record.modificationDate timeIntervalSince1970];
        }
        self.updateTime = [NSString stringWithFormat:@"%ld", updateTime];
    }
    
    return self;
}

//判断两个书签是否一致
- (BOOL)objectIsEqualTo:(id)obj
{
    BookMarkModel *item = obj;
    if(item.fileType != self.fileType) return false;
    if([self.bookmarkId isEqualToString:item.bookmarkId]) return true;
    if(self.fileType == BookMarkTypeFolder && [self.title isEqualToString:item.title]) return true;
    
    //url    
    return [self.url isEqualToString:item.url];
}

@end
