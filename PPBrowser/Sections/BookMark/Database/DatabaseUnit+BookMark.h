//
//  DatabaseUnit+BookMark.h
//  PPBrowser
//
//  Created by qingbin on 2022/4/29.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "DatabaseUnit.h"
#import "PPEnums.h"
#import "BookMarkModel.h"

@interface DatabaseUnit (BookMark)

// 查询所有节点,用于初始化内存缓存
+ (DatabaseUnit*)queryAllBookMarks;

// 添加一组节点
+ (DatabaseUnit*)addBookMarkWithItem:(BookMarkModel*)item;

// 测试，插入1万条书签
+ (DatabaseUnit*)addBookMarkWithArray:(NSArray<BookMarkModel *>*)array
                           toParentId:(NSString *)parentId;

//删除一组节点
+ (DatabaseUnit*)removeBookMarkTreeWithIds:(NSArray*)bookMarkIds;

//查询该节点
+ (DatabaseUnit*)queryBookMarkWithId:(NSString*)bookMarkId;

//查询该节点下所有的子节点
+ (DatabaseUnit*)queryBookMarkListWithId:(NSString*)bookMarkId;

//联想查询
//+ (DatabaseUnit*)searchBookMarkWithQuery:(NSString*)query
//                                maxCount:(NSUInteger)maxCount;

//移动一组节点
+ (DatabaseUnit*)updateBookMarkListWithArray:(NSArray*)bookMarkArray
                                    toparent:(NSString*)parentId;

//移动一组节点
+ (DatabaseUnit*)updateBookMarkListWithIds:(NSArray*)bookMarkIds
                                  toparent:(NSString*)parentId;

//更新名称
+ (DatabaseUnit*)updateBookMarkWithId:(NSString*)bookMarkId
                                title:(NSString*)title
                                  url:(NSString*)url
                             bookMark:(BookMarkModel *)bookMark;

//批量更新顺序
+ (DatabaseUnit*)updateBookMarkAllOrder:(NSArray*)allItems;

//书签导入导出
+ (DatabaseUnit*)addBookMarkArrayWithItem:(NSArray<BookMarkModel*>*)items;

//移动相关操作
+ (DatabaseUnit*)queryBookMarkMoveNodes;

///
// CloudKit, 添加多个
+ (DatabaseUnit*)addBookMarkArray:(NSArray<BookMarkModel*>*)items;

// CloudKit, 批量更新多个
+ (DatabaseUnit*)updateBookMarkArray:(NSArray<BookMarkModel*>*)array;

// CloudKit, 批量删除多个
+ (DatabaseUnit*)removeBookMarkArray:(NSArray*)bookMarkIds;


@end

