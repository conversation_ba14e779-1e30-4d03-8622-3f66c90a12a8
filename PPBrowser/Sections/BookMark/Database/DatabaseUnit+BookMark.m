//
//  DatabaseUnit+BookMark.m
//  PPBrowser
//
//  Created by qingbin on 2022/4/29.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "DatabaseUnit+BookMark.h"
#import "ReactiveCocoa.h"

#import "FMDatabaseQueue.h"
#import "FMDatabase.h"

#import "PPEnums.h"

#import "SyncEngine.h"
#import "CloudKitHelper.h"
#import "CommonDataManager.h"

@implementation DatabaseUnit (BookMark)

+ (DatabaseUnit*)addBookMarkWithItem:(BookMarkModel *)item
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    //必须有id
    if(item.bookmarkId.length == 0) {
        item.bookmarkId = [[NSUUID UUID] UUIDString];
    }
    
    //parentId外面保证赋值,如果是根节点, 那么parentId=="root"
    //fileType也是外层代码赋值
    if(item.url.length == 0) item.url = @"";
    if(item.title.length == 0) item.title = @"";
    
    NSString* time = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
    item.ctime = time;
    item.updateTime = time;
    //新添加的排在最前面
    item.ppOrder = 0;
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        //插入数据
        
        NSString* command = @"SELECT * FROM t_bookmark WHERE bookmarkId = ?";
        FMResultSet* set = [db executeQuery:command, item.bookmarkId];
        
        BOOL result = YES;
        if([set next]) {
            //有记录，更新
            NSString* command = [NSString stringWithFormat:@"UPDATE t_bookmark SET parentId=?, title=?, url=?, fileType=?, ppOrder=?, updateTime=? WHERE bookmarkId=?"];
            result = [db executeUpdate:command, item.parentId?:@"", item.title?:@"", item.url?:@"", @(item.fileType), @(item.ppOrder), item.updateTime, item.bookmarkId];
        } else {
            //没有记录，新增
            NSString* command = @"INSERT INTO t_bookmark(bookmarkId, parentId, title, url, fileType, ppOrder, updateTime, ctime) VALUES (?,?,?,?,?,?,?,?)";

            result = [db executeUpdate:command, item.bookmarkId, item.parentId?:@"", item.title?:@"", item.url?:@"", @(item.fileType), @(item.ppOrder), item.updateTime, item.ctime];
        }
        
        if(result) {
            //同步到CloudKit中
            [[SyncEngine shareInstance] syncRecordsToCloudKit:@[[item toCKRecord]] recordIDsToDelete:nil completion:nil];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
            
            [[CommonDataManager shareInstance] reloadBookMarks];
        });
    };
    
    return unit;
}

// 测试
+ (DatabaseUnit*)addBookMarkWithArray:(NSArray<BookMarkModel *>*)array
                           toParentId:(NSString *)parentId
{
    DatabaseUnit* unit = [DatabaseUnit new];
        
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        //插入数据
        
        BOOL result = YES;
        for(BookMarkModel* item in array) {
            NSString* time = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
            item.ctime = time;
            item.updateTime = time;
            //新添加的排在最前面
            item.ppOrder = 0;
            item.bookmarkId = [[NSUUID UUID] UUIDString];
            item.parentId = parentId;
            
            NSString* command = @"INSERT INTO t_bookmark(bookmarkId, parentId, title, url, fileType, ppOrder, updateTime, ctime) VALUES (?,?,?,?,?,?,?,?)";

            result = [db executeUpdate:command, item.bookmarkId, item.parentId?:@"", item.title?:@"", item.url?:@"", @(item.fileType), @(item.ppOrder), item.updateTime, item.ctime];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

/// 根据bookMarkId获取所有子类，包括直接子类和所有间接子类
+ (NSArray*)_queryBookMarkTreeWithId:(NSString*)bookMarkId
                            allItems:(NSMutableArray*)allItems
{
    if(bookMarkId.length == 0) return nil;
    NSMutableArray* result = [NSMutableArray array];
    for(BookMarkModel* item in allItems) {
        if([item.parentId isEqualToString:bookMarkId]) {
            [result addObject:item];
            if(item.fileType == BookMarkTypeFolder) {
                NSArray* tree = [self _queryBookMarkTreeWithId:item.bookmarkId allItems:allItems];
                if(tree.count > 0) {
                    [result addObjectsFromArray:tree];
                }
            }
        }
    }
    
    return result;
}

/// 根据bookMarkId删除缓存中所有子类，包括直接子类和所有间接子类，包括它自己
+ (NSArray*)_removeBookMarkTreeWithId:(NSString*)bookMarkId
                           allItemMap:(NSMutableDictionary*)allItemMap
                             allItems:(NSMutableArray*)allItems
{
    NSMutableArray* removeItems = [NSMutableArray array];
    //添加根节点
    BookMarkModel* item = allItemMap[bookMarkId];
    [removeItems addObject:item];
    
    NSArray* items = [self _queryBookMarkTreeWithId:bookMarkId allItems:allItems];
    [removeItems addObjectsFromArray:items];
    
    if(removeItems.count == 0) return nil;
            
    [allItems removeObjectsInArray:removeItems];
    
    for(BookMarkModel* item in removeItems) {
        allItemMap[item.bookmarkId] = nil;
    }
    
    return removeItems;
}

//删除一组节点
+ (DatabaseUnit*)removeBookMarkTreeWithIds:(NSArray*)bookMarkIds
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = [NSString stringWithFormat:@"SELECT * FROM t_bookmark"];
        FMResultSet* set = [db executeQuery:command];
        
        NSMutableArray *array = [[NSMutableArray alloc] init];
        NSMutableDictionary* allItemMap = [NSMutableDictionary dictionary];
        while([set next]) {
            BookMarkModel* item = [[BookMarkModel alloc]initWithDictionary:[set resultDictionary] error:nil];
            [array addObject:item];
            allItemMap[item.bookmarkId] = item;
        }
        
        NSMutableArray* allIds = [NSMutableArray array];
        for(NSString* bookMarkId in bookMarkIds) {
            //从缓存中删除对应树节点
            NSArray* items = [self _removeBookMarkTreeWithId:bookMarkId allItemMap:allItemMap allItems:array];
            for(BookMarkModel* item in items) {
                [allIds addObject:item.bookmarkId];
            }
        }
        
        NSMutableArray* recordIDs = [NSMutableArray array];
        NSMutableString* ids = [NSMutableString new];
        for(int i=0;i<allIds.count;i++) {
            NSString* str = allIds[i];
            NSString* option = [NSString stringWithFormat:@"\"%@\"",str];
            [ids appendString:option];
            if(i < allIds.count-1) {
                [ids appendString:@","];
            }
            
            //同步到CloudKit中
            CKRecordID* recordID = [[CKRecordID alloc]initWithRecordName:str zoneID:[CloudKitHelper focusZoneID]];
            [recordIDs addObject:recordID];
        }
    
        command = [NSString stringWithFormat:@"DELETE FROM t_bookmark WHERE bookmarkId in (%@);",ids];
        BOOL result = [db executeUpdate:command];
        
        if(result) {
            //同步到CloudKit中
            [[SyncEngine shareInstance] syncRecordsToCloudKit:nil recordIDsToDelete:recordIDs completion:nil];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
            
            [[CommonDataManager shareInstance] reloadBookMarks];
        });
    };
    
    return unit;
}

// 查询所有节点,用于初始化内存缓存
+ (DatabaseUnit*)queryAllBookMarks
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = [NSString stringWithFormat:@"SELECT * FROM t_bookmark"];
        FMResultSet* set = [db executeQuery:command];
        
        NSMutableArray *array = [[NSMutableArray alloc] init];
        while([set next]) {
            BookMarkModel* item = [[BookMarkModel alloc]initWithDictionary:[set resultDictionary] error:nil];
            
            //获取下一级目录的数量
            if (item.fileType == BookMarkTypeFolder) {
                //这里会非常耗时，如果不是目录，不要做这个操作
                command = [NSString stringWithFormat:@"SELECT * FROM t_bookmark WHERE parentId=?"];
                FMResultSet* countSet = [db executeQuery:command, item.bookmarkId];
                item.childrenCount = 0;
                while ([countSet next]) {
                    item.childrenCount++;
                }
            }

            [array addObject:item];
        }
        
        //数据库返回的顺序不正确
        array = [[array sortedArrayUsingComparator:^NSComparisonResult(BookMarkModel*  _Nonnull obj1, BookMarkModel*  _Nonnull obj2) {
            if(obj1.ppOrder < obj2.ppOrder) {
                return NSOrderedAscending;
            } else {
                if([obj1.ctime integerValue] > [obj2.ctime integerValue]) {
                    return NSOrderedAscending;
                } else {
                    return NSOrderedDescending;
                }
            }
        }] mutableCopy];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(array,YES);
            }
        });
    };
    
    return unit;
}

+ (DatabaseUnit*)queryBookMarkWithId:(NSString*)bookMarkId;
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        BookMarkModel* item;
        NSString* command = [NSString stringWithFormat:@"SELECT * FROM t_bookmark WHERE bookmarkId=? ORDER BY ppOrder asc, ctime desc"];
        FMResultSet* set = [db executeQuery:command, bookMarkId];
        
        if([set next]) {
            item = [[BookMarkModel alloc]initWithDictionary:[set resultDictionary] error:nil];
            
            //获取下一级目录的数量
            if (item.fileType == BookMarkTypeFolder) {
                //这里会非常耗时，如果不是目录，不要做这个操作
                command = [NSString stringWithFormat:@"SELECT * FROM t_bookmark WHERE parentId=?"];
                FMResultSet* countSet = [db executeQuery:command, item.bookmarkId];
                item.childrenCount = 0;
                while ([countSet next]) {
                    item.childrenCount++;
                }
            }
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(item,YES);
            }
        });
    };
    
    return unit;
}

+ (DatabaseUnit*)queryBookMarkListWithId:(NSString*)bookMarkId;
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)

        NSString* command = [NSString stringWithFormat:@"SELECT * FROM t_bookmark WHERE parentId=? ORDER BY ppOrder asc, ctime desc"];
        FMResultSet* set = [db executeQuery:command, bookMarkId];
        
        NSMutableArray *array = [[NSMutableArray alloc] init];
        while([set next]) {
            BookMarkModel* item = [[BookMarkModel alloc]initWithDictionary:[set resultDictionary] error:nil];
            
            //获取下一级目录的数量
            if (item.fileType == BookMarkTypeFolder) {
                //这里会非常耗时，如果不是目录，不要做这个操作
                command = [NSString stringWithFormat:@"SELECT * FROM t_bookmark WHERE parentId=?"];
                FMResultSet* countSet = [db executeQuery:command, item.bookmarkId];
                item.childrenCount = 0;
                while ([countSet next]) {
                    item.childrenCount++;
                }
            }

            [array addObject:item];
        }
        
        //数据库返回的顺序不正确
        array = [[array sortedArrayUsingComparator:^NSComparisonResult(BookMarkModel*  _Nonnull obj1, BookMarkModel*  _Nonnull obj2) {
            if(obj1.ppOrder < obj2.ppOrder) {
                return NSOrderedAscending;
            } else {
                if([obj1.ctime integerValue] > [obj2.ctime integerValue]) {
                    return NSOrderedAscending;
                } else {
                    return NSOrderedDescending;
                }
            }
        }] mutableCopy];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(array,YES);
            }
        });
    };
    
    return unit;
}

//移动一组节点
+ (DatabaseUnit*)updateBookMarkListWithArray:(NSArray*)bookMarkArray
                                    toparent:(NSString*)parentId
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSMutableString* ids = [NSMutableString new];
        for(int i=0;i<bookMarkArray.count;i++) {
            BookMarkModel* bookMark = bookMarkArray[i];
            NSString* str = bookMark.bookmarkId;
            NSString* option = [NSString stringWithFormat:@"\"%@\"",str];
            [ids appendString:option];
            if(i < bookMarkArray.count-1) {
                [ids appendString:@","];
            }
        }
        NSString* time = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        NSString* command = [NSString stringWithFormat:@"UPDATE t_bookmark SET parentId = ?, updateTime = ? WHERE bookmarkId in (%@);",ids];
        BOOL result = [db executeUpdate:command,parentId, time];
        
        if(result) {
            //同步到CloudKit中
            NSMutableArray* records = [NSMutableArray array];
            for(BookMarkModel* item in bookMarkArray) {
                CKRecord* record = [item toDefaultCKRecord];
                record[@"parentId"] = item.parentId?:@"";
                record[@"updateTime"] = time;
                
                [records addObject:record];
            }
            
            [[SyncEngine shareInstance] syncRecordsToCloudKit:records recordIDsToDelete:nil completion:nil];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil,result);
            }
        });
    };
    
    return unit;
}

//移动一组节点
+ (DatabaseUnit*)updateBookMarkListWithIds:(NSArray*)bookMarkIds
                                  toparent:(NSString*)parentId
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSMutableString* ids = [NSMutableString new];
        for(int i=0;i<bookMarkIds.count;i++) {
            NSString* str = bookMarkIds[i];
            NSString* option = [NSString stringWithFormat:@"\"%@\"",str];
            [ids appendString:option];
            if(i < bookMarkIds.count-1) {
                [ids appendString:@","];
            }
        }
        NSString* command = [NSString stringWithFormat:@"UPDATE t_bookmark SET parentId = ? WHERE bookmarkId in (%@);",ids];
        BOOL result = [db executeUpdate:command,parentId];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil,result);
            }
        });
    };
    
    return unit;
}

//更新名称
+ (DatabaseUnit*)updateBookMarkWithId:(NSString*)bookMarkId
                                title:(NSString*)title
                                  url:(NSString*)url
                             bookMark:(BookMarkModel *)bookMark
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* time = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        bookMark.updateTime = time;
        
        NSString* command = [NSString stringWithFormat:@"UPDATE t_bookmark SET title = ?, url = ?, updatTime = ? WHERE bookmarkId = ?;"];
        BOOL result = [db executeUpdate:command,title, url, time, bookMarkId];
        
        if(result) {
            //同步到CloudKit中
            CKRecord* record = [bookMark toDefaultCKRecord];
            record[@"title"] = bookMark.title?:@"";
            record[@"updateTime"] = time;
            [[SyncEngine shareInstance] syncRecordsToCloudKit:@[record] recordIDsToDelete:nil completion:nil];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil,result);
            }
        });
    };
    
    return unit;
}

//批量更新顺序
+ (DatabaseUnit*)updateBookMarkAllOrder:(NSArray*)allItems
{
    DatabaseUnit* unit = [DatabaseUnit new];
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        //有bug
//        [command appendString:@"UPDATE t_bookmark SET ppOrder = CASE bookmarkId \n"];
//
//        for(int i=0;i<allItems.count;i++) {
//            BookMarkModel* item = allItems[i];
//            item.ppOrder = i;
//            NSString* str = [NSString stringWithFormat:@"WHEN '%@' THEN %@ \n",item.bookmarkId,@(i)];
//            [command appendString:str];
//
//            if(i==allItems.count-1) {
//                [command appendString:@"END;"];
//            }
//        }
        NSInteger time = [[NSDate date] timeIntervalSince1970];
        BOOL result = YES;
        for(int i=0;i<allItems.count;i++) {
            //目的是在排序时，updateTime不能一样
            NSString* timeString = [NSString stringWithFormat:@"%ld", (long)(time + i)];
            BookMarkModel* item = allItems[i];
            item.ppOrder = i;
            item.updateTime = timeString;
            NSString* command = @"UPDATE t_bookmark SET ppOrder=?, updateTime=? WHERE bookmarkId=?";
            result = [db executeUpdate:command, @(item.ppOrder), timeString, item.bookmarkId] && result;
        }
        
        if(result) {
            //同步到CloudKit中
            NSMutableArray* records = [NSMutableArray array];
            for(BookMarkModel* item in allItems) {
                CKRecord* record = [item toDefaultCKRecord];
                record[@"ppOrder"] = @(item.ppOrder);
                record[@"updateTime"] = item.updateTime;
                
                [records addObject:record];
            }
            
            [[SyncEngine shareInstance] syncRecordsToCloudKit:records recordIDsToDelete:nil completion:nil];
        }
        
        if(unit.completeBlock) {
            unit.completeBlock(nil, result);
        }
    };
    
    return unit;
}

#pragma mark -- 移动相关操作
//移动相关操作
+ (DatabaseUnit*)queryBookMarkMoveNodes
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = [NSString stringWithFormat:@"SELECT * FROM t_bookmark ORDER BY ppOrder asc, ctime desc"];
        FMResultSet* set = [db executeQuery:command];
        
        NSMutableArray *array = [[NSMutableArray alloc] init];
        while([set next]) {
            BookMarkModel* item = [[BookMarkModel alloc]initWithDictionary:[set resultDictionary] error:nil];
            [array addObject:item];
        }
        
        BookMarkModel* roolModel = [self _queryBookMarkMoveNodesWithAllItems:array];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(roolModel, YES);
            }
        });
    };
    
    return unit;
}

/// 根据bookMarkId删除缓存中所有子类，包括直接子类和所有间接子类，包括它自己
+ (BookMarkModel*)_queryBookMarkMoveNodesWithAllItems:(NSMutableArray*)allItems
{
    NSString* rootId = [BookMarkModel rootId];
    //添加根节点
    BookMarkModel* item = [BookMarkModel new];
    item.bookmarkId = [BookMarkModel rootId];
    item.title = NSLocalizedString(@"bookmark.root.title", nil);
    item.fileType = BookMarkTypeFolder;
    item.level = 0;
    
    NSArray* items = [self _queryBookMarkMoveNodesWithId:rootId allItems:allItems];
    item.children = [items mutableCopy];
    
    return item;
}

+ (NSArray*)_queryBookMarkMoveNodesWithId:(NSString*)downloadId
                                 allItems:(NSMutableArray*)allItems
{
    if(downloadId.length == 0) return nil;
    NSMutableArray* result = [NSMutableArray array];
    for(BookMarkModel* item in allItems) {
        if([item.parentId isEqualToString:downloadId]) {
            if(item.fileType == BookMarkTypeFolder) {
                [result addObject:item];
                
                NSArray* tree = [self _queryBookMarkMoveNodesWithId:item.bookmarkId allItems:allItems];
                if(tree.count > 0) {
                    item.children = [tree mutableCopy];
                }
            }
        }
    }
    
    return result;
}

// 添加多个节点, 同时插入多个节点
+ (DatabaseUnit*)addBookMarkArrayWithItem:(NSArray<BookMarkModel*>*)items
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = @"INSERT INTO t_bookmark(bookmarkId, parentId, title, url, fileType, ppOrder, updateTime, ctime) VALUES (?,?,?,?,?,?,?,?)";
        
        BOOL result = YES;
        NSMutableArray* records = [NSMutableArray array];
        for(int i=0;i<items.count;i++) {
            BookMarkModel* item = items[i];
            
            //书签升序降序的逻辑
            NSString* time = nil;
            if(item.ctime.length > 0) {
                time = item.ctime;
            } else {
                time = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
            }
            
            item.ctime = time;
            item.updateTime = time;
            
            result = [db executeUpdate:command, item.bookmarkId, item.parentId, item.title, item.url, @(item.fileType), @(item.ppOrder), time, time];
            
            [records addObject:[item toCKRecord]];
        }
        
        if(result) {
            //同步到CloudKit中
            [[SyncEngine shareInstance] syncRecordsToCloudKit:records recordIDsToDelete:nil completion:nil];
        }

        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
            
            [[CommonDataManager shareInstance] reloadBookMarks];
        });
    };
    
    return unit;
}


#pragma mark -- CloudKit相关操作
// CloudKit, 添加多个
// CREATE TABLE IF NOT EXISTS t_bookmark(bookmarkId TEXT PRIMARY KEY,parentId TEXT,title TEXT, url TEXT, fileType INTEGER, ppOrder INTEGER, ctime TEXT)
+ (DatabaseUnit*)addBookMarkArray:(NSArray<BookMarkModel*>*)items
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = @"INSERT INTO t_bookmark(bookmarkId, parentId, title, url, fileType, ppOrder, updateTime, ctime) VALUES (?,?,?,?,?,?,?,?)";
        
        BOOL result = YES;
        for(int i=0;i<items.count;i++) {
            BookMarkModel* item = items[i];
            result = [db executeUpdate:command, item.bookmarkId, item.parentId?:@"", item.title?:@"", item.url?:@"", @(item.fileType), @(item.ppOrder), item.updateTime?:@"1", item.ctime];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

// CloudKit, 批量更新多个
+ (DatabaseUnit*)updateBookMarkArray:(NSArray<BookMarkModel*>*)array
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        BOOL result = NO;
        for(BookMarkModel* item in array) {
            NSString* command = @"UPDATE t_bookmark SET parentId=?, title=?, url=?, fileType=?, ppOrder=?, updateTime=?, ctime=? WHERE bookmarkId=?;";
            result = [db executeUpdate:command, item.parentId?:@"", item.title?:@"", item.url?:@"", @(item.fileType), @(item.ppOrder), item.updateTime?:@"1", item.ctime, item.bookmarkId];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

// CloudKit, 批量删除多个
+ (DatabaseUnit*)removeBookMarkArray:(NSArray*)bookMarkIds
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = [NSString stringWithFormat:@"DELETE FROM t_bookmark WHERE bookmarkId=?;"];

        BOOL result = YES;
        for(int i=0;i<bookMarkIds.count;i++) {
            NSString* bookmarkId = bookMarkIds[i];
            result = [db executeUpdate:command, bookmarkId];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

@end
