//
//  BookMarkController.h
//  PPBrowser
//
//  Created by qingbin on 2022/4/29.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "BaseViewController.h"
@class BookMarkModel;
@class Tab;

@interface BookMarkController : BaseViewController

- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)new NS_UNAVAILABLE;

- (instancetype)initWithTab:(Tab *)tab;

@property (nonatomic, copy) void (^loadRequestBlock)(NSURL* url);
@property (nonatomic, copy) void (^handleOpenInNewTabAction)(NSURL* url);
@property (nonatomic, copy) void (^handleOpenAction)(NSURL* url);
@property (nonatomic, copy) void (^handleOpenInBackendTabAction)(NSURL* url);
@property (nonatomic, copy) void (^handleCopyLinkContextMenu)(NSURL* url);

//当前所在目录
@property (nonatomic, strong) BookMarkModel *currentBookMarkItem;

@end

