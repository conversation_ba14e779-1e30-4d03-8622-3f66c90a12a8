//
//  BookMarkController.m
//  PPBrowser
//
//  Created by qingbin on 2022/4/29.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "BookMarkController.h"
#import "BookMarkCell.h"

#import "MaizyHeader.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"

#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "NSObject+Helper.h"
#import "NSString+Helper.h"
#import "UIView+FrameHelper.h"

#import "DatabaseUnit+BookMark.h"
#import "DatabaseUnit+CustomTag.h"
#import "CustomTextField.h"
#import "CustomButton.h"

#import "Tab.h"

#import "PPEnums.h"
#import "URIFixup.h"
#import "SearchManager.h"
#import "DateHelper.h"
#import "BookMarkModel.h"

#import "BookMarkMoveController.h"
#import "BaseNavigationController.h"
#import "UITableView+HintMessage.h"

#import "ThemeProtocol.h"
#import "PreferenceManager.h"
#import "PPNotifications.h"
#import "AddCustomTagController.h"

#import "BookMarkManager.h"
#import <UniformTypeIdentifiers/UTCoreTypes.h>

#import "LinkPreviewController.h"
#import "UIImage+Extension.h"
#import "BookToolbar.h"
#import "CommonDataManager.h"
#import "FavorEditController.h"
#import "UIAlertController+SafePresentation.h"

@interface BookMarkController ()<UITextFieldDelegate,UITableViewDelegate,UITableViewDataSource,UITableViewDragDelegate,UITableViewDropDelegate,UIDocumentPickerDelegate,ThemeProtocol>

@property (nonatomic, strong) UIView *searchBarView;
@property (nonatomic, strong) CustomTextField *textField;

@property (nonatomic, strong) UITableView* tableView;
@property (nonatomic, strong) NSMutableArray* model;

// iOS13才使用
@property (nonatomic, strong) UIButton* rightButton;
@property (nonatomic, strong) UIButton* addButton;

// iOS15才使用
//批量处理相关动作
@property (nonatomic, assign) BatchType batchType;
@property (nonatomic, strong) UIButton* menuButton; //导航栏
@property (nonatomic, strong) UIImageView* rightImageView; //菜单栏
@property (nonatomic, strong) BookToolbar *toolbar;
//文件排序上次选中的index
@property (nonatomic, assign) int lastSelectIndex;
// iOS15才使用

@property (nonatomic, strong) BookMarkManager* manager;
//
@property (nonatomic, weak) Tab* tab;

@end

@implementation BookMarkController

- (instancetype)initWithTab:(Tab *)tab
{
    self = [super init];
    if (self) {
        self.tab = tab;
    }
    
    return self;
}

- (void)viewDidLoad
{
    [super viewDidLoad];
    
    self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    [self commonInit];
    
    // 设置导航栏
    [self setupNavigationBar];
    
    [self addSubviews];
    [self defineLayout];
    [self setupObservers];

    [self requestBookMark];
    
    [self applyTheme];
}

- (void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:animated];
    
    [self requestBookMark];
}

- (void)dealloc
{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)commonInit
{
    FileSortType fileSort = [[PreferenceManager shareInstance].items.bookMarkSortType intValue];
    if(fileSort == FileSortTypeNameAscend || fileSort == FileSortTypeNameDescend) {
        self.lastSelectIndex = 1;
    } else if(fileSort == FileSortTypeTimeAscend || fileSort == FileSortTypeTimeDescend) {
        self.lastSelectIndex = 0;
    }
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.tableView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        
        self.searchBarView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        self.textField.backgroundColor = [UIColor colorWithHexString:kDarkThemeColorTextFiledBackgroundColor];
        self.textField.textColor = UIColor.whiteColor;
        [self.textField updatePlaceHolder:NSLocalizedString(@"bookmark.search.placeholder", nil) color:[UIColor colorWithHexString:kDarkThemeColorTextFiledContent]];
        
        self.rightImageView.tintColor = UIColor.whiteColor;
    } else {
        self.view.backgroundColor = UIColor.whiteColor;
        self.tableView.backgroundColor = UIColor.whiteColor;
        
        self.searchBarView.backgroundColor = [UIColor whiteColor];
        self.textField.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.textField.textColor = [UIColor colorWithHexString:@"#333333"];
        [self.textField updatePlaceHolder:NSLocalizedString(@"bookmark.search.placeholder", nil) color:[UIColor colorWithHexString:@"#999999"]];
        
        self.rightImageView.tintColor = [UIColor colorWithHexString:@"#333333"];
    }
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    //只有当前的controller会触发一次, 其它已存在的controller走通知
    if(needReload) {
        //修改暗黑模式
        [self applyTheme];
        [self.tableView reloadData];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}

#pragma mark -- layout
- (void)addSubviews
{
    [self.view addSubview:self.searchBarView];
    [self.searchBarView addSubview:self.textField];
    
    [self.view addSubview:self.tableView];
    [self.view addSubview:self.toolbar];
    
    //2.6.8 阿拉伯语布局适配
    [NSObject rtlLayoutSupportWithViews:@[self.textField, self.textField.leftView,
                                          self.searchBarView, self.toolbar, self.tableView]];
}

- (void)defineLayout
{
    float offset = iPadValue(30, 15);
    
    [self.searchBarView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view);
        make.left.right.equalTo(self.view);
        make.height.mas_equalTo(60);
    }];
    
    [self.textField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.searchBarView);
        make.left.equalTo(self.searchBarView).offset(offset);
        make.right.equalTo(self.searchBarView).offset(-offset);
        make.height.mas_equalTo(iPadValue(44, 36));
    }];
    
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.searchBarView.mas_bottom);
        make.left.right.equalTo(self.view);
        make.bottom.equalTo(self.toolbar.mas_top);
    }];
    
    //刚开始隐藏
    float height = [BookToolbar toolbarHeight];
    [self.toolbar mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.view);
        make.height.mas_equalTo(height);
        make.bottom.equalTo(self.view).offset([BookToolbar toolbarHeight]);
    }];
}

- (void)setupObservers
{
    @weakify(self)
    [[[[[self.textField rac_textSignal] skip:1] distinctUntilChanged] throttle:0.2]
    subscribeNext:^(id x) {
        @strongify(self)
        [self searchFromDatabase];
    }];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(requestBookMark)
                                                 name:kBookMarkDidChangeNotification
                                               object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserverForName:kReloadBookMarkNotification object:nil queue:nil usingBlock:^(NSNotification * _Nonnull notification) {
        @strongify(self)
        [self _reloadData];
    }];
    
    if (@available(iOS 15.0, *)) {
        [self.toolbar setSelectAllAction:^(BOOL isSelectedAll) {
            @strongify(self)
            [self.toolbar updateSelectAll:isSelectedAll];
            
            if(isSelectedAll) {
                //全选
                for(BookMarkModel* item in self.model) {
                    item.isSelected = YES;
                }
                
                [self.toolbar updateButtonEnabled:YES];
            } else {
                //取消全选
                for(BookMarkModel* item in self.model) {
                    item.isSelected = NO;
                }
                
                [self.toolbar updateButtonEnabled:NO];
            }
             
            [self.tableView reloadData];
        }];
        
        [self.toolbar setConfirmAction:^{
            @strongify(self)
            [self handleBatchOperation];
        }];
    }
}

#pragma mark -- UITableViewDataSource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return self.model.count;
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    BookMarkModel* model = self.model[indexPath.row];
    BookMarkCell* cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass(BookMarkCell.class)];
    [cell updateWithModel:model batchOperation:self.batchType];
    
    @weakify(self)
    [cell setDidTapAction:^(BookMarkModel *model) {
        @strongify(self)
        if(self.batchType == BatchTypeDefault) {
            //默认
            [self selectItem:model];
        } else {
            //批量操作
            model.isSelected = !model.isSelected;
            
            for(BookMarkModel* obj in self.model) {
                if(obj.isSelected) {
                    [self.toolbar updateButtonEnabled:YES];
                    break;
                }
            }
            
            [self.tableView reloadData];
        }
    }];
    
    [cell setDidMoreBtnTapAction:^(BookMarkModel *model) {
        @strongify(self)
        [self showMoreMenuForBookMark:model atIndexPath:indexPath];
    }];
    
    return cell;
}

- (BOOL)tableView:(UITableView *)tableView canEditRowAtIndexPath:(NSIndexPath *)indexPath
{
    return YES;
}

// 只适用于ios11 以及以后版本
- (UISwipeActionsConfiguration *)tableView:(UITableView *)tableView trailingSwipeActionsConfigurationForRowAtIndexPath:(NSIndexPath *)indexPath  API_AVAILABLE(ios(11.0))
{
    // 删除数据
    @weakify(self)
    UIContextualAction *deleteAction = [UIContextualAction contextualActionWithStyle:UIContextualActionStyleDestructive title:NSLocalizedString(@"bookmark.delete", nil) handler:^(UIContextualAction * _Nonnull action, __kindof UIView * _Nonnull sourceView, void (^ _Nonnull completionHandler)(BOOL)) {
        @strongify(self)
        BookMarkModel* item = self.model[indexPath.row];
    
        DatabaseUnit* unit = [DatabaseUnit removeBookMarkTreeWithIds:@[item.bookmarkId]];
        @weakify(self)
        unit.completeBlock = ^(id result, BOOL success) {
            @strongify(self)
            @weakify(self)
            dispatch_async(dispatch_get_main_queue(), ^{
                @strongify(self)
                if(success) {
                    [self.model removeObject:item];
                    [tableView reloadSections:[NSIndexSet indexSetWithIndex:0] withRowAnimation:UITableViewRowAnimationNone];
                    
                    [self showOrHideHintMessage];
                }

                completionHandler(YES);
            });
        };

        DB_EXEC(unit);
        
        [CommonDataManager shareInstance].currentBookMarkModel = nil;
        [[CommonDataManager shareInstance] reloadBookMarks];
    }];
    [deleteAction setBackgroundColor:UIColor.redColor];
        
    // 添加
    UISwipeActionsConfiguration *actions = [UISwipeActionsConfiguration configurationWithActions:@[deleteAction]];
    actions.performsFirstActionWithFullSwipe = NO;
    return actions;
}

#pragma mark -- Getters
- (UIView *)searchBarView
{
    if (!_searchBarView) {
        _searchBarView = [UIView new];
        _searchBarView.backgroundColor = [UIColor whiteColor];
    }
    return _searchBarView;
}

- (CustomTextField *)textField
{
    if(!_textField) {
        _textField = [[CustomTextField alloc] init];
        //2.6.8 阿拉伯语布局适配
        _textField.textAlignment = NSTextAlignmentLeft;
        
        _textField.font = [UIFont systemFontOfSize:iPadValue(18, 14)];
        _textField.layer.cornerRadius = iPadValue(10, 8);
        _textField.layer.masksToBounds = YES;
        
        _textField.leftViewMode = UITextFieldViewModeAlways;
        _textField.textColor = [UIColor colorWithHexString:@"#333333"];
        _textField.returnKeyType = UIReturnKeySearch;

        [_textField setBackgroundColor:[UIColor colorWithHexString:@"#f2f2f7"]];
        [_textField updatePlaceHolder:NSLocalizedString(@"bookmark.search.placeholder", nil) color:[UIColor colorWithHexString:@"#999999"]];
        [_textField setClearButtonMode:UITextFieldViewModeAlways];
        
        [_textField setDelegate:self];
        
        // 创建搜索图标
        float height = iPadValue(44, 36);
        float searchIconHeight = iPadValue(24, 20);
        float y = (height - searchIconHeight) / 2.0;
        CGRect rect = CGRectMake(0, 0, height, height);
        UIView *leftView = [[UIView alloc] initWithFrame:rect];
        UIImageView *searchIcon = [[UIImageView alloc] initWithFrame:CGRectMake(iPadValue(15, 10), y, searchIconHeight, searchIconHeight)];
        searchIcon.image = [UIImage systemImageNamed:@"magnifyingglass"];
        searchIcon.tintColor = [UIColor colorWithHexString:@"#999999"];
        searchIcon.contentMode = UIViewContentModeScaleAspectFit;
        [leftView addSubview:searchIcon];
        
        _textField.leftView = leftView;
        _textField.leftViewRect = rect;
    }
    
    return _textField;
}

- (UITableView *)tableView
{
    if(!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
        
        _tableView.separatorStyle  = UITableViewCellSeparatorStyleNone;
        _tableView.showsHorizontalScrollIndicator = NO;
        _tableView.showsVerticalScrollIndicator   = NO;
        _tableView.delegate   = self;
        _tableView.dataSource = self;
        _tableView.rowHeight = UITableViewAutomaticDimension;
        _tableView.estimatedRowHeight = 80;
        _tableView.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        
        [_tableView registerClass:[BookMarkCell class] forCellReuseIdentifier:NSStringFromClass([BookMarkCell class])];
        
        float height = iPadValue(10, 10);
        UIView* header = [[UIView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, height)];
        header.backgroundColor = UIColor.clearColor;
        _tableView.tableHeaderView = header;
        
        _tableView.tableFooterView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, height)];
        
        _tableView.sectionFooterHeight = 0.0;
        _tableView.sectionHeaderHeight = 0.0f;
        
        _tableView.dragInteractionEnabled = YES;
        _tableView.dragDelegate = self;
        _tableView.dropDelegate = self;
    }
    
    return _tableView;
}

- (NSMutableArray *)model
{
    if(!_model) {
        _model = [NSMutableArray array];
    }
    
    return _model;
}

#pragma mark -- 设置导航栏
- (void)setupNavigationBar
{
    self.title = NSLocalizedString(@"preference.bookMark", nil);
    [self createCustomLeftBarButtonItem];
    
    if (@available(iOS 15.0, *)) {
        [self setupNavigationBarForiOS15];
    } else {
        [self setupNavigationBarForiOS13];
    }
}

- (void)setupNavigationBarForiOS13
{
    //iOS 15.0以下样式
    // 添加按钮
    self.addButton = [UIButton buttonWithType:UIButtonTypeSystem];
    [self.addButton setImage:[[UIImage systemImageNamed:@"plus"] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate] forState:UIControlStateNormal];
    self.addButton.tintColor = [UIColor colorWithHexString:@"#333333"];
    [self.addButton addTarget:self action:@selector(setupNewBookMark) forControlEvents:UIControlEventTouchUpInside];
    
    FileSortType sort = [[PreferenceManager shareInstance].items.bookMarkSortType intValue];
    NSString* imageName = @"";
    if(sort == FileSortTypeTimeDescend) {
        imageName = @"arrow.down";
    } else {
        imageName = @"arrow.up";
    }
    
    // 排序按钮
    self.rightButton = [UIButton buttonWithType:UIButtonTypeSystem];
    [self.rightButton setImage:[[UIImage systemImageNamed:imageName] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate] forState:UIControlStateNormal];
    self.rightButton.tintColor = [UIColor colorWithHexString:@"#333333"];
    [self.rightButton addTarget:self action:@selector(rightBarbuttonClick) forControlEvents:UIControlEventTouchUpInside];
    
    UIBarButtonItem *fixedSpace = [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemFixedSpace target:nil action:nil];
    fixedSpace.width = 16; // 调整这个值以增大间距
    
    UIBarButtonItem *rightFixedSpace = [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemFixedSpace target:nil action:nil];
    rightFixedSpace.width = 16; // 调整这个值以增大间距
    
    self.navigationItem.rightBarButtonItems = @[
        rightFixedSpace,
        [[UIBarButtonItem alloc] initWithCustomView:self.addButton],
        fixedSpace,
        [[UIBarButtonItem alloc] initWithCustomView:self.rightButton]
    ];
}

- (void)setupNavigationBarForiOS15 API_AVAILABLE(ios(15.0))
{
    //iOS 15.0以上样式
    self.batchType = BatchTypeDefault;
    
    [self _createCustomRightBarButtonItem];
}

#pragma mark -- 右键功能按钮
- (void)_createCustomRightBarButtonItem API_AVAILABLE(ios(15.0))
{
    if(self.batchType == BatchTypeDefault) {
        //默认
        UIButton* menuButton = [self _createRightButtonWithImageName:@"ellipsis.circle"];
            
        UIBarButtonItem* menuBarButtonItem = [[UIBarButtonItem alloc] initWithCustomView:menuButton];
        
        UIBarButtonItem *spacer = [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemFixedSpace target:nil action:nil];
        spacer.width = -16.0f; // for example shift right bar button to the right

        menuButton.menu = [self menuForMenuBarButton];
        menuButton.showsMenuAsPrimaryAction = YES;
        self.menuButton = menuButton;
        
        self.navigationItem.rightBarButtonItems = @[spacer, menuBarButtonItem];
    } else if(self.batchType == BatchTypeMove
              || self.batchType == BatchTypeDelete
              || self.batchType == BatchTypeShare) {
        //批量操作
        //变成取消
        UIButton* menuButton = [self _createCancelButton];
        UIBarButtonItem* menuBarButtonItem = [[UIBarButtonItem alloc] initWithCustomView:menuButton];

        self.navigationItem.rightBarButtonItems = @[menuBarButtonItem];
    }
}

- (UIButton *)_createCancelButton API_AVAILABLE(ios(15.0))
{
    UIButton* cancelButton = [[UIButton alloc] initWithFrame:CGRectMake(0.0, 0.0f, 44.0f, 44.0)];
    [cancelButton setBackgroundColor:[UIColor clearColor]];

    UIImageView* imageView = [UIImageView new];
    
    //checkmark.circle
    UIImage* image = [UIImage imageNamed:@"common_close2_icon"];
    imageView.image = image;
    if([ThemeProtocol isDarkTheme]) {
        imageView.tintColor = [UIColor colorWithHexString:@"ffffff"];
    } else {
        imageView.tintColor = [UIColor colorWithHexString:@"333333"];
    }
    
    [cancelButton addSubview:imageView];
    [imageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(cancelButton);
        make.size.mas_equalTo(18);
    }];
    
    @weakify(self)
    [[cancelButton rac_signalForControlEvents:UIControlEventTouchUpInside]
     subscribeNext:^(id x) {
        @strongify(self)
        [self exitBatchOperation];
    }];
    
    return cancelButton;
}

- (void)handleBatchOperation API_AVAILABLE(ios(15.0))
{
    //完成批量操作
    NSMutableArray* array = [NSMutableArray array];
    NSMutableArray* ids = [NSMutableArray array];
    for(BookMarkModel* item in self.model) {
        if(item.isSelected) {
            [array addObject:item];
            [ids addObject:item.bookmarkId];
        }
    }
    
    if(array.count == 0) {
        [self exitBatchOperation];
        [self.tableView reloadData];
        
        return;
    }
    
    if(self.batchType == BatchTypeMove) {
        //移动
        BookMarkMoveController* vc = [BookMarkMoveController new];
    //    vc.currentBookMarkItem = model;
        vc.items = array;
        
        @weakify(self)
        [vc setFinishBlock:^(BookMarkModel *selectTarget){
            @strongify(self)
            //更新新的目录
            DatabaseUnit* unit = [DatabaseUnit updateBookMarkListWithIds:ids toparent:selectTarget.bookmarkId];
            @weakify(self)
            [unit setCompleteBlock:^(id result, BOOL success) {
                @strongify(self)
                if(success) {
                    [self requestBookMark];
                }
            }];
            DB_EXEC(unit);
            
            [self exitBatchOperation];
        }];
        
        [self presentViewController:vc animated:YES completion:nil];
        
    } else if(self.batchType == BatchTypeDelete) {
        //删除
        [self removeBookMarksWithAlert:ids];
    } else if(self.batchType == BatchTypeShare) {
        //分享
        @weakify(self)
        //不要复用，因为里面有些状态是不能重复使用的
        self.manager = [BookMarkManager new];
        
        [self.manager exportBookMarksWithArray:array completion:^{
            @strongify(self)
            [self exitBatchOperation];
        }];
    }
}

- (void)removeBookMarksWithAlert:(NSArray *)array API_AVAILABLE(ios(15.0))
{
    UIAlertController* alertController = [UIAlertController alertControllerWithTitle:NSLocalizedString(@"bookmark.deleteFileTips", nil) message:@"" preferredStyle:UIAlertControllerStyleAlert];
    UIAlertAction* action = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.cancel", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [alertController dismissViewControllerAnimated:YES completion:nil];
    }];
    [alertController addAction:action];

    @weakify(self)
    action = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.confirm", nil) style:UIAlertActionStyleDestructive handler:^(UIAlertAction * _Nonnull action) {
        @strongify(self)
        @weakify(self)
        DatabaseUnit* unit = [DatabaseUnit removeBookMarkTreeWithIds:array];
        [unit setCompleteBlock:^(id result, BOOL success) {
            @strongify(self)
            if(success) {
                [self requestBookMark];
            }
        }];
        
        DB_EXEC(unit);
        
        [self exitBatchOperation];
    }];
    [alertController addAction:action];
    
//    [self presentViewController:alertController animated:YES completion:nil];
    //v2.6.8, 统一present方法，防止崩溃
    [alertController presentSafelyFromViewController:self];
}


- (UIButton *)_createRightButtonWithImageName:(NSString *)imageName API_AVAILABLE(ios(15.0))
{
    UIButton* rightButton = [[UIButton alloc] initWithFrame:CGRectMake(0.0, 0.0f, 44.0f, 44.0)];
    [rightButton setBackgroundColor:[UIColor clearColor]];

    UIImageView* imageView = [UIImageView new];
    
    //checkmark.circle
    UIImage* image = [UIImage ext_systemImageNamed:imageName
                                         pointSize:22
                                        renderMode:UIImageRenderingModeAlwaysTemplate];
    imageView.image = image;
    imageView.tintColor = [UIColor colorWithHexString:@"333333"];
    self.rightImageView = imageView;
    
    [rightButton addSubview:imageView];
    [imageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(rightButton);
//        make.size.mas_equalTo(20);
    }];
    
    return rightButton;
}

- (UIMenu *)menuForMenuBarButton API_AVAILABLE(ios(15.0))
{
    NSMutableArray* actions = [NSMutableArray array];
    NSArray* sorts = @[NSLocalizedString(@"bookmark.time", nil), NSLocalizedString(@"bookmark.name", nil)];
    FileSortType filesSortType = [[PreferenceManager shareInstance].items.bookMarkSortType intValue];
    UIImage* actionImage;
    if(filesSortType == FileSortTypeNameAscend
       || filesSortType == FileSortTypeTimeAscend) {
        actionImage = [UIImage systemImageNamed:@"chevron.up"];
    } else {
        actionImage = [UIImage systemImageNamed:@"chevron.down"];
    }
    
    @weakify(self)
    for(int i=0;i<sorts.count;i++) {
        NSString* item = sorts[i];
        
        UIImage* image = nil;
        if(i == self.lastSelectIndex) {
            image = actionImage;
        }
        
        UIAction* action = [UIAction actionWithTitle:item image:image identifier:nil handler:^(__kindof UIAction * _Nonnull action) {
            @strongify(self)
            [self handleFileSortAction:i];
        }];
        
        if(i == self.lastSelectIndex) {
            action.state = UIMenuElementStateOn;
        } else {
            action.state = UIMenuElementStateOff;
        }
        
        [actions addObject:action];
    }
    UIMenu* sortMenu = [UIMenu menuWithTitle:@"" image:nil identifier:nil options:UIMenuOptionsDisplayInline children:actions];
    
    //导入导出
    UIAction* inputAction = [UIAction actionWithTitle:NSLocalizedString(@"bookmark.import", nil) image:nil identifier:nil handler:^(__kindof UIAction * _Nonnull action) {
        @strongify(self)
        [self importBookmarks];
    }];
    UIAction* outputAction = [UIAction actionWithTitle:NSLocalizedString(@"bookmark.export", nil) image:nil identifier:nil handler:^(__kindof UIAction * _Nonnull action) {
        @strongify(self)
        [UIView showLoading:NSLocalizedString(@"tips.handling", nil)];
        //不要复用，因为里面有些状态是不能重复使用的
        self.manager = [BookMarkManager new];
        [self.manager exportBookMarks];
    }];
    UIMenu* listMenu = [UIMenu menuWithTitle:@"" image:nil identifier:nil options:UIMenuOptionsDisplayInline children:@[
        inputAction,
        outputAction,
    ]];
    
    UIAction* moveAction = [UIAction actionWithTitle:NSLocalizedString(@"bookmark.move", nil) image:nil identifier:nil handler:^(__kindof UIAction * _Nonnull action) {
        @strongify(self)
        self.batchType = BatchTypeMove;
        [self enterBatchOperation];
    }];
    moveAction.state = UIMenuElementStateOff;
    
    UIAction* shareAction = [UIAction actionWithTitle:NSLocalizedString(@"preference.share", nil) image:nil identifier:nil handler:^(__kindof UIAction * _Nonnull action) {
        @strongify(self)
        self.batchType = BatchTypeShare;
        [self enterBatchOperation];
    }];
    shareAction.state = UIMenuElementStateOff;
    
    UIAction* deleteAction = [UIAction actionWithTitle:NSLocalizedString(@"common.delete", nil) image:nil identifier:nil handler:^(__kindof UIAction * _Nonnull action) {
        @strongify(self)
        self.batchType = BatchTypeDelete;
        [self enterBatchOperation];
    }];
    deleteAction.attributes = UIMenuElementAttributesDestructive;
    deleteAction.state = UIMenuElementStateOff;
    
    UIMenu* batchMenu = [UIMenu menuWithTitle:NSLocalizedString(@"bookmark.batch.operation", nil) image:nil identifier:nil options:UIMenuOptionsSingleSelection children:@[
        moveAction,
        shareAction,
        deleteAction,
    ]];

    //新建文件夹
    UIMenu* textReplaceMenu = [UIMenu menuWithTitle:@"" image:nil identifier:nil options:UIMenuOptionsDisplayInline children:@[
        [UIAction actionWithTitle:NSLocalizedString(@"bookmark.newFolder", nil) image:nil identifier:nil handler:^(__kindof UIAction * _Nonnull action) {
            @strongify(self)
            [self showCreateFolderAlert];
        }],
    ]];

    UIMenu* menu = [UIMenu menuWithTitle:@"" image:nil identifier:nil options:UIMenuOptionsDisplayInline children:@[
        sortMenu,
        listMenu,
        batchMenu,
        textReplaceMenu,
    ]];
    
    return menu;
}

#pragma mark -- 处理文件排序逻辑
- (void)handleFileSortAction:(int)selectIndex API_AVAILABLE(ios(15.0))
{
    //按照排序重排数组
    FileSortType filesSortType = [[PreferenceManager shareInstance].items.bookMarkSortType intValue];
    if(selectIndex == self.lastSelectIndex) {
        //升序倒序
        if(filesSortType == FileSortTypeNameAscend) {
            filesSortType = FileSortTypeNameDescend;
        } else if(filesSortType == FileSortTypeNameDescend) {
            filesSortType = FileSortTypeNameAscend;
        } else if(filesSortType == FileSortTypeTimeAscend) {
            filesSortType = FileSortTypeTimeDescend;
        } else if(filesSortType == FileSortTypeTimeDescend) {
            filesSortType = FileSortTypeTimeAscend;
        }
    } else {
        if(selectIndex == 0) {
            filesSortType = FileSortTypeTimeAscend;
        } else if(selectIndex == 1) {
            filesSortType = FileSortTypeNameAscend;
        }
    }
    
    [PreferenceManager shareInstance].items.bookMarkSortType = @(filesSortType);
    [[PreferenceManager shareInstance] encode];
    
    self.lastSelectIndex = selectIndex;
    
    //重置菜单栏
    self.menuButton.menu = [self menuForMenuBarButton];
    
    [[NSNotificationCenter defaultCenter] postNotificationName:kReloadBookMarkNotification object:nil];
    
    //刷新列表
    [self requestBookMark];
}

#pragma mark -- 批量操作
- (void)enterBatchOperation API_AVAILABLE(ios(15.0))
{
    //清除已选状态
    for(BookMarkModel* obj in self.model) {
        obj.isSelected = NO;
    }
    
    [self.toolbar updateButtonEnabled:NO];
    
    [self _createCustomRightBarButtonItem];
    
    if(self.batchType == BatchTypeMove) {
        [self.toolbar updateWithButtonTitle:NSLocalizedString(@"bookmark.move", nil)];
    } else if(self.batchType == BatchTypeDelete) {
        [self.toolbar updateWithButtonTitle:NSLocalizedString(@"bookmark.delete", nil)];
    } else if(self.batchType == BatchTypeShare) {
        [self.toolbar updateWithButtonTitle:NSLocalizedString(@"preference.share", nil)];
    }
    
    //显示底部工具栏
    self.toolbar.hidden = NO;
    [self.toolbar mas_updateConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.view).offset(0);
    }];
    [UIView animateWithDuration:0.25 animations:^{
        [self.view layoutIfNeeded];
    }];
    
    //刷新列表
    [self.tableView reloadData];
}

- (void)exitBatchOperation API_AVAILABLE(ios(15.0))
{
    self.batchType = BatchTypeDefault;
    
    //隐藏底部工具栏
    [self.toolbar mas_updateConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.view).offset([BookToolbar toolbarHeight]);
    }];
    [UIView animateWithDuration:0.25 animations:^{
        [self.view layoutIfNeeded];
    } completion:^(BOOL finished) {
        self.toolbar.hidden = YES;
    }];
    
    [self _createCustomRightBarButtonItem];
    
    //清除已选状态
    for(BookMarkModel* obj in self.model) {
        obj.isSelected = NO;
    }
    
    //刷新列表
    [self.tableView reloadData];
}

#pragma mark -- 获取历史表数据库联想词
- (void)searchFromDatabase
{
    NSString* text = [self.textField.text stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
    if(text.length == 0
       || [text stringByReplacingOccurrencesOfString:@" " withString:@""].length == 0)
    {
        [self requestBookMark];
        return;
    }
    
    //过滤项
    NSPredicate *predicate = [NSPredicate predicateWithFormat:@"title CONTAINS[cd] %@ OR url CONTAINS[cd] %@", text, text];
    
    DatabaseUnit* unit = [DatabaseUnit queryAllBookMarks];
    @weakify(self)
    [unit setCompleteBlock:^(NSArray<BookMarkModel *>* result, BOOL success) {
        @strongify(self)
        if(!self) return;
        if(success) {
            [self.model removeAllObjects];
            
            NSArray* filtered = [result filteredArrayUsingPredicate:predicate];
            self.model = [NSMutableArray arrayWithArray:filtered];
            
            [self updateCellStyle];
            
            [self.tableView reloadData];
        }
    }];
    
    DB_EXEC(unit);
}

#pragma mark -- 获取根节点书签记录
- (void)requestBookMark
{
    [self endEditting];
    [self.tableView hideHintMessage];
    
    @weakify(self)
    NSString* parentId;
    if(!self.currentBookMarkItem) {
        //根节点
        parentId = [BookMarkModel rootId];
        DatabaseUnit* unit = [DatabaseUnit queryBookMarkWithId:parentId];
        [unit setCompleteBlock:^(BookMarkModel* result, BOOL success) {
            @strongify(self)
            if(!self) return;
            if(success) {
                self.currentBookMarkItem = result;
            }
        }];
        
        DB_EXEC(unit);
    } else {
        parentId = self.currentBookMarkItem.bookmarkId;
    }
    
    //规则是ppOrder小的排前面
    //如果ppOrder相等, 那么时间大的排在前面(即新加入的书签在前面)
    DatabaseUnit* unit = [DatabaseUnit queryBookMarkListWithId:parentId];
    [unit setCompleteBlock:^(NSArray* result, BOOL success) {
        @strongify(self)
        if(!self) return;
        if(success) {
            [self.model removeAllObjects];
            
            [self.model addObjectsFromArray:result];
            
            [self _reloadData];
        }
    }];
    
    DB_EXEC(unit);
}

- (void)_reloadData
{
    //按照排序重排数组
//    FileSortType sort = [[PreferenceManager shareInstance].items.bookMarkSortType intValue];
//    if(sort == BookMarkSortTypeDescend) {
//        //降序
//        self.model = [[self.model sortedArrayUsingComparator:^NSComparisonResult(BookMarkModel*  _Nonnull obj1, BookMarkModel*  _Nonnull obj2) {
//            return obj1.ctime.intValue < obj2.ctime.intValue;
//        }] mutableCopy];
//    } else if(sort == BookMarkSortTypeAscend) {
//        //升序
//        //不能使用updateTime，iCloud同步的时候，会导致它们的updateTime都是一样的
//        self.model = [[self.model sortedArrayUsingComparator:^NSComparisonResult(BookMarkModel*  _Nonnull obj1, BookMarkModel*  _Nonnull obj2) {
//            return obj1.ctime.intValue > obj2.ctime.intValue;
//        }] mutableCopy];
//    }
    
    [self _sortFiles];
    
    [self updateCellStyle];
    [self showOrHideHintMessage];
    
    [self.tableView reloadData];
}

- (void)_sortFiles
{
    FileSortType fileSortType = [[PreferenceManager shareInstance].items.bookMarkSortType intValue];

    // 进行排序
    NSArray *sortedFileItems = [self.model sortedArrayUsingComparator:^NSComparisonResult(BookMarkModel*  _Nonnull obj1, BookMarkModel*  _Nonnull obj2) {
        if(fileSortType == FileSortTypeNameAscend) {
            NSString *firstLetter1 = [self getFirstChar:obj1.title];
            NSString *firstLetter2 = [self getFirstChar:obj2.title];
            
            return [firstLetter1 compare:firstLetter2];
        } else if(fileSortType == FileSortTypeNameDescend) {
            NSString *firstLetter1 = [self getFirstChar:obj1.title];
            NSString *firstLetter2 = [self getFirstChar:obj2.title];
            
            return [firstLetter2 compare:firstLetter1];
        } else if(fileSortType == FileSortTypeTimeAscend) {
            return [obj1.ctime compare:obj2.ctime];
        } else if(fileSortType == FileSortTypeTimeDescend) {
            return [obj2.ctime compare:obj1.ctime];
        } else {
            //默认, 时间降序
            return [obj2.ctime compare:obj1.ctime];
        }
    }];

    [self.model removeAllObjects];
    [self.model addObjectsFromArray:sortedFileItems];
}

- (NSString *)getFirstChar:(NSString *)text
{
    if (text.length == 0) return @"";
    return [text substringToIndex:1];;
}

#pragma mark -- 无数据提醒
- (void)showOrHideHintMessage
{
    if(self.model.count > 0) {
        [self.tableView hideHintMessage];
    } else {
        UIImage* image = [UIImage imageNamed:@"empty_data_logo"];
        [self.tableView showHintMessage:NSLocalizedString(@"tableview.emptyTips", nil)
                                  image:image
                          sectionMargin:iPadValue(150, 100)];
    }
}

#pragma mark -- 更新圆角信息
- (void)updateCellStyle
{
    for(BookMarkModel* item in self.model) {
        item.isFirstInSection = NO;
        item.isLastInSection = NO;
    }
    BookMarkModel* item = self.model.firstObject;
    item.isFirstInSection = YES;
    
    item = self.model.lastObject;
    item.isLastInSection = YES;
}

#pragma mark -- UITextFieldDelegate
- (BOOL)textFieldShouldReturn:(UITextField *)textField
{
    //点击搜索
    return YES;
}

- (BOOL)textFieldShouldClear:(UITextField *)textField
{
    return YES;
}

#pragma mark -- 导航栏
- (void)rightBarbuttonClick
{
    //升序/降序
    //按照排序重排数组
    FileSortType sort = [[PreferenceManager shareInstance].items.bookMarkSortType intValue];
    
    if(sort == FileSortTypeTimeDescend) {
        sort = FileSortTypeTimeAscend;
    } else {
        sort = FileSortTypeTimeDescend;
    }
    
    NSString* imageName = @"";
    if(sort == FileSortTypeTimeDescend) {
        imageName = @"arrow.down";
    } else {
        imageName = @"arrow.up";
    }
    [self.rightButton setImage:[[UIImage systemImageNamed:imageName] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate] forState:UIControlStateNormal];
    
    [PreferenceManager shareInstance].items.bookMarkSortType = @(sort);
    [[PreferenceManager shareInstance] encode];
    
    [[NSNotificationCenter defaultCenter] postNotificationName:kReloadBookMarkNotification object:nil];
}

#pragma mark -- UITableViewDragDelegate

- (NSArray<UIDragItem *> *)tableView:(UITableView *)tableView itemsForBeginningDragSession:(id<UIDragSession>)session atIndexPath:(NSIndexPath *)indexPath
{
    // 小震动
    UIImpactFeedbackGenerator* feedback = [[UIImpactFeedbackGenerator alloc]initWithStyle:UIImpactFeedbackStyleMedium];
    [feedback prepare];
    [feedback impactOccurred];

    BookMarkModel* item = self.model[indexPath.row];

    UIDragItem* dragItem = [[UIDragItem alloc] initWithItemProvider:[NSItemProvider new]];
    dragItem.localObject = item;

    return @[dragItem];
}

#pragma mark -- UITableViewDropDelegate

- (UITableViewDropProposal *)tableView:(UITableView *)tableView dropSessionDidUpdate:(id<UIDropSession>)session withDestinationIndexPath:(nullable NSIndexPath *)destinationIndexPath
{
    return [[UITableViewDropProposal alloc]initWithDropOperation:UIDropOperationMove intent:UITableViewDropIntentInsertAtDestinationIndexPath];
}

- (void)tableView:(UITableView *)tableView performDropWithCoordinator:(id<UITableViewDropCoordinator>)coordinator
{
    UIDragItem* dragItem = coordinator.items.firstObject.dragItem;
    BookMarkModel* item = dragItem.localObject;

    NSIndexPath* targetIndexPath = coordinator.destinationIndexPath;

    //最后一个item判断
    if(targetIndexPath.row >= self.model.count) {
        //get the last IndexPath
        //跨区间 section 拖放操作, 拖出结尾的时候，coordinator.destinationIndexPath是没有的。
        //如果没有coordinator.destinationIndexPath那么强行指定最后一格
        targetIndexPath = [NSIndexPath indexPathForRow:self.model.count-1 inSection:0];
    }

    @weakify(self)
    [tableView performBatchUpdates:^{
        @strongify(self)
        id<UITableViewDropItem> dropItem = coordinator.items.firstObject;
        NSIndexPath* sourceIndexPath = dropItem.sourceIndexPath;

        if(sourceIndexPath.row < targetIndexPath.row) {
            [self.model insertObject:item atIndex:targetIndexPath.row+1];
            [self.model removeObjectAtIndex:sourceIndexPath.row];
        } else {
            [self.model removeObject:item];
            NSInteger index = targetIndexPath.row;
            [self.model insertObject:item atIndex:index];
        }

        [self updateCellStyle];
        
        [tableView reloadData];
    } completion:^(BOOL finished) {
        //更新order
        NSMutableArray* items = [NSMutableArray array];
        for(int i=0;i<self.model.count;i++) {
            BookMarkModel* item = self.model[i];
            item.ppOrder = i;

            [items addObject:item];
        }

        DatabaseUnit* unit = [DatabaseUnit updateBookMarkAllOrder:items];
        DB_EXEC(unit);
    }];
}

//长按预览窗口
- (UIContextMenuConfiguration *)tableView:(UITableView *)tableView contextMenuConfigurationForRowAtIndexPath:(NSIndexPath *)indexPath point:(CGPoint)point
{
    BookMarkModel* item = self.model[indexPath.row];
    if(item.fileType == BookMarkTypeFolder) return nil;
    NSURL* URL = [NSURL URLWithString:item.url];
    
    UIContextMenuConfiguration *configuration = [UIContextMenuConfiguration configurationWithIdentifier:nil previewProvider:^UIViewController * _Nullable{
        BOOL enabledPreview = [[PreferenceManager shareInstance].items.enabledPreview boolValue];
        if(!enabledPreview) return nil;
        
        // 根据需要更新预览窗口的内容
        LinkPreviewController* vc = [[LinkPreviewController alloc]initWithURL:URL];
        return vc;
    } actionProvider:^UIMenu * _Nullable(NSArray<UIMenuElement *> * _Nonnull suggestedActions) {
        NSMutableArray* actions = [NSMutableArray array];
        // 创建和返回上下文菜单的操作项
        UIAction* openInNewTabAction = [UIAction actionWithTitle:NSLocalizedString(@"contextmenu.openInNewTab", nil) image:nil identifier:nil handler:^(__kindof UIAction * _Nonnull action) {
            if(self.handleOpenInNewTabAction) {
                self.handleOpenInNewTabAction(URL);
            }
        }];
        [actions addObject:openInNewTabAction];

        UIAction* openAction = [UIAction actionWithTitle:NSLocalizedString(@"contextmenu.openDirectly", nil) image:nil identifier:nil handler:^(__kindof UIAction * _Nonnull action) {
            if(self.handleOpenAction) {
                self.handleOpenAction(URL);
            }
            
            if([BrowserUtils isiPad]) {
                //iPad
                [self dismissViewControllerAnimated:YES completion:nil];
            } else {
                //iPhone
                if(self.navigationController.viewControllers.count > 1) {
                    [self.navigationController popToRootViewControllerAnimated:YES];
                } else {
                    [self dismissViewControllerAnimated:YES completion:nil];
                }
            }
        }];
        [actions addObject:openAction];
        
        UIAction* openInBackgroundAction = [UIAction actionWithTitle:NSLocalizedString(@"contextmenu.openInBackground", nil) image:nil identifier:nil handler:^(__kindof UIAction * _Nonnull action) {
            if(self.handleOpenInBackendTabAction) {
                self.handleOpenInBackendTabAction(URL);
            }
        }];
        [actions addObject:openInBackgroundAction];
        
        UIAction* openInSafariAction = [UIAction actionWithTitle:NSLocalizedString(@"contextmenu.openInSafari", nil) image:nil identifier:nil handler:^(__kindof UIAction * _Nonnull action) {
            if([[UIApplication sharedApplication] canOpenURL:URL]) {
                [[UIApplication sharedApplication] openURL:URL options:@{} completionHandler:nil];
            }
        }];
        [actions addObject:openInSafariAction];

        UIAction* copyAction = [UIAction actionWithTitle:NSLocalizedString(@"contextmenu.copyLink", nil) image:nil identifier:nil handler:^(__kindof UIAction * _Nonnull action) {
            if(self.handleCopyLinkContextMenu) {
                self.handleCopyLinkContextMenu(URL);
            }
        }];
        [actions addObject:copyAction];
        
        //添加到首页
        if (![self hasAddToCustomTag:item]) {
            UIAction* addHomeAction = [UIAction actionWithTitle:NSLocalizedString(@"addCustomTag.title", nil) image:nil identifier:nil handler:^(__kindof UIAction * _Nonnull action) {
                [self _handleAddBookMarkToCustomTag:item];
            }];
            [actions addObject:addHomeAction];
        }
        
        return [UIMenu menuWithTitle:@"" children:actions];
    }];
    
    return configuration;
}

- (BOOL)hasAddToCustomTag:(BookMarkModel *)bookMarkModel
{
    NSArray* customTagModels = [CommonDataManager shareInstance].customTagModels;
    for(CustomTagModel* item in customTagModels) {
        if([item.targetUrl isEqualToString:bookMarkModel.url]) {
            return true;
        }
    }
    
    return false;
}

- (void)_handleAddBookMarkToCustomTag:(BookMarkModel *)bookMarkModel
{
    //添加到首页
//    CustomTagModel* favorModel = [CustomTagModel new];
//    favorModel.title = bookMarkModel.title;
//    favorModel.targetUrl = bookMarkModel.url;
//    favorModel.type = CustomTagTypeWeb;
//    favorModel.iconUrl = bookMarkModel.iconUrl;
//    
//    NSInteger maxOrder = 0;
//    NSArray* customTagModels = [CommonDataManager shareInstance].customTagModels;
//    for(CustomTagModel* item in customTagModels) {
//        maxOrder = MAX(maxOrder, item.ppOrder);
//    }
//    maxOrder += 1;
//    maxOrder = MIN(maxOrder, INT_MAX);
//    favorModel.ppOrder = (int)maxOrder;
//    
//    DatabaseUnit* unit = [DatabaseUnit addCustomTagWithItem:favorModel];
//    DB_EXEC(unit);
//    
//    //重新加载
//    [[CommonDataManager shareInstance] reloadCustomTagModels];
//    //刷新首页标签
//    [[NSNotificationCenter defaultCenter] postNotificationName:kAddHomeCustomTagNotification object:favorModel];
//    
//    [UIView showToast:NSLocalizedString(@"tips.add.success", nil)];
    
    CustomTagModel* model = [CustomTagModel new];
    model.uuid = [[NSUUID UUID] UUIDString];
    model.title = bookMarkModel.title;
    model.targetUrl = bookMarkModel.url;
    model.type = 0;
    model.ppOrder = INT_MAX;
    FavorEditController* vc = [[FavorEditController alloc]init];
    [vc updateWithFavorModel:model tab:self.tab];
    BaseNavigationController* navc = [[BaseNavigationController alloc]initWithRootViewController:vc];
    
    //15.0的开启sheet模式
    if(@available(iOS 15.0,*)) {
        UISheetPresentationController* sheet = navc.sheetPresentationController;
        sheet.detents = @[
            UISheetPresentationControllerDetent.mediumDetent,
            UISheetPresentationControllerDetent.largeDetent,
        ];
        sheet.preferredCornerRadius = 10;
    }

//    [self presentViewController:navc animated:YES completion:nil];
    //v2.6.8,统一present
    [self presentCustomToViewController:navc];
}

#pragma mark - UIScrollViewDelegate

- (void)scrollViewWillBeginDragging:(UIScrollView *)scrollView
{
    [self.textField resignFirstResponder];
    [self.view endEditing:YES];
}

- (void)selectItem:(BookMarkModel*)item
{
    if(item.fileType == BookMarkTypeFile) {
        NSURL* url = [NSURL URLWithString:item.url];
        if(self.loadRequestBlock) {
            self.loadRequestBlock(url);
        }
        
        if([BrowserUtils isiPad]) {
            //iPad
            [self dismissViewControllerAnimated:YES completion:nil];
        } else {
            //iPhone
            if(self.navigationController.viewControllers.count > 1) {
                [self.navigationController popToRootViewControllerAnimated:YES];
            } else {
                [self dismissViewControllerAnimated:YES completion:nil];
            }
        }
    } else {
        BookMarkController* vc = [[BookMarkController alloc]init];
        vc.loadRequestBlock = self.loadRequestBlock;
        vc.handleOpenInNewTabAction = self.handleOpenInNewTabAction;
        vc.handleOpenAction = self.handleOpenAction;
        vc.handleOpenInBackendTabAction = self.handleOpenInBackendTabAction;
        vc.handleCopyLinkContextMenu = self.handleCopyLinkContextMenu;
        
        vc.currentBookMarkItem = item;
        [self.navigationController pushViewController:vc animated:YES];
    }
}

#pragma mark -- 书签管理
- (void)showMoreMenuForBookMark:(BookMarkModel *)model atIndexPath:(NSIndexPath *)indexPath
{
    UIAlertController* alertController = [UIAlertController alertControllerWithTitle:nil message:nil preferredStyle:[BrowserUtils isiPad] ? UIAlertControllerStyleAlert : UIAlertControllerStyleActionSheet];
    
    @weakify(self)
    if (@available(iOS 15.0, *)) {
        //编辑
        UIAlertAction* moveAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"customtag.edit", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            @strongify(self)
            [self browserBookMarkDidEditWithModel:model];
        }];
        [alertController addAction:moveAction];
    } else {
        //iOS 13
        if (model.fileType == BookMarkTypeFile) {
            // 网页书签选项
            UIAlertAction* openAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"bookmark.open", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                @strongify(self)
                [self selectItem:model];
            }];
            [alertController addAction:openAction];
            
            UIAlertAction* openNewTabAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"bookmark.openInNewTab", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                @strongify(self)
                if (self.handleOpenInNewTabAction) {
                    self.handleOpenInNewTabAction([NSURL URLWithString:model.url]);
                }
            }];
            [alertController addAction:openNewTabAction];
            
            UIAlertAction* copyAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"bookmark.copyLink", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                @strongify(self)
                if (self.handleCopyLinkContextMenu) {
                    self.handleCopyLinkContextMenu([NSURL URLWithString:model.url]);
                }
            }];
            [alertController addAction:copyAction];
        }
        
        // 通用选项
        UIAlertAction* renameAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"bookmark.rename", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            @strongify(self)
            [self showRenameAlertForBookMark:model atIndexPath:indexPath];
        }];
        [alertController addAction:renameAction];
    }
    
    UIAlertAction* moveAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"bookmark.move", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        @strongify(self)
        [self showMoveControllerForBookMark:model];
    }];
    [alertController addAction:moveAction];
    
    UIAlertAction* deleteAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"bookmark.delete", nil) style:UIAlertActionStyleDestructive handler:^(UIAlertAction * _Nonnull action) {
        @strongify(self)
        [self deleteBookMark:model atIndexPath:indexPath];
    }];
    [alertController addAction:deleteAction];
    
    UIAlertAction* cancelAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.cancel", nil) style:UIAlertActionStyleCancel handler:nil];
    [alertController addAction:cancelAction];
    
    if ([BrowserUtils isiPad]) {
        alertController.popoverPresentationController.sourceView = self.tableView;
        alertController.popoverPresentationController.sourceRect = [self.tableView rectForRowAtIndexPath:indexPath];
    }
    
//    [self presentViewController:alertController animated:YES completion:nil];
    //v2.6.8, 统一present方法，防止崩溃
    [alertController presentSafelyFromViewController:self];
}

- (void)showRenameAlertForBookMark:(BookMarkModel *)model atIndexPath:(NSIndexPath *)indexPath
{
    UIAlertController* alertController = [UIAlertController alertControllerWithTitle:NSLocalizedString(@"bookmark.renameTips", nil) message:nil preferredStyle:UIAlertControllerStyleAlert];
    
    [alertController addTextFieldWithConfigurationHandler:^(UITextField *textField) {
        textField.text = model.title;
        textField.clearButtonMode = UITextFieldViewModeWhileEditing;
    }];
    
    UIAlertAction* confirmAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.confirm", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        NSString* newTitle = alertController.textFields.firstObject.text;
        if (newTitle.length > 0) {
            [self renameBookMark:model withTitle:newTitle atIndexPath:indexPath];
        }
    }];
    [alertController addAction:confirmAction];
    
    UIAlertAction* cancelAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.cancel", nil) style:UIAlertActionStyleCancel handler:nil];
    [alertController addAction:cancelAction];
    
//    [self presentViewController:alertController animated:YES completion:nil];
    //v2.6.8, 统一present方法，防止崩溃
    [alertController presentSafelyFromViewController:self];
}

- (void)showMoveControllerForBookMark:(BookMarkModel *)model
{
    BookMarkMoveController* moveController = [[BookMarkMoveController alloc] init];
    moveController.items = @[model];
    
    @weakify(self)
    [moveController setFinishBlock:^(BookMarkModel *targetFolder) {
        @strongify(self)
        [self moveBookMark:model toFolder:targetFolder];
    }];
    
    BaseNavigationController* navigationController = [[BaseNavigationController alloc] initWithRootViewController:moveController];
    [self presentViewController:navigationController animated:YES completion:nil];
}

//编辑
- (void)browserBookMarkDidEditWithModel:(BookMarkModel *)model
{
    FavorEditController* vc = [FavorEditController new];
    BaseNavigationController* navc = [[BaseNavigationController alloc]initWithRootViewController:vc];
    
    [vc updateWithBookMarkModel:model tab:self.tab];
    
    @weakify(self)
    [vc setFinishBlock:^(BookMarkModel *selectTarget){
        @strongify(self)
        //更新新的目录
        [self requestBookMark];
    }];
    
//    if ([BrowserUtils isiPad]) {
//        // iPad
//        navc.modalPresentationStyle = UIModalPresentationFormSheet;
//        vc.preferredContentSize = CGSizeMake(kScreenWidth-90, kScreenHeight-90);
//        
//        [self presentViewController:navc animated:YES completion:nil];
//    } else {
//        // iPhone
//        [self presentViewController:navc animated:YES completion:nil];
//    }
    //v2.6.8,统一present
    [self presentCustomToViewController:navc];
}


- (void)renameBookMark:(BookMarkModel *)model withTitle:(NSString *)newTitle atIndexPath:(NSIndexPath *)indexPath
{
    DatabaseUnit* unit = [DatabaseUnit updateBookMarkWithId:model.bookmarkId title:newTitle url:model.url bookMark:model];
    @weakify(self)
    [unit setCompleteBlock:^(id result, BOOL success) {
        @strongify(self)
        if (success) {
            model.title = newTitle;
            [self.tableView reloadRowsAtIndexPaths:@[indexPath] withRowAnimation:UITableViewRowAnimationNone];
        }
    }];
    DB_EXEC(unit);
}

- (void)moveBookMark:(BookMarkModel *)model toFolder:(BookMarkModel *)targetFolder
{
    DatabaseUnit* unit = [DatabaseUnit updateBookMarkListWithArray:@[model] toparent:targetFolder.bookmarkId];
    @weakify(self)
    [unit setCompleteBlock:^(id result, BOOL success) {
        @strongify(self)
        if (success) {
            [self requestBookMark];
        }
    }];
    DB_EXEC(unit);
}

- (void)deleteBookMark:(BookMarkModel *)model atIndexPath:(NSIndexPath *)indexPath
{
    UIAlertController* alertController = [UIAlertController alertControllerWithTitle:NSLocalizedString(@"bookmark.deleteFileTips", nil) message:nil preferredStyle:UIAlertControllerStyleAlert];
    
    UIAlertAction* confirmAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.confirm", nil) style:UIAlertActionStyleDestructive handler:^(UIAlertAction * _Nonnull action) {
        DatabaseUnit* unit = [DatabaseUnit removeBookMarkTreeWithIds:@[model.bookmarkId]];
        @weakify(self)
        [unit setCompleteBlock:^(id result, BOOL success) {
            @strongify(self)
            if (success) {
                [self.model removeObject:model];
                [self.tableView deleteRowsAtIndexPaths:@[indexPath] withRowAnimation:UITableViewRowAnimationFade];
                [self showOrHideHintMessage];
            }
        }];
        DB_EXEC(unit);
    }];
    [alertController addAction:confirmAction];
    
    UIAlertAction* cancelAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.cancel", nil) style:UIAlertActionStyleCancel handler:nil];
    [alertController addAction:cancelAction];
    
//    [self presentViewController:alertController animated:YES completion:nil];
    //v2.6.8, 统一present方法，防止崩溃
    [alertController presentSafelyFromViewController:self];
}

- (void)setupNewBookMark
{
    if (self.currentBookMarkItem && ![self.currentBookMarkItem.bookmarkId isEqualToString:[BookMarkModel rootId]]) {
        [self showCreateFolderAlert];
    } else {
        [self showRootOptionsAlert];
    }
}

- (void)showCreateFolderAlert
{
    UIAlertController* alertController = [UIAlertController alertControllerWithTitle:NSLocalizedString(@"bookmark.newFolder", nil) message:nil preferredStyle:UIAlertControllerStyleAlert];
    
    [alertController addTextFieldWithConfigurationHandler:^(UITextField *textField) {
        textField.placeholder = NSLocalizedString(@"bookmark.folderNameTips", nil);
        textField.clearButtonMode = UITextFieldViewModeWhileEditing;
    }];
    
    UIAlertAction* confirmAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.confirm", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        NSString* folderName = alertController.textFields.firstObject.text;
        if (folderName.length > 0) {
            [self createNewFolderWithTitle:folderName];
        }
    }];
    [alertController addAction:confirmAction];
    
    UIAlertAction* cancelAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.cancel", nil) style:UIAlertActionStyleCancel handler:nil];
    [alertController addAction:cancelAction];
    
//    [self presentViewController:alertController animated:YES completion:nil];
    //v2.6.8, 统一present方法，防止崩溃
    [alertController presentSafelyFromViewController:self];
}

- (void)showRootOptionsAlert
{
    UIAlertController* alertController = [UIAlertController alertControllerWithTitle:nil message:nil preferredStyle:[BrowserUtils isiPad] ? UIAlertControllerStyleAlert : UIAlertControllerStyleActionSheet];
    
    UIAlertAction* newFolderAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"bookmark.newFolder", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [self showCreateFolderAlert];
    }];
    [alertController addAction:newFolderAction];
    
    UIAlertAction* importAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"bookmark.import", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [self importBookmarks];
    }];
    [alertController addAction:importAction];
    
    UIAlertAction* exportAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"bookmark.export", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [self exportBookmarks];
    }];
    [alertController addAction:exportAction];
    
    UIAlertAction* cancelAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.cancel", nil) style:UIAlertActionStyleCancel handler:nil];
    [alertController addAction:cancelAction];
    
//    [self presentViewController:alertController animated:YES completion:nil];
    //v2.6.8, 统一present方法，防止崩溃
    [alertController presentSafelyFromViewController:self];
}

- (void)createNewFolderWithTitle:(NSString *)title
{
    BookMarkModel* folder = [BookMarkModel new];
    folder.bookmarkId = [[NSUUID UUID] UUIDString];
    folder.parentId = self.currentBookMarkItem.bookmarkId;
    folder.title = title;
    folder.fileType = BookMarkTypeFolder;
    folder.updateTime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
    
    DatabaseUnit* unit = [DatabaseUnit addBookMarkWithItem:folder];
    @weakify(self)
    [unit setCompleteBlock:^(id result, BOOL success) {
        @strongify(self)
        if (success) {
            [self requestBookMark];
        }
    }];
    DB_EXEC(unit);
}

- (void)importBookmarks
{
//    UIDocumentPickerViewController* documentPicker = [[UIDocumentPickerViewController alloc] initForOpeningContentTypes:@[UTTypeHTML]];
//    documentPicker.delegate = self;
//    documentPicker.modalPresentationStyle = UIModalPresentationFormSheet;
//    [self presentViewController:documentPicker animated:YES completion:nil];
    
    UIDocumentPickerViewController* vc;
    if (@available(iOS 14.0, *)) {
        vc = [[UIDocumentPickerViewController alloc]initForOpeningContentTypes:@[UTTypeHTML,UTTypeText]];
    } else {
        //iOS13适配
        vc = [[UIDocumentPickerViewController alloc]initWithDocumentTypes:@[@"public.source-code", @"public.text"] inMode:UIDocumentPickerModeOpen];
    }
    
    vc.delegate = self;
    [self presentViewController:vc animated:YES completion:nil];
}

- (void)exportBookmarks
{
    self.manager = [BookMarkManager new];
    [self.manager exportBookMarks];
}

#pragma mark -- UIDocumentPickerDelegate

- (void)documentPicker:(UIDocumentPickerViewController *)controller didPickDocumentAtURL:(NSURL *)url
{
    BOOL shouldStopAccessing = [url startAccessingSecurityScopedResource];
    @try {
        NSError* error;
        NSString* htmlContent = [NSString stringWithContentsOfURL:url encoding:NSUTF8StringEncoding error:&error];
        if(htmlContent.length > 0) {
            //强引用，否则会被释放
            self.manager = [BookMarkManager new];
            [self.manager parseBookMarksFromHtml:htmlContent];
        }
    } @catch (NSException *exception) {
        
    } @finally {
        if(shouldStopAccessing) {
            [url stopAccessingSecurityScopedResource];
        }
    }
}

- (void)updateBookMarkOrder
{
    for (NSInteger i = 0; i < self.model.count; i++) {
        BookMarkModel* item = self.model[i];
        item.ppOrder = (int)i;
    }
    
    DatabaseUnit* unit = [DatabaseUnit updateBookMarkAllOrder:self.model];
    [unit setCompleteBlock:nil];
    DB_EXEC(unit);
}

- (void)endEditting
{
    
}

- (BookToolbar *)toolbar
{
    if(!_toolbar) {
        _toolbar = [BookToolbar new];
        _toolbar.hidden = YES;
    }
    
    return _toolbar;
}

@end
