//
//  PreferenceItemCell.m
//  PPBrowser
//
//  Created by qingbin on 2022/4/25.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "PreferenceItemCell.h"

#import "MaizyHeader.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "NSObject+Helper.h"
#import "NSString+Helper.h"

#import "CustomTitleAndImageView.h"
#import "PreferenceManager.h"

#import "VIPController.h"
#import "BaseNavigationController.h"
#import "AppDelegate.h"
#import "BrowserViewController.h"

#import "PaymentManager.h"
#import "BrowserUtils.h"
#import "TrialManager.h"
#import "UIAlertController+SafePresentation.h"

@interface PreferenceItemCell ()

@property (nonatomic, strong) CustomTitleAndImageView *itemBtn;
@property (nonatomic, strong) PreferenceItemModel *model;

@end

@implementation PreferenceItemCell

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if(self) {
        [self addSubviews];
        [self defineLayout];
        [self setupObservers];
    }
    
    return self;
}

#pragma mark -- 夜间模式
- (void)applyTheme
{}

- (void)updateWithModel:(PreferenceItemModel*)model
{
    self.model = model;
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    
    UIImage* image = [UIImage imageNamed:model.imageName];
    image = [image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
    [self.itemBtn updateWith:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
        titleLabel.text = model.title;
        imageView.image = image;
    }];
    
    UIColor* titleColor = [UIColor colorWithHexString:@"#222222"];
    UIColor* imageColor = [UIColor colorWithHexString:@"#000000"];
    if(isDarkTheme) {
        titleColor = UIColor.whiteColor;
        imageColor = UIColor.whiteColor;
    }
    
    //v2.7.4，无痕模式改为弹窗的形式
//    if(model.itemType == PreferenceItemTypePrivacy) {
//        //隐私模式
//        BOOL isPrivate = [[PreferenceManager shareInstance].items.isPrivate boolValue];
//        if(isPrivate) {
//            [self.itemBtn updateWith:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
//                imageView.tintColor = [UIColor colorWithHexString:@"#2D7AFE"];
//                titleLabel.textColor = [UIColor colorWithHexString:@"#2D7AFE"];
//            }];
//        } else {
//            [self.itemBtn updateWith:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
//                imageView.tintColor = imageColor;
//                titleLabel.textColor = titleColor;
//            }];
//        }
//    } else
    if(model.itemType == PreferenceItemTypeFullScreen) {
        //全屏模式
        BOOL isFullScreen = [[PreferenceManager shareInstance].items.isFullScreen boolValue];
        if(isFullScreen) {
            [self.itemBtn updateWith:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
                imageView.tintColor = [UIColor colorWithHexString:@"#2D7AFE"];
                titleLabel.textColor = [UIColor colorWithHexString:@"#2D7AFE"];
            }];
        } else {
            [self.itemBtn updateWith:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
                imageView.tintColor = imageColor;
                titleLabel.textColor = titleColor;
            }];
        }
    } else if(model.itemType == PreferenceItemTypeNightMode) {
        //暗黑模式
        BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
        if(isDarkTheme) {
            [self.itemBtn updateWith:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
                imageView.tintColor = [UIColor colorWithHexString:@"#2D7AFE"];
                titleLabel.textColor = [UIColor colorWithHexString:@"#2D7AFE"];
            }];
        } else {
            [self.itemBtn updateWith:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
                imageView.tintColor = imageColor;
                titleLabel.textColor = titleColor;
            }];
        }
    } else {
        //其它
        if(model.shouldAffectByHomeURL && model.isHomeURL) {
            //受到首页的影响,而且是首页
            [self.itemBtn updateWith:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
                imageView.tintColor = [UIColor colorWithHexString:@"#999999"];
                titleLabel.textColor = [UIColor colorWithHexString:@"#999999"];
            }];
        } else {
            if(model.itemType == PreferenceItemTypeNoImage) {
                //无图模式
                BOOL enabled = [[PreferenceManager shareInstance].items.isNoImage boolValue];
                if(enabled) {
                    [self.itemBtn updateWith:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
                        imageView.tintColor = [UIColor colorWithHexString:@"#2D7AFE"];
                        titleLabel.textColor = [UIColor colorWithHexString:@"#2D7AFE"];
                    }];
                } else {
                    [self.itemBtn updateWith:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
                        imageView.tintColor = imageColor;
                        titleLabel.textColor = titleColor;
                    }];
                }
            } else if(model.itemType == PreferenceItemTypeTagit) {
                //标记模式
                //忽略版权控制
                BOOL enabledTagit = [[PreferenceManager shareInstance].items.enabledTagit boolValue];
                if(model.isValidCopyright || enabledTagit) {
                    [self.itemBtn updateWith:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
                        imageView.tintColor = imageColor;
                        titleLabel.textColor = titleColor;
                    }];
                } else {
                    [self.itemBtn updateWith:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
                        imageView.tintColor = [UIColor colorWithHexString:@"#999999"];
                        titleLabel.textColor = [UIColor colorWithHexString:@"#999999"];
                    }];
                }
            } else {
                //其它
                [self.itemBtn updateWith:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
                    imageView.tintColor = imageColor;
                    titleLabel.textColor = titleColor;
                }];
            }
        }
    }
}

- (void)setupObservers
{
    UITapGestureRecognizer* tap = [[UITapGestureRecognizer alloc]init];
    @weakify(self)
    [tap.rac_gestureSignal subscribeNext:^(id x) {
        @strongify(self)
        //如果是首页,而且受到首页操作的影响,则直接返回
        if(self.model.shouldAffectByHomeURL && self.model.isHomeURL) {
            [UIView showToast:NSLocalizedString(@"tips.tap.home", nil)];
            return;
        }
        
        if(self.model.itemType == PreferenceItemTypeTagit) {
            //忽略版权控制
            BOOL enabledTagit = [[PreferenceManager shareInstance].items.enabledTagit boolValue];
            if(!self.model.isValidCopyright && !enabledTagit) {
                //没有版权
                return;
            }
            
            //会员判断
            if(![self checkCanUseWithFeatureType:FeatureTypeTagit]) {
                return;
            }
        } else if(self.model.itemType == PreferenceItemTypeTranslate) {
            //会员判断
            if(![self checkCanUseWithFeatureType:FeatureTypeTranslate]) {
                return;
            }
        }
        
        if(self.didTapAction) {
            self.didTapAction();
        }
    }];
    
    [self.contentView addGestureRecognizer:tap];
}

- (BOOL)checkCanUseWithFeatureType:(FeatureType)feature
{
    BOOL isVip = [[PaymentManager shareInstance] isVip];
    BOOL canUseFeature = isVip;
    if(!canUseFeature) {
        canUseFeature = [[TrialManager sharedManager] canUseFeature:feature];
    }
    
    NSString* message;
    if(feature == FeatureTypeTagit) {
        //标记模式
        message = NSLocalizedString(@"vip.alert.tagit.text", nil);
    } else if(feature == FeatureTypeTranslate) {
        //网页翻译
        message = NSLocalizedString(@"vip.alert.translate.text", nil);
    }
    
    if(!canUseFeature) {
        UIAlertController* alertController = [UIAlertController alertControllerWithTitle:NSLocalizedString(@"vip.alert.title", nil) message:message preferredStyle:UIAlertControllerStyleAlert];
        UIAlertAction* action = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.cancel", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            [alertController dismissViewControllerAnimated:YES completion:nil];
        }];
        [alertController addAction:action];

        action = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.toKnowMore", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            [self jumpToVip];
        }];
        [alertController addAction:action];

        AppDelegate *appDelegate = (AppDelegate*) [UIApplication sharedApplication].delegate;
        BrowserViewController* browser = appDelegate.browser;
//        [browser presentViewController:alertController animated:YES completion:nil];
        //v2.6.8, 统一present方法，防止崩溃
        [alertController presentSafelyFromViewController:browser];
    }
    
    return canUseFeature;
}

#pragma mark -- 打开Vip页面
- (void)jumpToVip
{
    VIPController* vc = [[VIPController alloc]init];
    BaseNavigationController* navc = [[BaseNavigationController alloc]initWithRootViewController:vc];
    navc.view.backgroundColor = UIColor.whiteColor;

    AppDelegate *appDelegate = (AppDelegate*) [UIApplication sharedApplication].delegate;
    BrowserViewController* _vc = appDelegate.browser;
    [_vc presentViewController:navc animated:YES completion:nil];
}

- (void)addSubviews
{
    [self.contentView addSubview:self.itemBtn];
}

- (void)defineLayout
{
    //保持一行显示的效果，计算1行时的高度
    // 设置一个固定高度，与1行时等效
    CGFloat titleHeight = [UIFont systemFontOfSize:iPadValue(18,14)].lineHeight;
    float height = iPadValue(36, 28) + iPadValue(20, 10) + titleHeight;
    [self.itemBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.contentView);
        make.top.equalTo(self.contentView.mas_centerY).offset(-height/2.0);
    }];
}

- (CustomTitleAndImageView *)itemBtn
{
    if(!_itemBtn) {
        _itemBtn = [[CustomTitleAndImageView alloc]initWithLayout:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            titleLabel.textColor = [UIColor colorWithHexString:@"#222222"];
            titleLabel.font = [UIFont systemFontOfSize:iPadValue(18,14)];
            titleLabel.textAlignment = NSTextAlignmentCenter;
            titleLabel.numberOfLines = 2;
            titleLabel.lineBreakMode = NSLineBreakByCharWrapping;
            
            //决定高度
            [imageView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.equalTo(view);
                make.size.mas_equalTo(iPadValue(36, 28));
                make.top.mas_offset(0);
            }];
                        
            [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.equalTo(view);
                make.top.equalTo(imageView.mas_bottom).offset(iPadValue(20, 10));
                make.bottom.mas_offset(0);
                //适配多语言版
                make.left.mas_greaterThanOrEqualTo(5);
                make.right.mas_lessThanOrEqualTo(-5);
            }];
        }];
        
        _itemBtn.userInteractionEnabled = NO;
    }
    
    return _itemBtn;
}

@end
