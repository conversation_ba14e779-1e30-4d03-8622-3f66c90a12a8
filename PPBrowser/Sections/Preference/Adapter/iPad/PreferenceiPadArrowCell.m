//
//  PreferenceiPadArrowCell.m
//  PPBrowser
//
//  Created by qing<PERSON> on 2022/9/30.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "PreferenceiPadArrowCell.h"

#import "MaizyHeader.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "NSObject+Helper.h"
#import "NSString+Helper.h"

#import "CustomButton.h"
#import "PreferenceManager.h"

@interface PreferenceiPadArrowCell ()

@property (nonatomic, strong) UIView* backView;

@property (nonatomic, strong) UIImageView *logo;

@property (nonatomic, strong) UILabel* titleLabel;

@property (nonatomic, strong) UIImageView* arrow;

@property(nonatomic,strong) UIView* line;

@property (nonatomic, strong) PreferenceItemModel *model;

@end

@implementation PreferenceiPadArrowCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        
        [self addSubviews];
        [self defineLayout];
    }
    
    return self;
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.backView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        self.titleLabel.textColor = UIColor.whiteColor;
        self.line.backgroundColor = [UIColor colorWithHexString:kDarkThemeColorLine];
        self.arrow.tintColor = UIColor.whiteColor;
    } else {
        self.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.backView.backgroundColor = UIColor.whiteColor;
        self.titleLabel.textColor = [UIColor colorWithHexString:@"#434343"];
        self.line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.arrow.tintColor = [UIColor colorWithHexString:@"#333333"];
    }
}

- (void)updateWithModel:(PreferenceItemModel*)model
{
    self.model = model;
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    
    UIImage* image = [UIImage imageNamed:model.imageName];
    image = [image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
    UIColor* titleColor = [UIColor colorWithHexString:@"#222222"];
    UIColor* imageColor = [UIColor colorWithHexString:@"#000000"];
    
    if(isDarkTheme) {
        titleColor = UIColor.whiteColor;
        imageColor = UIColor.whiteColor;
    }
    
    [self applyTheme];
    
    if(model.shouldAffectByHomeURL && model.isHomeURL) {
        //受到首页的影响,而且是首页
        titleColor = [UIColor colorWithHexString:@"#999999"];
        imageColor = [UIColor colorWithHexString:@"#999999"];
        self.arrow.tintColor = [UIColor colorWithHexString:@"#999999"];
    } else if(model.itemType == PreferenceItemTypeTagit) {
        //标记模式
        //忽略版权控制
        BOOL enabledTagit = [[PreferenceManager shareInstance].items.enabledTagit boolValue];
        if(!model.isValidCopyright && !enabledTagit) {
            titleColor = [UIColor colorWithHexString:@"#999999"];
            imageColor = [UIColor colorWithHexString:@"#999999"];
            self.arrow.tintColor = [UIColor colorWithHexString:@"#999999"];
        }
    }
    
    self.logo.image = image;
    self.logo.tintColor = imageColor;
    
    self.titleLabel.text = model.title;
    self.titleLabel.textColor = titleColor;
    
    [self updateCornerRadius];
}

- (void)updateCornerRadius
{
    // 圆角
    self.line.hidden = NO;
    self.backView.layer.cornerRadius = 20;
    self.backView.layer.masksToBounds = YES;
    
    if(self.model.isFirstInSection && self.model.isLastInSection) {
        //只有一个
        self.backView.layer.masksToBounds = YES;
        self.backView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner | kCALayerMinXMaxYCorner | kCALayerMaxXMaxYCorner;
        self.line.hidden = YES;
    } else {
        if(self.model.isFirstInSection) {
            //添加上圆角
            self.backView.layer.masksToBounds = YES;
            self.backView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
        } else if(self.model.isLastInSection) {
            //添加下圆角
            self.backView.layer.masksToBounds = YES;
            self.backView.layer.maskedCorners = kCALayerMinXMaxYCorner | kCALayerMaxXMaxYCorner;
            self.line.hidden = YES;
        } else {
            self.backView.layer.masksToBounds = NO;
            self.backView.layer.cornerRadius = 0;
        }
    }
}

- (void)addSubviews
{
    [self.contentView addSubview:self.backView];
    [self.backView addSubview:self.logo];
    [self.backView addSubview:self.titleLabel];
    [self.backView addSubview:self.arrow];
}

- (void)defineLayout
{
    [self.backView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(30);
        make.right.mas_offset(-30);
        make.top.bottom.equalTo(self.contentView);
    }];
    
    [self.logo mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(30);
        make.centerY.equalTo(self.backView);
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.logo.mas_right).offset(30);
        make.centerY.equalTo(self.backView);
    }];
    
    [self.arrow mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_offset(-30);
        make.centerY.equalTo(self.backView);
        make.size.mas_equalTo(15);
    }];
    
    UIView* line = [UIView new];
    line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    [self.backView addSubview:line];
    [line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.backView);
        make.left.mas_offset(30);
        make.right.mas_offset(-0);
        make.height.mas_equalTo(0.5);
    }];
    self.line = line;
}

- (UIImageView *)logo
{
    if(!_logo) {
        _logo = [UIImageView new];
    }
    
    return _logo;
}

- (UIImageView *)arrow
{
    if(!_arrow) {
        _arrow = [UIImageView new];
        
        UIImage* image = [UIImage imageNamed:@"standard_right_arrow"];
        image = [image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        _arrow.image = image;
    }
    
    return _arrow;
}

- (UILabel *)titleLabel
{
    if(!_titleLabel) {
        _titleLabel = [UIView createLabelWithTitle:nil
                                        textColor:[UIColor colorWithHexString:@"#333333"]
                                          bgColor:UIColor.clearColor
                                         fontSize:20
                                    textAlignment:NSTextAlignmentLeft
                                            bBold:NO];
    }
    
    return _titleLabel;
}

- (UIView *)backView
{
    if(!_backView) {
        _backView = [UIView new];
        _backView.backgroundColor = UIColor.whiteColor;
    }
    
    return _backView;
}

@end
