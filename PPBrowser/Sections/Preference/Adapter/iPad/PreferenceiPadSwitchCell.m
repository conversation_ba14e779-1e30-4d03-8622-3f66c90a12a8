//
//  PreferenceiPadSwitchCell.m
//  PPBrowser
//
//  Created by qing<PERSON> on 2022/9/30.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "PreferenceiPadSwitchCell.h"

#import "MaizyHeader.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "NSObject+Helper.h"
#import "NSString+Helper.h"

#import "CustomButton.h"
#import "PreferenceManager.h"

@interface PreferenceiPadSwitchCell ()

@property (nonatomic, strong) UIView* backView;

@property (nonatomic, strong) UIImageView *logo;

@property (nonatomic, strong) UILabel* titleLabel;

@property (nonatomic, strong) UISwitch *switchView;

@property(nonatomic,strong) UIView* line;

@property (nonatomic, strong) PreferenceItemModel *model;

@end

@implementation PreferenceiPadSwitchCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        
        [self addSubviews];
        [self defineLayout];
        [self setupObservers];
    }
    
    return self;
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.backView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        self.titleLabel.textColor = UIColor.whiteColor;
        self.line.backgroundColor = [UIColor colorWithHexString:kDarkThemeColorLine];
    } else {
        self.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.backView.backgroundColor = UIColor.whiteColor;
        self.titleLabel.textColor = [UIColor colorWithHexString:@"#434343"];
        self.line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
}

- (void)updateWithModel:(PreferenceItemModel*)model
{
    self.model = model;
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    
    UIImage* image = [UIImage imageNamed:model.imageName];
    image = [image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
    UIColor* titleColor = [UIColor colorWithHexString:@"#222222"];
    UIColor* imageColor = [UIColor colorWithHexString:@"#000000"];
    
    if(isDarkTheme) {
        titleColor = UIColor.whiteColor;
        imageColor = UIColor.whiteColor;
    }
    
    self.logo.image = image;
    self.logo.tintColor = imageColor;
    
    self.titleLabel.text = model.title;
    self.titleLabel.textColor = titleColor;
    
    if(model.itemType == PreferenceItemTypePrivacy) {
        //隐私模式
        BOOL isPrivate = [[PreferenceManager shareInstance].items.isPrivate boolValue];
        self.switchView.on = isPrivate;
    } else if(model.itemType == PreferenceItemTypeFullScreen) {
        //全屏模式
        BOOL isFullScreen = [[PreferenceManager shareInstance].items.isFullScreen boolValue];
        self.switchView.on = isFullScreen;
    } else if(model.itemType == PreferenceItemTypeNightMode) {
        //暗黑模式
        BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
        self.switchView.on = isDarkTheme;
    } else {
        //其它
        if(model.shouldAffectByHomeURL && model.isHomeURL) {
            //受到首页的影响,而且是首页            
            BOOL enabled = [[PreferenceManager shareInstance].items.isNoImage boolValue];
            self.switchView.on = enabled;
        } else {
            if(model.itemType == PreferenceItemTypeNoImage) {
                //无图模式
                BOOL enabled = [[PreferenceManager shareInstance].items.isNoImage boolValue];
                self.switchView.on = enabled;
            } else {
                //其它
            }
        }
    }
    
    [self updateCornerRadius];
    [self applyTheme];
}

- (void)updateCornerRadius
{
    // 圆角
    self.line.hidden = NO;
    self.backView.layer.cornerRadius = 20;
    self.backView.layer.masksToBounds = YES;
    
    if(self.model.isFirstInSection && self.model.isLastInSection) {
        //只有一个
        self.backView.layer.masksToBounds = YES;
        self.backView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner | kCALayerMinXMaxYCorner | kCALayerMaxXMaxYCorner;
        self.line.hidden = YES;
    } else {
        if(self.model.isFirstInSection) {
            //添加上圆角
            self.backView.layer.masksToBounds = YES;
            self.backView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
        } else if(self.model.isLastInSection) {
            //添加下圆角
            self.backView.layer.masksToBounds = YES;
            self.backView.layer.maskedCorners = kCALayerMinXMaxYCorner | kCALayerMaxXMaxYCorner;
            self.line.hidden = YES;
        } else {
            self.backView.layer.masksToBounds = NO;
            self.backView.layer.cornerRadius = 0;
        }
    }
}

- (void)setupObservers
{
    @weakify(self)
    [[self.switchView rac_newOnChannel] subscribeNext:^(id x) {
        @strongify(self)
        if(self.didSwithAction) {
            self.didSwithAction(self.switchView.isOn);
        }
    }];
}

- (void)addSubviews
{
    [self.contentView addSubview:self.backView];
    [self.backView addSubview:self.logo];
    [self.backView addSubview:self.titleLabel];
    [self.backView addSubview:self.switchView];
}

- (void)defineLayout
{
    [self.backView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(30);
        make.right.mas_offset(-30);
        make.top.bottom.equalTo(self.contentView);
    }];
    
    [self.logo mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(30);
        make.centerY.equalTo(self.backView);
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.logo.mas_right).offset(30);
        make.centerY.equalTo(self.backView);
    }];
    
    [self.switchView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_offset(-30);
        make.centerY.equalTo(self.backView);
    }];
    
    UIView* line = [UIView new];
    line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    [self.backView addSubview:line];
    [line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.backView);
        make.left.mas_offset(30);
        make.right.mas_offset(-0);
        make.height.mas_equalTo(0.5);
    }];
    self.line = line;
}

- (UIImageView *)logo
{
    if(!_logo) {
        _logo = [UIImageView new];
    }
    
    return _logo;
}

- (UISwitch *)switchView
{
    if(!_switchView) {
        _switchView = [UISwitch new];
        _switchView.on = NO;
    }
    
    return _switchView;
}

- (UILabel *)titleLabel
{
    if(!_titleLabel) {
        _titleLabel = [UIView createLabelWithTitle:nil
                                        textColor:[UIColor colorWithHexString:@"#333333"]
                                          bgColor:UIColor.clearColor
                                         fontSize:20
                                    textAlignment:NSTextAlignmentLeft
                                            bBold:NO];
    }
    
    return _titleLabel;
}

- (UIView *)backView
{
    if(!_backView) {
        _backView = [UIView new];
        _backView.backgroundColor = UIColor.whiteColor;
    }
    
    return _backView;
}


@end
