//
//  PreferenceFlowLayout.m
//  PPBrowser
//
//  Created by qingbin on 2022/4/25.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "PreferenceFlowLayout.h"

#import "Masonry.h"
#import "ReactiveCocoa.h"

#import "UIColor+Helper.h"
#import "NSObject+Helper.h"
#import "MaizyHeader.h"

#import "BrowserUtils.h"

@interface PreferenceFlowLayout ()

@property (nonatomic, assign) int columns;

@property (nonatomic, assign) int rows;

@property (nonatomic, assign) int pageSize;

@property (nonatomic, assign) float verticalCellSpacing;

@property (nonatomic, strong) NSMutableArray<UICollectionViewLayoutAttributes*> *cache;

@property (nonatomic, assign) float width;

@property (nonatomic, assign) float cellContentWidth;

@property (nonatomic, assign) float cellContentHeight;

@property (nonatomic, assign) float contentHeight;

@end

@implementation PreferenceFlowLayout

- (instancetype)initWithContentWidth:(float)contentWidth
{
    self = [super init];
    if(self) {
        self.width = contentWidth;
        
        self.cellContentWidth = self.width/(self.columns*1.0);
        self.cellContentHeight = self.cellContentWidth;
    }
    
    return self;
}

+ (int)rowCount
{
    return 2;
}

+ (int)columnCount
{
    if([BrowserUtils isiPhone] && [[BrowserUtils shareInstance] isLandscape]) {
        //横屏的iPhone因为只是popover的效果，宽度比较小，所以少一个的效果比较好
        return 4;
    }
    
    return 5;
}

+ (float)verticalCellSpacing
{
    return 15.0;
}

+ (float)collectionViewHeightWithContentWidth:(float)contentWidth
{
    return contentWidth*2.0 / [self columnCount] + [self verticalCellSpacing] + 10 /*v2.6.8,第二行高度加多10pt*/;
}

- (int)rows
{
    return [PreferenceFlowLayout rowCount];
}

- (int)columns
{
    return [PreferenceFlowLayout columnCount];
}

- (int)pageSize
{
    return self.rows * self.columns;
}

- (float)verticalCellSpacing
{
    /*要同步到高度中*/
    return [PreferenceFlowLayout verticalCellSpacing];
}

- (CGSize)collectionViewContentSize
{
    self.contentHeight = (self.rows-1)*self.verticalCellSpacing + self.rows*self.cellContentHeight + 10 /*v2.6.8,第二行高度加多10pt*/;
    
    NSInteger numberOfSections = [self.collectionView numberOfSections];
    return CGSizeMake(self.width*numberOfSections, self.contentHeight);
}

- (void)prepareLayout
{
    if(self.cache.count > 0) return;
    
    float width = self.collectionView.bounds.size.width;
//    float height = self.collectionView.bounds.size.height;
    
    NSInteger numberOfSections = [self.collectionView numberOfSections];
    for(int section=0;section<numberOfSections;section++) {
        NSInteger numberOfItems = [self.collectionView numberOfItemsInSection:section];
        for(int row=0;row<numberOfItems;row++) {
            int currentPage = section;
            float sectionOffsetX = width*currentPage;
            
            int columnIndex = row % self.columns;
            int rowIndex = row / self.columns;
            
            // create a frame for the item
            float x = (self.cellContentWidth * columnIndex) + sectionOffsetX;
            float y = rowIndex * self.verticalCellSpacing + (self.cellContentHeight * rowIndex);
            
            float cellContentHeight = self.cellContentHeight;
            if (rowIndex == 1) {
                //v2.6.8, 第二行高度加多10pt
                cellContentHeight += 10;
            }
            
            NSIndexPath* indexPath = [NSIndexPath indexPathForRow:row inSection:section];
            CGRect itemRect = CGRectMake(x, y, self.cellContentWidth, cellContentHeight);
            UICollectionViewLayoutAttributes* attributes = [UICollectionViewLayoutAttributes layoutAttributesForCellWithIndexPath:indexPath];
            attributes.frame = itemRect;
            [self.cache addObject:attributes];
        }
    }
}

- (UICollectionViewLayoutAttributes *)layoutAttributesForItemAtIndexPath:(NSIndexPath *)indexPath
{
    return self.cache[indexPath.row];
}

- (NSArray<__kindof UICollectionViewLayoutAttributes *> *)layoutAttributesForElementsInRect:(CGRect)rect
{
    NSMutableArray* layoutAttributes = [NSMutableArray array];
    for(UICollectionViewLayoutAttributes* item in self.cache) {
        CGRect intersect = CGRectIntersection(rect, item.frame);
        if(!CGRectEqualToRect(intersect, CGRectZero)) {
            [layoutAttributes addObject:item];
        }
    }
    
    return layoutAttributes;
}

- (BOOL)shouldInvalidateLayoutForBoundsChange:(CGRect)newBounds
{
    BOOL isEqual = CGSizeEqualToSize(newBounds.size, self.collectionView.bounds.size);
    return !isEqual;
}

- (void)invalidateLayout
{
    [self.cache removeAllObjects];
    [super invalidateLayout];
}

- (NSMutableArray<UICollectionViewLayoutAttributes *> *)cache
{
    if(!_cache) {
        _cache = [NSMutableArray array];
    }
    
    return _cache;
}

@end
