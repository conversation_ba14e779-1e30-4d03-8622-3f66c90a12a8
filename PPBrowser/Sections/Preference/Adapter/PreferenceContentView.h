//
//  PreferenceContentView.h
//  PPBrowser
//
//  Created by qingbin on 2022/4/25.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <UIKit/UIKit.h>

#import "PreferenceItemCell.h"
#import "PreferenceItemModel.h"

#import "ThemeProtocol.h"
#import "PreferenceManager.h"

@protocol PreferenceViewDelegate <NSObject>

- (void)preferenceViewDidSelectItem:(PreferenceItemModel*)item;

@end

@interface PreferenceContentView : UIView<ThemeProtocol>

- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)new NS_UNAVAILABLE;

- (instancetype)initWithContentWidth:(float)contentWidth;

- (void)reloadData;

+ (float)heightWithContentWidth:(float)contentWidth;

@property (nonatomic, weak) id<PreferenceViewDelegate> delegate;

@end


