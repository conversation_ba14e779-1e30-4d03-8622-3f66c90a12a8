//
//  PreferenceContentView.m
//  PPBrowser
//
//  Created by qingbin on 2022/4/25.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "PreferenceContentView.h"

#import "MaizyHeader.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "NSObject+Helper.h"

#import "PreferenceFlowLayout.h"
#import "PreferenceItemCell.h"

#import "InternalURL.h"
#import "BrowserViewController.h"
#import "AppDelegate.h"
#import "CopyrightHelper.h"

#import "CommonDataManager.h"

@interface PreferenceContentView () <UICollectionViewDelegate, UICollectionViewDataSource>

@property (nonatomic, strong) NSMutableArray* model;

@property (nonatomic, strong) UICollectionView *collectionView;

@property (nonatomic, strong) UIPageControl *pageControl;

@property (nonatomic, assign) float contentWidth;

@end

@implementation PreferenceContentView

- (instancetype)initWithContentWidth:(float)contentWidth
{
    self = [super init];
    if(self) {
        self.contentWidth = contentWidth;
        
        [self addSubviews];
        [self defineLayout];
        [self updateWithModel];
        
        [self applyTheme];
    }
    
    return self;
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        self.collectionView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        
        self.pageControl.pageIndicatorTintColor = [UIColor colorWithHexString:@"#b0b3b5"];
        self.pageControl.currentPageIndicatorTintColor = [UIColor colorWithHexString:@"#dfe1e1"];
    } else {
        self.backgroundColor = UIColor.whiteColor;
        self.collectionView.backgroundColor = UIColor.whiteColor;
        
        self.pageControl.pageIndicatorTintColor = [UIColor colorWithHexString:@"#dfe1e1"];
        self.pageControl.currentPageIndicatorTintColor = [UIColor colorWithHexString:@"#b0b3b5"];
    }
}

- (void)reloadData
{
    [self applyTheme];
    
    [self updateWithModel];
    [self.collectionView reloadData];
}

#pragma mark -- model
- (void)updateWithModel
{
    [self.model removeAllObjects];
    
    AppDelegate *appDelegate = (AppDelegate*) [UIApplication sharedApplication].delegate;
    BrowserViewController* _vc = appDelegate.browser;
    if(!_vc) return;
    
    NSURL* URL = _vc.tabManager.selectedTab.webView.URL;
    BOOL isHomeURL = [InternalURL isAboutHomeURL:URL.absoluteString];
    BOOL isValidCopyright = [[CopyrightHelper shareInstance] validAdBlockUrl:URL];
    
    [self.model addObject:[PreferenceItemModel bookMarkItem]];
    [self.model addObject:[PreferenceItemModel historyItem]];
    [self.model addObject:[PreferenceItemModel translateItem]];
    [self.model addObject:[PreferenceItemModel jsItem]];
    [self.model addObject:[PreferenceItemModel UAItem]];
//    [self.model addObject:[PreferenceItemModel addBookMarkItem]];
    
    //添加(首页+书签)
    PreferenceItemModel* addItemModel = [PreferenceItemModel new];
    addItemModel.itemType = PreferenceItemTypeAddBookMark;
    addItemModel.shouldAffectByHomeURL = YES;
    
    [CommonDataManager shareInstance].currentCustomTagModel = nil;
    [CommonDataManager shareInstance].currentBookMarkModel = nil;
    
    BOOL hasSelected = false;
    if(![InternalURL isValid:URL]) {
        //不是内置url
        NSArray* customTagModels = [CommonDataManager shareInstance].customTagModels;
        for(CustomTagModel* item in customTagModels) {
            if([item.targetUrl isEqualToString:URL.absoluteString]) {
                addItemModel.imageName = @"tool_edit_icon";
                addItemModel.title = NSLocalizedString(@"customtag.edit", nil);
                [CommonDataManager shareInstance].currentCustomTagModel = item;
                hasSelected = true;
                break;
            }
        }
        
        NSArray* bookMarks = [CommonDataManager shareInstance].bookMarks;
        for(BookMarkModel* item in bookMarks) {
            if([item.url isEqualToString:URL.absoluteString]) {
                addItemModel.imageName = @"tool_edit_icon";
                addItemModel.title = NSLocalizedString(@"customtag.edit", nil);
                [CommonDataManager shareInstance].currentBookMarkModel = item;
                hasSelected = true;
                break;
            }
        }
        
        if(!hasSelected) {
            addItemModel.imageName = @"tool_add_icon";
            addItemModel.title = NSLocalizedString(@"activity.addBookmark", nil);
        }
    } else {
        addItemModel.imageName = @"tool_add_icon";
        addItemModel.title = NSLocalizedString(@"activity.addBookmark", nil);
    }
        
    [self.model addObject:addItemModel];
    
    [self.model addObject:[PreferenceItemModel nightModeItem]];
    [self.model addObject:[PreferenceItemModel toolboxItem]];
    [self.model addObject:[PreferenceItemModel autoPageItem]];
//    [self.model addObject:[PreferenceItemModel privacyItem]];
    [self.model addObject:[PreferenceItemModel refreshItem]];
    [self.model addObject:[PreferenceItemModel screenshotItem]];
    [self.model addObject:[PreferenceItemModel noImageModeItem]];
    [self.model addObject:[PreferenceItemModel imageModeItem]];
    [self.model addObject:[PreferenceItemModel findInPageItem]];
    [self.model addObject:[PreferenceItemModel shareItem]];
    [self.model addObject:[PreferenceItemModel feedbackItem]];
//    [self.model addObject:[PreferenceItemModel downloadItem]];
    [self.model addObject:[PreferenceItemModel tagitItem]];
    [self.model addObject:[PreferenceItemModel privacyItem]];
    [self.model addObject:[PreferenceItemModel fullScreenItem]];
    [self.model addObject:[PreferenceItemModel settingItem]];
    
    for(PreferenceItemModel* item in self.model) {
        //当前URL是否是首页
        item.isHomeURL = isHomeURL;
        //当前URL是否有版权
        item.isValidCopyright = isValidCopyright;
    }
    
    [self.collectionView reloadData];
    
    self.pageControl.numberOfPages = ceil(self.model.count/(1.0*self.pageSize));
}

+ (float)collectionViewHeightWithContentWidth:(float)contentWidth
{
    return [PreferenceFlowLayout collectionViewHeightWithContentWidth:contentWidth];
}

+ (float)heightWithContentWidth:(float)contentWidth
{
    UIWindow* window = YBIBNormalWindow();
    float collectionViewHeight = [self collectionViewHeightWithContentWidth:contentWidth];
    float pageControlheight = 25;
    float buffer = 10;
    float height = 20 + collectionViewHeight + 15 + pageControlheight + buffer + window.safeAreaInsets.bottom;
    
    return height;
}

#pragma mark -- layout
- (void)addSubviews
{
    self.backgroundColor = [UIColor colorWithHexString:@"#ffffff"];
    [self addSubview:self.collectionView];
    [self addSubview:self.pageControl];
}

- (void)defineLayout
{
    [self.collectionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self).offset(0);
        make.left.equalTo(self);
        make.right.equalTo(self);
        make.height.mas_equalTo([PreferenceContentView collectionViewHeightWithContentWidth:self.contentWidth]);
    }];
    
    [self.pageControl mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self);
        make.top.equalTo(self.collectionView.mas_bottom).offset(5);
        make.height.mas_equalTo(25.0f);
        make.width.equalTo(self);
    }];
}

- (UIPageControl *)pageControl
{
    if (!_pageControl) {
        _pageControl = [[UIPageControl alloc] init];
        //dfe1e1
        _pageControl.pageIndicatorTintColor = [UIColor colorWithHexString:@"#dfe1e1"];
        //b0b3b5
        _pageControl.currentPageIndicatorTintColor = [UIColor colorWithHexString:@"#b0b3b5"];
        _pageControl.currentPage = 0;
        _pageControl.hidesForSinglePage = YES;
    }
    return _pageControl;
}

- (NSMutableArray *)model
{
    if(!_model) {
        _model = [NSMutableArray array];
    }
    
    return _model;
}

- (UICollectionView *)collectionView
{
    if (!_collectionView) {
        
        PreferenceFlowLayout *layout = [[PreferenceFlowLayout alloc] initWithContentWidth:self.contentWidth];
        layout.scrollDirection = UICollectionViewScrollDirectionHorizontal;
        layout.sectionInset = UIEdgeInsetsZero;
        layout.minimumInteritemSpacing = 0;
        layout.minimumLineSpacing = 0;
        
        _collectionView = [[UICollectionView alloc] initWithFrame:CGRectZero collectionViewLayout:layout];
        _collectionView.backgroundColor = [UIColor clearColor];
        _collectionView.pagingEnabled = YES;
        [_collectionView registerClass:[PreferenceItemCell class] forCellWithReuseIdentifier:NSStringFromClass(PreferenceItemCell.class)];
        _collectionView.delegate = self;
        _collectionView.dataSource = self;
        _collectionView.showsHorizontalScrollIndicator = NO;
        
    }
    return _collectionView;
}

- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView
{
    //计算当前页数
    int currentPage = scrollView.contentOffset.x / self.contentWidth;
    self.pageControl.currentPage = currentPage;
}

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section
{
    NSInteger less = self.model.count - self.pageSize*section;
    less = MIN(self.pageSize, less);
    
    return less;
}

- (int)pageSize
{
    int rows = [PreferenceFlowLayout rowCount];
    int columns = [PreferenceFlowLayout columnCount];
    int pageSize = rows*columns;
    
    return pageSize;
}

- (NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView
{
    int rows = [PreferenceFlowLayout rowCount];
    int columns = [PreferenceFlowLayout columnCount];
    NSInteger sections = ceil(self.model.count/(rows*columns*1.0));
    return sections;
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath
{
    PreferenceItemCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"PreferenceItemCell" forIndexPath:indexPath];
    
    NSInteger index = self.pageSize*indexPath.section + indexPath.row;
    PreferenceItemModel* model = self.model[index];
    [cell updateWithModel:model];
    
    @weakify(self)
    [cell setDidTapAction:^{
        @strongify(self)
        if(self.delegate && [self.delegate respondsToSelector:@selector(preferenceViewDidSelectItem:)]) {
            [self.delegate preferenceViewDidSelectItem:model];
        }
    }];
    
    return cell;
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath
{
}

@end
