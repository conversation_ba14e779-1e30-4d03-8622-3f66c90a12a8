//
//  PreferenceItemModel.m
//  PPBrowser
//
//  Created by qingbin on 2022/4/25.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "PreferenceItemModel.h"
#import "PreferenceManager.h"

@implementation PreferenceItemModel

//https://igoutu.cn/icon/581/%E6%A1%83%E5%BF%83
//+ (instancetype)addBookMarkItem
//{
//    //bookmark_remove_icon
//    PreferenceItemModel* item = [PreferenceItemModel new];
//    //保留，用来计算宽度
//    item.title = NSLocalizedString(@"preference.add_bookmark", nil);
//    //这里特殊处理了,因此不会在这里设置图片
////    item.imageName = @"bookmark_add_icon";
//    item.itemType = PreferenceItemTypeAddBookMark;
//    item.shouldAffectByHomeURL = YES;
//    
//    return item;
//}

//https://igoutu.cn/icon/56393/%E6%94%B6%E8%97%8F%E5%A4%B9
+ (instancetype)bookMarkItem
{
    PreferenceItemModel* item = [PreferenceItemModel new];
    item.title = NSLocalizedString(@"preference.bookMark", nil);
//    item.imageName = @"bookmark_icon";
    //https://yesicon.app/fluent/star-20-regular
    item.imageName = @"tool_bookmark_icon";
    item.itemType = PreferenceItemTypeBookMark;
    
    return item;
}

//https://igoutu.cn/icons/set/clock
+ (instancetype)historyItem
{
    PreferenceItemModel* item = [PreferenceItemModel new];
    item.title = NSLocalizedString(@"preference.history", nil);
    //https://yesicon.app/fluent/clock-20-regular
    item.imageName = @"tool_clock_icon";
    item.itemType = PreferenceItemTypeHistory;
    
    return item;
}

//https://igoutu.cn/icon/37130/%E8%A7%A3%E9%94%81%E7%A7%81%E5%AF%86
//https://igoutu.cn/icon/2985/%E7%A7%81%E4%BA%BA2

//动物脚印
//https://igoutu.cn/icon/25519/%E5%AE%A0%E7%89%A9%E5%91%BD%E4%BB%A4%E5%8F%AC%E5%94%A4
//https://igoutu.cn/icon/117106/%E5%AE%A0%E7%89%A9%E5%91%BD%E4%BB%A4%E8%A7%A3%E9%9B%87
+ (instancetype)privacyItem
{
    PreferenceItemModel* item = [PreferenceItemModel new];
    item.title = NSLocalizedString(@"preference.privacyMode", nil);
    
//    BOOL isPrivate = [[PreferenceManager shareInstance].items.isPrivate boolValue];
    //https://yesicon.app/fluent/incognito-20-regular
    item.imageName = @"tool_incognito_icon";

    item.itemType = PreferenceItemTypePrivacy;
    //开关型
    item.isSwitchOn = @(YES);
    
    return item;
}

//内边距15%
+ (instancetype)jsItem
{
    PreferenceItemModel* item = [PreferenceItemModel new];
    item.title = NSLocalizedString(@"preference.jsscript", nil);
    //https://yesicon.app/fluent/code-20-regular
    item.imageName = @"tool_js_icon";
    item.itemType = PreferenceItemTypeJS;
    
    return item;
}

//https://igoutu.cn/icons/set/computer-and-phone
+ (instancetype)UAItem
{
    PreferenceItemModel* item = [PreferenceItemModel new];
    item.title = NSLocalizedString(@"preference.ua", nil);
    //https://yesicon.app/fluent/phone-desktop-20-regular
    item.imageName = @"tool_ua_icon";
    item.itemType = PreferenceItemTypeUA;
    
    return item;
}

+ (instancetype)shareItem
{
    PreferenceItemModel* item = [PreferenceItemModel new];
    item.title = NSLocalizedString(@"preference.share", nil);
    //https://www.iconfont.cn/collections/detail?spm=a313x.icontype_histories.0.da5a778a4.11333a81jHrdp8&cid=22144
    item.imageName = @"tool_share_icon";
    item.itemType = PreferenceItemTypeShare;
    item.shouldAffectByHomeURL = YES;
    
    return item;
}

+ (instancetype)settingItem
{
    //https://yesicon.app/lets-icons/setting-alt-line-light
    
    PreferenceItemModel* item = [PreferenceItemModel new];
    item.title = NSLocalizedString(@"preference.setting", nil);
    //https://yesicon.app/lets-icons/setting-alt-line-light
    item.imageName = @"tool_setting_icon";
    item.itemType = PreferenceItemTypeSetting;
    
    return item;
}

//https://igoutu.cn/icon/oTXorLE1Vwz3/%E6%B2%A1%E6%9C%89%E5%9B%BE%E5%83%8F
//内边距10%
+ (instancetype)noImageModeItem
{
    PreferenceItemModel* item = [PreferenceItemModel new];
    item.title = NSLocalizedString(@"preference.noimageMode", nil);
    //https://yesicon.app/circum/image-off
    item.imageName = @"tool_noimage_icon";
    item.itemType = PreferenceItemTypeNoImage;
    item.shouldAffectByHomeURL = YES;
    
    //开关型
    item.isSwitchOn = @(YES);
    
    return item;
}

//https://igoutu.cn/icon/tSKyyMXYaNRe/%E6%9F%A5%E7%9C%8B%E6%96%87%E4%BB%B6
+ (instancetype)findInPageItem
{
    PreferenceItemModel* item = [PreferenceItemModel new];
    item.title = NSLocalizedString(@"preference.findInPage", nil);
    //https://yesicon.app/fluent/screen-search-20-regular
    item.imageName = @"tool_findinpage_icon";
    item.itemType = PreferenceItemTypeFindInPage;
    item.shouldAffectByHomeURL = YES;
    
    return item;
}

//https://igoutu.cn/icon/set/%E5%9B%BE%E7%89%87/small
+ (instancetype)imageModeItem
{
    PreferenceItemModel* item = [PreferenceItemModel new];
    item.title = NSLocalizedString(@"preference.imageMode", nil);
    //https://yesicon.app/circum/image-on
    item.imageName = @"tool_image_icon";
    item.itemType = PreferenceItemTypeImageMode;
    item.shouldAffectByHomeURL = YES;
    
    return item;
}

+ (instancetype)translateItem
{
    //https://yesicon.app/fluent/translate-20-regular
    //0%
    PreferenceItemModel* item = [PreferenceItemModel new];
    item.title = NSLocalizedString(@"preference.translate", nil);
    item.imageName = @"tool_translate_icon";
    item.itemType = PreferenceItemTypeTranslate;
    item.shouldAffectByHomeURL = YES;
    
    return item;
}

+ (instancetype)screenshotItem
{
    PreferenceItemModel* item = [PreferenceItemModel new];
    item.title = NSLocalizedString(@"preference.screenshot", nil);
    //https://yesicon.app/pepicons-pencil/scissors
    item.imageName = @"tool_scissors_icon";
    item.itemType = PreferenceItemTypeScreenshot;
    item.shouldAffectByHomeURL = YES;
    
    return item;
}

+ (instancetype)nightModeItem
{
    PreferenceItemModel* item = [PreferenceItemModel new];
    item.title = NSLocalizedString(@"preference.darkMode", nil);
    //https://yesicon.app/ph/moon-light
    item.imageName = @"tool_dark_icon";
    item.itemType = PreferenceItemTypeNightMode;
    
    //开关型
    item.isSwitchOn = @(YES);
    
    return item;
}

//https://igoutu.cn/icons/set/refresh
+ (instancetype)refreshItem
{
    PreferenceItemModel* item = [PreferenceItemModel new];
    item.title = NSLocalizedString(@"preference.refresh", nil);
    //https://www.iconfont.cn/collections/detail?spm=a313x.search_index.0.da5a778a4.24243a81F5xr4B&cid=12177
    //https://yesicon.app/iconamoon/restart-thin
    //https://yesicon.app/fluent-mdl2/update-restore
    item.imageName = @"tool_refresh_icon";
    item.itemType = PreferenceItemTypeRefresh;
    item.shouldAffectByHomeURL = YES;
    
    return item;
}

+ (instancetype)fullScreenItem
{
    //5%的内边距
    PreferenceItemModel* item = [PreferenceItemModel new];
    item.title = NSLocalizedString(@"preference.fullscreenMode", nil);
    //https://yesicon.app/prime/expand
    //https://yesicon.app/fluent/full-screen-maximize-20-regular
    item.imageName = @"tool_fullscreen2_icon";
    item.itemType = PreferenceItemTypeFullScreen;
    
    //开关型
    item.isSwitchOn = @(YES);
    
    return item;
}

+ (instancetype)feedbackItem
{
    //10%的内边距
    PreferenceItemModel* item = [PreferenceItemModel new];
    item.title = NSLocalizedString(@"preference.feedback", nil);
    //https://yesicon.app/material-symbols-light/mail-outline
    item.imageName = @"tool_feedback_icon";
    item.itemType = PreferenceItemTypeFeedback;
    
    return item;
}

+ (instancetype)downloadItem
{
    //10%的内边距
    PreferenceItemModel* item = [PreferenceItemModel new];
    item.title = NSLocalizedString(@"common.download", nil);
    //https://yesicon.app/ph/arrow-fat-down
    item.imageName = @"tool_down_icon";
    item.itemType = PreferenceItemTypeDownload;
    
    return item;
}

+ (instancetype)tagitItem
{
    //2px内边距
    PreferenceItemModel* item = [PreferenceItemModel new];
    item.title = NSLocalizedString(@"preference.tagit", nil);
    //https://yesicon.app/ph/cursor-click-light
    item.imageName = @"tool_click_icon";
    item.itemType = PreferenceItemTypeTagit;
    item.shouldAffectByHomeURL = YES;
    
    return item;
}

+ (instancetype)toolboxItem
{
    PreferenceItemModel* item = [PreferenceItemModel new];
    item.title = NSLocalizedString(@"preference.toolbox", nil);
    item.imageName = @"tool_toolbox_icon";
    item.itemType = PreferenceItemTypeToolBox;
    item.shouldAffectByHomeURL = YES;
    
    return item;
}

+ (instancetype)autoPageItem
{
    PreferenceItemModel* item = [PreferenceItemModel new];
    item.title = NSLocalizedString(@"preference.autoPage", nil);
    //https://yesicon.app/fluent/document-page-break-20-regular
    item.imageName = @"tool_autopage_icon";
    item.itemType = PreferenceItemTypeAutoPage;
    
    return item;
}

@end
