//
//  PreferenceItemModel.h
//  PPBrowser
//
//  Created by qing<PERSON> on 2022/4/25.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "PPEnums.h"

NS_ASSUME_NONNULL_BEGIN

//icon网站
//https://www.zhihu.com/question/19857245/answers/updated
@interface PreferenceItemModel : NSObject

@property (nonatomic, strong) NSString *imageName;

@property (nonatomic, strong) NSString *title;

@property (nonatomic, assign) PreferenceItemType itemType;
//是否是首页
@property (nonatomic, assign) BOOL isHomeURL;
//是否受到首页的影响
@property (nonatomic, assign) BOOL shouldAffectByHomeURL;
//是否是开关型选项
@property (nonatomic, strong) NSNumber* isSwitchOn;
//是否受到版权影响
@property (nonatomic, assign) BOOL isValidCopyright;
//是否已经添加到主页或者书签
//@property (nonatomic, assign) BOOL isAddBookMark;

//辅助字段,主要是卡片化
@property (nonatomic, assign) BOOL isFirstInSection;
@property (nonatomic, assign) BOOL isLastInSection;

//+ (instancetype)addBookMarkItem;

+ (instancetype)bookMarkItem;

+ (instancetype)historyItem;

+ (instancetype)privacyItem;

+ (instancetype)jsItem;

+ (instancetype)UAItem;

+ (instancetype)settingItem;

+ (instancetype)noImageModeItem;

+ (instancetype)findInPageItem;

+ (instancetype)imageModeItem;

+ (instancetype)translateItem;

+ (instancetype)screenshotItem;

+ (instancetype)nightModeItem;

+ (instancetype)refreshItem;

+ (instancetype)fullScreenItem;

+ (instancetype)feedbackItem;

+ (instancetype)tagitItem;

+ (instancetype)shareItem;

+ (instancetype)toolboxItem;

+ (instancetype)autoPageItem;

+ (instancetype)downloadItem;

@end

NS_ASSUME_NONNULL_END
