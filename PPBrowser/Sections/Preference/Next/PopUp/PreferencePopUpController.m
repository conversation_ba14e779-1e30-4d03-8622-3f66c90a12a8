//
//  PreferencePopUpController.m
//  PPBrowser
//
//  Created by qingbin on 2024/3/26.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import "PreferencePopUpController.h"
#import "PreferenceContentView.h"

#import "ReactiveCocoa.h"
#import "Masonry.h"

#import "PreferenceContentView.h"
#import "NSObject+Helper.h"
#import "UIColor+Helper.h"

#import "BrowserViewController+PreferenceViewDelegate.h"

@interface PreferencePopUpController ()

@property (strong, nonatomic) PreferenceContentView* preferenceView;

@end

@implementation PreferencePopUpController

- (void)viewDidLoad
{
    [super viewDidLoad];
    
    [self addSubviews];
    [self defineLayout];

    [self applyTheme];
}

- (void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:animated];
    
    [self.preferenceView reloadData];
}

+ (PreferencePopUpController *)showAt:(BrowserViewController *)controller sourceRect:(CGRect)sourceRect
{
    PreferencePopUpController* vc = [PreferencePopUpController new];
    vc.modalPresentationStyle = UIModalPresentationPopover;
    vc.popoverPresentationController.sourceView = controller.view;
    vc.popoverPresentationController.sourceRect = sourceRect;
    vc.popoverPresentationController.permittedArrowDirections = UIPopoverArrowDirectionAny;
    
    vc.popoverPresentationController.delegate = vc;
    vc.preferenceView.delegate = controller;
    
    [controller presentViewController:vc animated:YES completion:nil];
    
    return vc;
}

- (void)showAt:(BrowserViewController *)controller sourceRect:(CGRect)sourceRect
{
    self.modalPresentationStyle = UIModalPresentationPopover;
    self.popoverPresentationController.sourceView = controller.view;
    self.popoverPresentationController.sourceRect = sourceRect;
    self.popoverPresentationController.permittedArrowDirections = UIPopoverArrowDirectionAny;
    
    self.popoverPresentationController.delegate = self;
    self.preferenceView.delegate = controller;
    
    [controller presentViewController:self animated:YES completion:nil];
}

- (CGSize)preferredContentSize
{
    return [PreferencePopUpController popUpSize];
}

+ (CGSize)popUpSize
{
    float width = 350;
    if([BrowserUtils isiPhone]) {
        //横屏iPhone
    } else {
        //iPad或者其他
        width = 580;
    }
    
    UIWindow* window = [NSObject normalWindow];
    float height = [PreferenceContentView heightWithContentWidth:width] - window.safeAreaInsets.bottom - 10;
    
    return CGSizeMake(width, height);
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
    } else {
        self.view.backgroundColor = [UIColor colorWithHexString:@"#ffffff"];
    }
    
    [self.preferenceView applyTheme];
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    //只有当前的controller会触发一次, 其它已存在的controller走通知
    if(needReload) {
        //修改暗黑模式
        [self applyTheme];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
    
    [self.preferenceView reloadData];
}

#pragma mark -- layout

- (void)addSubviews
{
    [self.view addSubview:self.preferenceView];
}

- (void)defineLayout
{
    [self.preferenceView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.view);
        make.centerY.mas_offset(0);
        make.height.mas_equalTo([PreferencePopUpController popUpSize].height-20);
    }];
}

#pragma mark -- UIPopoverPresentationControllerDelegate

- (UIModalPresentationStyle)adaptivePresentationStyleForPresentationController:(UIPresentationController *)controller
{
    return UIModalPresentationNone;
}

#pragma mark -- Getters

- (PreferenceContentView *)preferenceView
{
    if(!_preferenceView) {
        _preferenceView = [[PreferenceContentView alloc]initWithContentWidth:[PreferencePopUpController popUpSize].width];
    }
    
    return _preferenceView;
}

@end
