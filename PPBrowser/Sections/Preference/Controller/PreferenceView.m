//
//  PreferenceView.m
//  PPBrowser
//
//  Created by qingbin on 2022/4/25.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "PreferenceView.h"

#import "MaizyHeader.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "NSObject+Helper.h"

#import "XLScrollView.h"
#import "PreferenceContentView.h"

#import "ThemeProtocol.h"
#import "PreferenceManager.h"
#import "PPNotifications.h"

@interface PreferenceView ()<UIScrollViewDelegate,ThemeProtocol>
/// 底部搜索框交互父类
@property (strong, nonatomic) XLScrollView *scrollView;
/// 内容页
@property (strong, nonatomic) UIView* contentView;
///
@property (assign, nonatomic) double bottomBarHeight;
///
@property (assign, nonatomic) double contentOffsetMinY;

@property (assign, nonatomic) double contentOffsetMidY;
//
@property (strong, nonatomic) UIView* masView;
// 是否主scrollView滚动
@property (assign, nonatomic) BOOL isMainScroll;

@end

@implementation PreferenceView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if(self) {
        [self addSubviews];
        [self defineLayout];
        [self setupObservers];
        self.backgroundColor = [UIColor.blackColor colorWithAlphaComponent:0.3];
        self.layer.opacity = 0;
        
        self.isMainScroll = YES;
        
        // 初始化位置
        CGPoint offset = self.scrollView.contentOffset;
        offset.y = self.contentOffsetMinY;
        self.scrollView.contentOffset = offset;
        
        [self applyTheme];
    }
    
    return self;
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.contentView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
    } else {
        self.contentView.backgroundColor = UIColor.whiteColor;
    }
    
    [self.preferenceView applyTheme];
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
    [self.preferenceView reloadData];
}

- (void)show
{
    [self.preferenceView reloadData];
    
    self.layer.opacity = 0;
    [UIView animateWithDuration:0.25 animations:^{
        self.layer.opacity = 1;
        CGPoint offset = self.scrollView.contentOffset;
        offset.y = self.contentOffsetMidY;
        self.scrollView.contentOffset = offset;
    }];
}

- (void)hide
{
    self.layer.opacity = 1;
    [UIView animateWithDuration:0.25 animations:^{
        self.layer.opacity = 0;
        CGPoint offset = self.scrollView.contentOffset;
        offset.y = self.contentOffsetMinY;
        self.scrollView.contentOffset = offset;
    } completion:^(BOOL finished) {
        [self removeFromSuperview];
    }];
}

- (CAShapeLayer*)createMaskWith:(CGRect)boundRect radius:(float)radius
{
    UIBezierPath *path = [UIBezierPath bezierPathWithRoundedRect:boundRect
                                               byRoundingCorners:UIRectCornerTopLeft|UIRectCornerTopRight
                                                     cornerRadii:CGSizeMake(radius,radius)];
    CAShapeLayer *mask = [CAShapeLayer layer];
    mask.path = path.CGPath;
    
    return mask;
}

- (void)addSubviews
{
    [self addSubview:self.scrollView];
    [self.scrollView addSubview:self.contentView];
    [self.contentView addSubview:self.preferenceView];
    [self addSubview:self.masView];
}

- (void)defineLayout
{
    [self.masView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.right.mas_offset(0);
        make.bottom.equalTo(self.contentView.mas_top);
    }];
    
    [self.preferenceView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.contentView);
        make.top.mas_offset(20);
        make.bottom.equalTo(self).priorityHigh(); //隐藏的时候,去除masonry警告
    }];
}

- (void)setupObservers
{
    UITapGestureRecognizer* tap = [UITapGestureRecognizer new];
    [self.masView addGestureRecognizer:tap];
    @weakify(self)
    [tap.rac_gestureSignal subscribeNext:^(id x) {
        @strongify(self)
        [self hide];
    }];
    
//    self.selectView.scrollDidScroll = ^(UIScrollView *scrollView) {
//        @strongify(self)
//        //tableView
//        if(!self.isMainScroll) {
//            if(scrollView.contentOffset.y <= 0) {
//                //往下拉, 到达极限位置
//                CGPoint offset = scrollView.contentOffset;
//                offset.y = 0;
//                scrollView.contentOffset = offset;
//
//                self.isMainScroll = YES;
//            }
//        }
//    };
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(darkThemeDidChangeNotification:)
                                                 name:kDarkThemeDidChangeNotification
                                               object:nil];
}

#pragma mark - UIScrollViewDelegate
- (void)scrollViewDidScroll:(UIScrollView *)scrollView
{
    //scrollView-contentOffset往上越来越大, 往下越来越小
    //这里在竖直方向上,只有主控,如果非主控,那么下拉的时候会表现非常奇怪,没有顺滑的交互效果
    if(self.isMainScroll) {
        if(scrollView == self.scrollView) {
            if(scrollView.contentOffset.y > self.contentOffsetMidY) {
                //往上拉
                CGPoint offset = scrollView.contentOffset;
                offset.y = self.contentOffsetMidY;
                scrollView.contentOffset = offset;
                //已到极限位置,变内容页滑动
//                self.isMainScroll = NO;
            } else if(scrollView.contentOffset.y < self.contentOffsetMinY) {
                //往下拉,收起
                CGPoint offset = scrollView.contentOffset;
                offset.y = self.contentOffsetMinY;
                scrollView.contentOffset = offset;
            }
        }
    } else {
        //非主控, 保持位置
        CGPoint offset = scrollView.contentOffset;
        offset.y = self.contentOffsetMidY;
        scrollView.contentOffset = offset;
    }
}

- (void)scrollViewWillEndDragging:(UIScrollView *)scrollView
                     withVelocity:(CGPoint)velocity
              targetContentOffset:(inout CGPoint *)targetContentOffset
{
    if(self.scrollView != scrollView) return;
    
    float currentOffsetY = scrollView.contentOffset.y;
    float gotoPointY = currentOffsetY;
    
    if(currentOffsetY < self.contentOffsetMidY
       && currentOffsetY > self.contentOffsetMinY) {
        if(velocity.y > 0) {
            gotoPointY = self.contentOffsetMidY;
        } else if(velocity.y < 0) {
            gotoPointY = self.contentOffsetMinY;
        }
    } else {
        if(velocity.y < 0) {
            gotoPointY = self.contentOffsetMinY;
        }
    }

    if(velocity.y == 0) {
        float distance = self.contentOffsetMidY;
        NSArray* locations = @[@(self.contentOffsetMinY),@(self.contentOffsetMidY)];
        for(NSNumber* item in locations) {
            float location = item.floatValue;
            float temp = fabs(location-currentOffsetY);
            if(distance > temp) {
                distance = temp;
                gotoPointY = location;
            }
        }
    }
        
    [self moveToScrollView:scrollView velocity:velocity.y movePointY:gotoPointY];
    *targetContentOffset = CGPointMake(0, gotoPointY);
}

- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView
{
    if(scrollView != self.scrollView) return;
    
    float currentOffsetY = scrollView.contentOffset.y;
    NSArray* locations = @[@(self.contentOffsetMinY),@(self.contentOffsetMidY)];
    if(![locations containsObject:@(currentOffsetY)]) {
        [self moveToScrollView:scrollView velocity:8 movePointY:self.contentOffsetMidY];
    }
}

- (void)moveToScrollView:(UIScrollView*)scrollView velocity:(float)velocity movePointY:(float)movePointY
{
    [UIView animateWithDuration:0.25 delay:0 usingSpringWithDamping:0.9 initialSpringVelocity:velocity options:UIViewAnimationOptionCurveEaseOut animations:^{
        if(movePointY == self.contentOffsetMinY) {
            self.layer.opacity = 0;
        }
        CGPoint offset = scrollView.contentOffset;
        offset.y = movePointY;
        scrollView.contentOffset = offset;
    } completion:^(BOOL finished) {
        if(movePointY == self.contentOffsetMinY) {
           [self removeFromSuperview];
        }
    }];
}


- (XLScrollView *)scrollView
{
    if(!_scrollView) {
        float height = kScreenHeight;
        
        _scrollView = [[XLScrollView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, height)];
        _scrollView.alwaysBounceVertical = YES;
        _scrollView.showsVerticalScrollIndicator = NO;
        _scrollView.showsHorizontalScrollIndicator = NO;
        _scrollView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
        _scrollView.delegate = self;
        _scrollView.clipsToBounds = NO;
        _scrollView.backgroundColor = UIColor.clearColor;
        _scrollView.contentSize = CGSizeMake(kScreenWidth, height*2);
    }

    return _scrollView;
}

- (UIView *)contentView
{
    if(!_contentView) {
        float height = [self contentOffsetMidY];
        float offsetY = kScreenHeight;
        _contentView = [[UIView alloc]initWithFrame:CGRectMake(0, offsetY, kScreenWidth, height)];
        _contentView.layer.mask = [self createMaskWith:_contentView.bounds radius:15];
        
        _contentView.backgroundColor = UIColor.whiteColor;//要在这里设置,否则无效
    }
    
    return _contentView;
}

- (PreferenceContentView *)preferenceView
{
    if(!_preferenceView) {
        _preferenceView = [[PreferenceContentView alloc]initWithContentWidth:kScreenWidth];
    }
    
    return _preferenceView;
}

- (UIView *)masView
{
    if(!_masView) {
        _masView = [UIView new];
        _masView.backgroundColor = UIColor.clearColor;
    }
    
    return _masView;
}

- (double)bottomBarHeight
{
    if(_bottomBarHeight <= 0) {
        _bottomBarHeight = 0/*最小高度*/;
    }
    
    return _bottomBarHeight;
}

- (double)contentOffsetMidY
{
    if(_contentOffsetMidY <= 0) {
        float height = [PreferenceContentView heightWithContentWidth:kScreenWidth];
        _contentOffsetMidY = height/*偏离最小高度多少*/;
    }
    
    return _contentOffsetMidY;
}

- (double)contentOffsetMinY
{
    return 0;
}

@end
