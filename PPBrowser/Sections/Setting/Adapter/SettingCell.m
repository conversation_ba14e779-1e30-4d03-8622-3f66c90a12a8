//
//  SettingCell.m
//  PPBrowser
//
//  Created by qingbin on 2022/6/3.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "SettingCell.h"

#import "Masonry.h"
#import "MaizyHeader.h"
#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "ReactiveCocoa.h"
#import "UIView+FrameHelper.h"
#import "UIViewController+Helper.h"
#import "NSObject+Helper.h"

#import "ThemeProtocol.h"
#import "PreferenceManager.h"
#import "BrowserUtils.h"
#import "CommonTextAndArrowView.h"

@interface SettingCell ()<ThemeProtocol>
//
@property (nonatomic, strong) UIView *backView;
//
@property (nonatomic, strong) CommonTextAndArrowView *componentView;
//
@property (nonatomic, strong) SettingModel *model;

@end

@implementation SettingCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        
        [self addSubviews];
        [self defineLayout];
    }
    
    return self;
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
    } else {
        self.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
}

- (void)updateWithModel:(SettingModel *)model
{
    self.model = model;
        
    BOOL showLine = YES;
    if(self.model.isFirstInSection && self.model.isLastInSection) {
        showLine = NO;
    } else if(self.model.isLastInSection) {
        showLine = NO;
    }
    
    [self.componentView updateWithTitle:model.title content:model.content icon:model.imageName iconBackgroundColor:model.imageBackgroundColor showLine:showLine];
    
    [self updateCornerRadius];
    [self applyTheme];
}

- (void)updateCornerRadius
{
    // 圆角
    self.backView.layer.cornerRadius = iPadValue(20, 10);
    self.backView.layer.masksToBounds = YES;
    
    if(self.model.isFirstInSection && self.model.isLastInSection) {
        //只有一个
        self.backView.layer.masksToBounds = YES;
        self.backView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner | kCALayerMinXMaxYCorner | kCALayerMaxXMaxYCorner;
    } else {
        if(self.model.isFirstInSection) {
            //添加上圆角
            self.backView.layer.masksToBounds = YES;
            self.backView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
        } else if(self.model.isLastInSection) {
            //添加下圆角
            self.backView.layer.masksToBounds = YES;
            self.backView.layer.maskedCorners = kCALayerMinXMaxYCorner | kCALayerMaxXMaxYCorner;
        } else {
            self.backView.layer.masksToBounds = NO;
            self.backView.layer.cornerRadius = 0;
        }
    }
}

- (void)addSubviews
{
    [self.contentView addSubview:self.backView];
    [self.backView addSubview:self.componentView];
}

- (void)defineLayout
{
    float height = iPadValue(88, 60);
    float offset = iPadValue(30, 15);
    [self.backView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(offset);
        make.right.mas_offset(-offset);
        make.top.bottom.equalTo(self.contentView);
        make.height.mas_equalTo(height);
    }];
    
    [self.componentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.backView);
    }];
}

- (UIView *)backView
{
    if(!_backView) {
        _backView = [UIView new];
        _backView.backgroundColor = UIColor.whiteColor;
    }
    
    return _backView;
}

- (CommonTextAndArrowView *)componentView
{
    if (!_componentView) {
        _componentView = [CommonTextAndArrowView new];
        _componentView.userInteractionEnabled = NO;
    }
    
    return _componentView;
}

@end
