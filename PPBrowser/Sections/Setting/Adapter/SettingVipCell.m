//
//  SettingVipCell.m
//  PPBrowser
//
//  Created by qingbin on 2025/3/6.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "SettingVipCell.h"


#import "Masonry.h"
#import "MaizyHeader.h"
#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "ReactiveCocoa.h"
#import "UIView+FrameHelper.h"
#import "UIViewController+Helper.h"
#import "NSObject+Helper.h"

#import "ThemeProtocol.h"
#import "PreferenceManager.h"
#import "BrowserUtils.h"
#import "PaymentManager.h"

@interface SettingVipCell ()<ThemeProtocol>
//
@property (nonatomic, strong) UIView *containerView;
@property (nonatomic, strong) CAGradientLayer *gradientLayer;
@property (nonatomic, strong) UIView *iconContainer;
@property (nonatomic, strong) UIImageView *crownIcon;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *descLabel;
@property (nonatomic, strong) UIImageView *arrowIcon;
// 未开通提示
@property (nonatomic, strong) UILabel *warningLabel;

@end

@implementation SettingVipCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        
        [self addSubviews];
        [self defineLayout];
        [self applyTheme];
    }
    
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    // 更新渐变层大小
    self.gradientLayer.frame = self.containerView.bounds;
}

- (void)updateWithModel
{
    BOOL isVip = [[PaymentManager shareInstance] isVip];
    self.warningLabel.hidden = isVip;
    [self applyTheme];
}

#pragma mark -- 夜间模式

- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
    } else {
        self.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
}

#pragma mark - layout

- (void)addSubviews
{
    // 容器视图
    self.containerView = [[UIView alloc] init];
    self.containerView.layer.cornerRadius = 14.0;
    self.containerView.layer.masksToBounds = YES;;
    [self addSubview:self.containerView];
    
    // 渐变背景
    self.gradientLayer = [CAGradientLayer layer];
    self.gradientLayer.colors = @[
        (id)[UIColor colorWithRed:0.227 green:0.482 blue:0.835 alpha:1.0].CGColor,
        (id)[UIColor colorWithRed:0.0 green:0.824 blue:1.0 alpha:1.0].CGColor
    ];
    self.gradientLayer.startPoint = CGPointMake(0.0, 0.0);
    self.gradientLayer.endPoint = CGPointMake(1.0, 1.0);
    [self.containerView.layer insertSublayer:self.gradientLayer atIndex:0];
    
    // 图标容器
    self.iconContainer = [[UIView alloc] init];
    self.iconContainer.backgroundColor = [UIColor colorWithWhite:1.0 alpha:0.9];
    self.iconContainer.layer.cornerRadius = 12.0;
    self.iconContainer.layer.shadowColor = [UIColor blackColor].CGColor;
    self.iconContainer.layer.shadowOffset = CGSizeMake(0, 2);
    self.iconContainer.layer.shadowOpacity = 0.1;
    self.iconContainer.layer.shadowRadius = 3.0;
    [self.containerView addSubview:self.iconContainer];
    
    // 皇冠图标
    self.crownIcon = [[UIImageView alloc] init];
    self.crownIcon.contentMode = UIViewContentModeScaleAspectFit;
    self.crownIcon.tintColor = [UIColor colorWithRed:0.227 green:0.482 blue:0.835 alpha:1.0];
    self.crownIcon.image = [[UIImage imageNamed:@"setting_crown"] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
    [self.iconContainer addSubview:self.crownIcon];
    
    // 标题
    self.titleLabel = [[UILabel alloc] init];
    self.titleLabel.text = NSLocalizedString(@"setting.vipTitle", nil);
    self.titleLabel.textColor = [UIColor whiteColor];
    self.titleLabel.font = [UIFont systemFontOfSize:iPadValue(20, 16) weight:UIFontWeightSemibold];
    [self.containerView addSubview:self.titleLabel];
    
    // 描述
    self.descLabel = [[UILabel alloc] init];
    self.descLabel.text = NSLocalizedString(@"setting.vip.detail", nil);
    self.descLabel.textColor = [UIColor colorWithWhite:1.0 alpha:0.9];
    self.descLabel.font = [UIFont systemFontOfSize:iPadValue(14, 12) weight:UIFontWeightRegular];
    [self.containerView addSubview:self.descLabel];
    
    // 右箭头
    self.arrowIcon = [[UIImageView alloc] init];
    self.arrowIcon.contentMode = UIViewContentModeScaleAspectFit;
    self.arrowIcon.tintColor = [UIColor colorWithWhite:1.0 alpha:0.9];
    self.arrowIcon.image = [[UIImage imageNamed:@"standard_right_arrow"] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
    [self.containerView addSubview:self.arrowIcon];
    
    self.warningLabel = [UILabel new];
    self.warningLabel.text = NSLocalizedString(@"vip.not.actived", nil);
    self.warningLabel.textColor = UIColor.whiteColor;
    self.warningLabel.font = [UIFont systemFontOfSize:iPadValue(20, 16) weight:UIFontWeightMedium];
    self.warningLabel.hidden = YES;
    [self.containerView addSubview:self.warningLabel];
}

- (void)defineLayout {
    float height = iPadValue(90, 76);
    float offset = iPadValue(30, 15);
    
    // 容器视图约束
    [self.containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(offset);
        make.right.mas_offset(-offset);
        make.top.bottom.equalTo(self.contentView);
        make.height.mas_equalTo(height);
    }];
    
    // 图标容器约束
    [self.iconContainer mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.containerView).offset(offset);
        make.centerY.equalTo(self.containerView);
        make.width.height.mas_equalTo(iPadValue(48, 38));
    }];
    
    // 皇冠图标约束
    [self.crownIcon mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.iconContainer);
        make.width.height.mas_equalTo(iPadValue(24, 20));
    }];
    
    // 标题约束
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.iconContainer.mas_right).offset(offset);
        make.bottom.equalTo(self.containerView.mas_centerY).offset(-iPadValue(3, 2));
    }];
    
    // 描述约束
    [self.descLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.titleLabel);
        make.top.equalTo(self.containerView.mas_centerY).offset(iPadValue(3, 2));
    }];
    
    // 未开通提示
    [self.warningLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.arrowIcon.mas_left).offset(-iPadValue(8, 5));
        make.centerY.equalTo(self.containerView);
    }];
    
    // 右箭头约束
    float size = iPadValue(15, 10);
    [self.arrowIcon mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.containerView).offset(-offset);
        make.centerY.equalTo(self.containerView);
        make.size.mas_equalTo(size);
    }];
}

@end
