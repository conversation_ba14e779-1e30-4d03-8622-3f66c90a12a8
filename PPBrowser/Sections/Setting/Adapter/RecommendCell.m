//
//  RecommendCell.m
//  PPBrowser
//
//  Created by qingbin on 2024/4/5.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import "RecommendCell.h"

#import "Masonry.h"
#import "MaizyHeader.h"
#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "ReactiveCocoa.h"
#import "UIView+FrameHelper.h"
#import "UIViewController+Helper.h"
#import "NSObject+Helper.h"

#import "ThemeProtocol.h"
#import "PreferenceManager.h"
#import "BrowserUtils.h"

@interface RecommendCell ()<ThemeProtocol>

@property (nonatomic, strong) UIView* backView;

@property (nonatomic, strong) UIImageView* logo;

@property (nonatomic, strong) UIStackView* stackView;

@property (nonatomic, strong) UILabel* titleLabel;

@property (nonatomic, strong) UILabel* contentLabel;

@property (nonatomic, strong) UIView* line;

@property (nonatomic, strong) SettingModel *model;

@end

@implementation RecommendCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        
        [self addSubviews];
        [self defineLayout];
    }
    
    return self;
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.backView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        self.titleLabel.textColor = [UIColor colorWithHexString:kDarkThemeColorText];
        self.contentLabel.textColor = [UIColor colorWithHexString:kDarkThemeColorText];
        self.line.backgroundColor = [UIColor colorWithHexString:kDarkThemeColorLine];
    } else {
        self.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.backView.backgroundColor = UIColor.whiteColor;
        self.titleLabel.textColor = [UIColor colorWithHexString:@"#333333"];
        self.contentLabel.textColor = [UIColor colorWithHexString:@"#666666"];
        self.line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
}

- (void)updateWithModel:(SettingModel*)model
{
    self.model = model;
    
    self.logo.image = [UIImage imageNamed:model.imageName];

    self.titleLabel.text = model.title;
    self.contentLabel.text = model.content;
    
    [self updateCornerRadius];
    [self applyTheme];
}

- (void)updateCornerRadius
{
    // 圆角
    self.line.hidden = NO;
    self.backView.layer.cornerRadius = iPadValue(20, 10);
    self.backView.layer.masksToBounds = YES;
    
    if(self.model.isFirstInSection && self.model.isLastInSection) {
        //只有一个
        self.backView.layer.masksToBounds = YES;
        self.backView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner | kCALayerMinXMaxYCorner | kCALayerMaxXMaxYCorner;
        self.line.hidden = YES;
    } else {
        if(self.model.isFirstInSection) {
            //添加上圆角
            self.backView.layer.masksToBounds = YES;
            self.backView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
        } else if(self.model.isLastInSection) {
            //添加下圆角
            self.backView.layer.masksToBounds = YES;
            self.backView.layer.maskedCorners = kCALayerMinXMaxYCorner | kCALayerMaxXMaxYCorner;
            self.line.hidden = YES;
        } else {
            self.backView.layer.masksToBounds = NO;
            self.backView.layer.cornerRadius = 0;
        }
    }
}

- (void)addSubviews
{
    [self.contentView addSubview:self.backView];
    [self.backView addSubview:self.logo];
    [self.backView addSubview:self.stackView];
}

- (void)defineLayout
{
    float height = iPadValue(88, 60);
    float offset = iPadValue(30, 15);
    [self.backView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(offset);
        make.right.mas_offset(-offset);
        make.top.bottom.equalTo(self.contentView);
        make.height.mas_equalTo(height);
    }];
    
    offset = iPadValue(30, 15);
    [self.logo mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(offset);
        make.centerY.equalTo(self.backView);
        make.size.mas_equalTo(iPadValue(50, 40));
    }];
        
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_offset(0);
        make.left.equalTo(self.logo.mas_right).mas_offset(iPadValue(20, 10));
        make.right.mas_lessThanOrEqualTo(0);
    }];
    
    UIView* line = [UIView new];
    line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    [self.backView addSubview:line];
    [line mas_makeConstraints:^(MASConstraintMaker *make) {
       make.left.equalTo(self.backView);
       make.right.mas_offset(-0);
       make.height.mas_equalTo(0.5);
       make.bottom.mas_offset(0);
    }];
    self.line = line;
}

#pragma mark -- Getters

- (UIView *)backView
{
    if(!_backView) {
        _backView = [UIView new];
    }
    
    return _backView;
}


- (UIStackView *)stackView
{
    if(!_stackView) {
        _stackView = [[UIStackView alloc]initWithArrangedSubviews:@[
            self.titleLabel,
            self.contentLabel
        ]];
        
        _stackView.axis = UILayoutConstraintAxisVertical;
        _stackView.spacing = iPadValue(8, 3);
    }
    
    return _stackView;
}

- (UILabel *)titleLabel
{
    if(!_titleLabel) {
        float font = iPadValue(20, 16);
        _titleLabel = [UIView createLabelWithTitle:nil
                                        textColor:[UIColor colorWithHexString:@"#333333"]
                                          bgColor:UIColor.clearColor
                                         fontSize:font
                                    textAlignment:NSTextAlignmentLeft
                                            bBold:NO];
    }
    
    return _titleLabel;
}

- (UILabel *)contentLabel
{
    if(!_contentLabel) {
        float font = iPadValue(18, 13);
        _contentLabel = [UIView createLabelWithTitle:nil
                                        textColor:[UIColor colorWithHexString:@"#666666"]
                                          bgColor:UIColor.clearColor
                                         fontSize:font
                                    textAlignment:NSTextAlignmentRight
                                            bBold:NO];
    }
    
    return _contentLabel;
}

- (UIImageView *)logo
{
    if(!_logo) {
        _logo = [UIImageView new];
        
        _logo.layer.cornerRadius = 10;
        _logo.layer.masksToBounds = YES;
    }
    
    return _logo;
}

@end
