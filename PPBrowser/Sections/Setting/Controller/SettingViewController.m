//
//  SettingViewController.m
//  PPBrowser
//
//  Created by qingbin on 2022/4/30.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "SettingViewController.h"

#import "Masonry.h"
#import "MaizyHeader.h"
#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "ReactiveCocoa.h"
#import "UIView+FrameHelper.h"
#import "UIViewController+Helper.h"
#import "NSObject+Helper.h"

#import "SettingCell.h"
#import "SettingModel.h"

#import "HomeSettingController.h"
#import "WebSettingController.h"
#import "AboutUsController.h"
#import "GeneralSettingController.h"
#import "iCloudSettingController.h"

#import "WebViewController.h"
#import "VIPController.h"
#import "BaseNavigationController.h"

#import "ThemeProtocol.h"
#import "PreferenceManager.h"

#import "PaymentManager.h"
#import "BrowserUtils.h"

#import "GuidelineController.h"
#import "AdBlockHomeController.h"
#import "DataClearViewController.h"
#import "TranslateSettingController.h"
#import "PhotoBrowserSettingController.h"
#import "AlertJumpViewController.h"
#import "AutoPageSettingViewController.h"
#import "TabSettingViewController.h"
#import "ToolbarConfigViewController.h"

#import "PPNotifications.h"
#import "RecommendCell.h"
#import "SettingVipCell.h"
#import "PaddingNewLabel.h"

#import "TempModel.h"
#import "SearchSettingViewController.h"

@interface SettingViewController () <UITableViewDelegate, UITableViewDataSource, ThemeProtocol>

@property (nonatomic, strong) UITableView* tableView;
//
@property (nonatomic, strong) NSMutableArray* model;
//section标题
@property (nonatomic, strong) NSMutableArray *sectionHeaderModel;

@end

@implementation SettingViewController

- (void)viewDidLoad
{
    [super viewDidLoad];

    self.navigationItem.title = NSLocalizedString(@"setting.title", nil);
    
    [self addSubviews];
    [self defineLayout];
    [self setupObservers];
    
    [self createCustomLeftBarButtonItem];
}

- (void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:animated];
    
    [self updateWithModel];
    [self applyTheme];
}

- (void)updateWithModel
{
    [self.model removeAllObjects];
    [self.sectionHeaderModel removeAllObjects];
    
    self.sectionHeaderModel = [@[
        @"", //vip
        NSLocalizedString(@"setting.basic.title", nil),
        NSLocalizedString(@"setting.advance.title", nil),
        NSLocalizedString(@"setting.support.title", nil),
        NSLocalizedString(@"setting.about.title", nil),
        NSLocalizedString(@"setting.more.apps", nil)
    ] mutableCopy];
    
    NSMutableArray* items = [NSMutableArray array];
    //第一部分
    [items addObject:[SettingModel vipSetting]];
    [self.model addObject:items];
        
    //第二部分
    items = [NSMutableArray array];
    [items addObject:[SettingModel generalSetting]];
    [items addObject:[SettingModel homeSetting]];
    [items addObject:[SettingModel webSetting]];
    [items addObject:[SettingModel searchSetting]];
    [items addObject:[SettingModel tabSetting]];
    [items addObject:[SettingModel toolbarConfigSetting]];
    [self.model addObject:items];
    
    //第三部分
    items = [NSMutableArray array];
    [items addObject:[SettingModel translateSetting]];
    [items addObject:[SettingModel autoPageSetting]];
    [items addObject:[SettingModel adBlockSetting]];
    [items addObject:[SettingModel alertJumpSetting]];
    [items addObject:[SettingModel dateClearSetting]];
    [items addObject:[SettingModel iCloudSetting]];
    [self.model addObject:items];
    
    //第四部分
    items = [NSMutableArray array];
    [items addObject:[SettingModel guidlelineSetting]];
    [items addObject:[SettingModel rate]];
    [items addObject:[SettingModel share]];
    [self.model addObject:items];
    
    //第五部分
    items = [NSMutableArray array];
    [items addObject:[SettingModel settingProtocol]];
    [items addObject:[SettingModel aboutUs]];
    [self.model addObject:items];
    
    //第六部分
    items = [NSMutableArray array];
    [items addObject:[SettingModel addonsRecommend]];
    [items addObject:[SettingModel qingyueRecommend]];
    [self.model addObject:items];
    
    for(NSArray* items in self.model) {
        SettingModel* item = items.firstObject;
        item.isFirstInSection = YES;
        item = items.lastObject;
        item.isLastInSection = YES;
    }
    
    [self.tableView reloadData];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
    } else {
        self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    //只有当前的controller会触发一次, 其它已存在的controller走通知
    if(needReload) {
        //修改暗黑模式
        [self applyTheme];
        [self updateWithModel];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
    [self updateWithModel];
}

- (void)setupObservers
{
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(updateWithModel)
                                                 name:kReloadVipNotification
                                               object:nil];
}

#pragma mark -- layout
- (void)addSubviews
{
    [self.view addSubview:self.tableView];
}

- (void)defineLayout
{
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(0);
        make.right.mas_offset(-0);
        make.top.equalTo(self.view).offset(0);
        make.bottom.mas_offset(0);
    }];
}

#pragma mark - UITableViewDataSource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    NSArray* data = self.model[section];
    return data.count;
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return self.model.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    NSArray* data = self.model[indexPath.section];
    SettingModel* model = data[indexPath.row];
    
    if(model.type == SettingTypeVIP) {
        SettingVipCell* cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass(SettingVipCell.class)];
        [cell updateWithModel];
        return cell;
    } else if(model.type == SettingTypeRecommend) {
        RecommendCell* cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass(RecommendCell.class)];
        [cell updateWithModel:model];
        return cell;
    } else {
        SettingCell* cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass(SettingCell.class)];
        [cell updateWithModel:model];
        return cell;
    }
}

#pragma mark - UITableViewDelegate
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    [tableView deselectRowAtIndexPath:indexPath animated:NO];
    
    NSArray* datas = self.model[indexPath.section];
    SettingModel* item = datas[indexPath.row];
    
    if(item.type == SettingTypeGeneralSetting) {
        GeneralSettingController* vc = [GeneralSettingController new];
        [self.navigationController pushViewController:vc animated:YES];
    } else if(item.type == SettingTypeHomeSetting) {
        HomeSettingController* vc = [HomeSettingController new];
        [self.navigationController pushViewController:vc animated:YES];
    } else if(item.type == SettingTypeWebSetting) {
        WebSettingController* vc = [WebSettingController new];
        [self.navigationController pushViewController:vc animated:YES];
    } else if(item.type == SettingTypeAboutUs) {
        //关于我们
        [self jumpToAboutUs];
    } else if(item.type == SettingTypeRate) {
        [self jumpToAppstore];
    } else if(item.type == SettingTypeShare) {
        [self shareToFriends];
    } else if(item.type == SettingTypeFeedback) {
        [self jumpToFeedback];
    } else if(item.type == SettingTypeProtocol) {
        [self jumpToProtocol];
    } else if(item.type == SettingTypeVIP) {
        [self jumpToVip];
    } else if(item.type == SettingTypeGuideline) {
        //用户指南
        GuidelineController* vc = [GuidelineController new];
        [self.navigationController pushViewController:vc animated:YES];
    } else if(item.type == SettingTypeAdBlock) {
        //广告过滤
        AdBlockHomeController* vc = [AdBlockHomeController new];
        [self.navigationController pushViewController:vc animated:YES];
    } else if(item.type == SettingTypeDataClear) {
        DataClearViewController* vc = [DataClearViewController new];
        [self.navigationController pushViewController:vc animated:YES];
    } else if(item.type == SettingTypeiCloud) {
        iCloudSettingController* vc = [iCloudSettingController new];
        [self.navigationController pushViewController:vc animated:YES];
    } else if(item.type == SettingTypeRecommend) {
        //跳转链接
        if([[UIApplication sharedApplication] canOpenURL:[NSURL URLWithString:item.link]]) {
            [[UIApplication sharedApplication] openURL:[NSURL URLWithString:item.link] options:@{} completionHandler:nil];
        }
    } else if(item.type == SettingTypeTranslate) {
        //翻译
        TranslateSettingController* vc = [TranslateSettingController new];
        [self.navigationController pushViewController:vc animated:YES];
    } else if(item.type == SettingTypeAutoPage) {
        //自动翻页
        AutoPageSettingViewController* vc = [AutoPageSettingViewController new];
        [self.navigationController pushViewController:vc animated:YES];
    } else if(item.type == SettingTypePhotoBrowser) {
        //看图模式
        PhotoBrowserSettingController* vc = [PhotoBrowserSettingController new];
        [self.navigationController pushViewController:vc animated:YES];
    } else if(item.type == SettingTypeExport) {
        //一键导出
//        [self _exportAll];
    } else if(item.type == SettingTypeAlertJump) {
        //屏蔽APP跳转
        AlertJumpViewController* vc = [AlertJumpViewController new];
        [self.navigationController pushViewController:vc animated:YES];
    } else if(item.type == SettingTypeTabSetting) {
        //标签页设置
        TabSettingViewController* vc = [TabSettingViewController new];
        [self.navigationController pushViewController:vc animated:YES];
    } else if(item.type == SettingTypeToolbarConfig) {
        //工具栏设置
        ToolbarConfigViewController* vc = [ToolbarConfigViewController new];
        [self.navigationController pushViewController:vc animated:YES];
    } else if (item.type == SettingTypeSearchSetting) {
        //v2.7.1 搜索设置
        SearchSettingViewController* vc = [SearchSettingViewController new];
        [self.navigationController pushViewController:vc animated:YES];
    }
}

#pragma mark -- 一键导出
//- (void)_exportAll
//{
//    [UIView showLoading:@"正在处理中"];
//    
//    [self _exportToFiles:[TempModel getAllFiles]];
//}
//
//- (void)_exportToFiles:(NSArray<NSString *> *)items
//{
//    ///var/mobile/Containers/Data/Application/A0526E47-FDCA-41E4-AD29-DBBD9D2B623D/Library/Focus/com.Daniels.Tiercel.Cache.f69740b2f1fb6af1e6859b98c6368f8d/Downloads/File/《绝区零》月城柳角色PV|鬼佑之人_哔哩哔哩_bilibili.mp4
//    
//    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
//        NSMutableArray* urlItems = [NSMutableArray array];
//        for(NSString* url in items) {
//            NSURL* URL = [NSURL fileURLWithPath:url];
//            [urlItems addObject:URL];
//        }
//        
//        dispatch_async(dispatch_get_main_queue(), ^{
//            if(urlItems.count == 0) {
//                [UIView showWarning:@"没有可导出的文件"];
//                return;
//            }
//            UIActivityViewController* vc = [[UIActivityViewController alloc]initWithActivityItems:urlItems applicationActivities:nil];
//            
//            if([BrowserUtils isiPad]) {
//                //iPad
//                vc.popoverPresentationController.sourceView = self.view;
//                vc.popoverPresentationController.sourceRect = CGRectMake(CGRectGetMidX(self.view.frame), CGRectGetMidY(self.view.frame), 0, 0);
//            }
//            
//            [vc setCompletionWithItemsHandler:^(UIActivityType __nullable activityType, BOOL completed, NSArray * __nullable returnedItems, NSError * __nullable activityError) {
//                if(!activityError && completed) {
//                    [UIView showSucceed:@"导出成功"];
//                } else {
//                    [UIView showFailed:@"导出失败"];
//                }
//            }];
//            
//            [self presentViewController:vc animated:YES completion:nil];
//        });
//    });
//}

- (void)jumpToVip
{
    VIPController* vc = [[VIPController alloc]init];
    [self.navigationController pushViewController:vc animated:YES];
}

- (void)jumpToAboutUs
{
    AboutUsController* vc = [AboutUsController new];
    [self.navigationController pushViewController:vc animated:YES];
}

- (void)jumpToProtocol
{
    WebViewController* vc = [[WebViewController alloc]initWithWebviewType:WebViewTypePush Title:NSLocalizedString(@"setting.privacyProtocol", nil)];
    NSURL* url = [NSURL URLWithString:NSLocalizedString(@"privacy.policy.fileUrl", nil)];
    NSURLRequest* request = [NSURLRequest requestWithURL:url];
    [vc loadRequest:request];
    
    vc.hidesBottomBarWhenPushed = YES;
    
    [self.navigationController pushViewController:vc animated:YES];
}

- (void)jumpToAppstore
{
    NSString* appId = kAppId;
    NSString *appUrl = [NSString stringWithFormat:@"itms-apps://itunes.apple.com/app/id%@?action=write-review",appId];
    if ([[UIApplication sharedApplication]canOpenURL:[NSURL URLWithString:appUrl]]) {
        [[UIApplication sharedApplication]openURL:[NSURL URLWithString:appUrl] options:@{} completionHandler:nil];
    }
}

- (void)shareToFriends
{
    NSString* appId = kAppId;
    NSString* url = [NSString stringWithFormat:@"https://apps.apple.com/app/id%@",appId];
    
    NSString* textToShare = NSLocalizedString(@"setting.textToShare", nil);
    UIImage *imageToShare = [UIImage imageNamed:@"focus_logo"];
    NSURL *urlToShare = [NSURL URLWithString:url];
    NSArray* items = @[urlToShare,textToShare,imageToShare];
    
    UIActivityViewController *vc = [[UIActivityViewController alloc] initWithActivityItems:items applicationActivities:nil];
    vc.completionWithItemsHandler = ^(UIActivityType __nullable activityType, BOOL completed, NSArray * __nullable returnedItems, NSError * __nullable activityError) {
    };
    
    if([BrowserUtils isiPad]) {
        //iPad
        vc.popoverPresentationController.sourceView = self.view;
        vc.popoverPresentationController.sourceRect = CGRectMake(CGRectGetMidX(self.view.frame), CGRectGetMidY(self.view.frame), 0, 0);
    }
    
    [self presentViewController:vc animated:YES completion:nil];
}

- (void)jumpToFeedback
{
    //参考 https://txc.qq.com/dashboard/new-product-success
    // 用户ID
//    NSString *open_id = [OpenUDID value];
//    NSString *user_id = [open_id substringFromIndex:open_id.length-6];
//
//    // 昵称
//    NSString *nickname = [NSString stringWithFormat:@"游客(%@)", user_id];
//   // 头像url地址
//   NSString *avatar = @"https://txc.qq.com/static/desktop/img/products/def-product-logo.png";
//
//   // 获得 webview url，请注意url单词是product而不是products，products是旧版本的参数，用错地址将不能成功提交
//   // 把1221数字换成你的产品ID，否则会不成功
//   NSString *appUrl = @"https://support.qq.com/product/383252";
//
//   // 设置请求体
//   NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:appUrl]];
//
//   // 请求方式为POST请求
//   [request setHTTPMethod:@"POST"];
//   [request setValue:@"application/x-www-form-urlencoded" forHTTPHeaderField:@"Content-Type"];
//   NSString *body = [NSString stringWithFormat:@"nickname=%@&avatar=%@&openid=%@", nickname, avatar, open_id];
//   [request setHTTPBody:[body dataUsingEncoding:NSUTF8StringEncoding]];
//
//    WebViewController* vc = [[WebViewController alloc]initWithWebviewType:WebViewTypePush Title:@"问题反馈"];
//    vc.hidesBottomBarWhenPushed = YES;
//    [vc loadRequest:request];
//    [self.navigationController pushViewController:vc animated:YES];
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section
{
    //section==0为vip，没有头部
    if (section == 0) {
        return 0;
    }
    
    return iPadValue(70, 48);
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section
{
    //section==0为vip，没有头部
    if (section == 0) {
        return [UIView new];
    }
    
    NSString* data = self.sectionHeaderModel[section];
    return [self _createSectionHeaderWithTitle:data];
}

- (UIView *)_createSectionHeaderWithTitle:(NSString *)title
{
    PaddingNewLabel* label = [[PaddingNewLabel alloc] initWithFrame:CGRectMake(0, 0, kScreenWidth, iPadValue(70, 48))];
    label.text = title;
    label.font = [UIFont systemFontOfSize:iPadValue(18, 14)];
    label.textColor = [UIColor colorWithHexString:@"#6B7280"];
    label.edgeInsets = UIEdgeInsetsMake(iPadValue(30, 20), iPadValue(30, 15)+iPadValue(10, 8), iPadValue(10, 8), 0);
    label.textAlignment = NSTextAlignmentLeft;
    return label;
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section
{
    return 0;
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section
{
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    if (indexPath.section == 0) {
        return iPadValue(90, 76);
    }
    
    return UITableViewAutomaticDimension;
}

- (UITableView *)tableView
{
    if(!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStyleGrouped];
        
        _tableView.separatorStyle  = UITableViewCellSeparatorStyleNone;
        _tableView.showsHorizontalScrollIndicator = NO;
        _tableView.showsVerticalScrollIndicator   = NO;
        _tableView.delegate   = self;
        _tableView.dataSource = self;
//        _tableView.estimatedRowHeight = iPadValue(88, 60);
//        _tableView.rowHeight = UITableViewAutomaticDimension;
        _tableView.backgroundColor = UIColor.clearColor;
        
        [_tableView registerClass:[SettingCell class] forCellReuseIdentifier:NSStringFromClass([SettingCell class])];
        [_tableView registerClass:[SettingVipCell class] forCellReuseIdentifier:NSStringFromClass([SettingVipCell class])];
        [_tableView registerClass:[RecommendCell class] forCellReuseIdentifier:NSStringFromClass([RecommendCell class])];
    
        float height = iPadValue(30, 20);
        UIView* header = [[UIView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, height)];
        header.backgroundColor = UIColor.clearColor;
        _tableView.tableHeaderView = header;
        
        UIWindow* window = [NSObject normalWindow];
        UIView* footer = [[UIView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, height+window.safeAreaInsets.bottom)];
        footer.backgroundColor = UIColor.clearColor;
        _tableView.tableFooterView = footer;
    }
    
    return _tableView;
}

- (NSMutableArray *)model
{
    if(!_model) {
        _model = [NSMutableArray array];
    }
    
    return _model;
}

- (NSMutableArray *)sectionHeaderModel
{
    if (!_sectionHeaderModel) {
        _sectionHeaderModel = [NSMutableArray array];
    }
    
    return _sectionHeaderModel;
}

@end
