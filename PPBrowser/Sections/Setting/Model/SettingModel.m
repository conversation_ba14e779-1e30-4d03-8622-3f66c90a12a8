//
//  SettingModel.m
//  PPBrowser
//
//  Created by qingbin on 2022/6/3.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "SettingModel.h"

#import "PaymentManager.h"

@implementation SettingModel

+ (instancetype)vipSetting
{
    SettingModel* item = [SettingModel new];
    item.title = NSLocalizedString(@"setting.vipTitle", nil);
    item.type = SettingTypeVIP;
    
    if([[PaymentManager shareInstance] isVip]) {
        item.content = @"";
    } else {
        item.content = NSLocalizedString(@"vip.not.actived", nil);
    }
    
//    item.imageName = @"setting_crown_icon";
    
    return item;
}

+ (instancetype)generalSetting
{
    SettingModel* item = [SettingModel new];
    item.title = NSLocalizedString(@"setting.generalSetting", nil);
    item.type = SettingTypeGeneralSetting;
    
//    item.imageName = @"setting_setting_icon";
    //https://fontawesome.com/icons/sliders?f=classic&s=solid
    item.imageName = @"setting_slider";
    item.imageBackgroundColor = @"#5856D6";
    
    return item;
}

+ (instancetype)homeSetting
{
    SettingModel* item = [SettingModel new];
    item.title = NSLocalizedString(@"setting.homeSetting", nil);
    item.type = SettingTypeHomeSetting;
    
//    item.imageName = @"setting_home_icon";
    //https://fontawesome.com/icons/house?f=classic&s=solid
    item.imageName = @"setting_home";
    item.imageBackgroundColor = @"#FF9500";
    
    return item;
}

+ (instancetype)webSetting
{
    SettingModel* item = [SettingModel new];
    item.title = NSLocalizedString(@"setting.websiteSetting", nil);
    item.type = SettingTypeWebSetting;
    
    //https://yesicon.app/ic/outline-web-stories
//    item.imageName = @"setting_browser_icon";
    //https://fontawesome.com/icons/globe?f=classic&s=solid
    item.imageName = @"setting_browser";
    item.imageBackgroundColor = @"#34C759";
    
    return item;
}

+ (instancetype)translateSetting
{
    //https://yesicon.app/tdesign/translate
    SettingModel* item = [SettingModel new];
    item.title = NSLocalizedString(@"setting.translate", nil);
    item.type = SettingTypeTranslate;
    
//    item.imageName = @"setting_translate_icon";
    //https://fontawesome.com/search?q=language&o=r
    item.imageName = @"setting_language";
//    item.imageBackgroundColor = @"#5AC8FA";
    item.imageBackgroundColor = @"#439cfc";
    
    return item;
}

//2.6.4 标签页设置
+ (instancetype)tabSetting
{
    SettingModel* item = [SettingModel new];
    item.title = NSLocalizedString(@"setting.tab", nil);
    item.type = SettingTypeTabSetting;
    
    //https://fontawesome.com/icons/table?f=classic&s=solid
    item.imageName = @"setting_tab";
    item.imageBackgroundColor = @"#fcca43";
    
    return item;
}

//2.6.8 工具类设置
+ (instancetype)toolbarConfigSetting
{
    SettingModel* item = [SettingModel new];
    item.title = NSLocalizedString(@"setting.toolbar", nil);
    item.type = SettingTypeToolbarConfig;
    
    //https://fontawesome.com/icons/grip?f=classic&s=solid
    item.imageName = @"setting_grid";
    item.imageBackgroundColor = @"#007AFF";
    
    return item;
}

//+ (instancetype)playSetting
//{
//    SettingModel* item = [SettingModel new];
//    item.title = @"播放设置";
//    item.type = SettingTypePlaySetting;
//
//    return item;
//}

//广告拦截
+ (instancetype)adBlockSetting
{
    SettingModel* item = [SettingModel new];
    item.title = NSLocalizedString(@"adblock.title", nil);
    item.type = SettingTypeAdBlock;
    
//    item.imageName = @"setting_shield_icon";
    //https://fontawesome.com/icons/ban?f=classic&s=solid
    item.imageName = @"setting_ban";
    item.imageBackgroundColor = @"#FF2D55";
    
    return item;
}

//屏蔽APP跳转
+ (instancetype)alertJumpSetting
{
    SettingModel* item = [SettingModel new];
    item.title = NSLocalizedString(@"alert.block.app.redirect", nil);
    item.type = SettingTypeAlertJump;
    
//    item.imageName = @"setting_alertjump_icon";
    //https://fontawesome.com/icons/shield-halved?f=classic&s=solid
    item.imageName = @"setting_alertjump";
    item.imageBackgroundColor = @"#FF9500";
    
    return item;
}

//2.6.3 自动翻页
+ (instancetype)autoPageSetting
{
    SettingModel* item = [SettingModel new];
    item.title = NSLocalizedString(@"setting.autoPage", nil);
    item.type = SettingTypeAutoPage;
    
//    item.imageName = @"setting_autopage_icon";
    //https://fontawesome.com/icons/file-lines?f=classic&s=solid
    item.imageName = @"setting_autopage";
    item.imageBackgroundColor = @"#AF52DE";
    
    return item;
}

+ (instancetype)searchSetting
{
    SettingModel* item = [SettingModel new];
    item.title = NSLocalizedString(@"search.ipad.title", nil);
    item.type = SettingTypeSearchSetting;
    
    item.imageName = @"setting_search";
    item.imageBackgroundColor = @"#4285F4";
    
    return item;
}

+ (instancetype)iCloudSetting
{
    SettingModel* item = [SettingModel new];
    item.title = NSLocalizedString(@"iCloud.title", nil);
    item.type = SettingTypeiCloud;
    
    //https://yesicon.app/material-symbols/cloud-outline
//    item.imageName = @"setting_cloud_icon";
    //https://fontawesome.com/icons/cloud?f=classic&s=solid
    item.imageName = @"setting_cloud";
    item.imageBackgroundColor = @"#64D2FF";
    
    return item;
}

+ (instancetype)guidlelineSetting
{
    SettingModel* item = [SettingModel new];
    item.title = NSLocalizedString(@"setting.guideline", nil);
    item.type = SettingTypeGuideline;
    
//    item.imageName = @"setting_book_icon";
    //https://fontawesome.com/icons/circle-question?f=classic&s=regular
    item.imageName = @"setting_question";
    item.imageBackgroundColor = @"#30B0C7";
    
    return item;
}

+ (instancetype)rate
{
    SettingModel* item = [SettingModel new];
    item.title = NSLocalizedString(@"setting.rate", nil);
    item.type = SettingTypeRate;
    
//    item.imageName = @"setting_star_icon";
    //https://fontawesome.com/icons/star?f=classic&s=solid
    item.imageName = @"setting_star";
    item.imageBackgroundColor = @"#FF9500";
    
    return item;
}

//+ (instancetype)feedback
//{
//    SettingModel* item = [SettingModel new];
//    item.title = @"意见反馈";
//    item.type = SettingTypeFeedback;
//    return item;
//}

+ (instancetype)share
{
    SettingModel* item = [SettingModel new];
    item.title = NSLocalizedString(@"setting.shareToFirend", nil);
    item.type = SettingTypeShare;
    
//    item.imageName = @"setting_share_icon";
    //https://fontawesome.com/icons/share-nodes?f=classic&s=solid
    item.imageName = @"setting_share";
    item.imageBackgroundColor = @"#34C759";
    
    return item;
}

+ (instancetype)settingProtocol
{
    SettingModel* item = [SettingModel new];
    item.title = NSLocalizedString(@"setting.privacyProtocol", nil);
    item.type = SettingTypeProtocol;
    
//    item.imageName = @"setting_lock_icon";
    //https://fontawesome.com/icons/lock?f=classic&s=solid
    item.imageName = @"setting_lock";
    item.imageBackgroundColor = @"#8E8E93";
    
    return item;
}

+ (instancetype)aboutUs
{
    SettingModel* item = [SettingModel new];
    item.title = NSLocalizedString(@"setting.aboutUs", nil);
    item.type = SettingTypeAboutUs;
    
//    item.imageName = @"setting_info_icon";
    //https://fontawesome.com/icons/info?f=classic&s=solid
    item.imageName = @"setting_info";
    item.imageBackgroundColor = @"#007AFF";
    
    NSString* version = [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleShortVersionString"];
    int localLanguage = [NSLocalizedString(@"opensearch.value", nil) intValue];
    if (localLanguage == 0) {
        //简体中文
        item.content = [NSString stringWithFormat:@"版本 %@",version];
    } else {
        //繁体中文/英语
        item.content = [NSString stringWithFormat:@"v%@",version];
    }

    return item;
}

//看图模式
//+ (instancetype)photoBrowserSetting
//{
//    //https://yesicon.app/mdi/image-multiple-outline
//    
//    SettingModel* item = [SettingModel new];
//    item.title = NSLocalizedString(@"photoBrowser.title", nil);
//    item.type = SettingTypePhotoBrowser;
//    
//    item.imageName = @"setting_photo_browser_icon";
//    
//    return item;
//}

+ (instancetype)dateClearSetting
{
    SettingModel* item = [SettingModel new];
    item.title = NSLocalizedString(@"dataClear.title", nil);
    item.type = SettingTypeDataClear;
    
//    item.imageName = @"setting_delete_icon";
    //https://fontawesome.com/icons/trash-can?f=classic&s=solid
    item.imageName = @"setting_trash";
    item.imageBackgroundColor = @"#FF3B30";
    
    return item;
}

+ (instancetype)addonsRecommend
{
    SettingModel* item = [SettingModel new];
    item.title = @"Addons";
    item.content = NSLocalizedString(@"setting.addons.detail", nil);
    item.link = @"https://itunes.apple.com/app/id6446811843";
    item.type = SettingTypeRecommend;
    
    item.imageName = @"addons_logo";
    
    
    return item;
}

+ (instancetype)qingyueRecommend
{
    SettingModel* item = [SettingModel new];
    item.title = NSLocalizedString(@"setting.qingyue.title", nil);
    item.content = NSLocalizedString(@"setting.qingyue.detail", nil);
    item.link = @"https://itunes.apple.com/app/id6468076268";
    item.type = SettingTypeRecommend;
    
    item.imageName = @"qingyue_logo";
    
    
    return item;
}

+ (instancetype)exportTemp
{
    SettingModel* item = [SettingModel new];
    item.title = @"一键导出所有资源";
    item.type = SettingTypeExport;
    
    item.imageName = @"setting_photo_browser_icon";
    
    return item;
}

@end
