//
//  SettingModel.h
//  PPBrowser
//
//  Created by qingbin on 2022/6/3.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "BaseModel.h"
#import "PPEnums.h"

@interface SettingModel : BaseModel
//图标
@property (nonatomic, strong) NSString *imageName;
//图标背景颜色
@property (nonatomic, strong) NSString *imageBackgroundColor;
//标题
@property (nonatomic, strong) NSString *title;
//内容
@property (nonatomic, strong) NSString *content;
//类型
@property (nonatomic, assign) SettingType type;
//推荐列表的跳转链接
@property (nonatomic, strong) NSString *link;

//辅助字段
@property (nonatomic, assign) BOOL isFirstInSection;

@property (nonatomic, assign) BOOL isLastInSection;

// builder
+ (instancetype)vipSetting;

+ (instancetype)generalSetting;

+ (instancetype)homeSetting;

+ (instancetype)webSetting;

+ (instancetype)translateSetting;

//+ (instancetype)playSetting;

+ (instancetype)rate;

//+ (instancetype)feedback;

+ (instancetype)share;

+ (instancetype)settingProtocol;

+ (instancetype)aboutUs;

+ (instancetype)guidlelineSetting;

//广告拦截
+ (instancetype)adBlockSetting;
//屏蔽APP跳转
+ (instancetype)alertJumpSetting;
//2.6.3 自动翻页
+ (instancetype)autoPageSetting;
//2.6.4 标签页设置
+ (instancetype)tabSetting;
//2.6.8 工具类设置
+ (instancetype)toolbarConfigSetting;
//2.7.1 搜索设置
+ (instancetype)searchSetting;

//看图模式
//+ (instancetype)photoBrowserSetting;

+ (instancetype)dateClearSetting;

+ (instancetype)iCloudSetting;

+ (instancetype)addonsRecommend;

+ (instancetype)qingyueRecommend;

+ (instancetype)exportTemp;

@end
