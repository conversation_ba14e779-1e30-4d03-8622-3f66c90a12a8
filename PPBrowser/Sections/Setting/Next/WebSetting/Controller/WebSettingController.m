//
//  WebSettingController.m
//  PPBrowser
//
//  Created by qingbin on 2022/6/6.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "WebSettingController.h"

#import "Masonry.h"
#import "MaizyHeader.h"
#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "ReactiveCocoa.h"
#import "NSObject+Helper.h"

#import "SettingSwitchView.h"
#import "SettingTextAndArrowView.h"
#import "SettingStepperView.h"
#import "SettingArrowView.h"

#import "PPNotifications.h"

#import "PreferenceManager.h"
#import "SearchManager.h"

#import "UserAgentController.h"
#import "BrowserUtils.h"
#import "SearchViewController.h"
#import "BaseNavigationController.h"

#import "AppDelegate.h"
#import "UserAgentController.h"

#import "UserAgentManager.h"
#import "PaddingNewLabel.h"
#import "SettingSegmentAndTitleView.h"
#import "UIAlertController+SafePresentation.h"

@interface WebSettingController ()

@property (nonatomic, strong) UIScrollView* scrollView;
//
@property (nonatomic, strong) UIStackView* stackView;
// 基本设置
@property (nonatomic, strong) PaddingNewLabel *basicSectionView;
// 搜索引擎
//@property (nonatomic, strong) SettingTextAndArrowView* searchEngineView;
// 浏览器标识
@property (nonatomic, strong) SettingTextAndArrowView* uaView;
// 弹出式窗口打开方式
@property (nonatomic, strong) SettingTextAndArrowView* popupWindowOptionView;

// 页面交互
@property (nonatomic, strong) PaddingNewLabel *pageSectionView;
// 开启下拉刷新
@property (nonatomic, strong) SettingSwitchView *enabledPull2RefreshView;
// 启动时恢复窗口
@property (nonatomic, strong) SettingSwitchView *openLastWindowView;

// 视频和图片
@property (nonatomic, strong) PaddingNewLabel *mediaSectionView;
// 开启视频模式
@property (nonatomic, strong) SettingSwitchView *playerView;
// 开启阅读模式
@property (nonatomic, strong) SettingSwitchView *readerView;
// 开启长按识别视频
@property (nonatomic, strong) SettingSwitchView *enableLongPressPlayerView;
// 开启长按识别图片
@property (nonatomic, strong) SettingSwitchView *detectPhotoView;
// 开启长按预览图片
@property (nonatomic, strong) SettingSwitchView *enablePreviewView;

@end

@implementation WebSettingController

- (void)viewDidLoad
{
    [super viewDidLoad];

    self.title = NSLocalizedString(@"setting.websiteSetting", nil);
    
    [self addSubviews];
    [self defineLayout];
    [self updateWithModel];
    [self setupObservers];
    [self createCustomLeftBarButtonItem];
    
    [self applyTheme];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
    } else {
        self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    //只有当前的controller会触发一次, 其它已存在的controller走通知
    if(needReload) {
        //修改暗黑模式
        [self applyTheme];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}

- (void)updateWithModel
{
    BOOL enabledPlayer = [[PreferenceManager shareInstance].items.enabledPlayer boolValue];
    [self.playerView updateWithTitle:NSLocalizedString(@"webSetting.videomode.text", nil) isOn:enabledPlayer];
    
    BOOL enabledReader = [[PreferenceManager shareInstance].items.enabledReader boolValue];
    [self.readerView updateWithTitle:NSLocalizedString(@"webSetting.reader.text", nil) isOn:enabledReader];
    
    BOOL enabledLongPressPlayer = [[PreferenceManager shareInstance].items.enabledLongPressPlayer boolValue];
    [self.enableLongPressPlayerView updateWithTitle:NSLocalizedString(@"webSetting.longpress.video.text", nil) isOn:enabledLongPressPlayer];
    
    if([BrowserUtils isiPad]) {
        self.uaView.hidden = YES;
    } else {
        //iPhone
        self.uaView.hidden = NO;
        
        UserAgentModel* item = [[UserAgentManager shareInstance] getCurrentUAModel];
        [self.uaView updateWithTitle:NSLocalizedString(@"webSetting.useragent.text", nil) content:item.title];
    }
    
    BOOL openLastWindow = [[PreferenceManager shareInstance].items.openLastWindow boolValue];
    [self.openLastWindowView updateWithTitle:NSLocalizedString(@"webSetting.restorewindow.text", nil) isOn:openLastWindow];
    
//    SearchModel* model = [[SearchManager shareInstance] getCurrentSearchEngine];
//    [self.searchEngineView updateWithTitle:NSLocalizedString(@"webSetting.searchEngine.text", nil) content:model.shortName];
    
    PopupWindowOption option = [[PreferenceManager shareInstance].items.popupWindowOption intValue];
    if(option == PopupWindowOptionAlwaysAsk) {
        [self.popupWindowOptionView updateWithTitle:NSLocalizedString(@"popupwindow.setting.title", nil) content:NSLocalizedString(@"popupwindow.setting.alwaysAsk", nil)];
    } else if(option == PopupWindowOptionDefault) {
        [self.popupWindowOptionView updateWithTitle:NSLocalizedString(@"popupwindow.setting.title", nil) content:NSLocalizedString(@"popupwindow.setting.default", nil)];
    } else if(option == PopupWindowOptionOpenInCurrent) {
        [self.popupWindowOptionView updateWithTitle:NSLocalizedString(@"popupwindow.setting.title", nil) content:NSLocalizedString(@"popupwindow.setting.openInCurrent", nil)];
    } else if(option == PopupWindowOptionOpenInNewWindow) {
        [self.popupWindowOptionView updateWithTitle:NSLocalizedString(@"popupwindow.setting.title", nil) content:NSLocalizedString(@"popupwindow.setting.openInInNewWindow", nil)];
    } else if(option == PopupWindowOptionOpenInBackWindow) {
        [self.popupWindowOptionView updateWithTitle:NSLocalizedString(@"popupwindow.setting.title", nil) content:NSLocalizedString(@"popupwindow.setting.openInInBackWindow", nil)];
    }
    
    BOOL enabledPullToRefesh = [[PreferenceManager shareInstance].items.enabledPullToRefesh boolValue];
    [self.enabledPull2RefreshView updateWithTitle:NSLocalizedString(@"scroll.enable.pull.to.refresh.text", nil) isOn:enabledPullToRefesh];
    
    BOOL enabledDetectPhoto = [[PreferenceManager shareInstance].items.enabledDetectPhoto boolValue];
    [self.detectPhotoView updateWithTitle:NSLocalizedString(@"webSetting.detect.photo.text", nil) isOn:enabledDetectPhoto];
    
    BOOL enabledPreview = [[PreferenceManager shareInstance].items.enabledPreview boolValue];
    [self.enablePreviewView updateWithTitle:NSLocalizedString(@"webSetting.longpress.preview.text", nil) isOn:enabledPreview];
}

- (void)setupObservers
{
    @weakify(self)
    [self.playerView setDidSwithAction:^(BOOL isOn) {
        [PreferenceManager shareInstance].items.enabledPlayer = @(isOn);
        [[PreferenceManager shareInstance] encode];
        
        [[NSNotificationCenter defaultCenter] postNotificationName:kReloadCurrentWebViewNotification object:nil];
    }];
    
    [self.readerView setDidSwithAction:^(BOOL isOn) {
        [PreferenceManager shareInstance].items.enabledReader = @(isOn);
        [[PreferenceManager shareInstance] encode];
        
        [[NSNotificationCenter defaultCenter] postNotificationName:kReloadCurrentWebViewNotification object:nil];
    }];
    
    [self.enableLongPressPlayerView setDidSwithAction:^(BOOL isOn) {
        [PreferenceManager shareInstance].items.enabledLongPressPlayer = @(isOn);
        [[PreferenceManager shareInstance] encode];
    }];
    
    [self.detectPhotoView setDidSwithAction:^(BOOL isOn) {
        [PreferenceManager shareInstance].items.enabledDetectPhoto = @(isOn);
        [[PreferenceManager shareInstance] encode];
    }];
    
    [self.enablePreviewView setDidSwithAction:^(BOOL isOn) {
        [PreferenceManager shareInstance].items.enabledPreview = @(isOn);
        [[PreferenceManager shareInstance] encode];
    }];
    
    [self.uaView setDidAction:^{
        @strongify(self)
        [self showUASelection];
    }];
    
//    [self.searchEngineView setDidAction:^{
//        @strongify(self)
//        SearchViewController* vc = [SearchViewController new];
//        @weakify(self)
//        [vc setUpdateSearchEngineBlock:^(NSString * _Nonnull text) {
//            @strongify(self)
//            [self.searchEngineView updateWithContent:text];
//        }];
//        [self.navigationController pushViewController:vc animated:YES];
//    }];
    
    [self.popupWindowOptionView setDidAction:^{
        @strongify(self)
        [self showPopupWindowOptionView];
    }];
    
    [self.openLastWindowView setDidSwithAction:^(BOOL isOn) {
        [PreferenceManager shareInstance].items.openLastWindow = @(isOn);
        [[PreferenceManager shareInstance] encode];
    }];

    [self.enabledPull2RefreshView setDidSwithAction:^(BOOL isOn) {
        [PreferenceManager shareInstance].items.enabledPullToRefesh = @(isOn);
        [[PreferenceManager shareInstance] encode];
        
        [[NSNotificationCenter defaultCenter] postNotificationName:kupdatePullToRefreshNotification object:nil];
    }];
            
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(_reloadUserAgent) name:kReloadCurrentWebViewNotification object:nil];
}

- (void)_reloadUserAgent
{
    [self updateWithModel];
}

- (void)showUASelection
{
    UserAgentController* vc = [UserAgentController new];
    [self.navigationController pushViewController:vc animated:YES];
}

- (void)showPopupWindowOptionView
{
    UIAlertControllerStyle style = UIAlertControllerStyleActionSheet;
    UIAlertController *alertController = [UIAlertController alertControllerWithTitle:nil message:nil preferredStyle:style];
    [alertController addAction:([UIAlertAction actionWithTitle:NSLocalizedString(@"alert.cancel", nil) style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
        
    }])];
    [alertController addAction:([UIAlertAction actionWithTitle:NSLocalizedString(@"popupwindow.setting.default", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [PreferenceManager shareInstance].items.popupWindowOption = @(PopupWindowOptionDefault);
        [[PreferenceManager shareInstance] encode];
        [self updateWithModel];
    }])];
    [alertController addAction:([UIAlertAction actionWithTitle:NSLocalizedString(@"popupwindow.setting.alwaysAsk", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [PreferenceManager shareInstance].items.popupWindowOption = @(PopupWindowOptionAlwaysAsk);
        [[PreferenceManager shareInstance] encode];
        [self updateWithModel];
    }])];
    [alertController addAction:([UIAlertAction actionWithTitle:NSLocalizedString(@"popupwindow.setting.openInCurrent", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [PreferenceManager shareInstance].items.popupWindowOption = @(PopupWindowOptionOpenInCurrent);
        [[PreferenceManager shareInstance] encode];
        [self updateWithModel];
    }])];
    [alertController addAction:([UIAlertAction actionWithTitle:NSLocalizedString(@"popupwindow.setting.openInInNewWindow", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [PreferenceManager shareInstance].items.popupWindowOption = @(PopupWindowOptionOpenInNewWindow);
        [[PreferenceManager shareInstance] encode];
        [self updateWithModel];
    }])];
    [alertController addAction:([UIAlertAction actionWithTitle:NSLocalizedString(@"popupwindow.setting.openInInBackWindow", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [PreferenceManager shareInstance].items.popupWindowOption = @(PopupWindowOptionOpenInBackWindow);
        [[PreferenceManager shareInstance] encode];
        [self updateWithModel];
    }])];
    
//    if([BrowserUtils isiPad]) {
//        //适配iPad
//        UIPopoverPresentationController* popover = alertController.popoverPresentationController;
//        if(popover) {
//            popover.sourceView = self.view;
//        
//            CGRect sourceRect = [self.popupWindowOptionView.superview convertRect:self.popupWindowOptionView.frame toView:self.view];
//            popover.sourceRect = sourceRect;
//            popover.permittedArrowDirections = UIPopoverArrowDirectionUp;
//        }
//    }
//    
//    [self presentViewController:alertController animated:YES completion:nil];
    
    CGRect sourceRect = [self.popupWindowOptionView.superview convertRect:self.popupWindowOptionView.frame toView:self.view];
    //v2.6.8, 统一present方法，防止崩溃
    [alertController presentSafelyFromViewController:self sourceView:self.view sourceRect:sourceRect];
}

#pragma mark -- layout
- (void)addSubviews
{
    //https://stackoverflow.com/questions/33927914/how-can-i-set-the-cornerradius-of-a-uistackview
    //UIStackView添加圆角适配
    
    [self.view addSubview:self.scrollView];
    [self.scrollView addSubview:self.stackView];
}

- (void)defineLayout
{
    float leftOffset = iPadValue(30, 15);
    float topOffset = iPadValue(30, 15);
    [self.scrollView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(leftOffset);
        make.right.mas_offset(-leftOffset);
        make.top.equalTo(self.view).offset(0);
        make.bottom.equalTo(self.view);
    }];
    
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.scrollView);
        make.bottom.equalTo(self.scrollView).offset(-topOffset);
        make.left.equalTo(self.view).offset(leftOffset);
        make.right.equalTo(self.view).offset(-leftOffset);
    }];
    
    [self.playerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([SettingSwitchView height]);
    }];
    
    [self.readerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([SettingSwitchView height]);
    }];
    
    [self.enableLongPressPlayerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([SettingSwitchView height]);
    }];
    
    [self.detectPhotoView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([SettingSwitchView height]);
    }];
    
    [self.enablePreviewView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([SettingSwitchView height]);
    }];
    
    [self.uaView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([SettingTextAndArrowView height]);
    }];
    
//    [self.searchEngineView mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.height.mas_equalTo([SettingTextAndArrowView height]);
//    }];
    
    [self.popupWindowOptionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([SettingTextAndArrowView height]);
    }];
        
    [self.openLastWindowView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([SettingSwitchView height]);
    }];
    
    [self.enabledPull2RefreshView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([SettingSwitchView height]);
    }];
}

#pragma mark -- lazy init
- (UIStackView *)stackView
{
    if(!_stackView) {
        _stackView = [[UIStackView alloc]initWithArrangedSubviews:@[
            self.basicSectionView,
//            self.searchEngineView,
            self.uaView,
            self.popupWindowOptionView,
            
            self.pageSectionView,
            self.enabledPull2RefreshView,
            self.openLastWindowView,
            
            self.mediaSectionView,
            self.playerView,
            self.readerView,
            self.enableLongPressPlayerView,
            self.detectPhotoView,
            self.enablePreviewView,
        ]];
        
        _stackView.spacing = 0;
        _stackView.axis = UILayoutConstraintAxisVertical;
        
        _stackView.backgroundColor = UIColor.clearColor;
        
        _stackView.layer.cornerRadius = 10;
        _stackView.layer.masksToBounds = YES;
    }
    
    return _stackView;
}

- (SettingSwitchView *)playerView
{
    if(!_playerView) {
        _playerView = [[SettingSwitchView alloc]initWithShowLine:YES];
        
        _playerView.layer.cornerRadius = 10;
        _playerView.layer.maskedCorners = UIRectCornerTopLeft | UIRectCornerTopRight;
    }
    
    return _playerView;
}

- (SettingSwitchView *)readerView
{
    if(!_readerView) {
        _readerView = [[SettingSwitchView alloc]initWithShowLine:YES];
    }
    
    return _readerView;
}

- (SettingSwitchView *)enableLongPressPlayerView
{
    if(!_enableLongPressPlayerView) {
        _enableLongPressPlayerView = [[SettingSwitchView alloc]initWithShowLine:YES];
    }
    
    return _enableLongPressPlayerView;
}

- (SettingSwitchView *)detectPhotoView
{
    if(!_detectPhotoView) {
        _detectPhotoView = [[SettingSwitchView alloc]initWithShowLine:YES];
    }
    
    return _detectPhotoView;
}

- (SettingSwitchView *)enablePreviewView
{
    if(!_enablePreviewView) {
        _enablePreviewView = [[SettingSwitchView alloc]initWithShowLine:NO];
    }
    
    return _enablePreviewView;
}

- (SettingTextAndArrowView *)uaView
{
    if(!_uaView) {
        _uaView = [[SettingTextAndArrowView alloc]initWithShowLine:YES];
        
        _uaView.layer.cornerRadius = 10;
        _uaView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
    }
    
    return _uaView;
}

//- (SettingTextAndArrowView *)searchEngineView
//{
//    if(!_searchEngineView) {
//        _searchEngineView = [[SettingTextAndArrowView alloc]initWithShowLine:YES];
//        
//        _searchEngineView.layer.cornerRadius = 10;
//        _searchEngineView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
//    }
//    
//    return _searchEngineView;
//}

- (SettingTextAndArrowView *)popupWindowOptionView
{
    if(!_popupWindowOptionView) {
        _popupWindowOptionView = [[SettingTextAndArrowView alloc]initWithShowLine:NO];
        
        _popupWindowOptionView.layer.cornerRadius = 10;
        _popupWindowOptionView.layer.maskedCorners = UIRectCornerBottomLeft | UIRectCornerBottomRight;
    }
    
    return _popupWindowOptionView;
}

- (SettingSwitchView *)openLastWindowView
{
    if(!_openLastWindowView) {
        _openLastWindowView = [[SettingSwitchView alloc]initWithShowLine:NO];
        
        _openLastWindowView.layer.cornerRadius = 10;
        _openLastWindowView.layer.maskedCorners = UIRectCornerBottomLeft | UIRectCornerBottomRight;
    }
    
    return _openLastWindowView;
}

- (SettingSwitchView *)enabledPull2RefreshView
{
    if(!_enabledPull2RefreshView) {
        _enabledPull2RefreshView = [[SettingSwitchView alloc]initWithShowLine:YES];
        
        _enabledPull2RefreshView.layer.cornerRadius = 10;
        _enabledPull2RefreshView.layer.maskedCorners = UIRectCornerTopLeft | UIRectCornerTopRight;
    }
    
    return _enabledPull2RefreshView;
}

- (UIScrollView *)scrollView
{
    if(!_scrollView) {
        _scrollView = [[UIScrollView alloc]init];
        _scrollView.showsVerticalScrollIndicator = NO;
        _scrollView.backgroundColor = UIColor.clearColor;
    }
    
    return _scrollView;
}

- (PaddingNewLabel *)basicSectionView
{
    if (!_basicSectionView) {
        _basicSectionView = [self _createSectionHeaderWithTitle:NSLocalizedString(@"webSetting.basic.title", nil)];
    }
    
    return _basicSectionView;
}

- (PaddingNewLabel *)pageSectionView
{
    if (!_pageSectionView) {
        _pageSectionView = [self _createSectionHeaderWithTitle:NSLocalizedString(@"webSetting.page.title", nil)];
    }
    
    return _pageSectionView;
}

- (PaddingNewLabel *)mediaSectionView
{
    if (!_mediaSectionView) {
        _mediaSectionView = [self _createSectionHeaderWithTitle:NSLocalizedString(@"webSetting.media.title", nil)];
    }
    
    return _mediaSectionView;
}

- (PaddingNewLabel *)_createSectionHeaderWithTitle:(NSString *)title
{
    PaddingNewLabel* label = [[PaddingNewLabel alloc] initWithFrame:CGRectMake(0, 0, kScreenWidth, iPadValue(70, 48))];
    label.text = title;
    label.font = [UIFont systemFontOfSize:iPadValue(18, 14)];
    label.textColor = [UIColor colorWithHexString:@"#6B7280"];
    label.edgeInsets = UIEdgeInsetsMake(iPadValue(30, 20), iPadValue(30, 15), iPadValue(10, 8), 0);
    label.textAlignment = NSTextAlignmentLeft;
    return label;
}

@end
