//
//  GeneralSettingController.m
//  PPBrowser
//
//  Created by qingbin on 2022/9/4.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "GeneralSettingController.h"

#import "Masonry.h"
#import "MaizyHeader.h"
#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "ReactiveCocoa.h"
#import "UIView+FrameHelper.h"
#import "UIViewController+Helper.h"
#import "NSObject+Helper.h"

#import "SettingSwitchView.h"
#import "SettingArrowView.h"
#import "SettingTextView.h"
#import "SettingSegmentView.h"

#import "PPNotifications.h"
#import "VIPController.h"
#import "BaseNavigationController.h"
#import "BrowserUtils.h"

#import "BrowserHelper.h"
#import "AppDelegate.h"

#import "PaymentManager.h"
#import "PPEnums.h"
#import "UIAlertController+SafePresentation.h"

@interface GeneralSettingController ()

@property (nonatomic, strong) UIStackView* stackView;
/// 指纹/面容识别
@property (nonatomic, strong) SettingSwitchView *biometricVerifyView;
///暗黑模式
@property (nonatomic, strong) SettingSegmentView *darkModeView;
/// 点击UA弹窗直接收起弹窗
@property (nonatomic, strong) SettingSwitchView *hideUAView;
/// 是否显示搜索历史
//@property (nonatomic, strong) SettingSwitchView *showHistoryView;
/// 搜索历史读取粘贴板
//@property (nonatomic, strong) SettingSwitchView *readPasteboardView;
/// 标签页排列方式
//@property (nonatomic, strong) SettingSegmentView *tabTrayArrangeView;

@end

@implementation GeneralSettingController

- (void)viewDidLoad
{
    [super viewDidLoad];

    self.title = NSLocalizedString(@"setting.generalSetting", nil);
    
    [self addSubviews];
    [self defineLayout];
    [self updateWithModel];
    [self setupObservers];
    [self createCustomLeftBarButtonItem];
}

- (void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:animated];
    
    [self applyTheme];
}

- (void)updateWithModel
{
    BiometricsType type = [BrowserHelper bioTypeForDevice];
    NSString* title = @"";
    if(type == BiometricsTypeTouchID) {
        title = @"TouchID";
    } else if(type == BiometricsTypeFaceID) {
        title = @"FaceID";
    } else {
        //默认
        title = @"FaceID";
    }
    BOOL isOpenBiometricVerify = [[PreferenceManager shareInstance].items.isOpenBiometricVerify boolValue];
    [self.biometricVerifyView updateWithTitle:title isOn:isOpenBiometricVerify];
    
    //暗黑模式
    DarkModeStatus status = [[PreferenceManager shareInstance].items.darkModeStatus intValue];
    [self.darkModeView updateWithSelectIndex:status];
    
    BOOL hideUA = [[PreferenceManager shareInstance].items.isHideUAViewAfterTapAction boolValue];
    [self.hideUAView updateWithTitle:NSLocalizedString(@"generalSetting.hide.ua", nil) isOn:hideUA];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
//        self.tipsView.backgroundColor = [UIColor.blackColor colorWithAlphaComponent:0.5];
    } else {
        self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
//        self.tipsView.backgroundColor = [UIColor.whiteColor colorWithAlphaComponent:0.5];
    }
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    //只有当前的controller会触发一次, 其它已存在的controller走通知
    if(needReload) {
        //修改暗黑模式
        [self applyTheme];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}

- (void)setupObservers
{
    @weakify(self)
    [self.biometricVerifyView setDidSwithAction:^(BOOL isOn) {
        @strongify(self)
        [self _biometricVerifyWithIsOn:isOn];
    }];
    
    [self.darkModeView setSelectIndexBlock:^(int index) {
        @strongify(self)
        [ThemeProtocol updateDarkModeStatus:index];
        [self notify];
        [self applyTheme];
        [self configNavigationBarStype];
    }];
        
    [self.hideUAView setDidSwithAction:^(BOOL isOn) {
        [PreferenceManager shareInstance].items.isHideUAViewAfterTapAction = @(isOn);
        [[PreferenceManager shareInstance] encode];
    }];
}

- (void)_biometricVerifyWithIsOn:(BOOL)isOn
{
    //先重置，等校验通过再开启
    [self.biometricVerifyView.switchView setOn:!isOn];
    
    if(![BrowserHelper isBiometricVerifyValid]) {
        [UIView showFailed:NSLocalizedString(@"generalSetting.verify.fail", nil)];
        return;
    }
    
    AppDelegate* delegate = (AppDelegate*)[UIApplication sharedApplication].delegate;
    delegate.verifyFaceIDStatus = FaceIDStatusHandling;
    
    [BrowserHelper biometricVerify:^(BOOL success) {
        if(success) {
            [self.biometricVerifyView.switchView setOn:isOn];
            [PreferenceManager shareInstance].items.isOpenBiometricVerify = @(isOn);
            [[PreferenceManager shareInstance] encode];
            
            //验证成功
            delegate.verifyFaceIDStatus = FaceIDStatusFinish;
        } else {
            
        }
    }];
}

- (BOOL)checkIsVip
{
    BOOL isVip = [[PaymentManager shareInstance] isVip];
    if(!isVip) {
        UIAlertController* alertController = [UIAlertController alertControllerWithTitle:NSLocalizedString(@"vip.alert.title", nil) message:NSLocalizedString(@"vip.alert.text", nil) preferredStyle:UIAlertControllerStyleAlert];
        UIAlertAction* action = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.cancel", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            [alertController dismissViewControllerAnimated:YES completion:nil];
        }];
        [alertController addAction:action];

        action = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.toKnowMore", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            [self jumpToVip];
        }];
        [alertController addAction:action];

//        [self presentViewController:alertController animated:YES completion:nil];
        //v2.6.8, 统一present方法，防止崩溃
        [alertController presentSafelyFromViewController:self];
    }
    
    return isVip;
}

#pragma mark -- 打开Vip页面
- (void)jumpToVip
{
    VIPController* vc = [[VIPController alloc]init];
    BaseNavigationController* navc = [[BaseNavigationController alloc]initWithRootViewController:vc];
    navc.view.backgroundColor = UIColor.whiteColor;

//    if ([BrowserUtils isiPad]) {
//        // iPad
//        navc.modalPresentationStyle = UIModalPresentationFormSheet;
//        vc.preferredContentSize = CGSizeMake(kScreenWidth-90, kScreenHeight-90);
//        
//        [self presentViewController:navc animated:YES completion:nil];
//    } else {
//        // iPhone
//        [self presentViewController:navc animated:YES completion:nil];
//    }
    //v2.6.8,统一present
    [self presentCustomToViewController:navc];
}

- (void)notify
{
    //重新加载脚本
    [[NSNotificationCenter defaultCenter] postNotificationName:kReloadUserScriptNotification object:nil];
    //切换夜间模式
    [[NSNotificationCenter defaultCenter] postNotificationName:kDarkThemeDidChangeNotification object:nil];
}

#pragma mark -- layout
- (void)addSubviews
{
    //https://stackoverflow.com/questions/33927914/how-can-i-set-the-cornerradius-of-a-uistackview
    //UIStackView添加圆角适配
    
    [self.view addSubview:self.stackView];
}

- (void)defineLayout
{
    float margin = iPadValue(30, 15);
    float topOffset = iPadValue(30, 15);
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(margin);
        make.right.mas_offset(-margin);
        make.top.equalTo(self.view).offset(topOffset);
    }];
    
    [self.darkModeView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([SettingSegmentView height]);
    }];
    
    [self.hideUAView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([SettingSwitchView height]);
    }];
    
    [self.biometricVerifyView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([SettingSwitchView height]);
    }];
}

#pragma mark -- lazy init

- (UIStackView *)stackView
{
    if(!_stackView) {
        _stackView = [[UIStackView alloc]initWithArrangedSubviews:@[
            self.darkModeView,
            self.biometricVerifyView,
            self.hideUAView,
//            self.showHistoryView,
//            self.readPasteboardView,
        ]];
        
        _stackView.spacing = 0;
        _stackView.axis = UILayoutConstraintAxisVertical;

        _stackView.backgroundColor = UIColor.clearColor;
    }
    
    return _stackView;
}

- (SettingSwitchView *)biometricVerifyView
{
    if(!_biometricVerifyView) {
        _biometricVerifyView = [[SettingSwitchView alloc]initWithShowLine:YES];
    }
    
    return _biometricVerifyView;
}

- (SettingSegmentView *)darkModeView
{
    if(!_darkModeView) {
        _darkModeView = [[SettingSegmentView alloc]initWithTitle:NSLocalizedString(@"generalSetting.darkTheme.text", nil) showLine:YES segments:@[
            NSLocalizedString(@"generalSetting.darkTheme.auto", nil),
            NSLocalizedString(@"generalSetting.darkTheme.light", nil),
            NSLocalizedString(@"generalSetting.darkTheme.dark", nil)
        ]];
        
        _darkModeView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
        _darkModeView.layer.cornerRadius = 10;
    }
    
    return _darkModeView;
}

- (SettingSwitchView *)hideUAView
{
    if(!_hideUAView) {
        _hideUAView = [[SettingSwitchView alloc]initWithShowLine:NO];
        _hideUAView.layer.maskedCorners = kCALayerMinXMaxYCorner | kCALayerMaxXMaxYCorner;
        _hideUAView.layer.cornerRadius = 10;
    }
    
    return _hideUAView;
}

//- (SettingSegmentView *)tabTrayArrangeView
//{
//    if(!_tabTrayArrangeView) {
//        _tabTrayArrangeView = [[SettingSegmentView alloc] initWithTitle:NSLocalizedString(@"generalSetting.tab.arrange", nil) showLine:YES segments:@[
//            NSLocalizedString(@"generalSetting.tab.arrange.top2bottom", nil),
//            NSLocalizedString(@"generalSetting.tab.arrange.bottom2top", nil)
//        ]];
//    }
//    
//    return _tabTrayArrangeView;
//}

//- (SettingSwitchView *)showHistoryView
//{
//    if(!_showHistoryView) {
//        _showHistoryView = [[SettingSwitchView alloc]initWithShowLine:YES];
//    }
//    
//    return _showHistoryView;
//}

//- (SettingSwitchView *)readPasteboardView
//{
//    if(!_readPasteboardView) {
//        _readPasteboardView = [[SettingSwitchView alloc]initWithShowLine:NO];
//        _readPasteboardView.layer.maskedCorners = kCALayerMinXMaxYCorner | kCALayerMaxXMaxYCorner;
//        _readPasteboardView.layer.cornerRadius = 10;
//    }
//    
//    return _readPasteboardView;
//}


@end
