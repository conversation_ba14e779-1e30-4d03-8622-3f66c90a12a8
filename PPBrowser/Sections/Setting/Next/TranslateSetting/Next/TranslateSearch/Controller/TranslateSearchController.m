//
//  TranslateSearchController.m
//  PPBrowser
//
//  Created by qingbin on 2024/8/21.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import "TranslateSearchController.h"

#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "UIView+FrameHelper.h"
#import "NSObject+Helper.h"

#import "ThemeProtocol.h"
#import "PreferenceManager.h"

#import "PPNotifications.h"
#import "BrowserUtils.h"

#import "TranslateManager.h"
#import "TranslateSearchCell.h"

@interface TranslateSearchController ()<UITableViewDelegate, UITableViewDataSource, ThemeProtocol>

@property (nonatomic, strong) NSMutableArray* model;

@property (nonatomic, strong) UITableView* tableView;

@end

@implementation TranslateSearchController

- (void)viewDidLoad
{
    [super viewDidLoad];
    
    self.navigationItem.title = NSLocalizedString(@"translate.select.target.text", nil);
    [self createCustomLeftBarButtonItem];
    
    [self addSubviews];
    [self defineLayout];
    [self updateWithModel];
}

- (void)updateWithModel
{
    [self.model removeAllObjects];
    
    Language tranlsateTargetLanguage = [[PreferenceManager shareInstance].items.tranlsateTargetLanguage intValue];
    
    NSArray* array = [TranslateManager createSortedLanguageArrayFromDictionary];
    for(TranslateLanguageModel* obj in array) {
        if(obj.language == tranlsateTargetLanguage) {
            obj.isSelected = YES;
        } else {
            obj.isSelected = NO;
        }
    }
    
    TranslateLanguageModel* firstObj = array.firstObject;
    firstObj.isFirstInSection = YES;
    
    TranslateLanguageModel* lastObj = array.lastObject;
    lastObj.isLastInSection = YES;
    
    [self.model addObjectsFromArray:array];
    
    [self applyTheme];
    
    [self.tableView reloadData];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        
        self.tableView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.tableView.tableHeaderView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.tableView.tableFooterView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
    } else {
        self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        
        self.tableView.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.tableView.tableHeaderView.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.tableView.tableFooterView.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    //只有当前的controller会触发一次, 其它已存在的controller走通知
    if(needReload) {
        //修改暗黑模式
        [self applyTheme];
        [self.tableView reloadData];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
    [self.tableView reloadData];
}

#pragma mark -- 屏幕旋转
- (void)viewWillTransitionToSize:(CGSize)size withTransitionCoordinator:(id<UIViewControllerTransitionCoordinator>)coordinator
{
    [self.tableView reloadData];
}

#pragma mark -- layout
- (void)addSubviews
{
    [self.view addSubview:self.tableView];
}

- (void)defineLayout
{
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(0);
        make.right.mas_offset(-0);
        make.top.equalTo(self.view).offset(0);
        make.bottom.equalTo(self.view);
    }];
}

#pragma mark - UITableViewDataSource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return self.model.count;
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    TranslateLanguageModel* model = self.model[indexPath.row];
    TranslateSearchCell *cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass(TranslateSearchCell.class)];
    [cell updateWithModel:model];
    
    return cell;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    TranslateLanguageModel* model = self.model[indexPath.row];
    
    [PreferenceManager shareInstance].items.tranlsateTargetLanguage = @(model.language);
    [[PreferenceManager shareInstance] encode];
    
    [self updateWithModel];
    
    if(self.changeLanguageAction) {
        self.changeLanguageAction(model.language);
    }
    
    //刷新页面
    [[NSNotificationCenter defaultCenter] postNotificationName:kReloadCurrentWebViewNotification object:nil];
    
    [self dismissViewControllerAnimated:YES completion:nil];
}

#pragma mark -- lazy init
- (UITableView *)tableView
{
    if(!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStyleGrouped];

        _tableView.separatorStyle  = UITableViewCellSeparatorStyleNone;
        _tableView.showsHorizontalScrollIndicator = NO;
        _tableView.showsVerticalScrollIndicator   = NO;
        _tableView.delegate   = self;
        _tableView.dataSource = self;
        _tableView.rowHeight = iPadValue(88, 60);
        _tableView.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        
        [_tableView registerClass:[TranslateSearchCell class] forCellReuseIdentifier:NSStringFromClass([TranslateSearchCell class])];
        
        UIView* view = [[UIView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, iPadValue(30, 15))];
        view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        _tableView.tableHeaderView = view;
        
        UIWindow* window = [NSObject normalWindow];
        float offset = iPadValue(30, 10) + window.safeAreaInsets.bottom;
        view = [[UIView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, offset)];
        view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        _tableView.tableFooterView = view;
        
        _tableView.sectionFooterHeight = 0.0;
        _tableView.sectionHeaderHeight = 0.0f;
    }
    
    return _tableView;
}

- (NSMutableArray *)model
{
    if(!_model) {
        _model = [NSMutableArray array];
    }
    
    return _model;
}



@end
