//
//  TranslateToolBar.m
//  PPBrowser
//
//  Created by qingbin on 2024/8/21.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import "TranslateToolBar.h"

#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "UIView+FrameHelper.h"
#import "NSObject+Helper.h"

#import "ThemeProtocol.h"
#import "PreferenceManager.h"

#import "PPNotifications.h"
#import "BrowserUtils.h"
#import "PaddingNewLabel.h"

#import "CustomTitleAndImageView.h"
#import "CommonDataManager.h"

@interface TranslateToolBar()

@property (nonatomic, strong) UIVisualEffectView *blurView;

@property (nonatomic, strong) UIStackView *stackView;
//关闭按钮
@property (nonatomic, strong) CustomTitleAndImageView *closeButton;
//设置按钮
@property (nonatomic, strong) CustomTitleAndImageView *settingButton;
//翻译按钮/显示原文按钮
@property (nonatomic, strong) PaddingNewLabel *translateButton;

//加载的loading
@property (nonatomic, strong) UIActivityIndicatorView* activityIndicator;

@end

@implementation TranslateToolBar

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    
    if(self) {
        self.backgroundColor = UIColor.clearColor;
        
        float height = [TranslateToolBar height];
        self.layer.cornerRadius = height/2.0;
        self.layer.masksToBounds = YES;
        
        [self addSubviews];
        [self defineLayout];
        [self handleEvents];
        
        [self updateWithModel];
    }
    
    return self;
}

- (void)dealloc
{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)updateWithModel
{
    TranslateStatus translateStatus = [CommonDataManager shareInstance].translateStatus;

    if(translateStatus == TranslateStatusDefault) {
        self.activityIndicator.hidden = YES;
        [self.activityIndicator stopAnimating];
        
        self.translateButton.text = NSLocalizedString(@"translate.translate", nil);
    } else if(translateStatus == TranslateStatusLoading) {
        self.activityIndicator.hidden = NO;
        [self.activityIndicator startAnimating];
        
        self.translateButton.text = @"";
    } else if(translateStatus == TranslateStatusRestore) {
        self.activityIndicator.hidden = YES;
        [self.activityIndicator stopAnimating];
        
        self.translateButton.text = NSLocalizedString(@"translate.restore", nil);
    }
}

//如果默认状态，尝试直接翻译(不要用户点击"翻译"时再开始)
- (void)startTranslateIfNeed
{
    TranslateStatus translateStatus = [CommonDataManager shareInstance].translateStatus;
    if(translateStatus == TranslateStatusDefault) {
        [CommonDataManager shareInstance].translateStatus = TranslateStatusLoading;
        
        [self updateWithModel];
        
        if(self.translateAction) {
            self.translateAction();
        }
    }
}

+ (float)height
{
    return iPadValue(78, 50);
}

+ (float)padding
{
    return iPadValue(10, 5);
}

- (void)handleEvents
{
    UITapGestureRecognizer* tap = [UITapGestureRecognizer new];
    [self.translateButton addGestureRecognizer:tap];
    
    @weakify(self)
    [tap.rac_gestureSignal subscribeNext:^(id x) {
        @strongify(self)
        TranslateStatus translateStatus = [CommonDataManager shareInstance].translateStatus;
        if(translateStatus == TranslateStatusDefault) {
            [CommonDataManager shareInstance].translateStatus = TranslateStatusLoading;
            
            [self updateWithModel];
            
            if(self.translateAction) {
                self.translateAction();
            }
        } else if(translateStatus == TranslateStatusRestore) {
            [CommonDataManager shareInstance].translateStatus = TranslateStatusDefault;
            
            [self updateWithModel];
            
            if(self.restoreAction) {
                self.restoreAction();
            }
        }
    }];
    
    [self.closeButton setTapAction:^{
        @strongify(self)
        if(self.closeAction) {
            self.closeAction();
        }
    }];
    
    [self.settingButton setTapAction:^{
        @strongify(self)
        if(self.settingAction) {
            self.settingAction();
        }
    }];
    
    //监听 从加载中到"显示原文"的变化通知
    [[NSNotificationCenter defaultCenter] addObserverForName:kTranslateStatusDidChangedNotification object:nil queue:nil usingBlock:^(NSNotification * _Nonnull notification) {
        @strongify(self)
        //从加载中变成"显示原文"
        [self updateWithModel];
    }];
    
    //监听 网页刷新的通知
    [[NSNotificationCenter defaultCenter] addObserverForName:kReloadCurrentWebViewNotification object:nil queue:nil usingBlock:^(NSNotification * _Nonnull notification) {
        //重置状态
        @strongify(self)
        [self reset];
    }];
}

#pragma mark -- 重置状态
- (void)reset
{
    [CommonDataManager shareInstance].translateStatus = TranslateStatusDefault;
    
    [self updateWithModel];
}

#pragma mark -- layout

- (void)addSubviews
{
    [self addSubview:self.blurView];
    [self addSubview:self.stackView];
    
    [self.translateButton addSubview:self.activityIndicator];
}

- (void)defineLayout
{
    [self.blurView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
    
    float height = [TranslateToolBar height];
    float padding = [TranslateToolBar padding];
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.mas_offset(padding);
        make.right.bottom.mas_offset(-padding);
    }];
    
    [self.closeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(height-padding*2);
    }];
    
    [self.settingButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(height-padding*2);
    }];
    
    [self.translateButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(height-padding*2);
        make.width.mas_equalTo(iPadValue(150, 120));
    }];
    
    [self.activityIndicator mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.translateButton);
    }];
}

#pragma mark -- Getter

- (UIVisualEffectView *)blurView
{
    if(!_blurView) {
        UIBlurEffect* blurEffect = [UIBlurEffect effectWithStyle:UIBlurEffectStyleLight];
        UIVisualEffectView* blurView = [[UIVisualEffectView alloc]initWithEffect:blurEffect];
        _blurView = blurView;
    }
    
    return _blurView;
}

- (UIStackView *)stackView
{
    if(!_stackView) {
        _stackView = [[UIStackView alloc]initWithArrangedSubviews:@[
            self.closeButton,
            self.settingButton,
            self.translateButton
        ]];
        _stackView.axis = UILayoutConstraintAxisHorizontal;
        _stackView.spacing = 10;
    }
    
    return _stackView;
}

- (CustomTitleAndImageView *)closeButton
{
    if(!_closeButton) {
        float height = [TranslateToolBar height];
        float padding = [TranslateToolBar padding];
        
        _closeButton = [[CustomTitleAndImageView alloc]initWithLayout:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            [imageView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.size.mas_equalTo(iPadValue(40, 25));
                make.center.mas_offset(0);
            }];
            
            UIImage* image = [[UIImage imageNamed:@"common_close_icon"] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
            imageView.image = image;
            imageView.tintColor = [UIColor colorWithHexString:@"#3d4048"];
        }];
        
        _closeButton.layer.cornerRadius = (height-2*padding)/2.0;
        _closeButton.layer.masksToBounds = YES;
        
        _closeButton.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
    
    return _closeButton;
}

- (CustomTitleAndImageView *)settingButton
{
    if(!_settingButton) {
        float height = [TranslateToolBar height];
        float padding = [TranslateToolBar padding];
        
        _settingButton = [[CustomTitleAndImageView alloc]initWithLayout:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            [imageView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.size.mas_equalTo(iPadValue(45, 30));
                make.center.mas_offset(0);
            }];
            
            UIImage* image = [[UIImage imageNamed:@"translate_setting_icon"] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
            imageView.image = image;
            imageView.tintColor = [UIColor colorWithHexString:@"#3d4048"];
            imageView.backgroundColor = UIColor.clearColor;
        }];
        
        _settingButton.layer.cornerRadius = (height-2*padding)/2.0;
        _settingButton.layer.masksToBounds = YES;
        
        _settingButton.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
    
    return _settingButton;
}

- (PaddingNewLabel *)translateButton
{
    if(!_translateButton) {
        _translateButton = [PaddingNewLabel new];
        _translateButton.text = NSLocalizedString(@"translate.translate", nil);
        _translateButton.textColor = UIColor.whiteColor;
        
        _translateButton.font = [UIFont systemFontOfSize:iPadValue(20, 16)];
        _translateButton.backgroundColor = [UIColor colorWithHexString:@"#2D7AFE"];
        
        _translateButton.userInteractionEnabled = YES;
        
        float height = [TranslateToolBar height];
        float padding = [TranslateToolBar padding];
        _translateButton.layer.cornerRadius = (height-2*padding)/2.0;
        _translateButton.layer.masksToBounds = YES;
    }
    
    return _translateButton;
}

- (UIActivityIndicatorView *)activityIndicator
{
    if(!_activityIndicator) {
        _activityIndicator = [[UIActivityIndicatorView alloc]initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleMedium];
        _activityIndicator.hidden = YES;
    }
    
    return _activityIndicator;
}

@end
