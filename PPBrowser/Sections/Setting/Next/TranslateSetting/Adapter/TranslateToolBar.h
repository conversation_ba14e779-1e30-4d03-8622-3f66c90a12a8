//
//  TranslateToolBar.h
//  PPBrowser
//
//  Created by qingbin on 2024/8/21.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import <UIKit/UIKit.h>

#import "PPEnums.h"
#import "TranslateManager.h"

@interface TranslateToolBar : UIView

+ (float)height;

//如果默认状态，尝试直接翻译(不要用户点击"翻译"时再开始)
- (void)startTranslateIfNeed;

//关闭
@property (nonatomic, copy) void (^closeAction)(void);
//打开设置页
@property (nonatomic, copy) void (^settingAction)(void);
//翻译
@property (nonatomic, copy) void (^translateAction)(void);
//显示原文
@property (nonatomic, copy) void (^restoreAction)(void);

@end

