//
//  TranslateManager.m
//  PPBrowser
//
//  Created by qingbin on 2024/8/21.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import "TranslateManager.h"
#import "PreferenceManager.h"
#import "PPEnums.h"

//static NSString *languageShortNames[] = {
//    [LanguageEnglish] = @"en",
//    [LanguageSimplifiedChinese] = @"zh-CN",
//    [LanguageTraditionalChinese] = @"zh-TW",
//    [LanguageArabic] = @"ar",
//    [LanguageBulgarian] = @"bg",
//    [LanguageCatalan] = @"ca",
//    [LanguageCroatian] = @"hr",
//    [LanguageCzech] = @"cs",
//    [LanguageDanish] = @"da",
//    [LanguageDutch] = @"nl",
//    [LanguageFinnish] = @"fi",
//    [LanguageFrench] = @"fr",
//    [LanguageGerman] = @"de",
//    [LanguageGreek] = @"el",
//    [LanguageHindi] = @"hi",
//    [LanguageHungarian] = @"hu",
//    [LanguageIndonesian] = @"id",
//    [LanguageItalian] = @"it",
//    [LanguageJapanese] = @"ja",
//    [LanguageKorean] = @"ko",
//    [LanguageMalay] = @"ms",
//    [LanguageMaltese] = @"mt",
//    [LanguageNorwegian] = @"nb",
//    [LanguagePolish] = @"pl",
//    [LanguagePortuguese] = @"pt",
//    [LanguageRomanian] = @"ro",
//    [LanguageRussian] = @"ru",
//    [LanguageSlovak] = @"sk",
//    [LanguageSlovenian] = @"sl",
//    [LanguageSpanish] = @"es",
//    [LanguageSwedish] = @"sv",
//    [LanguageTamil] = @"ta",
//    [LanguageTelugu] = @"te",
//    [LanguageThai] = @"th",
//    [LanguageTurkish] = @"tr",
//    [LanguageUkrainian] = @"uk",
//    [LanguageVietnamese] = @"vi"
//};
//
//static NSString *languageDisplayNames[] = {
//    [LanguageEnglish] = @"English",
//    [LanguageSimplifiedChinese] = @"简体中文",
//    [LanguageTraditionalChinese] = @"繁體中文",
//    [LanguageJapanese] = @"日本語",
//    [LanguageKorean] = @"한국어",
//    [LanguageThai] = @"ไทย",
//    [LanguageVietnamese] = @"Tiếng Việt",
//    [LanguageIndonesian] = @"Indonesia",
//    [LanguageArabic] = @"العربية",
//    [LanguageBulgarian] = @"Български",
//    [LanguageCatalan] = @"Català",
//    [LanguageCroatian] = @"Hrvatski",
//    [LanguageCzech] = @"Čeština",
//    [LanguageDanish] = @"Dansk",
//    [LanguageDutch] = @"Nederlands",
//    [LanguageFinnish] = @"Suomi",
//    [LanguageFrench] = @"Français",
//    [LanguageGerman] = @"Deutsch",
//    [LanguageGreek] = @"Ελληνικά",
//    [LanguageHindi] = @"हिन्दी",
//    [LanguageHungarian] = @"Magyar",
//    [LanguageItalian] = @"Italiano",
//    [LanguageMalay] = @"Melayu",
//    [LanguageMaltese] = @"Malti",
//    [LanguageNorwegian] = @"Norsk Bokmål",
//    [LanguagePolish] = @"Polski",
//    [LanguagePortuguese] = @"Português",
//    [LanguageRomanian] = @"Română",
//    [LanguageRussian] = @"Русский",
//    [LanguageSlovak] = @"Slovenčina",
//    [LanguageSlovenian] = @"Slovenščina",
//    [LanguageSpanish] = @"Español",
//    [LanguageSwedish] = @"Svenska",
//    [LanguageTamil] = @"தமிழ்",
//    [LanguageTelugu] = @"తెలుగు",
//    [LanguageTurkish] = @"Türkçe",
//    [LanguageUkrainian] = @"Українська",
//};

@implementation TranslateManager

+ (NSDictionary<NSNumber *, TranslateLanguageModel *> *)createLanguageDictionary
{
    NSMutableDictionary<NSNumber *, TranslateLanguageModel *> *languageDictionary = [NSMutableDictionary dictionary];

    languageDictionary[@(LanguageEnglish)] = [[TranslateLanguageModel alloc] initWithLanguage:LanguageEnglish shortName:@"en" displayName:@"English"];
        languageDictionary[@(LanguageSimplifiedChinese)] = [[TranslateLanguageModel alloc] initWithLanguage:LanguageSimplifiedChinese shortName:@"zh-CN" displayName:@"简体中文"];
        languageDictionary[@(LanguageTraditionalChinese)] = [[TranslateLanguageModel alloc] initWithLanguage:LanguageTraditionalChinese shortName:@"zh-TW" displayName:@"繁體中文"];
        languageDictionary[@(LanguageJapanese)] = [[TranslateLanguageModel alloc] initWithLanguage:LanguageJapanese shortName:@"ja" displayName:@"日本語"];
        languageDictionary[@(LanguageKorean)] = [[TranslateLanguageModel alloc] initWithLanguage:LanguageKorean shortName:@"ko" displayName:@"한국어"];
        languageDictionary[@(LanguageThai)] = [[TranslateLanguageModel alloc] initWithLanguage:LanguageThai shortName:@"th" displayName:@"ไทย"];
        languageDictionary[@(LanguageVietnamese)] = [[TranslateLanguageModel alloc] initWithLanguage:LanguageVietnamese shortName:@"vi" displayName:@"Tiếng Việt"];
        languageDictionary[@(LanguageIndonesian)] = [[TranslateLanguageModel alloc] initWithLanguage:LanguageIndonesian shortName:@"id" displayName:@"Indonesia"];
        languageDictionary[@(LanguageArabic)] = [[TranslateLanguageModel alloc] initWithLanguage:LanguageArabic shortName:@"ar" displayName:@"العربية"];
        languageDictionary[@(LanguageBulgarian)] = [[TranslateLanguageModel alloc] initWithLanguage:LanguageBulgarian shortName:@"bg" displayName:@"Български"];
        languageDictionary[@(LanguageCatalan)] = [[TranslateLanguageModel alloc] initWithLanguage:LanguageCatalan shortName:@"ca" displayName:@"Català"];
        languageDictionary[@(LanguageCroatian)] = [[TranslateLanguageModel alloc] initWithLanguage:LanguageCroatian shortName:@"hr" displayName:@"Hrvatski"];
        languageDictionary[@(LanguageCzech)] = [[TranslateLanguageModel alloc] initWithLanguage:LanguageCzech shortName:@"cs" displayName:@"Čeština"];
        languageDictionary[@(LanguageDanish)] = [[TranslateLanguageModel alloc] initWithLanguage:LanguageDanish shortName:@"da" displayName:@"Dansk"];
        languageDictionary[@(LanguageDutch)] = [[TranslateLanguageModel alloc] initWithLanguage:LanguageDutch shortName:@"nl" displayName:@"Nederlands"];
        languageDictionary[@(LanguageFinnish)] = [[TranslateLanguageModel alloc] initWithLanguage:LanguageFinnish shortName:@"fi" displayName:@"Suomi"];
        languageDictionary[@(LanguageFrench)] = [[TranslateLanguageModel alloc] initWithLanguage:LanguageFrench shortName:@"fr" displayName:@"Français"];
        languageDictionary[@(LanguageGerman)] = [[TranslateLanguageModel alloc] initWithLanguage:LanguageGerman shortName:@"de" displayName:@"Deutsch"];
        languageDictionary[@(LanguageGreek)] = [[TranslateLanguageModel alloc] initWithLanguage:LanguageGreek shortName:@"el" displayName:@"Ελληνικά"];
        languageDictionary[@(LanguageHindi)] = [[TranslateLanguageModel alloc] initWithLanguage:LanguageHindi shortName:@"hi" displayName:@"हिन्दी"];
        languageDictionary[@(LanguageHungarian)] = [[TranslateLanguageModel alloc] initWithLanguage:LanguageHungarian shortName:@"hu" displayName:@"Magyar"];
        languageDictionary[@(LanguageItalian)] = [[TranslateLanguageModel alloc] initWithLanguage:LanguageItalian shortName:@"it" displayName:@"Italiano"];
        languageDictionary[@(LanguageMalay)] = [[TranslateLanguageModel alloc] initWithLanguage:LanguageMalay shortName:@"ms" displayName:@"Melayu"];
        languageDictionary[@(LanguageMaltese)] = [[TranslateLanguageModel alloc] initWithLanguage:LanguageMaltese shortName:@"mt" displayName:@"Malti"];
        languageDictionary[@(LanguageNorwegian)] = [[TranslateLanguageModel alloc] initWithLanguage:LanguageNorwegian shortName:@"nb" displayName:@"Norsk Bokmål"];
        languageDictionary[@(LanguagePolish)] = [[TranslateLanguageModel alloc] initWithLanguage:LanguagePolish shortName:@"pl" displayName:@"Polski"];
        languageDictionary[@(LanguagePortuguese)] = [[TranslateLanguageModel alloc] initWithLanguage:LanguagePortuguese shortName:@"pt" displayName:@"Português"];
        languageDictionary[@(LanguageRomanian)] = [[TranslateLanguageModel alloc] initWithLanguage:LanguageRomanian shortName:@"ro" displayName:@"Română"];
        languageDictionary[@(LanguageRussian)] = [[TranslateLanguageModel alloc] initWithLanguage:LanguageRussian shortName:@"ru" displayName:@"Русский"];
        languageDictionary[@(LanguageSlovak)] = [[TranslateLanguageModel alloc] initWithLanguage:LanguageSlovak shortName:@"sk" displayName:@"Slovenčina"];
        languageDictionary[@(LanguageSlovenian)] = [[TranslateLanguageModel alloc] initWithLanguage:LanguageSlovenian shortName:@"sl" displayName:@"Slovenščina"];
        languageDictionary[@(LanguageSpanish)] = [[TranslateLanguageModel alloc] initWithLanguage:LanguageSpanish shortName:@"es" displayName:@"Español"];
        languageDictionary[@(LanguageSwedish)] = [[TranslateLanguageModel alloc] initWithLanguage:LanguageSwedish shortName:@"sv" displayName:@"Svenska"];
        languageDictionary[@(LanguageTamil)] = [[TranslateLanguageModel alloc] initWithLanguage:LanguageTamil shortName:@"ta" displayName:@"தமிழ்"];
        languageDictionary[@(LanguageTelugu)] = [[TranslateLanguageModel alloc] initWithLanguage:LanguageTelugu shortName:@"te" displayName:@"తెలుగు"];
        languageDictionary[@(LanguageTurkish)] = [[TranslateLanguageModel alloc] initWithLanguage:LanguageTurkish shortName:@"tr" displayName:@"Türkçe"];
        languageDictionary[@(LanguageUkrainian)] = [[TranslateLanguageModel alloc] initWithLanguage:LanguageUkrainian shortName:@"uk" displayName:@"Українська"];


    return languageDictionary;
}

//根据 language 枚举的顺序排序
+ (NSArray<TranslateLanguageModel *> *)createSortedLanguageArrayFromDictionary
{
    // 获取字典
    NSDictionary<NSNumber *, TranslateLanguageModel *> *languageDictionary = [self createLanguageDictionary];
    
    // 提取字典中的值，并根据 language 枚举的顺序排序
    NSArray<TranslateLanguageModel *> *sortedLanguageArray = [[languageDictionary allValues] sortedArrayUsingComparator:^NSComparisonResult(TranslateLanguageModel *obj1, TranslateLanguageModel *obj2) {
        return [@(obj1.language) compare:@(obj2.language)];
    }];

    return sortedLanguageArray;
}

//获取翻译结果的简称
+ (NSString *)getLanguageShortName:(Language)language
{
    NSDictionary<NSNumber *, TranslateLanguageModel *> *languageDictionary = [self createLanguageDictionary];
    TranslateLanguageModel* obj = languageDictionary[@(language)];
    
    if(obj) return obj.shortName;
    return nil;
}

//获取翻译结果的显示名称
+ (NSString *)getLanguageDisplayName:(Language)language
{
    NSDictionary<NSNumber *, TranslateLanguageModel *> *languageDictionary = [self createLanguageDictionary];
    TranslateLanguageModel* obj = languageDictionary[@(language)];
    
    if(obj) return obj.displayName;
    return nil;
}

//开始翻译网页
+ (void)startTranslatePageWithWebView:(WKWebView *)webView
{
    if(!webView) return;
    
    TranslateEngine engine = [[PreferenceManager shareInstance].items.translateEngine intValue];
    Language targetLanguage = [[PreferenceManager shareInstance].items.tranlsateTargetLanguage intValue];
    
    NSString* shortName = [self getLanguageShortName:targetLanguage];
    
    NSString *js = [NSString stringWithFormat:@"window.startTranslate('auto', '%@', %ld)", shortName, (long)engine];
    [webView evaluateJavaScript:js completionHandler:nil];
}

//显示原文
+ (void)restoreTranslateWithWebView:(WKWebView *)webView
{
    if(!webView) return;
    
    TranslateEngine engine = [[PreferenceManager shareInstance].items.translateEngine intValue];
    
    NSString *js = [NSString stringWithFormat:@"window.restoreTranslatePage(%ld)",(long)engine];
    [webView evaluateJavaScript:js completionHandler:nil];
}

@end
