//
//  TranslateLanguageModel.h
//  PPBrowser
//
//  Created by qing<PERSON> on 2024/8/21.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "PPEnums.h"

@interface TranslateLanguageModel : NSObject

@property (nonatomic, assign) Language language;

@property (nonatomic, strong) NSString *shortName;

@property (nonatomic, strong) NSString *displayName;

//是否选中
@property (nonatomic, assign) BOOL isSelected;
//辅助字段,主要是卡片化
@property (nonatomic, assign) BOOL isFirstInSection;
@property (nonatomic, assign) BOOL isLastInSection;

//初始化方法
- (instancetype)initWithLanguage:(NSInteger)language
                       shortName:(NSString *)shortName
                     displayName:(NSString *)displayName;

@end

