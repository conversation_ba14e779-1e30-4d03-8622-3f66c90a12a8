//
//  TranslateManager.h
//  PPBrowser
//
//  Created by qingbin on 2024/8/21.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "PPEnums.h"
#import <WebKit/WebKit.h>

#import "TranslateLanguageModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface TranslateManager : NSObject

//根据 language 枚举的顺序排序
+ (NSArray<TranslateLanguageModel *> *)createSortedLanguageArrayFromDictionary;

//获取翻译结果的简称
+ (NSString *)getLanguageShortName:(Language)language;
//获取翻译结果的显示名称
+ (NSString *)getLanguageDisplayName:(Language)language;

//翻译网页
+ (void)startTranslatePageWithWebView:(WKWebView *)webView;
//显示原文
+ (void)restoreTranslateWithWebView:(WKWebView *)webView;

@end

NS_ASSUME_NONNULL_END
