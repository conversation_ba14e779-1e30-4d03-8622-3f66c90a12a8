//
//  TranslateSettingController.m
//  PPBrowser
//
//  Created by qingbin on 2024/8/20.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import "TranslateSettingController.h"

#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "UIView+FrameHelper.h"
#import "NSObject+Helper.h"

#import "ThemeProtocol.h"
#import "PreferenceManager.h"

#import "PPNotifications.h"
#import "BrowserUtils.h"
#import "SettingTextAndSelectView.h"
#import "SettingTextAndArrowView.h"

#import "TranslateManager.h"
#import "TranslateSearchController.h"
#import "BaseNavigationController.h"

@interface TranslateSettingController ()<ThemeProtocol>
//标题
@property (nonatomic, strong) UILabel *titleLabel;
//
@property (nonatomic, strong) UIStackView* stackView;
//微软翻译
@property (nonatomic, strong) SettingTextAndSelectView* bingView;
//谷歌翻译
@property (nonatomic, strong) SettingTextAndSelectView* googleView;
//目标语言标题
@property (nonatomic, strong) UILabel *targetTitleLabel;
//目标语言
@property (nonatomic, strong) SettingTextAndArrowView *targetLanguageView;

@end

@implementation TranslateSettingController

- (void)viewDidLoad 
{
    [super viewDidLoad];

    self.title = NSLocalizedString(@"setting.translate", nil);
    
    [self addSubviews];
    [self defineLayout];
    
    [self createCustomLeftBarButtonItem];
    [self updateWithModel];
    [self handleEvents];
}

- (void)updateWithModel
{
    TranslateEngine engine = [[PreferenceManager shareInstance].items.translateEngine intValue];
    Language targetLanguage = [[PreferenceManager shareInstance].items.tranlsateTargetLanguage intValue];
    
    [self.bingView updateWithTitle:NSLocalizedString(@"translate.bing", nil) isSelect:(engine == TranslateEngineBing)];
    [self.googleView updateWithTitle:NSLocalizedString(@"translate.google", nil) isSelect:(engine == TranslateEngineGoogle)];
    
    [self.targetLanguageView updateWithTitle:NSLocalizedString(@"translate.target.language", nil) content:[TranslateManager getLanguageDisplayName:targetLanguage]];
    
    [self applyTheme];
}

- (void)handleEvents
{
    @weakify(self)
    [self.bingView setDidAction:^{
        @strongify(self)
        [PreferenceManager shareInstance].items.translateEngine = @(TranslateEngineBing);
        [[PreferenceManager shareInstance] encode];
        [self updateWithModel];
        
        //翻译引擎发生变化，重新刷新网页
        [[NSNotificationCenter defaultCenter] postNotificationName:kReloadCurrentWebViewNotification object:nil];
    }];
    
    [self.googleView setDidAction:^{
        @strongify(self)
        [PreferenceManager shareInstance].items.translateEngine = @(TranslateEngineGoogle);
        [[PreferenceManager shareInstance] encode];
        [self updateWithModel];
        
        //翻译引擎发生变化，重新刷新网页
        [[NSNotificationCenter defaultCenter] postNotificationName:kReloadCurrentWebViewNotification object:nil];
    }];
    
    [self.targetLanguageView setDidAction:^{
        @strongify(self)
        TranslateSearchController* vc = [TranslateSearchController new];
        BaseNavigationController* navc = [[BaseNavigationController alloc]initWithRootViewController:vc];
        
        @weakify(self)
        [vc setChangeLanguageAction:^(Language targetLanguage) {
            @strongify(self)
            //更换目标语言
            [self updateWithModel];
        }];
        
//        if ([BrowserUtils isiPad]) {
//            // iPad
//            navc.modalPresentationStyle = UIModalPresentationFormSheet;
//            vc.preferredContentSize = CGSizeMake(kScreenWidth-90, kScreenHeight-90);
//            
//            [self presentViewController:navc animated:YES completion:nil];
//        } else {
//            // iPhone
//            [self presentViewController:navc animated:YES completion:nil];
//        }
        //v2.6.8,统一present
        [self presentCustomToViewController:navc];
    }];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
    } else {
        self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    //只有当前的controller会触发一次, 其它已存在的controller走通知
    if(needReload) {
        //修改暗黑模式
        [self applyTheme];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}

#pragma mark -- layout
- (void)addSubviews
{
    [self.view addSubview:self.titleLabel];
    [self.view addSubview:self.stackView];
    [self.view addSubview:self.targetTitleLabel];
    [self.view addSubview:self.targetLanguageView];
}

- (void)defineLayout
{
    float offset = iPadValue(30, 15);
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(iPadValue(60, 30));
        make.top.equalTo(self.view).offset(iPadValue(40, 25));
        make.right.equalTo(self.stackView).offset(-5);
    }];
    
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.titleLabel.mas_bottom).offset(iPadValue(20, 10));
        make.left.mas_offset(offset);
        make.right.mas_offset(-offset);
    }];
    
    [self.bingView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(self.stackView);
        make.height.mas_equalTo([SettingTextAndSelectView height]);
    }];
    
    [self.googleView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(self.stackView);
        make.height.mas_equalTo([SettingTextAndSelectView height]);
    }];
    
    [self.targetTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(iPadValue(60, 30));
        make.top.equalTo(self.stackView.mas_bottom).offset(iPadValue(40, 25));
    }];
    
    [self.targetLanguageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.targetTitleLabel.mas_bottom).offset(iPadValue(20, 10));
        make.left.mas_offset(offset);
        make.right.mas_offset(-offset);
        make.height.mas_equalTo([SettingTextAndArrowView height]);
    }];
}

#pragma mark -- Getters

- (UILabel *)titleLabel
{
    if(!_titleLabel) {
        _titleLabel = [UIView createLabelWithTitle:NSLocalizedString(@"translate.select.text", nil)
                                         textColor:[UIColor colorWithHexString:@"#666666"]
                                           bgColor:UIColor.clearColor
                                          fontSize:iPadValue(18, 14)
                                     textAlignment:NSTextAlignmentLeft
                                             bBold:NO];
        _titleLabel.numberOfLines = 0;
    }
    
    return _titleLabel;
}

- (UIStackView *)stackView
{
    if(!_stackView) {
        _stackView = [[UIStackView alloc]initWithArrangedSubviews:@[
            self.bingView,
            self.googleView
        ]];
        _stackView.axis = UILayoutConstraintAxisVertical;
        _stackView.spacing = 0;
        
        _stackView.layer.cornerRadius = 10;
        _stackView.layer.masksToBounds = YES;
    }
    
    return _stackView;
}

- (SettingTextAndSelectView *)bingView
{
    if(!_bingView) {
        _bingView = [[SettingTextAndSelectView alloc]initWithShowLine:YES];
    }
    
    return _bingView;
}

- (SettingTextAndSelectView *)googleView
{
    if(!_googleView) {
        _googleView = [[SettingTextAndSelectView alloc]initWithShowLine:NO];
    }
    
    return _googleView;
}

- (UILabel *)targetTitleLabel
{
    if(!_targetTitleLabel) {
        _targetTitleLabel = [UIView createLabelWithTitle:NSLocalizedString(@"translate.select.target.text", nil)
                                         textColor:[UIColor colorWithHexString:@"#666666"]
                                           bgColor:UIColor.clearColor
                                          fontSize:iPadValue(18, 14)
                                     textAlignment:NSTextAlignmentLeft
                                             bBold:NO];
    }
    
    return _targetTitleLabel;
}

- (SettingTextAndArrowView *)targetLanguageView
{
    if(!_targetLanguageView) {
        _targetLanguageView = [[SettingTextAndArrowView alloc]initWithShowLine:NO];
        _targetLanguageView.layer.cornerRadius = 10;
        _targetLanguageView.layer.masksToBounds = YES;
    }
    
    return _targetLanguageView;
}

@end
