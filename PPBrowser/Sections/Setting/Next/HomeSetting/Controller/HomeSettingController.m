//
//  HomeSettingController.m
//  PPBrowser
//
//  Created by qingbin on 2022/6/6.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "HomeSettingController.h"

#import "Masonry.h"
#import "MaizyHeader.h"
#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "ReactiveCocoa.h"
#import "UIView+FrameHelper.h"
#import "UIViewController+Helper.h"
#import "NSObject+Helper.h"

#import "SettingSwitchView.h"
#import "CustomTagModel.h"
#import "DatabaseUnit+CustomTag.h"

#import "PPNotifications.h"
#import "SettingArrowView.h"
#import "WallPaperController.h"
#import "BrowserUtils.h"
#import "CustomTextField.h"

#import "TPKeyboardAvoidingScrollView.h"
#import "PaddingNewLabel.h"

#import "HomeHelper.h"
#import "InternalURL.h"

@interface HomeSettingController ()<UITextFieldDelegate, UIScrollViewDelegate>

@property (nonatomic, strong) TPKeyboardAvoidingScrollView *scrollView;

@property (nonatomic, strong) UIView *contentView;

@property (nonatomic, strong) UIView* backView;

@property (nonatomic, strong) UIStackView* stackView;
//设置壁纸
@property (nonatomic, strong) SettingArrowView* wallpaperView;
//显示书签
@property (nonatomic, strong) SettingSwitchView *bookMarkView;
//显示用户指南
@property (nonatomic, strong) SettingSwitchView *guidelineView;
//显示秘塔AI，只有大陆地区才会显示
@property (nonatomic, strong) SettingSwitchView *metasoView;
//自定义主页
@property (nonatomic, strong) UILabel *customUrlLabel;
//自定义主页
@property (nonatomic, strong) CustomTextField *customUrlTextField;
//温馨提示
@property (nonatomic, strong) UILabel *customTipsLabel;

@property (nonatomic, strong) NSMutableArray* homeItems;

@property (nonatomic, strong) CustomTagModel* bookMarkModel;

@property (nonatomic, strong) CustomTagModel* guidelineModel;

@property (nonatomic, strong) CustomTagModel* metasoModel;

@end

@implementation HomeSettingController

- (void)viewDidLoad
{
    [super viewDidLoad];

    self.title = NSLocalizedString(@"setting.homeSetting", nil);
    
    [self addSubviews];
    [self defineLayout];
    [self updateWithModel];
    [self setupObservers];
    [self createCustomLeftBarButtonItem];
    
    [self applyTheme];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        
        self.customUrlTextField.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        self.customUrlTextField.textColor = [UIColor colorWithHexString:@"#ffffff"];
    } else {
        self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        
        self.customUrlTextField.backgroundColor = [UIColor colorWithHexString:@"#ffffff"];
        self.customUrlTextField.textColor = [UIColor colorWithHexString:@"#333333"];
    }
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    //只有当前的controller会触发一次, 其它已存在的controller走通知
    if(needReload) {
        //修改暗黑模式
        [self applyTheme];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}

- (void)updateWithModel
{
    DatabaseUnit* unit = [DatabaseUnit queryAllCustomTags];
    @weakify(self)
    [unit setCompleteBlock:^(NSArray* result, BOOL success) {
        @strongify(self)
        if(success) {
            BOOL hasBookMark = false;
            BOOL hasGuideline = false;
            BOOL hasMetaso = false;
            
            for(CustomTagModel* item in result) {
                if(item.type == CustomTagTypeBookMark) {
                    hasBookMark = true;
                    self.bookMarkModel = item;
                } else if(item.type == CustomTagTypeGuideline) {
                    hasGuideline = true;
                    self.guidelineModel = item;
                } else if(item.type == CustomTagTypeMetaso) {
                    hasMetaso = true;
                    self.metasoModel = item;
                } else if(item.type == 2) {
                }
            }
            
            self.homeItems = [NSMutableArray array];
            [self.homeItems addObjectsFromArray:result];
            
            if(!hasBookMark) {
                self.bookMarkModel = [CustomTagModel bookMark];
            }
            
            if(!hasGuideline) {
                self.guidelineModel = [CustomTagModel guideline];
            }
            
            if(!hasMetaso) {
                self.metasoModel = [CustomTagModel metaso];
            }
            
            [self.bookMarkView updateWithTitle:NSLocalizedString(@"homeSetting.bookmark.text", nil) isOn:hasBookMark];
            [self.guidelineView updateWithTitle:NSLocalizedString(@"homeSetting.guideline.text", nil) isOn:hasGuideline];
            [self.metasoView updateWithTitle:@"显示秘塔AI" isOn:hasMetaso];
        }
    }];
    
    DB_EXEC(unit);
    
    NSString* customUrl = [HomeHelper getCustomUrl];
    if([InternalURL isAboutHomeURL:customUrl]) {
        //如果是默认值则显示未空
        customUrl = @"";
    }
    self.customUrlTextField.text = customUrl;
}

- (void)setupObservers
{
    @weakify(self)
    [self.wallpaperView setDidAction:^{
        @strongify(self)
        WallPaperController* vc = [WallPaperController new];
        [self.navigationController pushViewController:vc animated:YES];
    }];
        
    [self.bookMarkView setDidSwithAction:^(BOOL isOn) {
        @strongify(self)
        if(isOn) {
            [self addItem:self.bookMarkModel];
        } else {
            [self removeItem:self.bookMarkModel];
        }
    }];
    
    [self.guidelineView setDidSwithAction:^(BOOL isOn) {
        @strongify(self)
        if(isOn) {
            [self addItem:self.guidelineModel];
        } else {
            [self removeItem:self.guidelineModel];
        }
    }];
    
    [self.metasoView setDidSwithAction:^(BOOL isOn) {
        @strongify(self)
        if(isOn) {
            [self addItem:self.metasoModel];
        } else {
            [self removeItem:self.metasoModel];
        }
    }];
}

- (void)addItem:(CustomTagModel*)item
{
    DatabaseUnit* unit = [DatabaseUnit addCustomTagWithItem:item];
    DB_EXEC(unit);
    
    [self.homeItems addObject:item];
    
    for(int i=0;i<self.homeItems.count;i++) {
        CustomTagModel* item = self.homeItems[i];
        item.ppOrder = i;
    }
    
    unit = [DatabaseUnit updateAllTagsOrder:self.homeItems];
    DB_EXEC(unit);
    
    [[NSNotificationCenter defaultCenter] postNotificationName:kAddHomeCustomTagNotification object:nil];
}

- (void)removeItem:(CustomTagModel*)item
{
    DatabaseUnit* unit = [DatabaseUnit removeCustomTagWithId:item.uuid];
    DB_EXEC(unit);
    
    [self.homeItems addObject:item];
    
    for(int i=0;i<self.homeItems.count;i++) {
        CustomTagModel* item = self.homeItems[i];
        item.ppOrder = i;
    }
    
    unit = [DatabaseUnit updateAllTagsOrder:self.homeItems];
    DB_EXEC(unit);
    
    [[NSNotificationCenter defaultCenter] postNotificationName:kAddHomeCustomTagNotification object:nil];
}

#pragma mark -- UITextFieldDelegate

- (BOOL)textFieldShouldEndEditing:(UITextField *)textField
{
    [self _handleCustomUrl];

    return YES;
}

- (BOOL)textFieldShouldReturn:(UITextField *)textField
{    
    [self.view endEditing:YES];
    
    return YES;
}

- (void)_handleCustomUrl
{
    //收键盘
    [self.view endEditing:YES];
    
    [UIView showLoading:NSLocalizedString(@"tips.handling", nil)];
    
    NSString* text = self.customUrlTextField.text;
    if(text.length > 0) {
        if([text.lowercaseString hasPrefix:@"http"] == NO) {
            text = [NSString stringWithFormat:@"https://%@",text];
        }
        
        NSString* showText = text;
        if([InternalURL isAboutHomeURL:showText]) {
            //如果是默认值则显示未空
            showText = @"";
        }
        self.customUrlTextField.text = showText;
    }
    
    [HomeHelper handleCustomUrl:text completion:^(BOOL succ) {
        if(succ) {
            [UIView showToast:NSLocalizedString(@"tips.add.success", nil)];
        } else {
            [UIView showToast:NSLocalizedString(@"tips.add.fail", nil)];
        }
    }];
}

#pragma mark -- layout
- (void)addSubviews
{
    //https://stackoverflow.com/questions/33927914/how-can-i-set-the-cornerradius-of-a-uistackview
    //UIStackView添加圆角适配
    
    [self.view addSubview:self.scrollView];
    
    [self.scrollView addSubview:self.contentView];
    [self.contentView addSubview:self.backView];
    [self.backView addSubview:self.stackView];
    
    [self.contentView addSubview:self.customUrlLabel];
    [self.contentView addSubview:self.customUrlTextField];
    [self.contentView addSubview:self.customTipsLabel];
}

- (void)defineLayout
{
    float offset = iPadValue(30, 15);
    
    [self.scrollView mas_makeConstraints:^(MASConstraintMaker* make) {
        make.top.equalTo(self.view).offset(offset);
        make.left.equalTo(self.view).offset(offset);
        make.right.equalTo(self.view).offset(-offset);
        make.bottom.equalTo(self.view);
    }];
    
    [self.contentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.scrollView);
        make.width.equalTo(self.scrollView);
        make.bottom.equalTo(self.customUrlTextField);
    }];
    
    [self.backView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.right.equalTo(self.contentView);
        make.bottom.equalTo(self.stackView);
    }];
    
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.mas_offset(0);
        make.right.mas_offset(-0);
    }];
    
    [self.wallpaperView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([SettingArrowView height]);
    }];
        
    [self.bookMarkView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([SettingSwitchView height]);
    }];
    
    [self.guidelineView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([SettingSwitchView height]);
    }];
    
    [self.metasoView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([SettingSwitchView height]);
    }];
    
    [self.customUrlLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.backView.mas_bottom).offset(2*offset);
        make.left.mas_offset(offset);
    }];
    
    [self.customUrlTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(iPadValue(65, 50));
        make.left.mas_offset(0);
        make.right.mas_offset(0);
        make.top.equalTo(self.customUrlLabel.mas_bottom).offset(iPadValue(10, 8));
    }];
    
    [self.customTipsLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.customUrlTextField.mas_bottom).offset(iPadValue(10, 8));
        make.left.mas_offset(offset);
        make.right.mas_offset(-offset);
    }];
}

#pragma mark -- lazy init

- (TPKeyboardAvoidingScrollView*)scrollView
{
    if (!_scrollView) {
        _scrollView = [TPKeyboardAvoidingScrollView new];
        _scrollView.scrollEnabled = YES;
        _scrollView.showsVerticalScrollIndicator = NO;
        _scrollView.delegate = self;
    }

    return _scrollView;
}

- (UIView *)contentView
{
    if(!_contentView) {
        _contentView = [UIView new];
        _contentView.backgroundColor = UIColor.clearColor;
    }
    
    return _contentView;
}

- (UIView *)backView
{
    if(!_backView) {
        _backView = [UIView new];
        _backView.backgroundColor = UIColor.whiteColor;
        
        _backView.layer.cornerRadius = 10;
        _backView.layer.masksToBounds = YES;
    }
    
    return _backView;
}

- (UIStackView *)stackView
{
    if(!_stackView) {
        _stackView = [[UIStackView alloc]initWithArrangedSubviews:@[
            self.wallpaperView,
            self.bookMarkView,
            self.guidelineView,
            self.metasoView,
        ]];
        
        _stackView.spacing = 0;
        _stackView.axis = UILayoutConstraintAxisVertical;

        _stackView.backgroundColor = UIColor.whiteColor;
    }
    
    return _stackView;
}

- (SettingArrowView *)wallpaperView
{
    if(!_wallpaperView) {
        _wallpaperView = [[SettingArrowView alloc]initWithShowLine:YES];
        [_wallpaperView updateWithTitle:NSLocalizedString(@"homeSetting.wallpaper.text", nil)];
    }
    
    return _wallpaperView;
}

- (SettingSwitchView *)bookMarkView
{
    if(!_bookMarkView) {
        _bookMarkView = [[SettingSwitchView alloc]initWithShowLine:YES];
    }
    
    return _bookMarkView;
}

- (SettingSwitchView *)guidelineView
{
    if(!_guidelineView) {
        LocalizableOption localOption = [BrowserUtils localizableOption];
        BOOL showMetaso = (localOption == LocalizableOptionZh_Hans || localOption == LocalizableOptionZh_Hant);
        _guidelineView = [[SettingSwitchView alloc]initWithShowLine:showMetaso];
    }
    
    return _guidelineView;
}

- (SettingSwitchView *)metasoView
{
    if(!_metasoView) {
        _metasoView = [[SettingSwitchView alloc]initWithShowLine:NO];
        
        LocalizableOption localOption = [BrowserUtils localizableOption];
        BOOL showMetaso = (localOption == LocalizableOptionZh_Hans || localOption == LocalizableOptionZh_Hant);
        _metasoView.hidden = !showMetaso;
    }
    
    return _metasoView;
}

- (UILabel *)customUrlLabel
{
    if(!_customUrlLabel) {
        float font = iPadValue(18, 15);
        _customUrlLabel = [UIView createLabelWithTitle:NSLocalizedString(@"homeSetting.customUrl.text", nil)
                                        textColor:[UIColor colorWithHexString:@"#666666"]
                                          bgColor:UIColor.clearColor
                                         fontSize:font
                                    textAlignment:NSTextAlignmentLeft
                                            bBold:NO];
    }
    
    return _customUrlLabel;
}

- (CustomTextField *)customUrlTextField
{
    if(!_customUrlTextField) {
        CustomTextField* textField = [[CustomTextField alloc] init];
        textField.font = [UIFont systemFontOfSize:iPadValue(20, 16)];
        textField.layer.cornerRadius = 10;
        textField.layer.masksToBounds = YES;
        //2.6.8 阿拉伯语布局适配
        textField.textAlignment = NSTextAlignmentLeft;
        
        textField.placeholder = NSLocalizedString(@"homeSetting.customUrl.tips", nil);
        
        textField.leftView = [UIView new];
        textField.leftViewMode = UITextFieldViewModeAlways;
        textField.leftViewRect = CGRectMake(0, 0, iPadValue(30, 15), 44);
        
        textField.textColor = [UIColor colorWithHexString:@"#333333"];
        textField.returnKeyType = UIReturnKeyDone;
        
        [textField setBackgroundColor:[UIColor colorWithHexString:@"#ffffff"]];
        [textField setClearButtonMode:UITextFieldViewModeWhileEditing];

        [textField setDelegate:self];
      
        _customUrlTextField = textField;
    }
    
    return _customUrlTextField;
}

- (UILabel *)customTipsLabel
{
    if(!_customTipsLabel) {
        float font = iPadValue(18, 15);
        _customTipsLabel = [UIView createLabelWithTitle:NSLocalizedString(@"homeSetting.customTips.text", nil)
                                        textColor:[UIColor colorWithHexString:@"#999999"]
                                          bgColor:UIColor.clearColor
                                         fontSize:font
                                    textAlignment:NSTextAlignmentLeft
                                            bBold:NO];
        _customTipsLabel.numberOfLines = 0;
    }
    
    return _customTipsLabel;
}

@end
