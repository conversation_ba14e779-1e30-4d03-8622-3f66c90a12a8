//
//  SettingSegmentView.m
//  Saber
//
//  Created by qingbin on 2023/3/14.
//

#import "SettingSegmentView.h"

#import "Masonry.h"
#import "MaizyHeader.h"
#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "ReactiveCocoa.h"
#import "UIView+FrameHelper.h"
#import "UIViewController+Helper.h"
#import "NSObject+Helper.h"
#import "BrowserUtils.h"

#import "PPNotifications.h"
#import "ThemeProtocol.h"

@interface SettingSegmentView ()

@property (nonatomic, strong) UIView* tapView;

@property (nonatomic, strong) UILabel* titleLabel;

@property (nonatomic, strong) UIImageView* infoLogo;

@property (nonatomic, strong) UISegmentedControl* segment;

@property (nonatomic, strong) UIView* line;

@property (nonatomic, assign) BOOL bShowLine;

@property (nonatomic, assign) BOOL bShowInfo;

@property (nonatomic, assign) float leftOffset;

@property (nonatomic, strong) NSArray* datas;

@end

@implementation SettingSegmentView

- (instancetype)initWithTitle:(NSString *)title
                     showLine:(BOOL)bShowLine
                     segments:(NSArray *)segments
{
    self = [super init];
    
    if(self) {
        self.bShowLine = bShowLine;
        self.datas = segments;
        self.titleLabel.text = title;
        
        [self addSubviews];
        [self defineLayout];
        [self setupObservers];
        
        [self applyTheme];
        
        self.line.hidden = !self.bShowLine;
    }
    
    return self;
}

- (instancetype)initWithTitle:(NSString *)title
                     showLine:(BOOL)bShowLine
                     showInfo:(BOOL)bShowInfo
                   leftOffset:(float)leftOffset
                     segments:(NSArray *)segments
{
    self = [super init];
    
    if(self) {
        self.bShowLine = bShowLine;
        self.bShowInfo = bShowInfo;
        self.datas = segments;
        self.titleLabel.text = title;
        self.leftOffset = leftOffset;
        
        self.infoLogo.hidden = !bShowInfo;
        
        [self addSubviews];
        [self defineLayout];
        [self setupObservers];
        
        [self applyTheme];
        
        self.line.hidden = !self.bShowLine;
    }
    
    return self;
}

- (void)dealloc
{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        self.titleLabel.textColor = [UIColor colorWithHexString:kDarkThemeColorText];
        self.line.backgroundColor = [UIColor colorWithHexString:kDarkThemeColorLine];
    } else {
        self.backgroundColor = UIColor.whiteColor;
        self.titleLabel.textColor = [UIColor colorWithHexString:@"#333333"];
        self.line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
}

//修改暗黑模式
- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}

- (void)updateWithSelectIndex:(int)selectIndex
{
    self.segment.selectedSegmentIndex = selectIndex;
}

+ (float)height
{
    if([BrowserUtils isiPad]) {
        return 88;
    } else {
        return 60;
    }
}

- (void)selectedIndex:(id)sender
{
    if(self.selectIndexBlock) {
        self.selectIndexBlock((int)self.segment.selectedSegmentIndex);
    }
}

- (void)setupObservers
{
    [self.segment addTarget:self action:@selector(selectedIndex:) forControlEvents:UIControlEventValueChanged];

    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(darkThemeDidChangeNotification:)
                                                 name:kDarkThemeDidChangeNotification
                                               object:nil];
    
    UITapGestureRecognizer* tap = [UITapGestureRecognizer new];
    [self.tapView addGestureRecognizer:tap];
    @weakify(self)
    [tap.rac_gestureSignal subscribeNext:^(id x) {
        @strongify(self)
        if(self.selectInfoBlock) {
            self.selectInfoBlock();
        }
    }];
}

- (void)addSubviews
{
    [self addSubview:self.tapView];
    [self addSubview:self.titleLabel];
    [self addSubview:self.infoLogo];
    [self addSubview:self.segment];
}

- (void)defineLayout
{
    [self.tapView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.titleLabel);
        make.right.equalTo(self.infoLogo);
        make.top.bottom.equalTo(self);
    }];
    
    float offset = iPadValue(30, 15);
    if(self.leftOffset > 0) {
        offset = self.leftOffset;
    }
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(offset);
        make.centerY.equalTo(self);
    }];
    
    [self.infoLogo mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.titleLabel.mas_right).offset(5);
        make.centerY.equalTo(self);
    }];
    
//    float size = iPadValue(15, 10);
    [self.segment mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_offset(-offset);
        make.centerY.equalTo(self);
    }];
    if([BrowserUtils isiPad]) {
        //iPad
        self.segment.transform = CGAffineTransformMakeScale(1.2, 1.2);
    }
    
    UIView* line = [UIView new];
    line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    [self addSubview:line];
    [line mas_makeConstraints:^(MASConstraintMaker *make) {
       make.left.mas_offset(0);
       make.right.mas_offset(-0);
       make.height.mas_equalTo(0.5);
       make.bottom.mas_offset(0);
    }];
    self.line = line;
}


- (UILabel *)titleLabel
{
    if(!_titleLabel) {
        float font = iPadValue(20, 16);
        _titleLabel = [UIView createLabelWithTitle:nil
                                        textColor:[UIColor colorWithHexString:@"#333333"]
                                          bgColor:UIColor.clearColor
                                         fontSize:font
                                    textAlignment:NSTextAlignmentLeft
                                            bBold:NO];
    }
    
    return _titleLabel;
}

- (UISegmentedControl *)segment
{
    if(!_segment) {
        _segment = [[UISegmentedControl alloc]initWithItems:self.datas];
    }
    
    return _segment;
}

- (UIImageView *)infoLogo
{
    if(!_infoLogo) {
        _infoLogo = [UIImageView new];
        _infoLogo.image = [UIImage imageNamed:@"script_info_icon"];
        _infoLogo.hidden = YES;
    }
    
    return _infoLogo;
}

- (UIView *)tapView
{
    if(!_tapView) {
        _tapView = [UIView new];
        _tapView.backgroundColor = UIColor.clearColor;
    }
    
    return _tapView;
}

@end
