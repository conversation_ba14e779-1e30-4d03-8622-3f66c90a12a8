//
//  SettingStepperView.h
//  PPBrowser
//
//  Created by qing<PERSON> on 2023/8/5.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "ThemeProtocol.h"

NS_ASSUME_NONNULL_BEGIN

@interface SettingStepperView : UIView

- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)new NS_UNAVAILABLE;

- (instancetype)initWithShowLine:(BOOL)bShowLine;

- (void)updateWithTitle:(NSString *)title
                  value:(float)value;

- (void)updateWithMax:(float)maxValue minValue:(float)minValue;

+ (float)height;

@property (nonatomic, copy) void (^didValueChangeAction)(float value);

@end

NS_ASSUME_NONNULL_END
