//
//  SettingSegmentAndTextView.m
//  PPBrowser
//
//  Created by qingbin on 2025/2/22.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "SettingSegmentAndTextView.h"

#import "Masonry.h"
#import "MaizyHeader.h"
#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "ReactiveCocoa.h"
#import "UIView+FrameHelper.h"
#import "UIViewController+Helper.h"
#import "NSObject+Helper.h"
#import "BrowserUtils.h"

#import "PPNotifications.h"
#import "ThemeProtocol.h"
#import "YYText.h"

@interface SettingSegmentAndTextView ()

@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UISegmentedControl *segment;
@property (nonatomic, strong) UILabel *detailLabel;
@property (nonatomic, strong) UIView *line;

@property (nonatomic, assign) BOOL bShowLine;
@property (nonatomic, strong) NSArray *datas;

@end

@implementation SettingSegmentAndTextView

- (instancetype)initWithTitle:(NSString *)title
                    showLine:(BOOL)bShowLine
                   segments:(NSArray *)segments {
    self = [super init];
    if (self) {
        self.bShowLine = bShowLine;
        self.datas = segments;
        self.titleLabel.text = title;
        
        [self addSubviews];
        [self defineLayout];
        [self setupObservers];
        [self applyTheme];
        
        self.line.hidden = !bShowLine;
    }
    return self;
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

#pragma mark - Theme Support

- (void)applyTheme {
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if (isDarkTheme) {
        self.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        self.titleLabel.textColor = [UIColor colorWithHexString:kDarkThemeColorText];
        self.detailLabel.textColor = [UIColor colorWithHexString:@"#999999"];
        self.line.backgroundColor = [UIColor colorWithHexString:kDarkThemeColorLine];
    } else {
        self.backgroundColor = UIColor.whiteColor;
        self.titleLabel.textColor = [UIColor colorWithHexString:@"#333333"];
        self.detailLabel.textColor = [UIColor colorWithHexString:@"#666666"];
        self.line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification {
    [self applyTheme];
}

#pragma mark - Public Methods

- (void)updateWithTitle:(NSString *)title
                detail:(NSString *)detail
          selectIndex:(int)selectIndex {
    self.titleLabel.text = title;
//    self.detailLabel.text = detail;
    self.segment.selectedSegmentIndex = selectIndex;
    
    //v2.7.4,修改间距
    NSMutableAttributedString* attributedString = [[NSMutableAttributedString alloc]initWithString:detail];
    attributedString.yy_lineSpacing = 5;
    attributedString.yy_paragraphSpacing = 5;
    
    self.detailLabel.attributedText = attributedString;
}

+ (float)height {
    return iPadValue(120, 90);
}

- (void)updateWithDetail:(NSString *)detail
{
//    self.detailLabel.text = detail;
    
    //v2.7.4,修改间距
    NSMutableAttributedString* attributedString = [[NSMutableAttributedString alloc]initWithString:detail];
    attributedString.yy_lineSpacing = 5;
    attributedString.yy_paragraphSpacing = 5;
    
    self.detailLabel.attributedText = attributedString;
}

#pragma mark - Private Methods

- (void)selectedIndex:(id)sender {
    if (self.selectIndexBlock) {
        self.selectIndexBlock((int)self.segment.selectedSegmentIndex);
    }
}

- (void)setupObservers {
    [self.segment addTarget:self action:@selector(selectedIndex:) forControlEvents:UIControlEventValueChanged];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                           selector:@selector(darkThemeDidChangeNotification:)
                                               name:kDarkThemeDidChangeNotification
                                             object:nil];
}

#pragma mark - Layout

- (void)addSubviews {
    [self addSubview:self.titleLabel];
    [self addSubview:self.segment];
    [self addSubview:self.detailLabel];
    [self addSubview:self.line];
}

- (void)defineLayout {
    float margin = iPadValue(30, 15);
    float topOffset = iPadValue(20, 15);
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(margin);
        make.centerY.equalTo(self.segment);
    }];
    
    [self.segment mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_offset(-margin);
        make.top.mas_offset(topOffset);
    }];
    
    if ([BrowserUtils isiPad]) {
        self.segment.transform = CGAffineTransformMakeScale(1.2, 1.2);
    }
    
    [self.detailLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.segment.mas_bottom).offset(topOffset);
        make.left.equalTo(self).offset(margin);
        make.right.equalTo(self).offset(-margin);
        make.bottom.equalTo(self).offset(-topOffset);
    }];
    
    [self.line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.bottom.equalTo(self);
        make.height.mas_equalTo(0.5);
    }];
}

#pragma mark - Lazy Load

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [UIView createLabelWithTitle:nil
                                       textColor:[UIColor colorWithHexString:@"#333333"]
                                         bgColor:UIColor.clearColor
                                        fontSize:iPadValue(18, 14)
                                   textAlignment:NSTextAlignmentCenter
                                           bBold:NO];
    }
    return _titleLabel;
}

- (UISegmentedControl *)segment {
    if (!_segment) {
        _segment = [[UISegmentedControl alloc] initWithItems:self.datas];
    }
    return _segment;
}

- (UILabel *)detailLabel {
    if (!_detailLabel) {
        _detailLabel = [UIView createLabelWithTitle:nil
                                        textColor:[UIColor colorWithHexString:@"#666666"]
                                          bgColor:UIColor.clearColor
                                         fontSize:iPadValue(18, 14)
                                    textAlignment:NSTextAlignmentLeft
                                            bBold:NO];
        _detailLabel.numberOfLines = 0;
    }
    return _detailLabel;
}

- (UIView *)line {
    if (!_line) {
        _line = [[UIView alloc] init];
        _line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
    return _line;
}

@end
