//
//  SettingSwitchAndTextView.m
//  PPBrowser
//
//  Created by qing<PERSON> on 2023/11/28.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import "SettingSwitchAndTextView.h"

#import "Masonry.h"
#import "MaizyHeader.h"
#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "ReactiveCocoa.h"
#import "UIView+FrameHelper.h"
#import "UIViewController+Helper.h"
#import "NSObject+Helper.h"
#import "BrowserUtils.h"

#import "PPNotifications.h"
#import "ThemeProtocol.h"
#import "YYText.h"

@interface SettingSwitchAndTextView ()

@property (nonatomic, strong) UILabel* titleLabel;

@property (nonatomic, strong) UILabel* detailLabel;

@property (nonatomic, strong) UIView* line;

@property (nonatomic, assign) BOOL bShowLine;

@end

@implementation SettingSwitchAndTextView

- (instancetype)initWithShowLine:(BOOL)bShowLine
{
    self = [super init];
    
    if(self) {
        self.bShowLine = bShowLine;
        
        [self addSubviews];
        [self defineLayout];
        [self setupObservers];
        
        [self applyTheme];
    }
    
    return self;
}

- (void)dealloc
{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        self.titleLabel.textColor = [UIColor colorWithHexString:kDarkThemeColorText];
        self.line.backgroundColor = [UIColor colorWithHexString:kDarkThemeColorLine];
    } else {
        self.backgroundColor = UIColor.whiteColor;
        self.titleLabel.textColor = [UIColor colorWithHexString:@"#333333"];
        self.line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
}

//修改暗黑模式
- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}

- (void)updateWithTitle:(NSString *)title
                 detail:(NSString *)detail
                   isOn:(BOOL)isOn
{
    self.titleLabel.text = title;
    self.switchView.on = isOn;
//    self.detailLabel.text = detail;
    
    self.line.hidden = !self.bShowLine;
    
    //v2.7.4,修改间距
    NSMutableAttributedString* attributedString = [[NSMutableAttributedString alloc]initWithString:detail];
    attributedString.yy_lineSpacing = 5;
    attributedString.yy_paragraphSpacing = 5;
    
    self.detailLabel.attributedText = attributedString;
}

- (void)updateWithIsOn:(BOOL)isOn
{
    self.switchView.on = isOn;
}

- (void)setupObservers
{
    @weakify(self)
    [[self.switchView rac_newOnChannel] subscribeNext:^(id x) {
        @strongify(self)
        if(self.didSwithAction) {
            self.didSwithAction(self.switchView.isOn);
        }
    }];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(darkThemeDidChangeNotification:)
                                                 name:kDarkThemeDidChangeNotification
                                               object:nil];
}

- (void)addSubviews
{
    [self addSubview:self.titleLabel];
    [self addSubview:self.switchView];
    [self addSubview:self.detailLabel];
}

- (void)defineLayout
{
    float topOffset = iPadValue(30, 15);
    float offset = iPadValue(30, 15);
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(offset);
        make.centerY.equalTo(self.switchView);
    }];
    
    [self.switchView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_offset(-offset);
        make.top.mas_offset(topOffset);
    }];
    
    [self.detailLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.titleLabel);
        make.right.equalTo(self.switchView);
        //决定高度
        make.top.equalTo(self.switchView.mas_bottom).offset(iPadValue(20, 10));
        make.bottom.mas_offset(-topOffset);
    }];
    
    UIView* line = [UIView new];
    line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    [self addSubview:line];
    [line mas_makeConstraints:^(MASConstraintMaker *make) {
       make.left.mas_offset(0);
       make.right.mas_offset(-0);
       make.height.mas_equalTo(0.5);
       make.bottom.mas_offset(0);
    }];
    self.line = line;
}

- (UISwitch *)switchView
{
    if(!_switchView) {
        _switchView = [UISwitch new];
        _switchView.on = YES;
    }
    
    return _switchView;
}

- (UILabel *)titleLabel
{
    if(!_titleLabel) {
        float font = iPadValue(20, 16);
        _titleLabel = [UIView createLabelWithTitle:nil
                                        textColor:[UIColor colorWithHexString:@"#333333"]
                                          bgColor:UIColor.clearColor
                                         fontSize:font
                                    textAlignment:NSTextAlignmentLeft
                                            bBold:NO];
    }
    
    return _titleLabel;
}

- (UILabel *)detailLabel
{
    if(!_detailLabel) {
        float font = iPadValue(18, 14);
        _detailLabel = [UIView createLabelWithTitle:nil
                                        textColor:[UIColor colorWithHexString:@"#666666"]
                                          bgColor:UIColor.clearColor
                                         fontSize:font
                                    textAlignment:NSTextAlignmentLeft
                                            bBold:NO];
        
        _detailLabel.numberOfLines = 0;
    }
    
    return _detailLabel;
}


@end
