//
//  SettingArrowView.h
//  PPBrowser
//
//  Created by qing<PERSON> on 2022/6/6.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "ThemeProtocol.h"

NS_ASSUME_NONNULL_BEGIN

@interface SettingArrowView : UIView<ThemeProtocol>

- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)new NS_UNAVAILABLE;

- (instancetype)initWithShowLine:(BOOL)bShowLine;

+ (float)height;

- (void)updateWithTitle:(NSString *)title;

@property (nonatomic, copy) void (^didAction)(void);

@end

NS_ASSUME_NONNULL_END
