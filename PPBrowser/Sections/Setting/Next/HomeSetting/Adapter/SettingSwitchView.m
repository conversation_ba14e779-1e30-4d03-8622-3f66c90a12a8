//
//  SettingSwitchView.m
//  PPBrowser
//
//  Created by qingbin on 2022/6/6.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "SettingSwitchView.h"

#import "Masonry.h"
#import "MaizyHeader.h"
#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "ReactiveCocoa.h"
#import "UIView+FrameHelper.h"
#import "UIViewController+Helper.h"
#import "NSObject+Helper.h"
#import "BrowserUtils.h"

#import "PPNotifications.h"

@interface SettingSwitchView ()

@property (nonatomic, strong) UILabel* titleLabel;

@property (nonatomic, strong) UIView* line;

@property (nonatomic, assign) BOOL bShowLine;

@property (nonatomic, assign) float leftOffset;

@end

@implementation SettingSwitchView

- (instancetype)initWithShowLine:(BOOL)bShowLine
{
    self = [super init];
    
    if(self) {
        self.bShowLine = bShowLine;
        
        [self addSubviews];
        [self defineLayout];
        [self setupObservers];
        
        [self applyTheme];
    }
    
    return self;
}

- (instancetype)initWithShowLine:(BOOL)bShowLine leftOffset:(float)leftOffset
{
    self = [super init];
    if(self) {
        self.bShowLine = bShowLine;
        self.leftOffset = leftOffset;
        
        [self addSubviews];
        [self defineLayout];
        [self setupObservers];
        
        [self applyTheme];
    }
    
    return self;
}

- (void)dealloc
{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        self.titleLabel.textColor = [UIColor colorWithHexString:kDarkThemeColorText];
        self.line.backgroundColor = [UIColor colorWithHexString:kDarkThemeColorLine];
    } else {
        self.backgroundColor = UIColor.whiteColor;
        self.titleLabel.textColor = [UIColor colorWithHexString:@"#333333"];
        self.line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
}

//修改暗黑模式
- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}

- (void)updateWithTitle:(NSString *)title
                   isOn:(BOOL)isOn
{
    self.titleLabel.text = title;
    self.switchView.on = isOn;
    
    self.line.hidden = !self.bShowLine;
}

- (void)updateWithIsOn:(BOOL)isOn
{
    self.switchView.on = isOn;
}

+ (float)height
{
    if([BrowserUtils isiPad]) {
        return 88;
    } else {
        return 60;
    }
}

- (void)setupObservers
{
    @weakify(self)
    [[self.switchView rac_newOnChannel] subscribeNext:^(id x) {
        @strongify(self)
        if(self.didSwithAction) {
            self.didSwithAction(self.switchView.isOn);
        }
    }];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(darkThemeDidChangeNotification:)
                                                 name:kDarkThemeDidChangeNotification
                                               object:nil];
}

- (void)addSubviews
{
    [self addSubview:self.titleLabel];
    [self addSubview:self.switchView];
}

- (void)defineLayout
{
    float offset = iPadValue(30, 15);
    if(self.leftOffset > 0) {
        offset = self.leftOffset;
    }
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(offset);
        make.centerY.equalTo(self);
        make.right.lessThanOrEqualTo(self.switchView.mas_left).offset(-2);
    }];
    
    [self.switchView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_offset(-iPadValue(30, 15));
        make.centerY.equalTo(self);
    }];
    
    [self.titleLabel setContentCompressionResistancePriority:UILayoutPriorityDefaultHigh forAxis:UILayoutConstraintAxisHorizontal];
    [self.titleLabel setContentHuggingPriority:UILayoutPriorityDefaultHigh forAxis:UILayoutConstraintAxisHorizontal];
    
    [self.switchView setContentCompressionResistancePriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    [self.switchView setContentHuggingPriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    
    if([BrowserUtils isiPad]) {
        //iPad
        self.switchView.transform = CGAffineTransformMakeScale(1.2, 1.2);
    }
    
    UIView* line = [UIView new];
    line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    [self addSubview:line];
    [line mas_makeConstraints:^(MASConstraintMaker *make) {
       make.left.mas_offset(0);
       make.right.mas_offset(-0);
       make.height.mas_equalTo(0.5);
       make.bottom.mas_offset(0);
    }];
    self.line = line;
}

- (UISwitch *)switchView
{
    if(!_switchView) {
        _switchView = [UISwitch new];
        _switchView.on = YES;
    }
    
    return _switchView;
}

- (UILabel *)titleLabel
{
    if(!_titleLabel) {
        float font = iPadValue(20, 16);
        _titleLabel = [UIView createLabelWithTitle:nil
                                        textColor:[UIColor colorWithHexString:@"#333333"]
                                          bgColor:UIColor.clearColor
                                         fontSize:font
                                    textAlignment:NSTextAlignmentLeft
                                            bBold:NO];
    }
    
    return _titleLabel;
}

@end


