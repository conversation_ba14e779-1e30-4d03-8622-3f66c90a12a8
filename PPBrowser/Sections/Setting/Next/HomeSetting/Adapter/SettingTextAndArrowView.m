//
//  SettingTextAndArrowView.m
//  PPBrowser
//
//  Created by qingbin on 2022/6/6.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "SettingTextAndArrowView.h"

#import "Masonry.h"
#import "MaizyHeader.h"
#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "ReactiveCocoa.h"
#import "UIView+FrameHelper.h"
#import "UIViewController+Helper.h"
#import "NSObject+Helper.h"
#import "BrowserUtils.h"

#import "PPNotifications.h"
#import "YYText.h"

@interface SettingTextAndArrowView ()

@property (nonatomic, strong) UILabel* titleLabel;

@property (nonatomic, strong) UILabel* contentLabel;

@property (nonatomic, strong) UIImageView* arrow;

@property (nonatomic, strong) UIView* line;

@property (nonatomic, assign) BOOL bShowLine;

@end

@implementation SettingTextAndArrowView

- (instancetype)initWithShowLine:(BOOL)bShowLine
{
    self = [super init];
    
    if(self) {
        self.bShowLine = bShowLine;
        
        [self addSubviews];
        [self defineLayout];
        [self setupObservers];
        
        [self applyTheme];
    }
    
    return self;
}

- (void)dealloc
{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        self.titleLabel.textColor = [UIColor colorWithHexString:kDarkThemeColorText];
        self.line.backgroundColor = [UIColor colorWithHexString:kDarkThemeColorLine];
        self.arrow.tintColor = UIColor.whiteColor;
    } else {
        self.backgroundColor = UIColor.whiteColor;
        self.titleLabel.textColor = [UIColor colorWithHexString:@"#333333"];
        self.line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.arrow.tintColor = [UIColor colorWithHexString:@"#333333"];
    }
}

//修改暗黑模式
- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}

- (void)updateWithTitle:(NSString *)title
                content:(NSString *)content
{
    self.titleLabel.text = title;
    self.contentLabel.text = content;
    
    self.line.hidden = !self.bShowLine;
}

- (void)updateWithTitle:(NSString *)title
{
    self.titleLabel.text = title;
}

- (void)updateWithContent:(NSString *)content
{
    self.contentLabel.text = content;
}

+ (float)height
{
    if([BrowserUtils isiPad]) {
        return 88;
    } else {
        return 60;
    }
}

- (void)setupObservers
{
    UITapGestureRecognizer* tap = [UITapGestureRecognizer new];
    [self addGestureRecognizer:tap];
    @weakify(self)
    [tap.rac_gestureSignal subscribeNext:^(id x) {
        @strongify(self)
        if(self.didAction) {
            self.didAction();
        }
    }];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(darkThemeDidChangeNotification:)
                                                 name:kDarkThemeDidChangeNotification
                                               object:nil];
}

- (void)addSubviews
{
    [self addSubview:self.titleLabel];
    [self addSubview:self.arrow];
    [self addSubview:self.contentLabel];
}

- (void)defineLayout
{
    float offset = iPadValue(30, 15);
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(offset);
        make.centerY.equalTo(self);
    }];
    
    float size = iPadValue(15, 10);
    [self.arrow mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_offset(-offset);
        make.centerY.equalTo(self);
        make.size.mas_equalTo(size);
    }];
    
    [self.contentLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self);
        make.right.equalTo(self.arrow.mas_left).offset(-5);
    }];
    
    UIView* line = [UIView new];
    line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    [self addSubview:line];
    [line mas_makeConstraints:^(MASConstraintMaker *make) {
       make.left.mas_offset(0);
       make.right.mas_offset(-0);
       make.height.mas_equalTo(0.5);
       make.bottom.mas_offset(0);
    }];
    self.line = line;
}

- (UIImageView *)arrow
{
    if(!_arrow) {
        _arrow = [UIImageView new];
        
        UIImage* image = [UIImage imageNamed:@"standard_right_arrow"];
        image = [image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        
        _arrow.image = image;
    }
    
    return _arrow;
}

- (UILabel *)titleLabel
{
    if(!_titleLabel) {
        float font = iPadValue(20, 16);
        _titleLabel = [UIView createLabelWithTitle:nil
                                        textColor:[UIColor colorWithHexString:@"#333333"]
                                          bgColor:UIColor.clearColor
                                         fontSize:font
                                    textAlignment:NSTextAlignmentLeft
                                            bBold:NO];
    }
    
    return _titleLabel;
}

- (UILabel *)contentLabel
{
    if(!_contentLabel) {
        float font = iPadValue(20, 16);
        _contentLabel = [UIView createLabelWithTitle:nil
                                        textColor:[UIColor colorWithHexString:@"#999999"]
                                          bgColor:UIColor.clearColor
                                         fontSize:font
                                    textAlignment:NSTextAlignmentRight
                                            bBold:NO];
    }
    
    return _contentLabel;
}

@end
