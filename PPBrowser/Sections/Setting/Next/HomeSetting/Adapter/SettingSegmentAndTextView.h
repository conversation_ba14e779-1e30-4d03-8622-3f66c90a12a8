//
//  SettingSegmentAndTextView.h
//  PPBrowser
//
//  Created by qing<PERSON> on 2025/2/22.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface SettingSegmentAndTextView : UIView

@property (nonatomic, copy) void(^selectIndexBlock)(int index);

- (instancetype)initWithTitle:(NSString *)title
                    showLine:(BOOL)bShowLine
                   segments:(NSArray *)segments;

- (void)updateWithTitle:(NSString *)title
                detail:(NSString *)detail
          selectIndex:(int)selectIndex;

- (void)updateWithDetail:(NSString *)detail;

@end

NS_ASSUME_NONNULL_END
