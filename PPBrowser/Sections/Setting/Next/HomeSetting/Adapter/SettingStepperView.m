//
//  SettingStepperView.m
//  PPBrowser
//
//  Created by qingbin on 2023/8/5.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import "SettingStepperView.h"

#import "Masonry.h"
#import "MaizyHeader.h"
#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "ReactiveCocoa.h"
#import "UIView+FrameHelper.h"
#import "UIViewController+Helper.h"
#import "NSObject+Helper.h"
#import "BrowserUtils.h"

#import "PPNotifications.h"

@interface SettingStepperView ()

@property (nonatomic, strong) UILabel* titleLabel;

@property (nonatomic, strong) UILabel* contentLabel;

@property (nonatomic, strong) UIStepper* stepper;

@property (nonatomic, strong) UIView* line;

@property (nonatomic, assign) BOOL bShowLine;

@end

@implementation SettingStepperView

- (instancetype)initWithShowLine:(BOOL)bShowLine
{
    self = [super init];
    
    if(self) {
        self.bShowLine = bShowLine;
        
        [self addSubviews];
        [self defineLayout];
        [self setupObservers];
        
        [self applyTheme];
    }
    
    return self;
}

- (void)dealloc
{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        self.titleLabel.textColor = [UIColor colorWithHexString:kDarkThemeColorText];
        self.contentLabel.textColor = [UIColor colorWithHexString:kDarkThemeColorText];
        self.line.backgroundColor = [UIColor colorWithHexString:kDarkThemeColorLine];
    } else {
        self.backgroundColor = UIColor.whiteColor;
        self.titleLabel.textColor = [UIColor colorWithHexString:@"#333333"];
        self.contentLabel.textColor = [UIColor colorWithHexString:@"#333333"];
        self.line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
}

//修改暗黑模式
- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}

- (void)updateWithTitle:(NSString *)title
                  value:(float)value
{
    self.titleLabel.text = title;
    self.contentLabel.text = [NSString stringWithFormat:@"%.1f", value];
    self.stepper.value = value;
    
    self.line.hidden = !self.bShowLine;
}

- (void)updateWithMax:(float)maxValue minValue:(float)minValue
{
    self.stepper.minimumValue = minValue;
    self.stepper.maximumValue = maxValue;
}

+ (float)height
{
    if([BrowserUtils isiPad]) {
        return 88;
    } else {
        return 60;
    }
}

- (void)setupObservers
{
    @weakify(self)
    [[self.stepper rac_signalForControlEvents:UIControlEventValueChanged]
    subscribeNext:^(id x) {
        @strongify(self)
        
        double value = self.stepper.value;
        self.contentLabel.text = [NSString stringWithFormat:@"%.1f", value];
        
        if(self.didValueChangeAction) {
            self.didValueChangeAction(value);
        }
    }];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(darkThemeDidChangeNotification:)
                                                 name:kDarkThemeDidChangeNotification
                                               object:nil];
}

- (void)addSubviews
{
    [self addSubview:self.titleLabel];
    [self addSubview:self.contentLabel];
    [self addSubview:self.stepper];
}

- (void)defineLayout
{
    float offset = iPadValue(30, 15);
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(offset);
        make.centerY.equalTo(self);
    }];
    
    [self.stepper mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self);
        make.right.mas_offset(-offset);
    }];
    
    [self.contentLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self);
        make.right.equalTo(self.stepper.mas_left).offset(-10);
    }];
    
    UIView* line = [UIView new];
    line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    [self addSubview:line];
    [line mas_makeConstraints:^(MASConstraintMaker *make) {
       make.left.mas_offset(0);
       make.right.mas_offset(-0);
       make.height.mas_equalTo(0.5);
       make.bottom.mas_offset(0);
    }];
    self.line = line;
}

- (UILabel *)titleLabel
{
    if(!_titleLabel) {
        float font = iPadValue(20, 16);
        _titleLabel = [UIView createLabelWithTitle:nil
                                        textColor:[UIColor colorWithHexString:@"#333333"]
                                          bgColor:UIColor.clearColor
                                         fontSize:font
                                    textAlignment:NSTextAlignmentLeft
                                            bBold:NO];
    }
    
    return _titleLabel;
}

- (UILabel *)contentLabel
{
    if(!_contentLabel) {
        float font = iPadValue(20, 16);
        _contentLabel = [UIView createLabelWithTitle:nil
                                        textColor:[UIColor colorWithHexString:@"#333333"]
                                          bgColor:UIColor.clearColor
                                         fontSize:font
                                    textAlignment:NSTextAlignmentRight
                                            bBold:NO];
    }
    
    return _contentLabel;
}

- (UIStepper *)stepper
{
    if(!_stepper) {
        _stepper = [[UIStepper alloc]init];
    }
    
    return _stepper;
}

@end
