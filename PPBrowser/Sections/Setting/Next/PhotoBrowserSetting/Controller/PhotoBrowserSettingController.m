//
//  PhotoBrowserSettingController.m
//  PPBrowser
//
//  Created by qingbin on 2024/8/23.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import "PhotoBrowserSettingController.h"

#import "MaizyHeader.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"

#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "NSObject+Helper.h"
#import "UIView+FrameHelper.h"

#import "ThemeProtocol.h"
#import "BrowserUtils.h"

@interface PhotoBrowserSettingController ()

@end

@implementation PhotoBrowserSettingController

- (void)viewDidLoad 
{
    [super viewDidLoad];

    self.navigationItem.title = NSLocalizedString(@"photoBrowser.title", nil);
    
    [self createCustomLeftBarButtonItem];
}


@end
