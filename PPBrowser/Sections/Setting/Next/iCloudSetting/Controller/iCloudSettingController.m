//
//  iCloudSettingController.m
//  Saber
//
//  Created by qing<PERSON> on 2023/12/28.
//

#import "iCloudSettingController.h"

#import "Masonry.h"
#import "MaizyHeader.h"
#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "ReactiveCocoa.h"
#import "UIView+FrameHelper.h"
#import "UIViewController+Helper.h"
#import "NSObject+Helper.h"

#import "SettingSwitchView.h"
#import "SettingArrowView.h"
#import "SettingTextView.h"
#import "SettingSwitchAndTextView.h"
#import "SettingTextAndArrowView.h"

#import "PPNotifications.h"
#import "VIPController.h"
#import "BaseNavigationController.h"
#import "BrowserUtils.h"

#import "BrowserHelper.h"
#import "AppDelegate.h"

#import "PaymentManager.h"
#import "SyncEngine.h"
#import "CloudKitHelper.h"
#import "UIAlertController+SafePresentation.h"

@interface iCloudSettingController ()

@property (nonatomic, strong) UIStackView* stackView;
@property (nonatomic, strong) UIView* tipsView;
//启用iCloud同步
@property (nonatomic, strong) SettingSwitchAndTextView *enabledSyncView;

//立即同步(先拉取数据，再推送数据到iCloud)
@property (nonatomic, strong) SettingTextAndArrowView *forceSyncView;

@property (nonatomic, strong) UIActivityIndicatorView* activityIndicator;

@end

@implementation iCloudSettingController

- (void)viewDidLoad 
{
    [super viewDidLoad];

    self.title = NSLocalizedString(@"iCloud.title", nil);
    
    [self addSubviews];
    [self defineLayout];
    [self updateWithModel];
    [self setupObservers];
    
    [self createCustomLeftBarButtonItem];
    [self _createCustomRightBarButtonItem];
}

- (void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:animated];
    
    [self applyTheme];
    [self checkCloudAvailability];
}

- (void)updateWithModel
{
    BOOL availabled = [CloudKitHelper checkCloudAvailability];
    if(availabled) {
        BOOL enablediCloud = [[PreferenceManager shareInstance].items.enablediCloud2 boolValue];
        
        [self.enabledSyncView updateWithTitle:NSLocalizedString(@"generalSetting.icloud.text", nil) 
                                       detail:NSLocalizedString(@"iCloud.sync.detail", nil)
                                         isOn:enablediCloud];
    } else {
        [self.enabledSyncView updateWithTitle:NSLocalizedString(@"generalSetting.icloud.text", nil) 
                                       detail:NSLocalizedString(@"iCloud.sync.detail", nil)
                                         isOn:NO];
        
        [PreferenceManager shareInstance].items.enablediCloud2 = @(NO);
        [[PreferenceManager shareInstance] encode];
    }
    
    [self updateSyncTimestamp];
}

- (void)updateSyncTimestamp
{
    NSString* syncTime = [CloudKitHelper iCloudSyncTimestamp];
    [self.forceSyncView updateWithTitle:NSLocalizedString(@"iCloud.force.title", nil) content:syncTime];
}

// 检查iCloud是否可用
- (void)checkCloudAvailability
{
    BOOL availabled = [CloudKitHelper checkCloudAvailability];
    self.enabledSyncView.userInteractionEnabled = availabled;
    
    self.tipsView.hidden = availabled;
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.tipsView.backgroundColor = [UIColor.blackColor colorWithAlphaComponent:0.5];
    } else {
        self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.tipsView.backgroundColor = [UIColor.whiteColor colorWithAlphaComponent:0.5];
    }
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    //只有当前的controller会触发一次, 其它已存在的controller走通知
    if(needReload) {
        //修改暗黑模式
        [self applyTheme];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}

- (void)setupObservers
{
    @weakify(self)
    [self.enabledSyncView setDidSwithAction:^(BOOL isOn) {
        @strongify(self)
        if(isOn && ![self checkIsVip]) {
            [self.enabledSyncView.switchView setOn:NO];
            return;
        }
        
        [PreferenceManager shareInstance].items.enablediCloud2 = @(isOn);
        [[PreferenceManager shareInstance] encode];
        
        ///清空数据token
        [[SyncEngine shareInstance] updateWithPrivateChangeToken:nil];
        if(isOn) {
            //先拉取iCloud数据到本地，再推送数据到iCloud
            self.activityIndicator.hidden = NO;
            [self.activityIndicator startAnimating];
            @weakify(self)
            [[SyncEngine shareInstance] syncNowWithCompletion:^{
                @strongify(self)
                dispatch_async(dispatch_get_main_queue(), ^{
                    if(self.activityIndicator.isAnimating) {
                        [self.activityIndicator stopAnimating];
                    }
                    
                    [self updateSyncTimestamp];
                    
                    [UIView showToast:NSLocalizedString(@"generalSetting.icloud.sync.success", nil)];
                });
            }];
        }
    }];
    
    __block BOOL isSyncing = NO;
    [self.forceSyncView setDidAction:^{
        @strongify(self)
        if(![self checkIsVip]) {
            return;
        }
        
        BOOL enablediCloud = [[PreferenceManager shareInstance].items.enablediCloud2 boolValue];
        if(!enablediCloud) {
            [UIView showToast:NSLocalizedString(@"generalSetting.icloud.disable.text", nil)];
            return;
        }
        
        if(isSyncing) {
            [UIView showToast:NSLocalizedString(@"tips.handling", nil)];
            return;
        }
        isSyncing = YES;
        
        //先拉取iCloud数据到本地，再推送数据到iCloud
        self.activityIndicator.hidden = NO;
        [self.activityIndicator startAnimating];
        @weakify(self)
        [[SyncEngine shareInstance] syncNowWithCompletion:^{
            @strongify(self)
            isSyncing = NO;
            
            dispatch_async(dispatch_get_main_queue(), ^{
                if(self.activityIndicator.isAnimating) {
                    [self.activityIndicator stopAnimating];
                }
        
                [self updateSyncTimestamp];
                
                [UIView showToast:NSLocalizedString(@"generalSetting.icloud.sync.success", nil)];
            });
        }];
    }];
    
    UITapGestureRecognizer* tap = [UITapGestureRecognizer new];
    [self.tipsView addGestureRecognizer:tap];
    [tap.rac_gestureSignal subscribeNext:^(id x) {
        [UIView showToast:NSLocalizedString(@"generalSetting.icloud.disable.text", nil)];
    }];
    
    [RACObserve([SyncEngine shareInstance], isSyncing) subscribeNext:^(id x) {
        @strongify(self)
        @weakify(self)
        dispatch_async(dispatch_get_main_queue(), ^{
            @strongify(self)
            BOOL enablediCloud = [[PreferenceManager shareInstance].items.enablediCloud2 boolValue];
            if(!enablediCloud) {
                //没有启动iCloud
                self.activityIndicator.hidden = YES;
                return;
            }
            
            if([[SyncEngine shareInstance] isSyncing]) {
                self.activityIndicator.hidden = NO;
                [self.activityIndicator startAnimating];
            } else {
                if(self.activityIndicator.isAnimating) {
                    [self.activityIndicator stopAnimating];
                }
                
                self.activityIndicator.hidden = YES;
                
                [self updateSyncTimestamp];
            }
        });
    }];
}

- (BOOL)checkIsVip
{
    BOOL isVip = [[PaymentManager shareInstance] isVip];
    if(!isVip) {
        UIAlertController* alertController = [UIAlertController alertControllerWithTitle:NSLocalizedString(@"vip.alert.title", nil) message:NSLocalizedString(@"vip.alert.text", nil) preferredStyle:UIAlertControllerStyleAlert];
        UIAlertAction* action = [UIAlertAction actionWithTitle:NSLocalizedString(@"common.cancel", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            [alertController dismissViewControllerAnimated:YES completion:nil];
        }];
        [alertController addAction:action];

        action = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.toKnowMore", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            [self jumpToVip];
        }];
        [alertController addAction:action];

//        [self presentViewController:alertController animated:YES completion:nil];
        //v2.6.8, 统一present方法，防止崩溃
        [alertController presentSafelyFromViewController:self];
    }
    
    return isVip;
}

#pragma mark -- 打开Vip页面
- (void)jumpToVip
{
    VIPController* vc = [[VIPController alloc]init];
    BaseNavigationController* navc = [[BaseNavigationController alloc]initWithRootViewController:vc];
    navc.view.backgroundColor = UIColor.whiteColor;

//    if ([BrowserUtils isiPad]) {
//        // iPad
//        navc.modalPresentationStyle = UIModalPresentationFormSheet;
//        vc.preferredContentSize = CGSizeMake(kScreenWidth-90, kScreenHeight-90);
//        
//        [self presentViewController:navc animated:YES completion:nil];
//    } else {
//        // iPhone
//        [self presentViewController:navc animated:YES completion:nil];
//    }
    //v2.6.8,统一present
    [self presentCustomToViewController:navc];
}

- (void)notify
{
    //切换夜间模式
    [[NSNotificationCenter defaultCenter] postNotificationName:kDarkThemeDidChangeNotification object:nil];
}

#pragma mark -- 导航栏
- (void)_createCustomRightBarButtonItem
{
    UIBarButtonItem* barItem = [[UIBarButtonItem alloc] initWithCustomView:self.activityIndicator];

    UIBarButtonItem *spacer = [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemFixedSpace target:nil action:nil];
    spacer.width = -16.0f; // for example shift right bar button to the right

    self.navigationItem.rightBarButtonItems = @[spacer, barItem];
}

#pragma mark -- layout
- (void)addSubviews
{
    //https://stackoverflow.com/questions/33927914/how-can-i-set-the-cornerradius-of-a-uistackview
    //UIStackView添加圆角适配
    [self.view addSubview:self.stackView];
    
    [self.view addSubview:self.tipsView];
}

- (void)defineLayout
{
    float margin = iPadValue(30, 15);
    float topOffset = iPadValue(30, 15);
    
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(margin);
        make.right.mas_offset(-margin);
        make.top.equalTo(self.view).offset(topOffset);
    }];
        
    [self.tipsView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.enabledSyncView);
    }];
    
    [self.forceSyncView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([SettingTextAndArrowView height]);
    }];
}

#pragma mark -- lazy init

- (UIStackView *)stackView
{
    if(!_stackView) {
        _stackView = [[UIStackView alloc]initWithArrangedSubviews:@[
            self.enabledSyncView,
            self.forceSyncView,
        ]];
        
        float topOffset = iPadValue(30, 15);
        _stackView.spacing = topOffset;
        _stackView.axis = UILayoutConstraintAxisVertical;

        _stackView.backgroundColor = UIColor.clearColor;
    }
    
    return _stackView;
}

- (UIView *)tipsView
{
    if(!_tipsView) {
        _tipsView = [UIView new];
        _tipsView.backgroundColor = [UIColor.whiteColor colorWithAlphaComponent:0.5];
        _tipsView.userInteractionEnabled = YES;
        _tipsView.hidden = YES;
    }
    
    return _tipsView;
}

- (SettingSwitchAndTextView *)enabledSyncView
{
    if(!_enabledSyncView) {
        _enabledSyncView = [[SettingSwitchAndTextView alloc]initWithShowLine:NO];
        
        _enabledSyncView.layer.cornerRadius = 10;
        _enabledSyncView.layer.masksToBounds = YES;
    }
    
    return _enabledSyncView;
}

- (SettingTextAndArrowView *)forceSyncView
{
    if(!_forceSyncView) {
        _forceSyncView = [[SettingTextAndArrowView alloc]initWithShowLine:NO];
        
        _forceSyncView.layer.cornerRadius = 10;
        _forceSyncView.layer.masksToBounds = YES;
    }
    
    return _forceSyncView;
}

- (UIActivityIndicatorView *)activityIndicator
{
    if(!_activityIndicator) {
        _activityIndicator = [[UIActivityIndicatorView alloc]initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleMedium];
        _activityIndicator.hidden = YES;
    }
    
    return _activityIndicator;
}

@end
