//
//  TabSettingViewController.m
//  PPBrowser
//
//  Created by qingbin on 2025/3/9.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "TabSettingViewController.h"

#import "Masonry.h"
#import "MaizyHeader.h"
#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "ReactiveCocoa.h"
#import "UIView+FrameHelper.h"
#import "UIViewController+Helper.h"
#import "NSObject+Helper.h"

#import "SettingSwitchView.h"
#import "SettingArrowView.h"
#import "SettingTextView.h"
#import "SettingSegmentView.h"
#import "SettingSegmentAndTitleView.h"
#import "PaddingNewLabel.h"
#import "SettingSwitchAndTextView.h"

#import "BrowserUtils.h"
#import "PPNotifications.h"

@interface TabSettingViewController ()
//
@property (nonatomic, strong) UIStackView *stackView;
// 左右滑动关闭网页标签
@property (nonatomic, strong) SettingSegmentAndTitleView *swipeCloseTabTrayView;
// 分组视图
@property (nonatomic, strong) UIView *groupView;

@end

@implementation TabSettingViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.title = NSLocalizedString(@"setting.tab", nil);
    
    [self applyTheme];
    [self addSubviews];
    [self defineLayout];
    [self bindData];
    
    [self createCustomLeftBarButtonItem];
}

#pragma mark - Theme Support

- (void)applyTheme {
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if (isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.groupView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        // 暗黑模式下调整阴影
        self.groupView.layer.shadowOpacity = 0.2;
    } else {
        self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.groupView.backgroundColor = UIColor.whiteColor;
        self.groupView.layer.shadowOpacity = 0.1;
    }
}

#pragma mark - Layout

- (void)addSubviews {
    [self.view addSubview:self.groupView];
    [self.groupView addSubview:self.stackView];
}

- (void)defineLayout {
    float margin = iPadValue(30, 15);
    float topOffset = iPadValue(30, 20);
    
    [self.groupView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view.mas_safeAreaLayoutGuideTop).offset(topOffset);
        make.left.equalTo(self.view).offset(margin);
        make.right.equalTo(self.view).offset(-margin);
        make.bottom.equalTo(self.stackView);
    }];
    
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.right.equalTo(self.groupView);
    }];
    
    [self.swipeCloseTabTrayView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(self.stackView);
    }];
}

#pragma mark - Data Binding

- (void)bindData {    
    SwipeCloseTabTrayType swipeCloseTabTrayType = [[PreferenceManager shareInstance].items.swipeCloseTabTrayType intValue];
    [self.swipeCloseTabTrayView updateWithSelectIndex:(int)swipeCloseTabTrayType];
    
    // 绑定事件
    [self.swipeCloseTabTrayView setSelectIndexBlock:^(int index) {
        [PreferenceManager shareInstance].items.swipeCloseTabTrayType = @(index);
        [[PreferenceManager shareInstance] encode];
    }];
}

#pragma mark - Event Handlers


#pragma mark - Lazy Load

- (UIStackView *)stackView {
    if (!_stackView) {
        _stackView = [[UIStackView alloc] initWithArrangedSubviews:@[
            self.swipeCloseTabTrayView
        ]];
        _stackView.axis = UILayoutConstraintAxisVertical;
        _stackView.spacing = 0;
    }
    return _stackView;
}

- (UIView *)groupView {
    if (!_groupView) {
        _groupView = [[UIView alloc] init];
        
        // 设置圆角和阴影
        _groupView.layer.cornerRadius = 10;
        _groupView.layer.masksToBounds = YES;
        _groupView.layer.shadowColor = [UIColor blackColor].CGColor;
        _groupView.layer.shadowOffset = CGSizeMake(0, 2);
        _groupView.layer.shadowOpacity = 0.1;
        _groupView.layer.shadowRadius = 4;
    }
    return _groupView;
}

- (SettingSegmentAndTitleView *)swipeCloseTabTrayView
{
    if(!_swipeCloseTabTrayView) {
        _swipeCloseTabTrayView = [[SettingSegmentAndTitleView alloc]initWithTitle:NSLocalizedString(@"generalSetting.close.tab", nil) showLine:NO segments:@[
            NSLocalizedString(@"generalSetting.close.tab.pan", nil),
            NSLocalizedString(@"generalSetting.close.tab.horizontal", nil),
            NSLocalizedString(@"generalSetting.close.tab.noeffect", nil)
        ]];
    }
    
    return _swipeCloseTabTrayView;
}

@end
