//
//  AutoPageSettingViewController.m
//  PPBrowser
//
//  Created by qingbin on 2025/2/22.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "AutoPageSettingViewController.h"

#import "Masonry.h"
#import "MaizyHeader.h"
#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "ReactiveCocoa.h"
#import "UIView+FrameHelper.h"
#import "UIViewController+Helper.h"
#import "NSObject+Helper.h"

#import "SettingSwitchView.h"
#import "SettingArrowView.h"
#import "SettingTextView.h"
#import "SettingSegmentView.h"
#import "PaddingNewLabel.h"
#import "SettingSwitchAndTextView.h"

#import "AutoPageBlackListViewController.h"
#import "AutoPageMarkListViewController.h"
#import "BrowserUtils.h"
#import "PPNotifications.h"

@interface AutoPageSettingViewController ()

@property (nonatomic, strong) UIStackView *stackView;
// 自动翻页开关
@property (nonatomic, strong) SettingSegmentView *autoPageSegment;
// 显示分割线开关
@property (nonatomic, strong) SettingSwitchView *showSeparatorSwitch;
// 智能检测开关
@property (nonatomic, strong) SettingSwitchAndTextView *autoDetectSwitch;
// 黑名单入口
@property (nonatomic, strong) SettingArrowView *blacklistArrow;
// 手动标记入口
@property (nonatomic, strong) SettingArrowView *manualMarkArrow;

// 分组视图
@property (nonatomic, strong) UIView *groupView;

@end

@implementation AutoPageSettingViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.title = NSLocalizedString(@"autopage.setting.title", nil);
    [self applyTheme];
    [self addSubviews];
    [self defineLayout];
    [self bindData];
    
    [self createCustomLeftBarButtonItem];
}

#pragma mark - Theme Support

- (void)applyTheme {
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if (isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.groupView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        // 暗黑模式下调整阴影
        self.groupView.layer.shadowOpacity = 0.2;
    } else {
        self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.groupView.backgroundColor = UIColor.whiteColor;
        self.groupView.layer.shadowOpacity = 0.1;
    }
}

#pragma mark - Layout

- (void)addSubviews {
    [self.view addSubview:self.groupView];
    [self.groupView addSubview:self.stackView];
}

- (void)defineLayout {
    float margin = iPadValue(30, 15);
    float topOffset = iPadValue(30, 20);
    
    [self.groupView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view.mas_safeAreaLayoutGuideTop).offset(topOffset);
        make.left.equalTo(self.view).offset(margin);
        make.right.equalTo(self.view).offset(-margin);
        make.bottom.equalTo(self.stackView);
    }];
    
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.right.equalTo(self.groupView);
    }];
    
    [self.autoPageSegment mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([SettingSegmentView height]);
    }];
    
    [self.showSeparatorSwitch mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([SettingSwitchView height]);
    }];
    
    [self.blacklistArrow mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([SettingArrowView height]);
    }];
    
    [self.manualMarkArrow mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([SettingArrowView height]);
    }];
}

#pragma mark - Data Binding

- (void)bindData {
    // 自动翻页开关
    BOOL isAutoPage = [[PreferenceManager shareInstance].items.isAutoPage boolValue];
    [self.autoPageSegment updateWithSelectIndex:isAutoPage ? 0 : 1];
    
    // 分割线开关
    BOOL showSeparator = [[PreferenceManager shareInstance].items.isShowSeparatorLine boolValue];
    [self.showSeparatorSwitch updateWithTitle:NSLocalizedString(@"autopage.show.line", nil) isOn:showSeparator];
    
    // 绑定事件
    @weakify(self)
    [self.autoPageSegment setSelectIndexBlock:^(int index) {
        @strongify(self)
        [self handleAutoPageStatusChange:index];
    }];
    
    [self.showSeparatorSwitch setDidSwithAction:^(BOOL isOn) {
        @strongify(self)
        [self handleShowSeparatorChange:isOn];
    }];
    
    // 添加智能检测开关事件
    [self.autoDetectSwitch setDidSwithAction:^(BOOL isOn) {
        @strongify(self)
        [self handleAutoDetectChange:isOn];
    }];
    
    [self.blacklistArrow setDidAction:^{
        @strongify(self)
        [self handleBlacklistTap];
    }];
    
    [self.manualMarkArrow setDidAction:^{
        @strongify(self)
        [self handleManualMarkTap];
    }];
}

#pragma mark - Event Handlers

- (void)handleAutoPageStatusChange:(int)index {
    BOOL isOn = (index == 0);
    [PreferenceManager shareInstance].items.isAutoPage = @(isOn);
    [[PreferenceManager shareInstance] encode];
    
    //刷新页面，重新加载脚本
    [[NSNotificationCenter defaultCenter] postNotificationName:kReloadUserScriptNotification object:nil];
}

- (void)handleShowSeparatorChange:(BOOL)isOn {
    [PreferenceManager shareInstance].items.isShowSeparatorLine = @(isOn);
    [[PreferenceManager shareInstance] encode];
    
    //刷新页面，重新加载脚本
    [[NSNotificationCenter defaultCenter] postNotificationName:kReloadUserScriptNotification object:nil];
}

- (void)handleAutoDetectChange:(BOOL)isOn {
    [PreferenceManager shareInstance].items.isEnabledAutoPageAutoDetect = @(isOn);
    [[PreferenceManager shareInstance] encode];
    
    //刷新页面，重新加载脚本
    [[NSNotificationCenter defaultCenter] postNotificationName:kReloadUserScriptNotification object:nil];
}

- (void)handleBlacklistTap {
    AutoPageBlackListViewController *vc = [[AutoPageBlackListViewController alloc] init];
    [self.navigationController pushViewController:vc animated:YES];
}

- (void)handleManualMarkTap {
    // 手动标记功能实现
    AutoPageMarkListViewController *vc = [[AutoPageMarkListViewController alloc] init];
    [self.navigationController pushViewController:vc animated:YES];
}

#pragma mark - Lazy Load

- (UIStackView *)stackView {
    if (!_stackView) {
        _stackView = [[UIStackView alloc] initWithArrangedSubviews:@[
            self.autoPageSegment,
            self.showSeparatorSwitch,
            self.autoDetectSwitch,
            self.blacklistArrow,
            self.manualMarkArrow
        ]];
        _stackView.axis = UILayoutConstraintAxisVertical;
        _stackView.spacing = 0;
    }
    return _stackView;
}

- (UIView *)groupView {
    if (!_groupView) {
        _groupView = [[UIView alloc] init];
        
        // 设置圆角和阴影
        _groupView.layer.cornerRadius = 10;
        _groupView.layer.masksToBounds = YES;
        _groupView.layer.shadowColor = [UIColor blackColor].CGColor;
        _groupView.layer.shadowOffset = CGSizeMake(0, 2);
        _groupView.layer.shadowOpacity = 0.1;
        _groupView.layer.shadowRadius = 4;
    }
    return _groupView;
}

- (SettingSegmentView *)autoPageSegment {
    if (!_autoPageSegment) {
        _autoPageSegment = [[SettingSegmentView alloc] initWithTitle:NSLocalizedString(@"autopage.title", nil)
                                                          showLine:YES 
                                                            segments:@[NSLocalizedString(@"autopage.enable", nil),NSLocalizedString(@"autopage.disable", nil)]];
    }
    return _autoPageSegment;
}

- (SettingSwitchView *)showSeparatorSwitch {
    if (!_showSeparatorSwitch) {
        _showSeparatorSwitch = [[SettingSwitchView alloc] initWithShowLine:YES];
    }
    return _showSeparatorSwitch;
}

- (SettingSwitchAndTextView *)autoDetectSwitch {
    if (!_autoDetectSwitch) {
        _autoDetectSwitch = [[SettingSwitchAndTextView alloc] initWithShowLine:YES];
        BOOL isEnabled = [[PreferenceManager shareInstance].items.isEnabledAutoPageAutoDetect boolValue];
        [_autoDetectSwitch updateWithTitle:NSLocalizedString(@"autopage.smart.detect.enable", nil)
                                    detail:NSLocalizedString(@"autopage.smart.detect.detail", nil)
                                      isOn:isEnabled];
    }
    return _autoDetectSwitch;
}

- (SettingArrowView *)blacklistArrow {
    if (!_blacklistArrow) {
        _blacklistArrow = [[SettingArrowView alloc] initWithShowLine:YES];
        [_blacklistArrow updateWithTitle:NSLocalizedString(@"autopage.blacklist", nil)];
    }
    return _blacklistArrow;
}

- (SettingArrowView *)manualMarkArrow {
    if (!_manualMarkArrow) {
        _manualMarkArrow = [[SettingArrowView alloc] initWithShowLine:NO];
        [_manualMarkArrow updateWithTitle:NSLocalizedString(@"autopage.tagit", nil)];
    }
    return _manualMarkArrow;
}

@end
