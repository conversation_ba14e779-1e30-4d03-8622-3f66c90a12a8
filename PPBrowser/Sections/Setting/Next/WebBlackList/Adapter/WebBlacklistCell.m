//
//  WebBlacklistCell.m
//  PPBrowser
//
//  Created by qingbin on 2023/8/5.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import "WebBlacklistCell.h"

#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "UIView+FrameHelper.h"
#import "NSObject+Helper.h"
#import "ThemeProtocol.h"
#import "BrowserUtils.h"

@interface WebBlacklistCell ()

@property(nonatomic,strong) WebBlacklistModel* model;

@property(nonatomic,strong) UIView* backView;

@property(nonatomic,strong) UILabel* urlLabel;

@property(nonatomic,strong) UIView* line;

@end

@implementation WebBlacklistCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        self.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        [self addSubviews];
        [self defineLayout];
        [self setupObservers];
    }
    
    return self;
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.backView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        self.urlLabel.textColor = UIColor.whiteColor;
        self.line.backgroundColor = [UIColor colorWithHexString:kDarkThemeColorLine];
    } else {
        self.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.backView.backgroundColor = UIColor.whiteColor;
        self.urlLabel.textColor = [UIColor colorWithHexString:@"#333333"];
        self.line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
}

- (void)updateWithModel:(WebBlacklistModel*)model
{
    //可以增加区分, 联想词和url的联想词使用不同的logo
    self.model = model;
    
    self.urlLabel.text = model.url;

    [self updateCornerRadius];
    [self applyTheme];
}

- (void)updateCornerRadius
{
    self.backView.layer.cornerRadius = iPadValue(20, 10);
    
    // 圆角
    self.line.hidden = NO;
    if(self.model.isFirstInSection && self.model.isLastInSection) {
        //只有一个
        self.backView.layer.masksToBounds = YES;
        self.backView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner | kCALayerMinXMaxYCorner | kCALayerMaxXMaxYCorner;
        self.line.hidden = YES;
    } else {
        if(self.model.isFirstInSection) {
            //添加上圆角
            self.backView.layer.masksToBounds = YES;
            self.backView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMinXMaxYCorner;
        } else if(self.model.isLastInSection) {
            //添加下圆角
            self.backView.layer.masksToBounds = YES;
            self.backView.layer.maskedCorners = kCALayerMaxXMinYCorner | kCALayerMaxXMaxYCorner;
            self.line.hidden = YES;
        } else {
            self.backView.layer.masksToBounds = NO;
        }
    }
}

- (void)setupObservers
{
}

- (void)addSubviews
{
    [self.contentView addSubview:self.backView];
    [self.backView addSubview:self.urlLabel];
}

- (void)defineLayout
{
    float offset = iPadValue(30, 15);
    [self.backView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(offset);
        make.right.mas_offset(-offset);
        make.top.bottom.equalTo(self.contentView);
    }];
        
    [self.urlLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.backView);
        make.left.mas_offset(offset);
        make.right.mas_offset(-5);
    }];
    
    UIView* line = [UIView new];
    line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    [self.backView addSubview:line];
    [line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.backView);
        make.left.equalTo(self.backView);
        make.right.mas_offset(0);
        make.height.mas_equalTo(0.5);
    }];
    self.line = line;
}

- (UILabel *)urlLabel
{
    if(!_urlLabel) {
        float font = iPadValue(20, 15);
        _urlLabel = [UIView createLabelWithTitle:@""
                                         textColor:[UIColor colorWithHexString:@"#333333"]
                                           bgColor:UIColor.clearColor
                                          fontSize:font
                                     textAlignment:NSTextAlignmentLeft
                                             bBold:NO];
    }
    
    return _urlLabel;
}

- (UIView *)backView
{
    if(!_backView) {
        _backView = [UIView new];
        _backView.backgroundColor = UIColor.whiteColor;
        
        _backView.layer.masksToBounds = YES;
        _backView.layer.cornerRadius = iPadValue(20, 10);
    }
    
    return _backView;
}

@end
