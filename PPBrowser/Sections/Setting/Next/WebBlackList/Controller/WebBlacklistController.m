//
//  WebBlacklistController.m
//  PPBrowser
//
//  Created by qingbin on 2023/8/5.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import "WebBlacklistController.h"

#import "WebBlacklistCell.h"

#import "MaizyHeader.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"

#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "NSObject+Helper.h"
#import "UIView+FrameHelper.h"

#import "UITableView+HintMessage.h"

#import "ThemeProtocol.h"
#import "DatabaseUnit+WebBlacklist.h"

#import "BrowserUtils.h"
#import "WebBlacklistHelper.h"

#import "YYText.h"

@interface WebBlacklistController ()<UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) UIView *emptyView;

@property (nonatomic, strong) UILabel *tipsLabel;

@property (nonatomic, strong) UITableView* tableView;

@property (nonatomic, strong) NSMutableArray* model;

@end

@implementation WebBlacklistController

- (void)viewDidLoad
{
    [super viewDidLoad];
    
    self.title = NSLocalizedString(@"web.blacklist", nil);
    
    self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    
    [self createCustomLeftBarButtonItem];
    
    [self addSubviews];
    [self defineLayout];
    [self setupObservers];
    
    [self requestWebBlacklist];
    
    [self applyTheme];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.tableView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        
        self.emptyView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        self.tipsLabel.textColor = UIColor.whiteColor;
    } else {
        self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.tableView.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        
        self.emptyView.backgroundColor = UIColor.whiteColor;
        self.tipsLabel.textColor = [UIColor colorWithHexString:@"#333333"];
    }
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    //只有当前的controller会触发一次, 其它已存在的controller走通知
    if(needReload) {
        //修改暗黑模式
        [self applyTheme];
        [self.tableView reloadData];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
    [self.tableView reloadData];
}

#pragma mark -- layout
- (void)addSubviews
{
    [self.view addSubview:self.tableView];
    
    [self.view addSubview:self.emptyView];
    [self.emptyView addSubview:self.tipsLabel];
}

- (void)defineLayout
{
    float leftOffset = iPadValue(30, 15);
    float topOffset = iPadValue(30, 15);
    
    [self.emptyView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.view);
        make.top.equalTo(self.view).mas_offset(50);
        make.left.mas_offset(leftOffset);
        make.right.mas_offset(-leftOffset);
    }];
    
    [self.tipsLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(leftOffset);
        make.right.mas_offset(-leftOffset);
        make.top.mas_offset(topOffset);
        
        //决定empty高度
        make.bottom.mas_offset(-topOffset);
    }];
    
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(0);
        make.left.mas_offset(0);
        make.right.mas_offset(-0);
        make.bottom.equalTo(self.view);
    }];
}

- (void)setupObservers
{
}

#pragma mark -- 获取历史记录
- (void)requestWebBlacklist
{
    DatabaseUnit* unit = [DatabaseUnit queryAllWebBlacklist];
    @weakify(self)
    [unit setCompleteBlock:^(NSArray* result, BOOL success) {
        @strongify(self)
        if(!self) return;
        if(success) {
            [self.model removeAllObjects];
            [self.model addObjectsFromArray:result];
            
            WebBlacklistModel* item = self.model.firstObject;
            item.isFirstInSection = YES;
            
            item = self.model.lastObject;
            item.isLastInSection = YES;
            
            [self showOrHideHintMessage];
            
            [self.tableView reloadData];
        }
    }];
    
    DB_EXEC(unit);
}

#pragma mark -- 无数据提醒
- (void)showOrHideHintMessage
{
    if(self.model.count > 0) {
        self.emptyView.hidden = YES;
    } else {
        self.emptyView.hidden = NO;
    }
}

#pragma mark - UITableViewDataSource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return self.model.count;
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    WebBlacklistModel* item = self.model[indexPath.row];
    WebBlacklistCell *cell =  [tableView dequeueReusableCellWithIdentifier:NSStringFromClass(WebBlacklistCell.class)];
    [cell updateWithModel:item];
    
    return cell;
}

- (BOOL)tableView:(UITableView *)tableView canEditRowAtIndexPath:(NSIndexPath *)indexPath
{
    return YES;
}

// 只适用于ios11 以及以后版本
- (UISwipeActionsConfiguration *)tableView:(UITableView *)tableView trailingSwipeActionsConfigurationForRowAtIndexPath:(NSIndexPath *)indexPath  API_AVAILABLE(ios(11.0))
{
    // 删除数据
    @weakify(self)
    UIContextualAction *deleteAction = [UIContextualAction contextualActionWithStyle:UIContextualActionStyleDestructive title:NSLocalizedString(@"tableview.delete", nil) handler:^(UIContextualAction * _Nonnull action, __kindof UIView * _Nonnull sourceView, void (^ _Nonnull completionHandler)(BOOL)) {
        @strongify(self)
        [tableView setEditing:NO animated:YES];//退出编辑模式，隐藏左滑菜单
    
        WebBlacklistModel* item = self.model[indexPath.row];
        [[WebBlacklistHelper shareInstance] removeBlockModel:item];
         
        dispatch_async(dispatch_get_main_queue(), ^{
            [tableView beginUpdates];
            //这里的顺序很重要, 先删除model, 再删除ui
            [self.model removeObject:item];
            [tableView deleteRowsAtIndexPaths:@[indexPath] withRowAnimation:UITableViewRowAnimationFade];
            [tableView endUpdates];
            
            [self showOrHideHintMessage];
            completionHandler(YES);
        });
    }];
    [deleteAction setBackgroundColor:UIColor.redColor];
    
    // 添加
    UISwipeActionsConfiguration *actions = [UISwipeActionsConfiguration configurationWithActions:@[deleteAction]];
    actions.performsFirstActionWithFullSwipe = NO;
    return actions;
}

#pragma mark - UITableViewDelegate
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    [tableView deselectRowAtIndexPath:indexPath animated:NO];

}

#pragma mark -- lazy init
- (UITableView *)tableView
{
    if(!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];

        _tableView.separatorStyle  = UITableViewCellSeparatorStyleNone;
        _tableView.showsHorizontalScrollIndicator = NO;
        _tableView.showsVerticalScrollIndicator   = NO;
        _tableView.delegate   = self;
        _tableView.dataSource = self;
        _tableView.rowHeight = iPadValue(80, 50);
        _tableView.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        
        [_tableView registerClass:[WebBlacklistCell class] forCellReuseIdentifier:NSStringFromClass([WebBlacklistCell class])];
        
        _tableView.sectionFooterHeight = 0.0;
        
        float offset = iPadValue(30, 20);
        _tableView.tableHeaderView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, offset)];
        
        UIWindow* window = [NSObject normalWindow];
        _tableView.tableFooterView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, offset+window.safeAreaInsets.bottom)];
        
    }
    
    return _tableView;
}

- (NSMutableArray *)model
{
    if(!_model) {
        _model = [NSMutableArray array];
    }
    
    return _model;
}

- (UIView *)emptyView
{
    if(!_emptyView) {
        _emptyView = [UIView new];
        
        _emptyView.layer.cornerRadius = iPadValue(20, 10);
        _emptyView.layer.masksToBounds = YES;
        
        _emptyView.hidden = YES;
    }
    
    return _emptyView;
}

- (UILabel *)tipsLabel
{
    if(!_tipsLabel) {
        _tipsLabel = [UILabel new];
        _tipsLabel.numberOfLines = 0;
        _tipsLabel.font = [UIFont systemFontOfSize:iPadValue(20, 16)];
        _tipsLabel.textColor = [UIColor colorWithHexString:@"#666666"];
        _tipsLabel.textAlignment = NSTextAlignmentLeft;
        
        NSString* text = NSLocalizedString(@"adblock.blacklist.tips", nil);
        NSMutableAttributedString* attributedString = [[NSMutableAttributedString alloc]initWithString:text];
        attributedString.yy_lineSpacing = 5;
        attributedString.yy_paragraphSpacing = 10;
        
        _tipsLabel.attributedText = attributedString;
    }
    
    return _tipsLabel;
}

@end
