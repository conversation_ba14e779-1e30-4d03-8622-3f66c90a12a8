//
//  WebBlacklistModel.h
//  PPBrowser
//
//  Created by qing<PERSON> on 2023/8/5.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import "BaseModel.h"
#import "NSURL+Extension.h"
#import <CloudKit/CloudKit.h>
#import "SyncProtocol.h"

@interface WebBlacklistModel : BaseModel<SyncProtocol>

//id
@property (nonatomic, strong) NSString *uuid;
//host
@property (nonatomic, strong) NSString *url;
//生成时间
@property (nonatomic, strong) NSString *ctime;
// 更新时间，和iCloud的对比
@property (nonatomic, strong) NSString *updateTime;

//辅助字段,主要是卡片化
@property (nonatomic, assign) BOOL isFirstInSection;
@property (nonatomic, assign) BOOL isLastInSection;

//CloudKit同步
//获取一个默认的CKRecord
- (CKRecord *)toDefaultCKRecord;
//转换成CKRecord
- (CKRecord *)toCKRecord;
//从CKRecord转换为WebBlacklistModel
- (instancetype)initWithCKRecord:(CKRecord *)record;
//判断两个是否相同
- (BOOL)objectIsEqualTo:(id)obj;

//返回uuid
- (NSString*)getUuid;
//更新时间
- (NSString*)getUpdateTime;

@end

