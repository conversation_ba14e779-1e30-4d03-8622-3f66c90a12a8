//
//  WebBlacklistHelper.m
//  PPBrowser
//
//  Created by qingbin on 2023/8/6.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import "WebBlacklistHelper.h"
#import "InternalURL.h"
#import "NSURL+Extension.h"

#import "DatabaseUnit+WebBlacklist.h"

#import "PPNotifications.h"
#import "ReactiveCocoa.h"
#import "NSObject+Helper.h"
#import "UIView+Helper.h"

#import "BrowserUtils.h"
#import "UIAlertController+SafePresentation.h"

@interface WebBlacklistHelper()

@property (nonatomic, strong) NSMutableDictionary* urlMapper;

@property (nonatomic, strong) NSMutableArray* urlArray;

@end

@implementation WebBlacklistHelper

+ (instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    static WebBlacklistHelper* obj;
    dispatch_once(&onceToken, ^{
        obj = [WebBlacklistHelper new];
    });
    
    return obj;
}

- (instancetype)init
{
    self = [super init];
    if(self) {
        [self setupObservers];
    }
    
    return self;
}

- (void)reloadData
{
    [self.urlArray removeAllObjects];
    self.urlArray = nil;
    
    [self.urlMapper removeAllObjects];
    self.urlMapper = nil;
    
    DatabaseUnit* unit = [DatabaseUnit queryAllWebBlacklist];
    @weakify(self)
    [unit setCompleteBlock:^(NSArray* result, BOOL success) {
        @strongify(self)
        if(!self) return;
        if(success) {
            [self.urlArray addObjectsFromArray:result];
            for(WebBlacklistModel *item in result) {
                self.urlMapper[item.url] = item;
            }
        }
    }];
    
    DB_EXEC(unit);
}

- (void)setupObservers
{
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(reloadData)
                                                 name:kCloudKitDataDidChangeNotification
                                               object:nil];
}

- (void)addBlockURL:(NSURL *)URL
{
    if([InternalURL isValid:URL]) return;
    
    NSString* normalizedHost = [URL normalizedHost];
    if(normalizedHost.length > 0) {
        if(self.urlMapper[normalizedHost]) return;
    } else {
        return;
    }
    
    UIAlertController* alertController = [UIAlertController alertControllerWithTitle:NSLocalizedString(@"webblacklist.add.title", nil) message:NSLocalizedString(@"webblacklist.add.text", nil) preferredStyle:UIAlertControllerStyleAlert];
    UIAlertAction* action = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.cancel", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [alertController dismissViewControllerAnimated:YES completion:nil];
    }];
    [alertController addAction:action];
    
    action = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.confirm", nil) style:UIAlertActionStyleDestructive handler:^(UIAlertAction * _Nonnull action) {
        [self _addBlockUrl:normalizedHost];
    }];
    [alertController addAction:action];

    UIWindow* window = [NSObject normalWindow];
//    [window.rootViewController presentViewController:alertController animated:YES completion:nil];
    //v2.6.8, 统一present方法，防止崩溃
    [alertController presentSafelyFromViewController:window.rootViewController];
}

- (void)_addBlockUrl:(NSString *)url
{
    WebBlacklistModel* model = [WebBlacklistModel new];
    model.url = url;
    
    DatabaseUnit* unit = [DatabaseUnit addWebBlacklistWithItem:model];
    DB_EXEC(unit);
    
    self.urlMapper[url] = model;
    [self.urlArray addObject:model];
    
    [UIView showSucceed:NSLocalizedString(@"webblacklist.add.success", nil)];
}

- (void)removeBlockModel:(WebBlacklistModel *)model
{
    DatabaseUnit* unit = [DatabaseUnit removeWebBlacklistWithItem:model];
    @weakify(self)
    unit.completeBlock = ^(id result, BOOL success) {
        @strongify(self)
        if(success) {
            [self reloadData];
            //刷新所有页面
            [[NSNotificationCenter defaultCenter] postNotificationName:kReloadUserScriptNotification object:nil];
        }
    };

    DB_EXEC(unit);
}

- (BOOL)isBlockURL:(NSURL *)URL
{
    if(!URL) return NO;
    if([InternalURL isValid:URL]) return NO;
    
    NSString* normalizedHost = [URL normalizedHost];
    return self.urlMapper[normalizedHost] != nil;
}

- (NSMutableArray *)urlArray
{
    if(!_urlArray) {
        _urlArray = [NSMutableArray array];
    }
    
    return _urlArray;
}

- (NSMutableDictionary *)urlMapper
{
    if(!_urlMapper) {
        _urlMapper = [NSMutableDictionary dictionary];
    }
    
    return _urlMapper;
}

@end
