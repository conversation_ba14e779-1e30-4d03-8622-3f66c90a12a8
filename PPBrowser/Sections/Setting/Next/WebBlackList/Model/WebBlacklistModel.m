//
//  WebBlacklistModel.m
//  PPBrowser
//
//  Created by qingbin on 2023/8/5.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import "WebBlacklistModel.h"

#import "OpenUDID.h"
#import "PPEnums.h"
#import "CloudKitHelper.h"

@implementation WebBlacklistModel

//返回uuid
- (NSString*)getUuid
{
    return self.uuid;
}

//更新时间
- (NSString*)getUpdateTime
{
    return self.updateTime;
}

//CloudKit同步
//获取一个默认的CKRecord
- (CKRecord *)toDefaultCKRecord
{
    CKRecordID* recordID = [[CKRecordID alloc]initWithRecordName:self.uuid zoneID:[CloudKitHelper focusZoneID]];
    CKRecord* record = [[CKRecord alloc]initWithRecordType:NSStringFromClass(WebBlacklistModel.class) recordID:recordID];
    
    return record;
}

//转换成CKRecord
- (CKRecord *)toCKRecord
{
    CKRecord* record = [self toDefaultCKRecord];
    
    record[@"uuid"] = self.uuid;
    record[@"url"] = self.url?:@"";
    record[@"ctime"] = self.ctime;
    record[@"updateTime"] = self.updateTime?:@"1";
    
    //额外添加的字段，用于CloudKit
    //app版本号
    NSString* version = [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleShortVersionString"];
    record[@"appVersion"] = version?:@"";
    //上传到CloudKit创建时间
    record[@"appCtime"] = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
    //用户的uuid
    record[@"appUserID"] = [OpenUDID value]?:@"";
    //添加类型区分
    record[@"appType"] = @(CloudModelTypeWebBlacklist);
    
    return record;
}
//从CKRecord转换为CustomTagModel
- (instancetype)initWithCKRecord:(CKRecord *)record
{
    self = [super init];
    if(self) {
        self.uuid = record[@"uuid"];
        self.url = record[@"url"];
        self.ctime = record[@"ctime"];
        
        NSString* updateTimeText = record[@"updateTime"];
        NSInteger updateTime = [updateTimeText integerValue];
        if(updateTime == 0 || updateTime == 1) {
            //初始化
            updateTime = [record.modificationDate timeIntervalSince1970];
        }
        self.updateTime = [NSString stringWithFormat:@"%ld", updateTime];
    }
    
    return self;
}

//判断两个是否相同
- (BOOL)objectIsEqualTo:(id)obj
{
    WebBlacklistModel *item = obj;
    if([self.uuid isEqualToString:item.uuid]) return YES;
    
    return [self.url isEqualToString:item.url];
}

@end
