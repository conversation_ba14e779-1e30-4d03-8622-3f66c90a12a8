//
//  WebBlacklistHelper.h
//  PPBrowser
//
//  Created by qing<PERSON> on 2023/8/6.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>

#import "WebBlacklistModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface WebBlacklistHelper : NSObject

+ (instancetype)shareInstance;

- (BOOL)isBlockURL:(NSURL *)URL;

- (void)addBlockURL:(NSURL *)URL;

- (void)removeBlockModel:(WebBlacklistModel *)model;

- (void)reloadData;

@end

NS_ASSUME_NONNULL_END
