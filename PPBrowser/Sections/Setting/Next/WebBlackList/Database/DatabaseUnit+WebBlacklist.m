//
//  DatabaseUnit+WebBlacklist.m
//  PPBrowser
//
//  Created by qingbin on 2023/8/5.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import "DatabaseUnit+WebBlacklist.h"
#import "ReactiveCocoa.h"

#import "FMDatabaseQueue.h"
#import "FMDatabase.h"

#import "PPEnums.h"
#import "SyncEngine.h"
#import "CloudKitHelper.h"

@implementation DatabaseUnit (WebBlacklist)

+ (DatabaseUnit*)addWebBlacklistWithItem:(WebBlacklistModel*)item
{
    //https://stackoverflow.com/questions/3634984/insert-if-not-exists-else-update
    DatabaseUnit* unit = [DatabaseUnit new];

    //必须有id
    if(item.uuid.length == 0) {
        item.uuid = [[NSUUID UUID] UUIDString];
    }
    
    if(item.url.length == 0) item.url = @"";
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* time = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        item.ctime = time;
        item.updateTime = time;
        
        NSString* command = @"INSERT INTO t_webBlacklist(uuid, url, updateTime, ctime) VALUES (?,?,?,?);";
        BOOL result = [db executeUpdate:command, item.uuid, item.url?:@"", item.updateTime, item.ctime];
    
        if(result) {
            //同步到CloudKit中
            [[SyncEngine shareInstance] syncRecordsToCloudKit:@[[item toCKRecord]] recordIDsToDelete:nil completion:nil];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

+ (DatabaseUnit*)removeWebBlacklistWithItem:(WebBlacklistModel*)item
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = @"DELETE FROM t_webBlacklist WHERE uuid=?;";
        BOOL result = [db executeUpdate:command, item.uuid];

        if(result) {
            //同步到CloudKit中
            CKRecordID* recordID = [[CKRecordID alloc]initWithRecordName:item.uuid zoneID:[CloudKitHelper focusZoneID]];
            [[SyncEngine shareInstance] syncRecordsToCloudKit:nil recordIDsToDelete:@[recordID] completion:nil];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

+ (DatabaseUnit*)queryAllWebBlacklist
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = [NSString stringWithFormat:@"SELECT * FROM t_webBlacklist ORDER BY ctime DESC"];
        FMResultSet* set = [db executeQuery:command];
        
        NSMutableArray *array = [[NSMutableArray alloc] init];
        while([set next]) {
            WebBlacklistModel* item = [[WebBlacklistModel alloc]initWithDictionary:[set resultDictionary] error:nil];
            [array addObject:item];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(array,YES);
            }
        });
    };
    
    return unit;
}

#pragma mark -- CloudKit相关操作
// CloudKit, 添加多个
// CREATE TABLE IF NOT EXISTS t_webBlacklist(uuid TEXT PRIMARY KEY, url TEXT, ctime TEXT)
+ (DatabaseUnit*)addWebBlacklistArray:(NSArray<WebBlacklistModel*>*)items
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = @"INSERT INTO t_webBlacklist(uuid, url, updateTime, ctime) VALUES (?,?,?,?)";
        BOOL result = YES;
        for(int i=0;i<items.count;i++) {
            WebBlacklistModel* item = items[i];
            result = [db executeUpdate:command, item.uuid, item.url?:@"", item.updateTime?:@"1", item.ctime];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

// CloudKit, 批量更新多个
+ (DatabaseUnit*)updateWebBlacklistArray:(NSArray<WebBlacklistModel*>*)array
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        BOOL result = NO;
        for(WebBlacklistModel* item in array) {
            NSString* command = @"UPDATE t_webBlacklist SET url=?, updateTime=?, ctime=? WHERE uuid=?;";
            result = [db executeUpdate:command, item.url?:@"", item.updateTime?:@"1", item.ctime, item.uuid];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

// CloudKit, 批量删除多个
+ (DatabaseUnit*)removeWebBlacklistArray:(NSArray*)webBlacklistIds
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = [NSString stringWithFormat:@"DELETE FROM t_webBlacklist WHERE uuid=?;"];

        BOOL result = YES;
        for(int i=0;i<webBlacklistIds.count;i++) {
            NSString* webBlacklistId = webBlacklistIds[i];
            result = [db executeUpdate:command, webBlacklistId];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

@end
