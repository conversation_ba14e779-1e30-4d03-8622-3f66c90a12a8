//
//  DatabaseUnit+WebBlacklist.h
//  PPBrowser
//
//  Created by qingbin on 2023/8/5.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import "DatabaseUnit.h"

#import "WebBlacklistModel.h"
#import "PPEnums.h"

@interface DatabaseUnit (WebBlacklist)

+ (DatabaseUnit*)addWebBlacklistWithItem:(WebBlacklistModel*)item;

+ (DatabaseUnit*)removeWebBlacklistWithItem:(WebBlacklistModel*)item;

+ (DatabaseUnit*)queryAllWebBlacklist;

///
// CloudKit, 添加多个
+ (DatabaseUnit*)addWebBlacklistArray:(NSArray<WebBlacklistModel*>*)items;

// CloudKit, 批量更新多个
+ (DatabaseUnit*)updateWebBlacklistArray:(NSArray<WebBlacklistModel*>*)array;

// CloudKit, 批量删除多个
+ (DatabaseUnit*)removeWebBlacklistArray:(NSArray*)webBlacklistIds;

@end

