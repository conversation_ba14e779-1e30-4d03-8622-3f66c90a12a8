//
//  ToolbarOrderSettingController.m
//  PPBrowser
//
//  Created by qingbin on 2022/8/29.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "ToolbarOrderSettingController.h"

#import "PPNotifications.h"

#import "ThemeProtocol.h"
#import "PreferenceManager.h"

#import "Masonry.h"
#import "MaizyHeader.h"
#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "ReactiveCocoa.h"
#import "UIView+FrameHelper.h"
#import "UIViewController+Helper.h"
#import "NSObject+Helper.h"
#import "ToolbarOptionView.h"
#import "BrowserUtils.h"

@interface ToolbarOrderSettingController ()<ThemeProtocol>

@property (nonatomic, strong) UIScrollView* scrollView;
@property (nonatomic, strong) UIStackView* stackView;
@property (nonatomic, strong) NSMutableArray* optionViews;
@property (nonatomic, strong) UILabel* infoLabel;
@property (nonatomic, strong) UIView* infoView;
@property (nonatomic, assign) ToolbarOption selectOption;

@end

@implementation ToolbarOrderSettingController

- (void)viewDidLoad
{
    [super viewDidLoad];

    self.title = NSLocalizedString(@"homeSetting.bottomtoolbar.text", nil);
    
    [self addSubviews];
    [self defineLayout];
    [self createCustomLeftBarButtonItem];
    [self updateWithModel];
    
    [self applyTheme];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.scrollView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        
        // 更新信息提示框样式
        self.infoView.backgroundColor = [UIColor colorWithHexString:@"#1a3a57"];
        self.infoLabel.textColor = [UIColor colorWithHexString:@"#a8c7fa"];
    } else {
        self.view.backgroundColor = [UIColor colorWithHexString:@"#f8f9fa"];
        self.scrollView.backgroundColor = [UIColor colorWithHexString:@"#f8f9fa"];
        
        // 更新信息提示框样式
        self.infoView.backgroundColor = [UIColor colorWithHexString:@"#e6f0ff"];
        self.infoLabel.textColor = [UIColor colorWithHexString:@"#1a3a57"];
    }
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    //只有当前的controller会触发一次, 其它已存在的controller走通知
    if(needReload) {
        //修改暗黑模式
        [self applyTheme];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}

- (void)updateWithModel
{
    ToolbarOption selectOption = [[PreferenceManager shareInstance].items.toolBarOption intValue];
    self.selectOption = selectOption;
    
    for(int i=0;i<5;i++) {
        ToolbarOptionView* view = [[ToolbarOptionView alloc] initWithOption:i];
        [self.stackView addArrangedSubview:view];
        
        [view mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.equalTo(self.stackView);
            make.height.mas_equalTo(120);
        }];
        
        [self.optionViews addObject:view];
        
        @weakify(self)
        [view setDidSelectAction:^{
            @strongify(self)
            self.selectOption = i;
            for(ToolbarOptionView* item in self.optionViews) {
                [item updateSelectOption:i];
            }
            
            [self _saveSelectOption];
        }];
        
        [view updateSelectOption:selectOption];
    }
}

- (void)_saveSelectOption
{
    [PreferenceManager shareInstance].items.toolBarOption = @(self.selectOption);
    [[PreferenceManager shareInstance] encode];
    [[NSNotificationCenter defaultCenter] postNotificationName:kToolbarDidChangeNotification object:nil];
    
    [UIView showToast:NSLocalizedString(@"toolbarSetting.save.text", nil)];
}

#pragma mark -- layout
- (void)addSubviews
{
    [self.view addSubview:self.scrollView];
    [self.scrollView addSubview:self.infoView];
    [self.scrollView addSubview:self.stackView];
}

- (void)defineLayout
{
    float leftOffset = iPadValue(30, 15);
    float topOffset = iPadValue(30, 15);
    
    [self.scrollView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
    }];
    
    UIWindow* window = [NSObject normalWindow];
    [self.infoView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.scrollView).offset(topOffset);
        make.left.equalTo(self.scrollView).offset(leftOffset);
        make.right.equalTo(self.scrollView).offset(-leftOffset);
    }];
    
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.infoView.mas_bottom).offset(topOffset);
        make.left.equalTo(self.scrollView).offset(leftOffset);
        make.right.equalTo(self.scrollView).offset(-leftOffset);
        make.width.equalTo(self.scrollView).offset(-leftOffset*2);
        make.bottom.equalTo(self.scrollView).offset(-(30 + window.safeAreaInsets.bottom));
    }];
}

#pragma mark -- lazy init
- (UIScrollView *)scrollView
{
    if (!_scrollView) {
        _scrollView = [[UIScrollView alloc] init];
        _scrollView.showsVerticalScrollIndicator = NO;
        _scrollView.showsHorizontalScrollIndicator = NO;
        _scrollView.backgroundColor = [UIColor colorWithHexString:@"#f8f9fa"];
        _scrollView.alwaysBounceVertical = YES;
    }
    return _scrollView;
}

- (UIStackView *)stackView
{
    if(!_stackView) {
        _stackView = [[UIStackView alloc]init];
        
        _stackView.spacing = 16;
        _stackView.axis = UILayoutConstraintAxisVertical;
        _stackView.backgroundColor = UIColor.clearColor;
    }
    
    return _stackView;
}

- (NSMutableArray *)optionViews
{
    if(!_optionViews) {
        _optionViews = [NSMutableArray array];
    }
    
    return _optionViews;
}

- (UIView *)infoView
{
    if (!_infoView) {
        _infoView = [[UIView alloc] init];
        _infoView.backgroundColor = [UIColor colorWithHexString:@"#e6f0ff"];
        _infoView.layer.cornerRadius = 14;
        _infoView.layer.masksToBounds = YES;
        
        // 添加提示信息视图
        UIView *infoIconView = [[UIView alloc] init];
        UIImage* image = [[UIImage imageNamed:@"setting_circle_info"] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        UIImageView *infoIcon = [[UIImageView alloc] initWithImage:image];
        infoIcon.tintColor = [UIColor colorWithHexString:@"#007aff"];
        
        [infoIconView addSubview:infoIcon];
        [infoIcon mas_makeConstraints:^(MASConstraintMaker *make) {
            make.size.mas_equalTo(CGSizeMake(20, 20));
            make.center.equalTo(infoIconView);
        }];
        
        [_infoView addSubview:infoIconView];
        [_infoView addSubview:self.infoLabel];
        
        [infoIconView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.size.mas_equalTo(CGSizeMake(30, 30));
            make.left.equalTo(self.infoView).offset(12);
            make.centerY.equalTo(self.infoView);
        }];
        
        [self.infoLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(infoIconView.mas_right).offset(8);
            make.top.equalTo(self.infoView).offset(12);
            make.right.equalTo(self.infoView).offset(-12);
            make.bottom.equalTo(self.infoView).offset(-12);
        }];
    }
    return _infoView;
}

- (UILabel *)infoLabel
{
    if (!_infoLabel) {
        _infoLabel = [[UILabel alloc] init];
        _infoLabel.text = NSLocalizedString(@"toolbarSetting.tips.text", nil);
        _infoLabel.textColor = [UIColor colorWithHexString:@"#1a3a57"];
        _infoLabel.font = [UIFont systemFontOfSize:iPadValue(18, 14)];
        _infoLabel.numberOfLines = 0;
    }
    return _infoLabel;
}

@end
