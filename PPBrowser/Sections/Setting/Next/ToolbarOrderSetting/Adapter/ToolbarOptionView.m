//
//  ToolbarOptionView.m
//  PPBrowser
//
//  Created by qingbin on 2022/8/29.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "ToolbarOptionView.h"

#import "ThemeProtocol.h"
#import "PreferenceManager.h"

#import "Masonry.h"
#import "MaizyHeader.h"
#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "ReactiveCocoa.h"
#import "UIView+FrameHelper.h"
#import "UIViewController+Helper.h"
#import "NSObject+Helper.h"
#import "PPNotifications.h"

@interface ToolbarOptionView ()

@property (nonatomic, strong) UILabel *titleLabel;

// 自定义工具栏 - 改为 UIStackView 实现
@property (nonatomic, strong) UIStackView *toolbarStackView;
@property (nonatomic, strong) NSMutableArray<UIView *> *toolbarButtons;

// 选中 - 改为单选按钮样式
@property (nonatomic, strong) UIView *radioButton;
@property (nonatomic, strong) UIView *innerCircle;

@property (nonatomic, assign) ToolbarOption option;

@end

@implementation ToolbarOptionView

- (instancetype)initWithOption:(ToolbarOption)option
{
    self = [super init];
    if(self) {
        self.option = option;
        
        [self setupViewStyle];
        [self addSubviews];
        [self defineLayout];
        [self setupObservers];
        
        [self updateWithModel];
        [self applyTheme];
    }
    
    return self;
}

- (void)dealloc
{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)setupViewStyle
{
    // 设置卡片样式
    self.layer.cornerRadius = 14.0;
    self.layer.masksToBounds = NO;
    
    // 添加阴影效果
    self.layer.shadowColor = [UIColor blackColor].CGColor;
    self.layer.shadowOffset = CGSizeMake(0, 2);
    self.layer.shadowOpacity = 0.03;
    self.layer.shadowRadius = 10;
    
    // 初始化工具栏按钮数组
    self.toolbarButtons = [NSMutableArray array];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    
    if (isDarkTheme) {
        self.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        self.titleLabel.textColor = [UIColor whiteColor];
        self.layer.shadowOpacity = 0.05;
        
        self.radioButton.backgroundColor = [UIColor clearColor];
        self.radioButton.layer.borderColor = [UIColor colorWithHexString:@"#d1d1d6"].CGColor;
        self.innerCircle.backgroundColor = [UIColor colorWithHexString:@"#007aff"];
        
        // 工具栏背景
        self.toolbarStackView.backgroundColor = [UIColor colorWithHexString:@"#1c1c1e"];
        
        // 更新工具栏按钮颜色
        for (UIView *iconBackground in self.toolbarButtons) {
            iconBackground.backgroundColor = [UIColor colorWithHexString:@"#2c2c2e"];
        }
    } else {
        self.backgroundColor = [UIColor whiteColor];
        self.titleLabel.textColor = [UIColor colorWithHexString:@"#333333"];
        self.layer.shadowOpacity = 0.03;
        
        self.radioButton.backgroundColor = [UIColor clearColor];
        self.radioButton.layer.borderColor = [UIColor colorWithHexString:@"#d1d1d6"].CGColor;
        self.innerCircle.backgroundColor = [UIColor colorWithHexString:@"#007aff"];
        
        // 工具栏背景
        self.toolbarStackView.backgroundColor = [UIColor whiteColor];
        
        // 更新工具栏按钮颜色
        for (UIView *iconBackground in self.toolbarButtons) {
            iconBackground.backgroundColor = [UIColor colorWithHexString:@"#f2f2f7"];
        }
    }
}

- (void)updateSelectOption:(ToolbarOption)option
{
    if(option == self.option) {
        // 选中状态
        self.radioButton.layer.borderColor = [UIColor colorWithHexString:@"#007aff"].CGColor;
        self.innerCircle.hidden = NO;
        
        // 选中时添加蓝色边框
        self.layer.borderWidth = 2.0;
        self.layer.borderColor = [UIColor colorWithHexString:@"#007aff"].CGColor;
    } else {
        // 未选中状态
        BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
        self.radioButton.layer.borderColor = [UIColor colorWithHexString:isDarkTheme ? @"#666666" : @"#d1d1d6"].CGColor;
        self.innerCircle.hidden = YES;
        
        // 移除边框
        self.layer.borderWidth = 0;
    }
}

- (void)updateWithModel
{
    if(self.option == ToolbarOptionDefault) {
        self.titleLabel.text = NSLocalizedString(@"toolbarSetting.theme1.text", nil);
        [self setupToolbarButtons:@[@"back", @"forward", @"plus", @"layer_group", @"menu"]];
    } else if(self.option == ToolbarOption1) {
        self.titleLabel.text = NSLocalizedString(@"toolbarSetting.theme2.text", nil);
        [self setupToolbarButtons:@[@"back", @"forward", @"menu", @"layer_group", @"menu_home_icon"]];
    } else if(self.option == ToolbarOption2) {
        self.titleLabel.text = NSLocalizedString(@"toolbarSetting.theme3.text", nil);
        [self setupToolbarButtons:@[@"back", @"forward", @"menu_home_icon", @"layer_group", @"menu"]];
    } else if(self.option == ToolbarOption3) {
        self.titleLabel.text = NSLocalizedString(@"toolbarSetting.theme4.text", nil);
        [self setupToolbarButtons:@[@"back", @"forward", @"layer_group", @"menu", @"menu_home_icon"]];
    } else if(self.option == ToolbarOption4) {
        self.titleLabel.text = NSLocalizedString(@"toolbarSetting.theme5.text", nil);
        [self setupToolbarButtons:@[@"back", @"forward", @"add", @"menu", @"layer_group"]];
    }
}

- (UIImageView *)createIconImageView:(NSString *)iconName 
{
    UIImageView *iconView = [[UIImageView alloc] init];
    iconView.contentMode = UIViewContentModeScaleAspectFit;
    iconView.tintColor = [UIColor colorWithHexString:@"#007aff"];
    
    // 使用已有的图标资源
    UIImage *image = [[UIImage imageNamed:iconName] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
    iconView.image = image;
    
    return iconView;
}

- (void)setupToolbarButtons:(NSArray<NSString *> *)icons
{
    // 清除现有按钮
    for (UIView *button in self.toolbarButtons) {
        [button removeFromSuperview];
    }
    [self.toolbarButtons removeAllObjects];
    [self.toolbarStackView.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
    
    // 创建新的图标按钮
    for (NSString *iconName in icons) {
        // 创建图标背景
        UIView *iconBackground = [[UIView alloc] init];
        iconBackground.backgroundColor = [UIColor colorWithHexString:@"#f2f2f7"];
        iconBackground.layer.cornerRadius = 10;
        
        // 创建图标
        UIImageView *iconView = [self createIconImageView:iconName];
        [iconBackground addSubview:iconView];
        
        [iconView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.center.equalTo(iconBackground);
            make.size.mas_equalTo(CGSizeMake(20, 20));
        }];
        
        // 设置背景尺寸
        [iconBackground mas_makeConstraints:^(MASConstraintMaker *make) {
            make.size.mas_equalTo(CGSizeMake(40, 40));
        }];
        
        [self.toolbarStackView addArrangedSubview:iconBackground];
        [self.toolbarButtons addObject:iconBackground];
    }
}

- (void)setupObservers
{
    @weakify(self)    
    UITapGestureRecognizer* tap = [UITapGestureRecognizer new];
    [self addGestureRecognizer:tap];
    [tap.rac_gestureSignal subscribeNext:^(id x) {
        @strongify(self)
        if(self.didSelectAction) {
            self.didSelectAction();
        }
    }];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(darkThemeDidChangeNotification:)
                                                 name:kDarkThemeDidChangeNotification
                                               object:nil];
}

//修改暗黑模式
- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}

#pragma mark -- layout
- (void)addSubviews
{
    [self addSubview:self.toolbarStackView];
    [self addSubview:self.titleLabel];
    [self addSubview:self.radioButton];
    [self.radioButton addSubview:self.innerCircle];
}

- (void)defineLayout
{
    [self.toolbarStackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(16);
        make.right.mas_offset(-16);
        make.top.mas_offset(16);
        make.height.mas_equalTo(60);
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(16);
        make.top.equalTo(self.toolbarStackView.mas_bottom).offset(14);
        make.bottom.mas_offset(-16);
    }];
    
    [self.radioButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(22, 22));
        make.right.mas_offset(-16);
        make.centerY.equalTo(self.titleLabel);
    }];
    
    [self.innerCircle mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(12, 12));
        make.center.equalTo(self.radioButton);
    }];
}

- (UILabel *)titleLabel
{
    if(!_titleLabel) {
        _titleLabel = [UIView createLabelWithTitle:NSLocalizedString(@"toolbarSetting.theme1.text", nil)
                                         textColor:[UIColor colorWithHexString:@"#333333"]
                                           bgColor:UIColor.clearColor
                                          fontSize:15
                                     textAlignment:NSTextAlignmentLeft
                                             bBold:NO];
        _titleLabel.font = [UIFont systemFontOfSize:15 weight:UIFontWeightMedium];
    }
    
    return _titleLabel;
}

- (UIStackView *)toolbarStackView
{
    if(!_toolbarStackView) {
        _toolbarStackView = [[UIStackView alloc] init];
        _toolbarStackView.axis = UILayoutConstraintAxisHorizontal;
        _toolbarStackView.distribution = UIStackViewDistributionEqualSpacing;
        _toolbarStackView.alignment = UIStackViewAlignmentCenter;
        _toolbarStackView.backgroundColor = [UIColor whiteColor];
        _toolbarStackView.layer.cornerRadius = 10;
        _toolbarStackView.layer.masksToBounds = YES;
        _toolbarStackView.layoutMargins = UIEdgeInsetsMake(10, 10, 10, 10);
        _toolbarStackView.layoutMarginsRelativeArrangement = YES;
    }
    
    return _toolbarStackView;
}

- (UIView *)radioButton
{
    if(!_radioButton) {
        _radioButton = [UIView new];
        _radioButton.backgroundColor = [UIColor clearColor];
        _radioButton.layer.cornerRadius = 11;
        _radioButton.layer.borderWidth = 2;
        _radioButton.layer.borderColor = [UIColor colorWithHexString:@"#d1d1d6"].CGColor;
        _radioButton.userInteractionEnabled = NO;
    }
    
    return _radioButton;
}

- (UIView *)innerCircle
{
    if(!_innerCircle) {
        _innerCircle = [UIView new];
        _innerCircle.backgroundColor = [UIColor colorWithHexString:@"#007aff"];
        _innerCircle.layer.cornerRadius = 6;
        _innerCircle.hidden = YES;
    }
    
    return _innerCircle;
}

@end
