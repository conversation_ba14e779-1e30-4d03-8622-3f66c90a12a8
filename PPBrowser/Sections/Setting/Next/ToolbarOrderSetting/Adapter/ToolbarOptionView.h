//
//  ToolbarOptionView.h
//  PPBrowser
//
//  Created by qingbin on 2022/8/29.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "PPEnums.h"

#import "ThemeProtocol.h"
#import "PreferenceManager.h"

NS_ASSUME_NONNULL_BEGIN

@interface ToolbarOptionView : UIView<ThemeProtocol>

- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)new NS_UNAVAILABLE;

- (instancetype)initWithOption:(ToolbarOption)option;

- (void)updateSelectOption:(ToolbarOption)option;

@property (nonatomic, copy) void (^didSelectAction)(void);

@end

NS_ASSUME_NONNULL_END
