//
//  AboutUsController.m
//  PPBrowser
//
//  Created by qingbin on 2022/6/6.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "AboutUsController.h"

#import "Masonry.h"
#import "MaizyHeader.h"
#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "ReactiveCocoa.h"
#import "UIView+FrameHelper.h"
#import "UIViewController+Helper.h"
#import "NSObject+Helper.h"

#import "SettingSwitchView.h"
#import "SettingTextAndArrowView.h"
#import "SettingTextView.h"

#import "PPNotifications.h"
#import "PreferenceManager.h"
#import "BrowserUtils.h"
#import "BrowserHelper.h"

@interface AboutUsController ()

@property (nonatomic, strong) UIView* backView;

@property (nonatomic, strong) UIStackView* stackView;

@property (nonatomic, strong) SettingTextAndArrowView* joinQQView;

@property (nonatomic, strong) SettingTextAndArrowView* joinTelegramView;

@property (nonatomic, strong) SettingTextView* versionView;

@end

@implementation AboutUsController

- (void)viewDidLoad
{
    [super viewDidLoad];

    self.title = NSLocalizedString(@"setting.aboutUs", nil);
    
    [self addSubviews];
    [self defineLayout];
    [self updateWithModel];
    [self setupObservers];
    [self createCustomLeftBarButtonItem];
    
    [self applyTheme];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
    } else {
        self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    //只有当前的controller会触发一次, 其它已存在的controller走通知
    if(needReload) {
        //修改暗黑模式
        [self applyTheme];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}

- (void)updateWithModel
{
    [self.joinQQView updateWithTitle:NSLocalizedString(@"aboutus.joinqq.text", nil) content:@"945578760"];
    [self.joinTelegramView updateWithTitle:NSLocalizedString(@"aboutus.join.telegram.text", nil) content:@"https://t.me/focus_addons"];
    
    NSString* version = [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleShortVersionString"];
    [self.versionView updateWithTitle:NSLocalizedString(@"aboutus.version.text", nil) content:version];
}

- (void)setupObservers
{
    [self.joinQQView setDidAction:^{
        [BrowserHelper joinQQGroup];
    }];
    
    [self.joinTelegramView setDidAction:^{
        NSURL* URL = [NSURL URLWithString:@"https://t.me/focus_addons"];
        [[UIApplication sharedApplication] openURL:URL options:@{} completionHandler:nil];
    }];
    
    __block int countNumber = 0;
    [self.versionView setDidAction:^{
        countNumber = countNumber + 1;
        if(countNumber >= 10) {
            //解锁标记模式
            [PreferenceManager shareInstance].items.enabledTagit = @(YES);
            [[PreferenceManager shareInstance] encode];
        }
    }];
}

#pragma mark -- layout
- (void)addSubviews
{
    //https://stackoverflow.com/questions/33927914/how-can-i-set-the-cornerradius-of-a-uistackview
    //UIStackView添加圆角适配
    
    [self.view addSubview:self.backView];
    [self.backView addSubview:self.stackView];
}

- (void)defineLayout
{
    float offset = iPadValue(30, 15);
    [self.backView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.bottom.equalTo(self.stackView);
        make.left.mas_offset(offset);
        make.right.mas_offset(-offset);
    }];
    
    offset = iPadValue(30, 15);
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(0);
        make.right.mas_offset(-0);
        make.top.equalTo(self.view).offset(offset);
    }];
    
    [self.joinQQView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([SettingTextAndArrowView height]);
    }];
    
    [self.joinTelegramView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([SettingTextAndArrowView height]);
    }];
    
    [self.versionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([SettingTextAndArrowView height]);
    }];
}

#pragma mark -- lazy init
- (UIView *)backView
{
    if(!_backView) {
        _backView = [UIView new];
        _backView.backgroundColor = UIColor.whiteColor;
        
        _backView.layer.cornerRadius = 10;
        _backView.layer.masksToBounds = YES;
    }
    
    return _backView;
}

- (UIStackView *)stackView
{
    if(!_stackView) {
        _stackView = [[UIStackView alloc]initWithArrangedSubviews:@[
            self.joinQQView,
            self.joinTelegramView,
            self.versionView,
        ]];
        
        _stackView.spacing = 0;
        _stackView.axis = UILayoutConstraintAxisVertical;
        
        _stackView.backgroundColor = UIColor.whiteColor;
    }
    
    return _stackView;
}

- (SettingTextAndArrowView *)joinQQView
{
    if(!_joinQQView) {
        _joinQQView = [[SettingTextAndArrowView alloc]initWithShowLine:YES];
    }
    
    return _joinQQView;
}

- (SettingTextView *)versionView
{
    if(!_versionView) {
        _versionView = [[SettingTextView alloc]initWithShowLine:NO];
    }
    
    return _versionView;
}

- (SettingTextAndArrowView *)joinTelegramView
{
    if(!_joinTelegramView) {
        _joinTelegramView = [[SettingTextAndArrowView alloc]initWithShowLine:YES];
    }
    
    return _joinTelegramView;
}

@end
