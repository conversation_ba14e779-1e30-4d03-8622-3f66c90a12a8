//
//  ToolbarConfigViewController.m
//  PPBrowser
//
//  Created by qingbin on 2025/3/26.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "ToolbarConfigViewController.h"

#import "Masonry.h"
#import "MaizyHeader.h"
#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "ReactiveCocoa.h"
#import "UIView+FrameHelper.h"
#import "UIViewController+Helper.h"
#import "NSObject+Helper.h"

#import "SettingSwitchView.h"
#import "SettingTextAndArrowView.h"
#import "SettingStepperView.h"
#import "SettingArrowView.h"
#import "SettingSwitchAndTextView.h"

#import "PPNotifications.h"

#import "PreferenceManager.h"
#import "SearchManager.h"

#import "UserAgentController.h"
#import "BrowserUtils.h"
#import "SearchViewController.h"
#import "BaseNavigationController.h"

#import "AppDelegate.h"

#import "PaddingNewLabel.h"
#import "SettingSegmentAndTitleView.h"
#import "ToolbarOrderSettingController.h"
#import "ToolbarGestureViewController.h"

@interface ToolbarConfigViewController ()

@property (nonatomic, strong) UIScrollView* scrollView;
//
@property (nonatomic, strong) UIStackView* stackView;

// 基本设置
@property (nonatomic, strong) PaddingNewLabel *basicSectionView;
// 设置底部按钮排序
@property (nonatomic, strong) SettingArrowView* toolbarSettingView;
// 设置工具栏按钮长按事件
@property (nonatomic, strong) SettingArrowView *toolbarGestureView;
// 开启前进后台手势
@property (nonatomic, strong) SettingSwitchAndTextView *enableSwipeNavigationView;
// 开启手势震动反馈
@property (nonatomic, strong) SettingSwitchView *enableGestureHapticsView;

// 地址栏
@property (nonatomic, strong) PaddingNewLabel *toolbarSectionView;
// 地址栏显示内容
@property (nonatomic, strong) SettingSegmentAndTitleView *topToolDisplayView;

@end

@implementation ToolbarConfigViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.title = NSLocalizedString(@"setting.toolbar", nil);
    
    [self addSubviews];
    [self defineLayout];
    [self updateWithModel];
    [self setupObservers];
    [self createCustomLeftBarButtonItem];
    
    [self applyTheme];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
    } else {
        self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    //只有当前的controller会触发一次, 其它已存在的controller走通知
    if(needReload) {
        //修改暗黑模式
        [self applyTheme];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}

- (void)updateWithModel
{
    BOOL enableSwipeNavigation = [[PreferenceManager shareInstance].items.enableSwipeNavigation boolValue];
    [self.enableSwipeNavigationView updateWithTitle:NSLocalizedString(@"toolbar.enableSwipeNavigation.text", nil)
                                             detail:NSLocalizedString(@"toolbarSetting.swipe.text", nil)
                                               isOn:enableSwipeNavigation];
    
    BOOL enableGestureHaptics = [[PreferenceManager shareInstance].items.enableGestureHaptics boolValue];
    [self.enableGestureHapticsView updateWithTitle:NSLocalizedString(@"toolbar.enableGestureHaptics.text", nil) isOn:enableGestureHaptics];
    
    AddressBarDisplayMode displayMode = [[PreferenceManager shareInstance].items.toptoolDisplayMode intValue];
    [self.topToolDisplayView updateWithSelectIndex:(int)displayMode];
}

- (void)setupObservers
{
    // 绑定事件
    @weakify(self)
    [self.toolbarSettingView setDidAction:^{
        @strongify(self)
        ToolbarOrderSettingController *vc = [ToolbarOrderSettingController new];
        [self.navigationController pushViewController:vc animated:YES];
    }];
    
    [self.toolbarGestureView setDidAction:^{
        @strongify(self)
        ToolbarGestureViewController *vc = [ToolbarGestureViewController new];
        [self.navigationController pushViewController:vc animated:YES];
    }];
    
    [self.topToolDisplayView setSelectIndexBlock:^(int index) {
        [PreferenceManager shareInstance].items.toptoolDisplayMode = @(index);
        [[PreferenceManager shareInstance] encode];
        
        //刷新网页
        [[NSNotificationCenter defaultCenter] postNotificationName:kReloadCurrentWebViewNotification object:nil];
    }];
    
    [self.enableSwipeNavigationView setDidSwithAction:^(BOOL isOn) {
        [PreferenceManager shareInstance].items.enableSwipeNavigation = @(isOn);
        [[PreferenceManager shareInstance] encode];
        
        //发送通知
        [[NSNotificationCenter defaultCenter] postNotificationName:kUpdateSwipeGesutreNotification object:nil];
    }];
    
    [self.enableGestureHapticsView setDidSwithAction:^(BOOL isOn) {
        [PreferenceManager shareInstance].items.enableGestureHaptics = @(isOn);
        [[PreferenceManager shareInstance] encode];
    }];
}

#pragma mark - layout

- (void)addSubviews
{
    [self.view addSubview:self.scrollView];
    [self.scrollView addSubview:self.stackView];
}

- (void)defineLayout
{
    float leftOffset = iPadValue(30, 15);
    float topOffset = iPadValue(30, 15);
    [self.scrollView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(leftOffset);
        make.right.mas_offset(-leftOffset);
        make.top.equalTo(self.view).offset(0);
        make.bottom.equalTo(self.view);
    }];
    
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.scrollView);
        make.bottom.equalTo(self.scrollView).offset(-topOffset);
        make.left.equalTo(self.view).offset(leftOffset);
        make.right.equalTo(self.view).offset(-leftOffset);
    }];
    
    [self.toolbarSettingView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([SettingArrowView height]);
    }];
    
    [self.toolbarGestureView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([SettingArrowView height]);
    }];
    
    [self.topToolDisplayView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(self.stackView);
    }];
    
    [self.enableGestureHapticsView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([SettingSwitchView height]);
    }];
}

#pragma mark - getters

- (UIStackView *)stackView
{
    if(!_stackView) {
        _stackView = [[UIStackView alloc]initWithArrangedSubviews:@[
            self.basicSectionView,
            self.toolbarSettingView,
            self.toolbarGestureView,
            self.enableSwipeNavigationView,
            self.enableGestureHapticsView,
            
            self.toolbarSectionView,
            self.topToolDisplayView,
        ]];
        
        _stackView.spacing = 0;
        _stackView.axis = UILayoutConstraintAxisVertical;
        
        _stackView.backgroundColor = UIColor.clearColor;
        
        _stackView.layer.cornerRadius = 10;
        _stackView.layer.masksToBounds = YES;
    }
    
    return _stackView;
}

- (UIScrollView *)scrollView
{
    if(!_scrollView) {
        _scrollView = [[UIScrollView alloc]init];
        _scrollView.showsVerticalScrollIndicator = NO;
        _scrollView.backgroundColor = UIColor.clearColor;
    }
    
    return _scrollView;
}

- (PaddingNewLabel *)basicSectionView
{
    if (!_basicSectionView) {
        _basicSectionView = [self _createSectionHeaderWithTitle:NSLocalizedString(@"webSetting.basic.title", nil)];
    }
    
    return _basicSectionView;
}

- (SettingArrowView *)toolbarSettingView
{
    if(!_toolbarSettingView) {
        _toolbarSettingView = [[SettingArrowView alloc]initWithShowLine:YES];
        [_toolbarSettingView updateWithTitle:NSLocalizedString(@"homeSetting.bottomtoolbar.text", nil)];
        
        //iPad隐藏该选项
        _toolbarSettingView.hidden = [BrowserUtils isiPad];
        
        if (_toolbarSettingView.hidden == false) {
            _toolbarSettingView.layer.cornerRadius = 10;
            _toolbarSettingView.layer.maskedCorners = UIRectCornerTopLeft | UIRectCornerTopRight;
        }
    }
    
    return _toolbarSettingView;
}

- (SettingArrowView *)toolbarGestureView
{
    if(!_toolbarGestureView) {
        _toolbarGestureView = [[SettingArrowView alloc]initWithShowLine:YES];
        [_toolbarGestureView updateWithTitle:NSLocalizedString(@"toolbarGesture.title", nil)];
                
        if (_toolbarSettingView.hidden) {
            _toolbarGestureView.layer.cornerRadius = 10;
            _toolbarGestureView.layer.maskedCorners = UIRectCornerTopLeft | UIRectCornerTopRight;
        }
    }
    
    return _toolbarGestureView;
}

- (SettingSwitchAndTextView *)enableSwipeNavigationView
{
    if(!_enableSwipeNavigationView) {
        _enableSwipeNavigationView = [[SettingSwitchAndTextView alloc] initWithShowLine:YES];
    }
    
    return _enableSwipeNavigationView;
}

- (SettingSwitchView *)enableGestureHapticsView
{
    if(!_enableGestureHapticsView) {
        _enableGestureHapticsView = [[SettingSwitchView alloc] initWithShowLine:NO];
        
        _enableGestureHapticsView.layer.cornerRadius = 10;
        _enableGestureHapticsView.layer.maskedCorners = UIRectCornerBottomLeft | UIRectCornerBottomRight;
    }
    
    return _enableGestureHapticsView;
}

- (SettingSegmentAndTitleView *)topToolDisplayView
{
    if(!_topToolDisplayView) {
        _topToolDisplayView = [[SettingSegmentAndTitleView alloc]initWithTitle:NSLocalizedString(@"toptool.display.title", nil) showLine:NO segments:@[
            NSLocalizedString(@"toptool.display.item.url", nil),
            NSLocalizedString(@"toptool.display.item.title", nil),
            NSLocalizedString(@"toptool.display.item.domain", nil)
        ]];
        
        _topToolDisplayView.layer.cornerRadius = 10;
        _topToolDisplayView.layer.masksToBounds = YES;
    }
    
    return _topToolDisplayView;
}

- (PaddingNewLabel *)toolbarSectionView
{
    if (!_toolbarSectionView) {
        _toolbarSectionView = [self _createSectionHeaderWithTitle:NSLocalizedString(@"toptool.section.title", nil)];
    }
    
    return _toolbarSectionView;
}

- (PaddingNewLabel *)_createSectionHeaderWithTitle:(NSString *)title
{
    PaddingNewLabel* label = [[PaddingNewLabel alloc] initWithFrame:CGRectMake(0, 0, kScreenWidth, iPadValue(70, 48))];
    label.text = title;
    label.font = [UIFont systemFontOfSize:iPadValue(18, 14)];
    label.textColor = [UIColor colorWithHexString:@"#6B7280"];
    label.edgeInsets = UIEdgeInsetsMake(iPadValue(30, 20), iPadValue(30, 15), iPadValue(10, 8), 0);
    label.textAlignment = NSTextAlignmentLeft;
    return label;
}

@end
