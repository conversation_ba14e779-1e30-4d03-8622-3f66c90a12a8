//
//  ToolbarGestureEmptyStateCell.h
//  PPBrowser
//
//  Created by qingbin on 2025/3/27.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface ToolbarGestureEmptyStateCell : UITableViewCell

// 空状态容器视图
@property (nonatomic, strong) UIView *emptyContainer;
// 图标视图
@property (nonatomic, strong) UIImageView *iconView;
// 主标题标签
@property (nonatomic, strong) UILabel *titleLabel;
// 副标题标签
//@property (nonatomic, strong) UILabel *subtitleLabel;

// 配置空状态单元格
- (void)setupUI;
// 应用主题
- (void)applyTheme:(BOOL)isDarkTheme;

@end

NS_ASSUME_NONNULL_END
