//
//  ToolbarGestureHeader.m
//  PPBrowser
//
//  Created by qingbin on 2025/3/27.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "ToolbarGestureHeader.h"

#import "Masonry.h"
#import "MaizyHeader.h"
#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "ReactiveCocoa.h"
#import "NSObject+Helper.h"
#import "ThemeProtocol.h"
#import "BrowserUtils.h"

@interface ToolbarGestureHeader()

@property (nonatomic, strong) UIStackView *toolbarStackView;
@property (nonatomic, strong) NSMutableArray<UIView *> *toolbarButtons;
@property (nonatomic, strong) NSArray<NSString *> *buttonIcons;
@property (nonatomic, strong) NSArray<NSNumber *> *buttonTypes;
@property (nonatomic, assign) ToolbarGroup selectedGroup;
//
@property (nonatomic, assign) ToolbarOption option;

@end

@implementation ToolbarGestureHeader

- (instancetype)init
{
    self = [super init];
    
    if(self) {
        self.option = [[PreferenceManager shareInstance].items.toolBarOption intValue];
        [self updateWithModel];
        
        self.selectedGroup = ToolbarGroupBack; // 默认选中后退按钮
        
        [self addSubviews];
        [self defineLayout];
        [self setupToolbarButtons];
        [self applyTheme];
    }
    
    return self;
}

- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    
    if (isDarkTheme) {
        // 工具栏背景
        self.backgroundColor = [UIColor colorWithHexString:@"#1c1c1e"];
        self.toolbarStackView.backgroundColor = [UIColor colorWithHexString:@"#1c1c1e"];
        
        // 更新工具栏按钮颜色
        for (UIView *buttonContainer in self.toolbarButtons) {
            UIView *iconBackground = [buttonContainer viewWithTag:100];
            iconBackground.backgroundColor = [UIColor colorWithHexString:@"#2c2c2e"];
        }
    } else {
        // 工具栏背景
        self.backgroundColor = UIColor.whiteColor;
        self.toolbarStackView.backgroundColor = [UIColor whiteColor];
        
        // 更新工具栏按钮颜色
        for (UIView *buttonContainer in self.toolbarButtons) {
            UIView *iconBackground = [buttonContainer viewWithTag:100];
            iconBackground.backgroundColor = [UIColor colorWithHexString:@"#f2f2f7"];
        }
    }
    
    // 更新选中状态
    [self updateSelectedToolbarGroup:self.selectedGroup];
}

- (void)updateWithModel
{
    if ([BrowserUtils isiPhone] && ![[BrowserUtils shareInstance] isLandscape]) {
        // iPhone竖屏 - 使用半模态弹窗
        if(self.option == ToolbarOptionDefault) {
            self.buttonIcons = @[@"back", @"forward", @"plus", @"layer_group", @"menu"];
            self.buttonTypes = @[@(ToolbarGroupBack), @(ToolbarGroupForward), @(ToolbarGroupNew), @(ToolbarGroupTabs), @(ToolbarGroupFeatures)];
        } else if(self.option == ToolbarOption1) {
            self.buttonIcons = @[@"back", @"forward", @"menu", @"layer_group", @"menu_home_icon"];
            self.buttonTypes = @[@(ToolbarGroupBack), @(ToolbarGroupForward), @(ToolbarGroupFeatures), @(ToolbarGroupTabs), @(ToolbarGroupHome)];
        } else if(self.option == ToolbarOption2) {
            self.buttonIcons = @[@"back", @"forward", @"menu_home_icon", @"layer_group", @"menu"];
            self.buttonTypes = @[@(ToolbarGroupBack), @(ToolbarGroupForward), @(ToolbarGroupHome), @(ToolbarGroupTabs), @(ToolbarGroupFeatures)];
        } else if(self.option == ToolbarOption3) {
            self.buttonIcons = @[@"back", @"forward", @"layer_group", @"menu", @"menu_home_icon"];
            self.buttonTypes = @[@(ToolbarGroupBack), @(ToolbarGroupForward), @(ToolbarGroupTabs), @(ToolbarGroupFeatures), @(ToolbarGroupHome)];
        } else if(self.option == ToolbarOption4) {
            self.buttonIcons = @[@"back", @"forward", @"add", @"menu", @"layer_group"];
            self.buttonTypes = @[@(ToolbarGroupBack), @(ToolbarGroupForward), @(ToolbarGroupNew), @(ToolbarGroupFeatures), @(ToolbarGroupTabs)];
        }
    } else {
        // iPad / iPhone横屏 - 使用普通Push
        self.buttonIcons = @[@"back", @"forward", @"plus", @"layer_group", @"menu", @"menu_home_icon"];
        self.buttonTypes = @[@(ToolbarGroupBack), @(ToolbarGroupForward), @(ToolbarGroupNew), @(ToolbarGroupTabs), @(ToolbarGroupFeatures), @(ToolbarGroupHome)];
    }
}

// 更新工具栏按钮
- (void)setupToolbarButtons
{
    // 清除现有按钮
    for (UIView *button in self.toolbarButtons) {
        [button removeFromSuperview];
    }
    [self.toolbarButtons removeAllObjects];
    [self.toolbarStackView.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
    
    // 创建新的工具栏按钮
    for (NSInteger i = 0; i < self.buttonIcons.count; i++) {
        // 创建按钮容器
        UIView *buttonContainer = [[UIView alloc] init];
        buttonContainer.backgroundColor = [UIColor clearColor];
        buttonContainer.tag = [self.buttonTypes[i] intValue];
        
        // 创建图标背景
        UIView *iconBackground = [[UIView alloc] init];
        iconBackground.backgroundColor = [UIColor colorWithHexString:@"#f2f2f7"];
        iconBackground.layer.cornerRadius = 12;
        iconBackground.tag = 100;
        
        // 创建图标
        UIImageView *iconView = [[UIImageView alloc] init];
        iconView.contentMode = UIViewContentModeScaleAspectFit;
        iconView.tintColor = [UIColor colorWithHexString:@"#007aff"];
        iconView.tag = 101;
        
        // 使用 FA 图标名称
        NSString *iconName = self.buttonIcons[i];
        UIImage *image = [UIImage imageNamed:iconName];
        if (image) {
            iconView.image = [image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        }
        
        // 添加到容器
        [iconBackground addSubview:iconView];
        [buttonContainer addSubview:iconBackground];
        
        // 设置布局
        float iconSize = iPadValue(30, 20);
        [iconView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.center.equalTo(iconBackground);
            make.size.mas_equalTo(iconSize);
        }];
        
        float iconBackgroundSize = iPadValue(50, 44);
        [iconBackground mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.equalTo(buttonContainer);
            make.size.mas_equalTo(iconBackgroundSize);
        }];
        
        // 添加点击事件
        @weakify(self)
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] init];
        [buttonContainer addGestureRecognizer:tap];
        [[tap rac_gestureSignal] subscribeNext:^(id x) {
            @strongify(self)
            if (self.didSelectToolbarGroupAction) {
                self.didSelectToolbarGroupAction((ToolbarGroup)buttonContainer.tag);
                [self updateSelectedToolbarGroup:(ToolbarGroup)buttonContainer.tag];
            }
        }];
        
        [self.toolbarStackView addArrangedSubview:buttonContainer];
        [self.toolbarButtons addObject:buttonContainer];
    }
}

// 更新选中的工具栏按钮
- (void)updateSelectedToolbarGroup:(ToolbarGroup)group
{
    self.selectedGroup = group;
    
    for (UIView *buttonContainer in self.toolbarButtons) {
        if (buttonContainer.tag == group) {
            // 选中状态
            UIView *iconBackground = [buttonContainer viewWithTag:100];
            iconBackground.backgroundColor = [UIColor colorWithHexString:@"#e1f0ff"];
            
            UIImageView *iconView = [buttonContainer viewWithTag:101];
            iconView.tintColor = [UIColor colorWithHexString:@"#007aff"];
            
            // 添加阴影效果
//            iconBackground.layer.shadowColor = [UIColor colorWithHexString:@"#007aff"].CGColor;
//            iconBackground.layer.shadowOffset = CGSizeMake(0, 2);
//            iconBackground.layer.shadowOpacity = 0.2;
//            iconBackground.layer.shadowRadius = 4;
        } else {
            // 未选中状态
            UIView *iconBackground = [buttonContainer viewWithTag:100];
            BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
            iconBackground.backgroundColor = [UIColor colorWithHexString:isDarkTheme ? @"#2c2c2e" : @"#f2f2f7"];
            
            UIImageView *iconView = [buttonContainer viewWithTag:101];
            iconView.tintColor = [UIColor colorWithHexString:@"#007aff"];
            
            // 移除阴影
//            iconBackground.layer.shadowOpacity = 0;
        }
    }
}

#pragma mark - layout

- (void)addSubviews
{
    [self addSubview:self.toolbarStackView];
}

- (void)defineLayout
{
    float topPadding = iPadValue(15, 10);
    float leftPadding = iPadValue(20, 10);
    [self.toolbarStackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self).insets(UIEdgeInsetsMake(topPadding, leftPadding, topPadding, leftPadding));
    }];
}

#pragma mark - getters

- (UIStackView *)toolbarStackView
{
    if (!_toolbarStackView) {
        _toolbarStackView = [[UIStackView alloc] init];
        _toolbarStackView.axis = UILayoutConstraintAxisHorizontal;
        _toolbarStackView.distribution = UIStackViewDistributionEqualSpacing;
        _toolbarStackView.alignment = UIStackViewAlignmentCenter;
        _toolbarStackView.backgroundColor = [UIColor whiteColor];
        _toolbarStackView.layer.cornerRadius = iPadValue(10, 8);
        _toolbarStackView.layer.masksToBounds = YES;
    }
    
    return _toolbarStackView;
}

- (NSMutableArray<UIView *> *)toolbarButtons
{
    if (!_toolbarButtons) {
        _toolbarButtons = [NSMutableArray array];
    }
    
    return _toolbarButtons;
}

@end
