//
//  ToolbarGestureHeader.h
//  PPBrowser
//
//  Created by qingbin on 2025/3/27.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "PPEnums.h"

NS_ASSUME_NONNULL_BEGIN

@interface ToolbarGestureHeader : UIView

// 更新选中的工具栏类型
- (void)updateSelectedToolbarGroup:(ToolbarGroup)group;

// 点击工具栏按钮回调
@property (nonatomic, copy) void (^didSelectToolbarGroupAction)(ToolbarGroup group);

@end

NS_ASSUME_NONNULL_END
