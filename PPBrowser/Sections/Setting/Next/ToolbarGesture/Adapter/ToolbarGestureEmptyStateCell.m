//
//  ToolbarGestureEmptyStateCell.m
//  PPBrowser
//
//  Created by qingbin on 2025/3/27.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "ToolbarGestureEmptyStateCell.h"
#import "Masonry.h"
#import "UIColor+Helper.h"

@implementation ToolbarGestureEmptyStateCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        self.backgroundColor = [UIColor clearColor];
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    // 创建空状态容器
    self.emptyContainer = [[UIView alloc] init];
    self.emptyContainer.backgroundColor = [UIColor clearColor];
    [self.contentView addSubview:self.emptyContainer];
    
    // 创建图标
    self.iconView = [[UIImageView alloc] init];
    self.iconView.contentMode = UIViewContentModeScaleAspectFit;
    self.iconView.tintColor = [UIColor colorWithHexString:@"#8e8e93"];
    
    // 使用 hand-point-up 图标
    UIImage *image = [UIImage imageNamed:@"gesture-hand-point-up"];
    if (image) {
        self.iconView.image = [image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
    }
    [self.emptyContainer addSubview:self.iconView];
    
    // 创建主标题
    self.titleLabel = [[UILabel alloc] init];
    self.titleLabel.text = NSLocalizedString(@"toolbar.gesture.empty.title", nil);
    self.titleLabel.font = [UIFont systemFontOfSize:15];
    self.titleLabel.textColor = [UIColor colorWithHexString:@"#8e8e93"];
    self.titleLabel.textAlignment = NSTextAlignmentCenter;
    [self.emptyContainer addSubview:self.titleLabel];
    
    // 创建副标题
//    self.subtitleLabel = [[UILabel alloc] init];
//    self.subtitleLabel.text = @"点击下方按钮添加手势";
//    self.subtitleLabel.font = [UIFont systemFontOfSize:12];
//    self.subtitleLabel.textColor = [UIColor colorWithHexString:@"#8e8e93"];
//    self.subtitleLabel.textAlignment = NSTextAlignmentCenter;
//    [self.emptyContainer addSubview:self.subtitleLabel];
    
    // 设置布局
    [self.emptyContainer mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.contentView).insets(UIEdgeInsetsMake(32, 16, 32, 16));
    }];
    
    [self.iconView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.emptyContainer);
        make.centerX.equalTo(self.emptyContainer);
        make.size.mas_equalTo(CGSizeMake(40, 40));
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.iconView.mas_bottom).offset(12);
        make.left.right.equalTo(self.emptyContainer);
    }];
    
//    [self.subtitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.top.equalTo(self.titleLabel.mas_bottom).offset(8);
//        make.left.right.equalTo(self.emptyContainer);
//        make.bottom.equalTo(self.emptyContainer);
//    }];
}

- (void)applyTheme:(BOOL)isDarkTheme {
    if (isDarkTheme) {
        self.iconView.tintColor = [UIColor colorWithHexString:@"#636366"];
        self.titleLabel.textColor = [UIColor colorWithHexString:@"#8e8e93"];
//        self.subtitleLabel.textColor = [UIColor colorWithHexString:@"#8e8e93"];
    } else {
        self.iconView.tintColor = [UIColor colorWithHexString:@"#8e8e93"];
        self.titleLabel.textColor = [UIColor colorWithHexString:@"#8e8e93"];
//        self.subtitleLabel.textColor = [UIColor colorWithHexString:@"#8e8e93"];
        
        self.contentView.backgroundColor = UIColor.whiteColor;
    }
}

@end
