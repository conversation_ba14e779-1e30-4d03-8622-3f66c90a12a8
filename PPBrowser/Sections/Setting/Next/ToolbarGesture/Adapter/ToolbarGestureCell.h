//
//  ToolbarGestureCell.h
//  PPBrowser
//
//  Created by qingbin on 2025/3/27.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "ToolbarGestureModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface ToolbarGestureCell : UITableViewCell

// 更新 cell 数据
- (void)updateWithModel:(ToolbarGestureModel *)model;

// 删除按钮回调
@property (nonatomic, copy) void (^didDeleteAction)(ToolbarGestureModel *model);

@end

NS_ASSUME_NONNULL_END
