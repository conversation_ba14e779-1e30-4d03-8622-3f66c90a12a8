//
//  ToolbarGestureAddGestureCell.m
//  PPBrowser
//
//  Created by qingbin on 2025/3/27.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "ToolbarGestureAddGestureCell.h"
#import "Masonry.h"
#import "MaizyHeader.h"
#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "ReactiveCocoa.h"
#import "NSObject+Helper.h"
#import "ThemeProtocol.h"

#import "CustomTitleAndImageView.h"

@interface ToolbarGestureAddGestureCell ()
// 添加按钮
@property (nonatomic, strong) CustomTitleAndImageView *addButton;

@end

@implementation ToolbarGestureAddGestureCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        [self addSubviews];
        [self defineLayout];
        [self handleEvents];
    }
    return self;
}

- (void)updateWithModel
{
    [self applyTheme];
}

- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    
    if (isDarkTheme) {
        [self.addButton updateWith:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            imageView.tintColor = [UIColor colorWithHexString:@"#0a84ff"];
            titleLabel.textColor = [UIColor colorWithHexString:@"#0a84ff"];
        }];
    } else {
        [self.addButton updateWith:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            imageView.tintColor = [UIColor colorWithHexString:@"#007aff"];
            titleLabel.textColor = [UIColor colorWithHexString:@"#007aff"];
        }];
        
        self.contentView.backgroundColor = UIColor.whiteColor;
        self.backgroundColor = UIColor.whiteColor;
    }
}

#pragma mark - Handle Events

- (void)handleEvents
{
    @weakify(self)
    [self.addButton setTapAction:^{
        @strongify(self)
        if (self.addBlock) {
            self.addBlock();
        }
    }];
}

#pragma mark - layout

- (void)addSubviews
{
    [self.contentView addSubview:self.addButton];
}

- (void)defineLayout
{
    [self.addButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.bottom.equalTo(self.contentView);
        make.centerX.equalTo(self.contentView);
        //决定高度
        make.height.mas_equalTo(50);
    }];
}

#pragma mark - getters

- (CustomTitleAndImageView *)addButton
{
    if (!_addButton) {
        _addButton = [[CustomTitleAndImageView alloc] initWithLayout:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            titleLabel.text = NSLocalizedString(@"toolbar.gesture.add.title", nil);
            titleLabel.textColor = [UIColor colorWithHexString:@"#007aff"];
            titleLabel.font = [UIFont systemFontOfSize:15 weight:UIFontWeightMedium];
            
            imageView.image = [[UIImage imageNamed:@"gesture_plus"] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
            imageView.tintColor = [UIColor colorWithHexString:@"#007aff"];
            
            [imageView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.equalTo(view);
                make.centerY.equalTo(view);
                make.size.mas_equalTo(16);
            }];
            
            [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
                make.right.equalTo(view);
                make.centerY.equalTo(view);
                make.left.equalTo(imageView.mas_right).offset(10);
            }];
        }];
    }
    
    return _addButton;
}

@end
