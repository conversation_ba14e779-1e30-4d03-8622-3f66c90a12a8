//
//  ToolbarGestureCell.m
//  PPBrowser
//
//  Created by qingbin on 2025/3/27.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "ToolbarGestureCell.h"

#import "Masonry.h"
#import "MaizyHeader.h"
#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "ReactiveCocoa.h"
#import "NSObject+Helper.h"
#import "ThemeProtocol.h"
#import "BrowserUtils.h"

@interface ToolbarGestureCell()

// 手势图标容器
@property (nonatomic, strong) UIView *iconContainer;
// 手势图标
@property (nonatomic, strong) UIImageView *iconImageView;
// 手势名称
@property (nonatomic, strong) UILabel *nameLabel;
// 删除按钮容器
@property (nonatomic, strong) UIView *deleteButtonContainer;
// 删除图标
@property (nonatomic, strong) UIImageView *deleteIconImageView;
// 分隔线
@property (nonatomic, strong) UIView *separatorLine;
//
@property (nonatomic, strong) ToolbarGestureModel *model;
@end

@implementation ToolbarGestureCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        [self setupUI];
        [self addSubviews];
        [self defineLayout];
        [self setupObservers];
        [self applyTheme];
    }
    return self;
}

- (void)setupUI {
    self.selectionStyle = UITableViewCellSelectionStyleNone;
    self.backgroundColor = [UIColor clearColor];
    self.contentView.backgroundColor = [UIColor clearColor];
}

- (void)addSubviews {
    [self.contentView addSubview:self.iconContainer];
    [self.iconContainer addSubview:self.iconImageView];
    [self.contentView addSubview:self.nameLabel];
    [self.contentView addSubview:self.deleteButtonContainer];
    [self.deleteButtonContainer addSubview:self.deleteIconImageView];
    [self.contentView addSubview:self.separatorLine];
}

- (void)defineLayout {
    // 图标容器
    float iconContainerSize = iPadValue(44, 36);
    float leftOffset = iPadValue(20, 15);
    [self.iconContainer mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).offset(leftOffset);
        make.centerY.equalTo(self.contentView);
        make.size.mas_equalTo(iconContainerSize);
    }];
    
    // 图标
    float iconSize = iPadValue(25, 20);
    [self.iconImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.iconContainer);
        make.size.mas_equalTo(iconSize);
    }];
    
    // 名称标签
    float leftNameOffset = iPadValue(20, 12);
    [self.nameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.iconContainer.mas_right).offset(leftNameOffset);
        make.centerY.equalTo(self.contentView);
        make.right.lessThanOrEqualTo(self.deleteButtonContainer.mas_left).offset(-leftNameOffset);
    }];
    
    // 删除按钮容器
    [self.deleteButtonContainer mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.contentView).offset(-leftOffset);
        make.centerY.equalTo(self.contentView);
        make.size.mas_equalTo(iPadValue(40, 32));
    }];
    
    // 删除图标
    [self.deleteIconImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.deleteButtonContainer);
        make.size.mas_equalTo(iPadValue(20, 16));
    }];
    
    // 分隔线
    [self.separatorLine mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.nameLabel);
        make.right.equalTo(self.contentView).offset(-leftOffset);
        make.bottom.equalTo(self.contentView);
        make.height.mas_equalTo(0.5);
    }];
}

- (void)setupObservers {
    // 添加删除按钮点击事件
    @weakify(self)
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] init];
    [self.deleteButtonContainer addGestureRecognizer:tap];
    [[tap rac_gestureSignal] subscribeNext:^(id x) {
        @strongify(self)
        if (self.didDeleteAction) {
            self.didDeleteAction(self.model);
        }
    }];
}

- (void)applyTheme {
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    
    if (isDarkTheme) {
        self.iconContainer.backgroundColor = [UIColor colorWithHexString:@"#2c2c2e"];
        self.nameLabel.textColor = [UIColor whiteColor];
        self.deleteButtonContainer.backgroundColor = [UIColor colorWithHexString:@"#3a3a3c"];
        self.separatorLine.backgroundColor = [UIColor colorWithHexString:@"#2c2c2e"];
    } else {
        self.iconContainer.backgroundColor = [UIColor colorWithHexString:@"#f2f2f7"];
        self.nameLabel.textColor = [UIColor colorWithHexString:@"#333333"];
        self.deleteButtonContainer.backgroundColor = [UIColor colorWithHexString:@"#f2f2f7"];
        self.separatorLine.backgroundColor = [UIColor colorWithHexString:@"#e5e5ea"];
        
        self.contentView.backgroundColor = UIColor.whiteColor;
    }
}

- (void)updateWithModel:(ToolbarGestureModel *)model {
    self.model = model;
    self.nameLabel.text = [ToolbarGestureModel titleForToolbarLongPressAction:model.type];
    
    // 根据类型设置图标
    NSString *iconName = [ToolbarGestureModel iconNameForActionType:model.type];
    UIImage *image = [UIImage imageNamed:iconName];
    if (image) {
        self.iconImageView.image = [image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
    }
}

#pragma mark - Getters

- (UIView *)iconContainer {
    if (!_iconContainer) {
        _iconContainer = [[UIView alloc] init];
        _iconContainer.backgroundColor = [UIColor colorWithHexString:@"#f2f2f7"];
        _iconContainer.layer.cornerRadius = 10;
    }
    return _iconContainer;
}

- (UIImageView *)iconImageView {
    if (!_iconImageView) {
        _iconImageView = [[UIImageView alloc] init];
        _iconImageView.contentMode = UIViewContentModeScaleAspectFit;
        _iconImageView.tintColor = [UIColor colorWithHexString:@"#007aff"];
    }
    return _iconImageView;
}

- (UILabel *)nameLabel {
    if (!_nameLabel) {
        _nameLabel = [[UILabel alloc] init];
        _nameLabel.font = [UIFont systemFontOfSize:iPadValue(18, 15) weight:UIFontWeightMedium];
        _nameLabel.textColor = [UIColor colorWithHexString:@"#333333"];
    }
    return _nameLabel;
}

- (UIView *)deleteButtonContainer {
    if (!_deleteButtonContainer) {
        _deleteButtonContainer = [[UIView alloc] init];
        _deleteButtonContainer.backgroundColor = [UIColor colorWithHexString:@"#f2f2f7"];
        _deleteButtonContainer.layer.cornerRadius = iPadValue(20, 16);
    }
    return _deleteButtonContainer;
}

- (UIImageView *)deleteIconImageView {
    if (!_deleteIconImageView) {
        _deleteIconImageView = [[UIImageView alloc] init];
        _deleteIconImageView.contentMode = UIViewContentModeScaleAspectFit;
        _deleteIconImageView.tintColor = [UIColor colorWithHexString:@"#ff3b30"]; // 红色
        
        // 使用 trash-can 图标
        UIImage *image = [UIImage imageNamed:@"gesture-trash-can"];
        if (image) {
            _deleteIconImageView.image = [image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        }
    }
    return _deleteIconImageView;
}

- (UIView *)separatorLine {
    if (!_separatorLine) {
        _separatorLine = [[UIView alloc] init];
        _separatorLine.backgroundColor = [UIColor colorWithHexString:@"#e5e5ea"];
    }
    return _separatorLine;
}

@end
