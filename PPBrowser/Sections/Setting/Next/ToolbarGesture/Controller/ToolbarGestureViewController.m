//
//  ToolbarGestureViewController.m
//  PPBrowser
//
//  Created by qingbin on 2025/3/27.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "ToolbarGestureViewController.h"

#import "Masonry.h"
#import "MaizyHeader.h"
#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "ReactiveCocoa.h"
#import "UIView+FrameHelper.h"
#import "UIViewController+Helper.h"
#import "NSObject+Helper.h"

#import "SettingSwitchView.h"
#import "SettingTextAndArrowView.h"
#import "SettingStepperView.h"
#import "SettingArrowView.h"
#import "SettingSwitchAndTextView.h"

#import "PPNotifications.h"

#import "PreferenceManager.h"
#import "SearchManager.h"

#import "UserAgentController.h"
#import "BrowserUtils.h"
#import "SearchViewController.h"
#import "BaseNavigationController.h"

#import "AppDelegate.h"

#import "PaddingNewLabel.h"
#import "SettingSegmentAndTitleView.h"
#import "ToolbarGestureHeader.h"
#import "ToolbarGestureCell.h"
#import "ToolbarGestureModel.h"
#import "ThemeProtocol.h"

#import "ToolbarGestureEmptyStateCell.h"
#import "ToolbarGestureAddGestureCell.h"
#import "DatabaseUnit+ToolbarGesture.h"
#import "PPEnums.h"
#import "UIAlertController+SafePresentation.h"

// 添加手势 Cell 标识符
static NSString *const kAddGestureCellIdentifier = @"AddGestureCell";
// 手势 Cell 标识符
static NSString *const kGestureCellIdentifier = @"GestureCell";
// 空状态 Cell 标识符
static NSString *const kEmptyStateCellIdentifier = @"EmptyStateCell";

@interface ToolbarGestureViewController () <UITableViewDelegate, UITableViewDataSource, UITableViewDragDelegate, UITableViewDropDelegate>

// 滚动视图
@property (nonatomic, strong) UIScrollView *scrollView;
// 主容器视图
@property (nonatomic, strong) UIView *containerView;
// 工具栏头部视图
@property (nonatomic, strong) ToolbarGestureHeader *toolbarHeader;
// 描述标签
@property (nonatomic, strong) UILabel *descriptionLabel;
// 分组标题标签
@property (nonatomic, strong) UILabel *sectionTitleLabel;
// 手势列表
@property (nonatomic, strong) UITableView *tableView;
// 手势数据数组（按工具栏按钮分组）
@property (nonatomic, strong) NSMutableDictionary<NSNumber *, NSMutableArray<ToolbarGestureModel *> *> *gestureData;
// 当前选中的工具栏组
@property (nonatomic, assign) ToolbarGroup selectedGroup;

@end

@implementation ToolbarGestureViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.title = NSLocalizedString(@"toolbarGesture.title", nil);
    
    [self addSubviews];
    [self defineLayout];
    [self setupObservers];
    [self setupDragAndDrop];
    [self loadData];
    [self createCustomLeftBarButtonItem];
    
    [self applyTheme];
}

#pragma mark -- 夜间模式
- (void)applyTheme {
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if (isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.containerView.backgroundColor = [UIColor clearColor];
        self.descriptionLabel.textColor = [UIColor colorWithHexString:@"#8e8e93"];
        self.sectionTitleLabel.textColor = [UIColor colorWithHexString:@"#8e8e93"];
//        self.tableView.backgroundColor = [UIColor colorWithHexString:@"#1c1c1e"];
        self.tableView.backgroundColor = UIColor.clearColor;
        self.tableView.separatorColor = [UIColor colorWithHexString:@"#2c2c2e"];
    } else {
        self.view.backgroundColor = [UIColor colorWithHexString:@"#f8f9fa"];
        self.containerView.backgroundColor = [UIColor clearColor];
        self.descriptionLabel.textColor = [UIColor colorWithHexString:@"#8e8e93"];
        self.sectionTitleLabel.textColor = [UIColor colorWithHexString:@"#8e8e93"];
//        self.tableView.backgroundColor = [UIColor colorWithHexString:@"#ffffff"];
        self.tableView.backgroundColor = UIColor.clearColor;
        self.tableView.separatorColor = [UIColor colorWithHexString:@"#e5e5ea"];
    }
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload {
    //只有当前的controller会触发一次, 其它已存在的controller走通知
    if (needReload) {
        //修改暗黑模式
        [self applyTheme];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification {
    [self applyTheme];
}

#pragma mark -- 数据处理

// 设置拖拽排序
- (void)setupDragAndDrop {
    if (@available(iOS 11.0, *)) {
        self.tableView.dragDelegate = self;
        self.tableView.dropDelegate = self;
        self.tableView.dragInteractionEnabled = YES;
    }
}

// 加载手势数据
- (void)loadData {
    // 初始化数据
    self.gestureData = [NSMutableDictionary dictionary];
    for (int i = ToolbarGroupBack; i <= ToolbarGroupHome; i++) {
        self.gestureData[@(i)] = [NSMutableArray array];
    }
    
    // 设置初始选中分组
    self.selectedGroup = ToolbarGroupBack;
    [self.toolbarHeader updateSelectedToolbarGroup:self.selectedGroup];
//    [self updateSectionTitle];
    
    // 从数据库加载所有手势数据
    DatabaseUnit *unit = [DatabaseUnit queryAllToolbarGestures];
    @weakify(self)
    [unit setCompleteBlock:^(NSArray<ToolbarGestureModel *> *result, BOOL success) {
        @strongify(self)
        if (success) {
            // 根据 groupType 分组存储手势
            for (ToolbarGestureModel *gesture in result) {
                NSMutableArray *groupGestures = self.gestureData[@(gesture.groupType)];
                if (groupGestures) {
                    [groupGestures addObject:gesture];
                }
            }
            
            // 更新表格高度
            [self updateTableViewHeight];
            
            // 刷新表格
            [self.tableView reloadData];
        }
    }];
    
    DB_EXEC(unit);
}

// 更新表格视图高度
- (void)updateTableViewHeight {
    NSMutableArray *gestures = self.gestureData[@(self.selectedGroup)];
    CGFloat height = 0;
    
    if (gestures.count == 0) {
        // 空状态 + 添加按钮
        height = 140 + 50;
    } else {
        // 手势列表 + 添加按钮
        height = gestures.count * iPadValue(80, 60) + 50;
    }
    
    // 更新高度约束
    [self.tableView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(height);
    }];
}

// 添加新手势
- (void)addNewGesture {
    // 创建操作列表弹窗
    UIAlertController *actionSheet = [UIAlertController alertControllerWithTitle:NSLocalizedString(@"toolbar.gesture.section.title", nil)
                                                                         message:nil
                                                                  preferredStyle:UIAlertControllerStyleActionSheet];
    
    // 添加所有可能的操作类型，根据PPEnums.h中的ToolbarLongPressAction枚举
    NSMutableArray* actionTypes = [NSMutableArray array];
    // 增加类型的话，这里的迭代最大数要更新
    for (int i = ToolbarLongPressActionGoHome; i <= ToolbarLongPressActionOpenUseragent; i++) {
        [actionTypes addObject:@(i)];
    }
    
    @weakify(self)
    for (NSNumber *type in actionTypes) {
        ToolbarLongPressAction actionType = [type intValue];
        NSString *title = [ToolbarGestureModel titleForToolbarLongPressAction:actionType];
        
        UIAlertAction *action = [UIAlertAction actionWithTitle:title style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            @strongify(self)
            // 创建新的手势模型
            ToolbarGestureModel *newGesture = [[ToolbarGestureModel alloc] init];
            newGesture.uuid = [[NSUUID UUID] UUIDString];
            newGesture.type = actionType;
            newGesture.groupType = self.selectedGroup;
            
            // 设置排序位置
            NSMutableArray *groupGestures = self.gestureData[@(self.selectedGroup)];
            newGesture.position = (int)groupGestures.count;
            
            // 保存到数据库
            [self saveGestureToDatabase:newGesture completion:^(BOOL success) {
                if (success) {
                    // 添加到内存中
                    [groupGestures addObject:newGesture];
                    
                    // 更新表格高度
                    [self updateTableViewHeight];
                    
                    // 刷新表格
                    [self.tableView reloadData];
                }
            }];
        }];
        
        [actionSheet addAction:action];
    }
    
    // 添加取消按钮
    UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"common.cancel", nil) style:UIAlertActionStyleCancel handler:nil];
    [actionSheet addAction:cancelAction];
    
    // 适配iPad
//    UIPopoverPresentationController *popover = actionSheet.popoverPresentationController;
//    if (popover) {
//        NSIndexPath *selectedPath;
//        if (self.gestureData[@(self.selectedGroup)].count == 0) {
//            selectedPath = [NSIndexPath indexPathForRow:1 inSection:0]; // 添加按钮
//        } else {
//            selectedPath = [NSIndexPath indexPathForRow:self.gestureData[@(self.selectedGroup)].count inSection:0]; // 添加按钮
//        }
//        
//        UITableViewCell *cell = [self.tableView cellForRowAtIndexPath:selectedPath];
//        popover.sourceView = cell;
//        popover.sourceRect = cell.bounds;
//        popover.permittedArrowDirections = UIPopoverArrowDirectionAny;
//    }
    
    NSIndexPath *selectedPath;
    if (self.gestureData[@(self.selectedGroup)].count == 0) {
        selectedPath = [NSIndexPath indexPathForRow:1 inSection:0]; // 添加按钮
    } else {
        selectedPath = [NSIndexPath indexPathForRow:self.gestureData[@(self.selectedGroup)].count inSection:0]; // 添加按钮
    }
    
    UITableViewCell *cell = [self.tableView cellForRowAtIndexPath:selectedPath];
    // 显示弹窗
//    [self presentViewController:actionSheet animated:YES completion:nil];
    //v2.6.8, 统一present方法，防止崩溃
    [actionSheet presentSafelyFromViewController:self sourceView:cell sourceRect:cell.bounds];
}

// 保存手势到数据库
- (void)saveGestureToDatabase:(ToolbarGestureModel *)gesture completion:(void(^)(BOOL success))completion {
    DatabaseUnit *unit = [DatabaseUnit addToolbarGestureWithItem:gesture];
    
    [unit setCompleteBlock:^(id result, BOOL success) {
        if (completion) {
            completion(success);
        }
    }];
    
    DB_EXEC(unit);
}

// 删除手势
- (void)deleteGestureWithModel:(ToolbarGestureModel *)gesture {
    NSMutableArray *gestures = self.gestureData[@(self.selectedGroup)];
    NSString *gestureUuid = gesture.uuid;
    
    // 从数据库中删除
    DatabaseUnit *unit = [DatabaseUnit removeToolbarGestureWithUuids:@[gestureUuid]];
    
    @weakify(self)
    [unit setCompleteBlock:^(id result, BOOL success) {
        @strongify(self)
        if (success) {
            // 查找当前数组中的实际位置（以防在异步操作期间数组已经改变）
            NSInteger currentIndex = -1;
            for (NSInteger i = 0; i < gestures.count; i++) {
                ToolbarGestureModel *currentGesture = gestures[i];
                if ([currentGesture.uuid isEqualToString:gestureUuid]) {
                    currentIndex = i;
                    break;
                }
            }
            
            // 如果找到了对应的手势才执行删除
            if (currentIndex != -1) {
                // 从内存中移除
                [gestures removeObjectAtIndex:currentIndex];
                
                // 更新表格高度
                [self updateTableViewHeight];
                
                // 如果删除后无数据，刷新整个表格显示空状态
                if (gestures.count == 0) {
                    [self.tableView reloadData];
                } else {
                    // 更新剩余手势的位置索引并保存
                    [self updateGesturePositions:gestures];
                    
                    // 删除对应行（使用当前索引创建新的indexPath）
//                    NSIndexPath *currentIndexPath = [NSIndexPath indexPathForRow:currentIndex inSection:0];
//                    [self.tableView deleteRowsAtIndexPaths:@[currentIndexPath] withRowAnimation:UITableViewRowAnimationFade];
                    [self.tableView reloadData];
                }
            } else {
                // 如果找不到对应的手势（可能已被删除），则刷新整个表格
                [self.tableView reloadData];
            }
        }
    }];
    DB_EXEC(unit);
}

// 更新手势排序位置
- (void)updateGesturePositions:(NSArray<ToolbarGestureModel *> *)gestures {
    // 更新位置索引
    for (int i = 0; i < gestures.count; i++) {
        ToolbarGestureModel *gesture = gestures[i];
        gesture.position = i;
    }
    
    // 保存到数据库
    DatabaseUnit *unit = [DatabaseUnit updateToolbarGestureAllIndexes:gestures];
    
    [unit setCompleteBlock:^(id result, BOOL success) {
    }];
    
    DB_EXEC(unit);
}

#pragma mark -- 表格视图代理方法

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    NSMutableArray *gestures = self.gestureData[@(self.selectedGroup)];
    
    // 如果没有手势数据，返回2（空状态 + 添加按钮）
    if (gestures.count == 0) {
        return 2;
    }
    
    // 手势列表 + 添加按钮
    return gestures.count + 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    NSMutableArray *gestures = self.gestureData[@(self.selectedGroup)];
    
    // 如果没有手势数据
    if (gestures.count == 0) {
        if (indexPath.row == 0) {
            // 空状态 Cell
            ToolbarGestureEmptyStateCell *cell = [tableView dequeueReusableCellWithIdentifier:kEmptyStateCellIdentifier forIndexPath:indexPath];
            
            // 应用主题
            BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
            [cell applyTheme:isDarkTheme];
            
            return cell;
        } else {
            // 添加 Cell
            ToolbarGestureAddGestureCell *cell = [tableView dequeueReusableCellWithIdentifier:kAddGestureCellIdentifier forIndexPath:indexPath];
            // 设置添加按钮点击事件
            @weakify(self)
            [cell setAddBlock:^{
                @strongify(self)
                [self addNewGesture];
            }];
            
            return cell;
        }
    }
    
    // 有手势数据
    if (indexPath.row < gestures.count) {
        // 手势 Cell
        ToolbarGestureCell *cell = [tableView dequeueReusableCellWithIdentifier:kGestureCellIdentifier forIndexPath:indexPath];
        
        // 配置手势 Cell
        ToolbarGestureModel *model = gestures[indexPath.row];
        [cell updateWithModel:model];
        
        // 设置删除回调
        @weakify(self)
        [cell setDidDeleteAction:^(ToolbarGestureModel *model){
            @strongify(self)
            [self deleteGestureWithModel:model];
        }];
        
        return cell;
    } else {
        // 添加 Cell
        ToolbarGestureAddGestureCell *cell = [tableView dequeueReusableCellWithIdentifier:kAddGestureCellIdentifier forIndexPath:indexPath];
        
        // 设置添加按钮点击事件
        @weakify(self)
        [cell setAddBlock:^{
            @strongify(self)
            [self addNewGesture];
        }];
        
        return cell;
    }
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    NSMutableArray *gestures = self.gestureData[@(self.selectedGroup)];
    
    // 如果没有手势数据
    if (gestures.count == 0) {
        if (indexPath.row == 0) {
            // 空状态 Cell
            return 140;
        } else {
            // 添加 Cell
            return 50;
        }
    }
    
    // 有手势数据
    if (indexPath.row < gestures.count) {
        // 手势 Cell
        return iPadValue(80, 60);
    } else {
        // 添加 Cell
        return 50;
    }
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    
    NSMutableArray *gestures = self.gestureData[@(self.selectedGroup)];
    
    // 添加手势操作
    if ((gestures.count == 0 && indexPath.row == 1) || 
        (gestures.count > 0 && indexPath.row == gestures.count)) {
        [self addNewGesture];
    }
}

#pragma mark - 拖放代理方法

// 提供可以拖动的项目
- (NSArray<UIDragItem *> *)tableView:(UITableView *)tableView itemsForBeginningDragSession:(id<UIDragSession>)session atIndexPath:(NSIndexPath *)indexPath API_AVAILABLE(ios(11.0)) {
    NSMutableArray *gestures = self.gestureData[@(self.selectedGroup)];
    
    // 添加按钮和空状态不能拖动
    if (indexPath.row >= gestures.count || gestures.count == 0) {
        return @[];
    }
    
    ToolbarGestureModel *gesture = gestures[indexPath.row];
    NSItemProvider *itemProvider = [[NSItemProvider alloc] initWithObject:gesture.uuid];
    UIDragItem *dragItem = [[UIDragItem alloc] initWithItemProvider:itemProvider];
    dragItem.localObject = gesture;
    return @[dragItem];
}

// 确定是否可以在特定放置位置放置项目
- (UITableViewDropProposal *)tableView:(UITableView *)tableView dropSessionDidUpdate:(id<UIDropSession>)session withDestinationIndexPath:(NSIndexPath *)destinationIndexPath API_AVAILABLE(ios(11.0)) {
    NSMutableArray *gestures = self.gestureData[@(self.selectedGroup)];
    
    // 不能拖到添加按钮的位置
    if (destinationIndexPath && destinationIndexPath.row >= gestures.count) {
        return [[UITableViewDropProposal alloc] initWithDropOperation:UIDropOperationForbidden intent:UITableViewDropIntentUnspecified];
    }
    
    if (session.localDragSession) {
        return [[UITableViewDropProposal alloc] initWithDropOperation:UIDropOperationMove intent:UITableViewDropIntentInsertAtDestinationIndexPath];
    }
    
    return [[UITableViewDropProposal alloc] initWithDropOperation:UIDropOperationForbidden intent:UITableViewDropIntentUnspecified];
}

// 执行放置操作
- (void)tableView:(UITableView *)tableView performDropWithCoordinator:(id<UITableViewDropCoordinator>)coordinator API_AVAILABLE(ios(11.0)) {
    NSIndexPath *destinationIndexPath = coordinator.destinationIndexPath;
    NSMutableArray *gestures = self.gestureData[@(self.selectedGroup)];
    
    // 不能拖到添加按钮的位置
    if (!destinationIndexPath || destinationIndexPath.row >= gestures.count) {
        return;
    }
    
    // 仅支持重新排序
    if (coordinator.proposal.operation == UIDropOperationMove && coordinator.items.count == 1) {
        NSIndexPath *sourceIndexPath = [coordinator.items firstObject].sourceIndexPath;
        ToolbarGestureModel *movedGesture = gestures[sourceIndexPath.row];
        
        // 从源位置移除
        [gestures removeObjectAtIndex:sourceIndexPath.row];
        
        // 插入到目标位置
        [gestures insertObject:movedGesture atIndex:destinationIndexPath.row];
        
        // 更新UI
        [tableView moveRowAtIndexPath:sourceIndexPath toIndexPath:destinationIndexPath];
        
        // 更新所有手势的排序位置并保存到数据库
        [self updateGesturePositions:gestures];
    }
}

#pragma mark -- 观察者设置

- (void)setupObservers {
    // 工具栏按钮组选择回调
    @weakify(self)
    [self.toolbarHeader setDidSelectToolbarGroupAction:^(ToolbarGroup group) {
        @strongify(self)
        self.selectedGroup = group;
        
        // 更新分组标题
//        [self updateSectionTitle];
        
        // 更新表格高度
        [self updateTableViewHeight];
        
        // 刷新表格
        [self.tableView reloadData];
    }];
}

//- (void)updateSectionTitle {
//    NSString *titleFormat = @"长按%@按钮触发的操作";
//    NSString *buttonName = @"";
//    
//    switch (self.selectedGroup) {
//        case ToolbarGroupBack:
//            buttonName = @"后退";
//            break;
//        case ToolbarGroupForward:
//            buttonName = @"前进";
//            break;
//        case ToolbarGroupNew:
//            buttonName = @"新建";
//            break;
//        case ToolbarGroupTabs:
//            buttonName = @"标签页";
//            break;
//        case ToolbarGroupFeatures:
//            buttonName = @"功能";
//            break;
//        case ToolbarGroupHome:
//            buttonName = @"首页";
//            break;
//        default:
//            buttonName = @"工具栏";
//            break;
//    }
//    
//    self.sectionTitleLabel.text = [NSString stringWithFormat:titleFormat, buttonName];
//}

#pragma mark - layout

- (void)addSubviews
{
    [self.view addSubview:self.scrollView];
    [self.scrollView addSubview:self.containerView];
    
    [self.containerView addSubview:self.descriptionLabel];
    [self.containerView addSubview:self.toolbarHeader];
    [self.containerView addSubview:self.sectionTitleLabel];
    [self.containerView addSubview:self.tableView];
}

- (void)defineLayout
{
    float leftOffset = iPadValue(30, 15);
    float topOffset = iPadValue(30, 15);
    
    // 滚动视图
    [self.scrollView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
    }];
    
    // 容器视图
    [self.containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.scrollView);
        make.width.equalTo(self.view);
        make.bottom.equalTo(self.tableView).offset(20);
    }];
    
    // 描述标签
    [self.descriptionLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.containerView).offset(topOffset);
        make.left.equalTo(self.containerView).offset(leftOffset);
        make.right.equalTo(self.containerView).offset(-leftOffset);
    }];
    
    // 工具栏头部视图
    [self.toolbarHeader mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.descriptionLabel.mas_bottom).offset(iPadValue(15, 15));
        make.left.equalTo(self.containerView).offset(leftOffset);
        make.right.equalTo(self.containerView).offset(-leftOffset);
//        make.height.mas_equalTo(60);
    }];
    
    // 分组标题标签
    [self.sectionTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.toolbarHeader.mas_bottom).offset(iPadValue(30, 24));
        make.left.equalTo(self.containerView).offset(leftOffset);
        make.right.equalTo(self.containerView).offset(-leftOffset);
    }];
    
    // 表格视图
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.sectionTitleLabel.mas_bottom).offset(iPadValue(15, 8));
        make.left.equalTo(self.containerView).offset(leftOffset);
        make.right.equalTo(self.containerView).offset(-leftOffset);
        make.height.mas_equalTo(0);
    }];
}

#pragma mark - getters

- (UIScrollView *)scrollView {
    if (!_scrollView) {
        _scrollView = [[UIScrollView alloc] init];
        _scrollView.showsVerticalScrollIndicator = NO;
        _scrollView.backgroundColor = [UIColor clearColor];
    }
    return _scrollView;
}

- (UIView *)containerView {
    if (!_containerView) {
        _containerView = [[UIView alloc] init];
        _containerView.backgroundColor = [UIColor clearColor];
    }
    return _containerView;
}

- (ToolbarGestureHeader *)toolbarHeader {
    if (!_toolbarHeader) {
        _toolbarHeader = [[ToolbarGestureHeader alloc] init];
        _toolbarHeader.layer.cornerRadius = 8;
        _toolbarHeader.layer.masksToBounds = YES;
        
        // 设置阴影
        _toolbarHeader.layer.shadowColor = [UIColor blackColor].CGColor;
        _toolbarHeader.layer.shadowOffset = CGSizeMake(0, 2);
        _toolbarHeader.layer.shadowOpacity = 0.1;
        _toolbarHeader.layer.shadowRadius = 8;
    }
    return _toolbarHeader;
}

- (UILabel *)descriptionLabel {
    if (!_descriptionLabel) {
        _descriptionLabel = [[UILabel alloc] init];
        _descriptionLabel.text = NSLocalizedString(@"toolbar.gesture.toolbar.title", nil);
        _descriptionLabel.font = [UIFont systemFontOfSize:iPadValue(16, 14)];
        _descriptionLabel.textColor = [UIColor colorWithHexString:@"#8e8e93"];
        _descriptionLabel.numberOfLines = 0;
    }
    return _descriptionLabel;
}

- (UILabel *)sectionTitleLabel {
    if (!_sectionTitleLabel) {
        _sectionTitleLabel = [[UILabel alloc] init];
        _sectionTitleLabel.text = NSLocalizedString(@"toolbar.gesture.section.title", nil);
        _sectionTitleLabel.font = [UIFont systemFontOfSize:iPadValue(16, 14)];
        _sectionTitleLabel.textColor = [UIColor colorWithHexString:@"#8e8e93"];
    }
    return _sectionTitleLabel;
}

- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
        _tableView.delegate = self;
        _tableView.dataSource = self;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.showsVerticalScrollIndicator = NO;
        _tableView.rowHeight = UITableViewAutomaticDimension;
        _tableView.estimatedRowHeight = 50;
        _tableView.estimatedSectionHeaderHeight = 0;
        _tableView.estimatedSectionFooterHeight = 0;
                
        [self.tableView registerClass:[ToolbarGestureCell class] forCellReuseIdentifier:kGestureCellIdentifier];
        [self.tableView registerClass:[ToolbarGestureAddGestureCell class] forCellReuseIdentifier:kAddGestureCellIdentifier];
        [self.tableView registerClass:[ToolbarGestureEmptyStateCell class] forCellReuseIdentifier:kEmptyStateCellIdentifier];
    }
    return _tableView;
}

@end
