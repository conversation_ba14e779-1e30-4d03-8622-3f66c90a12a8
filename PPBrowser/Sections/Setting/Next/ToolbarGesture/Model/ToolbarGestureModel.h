//
//  ToolbarGestureModel.h
//  PPBrowser
//
//  Created by qingbin on 2025/3/27.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "BaseModel.h"
#import "PPEnums.h"

NS_ASSUME_NONNULL_BEGIN

@interface ToolbarGestureModel : BaseModel
// id
@property (nonatomic, strong) NSString *uuid;
// 类型
@property (nonatomic, assign) ToolbarLongPressAction type;
// 所在分组
@property (nonatomic, assign) ToolbarGroup groupType;
// 在所在分组的排序排名
@property (nonatomic, assign) int position;

// 这个方法返回不同类型操作的 FontAwesome 图标名称
+ (NSString *)iconNameForActionType:(ToolbarLongPressAction)type;

// 根据不同类型返回标题
+ (NSString *)titleForToolbarLongPressAction:(ToolbarLongPressAction)action;

// 返回默认的内置列表到数据库中
+ (NSArray<ToolbarGestureModel *> *)builtInList;

@end

NS_ASSUME_NONNULL_END
