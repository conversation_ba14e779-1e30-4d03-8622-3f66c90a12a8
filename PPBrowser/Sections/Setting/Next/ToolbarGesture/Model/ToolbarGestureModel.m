//
//  ToolbarGestureModel.m
//  PPBrowser
//
//  Created by qingbin on 2025/3/27.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "ToolbarGestureModel.h"

@implementation ToolbarGestureModel

// 这个方法返回不同类型操作的 FontAwesome 图标名称
+ (NSString *)iconNameForActionType:(ToolbarLongPressAction)type {
    switch (type) {
//        case ToolbarLongPressActionBackHistory:
//            return @"clock-rotate-left-solid";
//        case ToolbarLongPressActionForwardHistory:
//            return @"clock-rotate-left-solid";
        case ToolbarLongPressActionGoHome:
            return @"gesture_home";
        case ToolbarLongPressActionNewTab:
            return @"gesture_plus";
        case ToolbarLongPressActionShare:
            return @"share-nodes";
        case ToolbarLongPressActionAddBookmark:
            return @"gesture_bookmark";
        case ToolbarLongPressActionAddHome:
            return @"gesture_star";
        case ToolbarLongPressActionOpenSettings:
            return @"gesture_gear";
        case ToolbarLongPressActionFindInPage:
            return @"gesture_search";
        case ToolbarLongPressActionImageMode:
            return @"gesture_image";
        case ToolbarLongPressActionRefresh:
            return @"gesture_rotate";
        case ToolbarLongPressActionScrollToTop:
            return @"gesture-arrow-up";
        case ToolbarLongPressActionScrollToBottom:
            return @"gesture-arrow-down";
        case ToolbarLongPressActionOpenHistory:
            return @"clock-rotate-left-solid";
        case ToolbarLongPressActionOpenBookmarks:
            return @"gesture_bookmark";
        case ToolbarLongPressActionCloseCurrentTab:
            return @"geture_xmark";
        case ToolbarLongPressActionCloseOtherTabs:
            return @"gesture-xmark-circle";
        case ToolbarLongPressActionCloseAllTabs:
            return @"gesture-xmarks-lines";
        case ToolbarLongPressActionOpenTranslation:
            return @"gesture_language";
        case ToolbarLongPressActionLockFullscreen:
            return @"gesture-expand";
        case ToolbarLongPressActionOpenUserScript:
            return @"gesture_code";
        case ToolbarLongPressActionOpenTagit:
            return @"gesture_marker";
        case ToolbarLongPressActionOpenUseragent:
            return @"gesture_mobile";
        default:
            return @"gesutre-circle-question";
    }
}

// 根据不同类型返回标题
+ (NSString *)titleForToolbarLongPressAction:(ToolbarLongPressAction)action {
    switch (action) {
//        case ToolbarLongPressActionBackHistory:
//            return NSLocalizedString(@"toolbar.gesture.backhistory", nil);
//        case ToolbarLongPressActionForwardHistory:
//            return NSLocalizedString(@"toolbar.gesture.forwardhistory", nil);
        case ToolbarLongPressActionGoHome:
            return NSLocalizedString(@"toolbar.gesture.gohome", nil);
        case ToolbarLongPressActionNewTab:
            return NSLocalizedString(@"toolbar.gesture.newtab", nil);
        case ToolbarLongPressActionShare:
            return NSLocalizedString(@"toolbar.gesture.share", nil);
        case ToolbarLongPressActionAddBookmark:
            return NSLocalizedString(@"toolbar.gesture.addbookmark", nil);
        case ToolbarLongPressActionAddHome:
            return NSLocalizedString(@"toolbar.gesture.addhome", nil);
        case ToolbarLongPressActionOpenSettings:
            return NSLocalizedString(@"toolbar.gesture.opensetting", nil);
        case ToolbarLongPressActionFindInPage:
            return NSLocalizedString(@"toolbar.gesture.findinpage", nil);
        case ToolbarLongPressActionImageMode:
            return NSLocalizedString(@"toolbar.gesture.imagemode", nil);
        case ToolbarLongPressActionRefresh:
            return NSLocalizedString(@"toolbar.gesture.refresh", nil);
        case ToolbarLongPressActionScrollToTop:
            return NSLocalizedString(@"toolbar.gesture.scrolltotop", nil);
        case ToolbarLongPressActionScrollToBottom:
            return NSLocalizedString(@"toolbar.gesture.scrolltobottom", nil);
        case ToolbarLongPressActionOpenHistory:
            return NSLocalizedString(@"toolbar.gesture.openhistory", nil);
        case ToolbarLongPressActionOpenBookmarks:
            return NSLocalizedString(@"toolbar.gesture.openbookmarks", nil);
        case ToolbarLongPressActionCloseCurrentTab:
            return NSLocalizedString(@"toolbar.gesture.closecurrenttab", nil);
        case ToolbarLongPressActionCloseOtherTabs:
            return NSLocalizedString(@"toolbar.gesture.closeothertabs", nil);
        case ToolbarLongPressActionCloseAllTabs:
            return NSLocalizedString(@"toolbar.gesture.closealltabs", nil);
        case ToolbarLongPressActionOpenTranslation:
            return NSLocalizedString(@"toolbar.gesture.opentranslation", nil);
        case ToolbarLongPressActionLockFullscreen:
            return NSLocalizedString(@"toolbar.gesture.lockfullscreen", nil);
        case ToolbarLongPressActionOpenUserScript:
            return NSLocalizedString(@"toolbar.gesture.openuserscript", nil);
        case ToolbarLongPressActionOpenTagit:
            return NSLocalizedString(@"toolbar.gesture.opentagit", nil);
        case ToolbarLongPressActionOpenUseragent:
            return NSLocalizedString(@"toolbar.gesture.openuseragent", nil);
    }
    
    return @"";
}

#pragma mark - 返回默认的内置列表到数据库中

+ (NSArray<ToolbarGestureModel *> *)builtInList
{
    NSMutableArray* array = [NSMutableArray array];
    
    // 后退按钮长按动作
    {
        ToolbarGestureModel *model = [[ToolbarGestureModel alloc] init];
        model.uuid = [[NSUUID UUID] UUIDString];
        model.type = ToolbarLongPressActionScrollToTop;
        model.groupType = ToolbarGroupBack;
        model.position = 0;
        [array addObject:model];
        
        model = [[ToolbarGestureModel alloc] init];
        model.uuid = [[NSUUID UUID] UUIDString];
        model.type = ToolbarLongPressActionScrollToBottom;
        model.groupType = ToolbarGroupBack;
        model.position = 1;
        [array addObject:model];
        
        model = [[ToolbarGestureModel alloc] init];
        model.uuid = [[NSUUID UUID] UUIDString];
        model.type = ToolbarLongPressActionOpenHistory;
        model.groupType = ToolbarGroupBack;
        model.position = 2;
        [array addObject:model];
    }
    
    // 前进按钮长按动作
    {
        ToolbarGestureModel *model = [[ToolbarGestureModel alloc] init];
        model.uuid = [[NSUUID UUID] UUIDString];
        model.type = ToolbarLongPressActionScrollToTop;
        model.groupType = ToolbarGroupForward;
        model.position = 0;
        [array addObject:model];
        
        model = [[ToolbarGestureModel alloc] init];
        model.uuid = [[NSUUID UUID] UUIDString];
        model.type = ToolbarLongPressActionScrollToBottom;
        model.groupType = ToolbarGroupForward;
        model.position = 1;
        [array addObject:model];
        
        model = [[ToolbarGestureModel alloc] init];
        model.uuid = [[NSUUID UUID] UUIDString];
        model.type = ToolbarLongPressActionOpenHistory;
        model.groupType = ToolbarGroupForward;
        model.position = 2;
        [array addObject:model];
    }
    
    // 新建按钮长按动作
    {
        ToolbarGestureModel *model = [[ToolbarGestureModel alloc] init];
        model.uuid = [[NSUUID UUID] UUIDString];
        model.type = ToolbarLongPressActionShare;
        model.groupType = ToolbarGroupNew;
        model.position = 0;
        [array addObject:model];
        
        model = [[ToolbarGestureModel alloc] init];
        model.uuid = [[NSUUID UUID] UUIDString];
        model.type = ToolbarLongPressActionAddBookmark;
        model.groupType = ToolbarGroupNew;
        model.position = 1;
        [array addObject:model];
        
        model = [[ToolbarGestureModel alloc] init];
        model.uuid = [[NSUUID UUID] UUIDString];
        model.type = ToolbarLongPressActionAddHome;
        model.groupType = ToolbarGroupNew;
        model.position = 1;
        [array addObject:model];
    }
    
    // 标签页按钮长按动作
    {
        ToolbarGestureModel *model = [[ToolbarGestureModel alloc] init];
        model.uuid = [[NSUUID UUID] UUIDString];
        model.type = ToolbarLongPressActionNewTab;
        model.groupType = ToolbarGroupTabs;
        model.position = 0;
        [array addObject:model];
        
        model = [[ToolbarGestureModel alloc] init];
        model.uuid = [[NSUUID UUID] UUIDString];
        model.type = ToolbarLongPressActionCloseCurrentTab;
        model.groupType = ToolbarGroupTabs;
        model.position = 1;
        [array addObject:model];
        
        model = [[ToolbarGestureModel alloc] init];
        model.uuid = [[NSUUID UUID] UUIDString];
        model.type = ToolbarLongPressActionCloseOtherTabs;
        model.groupType = ToolbarGroupTabs;
        model.position = 2;
        [array addObject:model];
        
        model = [[ToolbarGestureModel alloc] init];
        model.uuid = [[NSUUID UUID] UUIDString];
        model.type = ToolbarLongPressActionCloseAllTabs;
        model.groupType = ToolbarGroupTabs;
        model.position = 3;
        [array addObject:model];
    }
    
    // 功能页按钮长按动作
    {
        ToolbarGestureModel* model = [[ToolbarGestureModel alloc] init];
        model.uuid = [[NSUUID UUID] UUIDString];
        model.type = ToolbarLongPressActionOpenBookmarks;
        model.groupType = ToolbarGroupFeatures;
        model.position = 2;
        [array addObject:model];
        
        model = [[ToolbarGestureModel alloc] init];
        model.uuid = [[NSUUID UUID] UUIDString];
        model.type = ToolbarLongPressActionFindInPage;
        model.groupType = ToolbarGroupFeatures;
        model.position = 3;
        [array addObject:model];
        
        model = [[ToolbarGestureModel alloc] init];
        model.uuid = [[NSUUID UUID] UUIDString];
        model.type = ToolbarLongPressActionImageMode;
        model.groupType = ToolbarGroupFeatures;
        model.position = 4;
        [array addObject:model];
                
        model = [[ToolbarGestureModel alloc] init];
        model.uuid = [[NSUUID UUID] UUIDString];
        model.type = ToolbarLongPressActionOpenTranslation;
        model.groupType = ToolbarGroupFeatures;
        model.position = 5;
        [array addObject:model];
        
        model = [[ToolbarGestureModel alloc] init];
        model.uuid = [[NSUUID UUID] UUIDString];
        model.type = ToolbarLongPressActionOpenSettings;
        model.groupType = ToolbarGroupFeatures;
        model.position = 6;
        [array addObject:model];
        
        model = [[ToolbarGestureModel alloc] init];
        model.uuid = [[NSUUID UUID] UUIDString];
        model.type = ToolbarLongPressActionLockFullscreen;
        model.groupType = ToolbarGroupFeatures;
        model.position = 7;
        [array addObject:model];
        
        model = [[ToolbarGestureModel alloc] init];
        model.uuid = [[NSUUID UUID] UUIDString];
        model.type = ToolbarLongPressActionRefresh;
        model.groupType = ToolbarGroupFeatures;
        model.position = 8;
        [array addObject:model];
    }
    
    // 首页按钮长按动作
    {
        ToolbarGestureModel *model = [[ToolbarGestureModel alloc] init];
        model.uuid = [[NSUUID UUID] UUIDString];
        model.type = ToolbarLongPressActionGoHome;
        model.groupType = ToolbarGroupHome;
        model.position = 0;
        [array addObject:model];
        
        model = [[ToolbarGestureModel alloc] init];
        model.uuid = [[NSUUID UUID] UUIDString];
        model.type = ToolbarLongPressActionAddHome;
        model.groupType = ToolbarGroupHome;
        model.position = 1;
        [array addObject:model];
        
        model = [[ToolbarGestureModel alloc] init];
        model.uuid = [[NSUUID UUID] UUIDString];
        model.type = ToolbarLongPressActionScrollToTop;
        model.groupType = ToolbarGroupHome;
        model.position = 2;
        [array addObject:model];
        
        model = [[ToolbarGestureModel alloc] init];
        model.uuid = [[NSUUID UUID] UUIDString];
        model.type = ToolbarLongPressActionScrollToBottom;
        model.groupType = ToolbarGroupHome;
        model.position = 3;
        [array addObject:model];
        
        model = [[ToolbarGestureModel alloc] init];
        model.uuid = [[NSUUID UUID] UUIDString];
        model.type = ToolbarLongPressActionLockFullscreen;
        model.groupType = ToolbarGroupHome;
        model.position = 4;
        [array addObject:model];
    }
    
    return array;
}

@end
