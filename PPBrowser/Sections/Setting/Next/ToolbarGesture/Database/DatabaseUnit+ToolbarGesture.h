//
//  DatabaseUnit+ToolbarGesture.h
//  PPBrowser
//
//  Created by qingbin on 2025/3/27.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "DatabaseUnit.h"
#import "ToolbarGestureModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface DatabaseUnit (ToolbarGesture)

// 查询所有手势
+ (DatabaseUnit*)queryAllToolbarGestures;

// 添加手势
+ (DatabaseUnit*)addToolbarGestureWithItem:(ToolbarGestureModel *)item;

// 删除手势
+ (DatabaseUnit*)removeToolbarGestureWithUuids:(NSArray *)uuids;

// 查询指定分组下的手势列表
+ (DatabaseUnit*)queryToolbarGestureListWithGroup:(ToolbarGroup)group;

// 更新手势排序
+ (DatabaseUnit*)updateToolbarGestureAllIndexes:(NSArray *)allItems;

@end

NS_ASSUME_NONNULL_END
