//
//  DatabaseUnit+ToolbarGesture.m
//  PPBrowser
//
//  Created by qingbin on 2025/3/27.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "DatabaseUnit+ToolbarGesture.h"
#import "ReactiveCocoa.h"
#import "FMDatabaseQueue.h"
#import "FMDatabase.h"

@implementation DatabaseUnit (ToolbarGesture)

#pragma mark - 查询操作

// 查询所有手势，用于内存缓存初始化
+ (DatabaseUnit*)queryAllToolbarGestures
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = @"SELECT * FROM t_toolbar_gesture ORDER BY position ASC";
        FMResultSet* set = [db executeQuery:command];
        
        NSMutableArray *array = [[NSMutableArray alloc] init];
        while ([set next]) {
            ToolbarGestureModel* item = [[ToolbarGestureModel alloc] initWithDictionary:[set resultDictionary] error:nil];
            [array addObject:item];
        }
        
        // 按索引排序
        array = [[array sortedArrayUsingComparator:^NSComparisonResult(ToolbarGestureModel* obj1, ToolbarGestureModel* obj2) {
            if (obj1.position < obj2.position) {
                return NSOrderedAscending;
            } else {
                return NSOrderedDescending;
            }
        }] mutableCopy];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if (unit.completeBlock) {
                unit.completeBlock(array, YES);
            }
        });
    };
    
    return unit;
}

// 查询指定分组下的手势列表
+ (DatabaseUnit*)queryToolbarGestureListWithGroup:(ToolbarGroup)group
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = @"SELECT * FROM t_toolbar_gesture WHERE groupType = ? ORDER BY position ASC";
        FMResultSet* set = [db executeQuery:command, @(group)];
        
        NSMutableArray *array = [[NSMutableArray alloc] init];
        while ([set next]) {
            ToolbarGestureModel* item = [[ToolbarGestureModel alloc] initWithDictionary:[set resultDictionary] error:nil];
            [array addObject:item];
        }
        
        // 按索引排序
        array = [[array sortedArrayUsingComparator:^NSComparisonResult(ToolbarGestureModel* obj1, ToolbarGestureModel* obj2) {
            if (obj1.position < obj2.position) {
                return NSOrderedAscending;
            } else {
                return NSOrderedDescending;
            }
        }] mutableCopy];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if (unit.completeBlock) {
                unit.completeBlock(array, YES);
            }
        });
    };
    
    return unit;
}

#pragma mark - 添加操作

// 添加一个手势
+ (DatabaseUnit*)addToolbarGestureWithItem:(ToolbarGestureModel *)item
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    // 必须有uuid
    if (item.uuid.length == 0) {
        item.uuid = [[NSUUID UUID] UUIDString];
    }
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        
        // 检查是否已存在相同的记录
        NSString* checkCommand = @"SELECT * FROM t_toolbar_gesture WHERE uuid = ?";
        FMResultSet* set = [db executeQuery:checkCommand, item.uuid];
        
        BOOL result = YES;
        if ([set next]) {
            // 已存在，更新
            NSString* command = @"UPDATE t_toolbar_gesture SET type = ?, groupType = ?, position = ? WHERE uuid = ?";
            result = [db executeUpdate:command, @(item.type), @(item.groupType), @(item.position), item.uuid];
        } else {
            // 不存在，插入
            NSString* command = @"INSERT INTO t_toolbar_gesture(uuid, type, groupType, position) VALUES (?, ?, ?, ?)";
            result = [db executeUpdate:command, item.uuid, @(item.type), @(item.groupType), @(item.position)];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if (unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

#pragma mark - 删除操作

// 删除一组手势
+ (DatabaseUnit*)removeToolbarGestureWithUuids:(NSArray *)uuids
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        
        NSMutableString* idList = [NSMutableString new];
        for (int i = 0; i < uuids.count; i++) {
            NSString* uuid = uuids[i];
            NSString* option = [NSString stringWithFormat:@"\"%@\"", uuid];
            [idList appendString:option];
            if (i < uuids.count - 1) {
                [idList appendString:@","];
            }
        }
        
        NSString* command = [NSString stringWithFormat:@"DELETE FROM t_toolbar_gesture WHERE uuid in (%@);", idList];
        BOOL result = [db executeUpdate:command];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if (unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

#pragma mark - 更新操作

// 批量更新手势排序
+ (DatabaseUnit*)updateToolbarGestureAllIndexes:(NSArray*)allItems
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        
        BOOL result = YES;
        for (int i = 0; i < allItems.count; i++) {
            ToolbarGestureModel* item = allItems[i];
            item.position = i;
            NSString* command = @"UPDATE t_toolbar_gesture SET position = ? WHERE uuid = ?";
            result = [db executeUpdate:command, @(item.position), item.uuid] && result;
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if (unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

@end
