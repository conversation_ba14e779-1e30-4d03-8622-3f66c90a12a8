//
//  SearchSettingViewController.m
//  PPBrowser
//
//  Created by qingbin on 2025/4/13.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "SearchSettingViewController.h"
#import "Masonry.h"
#import "MaizyHeader.h"
#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "ReactiveCocoa.h"
#import "NSObject+Helper.h"
#import "SettingSwitchView.h"
#import "SettingArrowView.h"
#import "PaddingNewLabel.h"
#import "PreferenceManager.h"
#import "PPNotifications.h"
#import "BrowserUtils.h"
#import "SearchViewController.h"

@interface SearchSettingViewController ()

@property (nonatomic, strong) UIScrollView* scrollView;
@property (nonatomic, strong) UIStackView* stackView;

// 搜索体验
@property (nonatomic, strong) PaddingNewLabel *searchExperienceSectionView;
@property (nonatomic, strong) SettingSwitchView *showSearchHistoryView;
@property (nonatomic, strong) SettingSwitchView *showSearchSuggestionView;
@property (nonatomic, strong) SettingSwitchView *showPasteContentView;

// 搜索引擎
@property (nonatomic, strong) PaddingNewLabel *searchEngineSectionView;
//@property (nonatomic, strong) SettingSwitchView *showSearchEngineBarView;
@property (nonatomic, strong) SettingArrowView *searchEngineListView;

@end

@implementation SearchSettingViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.title = NSLocalizedString(@"setting.searchSetting", nil);
    
    [self addSubviews];
    [self defineLayout];
    [self updateWithModel];
    [self setupObservers];
    [self createCustomLeftBarButtonItem];
    
    [self applyTheme];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
    } else {
        self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    //只有当前的controller会触发一次, 其它已存在的controller走通知
    if(needReload) {
        //修改暗黑模式
        [self applyTheme];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}

- (void)updateWithModel
{
    BOOL showSearchHistory = [[PreferenceManager shareInstance].items.isShowSearchHistory boolValue];
    [self.showSearchHistoryView updateWithTitle:NSLocalizedString(@"searchSetting.showHistory.text", nil) isOn:showSearchHistory];
    
    BOOL showSearchSuggestion = [[PreferenceManager shareInstance].items.isShowSearchSuggestion boolValue];
    [self.showSearchSuggestionView updateWithTitle:NSLocalizedString(@"searchSetting.showSuggestion.text", nil) isOn:showSearchSuggestion];
    
    BOOL showPasteContent = [[PreferenceManager shareInstance].items.isReadPasteboard boolValue];
    [self.showPasteContentView updateWithTitle:NSLocalizedString(@"searchSetting.showPasteContent.text", nil) isOn:showPasteContent];
    
//    BOOL showSearchEngineBar = [[PreferenceManager shareInstance].items.isShowSearchEngineBar boolValue];
//    [self.showSearchEngineBarView updateWithTitle:NSLocalizedString(@"searchSetting.showEngineBar.text", nil) isOn:showSearchEngineBar];
}

- (void)setupObservers
{
    @weakify(self)
    [self.showSearchHistoryView setDidSwithAction:^(BOOL isOn) {
        [PreferenceManager shareInstance].items.isShowSearchHistory = @(isOn);
        [[PreferenceManager shareInstance] encode];
    }];
    
    [self.showSearchSuggestionView setDidSwithAction:^(BOOL isOn) {
        [PreferenceManager shareInstance].items.isShowSearchSuggestion = @(isOn);
        [[PreferenceManager shareInstance] encode];
    }];
    
    [self.showPasteContentView setDidSwithAction:^(BOOL isOn) {
        [PreferenceManager shareInstance].items.isReadPasteboard = @(isOn);
        [[PreferenceManager shareInstance] encode];
    }];
    
//    [self.showSearchEngineBarView setDidSwithAction:^(BOOL isOn) {
//        [PreferenceManager shareInstance].items.isShowSearchEngineBar = @(isOn);
//        [[PreferenceManager shareInstance] encode];
//    }];
    
    [self.searchEngineListView setDidAction:^{
        @strongify(self)
        SearchViewController* vc = [SearchViewController new];
        [self.navigationController pushViewController:vc animated:YES];
    }];
}

#pragma mark -- layout
- (void)addSubviews
{
    [self.view addSubview:self.scrollView];
    [self.scrollView addSubview:self.stackView];
}

- (void)defineLayout
{
    float leftOffset = iPadValue(30, 15);
    float topOffset = iPadValue(30, 15);
    
    [self.scrollView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(leftOffset);
        make.right.mas_offset(-leftOffset);
        make.top.equalTo(self.view).offset(0);
        make.bottom.equalTo(self.view);
    }];
    
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.scrollView);
        make.bottom.equalTo(self.scrollView).offset(-topOffset);
        make.left.equalTo(self.view).offset(leftOffset);
        make.right.equalTo(self.view).offset(-leftOffset);
    }];
    
    [self.showSearchHistoryView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([SettingSwitchView height]);
    }];
    
    [self.showSearchSuggestionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([SettingSwitchView height]);
    }];
    
    [self.showPasteContentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([SettingSwitchView height]);
    }];
    
//    [self.showSearchEngineBarView mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.height.mas_equalTo([SettingSwitchView height]);
//    }];
    
    [self.searchEngineListView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([SettingArrowView height]);
    }];
}

#pragma mark -- lazy init
- (UIStackView *)stackView
{
    if(!_stackView) {
        _stackView = [[UIStackView alloc]initWithArrangedSubviews:@[
            self.searchExperienceSectionView,
            self.showSearchHistoryView,
            self.showSearchSuggestionView,
            self.showPasteContentView,
            
            self.searchEngineSectionView,
//            self.showSearchEngineBarView,
            self.searchEngineListView
        ]];
        
        _stackView.spacing = 0;
        _stackView.axis = UILayoutConstraintAxisVertical;
        _stackView.backgroundColor = UIColor.clearColor;
        _stackView.layer.cornerRadius = 10;
        _stackView.layer.masksToBounds = YES;
    }
    
    return _stackView;
}

- (SettingSwitchView *)showSearchHistoryView
{
    if(!_showSearchHistoryView) {
        _showSearchHistoryView = [[SettingSwitchView alloc]initWithShowLine:YES];
        _showSearchHistoryView.layer.cornerRadius = 10;
        _showSearchHistoryView.layer.maskedCorners = UIRectCornerTopLeft | UIRectCornerTopRight;
    }
    return _showSearchHistoryView;
}

- (SettingSwitchView *)showSearchSuggestionView
{
    if(!_showSearchSuggestionView) {
        _showSearchSuggestionView = [[SettingSwitchView alloc]initWithShowLine:YES];
    }
    return _showSearchSuggestionView;
}

- (SettingSwitchView *)showPasteContentView
{
    if(!_showPasteContentView) {
        _showPasteContentView = [[SettingSwitchView alloc]initWithShowLine:NO];
        _showPasteContentView.layer.cornerRadius = 10;
        _showPasteContentView.layer.maskedCorners = UIRectCornerBottomLeft | UIRectCornerBottomRight;
    }
    return _showPasteContentView;
}

//- (SettingSwitchView *)showSearchEngineBarView
//{
//    if(!_showSearchEngineBarView) {
//        _showSearchEngineBarView = [[SettingSwitchView alloc]initWithShowLine:YES];
//        _showSearchEngineBarView.layer.cornerRadius = 10;
//        _showSearchEngineBarView.layer.maskedCorners = UIRectCornerTopLeft | UIRectCornerTopRight;
//    }
//    return _showSearchEngineBarView;
//}

- (SettingArrowView *)searchEngineListView
{
    if(!_searchEngineListView) {
        _searchEngineListView = [[SettingArrowView alloc]initWithShowLine:NO];
        _searchEngineListView.layer.cornerRadius = 10;
//        _searchEngineListView.layer.maskedCorners = UIRectCornerTopLeft | UIRectCornerTopRight;
//        _searchEngineListView.layer.maskedCorners = UIRectCornerBottomLeft | UIRectCornerBottomRight;
        _searchEngineListView.layer.masksToBounds = YES;
        
        [_searchEngineListView updateWithTitle:NSLocalizedString(@"searchSetting.searchlist.text", nil)];
    }
    return _searchEngineListView;
}

- (UIScrollView *)scrollView
{
    if(!_scrollView) {
        _scrollView = [[UIScrollView alloc]init];
        _scrollView.showsVerticalScrollIndicator = NO;
        _scrollView.backgroundColor = UIColor.clearColor;
    }
    return _scrollView;
}

- (PaddingNewLabel *)searchExperienceSectionView
{
    if (!_searchExperienceSectionView) {
        _searchExperienceSectionView = [self _createSectionHeaderWithTitle:NSLocalizedString(@"searchSetting.experience.title", nil)];
    }
    return _searchExperienceSectionView;
}

- (PaddingNewLabel *)searchEngineSectionView
{
    if (!_searchEngineSectionView) {
        _searchEngineSectionView = [self _createSectionHeaderWithTitle:NSLocalizedString(@"searchSetting.engine.title", nil)];
    }
    return _searchEngineSectionView;
}

- (PaddingNewLabel *)_createSectionHeaderWithTitle:(NSString *)title
{
    PaddingNewLabel* label = [[PaddingNewLabel alloc] initWithFrame:CGRectMake(0, 0, kScreenWidth, iPadValue(70, 48))];
    label.text = title;
    label.font = [UIFont systemFontOfSize:iPadValue(18, 14)];
    label.textColor = [UIColor colorWithHexString:@"#6B7280"];
    label.edgeInsets = UIEdgeInsetsMake(iPadValue(30, 20), iPadValue(30, 15), iPadValue(10, 8), 0);
    label.textAlignment = NSTextAlignmentLeft;
    return label;
}

@end
