//
//  TempModel.m
//  PPBrowser
//
//  Created by qingbin on 2024/11/5.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import "TempModel.h"
#import "NSString+MKNetworkKitAdditions.h"

@implementation TempModel

- (NSString *)fileUrl
{
    NSFileManager* fileManager = [NSFileManager defaultManager];
    
    NSString* kDownloadIdentifierV2 = @"com.browser.downloadmodule";
    NSString* kDownloadIdentifierV3 = @"v3.com.browser.downloadmodule";
    
    NSString* v3Path = [[TempModel _diskSavePathWithIdentifierV3:kDownloadIdentifierV3] stringByAppendingPathComponent:self.fileName];
    
    if([fileManager fileExistsAtPath:v3Path]) {
        return v3Path;
    }
    
    NSString* v2Path = [[TempModel _diskSavePathWithIdentifierV2:kDownloadIdentifierV2] stringByAppendingPathComponent:self.fileName];
    if([fileManager fileExistsAtPath:v2Path]) {
        return v2Path;
    }
    
    return NULL;
}

// 根据identifier获取缓存目录
+ (NSString*)_diskSavePathWithIdentifierV2:(NSString *)identifier
{
    //SystemUTIsHLSV2
//    NSString* cacheName = [NSString stringWithFormat:@"com.Daniels.Tiercel.Cache.%@", identifier];
//    NSString* diskCachePath = [[self defaultDiskCachePath] stringByAppendingPathComponent:cacheName];
    NSString* diskCachePath = [self defaultDiskCachePath];
//    NSString* path = [diskCachePath stringByAppendingPathComponent:@"Downloads"];
//    NSString* downloadPath = [path stringByAppendingPathComponent:@"File"];
    
    if(![[NSFileManager defaultManager] fileExistsAtPath:diskCachePath]) {
        [[NSFileManager defaultManager] createDirectoryAtPath:diskCachePath
                                  withIntermediateDirectories:YES
                                                   attributes:nil
                                                        error:nil];
    }
    
    return diskCachePath;
}

+ (NSString *)defaultDiskCachePath
{
    NSString* dstPath = [NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, true) firstObject];
    return dstPath;
}

+ (NSString*)_diskSavePathWithIdentifierV3:(NSString *)identifier
{
    //SystemUTIsHLSV3
//    NSString* cacheName = [NSString stringWithFormat:@"com.Daniels.Tiercel.Cache.%@", identifier];
//    NSString* diskCachePath = [[self customDiskCachePath] stringByAppendingPathComponent:cacheName];
    NSString* diskCachePath = [self customDiskCachePath];
//    NSString* path = [diskCachePath stringByAppendingPathComponent:@"Downloads"];
//    NSString* downloadPath = [path stringByAppendingPathComponent:@"File"];
    
    if(![[NSFileManager defaultManager] fileExistsAtPath:diskCachePath]) {
        [[NSFileManager defaultManager] createDirectoryAtPath:diskCachePath
                                  withIntermediateDirectories:YES
                                                   attributes:nil
                                                        error:nil];
    }
    
    NSLog(@"测试: path = %@", diskCachePath);
    
    return diskCachePath;
}

+ (NSString *)customDiskCachePath
{
    NSString* dstPath = [NSSearchPathForDirectoriesInDomains(NSLibraryDirectory, NSUserDomainMask, true) firstObject];
    dstPath = [dstPath stringByAppendingPathComponent:@"Focus"];
    return dstPath;
}

+ (NSArray<NSString *> *)getAllFiles
{
    NSString* kDownloadIdentifierV2 = @"com.browser.downloadmodule";
    NSString* kDownloadIdentifierV3 = @"v3.com.browser.downloadmodule";
    
    NSMutableArray* fileArray = [NSMutableArray array];
    NSString* v3Path = [TempModel _diskSavePathWithIdentifierV3:kDownloadIdentifierV3];
    NSString* v2Path = [TempModel _diskSavePathWithIdentifierV2:kDownloadIdentifierV2];
    
    NSArray* v3Files = traverseDirectoryAndGetMP4Files(v3Path);
    NSArray* v2Files = traverseDirectoryAndGetMP4Files(v2Path);
    
    [fileArray addObjectsFromArray:v3Files];
    [fileArray addObjectsFromArray:v2Files];
    
    return fileArray;
}

// 递归遍历目录，返回所有 .mp4 文件路径的数组
NSArray* traverseDirectoryAndGetMP4Files(NSString *directoryPath) {
    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSMutableArray *mp4Files = [NSMutableArray array];
    
    // 获取目录下的所有文件和子目录
    NSError *error = nil;
    NSArray *contents = [fileManager contentsOfDirectoryAtPath:directoryPath error:&error];
    
    if (error) {
        NSLog(@"Error reading directory: %@", error.localizedDescription);
        return nil;
    }
    
    // 遍历所有文件和子目录
    for (NSString *item in contents) {
        NSString *itemPath = [directoryPath stringByAppendingPathComponent:item];
        BOOL isDirectory;
        
        // 判断当前项是否是目录
        if ([fileManager fileExistsAtPath:itemPath isDirectory:&isDirectory]) {
            if (isDirectory) {
                // 如果是目录，递归调用遍历
                NSArray *subDirMP4Files = traverseDirectoryAndGetMP4Files(itemPath);
                [mp4Files addObjectsFromArray:subDirMP4Files];
            } else {
                // 如果是文件，检查是否是MP4文件
                if ([itemPath.pathExtension isEqualToString:@"mp4"]) {
                    [mp4Files addObject:itemPath];
                }
            }
        }
    }
    
    return mp4Files;
}

@end
