//
//  SnifferHelper.h
//  PPBrowser
//
//  Created by qingbin on 2022/11/24.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>

#import "NSURL+Extension.h"
#import "NSString+Helper.h"
#import "NSObject+Helper.h"

#import "MaizyHeader.h"
#import "ReactiveCocoa.h"

#import "Tab.h"
#import "PPEnums.h"
#import "SnifferModel.h"

/**
 WKWebView 请求拦截探索与实践
 https://segmentfault.com/a/1190000039111381
 */

//参考《腾讯云视频防盗链》可知, referer的命名规则是:
//域名前不要带协议名（http://和https://），域名为前缀匹配（如填写abc.com，则abc.com/123和abc.com.cn也会匹配），且支持通配符（*.abc.com）。
//所以这里只取abc.com这部分
//https://zhuanlan.zhihu.com/p/319611102
//https://cloud.tencent.com/document/product/266/14046

//https://developer.mozilla.org/zh-CN/docs/Web/HTTP/Headers/Referer
//https://developer.mozilla.org/zh-CN/docs/Web/HTTP/Headers/Referrer-Policy
//referer的策略参考链接
/*只有3钟情况：
第一种,不需要传referer
第二种,只需要传origin
第三种,只需要传全路径(去除?以及后面的参数)
*/


/// originUrl - 当前网页的url
/// videoUrl  - 音视频的url
/// 嗅探的策略
///
//SnifferPolicy0 = 0,     /// referer: originUrl的domain
//SnifferPolicy1,         /// referer不传
//SnifferPolicy2,         /// referer: originUrl的全部
//SnifferPolicy3,         /// referer: http(s)://domain, 即origin， 靠,missav会检测这个https请求协议
//SnifferPolicy4,         /// 什么都不传

@interface SnifferHelper : NSObject

+ (instancetype)shareInstance;

/// originUrl - 当前网页的url
/// videoUrl  - 音视频的url
- (void)snifferWithOriginUrl:(NSString *)originUrl
                    videoUrl:(NSString *)videoUrl
                  completion:(void(^)(SnifferModel *))completion;

/// 为了加快播放模式的播放速度，如果没有拿到请求头，直接返回SnifferPolicy0
- (void)asycSnifferWithOriginUrl:(NSString *)originUrl
                        videoUrl:(NSString *)videoUrl
                      completion:(void(^)(SnifferModel *))completion;


// 从url scheme/decidePolicyForNavigationAction拦截到的requestHeader
- (void)updateRequestHeader:(NSDictionary *)requestHeader forUrl:(NSString *)url;
// url scheme获取到requestHeader, 针对blob加密链接，因此用网站链接来保存
- (void)updateRequestHeader:(NSDictionary *)requestHeader forOriginUrl:(NSString *)originUrl;

// 根据url(如果是iframe嵌套的形式,那么则会是包含的形式)
- (NSDictionary *)getRequestHeaderForUrl:(NSString *)url;

@end


/*
 Host 和 Origin 是 HTTP 请求中常见的两个头部字段，它们有不同的作用和含义。

 Host 是用于标识目标服务器的头部字段。它指示客户端正在请求的目标主机的域名或 IP 地址。通常，客户端会在请求中包含 Host 头部，以便服务器可以确定客户端要访问的目标主机。例如，对于网站 www.example.com 的请求，其 Host 头部将包含值 "www.example.com"。

 Origin 是指请求来源的头部字段。它指示客户端发起请求的源地址，包括协议、主机名和端口号。通常，客户端会在跨域请求时包含 Origin 头部，以便服务器可以识别请求的来源，从而确定是否允许跨域访问。例如，对于从 "https://www.example.com" 网站发起的 AJAX 请求，其 Origin 头部将包含值 "https://www.example.com"。

 虽然 Host 和 Origin 都包含了主机信息，但它们的作用和含义有所不同。Host 是用于标识目标服务器的头部字段，用于路由请求到正确的服务器或虚拟主机。而 Origin 是用于识别请求来源的头部字段，用于确保跨域请求的安全性。
 
 
 */
