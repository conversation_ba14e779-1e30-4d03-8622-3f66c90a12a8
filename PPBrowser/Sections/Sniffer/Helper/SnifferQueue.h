//
//  SnifferQueue.h
//  PPBrowser
//
//  Created by qingbin on 2022/11/28.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "SnifferModel.h"

@interface SnifferQueue : NSObject

+ (instancetype)new NS_UNAVAILABLE;
- (instancetype)init NS_UNAVAILABLE;

+ (instancetype)shareInstance;

- (void)putItem:(SnifferModel *)item;

//注意这里sync和async返回的条件是不一样的
//这里只有status==1即处理成功才会返回
- (SnifferModel *)getSyncWithUrl:(NSString *)url;

//asyc则无脑返回
- (SnifferModel *)getAsyncWithUrl:(NSString *)url;

- (void)flush;

- (void)destroy;

@end

