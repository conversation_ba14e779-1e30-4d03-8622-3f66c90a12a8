//
//  SnifferHelper.m
//  PPBrowser
//
//  Created by qingbin on 2022/11/24.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "SnifferHelper.h"

#import "AFNetworking.h"
#import "AFNetworkReachabilityManager.h"
#import "NSString+Helper.h"

#import "SnifferQueue.h"
#import "SnifferModel.h"
#import "NSURL+Extension.h"

#import "UserAgentManager.h"
#import "InternalURL.h"

@interface SnifferHelper ()

@property (nonatomic, strong) dispatch_queue_t queue;

@property (nonatomic, strong) NSMutableDictionary* requestHeaderMap;
//针对blob链接
@property (nonatomic, strong) NSMutableDictionary* originUrlForRequestHeaderMap;;

@end

@implementation SnifferHelper

+ (instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    static SnifferHelper* obj;
    dispatch_once(&onceToken, ^{
        obj = [SnifferHelper new];
    });
    
    return obj;
}

- (instancetype)init
{
    self = [super init];
    if(self) {
        self.queue = dispatch_queue_create("com.sniffer.queue", DISPATCH_QUEUE_CONCURRENT);
        self.requestHeaderMap = [NSMutableDictionary dictionary];
        self.originUrlForRequestHeaderMap = [NSMutableDictionary dictionary];
    }
    
    return self;
}

/// 为了加快播放模式的播放速度，如果没有拿到请求头，直接返回SnifferPolicy0
- (void)asycSnifferWithOriginUrl:(NSString *)originUrl
                        videoUrl:(NSString *)videoUrl
                      completion:(void(^)(SnifferModel*))completion
{
    //这里是特别给播放模式调用,为了快速返回
    //为了加快访问速度，如果还没有获取到策略，header取策略0
    @weakify(self)
    dispatch_async(self.queue, ^{
        @strongify(self)
        
        NSDictionary* requestHeader = [self getRequestHeaderForUrl:videoUrl];
        if(!requestHeader) {
            //可能是blob链接，因此从网址链接中获取
            requestHeader = [self getRequestHeaderForOriginUrl:originUrl];
        }
        
        if(requestHeader) {
            SnifferModel* item = [SnifferModel new];
            item.requestHeader = requestHeader;
            item.url = videoUrl;
            item.policy = SnifferPolicy5;
            item.status = 1;
            
            dispatch_async(dispatch_get_main_queue(), ^{                
                if(completion) {
                    completion(item);
                }
            });
            return;
        }
        
        ///取缓存
        SnifferModel* item = [[SnifferQueue shareInstance] getAsyncWithUrl:videoUrl];
        int status = -1;
        @synchronized (self) {
            if(item) {
                status = item.status;
            }
        }
        
        if(status == 1) {
            //有缓存
            dispatch_async(dispatch_get_main_queue(), ^{
                if(completion) {
                    completion(item);
                }
            });
            return;
        } else {
            //等待中/新任务状态,直接返回
            NSDictionary* policyHeader = [self _getRequestHeaderWithOriginUrl:originUrl
                                                                     videoUrl:videoUrl
                                                                       policy:SnifferPolicy4
                                                                  appendRange:NO];
            dispatch_async(dispatch_get_main_queue(), ^{
                if(completion) {
                    SnifferModel* item = [SnifferModel new];
                    item.requestHeader = policyHeader;
                    completion(item);
                }
            });
            return;
        }
    });
}

#pragma mark -- 阻塞请求
- (void)snifferWithOriginUrl:(NSString *)originUrl
                    videoUrl:(NSString *)videoUrl
                  completion:(void(^)(SnifferModel*))completion
{
    @weakify(self)
    dispatch_async(self.queue, ^{
        @strongify(self)
        [self _asyncSnifferWithOriginUrl:originUrl
                                videoUrl:videoUrl
                              completion:completion];
    });
}

- (void)_asyncSnifferWithOriginUrl:(NSString *)originUrl
                          videoUrl:(NSString *)videoUrl
                        completion:(void(^)(SnifferModel*))completion
{
    ///url无效
    if(videoUrl.length <= 0) {
        //这种情况传空
        NSDictionary* header = [self _getRequestHeaderWithOriginUrl:originUrl
                                                           videoUrl:videoUrl
                                                             policy:SnifferPolicy4
                                                        appendRange:NO];
        dispatch_async(dispatch_get_main_queue(), ^{
            if(completion) {
                SnifferModel* item = [SnifferModel new];
                item.requestHeader = header;
                completion(item);
            }
        });
        return;
    }
    
    NSDictionary* requestHeader = [self getRequestHeaderForUrl:videoUrl];
    if(!requestHeader) {
        //可能是blob链接，因此从网址链接中获取
        requestHeader = [self getRequestHeaderForOriginUrl:originUrl];
    }
    
    if(requestHeader) {
        //从url scheme中获取到了值，直接构造结果返回
        SnifferModel* item = [SnifferModel new];
        item.requestHeader = requestHeader;
        item.url = videoUrl;
        item.policy = SnifferPolicy5;
        item.status = 1;
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(completion) {
                completion(item);
            }
        });
        return;
    }
    
    ///取缓存
    SnifferModel* item = [[SnifferQueue shareInstance] getAsyncWithUrl:videoUrl];
    int status = -1;
    @synchronized (self) {
        if(item) {
            status = item.status;
        }
    }
    
    if(status == 1) {
        //有缓存
        dispatch_async(dispatch_get_main_queue(), ^{
            if(completion) {
                completion(item);
            }
        });
        return;
    } else if(status == 0) {
        //正在处理中,进入睡眠状态
        SnifferModel* obj = [[SnifferQueue shareInstance] getSyncWithUrl:videoUrl];
        //睡眠结束
        dispatch_async(dispatch_get_main_queue(), ^{
            if(completion) {
                completion(obj);
            }
        });
        return;
    } else {
        //开启新任务
        //SnifferPolicy0成功的概率是最大的,所以先尝试请求SnifferPolicy0策略。
        //先尝试SnifferPolicy0是否成功,如果不成功,那么则同时并发测试其它4个策略。
        
        SnifferModel* obj = [SnifferModel new];
        obj.status = 0;
        obj.url = videoUrl;
        obj.requestHeader = nil;
        obj.data = nil;
        obj.policy = INT_MAX;
        
        [[SnifferQueue shareInstance] putItem:obj];
        
        if(completion) {
            dispatch_async(self.queue, ^{
                //正在处理中,进入睡眠状态
                SnifferModel* obj = [[SnifferQueue shareInstance] getSyncWithUrl:videoUrl];
                //睡眠结束
                dispatch_async(dispatch_get_main_queue(), ^{
                    completion(obj);
                });
            });
        }
        
        [self _trySnifferWithOriginUrl:originUrl
                              videoUrl:videoUrl
                          snifferModel:obj];
    }
}

- (void)_trySnifferWithOriginUrl:(NSString *)originUrl
                        videoUrl:(NSString *)videoUrl
                    snifferModel:(SnifferModel *)snifferModel
{
    __block int completionCount = 0;
    //v2.1.6.0修改, 并发请求所有策略
    NSArray* policies = @[@(SnifferPolicy0),@(SnifferPolicy1),@(SnifferPolicy2),@(SnifferPolicy3),@(SnifferPolicy4)];
    for(int i=0;i<policies.count;i++) {
        SnifferPolicy policy = [policies[i] intValue];
        NSDictionary* header = [self _getRequestHeaderWithOriginUrl:originUrl
                                                           videoUrl:videoUrl
                                                             policy:policy
                                                        appendRange:YES];
        
//        {
//            //2.6.2 这里强制加Range, 防止第一帧数据太大,如果不加上Range,那么加载时间会非常大
//            NSMutableDictionary* requestHeader = [NSMutableDictionary dictionary];
//            [requestHeader addEntriesFromDictionary:header];
//            [requestHeader addEntriesFromDictionary:@{
//                @"Range": @"bytes=0-1"
//            }];
//            
//            header = [requestHeader copy];
//        }

        @weakify(self)
        [NSObject requestForHeader:header url:videoUrl completion:^(BOOL succ, NSDictionary *header, id data) {
            @strongify(self)
            @synchronized (self) {
                completionCount++;
            }
            if(succ) {
                //需要移除测试的range
                NSMutableDictionary* requestHeader = [NSMutableDictionary dictionary];
                [requestHeader addEntriesFromDictionary:header];
                [requestHeader removeObjectForKey:@"Range"];
                
                SnifferModel* lastSnifferModel = [[SnifferQueue shareInstance] getAsyncWithUrl:videoUrl];
                if (lastSnifferModel && lastSnifferModel.status == 1 && lastSnifferModel.policy == SnifferPolicy4) {
                    //多个成功的, nil的优先级最高
                } else {
                    snifferModel.requestHeader = requestHeader;
                    snifferModel.status = 1;
                    snifferModel.data = data;
                    snifferModel.policy = policy;
                    [[SnifferQueue shareInstance] putItem:snifferModel];
                }
            } else {
                if(completionCount >= policies.count) {
                    //5个都是失败,取SnifferPolicy3
//                    snifferModel.requestHeader = nil;
                    snifferModel.requestHeader = [self _getRequestHeaderWithOriginUrl:originUrl videoUrl:videoUrl policy:SnifferPolicy3 appendRange:NO];
                    snifferModel.status = 1;
                    snifferModel.data = data;
                    [[SnifferQueue shareInstance] putItem:snifferModel];
                }
            }
        }];
    }
}

#pragma mark -- 根据policy获取对应的请求头
- (NSDictionary *)_getRequestHeaderWithOriginUrl:(NSString *)originUrl
                                        videoUrl:(NSString *)videoUrl
                                          policy:(SnifferPolicy)policy
                                     appendRange:(BOOL)appendRange
{
    if(policy == SnifferPolicy4) {
        //这里要注意，大文件mp4的情况下，会导致内存达到2.05G，然后崩溃
        NSMutableDictionary* requestHeader = [NSMutableDictionary dictionary];
        if(appendRange) {
            //v2.6.1 防止第一帧数据太大,如果不加上Range,那么加载时间会非常大
            requestHeader[@"Range"] = @"bytes=0-1";
        } else {
            return nil;
        }
        
        return requestHeader;
    }
    
    NSMutableDictionary* requestHeader = [NSMutableDictionary dictionary];
    
    NSURL* originURL = [NSURL URLWithString:originUrl];
    NSURL* videoURL = [NSURL URLWithString:videoUrl];
    
    NSString* userAgent = [[UserAgentManager shareInstance] getCurrentUserAgent];
    NSString* host = videoURL.host;
    NSString *origin = [NSString stringWithFormat:@"%@://%@", originURL.scheme, originURL.host];
    NSString* referer = nil;
    
    if(appendRange) {
        //防止第一帧数据太大,如果不加上Range,那么加载时间会非常大
        requestHeader[@"Range"] = @"bytes=0-1";
    }
    
    if(videoURL.host.length > 0) {
        requestHeader[@"Host"] = host;
    }
    
    requestHeader[@"User-Agent"] = userAgent;
        
/*
 SnifferPolicy0 = 0,     /// referer: originUrl的domain
 SnifferPolicy1,         /// referer不传
 SnifferPolicy2,         /// referer: originUrl的全部
 SnifferPolicy3,         /// referer: http(s)://domain, 即origin
 SnifferPolicy4,         /// 什么都不传
 */
    
    NSString* domain = [originURL normalizedHost];
    if(policy == SnifferPolicy0) {
        referer = domain;
    } else if(policy == SnifferPolicy1) {
        referer = nil;
    } else if(policy == SnifferPolicy2) {
        referer = originUrl;
    } else if(policy == SnifferPolicy3) {
        referer = origin;
    }
    
    if(referer.length > 0) {
        requestHeader[@"Referer"] = referer;
    }
    
    if(origin.length > 0) {
        requestHeader[@"Origin"] = origin;
    }
    
    requestHeader[@"Accept"] = @"*/*";
    requestHeader[@"Accept-Language"] = @"zh-CN,zh-Hans;q=0.9";
    requestHeader[@"Connection"] = @"keep-alive";
    requestHeader[@"Accept-Encoding"] = @"gzip, deflate, br";
    
    /*
     Accept-Encoding 的取值会根据以下几种情况变化：
     1、gzip, deflate, br - 标准压缩模式：
         正常网络条件下
         浏览器支持压缩
         没有特殊限制时的默认值
     2、identity - 不压缩模式：
         当请求范围下载(Range requests)时
         当浏览器需要精确控制内容长度时
         某些流媒体协议要求不压缩
         某些视频分片请求(如 HLS/DASH)需要原始数据
     */
    if([videoUrl rangeOfString:@".m4a"].location != NSNotFound
       || [videoUrl rangeOfString:@".mp4"].location != NSNotFound
       || [videoUrl rangeOfString:@".mp3"].location != NSNotFound) {
        requestHeader[@"Accept-Encoding"] = @"identity";
    }
    
    return requestHeader;
}

#pragma mark -- 提前获取
// 从decidePolicyForNavigationAction拦截到的requestHeader
- (void)updateRequestHeader:(NSDictionary *)requestHeader forUrl:(NSString *)url
{
    if(url.length == 0 || !requestHeader) return;
    if(![NSURL URLWithString:url]) return;
    if([InternalURL isValid:[NSURL URLWithString:url]]) return;
    //v2.6.2 针对于avbebe.com/archives/233326，会接收到两次请求体的响应，第二次的请求体是无效的
//    if (self.requestHeaderMap[url]) return;
    //v2.6.3 发现有效的请求头，都是带有X-Playback-Session-Id，因此根据这个来判断
    if (self.requestHeaderMap[url]) {
        //有缓存，判断是否有X-Playback-Session-Id，有则更新
        if (requestHeader[@"X-Playback-Session-Id"]) {
            //更新
            self.requestHeaderMap[url] = requestHeader;
        }
    } else {
        //没有缓存
        self.requestHeaderMap[url] = requestHeader;
    }
}

// 根据url(如果是iframe嵌套的形式,那么则会是包含的形式)
- (NSDictionary *)getRequestHeaderForUrl:(NSString *)url
{
    if(url.length == 0) return nil;
    __block NSDictionary* header = nil;
    [self.requestHeaderMap enumerateKeysAndObjectsUsingBlock:^(NSString*  _Nonnull key, NSDictionary*  _Nonnull obj, BOOL * _Nonnull stop) {
        if([key rangeOfString:url].location != NSNotFound) {
            header = obj;
            *stop = YES;
        }
    }];
    
    return header;
}

// url scheme获取到requestHeader, 针对blob加密链接，因此用网站链接来保存
- (void)updateRequestHeader:(NSDictionary *)requestHeader forOriginUrl:(NSString *)originUrl
{
    if(originUrl.length == 0) return;
    if([InternalURL isValid:[NSURL URLWithString:originUrl]]) return;
    
    //v2.6.3 发现有效的请求头，都是带有X-Playback-Session-Id，因此根据这个来判断
    if (self.originUrlForRequestHeaderMap[originUrl]) {
        //有缓存，判断是否有X-Playback-Session-Id，有则更新
        if (requestHeader[@"X-Playback-Session-Id"]) {
            //更新
            self.originUrlForRequestHeaderMap[originUrl] = requestHeader;
        }
    } else {
        //没有缓存
        self.originUrlForRequestHeaderMap[originUrl] = requestHeader;
    }
}

- (NSDictionary *)getRequestHeaderForOriginUrl:(NSString *)originUrl
{
    if(originUrl.length == 0) return nil;
    __block NSDictionary* header = nil;
    [self.originUrlForRequestHeaderMap enumerateKeysAndObjectsUsingBlock:^(NSString*  _Nonnull key, NSDictionary*  _Nonnull obj, BOOL * _Nonnull stop) {
        if([key rangeOfString:originUrl].location != NSNotFound) {
            header = obj;
            *stop = YES;
        }
    }];
    
    return header;
}

@end
