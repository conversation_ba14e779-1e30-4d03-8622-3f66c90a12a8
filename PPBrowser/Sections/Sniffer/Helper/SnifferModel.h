//
//  SnifferModel.h
//  PPBrowser
//
//  Created by qingbin on 2022/11/28.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "PPEnums.h"
#import "BaseModel.h"

@class JsSnifferModel;

@interface SnifferModel : NSObject
//优先级
@property (nonatomic, assign) SnifferPolicy policy;
//视频url
@property (nonatomic, strong) NSString *url;
//请求头
@property (nonatomic, strong) NSDictionary *requestHeader;
//0:处理中, 1:处理完成
@property (nonatomic, assign) int status;
//视频的数据
@property (nonatomic, strong) id data;
//session id
@property (nonatomic, strong) NSString* sessionId;
//
@property (nonatomic, strong) JsSnifferModel *jsModel;

@end


//v2.6.3 js中获取到的相关信息
@interface JsSnifferModel : BaseModel
//User-Agent
@property (nonatomic, strong) NSString *userAgent;
//
@property (nonatomic, strong) NSString *acceptLanguage;
//是否跨域
@property (nonatomic, assign) BOOL isInIframe;
//
@property (nonatomic, strong) NSString *url;
//
@property (nonatomic, strong) NSString *origin;
//
@property (nonatomic, strong) NSString *acceptEncoding;

@end
