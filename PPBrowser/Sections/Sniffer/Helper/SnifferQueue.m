//
//  SnifferQueue.m
//  PPBrowser
//
//  Created by qingbin on 2022/11/28.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "SnifferQueue.h"

#import "ReactiveCocoa.h"
#import "PPEnums.h"
#import "NSString+MKNetworkKitAdditions.h"
#import "NSURL+Extension.h"

@interface SnifferQueue()

@property (nonatomic, strong) NSCondition * condition;

@property (nonatomic, strong) NSMutableDictionary *caches;

@property (nonatomic, assign) BOOL destoryToken;

@end

@implementation SnifferQueue

+ (instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    static SnifferQueue* obj;
    dispatch_once(&onceToken, ^{
        obj = [[SnifferQueue alloc]init];
    });

    return obj;
}

- (instancetype)init
{
    self = [super init];
    if(self) {
        self.condition = [[NSCondition alloc]init];
    }
    
    return self;
}

- (void)putItem:(SnifferModel *)item
{
    if(!item) return;
    [self.condition lock];
    
    if(self.destoryToken) {
        [self.condition unlock];
        return;
    }
    
    NSString* key = [item.url md5];
    SnifferModel* obj = self.caches[key];
    if(obj) {
        //已经有数据, 比较优先级
        if(obj.policy >= item.policy) {
            //优先级比较高才更新
            self.caches[key] = item;
        } else {
            //优先级比较低，不更新
        }
    } else {
        //没有数据，直接更新
        self.caches[key] = item;
    }
    
    [self.condition broadcast];
    [self.condition unlock];
}

- (SnifferModel *)getSyncWithUrl:(NSString *)url
{
    [self.condition lock];
    
    SnifferModel* item;
    BOOL succ = NO;
    while (!succ) {
        if(self.destoryToken) {
            [self.condition unlock];
            return nil;
        }
        
        NSString* key = [url md5];
        item = self.caches[key];
        if(item && item.status == 1) {
            succ = YES;
            break;
        }
        
        [self.condition wait];
    }
    
    [self.condition unlock];
    
    return item;
}

- (SnifferModel *)getAsyncWithUrl:(NSString *)url
{
    [self.condition lock];
    
    NSString* key = [url md5];
    SnifferModel* item = self.caches[key];
    if(self.destoryToken || !item) {
        [self.condition unlock];
        return nil;
    }
    
    [self.condition unlock];
    
    return item;
}

- (void)flush
{
    [self.condition lock];

    [self.caches removeAllObjects];
    self.caches = nil;
    
    [self.condition unlock];
}

- (void)destroy
{
    [self.condition lock];
    
    [self.caches removeAllObjects];
    self.caches = nil;
    
    self.destoryToken = YES;
    [self.condition broadcast];
    [self.condition unlock];
}

- (void)dealloc
{
#if DEBUG
    NSLog(@"%@ release...",[self class]);
#endif
}

- (NSMutableDictionary *)caches
{
    if(!_caches) {
        _caches = [NSMutableDictionary dictionary];
    }
    
    return _caches;
}

@end
