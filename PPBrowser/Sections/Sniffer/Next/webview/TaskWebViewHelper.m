//
//  TaskWebViewHelper.m
//  PPBrowser
//
//  Created by qingbin on 2024/7/17.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import "TaskWebViewHelper.h"
#import <WebKit/WebKit.h>

#import "ReactiveCocoa.h"
#import "TaskWebViewHandler.h"

@interface TaskWebViewHelper ()

@property (nonatomic, copy) void (^completion)(TaskWebViewModel* model);

@property (nonatomic, strong) dispatch_queue_t queue;

@property (nonatomic, strong) NSMutableDictionary* handlerMap;

@end

@implementation TaskWebViewHelper

+ (instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    static TaskWebViewHelper* obj;
    dispatch_once(&onceToken, ^{
        obj = [TaskWebViewHelper new];
    });
    
    return obj;
}

- (instancetype)init
{
    self = [super init];
    if(self) {
        self.queue = dispatch_queue_create("com.task.webview.queue", DISPATCH_QUEUE_CONCURRENT);
    }
    
    return self;
}

#pragma mark -- 加载请求
- (void)loadWithUrl:(NSString *)url
         completion:(void (^)(TaskWebViewModel* model))completion
{
    if(!url) {
        TaskWebViewModel* model = [TaskWebViewModel new];
        model.succ = NO;
        
        if(completion) {
            completion(model);
        }
        
        return;
    }
    self.completion = completion;
    
    dispatch_async(self.queue, ^{
        TaskWebViewHandler* handler = [TaskWebViewHandler new];
        [handler loadWithUrl:url completion:^(TaskWebViewModel *model) {
            if(completion) {
                completion(model);
            }
            
            @autoreleasepool {
                //释放内存
                @synchronized (self) {
                    [self.handlerMap removeObjectForKey:url];
                }
            }
        }];
        
        @synchronized (self) {
            //强引用，防止释放
            if(url) {
                self.handlerMap[url] = handler;
            }
        }
    });
}

#pragma mark -- Getters

- (NSMutableDictionary *)handlerMap
{
    if(!_handlerMap) {
        _handlerMap = [NSMutableDictionary dictionary];
    }
    
    return _handlerMap;
}

@end
