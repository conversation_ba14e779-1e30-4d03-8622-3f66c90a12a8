//
//  TaskWebViewHandler.m
//  PPBrowser
//
//  Created by qingbin on 2024/7/19.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import "TaskWebViewHandler.h"

#import <WebKit/WebKit.h>

#import "ReactiveCocoa.h"

@interface TaskWebViewHandler ()<WKURLSchemeHandler>

@property (nonatomic, strong) WKWebView* webView;

@property (nonatomic, strong) TaskWebViewModel* model;

@property (nonatomic, strong) NSString* originalScheme;

@property (nonatomic, strong) NSDictionary* requestHeader;

@property (nonatomic, copy) void (^completion)(TaskWebViewModel* model);

@end

@implementation TaskWebViewHandler

- (void)loadWithUrl:(NSString *)url
         completion:(void (^)(TaskWebViewModel* model))completion
{
    self.model.url= url;
    self.completion = completion;
    
    NSString* replaceUrl = [self replaceSchemeWithInternal:url];
    NSURL* URL = [NSURL URLWithString:replaceUrl];
    if(!URL) {
        self.model.succ = NO;
        if(completion) {
            completion(self.model);
        }
        
        return;
    }
    
    NSURLRequest* request = [NSURLRequest requestWithURL:URL];
    //必须主线程，否则会报错
    dispatch_async(dispatch_get_main_queue(), ^{
        [self.webView loadRequest:request];
    });
}

#pragma mark -- 替换http/https为internal
- (NSString *)replaceSchemeWithInternal:(NSString *)urlString
{
    NSURL *url = [NSURL URLWithString:urlString];
    NSString *originalScheme = [url scheme];
    self.originalScheme = originalScheme;
    
    NSURLComponents *urlComponents = [NSURLComponents componentsWithURL:url resolvingAgainstBaseURL:NO];
    urlComponents.scheme = @"internal";

    NSURL *internalURL = urlComponents.URL;
    return internalURL.absoluteString;
}

#pragma mark -- WKURLSchemeHandler

- (void)webView:(WKWebView *)webView startURLSchemeTask:(id <WKURLSchemeTask>)urlSchemeTask
{
    NSURL *url = urlSchemeTask.request.URL;
    if (!url) {
        self.model.succ = NO;
        if(self.completion) {
            self.completion(self.model);
        }
        
        return;
    }

    // 替换自定义 Scheme 为 http/https
    NSURLComponents *urlComponents = [NSURLComponents componentsWithURL:url resolvingAgainstBaseURL:NO];
    urlComponents.scheme = self.originalScheme;

    NSURL *originalURL = urlComponents.URL;
    if (!originalURL) {
        self.model.succ = NO;
        if(self.completion) {
            self.completion(self.model);
        }
        
        return;
    }

    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:originalURL];
    request.allHTTPHeaderFields = urlSchemeTask.request.allHTTPHeaderFields;
    self.requestHeader = request.allHTTPHeaderFields;

    NSURLSession *session = [NSURLSession sharedSession];
    @weakify(self)
    NSURLSessionDataTask *dataTask = [session dataTaskWithRequest:request completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {
        @strongify(self)
        if (error) {
            [urlSchemeTask didFailWithError:error];
            
            self.model.succ = NO;
            if(self.completion) {
                self.completion(self.model);
            }
            return;
        }

        self.model.succ = YES;
        self.model.requestHeader = self.requestHeader;
        
        if(data.length > 0) {
            NSString* resultText = [[NSString alloc]initWithData:data encoding:NSUTF8StringEncoding];
            
            self.model.resultText = resultText;
            
            if(self.completion) {
                self.completion(self.model);
            }
        } else {
            if(self.completion) {
                self.completion(self.model);
            }
        }
        
        if (response) {
            [urlSchemeTask didReceiveResponse:response];
        }

        if (data) {
            [urlSchemeTask didReceiveData:data];
        }

        [urlSchemeTask didFinish];
    }];
    [dataTask resume];
    
    NSLog(@"task custom scheme: %s, headers = %@", __func__, request.allHTTPHeaderFields);
}

- (void)webView:(WKWebView *)webView stopURLSchemeTask:(id <WKURLSchemeTask>)urlSchemeTask
{
    
}

#pragma mark -- Getters

- (WKWebView *)webView
{
    if(!_webView) {
        WKWebViewConfiguration *configuration = [WKWebViewConfiguration new];
        configuration.userContentController = [WKUserContentController new];
        configuration.preferences = [WKPreferences new];
        configuration.preferences.javaScriptCanOpenWindowsAutomatically = false;
        configuration.allowsInlineMediaPlayback = YES;
        // Enables Zoom in website by ignoring their javascript based viewport Scale limits.
        configuration.ignoresViewportScaleLimits = YES;
        
        if(![configuration urlSchemeHandlerForURLScheme:@"internal"]) {
            [configuration setURLSchemeHandler:self forURLScheme:@"internal"];
        }
        
        _webView = [[WKWebView alloc]initWithFrame:CGRectMake(0, 0, 10, 10) configuration:configuration];

        //iOS16.4之后，需要设置该属性才能调试web页
        //https://blog.csdn.net/siwen1990/article/details/130363477
    #if DEBUG
        if(@available(iOS 16.4, *)) {
        #if __IPHONE_OS_VERSION_MAX_ALLOWED >= 160400
            // 在 iOS 16.4 或更高版本下会执行这里的代码
            self.webView.inspectable = YES;
        #endif
        }
    #endif
    }
    
    return _webView;
}

- (TaskWebViewModel *)model
{
    if(!_model) {
        _model = [TaskWebViewModel new];
    }
    
    return _model;
}

@end
