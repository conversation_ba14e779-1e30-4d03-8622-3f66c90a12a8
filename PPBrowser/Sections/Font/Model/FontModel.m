//
//  FontModel.m
//  PPBrowser
//
//  Created by qingbin on 2023/5/30.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import "FontModel.h"

@implementation FontModel

- (NSString *)title
{
    if(!_title) {
        if(self.fontSize == FontSize50) {
            _title = @"50%";
        } else if(self.fontSize == FontSize75) {
            _title = @"75%";
        } else if(self.fontSize == FontSize85) {
            _title = @"85%";
        } else if(self.fontSize == FontSize100) {
            _title = @"100%";
        } else if(self.fontSize == FontSize115) {
            _title = @"115%";
        } else if(self.fontSize == FontSize125) {
            _title = @"125%";
        } else if(self.fontSize == FontSize150) {
            _title = @"150%";
        } else if(self.fontSize == FontSize175) {
            _title = @"175%";
        } else if(self.fontSize == FontSize200) {
            _title = @"200%";
        } else {
            _title = @"100%";
        }
    }
    
    return _title;
}

+ (int)fontValueWithSize:(FontSize)size
{    
    if(size == FontSize50) {
        return 50;
    } else if(size == FontSize75) {
        return 75;
    } else if(size == FontSize85) {
        return 85;
    } else if(size == FontSize100) {
        return 100;
    } else if(size == FontSize115) {
        return 115;
    } else if(size == FontSize125) {
        return 125;
    } else if(size == FontSize150) {
        return 150;
    } else if(size == FontSize175) {
        return 175;
    } else if(size == FontSize200) {
        return 200;
    }
    
    return 100;
}

@end
