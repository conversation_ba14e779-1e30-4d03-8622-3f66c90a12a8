//
//  FontStepView.m
//  PPBrowser
//
//  Created by qingbin on 2023/5/31.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import "FontStepView.h"

#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "UIView+FrameHelper.h"
#import "NSObject+Helper.h"

#import "ThemeProtocol.h"
#import "PreferenceManager.h"
#import "BrowserUtils.h"

#import "BottomToolbar.h"
#import "TopToolbar.h"
#import "FontModel.h"

#import "PPNotifications.h"

#import "UIImage+Extension.h"

@interface FontStepView ()

@property (nonatomic, strong) UIView *backView;

@property (nonatomic, strong) UIStackView *stackView;

@property (nonatomic, strong) UIButton *decreaseBtn;
 
@property (nonatomic, strong) UIView *leftLine;

@property (nonatomic, strong) UIButton *textBtn;

@property (nonatomic, strong) UIView *rightLine;

@property (nonatomic, strong) UIButton *expandBtn;

@property (nonatomic, assign) FontSize fontSize;

@end

@implementation FontStepView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    
    if(self) {
        self.backgroundColor = UIColor.clearColor;
        
        [self addSubviews];
        [self defineLayout];
        [self setupObservers];
    }
    
    return self;
}

+ (void)show
{
    UIWindow* window = [NSObject normalWindow];
    FontStepView* view = [FontStepView new];
    view.alpha = 0;
    [window addSubview:view];
    
    [view mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(window);
    }];
    
    NSNumber* fontSize = [PreferenceManager shareInstance].items.fontSize;
    if(!fontSize) {
        view.fontSize = FontSize100;
    } else {
        view.fontSize = [fontSize intValue];
    }
    
    int value = [FontModel fontValueWithSize:view.fontSize];
    NSString* title = [NSString stringWithFormat:@"%d%%",value];
    [view.textBtn setTitle:title forState:UIControlStateNormal];
    
    [UIView animateWithDuration:0.25 animations:^{
        view.alpha = 1.0;
    }];
}

- (void)hide
{
    [UIView animateWithDuration:0.25 animations:^{
        self.alpha = 0;
    } completion:^(BOOL finished) {
        [self removeFromSuperview];
    }];
}

- (void)updateWithFontSize
{
    [PreferenceManager shareInstance].items.fontSize = @(self.fontSize);
    [[PreferenceManager shareInstance] encode];

    int value = [FontModel fontValueWithSize:self.fontSize];
    NSString* title = [NSString stringWithFormat:@"%d%%",value];
    [self.textBtn setTitle:title forState:UIControlStateNormal];
    
    [[NSNotificationCenter defaultCenter] postNotificationName:kReloadUserScriptNotification object:nil];
}

- (void)setupObservers
{
    UITapGestureRecognizer* tap = [UITapGestureRecognizer new];
    [self addGestureRecognizer:tap];
    
    @weakify(self)
    [tap.rac_gestureSignal subscribeNext:^(id x) {
        @strongify(self)
        [self hide];
    }];
    
    [[self.decreaseBtn rac_signalForControlEvents:UIControlEventTouchUpInside]
    subscribeNext:^(id x) {
        @strongify(self)
        if(self.fontSize == FontSize50) return;
        self.fontSize = self.fontSize - 1;
        
        [self updateWithFontSize];
    }];
    
    [[self.expandBtn rac_signalForControlEvents:UIControlEventTouchUpInside]
    subscribeNext:^(id x) {
        @strongify(self)
        if(self.fontSize == FontSize200) return;
        self.fontSize = self.fontSize + 1;
        
        [self updateWithFontSize];
    }];
    
    [[self.textBtn rac_signalForControlEvents:UIControlEventTouchUpInside]
    subscribeNext:^(id x) {
        @strongify(self)
        self.fontSize = FontSize100;
        
        [self updateWithFontSize];
    }];
    
    UIPanGestureRecognizer* panGesture = [[UIPanGestureRecognizer alloc]initWithTarget:self action:@selector(viewDragged:)];
    [self.backView addGestureRecognizer:panGesture];
}

- (void)viewDragged:(UIPanGestureRecognizer *)gestureRecognizer
{
    //该代码段由ChatGPT生成
    UIView *view = gestureRecognizer.view;
        
    // 获取拖动的偏移量
    CGPoint translation = [gestureRecognizer translationInView:view.superview];
    
    // 移动视图
    view.center = CGPointMake(view.center.x + translation.x, view.center.y + translation.y);
    
    // 重置拖动的偏移量
    [gestureRecognizer setTranslation:CGPointZero inView:view.superview];
}

#pragma mark -- layout
- (void)addSubviews
{
    [self addSubview:self.backView];
    [self.backView addSubview:self.stackView];
}

- (void)defineLayout
{
    CGSize buttonSize = CGSizeMake(75, 45);
    [self.decreaseBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(buttonSize);
    }];
    
    [self.textBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(buttonSize);
    }];
    
    [self.expandBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(buttonSize);
    }];
    
    [self.leftLine mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.bottom.equalTo(self.stackView);
        make.width.mas_equalTo(1);
    }];
    
    [self.rightLine mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.bottom.equalTo(self.stackView);
        make.width.mas_equalTo(1);
    }];
    
    float offset = [BottomToolbar toolbarHeight] + 20;
    [self.backView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self);
        make.bottom.mas_offset(-offset);
    }];
    
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.backView);
    }];
}

- (UIView *)backView
{
    if(!_backView) {
        _backView = [UIView new];
        _backView.backgroundColor = [UIColor colorWithHexString:@"#303030"];
        
        _backView.layer.cornerRadius = 20;
        _backView.layer.masksToBounds = YES;
    }
    
    return _backView;
}

- (UIStackView *)stackView
{
    if(!_stackView) {
        _stackView = [[UIStackView alloc]initWithArrangedSubviews:@[
            self.decreaseBtn,
            self.leftLine,
            self.textBtn,
            self.rightLine,
            self.expandBtn
        ]];
        
        _stackView.spacing = 0;
        _stackView.axis = UILayoutConstraintAxisHorizontal;
    }
    
    return _stackView;
}

- (UIButton *)decreaseBtn
{
    if(!_decreaseBtn) {
        _decreaseBtn = [UIButton new];
        
        UIImage* image = [UIImage ext_systemImageNamed:@"minus"
                                             pointSize:23
                                            renderMode:UIImageRenderingModeAlwaysTemplate];
    
        [_decreaseBtn setImage:image forState:UIControlStateNormal];
        _decreaseBtn.tintColor = UIColor.whiteColor;
    }
    
    return _decreaseBtn;
}

- (UIView *)leftLine
{
    if(!_leftLine) {
        _leftLine = [UIView new];
        _leftLine.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
    
    return _leftLine;
}

- (UIButton *)textBtn
{
    if(!_textBtn) {
        _textBtn = [UIButton new];
        [_textBtn setTitleColor:[UIColor colorWithHexString:@"#ffffff"] forState:UIControlStateNormal];
        _textBtn.titleLabel.font = [UIFont systemFontOfSize:iPadValue(20, 16)];
    }
    
    return _textBtn;
}

- (UIView *)rightLine
{
    if(!_rightLine) {
        _rightLine = [UIView new];
        _rightLine.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
    
    return _rightLine;
}

- (UIButton *)expandBtn
{
    if(!_expandBtn) {
        _expandBtn = [UIButton new];
        UIImage* image = [UIImage ext_systemImageNamed:@"plus"
                                             pointSize:23
                                            renderMode:UIImageRenderingModeAlwaysTemplate];
        
        [_expandBtn setImage:image forState:UIControlStateNormal];
        _expandBtn.tintColor = UIColor.whiteColor;
    }
    
    return _expandBtn;
}

@end

