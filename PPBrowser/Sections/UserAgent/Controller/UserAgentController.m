//
//  UserAgentController.m
//  PPBrowser
//
//  Created by qingbin on 2022/10/3.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "UserAgentController.h"

#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "UIView+FrameHelper.h"
#import "NSObject+Helper.h"

#import "ThemeProtocol.h"
#import "PreferenceManager.h"

#import "UserAgentiPadCell.h"
#import "PPNotifications.h"
#import "BrowserUtils.h"
#import "XLTableView.h"

#import "UIImage+Extension.h"

#import "UserAgentEditController.h"
#import "BaseNavigationController.h"

#import "UserAgentManager.h"
#import "DatabaseUnit+UserAgent.h"

@interface UserAgentController ()<UITableViewDelegate, UITableViewDataSource, ThemeProtocol>

@property (nonatomic, strong) NSMutableArray* model;

@property (nonatomic, strong) UITableView* tableView;
//设置页中的自定义UA
@property (nonatomic, strong) UIImageView* rightImageView;

@end

@implementation UserAgentController

- (void)viewDidLoad
{
    [super viewDidLoad];

    self.navigationItem.title = NSLocalizedString(@"useragent.window.title", nil);
    
    [self createCustomLeftBarButtonItem];
    //设置页中的自定义UA
    [self _createCustomRightBarButtonItem];
    
    [self addSubviews];
    [self defineLayout];
    [self updateWithModel];
}

- (void)updateWithModel
{
    [self.model removeAllObjects];
    
    NSArray* model = [[UserAgentManager shareInstance] model];
    //重置一下状态
    for(UserAgentModel* item in model) {
        item.isFirstInSection = NO;
        item.isLastInSection = NO;
    }
    
    NSArray* items = [[UserAgentManager shareInstance] customUserAgents];
    //自定义UA
    if(items.count > 0) {
        UserAgentModel* item = items.firstObject;
        item.isFirstInSection = YES;
        item = items.lastObject;
        item.isLastInSection = YES;
        [self.model addObject:items];
    }
    
    items = [[UserAgentManager shareInstance] mobileUserAgents];
    UserAgentModel* item = items.firstObject;
    item.isFirstInSection = YES;
    item = items.lastObject;
    item.isLastInSection = YES;
    [self.model addObject:items];
    
    items = [[UserAgentManager shareInstance] pcUserAgents];
    item = items.firstObject;
    item.isFirstInSection = YES;
    item = items.lastObject;
    item.isLastInSection = YES;
    [self.model addObject:items];
    
    [self applyTheme];
    
    [self.tableView reloadData];
}

+ (float)getHeight
{
    UIWindow* window = [NSObject normalWindow];
    float height = iPadValue(30, 10) + iPadValue(80, 60)*2 + 6*iPadValue(88, 60) + window.safeAreaInsets.bottom + 30/*加一点缓冲*/;
    return height;
}

- (void)addUserAgent
{
    UserAgentEditController* vc = [UserAgentEditController new];
    BaseNavigationController* navc = [[BaseNavigationController alloc]initWithRootViewController:vc];
//    if ([BrowserUtils isiPad]) {
//        // iPad
//        navc.modalPresentationStyle = UIModalPresentationFormSheet;
//        vc.preferredContentSize = CGSizeMake(kScreenWidth-90, kScreenHeight-90);
//        
//        [self presentViewController:navc animated:YES completion:nil];
//    } else {
//        // iPhone
//        [self presentViewController:navc animated:YES completion:nil];
//    }
    //v2.6.8,统一present
    [self presentCustomToViewController:navc];
    
    @weakify(self)
    [vc setSaveAction:^{
        @strongify(self)
        //刷新列表
        [self updateWithModel];
    }];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.rightImageView.tintColor = UIColor.whiteColor;
        
        self.tableView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.tableView.tableHeaderView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.tableView.tableFooterView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
    } else {
        self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.rightImageView.tintColor = [UIColor colorWithHexString:@"#222222"];
        
        self.tableView.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.tableView.tableHeaderView.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.tableView.tableFooterView.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    //只有当前的controller会触发一次, 其它已存在的controller走通知
    if(needReload) {
        //修改暗黑模式
        [self applyTheme];
        [self.tableView reloadData];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
    [self.tableView reloadData];
}

#pragma mark -- 屏幕旋转
- (void)viewWillTransitionToSize:(CGSize)size withTransitionCoordinator:(id<UIViewControllerTransitionCoordinator>)coordinator
{
    [self.tableView reloadData];
}

#pragma mark -- 导航栏
- (void)_createCustomRightBarButtonItem
{
    UIButton* rightButton = [[UIButton alloc] initWithFrame:CGRectMake(0.0, 0.0f, 44.0f, 44.0)];
    [rightButton setBackgroundColor:[UIColor clearColor]];

    UIImageView* imageView = [UIImageView new];
    
    UIImage* image = [UIImage imageNamed:@"script_add_icon"];
    image = [image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
    imageView.image = image;
    self.rightImageView = imageView;
    
    [rightButton addSubview:imageView];
    [imageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(rightButton);
        make.size.mas_equalTo(20);
    }];
    
    [rightButton addTarget:self action:@selector(rightBarbuttonClick) forControlEvents:UIControlEventTouchUpInside];
    UIBarButtonItem* barItem = [[UIBarButtonItem alloc] initWithCustomView:rightButton];

    UIBarButtonItem *spacer = [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemFixedSpace target:nil action:nil];
    spacer.width = -16.0f; // for example shift right bar button to the right

    self.navigationItem.rightBarButtonItems = @[spacer, barItem];
}

- (void)rightBarbuttonClick
{
    [self addUserAgent];
}

#pragma mark -- layout
- (void)addSubviews
{
    [self.view addSubview:self.tableView];
}

- (void)defineLayout
{
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(0);
        make.right.mas_offset(-0);
        make.top.equalTo(self.view).offset(0);
        make.bottom.equalTo(self.view);
    }];
}


#pragma mark - UITableViewDataSource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    NSArray* items = self.model[section];
    return items.count;
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return self.model.count;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section
{
    return iPadValue(80, 60);
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section
{
    UIView* header = [UIView new];
    header.backgroundColor = UIColor.clearColor;
    
    UILabel* titleLabel = [UIView createLabelWithTitle:@""
        textColor:[UIColor colorWithHexString:@"#333333"]
          bgColor:UIColor.clearColor
         fontSize:iPadValue(18, 14)
    textAlignment:NSTextAlignmentLeft
            bBold:NO];
    [header addSubview:titleLabel];
    
    float leftOffset = iPadValue(60, 30);
    float bottomOffset = iPadValue(15, 10);
    [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_offset(-bottomOffset);
        make.left.mas_offset(leftOffset);
    }];
    
    if([UserAgentManager shareInstance].customUserAgents.count > 0) {
        //有自定义UA
        if(section == 0) {
            titleLabel.text = NSLocalizedString(@"useragent.title.custom", nil);
        } else if(section == 1) {
            titleLabel.text = NSLocalizedString(@"useragent.title.mobile", nil);
        } else {
            titleLabel.text = NSLocalizedString(@"useragent.title.pc", nil);
        }
    } else {
        //没有自定义UA
        if(section == 0) {
            titleLabel.text = NSLocalizedString(@"useragent.title.mobile", nil);
        } else {
            titleLabel.text = NSLocalizedString(@"useragent.title.pc", nil);
        }
    }

    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        header.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        titleLabel.textColor = [UIColor colorWithHexString:@"#ffffff"];
    } else {
        header.backgroundColor = UIColor.clearColor;
        titleLabel.textColor = [UIColor colorWithHexString:@"#333333"];
    }
    
    return header;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    NSArray* items = self.model[indexPath.section];
    UserAgentModel* model = items[indexPath.row];
    UserAgentiPadCell *cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass(UserAgentiPadCell.class)];
    [cell updateWithModel:model];
    
    @weakify(self)
    [cell setDidSelectUAAction:^(UserAgentModel *item) {
        @strongify(self)
        [[UserAgentManager shareInstance] updateSelectUserAgentWithId:item.uuid];
        [self updateWithModel];
        [[NSNotificationCenter defaultCenter] postNotificationName:kReloadCurrentWebViewNotification object:nil];
        
        BOOL hideUA = [[PreferenceManager shareInstance].items.isHideUAViewAfterTapAction boolValue];
        if(hideUA) {
            //收起弹窗
            [super leftBarbuttonClick];
        }
    }];
    
    return cell;
}

- (BOOL)tableView:(UITableView *)tableView canEditRowAtIndexPath:(NSIndexPath *)indexPath
{
    NSArray* items = self.model[indexPath.section];
    UserAgentModel* model = items[indexPath.row];
    if(model.type == UserAgentTypeCustom) {
        return YES;
    }
    
    return NO;
}

// 只适用于ios11 以及以后版本
- (UISwipeActionsConfiguration *)tableView:(UITableView *)tableView trailingSwipeActionsConfigurationForRowAtIndexPath:(NSIndexPath *)indexPath  API_AVAILABLE(ios(11.0))
{
    // 删除数据
    @weakify(self)
    UIContextualAction *deleteAction = [UIContextualAction contextualActionWithStyle:UIContextualActionStyleDestructive title:NSLocalizedString(@"tableview.delete", nil) handler:^(UIContextualAction * _Nonnull action, __kindof UIView * _Nonnull sourceView, void (^ _Nonnull completionHandler)(BOOL)) {
        @strongify(self)
        [tableView setEditing:NO animated:YES];//退出编辑模式，隐藏左滑菜单
    
        NSArray* items = self.model[indexPath.section];
        UserAgentModel* model = items[indexPath.row];
        [[UserAgentManager shareInstance] removeUserAgentWithId:model.uuid];
        
        [self updateWithModel];
    }];
    [deleteAction setBackgroundColor:UIColor.redColor];

    UISwipeActionsConfiguration *actions = [UISwipeActionsConfiguration configurationWithActions:@[deleteAction]];
    actions.performsFirstActionWithFullSwipe = NO;
    return actions;
}

#pragma mark -- lazy init
- (UITableView *)tableView
{
    if(!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStyleGrouped];

        _tableView.separatorStyle  = UITableViewCellSeparatorStyleNone;
        _tableView.showsHorizontalScrollIndicator = NO;
        _tableView.showsVerticalScrollIndicator   = NO;
        _tableView.delegate   = self;
        _tableView.dataSource = self;
        _tableView.rowHeight = iPadValue(88, 60);
        _tableView.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        
        [_tableView registerClass:[UserAgentiPadCell class] forCellReuseIdentifier:NSStringFromClass([UserAgentiPadCell class])];
        
        UIView* view = [[UIView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, CGFLOAT_MIN)];
        view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        _tableView.tableHeaderView = view;
        
        UIWindow* window = [NSObject normalWindow];
        float offset = iPadValue(30, 10) + window.safeAreaInsets.bottom;
        view = [[UIView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, offset)];
        view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        _tableView.tableFooterView = view;
        
        _tableView.sectionFooterHeight = 0.0;
        _tableView.sectionHeaderHeight = 0.0f;
    }
    
    return _tableView;
}

- (NSMutableArray *)model
{
    if(!_model) {
        _model = [NSMutableArray array];
    }
    
    return _model;
}

@end
