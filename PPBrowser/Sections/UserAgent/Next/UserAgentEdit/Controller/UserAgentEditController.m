//
//  UserAgentEditController.m
//  PPBrowser
//
//  Created by qingbin on 2023/6/25.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import "UserAgentEditController.h"

#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "UIView+FrameHelper.h"
#import "NSObject+Helper.h"

#import "ThemeProtocol.h"
#import "PreferenceManager.h"
#import "CustomTextField.h"

#import "BrowserUtils.h"
#import "UserAgentModel.h"
#import "DatabaseUnit+UserAgent.h"

#import "UserAgentManager.h"

NSString* userAgentPlaceHolder;

@interface UserAgentEditController ()<UITextViewDelegate,UITextFieldDelegate, UITextPasteDelegate, ThemeProtocol>

@property (nonatomic, strong) CustomTextField *titleTextField;

@property (nonatomic, strong) UITextView *textView;

@property (nonatomic, strong) UIButton* rightButton;

@end

@implementation UserAgentEditController

- (void)viewDidLoad
{
    [super viewDidLoad];

    self.title = NSLocalizedString(@"useragent.title.custom", nil);
    userAgentPlaceHolder = NSLocalizedString(@"useragent.input.ua", nil);
    
    //防止下拉关闭,一定要选择同意或者拒绝
    self.modalInPresentation = YES;
    
    [self addSubviews];
    [self defineLayout];
    
    [self createCustomLeftBarButtonItem];
    [self _createCustomRightBarButtonItem];
    
    [self applyTheme];
}

- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event
{
    [self.view endEditing:YES];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        [self.rightButton setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
        
        self.titleTextField.textColor = UIColor.whiteColor;
        self.titleTextField.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        
        self.textView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        if([self.textView.text isEqualToString:userAgentPlaceHolder]) {
            self.textView.textColor = [UIColor colorWithHexString:@"#999999"];
        } else {
            self.textView.textColor = [UIColor colorWithHexString:@"#ffffff"];
        }
    } else {
        self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        [self.rightButton setTitleColor:[UIColor colorWithHexString:@"#2D7AFE"] forState:UIControlStateNormal];
        
        self.titleTextField.textColor = [UIColor colorWithHexString:@"#333333"];
        self.titleTextField.backgroundColor = UIColor.whiteColor;
         
        self.textView.backgroundColor = UIColor.whiteColor;
        if([self.textView.text isEqualToString:userAgentPlaceHolder]) {
            self.textView.textColor = [UIColor colorWithHexString:@"#999999"];
        } else {
            self.textView.textColor = [UIColor colorWithHexString:@"#333333"];
        }
    }
}

- (void)addSubviews
{
    [self.view addSubview:self.titleTextField];
    [self.view addSubview:self.textView];
}

- (void)defineLayout
{
    float height = iPadValue(60, 44);
    float offset = iPadValue(30, 15);
    [self.titleTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(iPadValue(20, 15));
        make.left.equalTo(self.view).offset(offset);
        make.right.equalTo(self.view).offset(-offset);
        make.height.mas_equalTo(height);
    }];
    
    [self.textView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.titleTextField.mas_bottom).offset(20);
        make.left.equalTo(self.view).offset(offset);
        make.right.equalTo(self.view).offset(-offset);
        make.height.mas_equalTo(150);
    }];
}

#pragma mark -- 导航栏
- (void)_createCustomRightBarButtonItem
{
    UIButton* rightButton = [[UIButton alloc] initWithFrame:CGRectMake(0.0, 0.0f, 44.0f, 44.0)];
    [rightButton setBackgroundColor:[UIColor clearColor]];

    rightButton.titleLabel.font = [UIFont systemFontOfSize:16.0];
    [rightButton setTitle:NSLocalizedString(@"userscriptEdit.save.confirm", nil) forState:UIControlStateNormal];
    [rightButton setTitleColor:[UIColor colorWithHexString:@"#2D7AFE"] forState:UIControlStateNormal];
    
    [rightButton addTarget:self action:@selector(rightBarbuttonClick) forControlEvents:UIControlEventTouchUpInside];
    self.rightButton = rightButton;
    
    UIBarButtonItem* barItem = [[UIBarButtonItem alloc] initWithCustomView:rightButton];

    UIBarButtonItem *spacer = [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemFixedSpace target:nil action:nil];
    spacer.width = -16.0f; // for example shift right bar button to the right

    self.navigationItem.rightBarButtonItems = @[spacer, barItem];
}

- (void)rightBarbuttonClick
{
    //保存
    [self saveUserAgent];
}

- (void)saveUserAgent
{
    if(self.titleTextField.text.length == 0) {
        [UIView showWarning:NSLocalizedString(@"useragent.input.title", nil)];
        return;
    }
    
    if(self.textView.text.length == 0
       || [self.textView.text isEqualToString:userAgentPlaceHolder]) {
        [UIView showWarning:NSLocalizedString(@"useragent.input.ua", nil)];
        return;
    }
    
    UserAgentModel* item = [UserAgentModel new];
    item.uuid = [[NSUUID UUID] UUIDString];
    item.title = self.titleTextField.text;
    item.value = self.textView.text;
    item.type = UserAgentTypeCustom;
    item.isSelected = NO;
    item.ctime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
    item.ppOrder = [[UserAgentManager shareInstance] getMaxPPOrder] + 1;
    
    [[UserAgentManager shareInstance] addUserAgentWithItem:item];
    
    if(self.saveAction) {
        self.saveAction();
    }
    
    //退出
    [self leftBarbuttonClick];
}

#pragma mark -- UITextViewDelegate
- (BOOL)textViewShouldBeginEditing:(UITextView *)textView
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    
    if(isDarkTheme) {
        self.textView.textColor = UIColor.whiteColor;
    } else {
        self.textView.textColor = [UIColor colorWithHexString:@"#333333"];
    }
    
    if([self.textView.text isEqualToString:userAgentPlaceHolder]) {
        self.textView.text = @"";
    }
    
    return YES;
}

- (BOOL)textViewShouldEndEditing:(UITextView *)textView
{
    if([self.textView.text isEqualToString:@""]) {
        self.textView.textColor = [UIColor colorWithHexString:@"#999999"];
        self.textView.text = userAgentPlaceHolder;
    }
    
    return YES;
}

- (void)textViewDidChange:(UITextView *)textView
{
    
}

#pragma mark -- UITextPasteDelegate
//参考https://stackoverflow.com/questions/47227011/uitextview-paste-action-inserts-empty-string-on-ios-11
//bug:直接添加脚本 -- 填写标题 -- 粘贴代码,崩溃
//Set a Hook to know there's a paste action while I'm the first responder - fired when Paste was tapped
- (void)textPasteConfigurationSupporting:(id<UITextPasteConfigurationSupporting>)textPasteConfigurationSupporting transformPasteItem:(id<UITextPasteItem>)item API_AVAILABLE(ios(11.0)) {
    [self paste:textPasteConfigurationSupporting];
}

//generic paste action handler
- (void)paste:(id)sender {
    UIPasteboard *pasteBoard = [UIPasteboard generalPasteboard];
    if ([pasteBoard hasStrings]) {
        id<UITextPasteConfigurationSupporting> pasteItem = (id<UITextPasteConfigurationSupporting>)sender;
        if ([pasteItem isKindOfClass:[UITextView class]]) {
            UITextView* myTextView = (UITextView*)pasteItem;
                [myTextView insertText:[pasteBoard string]];
        }
    }
}

#pragma mark -- UITextFieldDelegate
- (BOOL)textField:(UITextField *)textField shouldChangeCharactersInRange:(NSRange)range replacementString:(NSString *)string
{
    return YES;
}

- (BOOL)textFieldShouldBeginEditing:(UITextField *)textField
{

    
    return YES;
}

- (CustomTextField *)titleTextField
{
    if(!_titleTextField) {
        _titleTextField = [[CustomTextField alloc]init];
        //2.6.8 阿拉伯语布局适配
        _titleTextField.textAlignment = NSTextAlignmentLeft;
        
        _titleTextField.delegate = self;
        
        float font = iPadValue(20, 15);
        [_titleTextField setFont:[UIFont systemFontOfSize:font]];
        _titleTextField.textAlignment = NSTextAlignmentLeft;
    
        _titleTextField.leftView = [UIView new];
        _titleTextField.leftViewMode = UITextFieldViewModeAlways;
        _titleTextField.leftViewRect = CGRectMake(0, 0, iPadValue(20, 10), 44);
        
        _titleTextField.layer.cornerRadius = 10;
        _titleTextField.layer.masksToBounds = YES;
        
        [_titleTextField updatePlaceHolder:NSLocalizedString(@"useragent.input.title", nil) color:[UIColor colorWithHexString:@"#999999"]];
    }
    
    return _titleTextField;
}

- (UITextView *)textView
{
    if(!_textView) {
        _textView = [UITextView new];
        _textView.delegate = self;
        _textView.pasteDelegate = self;
        
        float font = iPadValue(20, 15);
        _textView.font = [UIFont systemFontOfSize:font];
        _textView.text = userAgentPlaceHolder;
        float offset = iPadValue(20, 10);
        _textView.contentInset = UIEdgeInsetsMake(offset, offset, offset, offset);
        
        _textView.layer.cornerRadius = 10;
        _textView.layer.masksToBounds = YES;
    }
    
    return _textView;
}


@end
