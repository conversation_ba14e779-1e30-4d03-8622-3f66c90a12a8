//
//  UserAgentManager.m
//  PPBrowser
//
//  Created by qingbin on 2023/6/25.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import "UserAgentManager.h"

#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "UIColor+Helper.h"
#import "NSObject+Helper.h"

#import "ThemeProtocol.h"
#import "PreferenceManager.h"

#import "BrowserUtils.h"
#import "UserAgentUtil.h"

#import "PPNotifications.h"

@interface UserAgentManager ()

@property (nonatomic, strong) NSMutableDictionary *mapper;

@property (nonatomic, strong) NSMutableArray *model;

@property (nonatomic, strong) NSMutableArray *customUserAgents;

@property (nonatomic, strong) NSMutableArray *mobileUserAgents;

@property (nonatomic, strong) NSMutableArray *pcUserAgents;

@end

@implementation UserAgentManager

+ (instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    static UserAgentManager* obj;
    dispatch_once(&onceToken, ^{
        obj = [UserAgentManager new];
    });
    
    return obj;
}

#pragma mark -- 获取当前选中的UA
- (NSString*)getCurrentUserAgent
{
    for(UserAgentModel* item in self.model) {
        if(item.isSelected) {
            return item.value;
        }
    }
    
    //否则直接返回默认的UA
    if([BrowserUtils isiPhone]) {
        return [UserAgentUtil iPhoneUserAgent];
    } else {
        return [UserAgentUtil iPadUserAgent];
    }
}

// 获取当前UA的model
- (UserAgentModel*)getCurrentUAModel
{
    for(UserAgentModel* item in self.model) {
        if(item.isSelected) {
            return item;
        }
    }
    
    return self.mobileUserAgents.firstObject;
}

#pragma mark -- 重新加载数据
- (void)reloadData:(void(^)(void))completion
{
    [self clearAll];
    
    DatabaseUnit* unit = [DatabaseUnit queryAllUserAgentItems];
    @weakify(self)
    [unit setCompleteBlock:^(NSArray* result, BOOL success) {
        @strongify(self)
        [self _filterUserAgent:result];
        
        if(completion) {
            completion();
        }
    }];
    
    DB_EXEC(unit);
}

- (void)_filterUserAgent:(NSArray *)result
{
    self.model = [result mutableCopy];
    
    for(UserAgentModel* item in result) {
        if(item.type == UserAgentTypeCustom) {
            [self.customUserAgents addObject:item];
        } else if(item.type == UserAgentTypePhone) {
            [self.mobileUserAgents addObject:item];
        } else if(item.type == UserAgentTypePC) {
            [self.pcUserAgents addObject:item];
        }
        
        self.mapper[item.uuid] = item;
    }
    
    //根据ppOrder排序(小的在前面，大的在后面)
    [self.customUserAgents sortUsingComparator:^NSComparisonResult(UserAgentModel*  _Nonnull obj1, UserAgentModel*  _Nonnull obj2) {
        return obj1.ppOrder < obj2.ppOrder;
    }];
    
    [self.mobileUserAgents sortUsingComparator:^NSComparisonResult(UserAgentModel*  _Nonnull obj1, UserAgentModel*  _Nonnull obj2) {
        return obj1.ppOrder > obj2.ppOrder;
    }];
    
    [self.pcUserAgents sortUsingComparator:^NSComparisonResult(UserAgentModel*  _Nonnull obj1, UserAgentModel*  _Nonnull obj2) {
        return obj1.ppOrder > obj2.ppOrder;
    }];
}

- (void)clearAll
{
    [self.mapper removeAllObjects];
    
    [self.model removeAllObjects];
    [self.customUserAgents removeAllObjects];
    [self.mobileUserAgents removeAllObjects];
    [self.pcUserAgents removeAllObjects];
}

- (int)getMaxPPOrder
{
    int max = 0;
    for(UserAgentModel* item in self.model) {
        max = MAX(item.ppOrder, max);
    }
    
    return max;
}

#pragma mark -- 数据库相关操作
// 自定义UserAgent
- (void)addUserAgentWithItem:(UserAgentModel*)item
{
    self.mapper[item.uuid] = item;
    [self.model addObject:item];
    //新的在前面
    [self.customUserAgents insertObject:item atIndex:0];
    
    DatabaseUnit* unit = [DatabaseUnit addUserAgentWithItem:item];
    DB_EXEC(unit);
}

// 删除一个自定义的UserAgent
- (void)removeUserAgentWithId:(NSString*)uuid
{
    UserAgentModel* item = self.mapper[uuid];
    [self.mapper removeObjectForKey:uuid];
    
    [self.model removeObject:item];
    [self.customUserAgents removeObject:item];
    [self.mobileUserAgents removeObject:item];
    [self.pcUserAgents removeObject:item];
    
    DatabaseUnit* unit = [DatabaseUnit removeUserAgentWithId:uuid];
    DB_EXEC(unit);
    
    if(item.isSelected) {
        //如果删除的是当前选中的UA，那么需要重新更新UA状态
        UserAgentModel* selectObj;
        if([BrowserUtils isiPhone]) {
            //iPhone
            for(UserAgentModel* item in self.mobileUserAgents) {
                if([item.uuid isEqualToString:@"0"]) {
                    item.isSelected = YES;
                    selectObj = item;
                    break;
                }
            }
        } else {
            //iPad
            for(UserAgentModel* item in self.mobileUserAgents) {
                if([item.uuid isEqualToString:@"1"]) {
                    item.isSelected = YES;
                    selectObj = item;
                    break;
                }
            }
        }
        
        DatabaseUnit* unit = [DatabaseUnit updateSelectUserAgentWithId:selectObj.uuid];
        DB_EXEC(unit);
        
        [[NSNotificationCenter defaultCenter] postNotificationName:kReloadCurrentWebViewNotification object:nil];
    }
}

// 更新选中状态(会先反选其它,然后再选中)
- (void)updateSelectUserAgentWithId:(NSString*)uuid
{
    for(UserAgentModel* item in self.model) {
        if([item.uuid isEqualToString:uuid]) {
            item.isSelected = YES;
        } else {
            item.isSelected = NO;
        }
    }
    
    DatabaseUnit* unit = [DatabaseUnit updateSelectUserAgentWithId:uuid];
    DB_EXEC(unit);
}

- (NSMutableArray *)model
{
    if(!_model) {
        _model = [NSMutableArray array];
    }
    
    return _model;
}

- (NSMutableArray *)customUserAgents
{
    if(!_customUserAgents) {
        _customUserAgents = [NSMutableArray array];
    }
    
    return _customUserAgents;
}

- (NSMutableArray *)mobileUserAgents
{
    if(!_mobileUserAgents) {
        _mobileUserAgents = [NSMutableArray array];
    }
    
    return _mobileUserAgents;
}

- (NSMutableArray *)pcUserAgents
{
    if(!_pcUserAgents) {
        _pcUserAgents = [NSMutableArray array];
    }
    
    return _pcUserAgents;
}

- (NSMutableDictionary *)mapper
{
    if(!_mapper) {
        _mapper = [NSMutableDictionary dictionary];
    }
    
    return _mapper;
}

@end
