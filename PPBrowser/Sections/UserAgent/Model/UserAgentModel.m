//
//  UserAgentModel.m
//  PPBrowser
//
//  Created by qing<PERSON> on 2022/5/30.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "UserAgentModel.h"
#import "UserAgentUtil.h"
#import "BrowserUtils.h"
#import "PPEnums.h"

//参考https://cloud.tencent.com/developer/article/1758440

@implementation UserAgentModel

//移动版
+ (instancetype)iphone
{
    //safari on iphone
    UserAgentModel* item = [UserAgentModel new];
    item.uuid = @"0";
    item.title = @"iPhone";
    item.value = [UserAgentUtil iPhoneUserAgent];
    item.type = UserAgentTypePhone;
    item.ctime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
    item.ppOrder = 0;
    
    //写表时的初始化值
    if([BrowserUtils isiPhone]) {
        item.isSelected = YES;
    } else {
        item.isSelected = NO;
    }
    
    return item;
}

+ (instancetype)ipad
{
    //safari on ipad
    UserAgentModel* item = [UserAgentModel new];
    item.uuid = @"1";
    item.title = @"iPad";
    item.value = [UserAgentUtil iPadUserAgent];
    item.type = UserAgentTypePhone;
    item.ctime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
    item.ppOrder = 1;
    
    //写表时的初始化值
    if([BrowserUtils isiPhone]) {
        item.isSelected = NO;
    } else {
        item.isSelected = YES;
    }
    
    return item;
}

+ (instancetype)android
{
    //chrome on android
    UserAgentModel* item = [UserAgentModel new];
    item.uuid = @"2";
    item.title = @"Android";
    item.value = [UserAgentUtil androidUserAgent];
//    item.value = @"Mozilla/5.0 (Linux; Android 8.0.0; SM-G955U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Mobile Safari/537.36";
    item.type = UserAgentTypePhone;
    item.ctime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
    item.ppOrder = 2;
    item.isSelected = NO;
    
    return item;
}

//电脑版
+ (instancetype)safari
{
    //safari on iMac
    UserAgentModel* item = [UserAgentModel new];
    item.uuid = @"3";
    item.title = @"Safari";
    item.value = [UserAgentUtil safariUserAgent];
    item.type = UserAgentTypePC;
    item.ctime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
    item.ppOrder = 3;
    item.isSelected = NO;
    
    return item;
}

+ (instancetype)edge
{
    UserAgentModel* item = [UserAgentModel new];
    item.uuid = @"4";
    item.title = @"Edge";
    item.value = [UserAgentUtil edgeUserAgent];
    item.type = UserAgentTypePC;
    item.ctime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
    item.ppOrder = 4;
    item.isSelected = NO;
    
    return item;
}

//自动获取UA的网址
//http://www.sunchateau.com/free/ua.htm
+ (instancetype)chrome
{
    //chrome on iMac
    UserAgentModel* item = [UserAgentModel new];
    item.uuid = @"5";
    item.title = @"Chrome";
    item.value = [UserAgentUtil chromeUserAgent];
    item.type = UserAgentTypePC;
    item.ctime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
    item.ppOrder = 5;
    item.isSelected = NO;
    
    return item;
}

+ (instancetype)firefox
{
    //firefox on iMac
    UserAgentModel* item = [UserAgentModel new];
    item.uuid = @"6";
    item.title = @"Firefox";
    item.value = [UserAgentUtil firefoxUserAgent];
    item.type = UserAgentTypePC;
    item.ctime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
    item.ppOrder = 6;
    item.isSelected = NO;
    
    return item;
}

#pragma mark -- Getters
//默认useragent的值不能写死，根据ppOrder来从代码中读取真正的值, 大于等于7的读取自定义值
- (NSString *)value
{
    if(self.type == UserAgentTypeCustom) return _value;
    
    if(self.ppOrder == UserAgentDefaultTypeiPhone) {
        _value = [UserAgentUtil iPhoneUserAgent];
    } else if(self.ppOrder == UserAgentDefaultTypeiPad) {
        _value = [UserAgentUtil iPadUserAgent];
    } else if(self.ppOrder == UserAgentDefaultTypeAndriod) {
        _value = [UserAgentUtil androidUserAgent];
    } else if(self.ppOrder == UserAgentDefaultTypeSafari) {
        _value = [UserAgentUtil safariUserAgent];
    } else if(self.ppOrder == UserAgentDefaultTypeEdge) {
        _value = [UserAgentUtil edgeUserAgent];
    } else if(self.ppOrder == UserAgentDefaultTypeChrome) {
        _value = [UserAgentUtil chromeUserAgent];
    } else if(self.ppOrder == UserAgentDefaultTypeFirefox) {
        _value = [UserAgentUtil firefoxUserAgent];
    }
    
    return _value;

}


@end
