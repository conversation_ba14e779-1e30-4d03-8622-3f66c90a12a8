//
//  UserAgentUtil.m
//  PandaBrowser
//
//  Created by q<PERSON><PERSON> on 2022/3/2.
//

#import "UserAgentUtil.h"
#import <UIKit/UIKit.h>

#import "BrowserUtils.h"

@interface UserAgentUtil ()

@end

@implementation UserAgentUtil

+ (NSString*)mobileUserAgent
{
    //因为随着iPhone/iPad的切换
    //会导致cpuInfo的获取也很发生切换
    
    NSString* kernelVersion = @"15E148";
    NSString* safariBuildNumber = @"604.1";
    NSString* webkitVersion = @"605.1.15";
    NSString* cpuInfo = [self cpuInfo];
    NSString* result = [NSString stringWithFormat:@"Mozilla/5.0 (%@) AppleWebKit/%@ (KHTML, like Gecko) Version/%@ Mobile/%@ Safari/%@",
             cpuInfo,
             webkitVersion,
             [self safariVersion],
             kernelVersion,
             safariBuildNumber
            ];

    return result;
}

+ (NSString *)iPadUserAgent
{
    NSString* ua = [self mobileUserAgent];
    if([BrowserUtils isiPad]) {
    } else {
        ua = [ua stringByReplacingOccurrencesOfString:@"iPhone" withString:@"iPad"];
    }
    
    return ua;
}

+ (NSString *)iPhoneUserAgent
{
    NSString* ua = [self mobileUserAgent];
    if([BrowserUtils isiPad]) {
        ua = [ua stringByReplacingOccurrencesOfString:@"iPad" withString:@"iPhone"];
    } else {
    }
    
    return ua;
}

+ (NSString *)androidUserAgent
{
    return @"Mozilla/5.0 (Linux; Android 8.0; MI 6 Build/OPR1.170623.027; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/48.0.2564.116 Mobile Safari/537.36 T7/10.3 SearchCraft/2.6.3 (Baidu; P1 8.0.0)";
}

+ (NSString *)edgeUserAgent
{
    return @"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/111.0.1661.44";
}

+ (NSString *)chromeUserAgent
{
    return @"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36";
}

+ (NSString *)firefoxUserAgent
{
    return @"Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:131.0) Gecko/20100101 Firefox/131.0";
}

+ (NSString*)safariUserAgent
{
    return @"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Safari/605.1.15";
}

+ (NSString*)cpuInfo
{
    NSString* model = [[UIDevice currentDevice] model];
    model = [model componentsSeparatedByString:@" "].firstObject;
    
    NSString* platform = nil;
    if([UIDevice currentDevice].userInterfaceIdiom == UIUserInterfaceIdiomPad) {
        platform = @"OS";
    } else {
        platform = @"iPhone OS";
    }
    
    return [NSString stringWithFormat:@"%@; CPU %@ %@ like MAC OS X",model, platform, [self osVersion]];
}

/// 'Version/13.0' part of UA. It seems to be based on Safaris build number.
+ (NSString*)safariVersion
{
//    NSOperatingSystemVersion version = [[NSProcessInfo processInfo] operatingSystemVersion];
//    NSInteger majorVersion = version.majorVersion;
//    
//    if(majorVersion == 13) return @"13.1.2";
//    if(majorVersion == 14) return @"14.1.1";
//    if(majorVersion == 15) return @"15.0";
//    
//    return [NSString stringWithFormat:@"%ld_0",(long)majorVersion];
    
    //"主版本.次版本"
    //如果多了补丁版本，那么打开bing.com会变空白
    //不加"补丁版本",和Alook对齐
    NSString *systemVersion = [UIDevice currentDevice].systemVersion;
    NSArray *versionComponents = [systemVersion componentsSeparatedByString:@"."];

    NSMutableArray *paddedComponents = [NSMutableArray arrayWithArray:versionComponents];
    while (paddedComponents.count < 2) {
        [paddedComponents addObject:@"0"];
    }

    return [paddedComponents componentsJoinedByString:@"."];
}

/// 'Version/13.0' part of UA. It seems to be based on Safaris build number.
+ (NSString*)osVersion
{
//    NSOperatingSystemVersion version = [[NSProcessInfo processInfo] operatingSystemVersion];
//    NSInteger majorVersion = version.majorVersion;
//    
//    if(majorVersion == 13) return @"13_6_1";
//    if(majorVersion == 14) return @"14_6";
//    if(majorVersion == 15) return @"15_0";
//    
//    return [NSString stringWithFormat:@"%ld_0",(long)majorVersion];
    
    //为了适配m.vk.com
    //这个网站useragent不对，会报错
    
    //"主版本_次版本"
    //如果多了补丁版本，那么打开bing.com会变空白
    //不加"补丁版本",和Alook对齐
    NSString *systemVersion = [UIDevice currentDevice].systemVersion;
    NSArray *versionComponents = [systemVersion componentsSeparatedByString:@"."];

    NSMutableArray *paddedComponents = [NSMutableArray arrayWithArray:versionComponents];
    while (paddedComponents.count < 2) {
        [paddedComponents addObject:@"0"];
    }

    return [paddedComponents componentsJoinedByString:@"_"];
}


@end
