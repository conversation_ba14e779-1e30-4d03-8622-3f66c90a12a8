//
//  UserAgentModel.h
//  PPBrowser
//
//  Created by qingbin on 2022/5/30.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "BaseModel.h"
#import "PPEnums.h"

NS_ASSUME_NONNULL_BEGIN

@interface UserAgentModel : BaseModel
//id
@property (nonatomic, strong) NSString* uuid;
//实际的ua
@property (nonatomic, strong) NSString* value;
//显示的标题
@property (nonatomic, strong) NSString *title;
//是否选中
@property (nonatomic, assign) BOOL isSelected;
//类型(移动端+PC+自定义)
@property (nonatomic, assign) UserAgentType type;
//生成时间
@property (nonatomic, strong) NSString *ctime;
//默认useragent的值不能写死，根据ppOrder来从代码中读取真正的值, 大于等于7的读取自定义值
@property (nonatomic, assign) UserAgentDefaultType ppOrder;

//辅助字段,主要是卡片化
@property (nonatomic, assign) BOOL isFirstInSection;
@property (nonatomic, assign) BOOL isLastInSection;

//自定义


//移动版
+ (instancetype)iphone;

+ (instancetype)ipad;

+ (instancetype)android;

//电脑版
+ (instancetype)safari;

+ (instancetype)edge;

+ (instancetype)chrome;

+ (instancetype)firefox;

//+ (instancetype)ie;

@end

NS_ASSUME_NONNULL_END
