//
//  UserAgentManager.h
//  PPBrowser
//
//  Created by qingbin on 2023/6/25.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>

#import "UserAgentModel.h"
#import "DatabaseUnit+UserAgent.h"


// UserAgent相关研究: https://blog.csdn.net/crasowas/article/details/135330574
/**
 UserAgent设置的三种方式：
 NSUserDefaults方式：在WKWebView对象初始化前，通过registerDefaults方法设置，一次设置全局生效，已过时
 applicationNameForUserAgent方式：在WKWebView对象初始化时，通过WKWebViewConfiguration对象设置，后续再修改无效
 customUserAgent方式：在WKWebView对象初始化后，直接赋值设置，支持动态修改
 */
@interface UserAgentManager : NSObject

+ (instancetype)shareInstance;

// 重新加载数据
- (void)reloadData:(void(^)(void))completion;

// 获取当前UA
- (NSString*)getCurrentUserAgent;

// 获取当前UA的model
- (UserAgentModel*)getCurrentUAModel;

// 获取当前最大的PPOrder
- (int)getMaxPPOrder;

@property (readonly) NSMutableArray *model;

@property (readonly) NSMutableArray *customUserAgents;

@property (readonly) NSMutableArray *mobileUserAgents;

@property (readonly) NSMutableArray *pcUserAgents;

/// 为了交互体验，同步数据库的操作(先操作内存，再操作数据库)
// 自定义UserAgent
- (void)addUserAgentWithItem:(UserAgentModel*)item;

// 删除一个自定义的UserAgent
- (void)removeUserAgentWithId:(NSString*)uuid;

// 更新选中状态(会先反选其它,然后再选中)
- (void)updateSelectUserAgentWithId:(NSString*)uuid;

@end

