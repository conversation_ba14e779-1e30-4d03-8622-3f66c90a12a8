//
//  DatabaseUnit+UserAgent.h
//  PPBrowser
//
//  Created by qingbin on 2023/6/24.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import "DatabaseUnit.h"
#import "UserAgentModel.h"
#import "PPEnums.h"

NS_ASSUME_NONNULL_BEGIN

@interface DatabaseUnit (UserAgent)

// 自定义UserAgent
+ (DatabaseUnit*)addUserAgentWithItem:(UserAgentModel*)item;

// 删除一个自定义的UserAgent
+ (DatabaseUnit*)removeUserAgentWithId:(NSString*)uuid;

// 查询所有UserAgent
+ (DatabaseUnit*)queryAllUserAgentItems;

// 更新自定义UserAgent(标题+内容)
+ (DatabaseUnit*)updateUserAgentWithItem:(UserAgentModel*)item;

// 更新选中状态(会先反选其它,然后再选中)
+ (DatabaseUnit*)updateSelectUserAgentWithId:(NSString*)uuid;

@end

NS_ASSUME_NONNULL_END
