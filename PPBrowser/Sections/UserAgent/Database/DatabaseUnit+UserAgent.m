//
//  DatabaseUnit+UserAgent.m
//  PPBrowser
//
//  Created by qingbin on 2023/6/24.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import "DatabaseUnit+UserAgent.h"
#import "ReactiveCocoa.h"

#import "FMDatabaseQueue.h"
#import "FMDatabase.h"

#import "UserAgentManager.h"

@implementation DatabaseUnit (UserAgent)

// 自定义UserAgent
+ (DatabaseUnit*)addUserAgentWithItem:(UserAgentModel*)item
{
//    uuid TEXT PRIMARY KEY, title TEXT, value TEXT, type INTEGER, ppOrder INTEGER, isSelected INTEGER, ctime TEXT
    
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        
        NSString* command = @"INSERT INTO t_useragent(uuid, title, value, type, ppOrder, isSelected, ctime) VALUES (?,?,?,?,?,?,?)";
        BOOL result = [db executeUpdate:command, item.uuid, item.title, item.value, @(item.type), @(item.ppOrder), @(item.isSelected), item.ctime];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

// 删除一个自定义的UserAgent
+ (DatabaseUnit*)removeUserAgentWithId:(NSString*)uuid
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        
        NSString* command = [NSString stringWithFormat:@"DELETE FROM t_useragent WHERE uuid = ?;"];
        BOOL result = [db executeUpdate:command, uuid];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

// 查询所有UserAgent
+ (DatabaseUnit*)queryAllUserAgentItems
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = [NSString stringWithFormat:@"SELECT * FROM t_useragent ORDER BY ppOrder DESC, ctime DESC"];
        FMResultSet* set = [db executeQuery:command];
        
        NSMutableArray *array = [[NSMutableArray alloc] init];
        while([set next]) {
            UserAgentModel* item = [[UserAgentModel alloc]initWithDictionary:[set resultDictionary] error:nil];
            [array addObject:item];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(array,YES);
            }
        });
    };
    
    return unit;
}

// 更新自定义UserAgent
+ (DatabaseUnit*)updateUserAgentWithItem:(UserAgentModel*)item
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = [NSString stringWithFormat:@"UPDATE t_useragent SET title = ?, value = ? WHERE uuid = ?;"];
        BOOL result = [db executeUpdate:command, item.title, item.value, item.uuid];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil,result);
            }
        });
    };
    
    return unit;
}

// 更新选中状态(会先反选其它,然后再选中)
+ (DatabaseUnit*)updateSelectUserAgentWithId:(NSString*)uuid
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = [NSString stringWithFormat:@"UPDATE t_useragent SET isSelected = ?"];
        BOOL result = [db executeUpdate:command, @(NO)];
        
        command = [NSString stringWithFormat:@"UPDATE t_useragent SET isSelected = ? WHERE uuid = ?"];
        result = [db executeUpdate:command, @(YES), uuid];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil,result);
            }
        });
    };
    
    return unit;
}

@end
