//
//  GuidelineController.m
//  PPBrowser
//
//  Created by qingbin on 2022/10/8.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "GuidelineController.h"

#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "UIView+FrameHelper.h"
#import "NSObject+Helper.h"

#import "ThemeProtocol.h"
#import "PreferenceManager.h"

#import "GuidelineCell.h"
#import "GuidelineModel.h"

#import "BrowserUtils.h"
#import "WebViewController.h"

#import "PlayerView.h"
#import "PlayModel.h"

@interface GuidelineController ()<UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) NSMutableArray* model;

@property (nonatomic, strong) UITableView* tableView;

@property (nonatomic, strong) PlayerView* playerView;

@end

@implementation GuidelineController

- (void)viewDidLoad
{
    [super viewDidLoad];

    self.navigationItem.title = NSLocalizedString(@"guideline.title", nil);
    self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    
    [self createCustomLeftBarButtonItem];
    
    [self addSubviews];
    [self defineLayout];
    [self updateWithModel];
    
    [self applyTheme];
}

- (void)updateWithModel
{
    [self.model removeAllObjects];
    
    NSArray* items = @[
        [GuidelineModel scriptItem],
        [GuidelineModel videoItem],
        [GuidelineModel fullscreenItem],
        [GuidelineModel translateItem],
        [GuidelineModel autoPageItem],
        [GuidelineModel customUrlItem],
        [GuidelineModel bookmarkItem],
        [GuidelineModel tagitItem],
    ];
    
    for(int i=0;i<items.count;i++) {
        NSMutableArray* array = [NSMutableArray array];
        [array addObject:items[i]];
        
        [self.model addObject:array];
    }
    
    for(NSArray* items in self.model) {
        GuidelineModel* item = items.firstObject;
        item.isFirstInSection = YES;
        item = items.lastObject;
        item.isLastInSection = YES;
    }
    
    [self.tableView reloadData];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        
        self.tableView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.tableView.tableHeaderView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.tableView.tableFooterView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
    } else {
        self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        
        self.tableView.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.tableView.tableHeaderView.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.tableView.tableFooterView.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    //只有当前的controller会触发一次, 其它已存在的controller走通知
    if(needReload) {
        //修改暗黑模式
        [self applyTheme];
        [self.tableView reloadData];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
    [self.tableView reloadData];
}

#pragma mark -- layout
- (void)addSubviews
{
    [self.view addSubview:self.tableView];
}

- (void)defineLayout
{
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(0);
        make.right.mas_offset(-0);
        make.top.equalTo(self.view).offset(0);
        make.bottom.equalTo(self.view);
    }];
}

#pragma mark - UITableViewDataSource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    NSArray* items = self.model[section];
    return items.count;
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return self.model.count;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section
{
    return iPadValue(60, 45);
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section
{
    NSArray* array = self.model[section];
    GuidelineModel* item = array.firstObject;
    
    UIView* header = [UIView new];
    header.backgroundColor = UIColor.clearColor;
    
    UILabel* titleLabel = [UIView createLabelWithTitle:@""
        textColor:[UIColor colorWithHexString:@"#666666"]
          bgColor:UIColor.clearColor
         fontSize:iPadValue(18, 14)
    textAlignment:NSTextAlignmentLeft
            bBold:NO];
    [header addSubview:titleLabel];
    [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_offset(-iPadValue(15, 5));
        make.left.mas_offset(iPadValue(60, 30));
    }];
    
    titleLabel.text = item.title;
    
    //油猴脚本
    //视频模式
//    if(section == 0) {
//        titleLabel.text = NSLocalizedString(@"guideline.script.title", nil);
//    } else if(section == 1) {
//        titleLabel.text = NSLocalizedString(@"guideline.video.title", nil);
//    } else if(section == 2) {
//        titleLabel.text = NSLocalizedString(@"guideline.fullscreen.title", nil);
//    } else if(section == 3) {
//        titleLabel.text = NSLocalizedString(@"guideline.translate.title", nil);
//    } else if(section == 4) {
//        titleLabel.text = NSLocalizedString(@"guideline.custom.url.title", nil);
//    } else if(section == 5) {
//        titleLabel.text = NSLocalizedString(@"guideline.bookmark.title", nil);
//    }
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        header.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        titleLabel.textColor = [UIColor colorWithHexString:@"#666666"];
    } else {
        header.backgroundColor = UIColor.clearColor;
        titleLabel.textColor = [UIColor colorWithHexString:@"#666666"];
    }
    
    return header;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    NSArray* items = self.model[indexPath.section];
    GuidelineModel* model = items[indexPath.row];
    GuidelineCell *cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass(GuidelineCell.class)];
    [cell updateWithModel:model];
    
    @weakify(self)
    [cell setDidSelectAction:^(GuidelineModel *item) {
        @strongify(self)
        [self _handleJumpToWebViewWithModel:item];
    }];
    
    return cell;
}

- (void)_handleJumpToWebViewWithModel:(GuidelineModel *)model
{
    WebViewController* vc;
    if(model.type == GuideLineTypeScript
       || model.type == GuideLineTypeTranslate
       || model.type == GuideLineTypeAutoPage
       || model.type == GuideLineTypeTagit) {
        @weakify(self)
        vc = [[WebViewController alloc]initWithWebviewType:WebViewTypePush Title:model.content rightButtonTitle:NSLocalizedString(@"guideline.guide.video", nil) rightButtonClickAction:^(UIView *view) {
            //添加播放器
            @strongify(self)
            [self _enterPlayVideoWithModel:model view:view];
        }];
    } else {
        vc = [[WebViewController alloc]initWithWebviewType:WebViewTypePush Title:model.content];
    }
    [vc loadFileName:model.fileUrl];
    
    [self.navigationController pushViewController:vc animated:YES];
}

- (void)_enterPlayVideoWithModel:(GuidelineModel *)model view:(UIView *)view
{
    // 获取视频路径
    NSString *bundle = [[NSBundle mainBundle] pathForResource:@"Help" ofType:@"bundle"];
    NSString *videoPath = [[NSBundle bundleWithPath:bundle] pathForResource:model.videoGuideLineFileName ofType:@"mp4"];
    if (!videoPath) {
        return;
    }
    
    if(!self.playerView) {
        //初始化
        [self.playerView removeFromSuperview];
        self.playerView = nil;
        self.playerView = [[PlayerView alloc] init];
        
        @weakify(self)
        [self.playerView setClosePlayerBlock:^{
            @strongify(self)
            [self.playerView removeFromSuperview];
            self.playerView = nil;
        }];
    }
    
    if(!self.playerView.superview) {
        //添加在BrowserViewController的view上
        [view addSubview:self.playerView];
        [self.playerView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.equalTo(view);
        }];
    }
    
    PlayModel* playModel = [PlayModel new];
    playModel.status = PlayModelItemStatusFile;
    playModel.src = videoPath;
        
    [self.playerView playWithModel:playModel];
}

#pragma mark -- lazy init
- (UITableView *)tableView
{
    if(!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStyleGrouped];

        _tableView.separatorStyle  = UITableViewCellSeparatorStyleNone;
        _tableView.showsHorizontalScrollIndicator = NO;
        _tableView.showsVerticalScrollIndicator   = NO;
        _tableView.delegate   = self;
        _tableView.dataSource = self;
        _tableView.rowHeight = iPadValue(88, 50);
        _tableView.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        
        [_tableView registerClass:[GuidelineCell class] forCellReuseIdentifier:NSStringFromClass([GuidelineCell class])];
        
        UIView* view = [[UIView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, CGFLOAT_MIN)];
        view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        _tableView.tableHeaderView = view;
        
        UIWindow* window = [NSObject normalWindow];
        view = [[UIView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, iPadValue(30, 10) + window.safeAreaInsets.bottom)];
        view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        _tableView.tableFooterView = view;
        
        _tableView.sectionFooterHeight = 0.0;
        _tableView.sectionHeaderHeight = 0.0f;
    }
    
    return _tableView;
}

- (NSMutableArray *)model
{
    if(!_model) {
        _model = [NSMutableArray array];
    }
    
    return _model;
}

@end
