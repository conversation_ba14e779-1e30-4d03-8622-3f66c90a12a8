<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>自动翻页</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <meta name="viewport"
        content="width=device-width, initial-scale=1.0, user-scalable=0, minimum-scale=1.0, maximum-scale=1.0, minimal-ui">
    <meta name="format-detection" content="telephone=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta http-equiv="X-UA-Compatible" content="IE=Edge;chrome=1">
    <meta http-equiv="pragma" content="no-cache" />
    <style>html{font-size: 12px;}</style>
    <script src="http://code.jquery.com/jquery-1.9.1.min.js"></script>
    
    <script type="text/javascript">
        (function () {
            function resetRootSize() {
                var sw = document.documentElement.clientWidth;
                var sh = document.documentElement.clientHeight;
                document.documentElement.style.fontSize = document.documentElement.clientWidth / 30 + 'px';
                if(document.documentElement.clientWidth / 30 > 14.5){
                    document.documentElement.style.fontSize="14.33px";
                }
                
            }
            resetRootSize();
            window.onresize = function () {
                resetRootSize();
            };
        })();
    </script>
    <style>
        * {
            margin     : 0;
            padding    : 0;
            font-family: Microsoft Yahei;
        }

        html {
            height: 100%;
        }

        article,
        aside,
        details,
        figcaption,
        figure,
        footer,
        header,
        main,
        menu,
        nav,
        section,
        summary {
            display: block
        }

        a {
            background-color           : transparent;
            -webkit-tap-highlight-color: transparent;
        }

        a:active,
        a:hover {
            outline-width: 0
        }

        img {
            border-style: none
        }

        article,
        aside,
        details,
        figcaption,
        figure,
        footer,
        header,
        hgroup,
        menu,
        nav,
        section {
            display: block;
        }

        a {
            text-decoration: none
        }

        input,
        textarea,
        button {
            border : 0;
            outline: 0;
            padding: 0;
            margin : 0
        }
        video {
            width  : 100%;
            display: block;
            margin : 0.8rem auto 1.67rem auto;
        }
        html,
        body {
          position: relative;
          height: 100%;
        }
        body{max-width: 1000px;margin: 0 auto;}
        #zh,#en{height: 100%;}
        @media screen and (min-device-width: 481px) {
            #zh,#en{max-width: 460px;
        margin: 0 auto;}
        }


        @media screen and (max-height:667px){
            video {
                width  :87%;
                display: block;
            }
        }
        #mk1{padding: 1.67rem 1.67rem 4.5rem 1.67rem;box-sizing: border-box;}
        #mk1 h2{font-family: PingFangSC-Semibold;
            font-size: 1.67rem;
            color: #000000;
            letter-spacing: 0;
            font-weight: 600;margin-bottom: 0.75rem;}
        #mk1 p{font-family: PingFangSC-Regular;
            font-size: 1.33rem;
            color: #000000;
            letter-spacing: 0;
            line-height: 2rem;
            font-weight: 400; text-align: justify;}
        #mk1 img{width: 70%; display:block;margin:1.67rem auto;}
        @media screen and (min-device-width: 481px) {
            #mk1 img{width: 70%; display:block;margin:1.67rem auto;max-width: 300px;}
        }
        
        
        p span{
            font-family: PingFangSC-Medium;
            font-size: 1.33rem;
            color: #439CFC;
            font-weight: 500;
        }
        .more{
            font-family: PingFangSC-Medium;
            font-size: 1.33rem;
            color: #439CFC;
            font-weight: 500;
            text-align: center;
            display: block;text-decoration: underline;
        }
        @media (prefers-color-scheme: dark) {
            html,body {background: #0F0E1F;}
            #mk1 h2,#mk1 p{color: #fff;}
        }
        a{
            font-size:16px
        }
    </style>
</head>

<body>
    <div id="mk1" style="padding: 1.67rem 1.6rem 4.5rem 1.67rem;">        
        <p>1、自动翻页是Focus浏览器中的非常重要的一个功能，它可以帮助用户自动加载下一页。它的工作原理主要有以下几部分组成：</p><br>
        
        <img src="focus://autopage_s1.png" />
        
        <p style="margin-top: 2.83rem;">1.1、Focus浏览器中针对常见的网站，内置了自动翻页规则，按照上面截图打开自动翻页的总开关，那么则会自动运行内置规则。</p>
        
        <p style="margin-top: 2.83rem;">1.2、如果没有匹配到内置规则，那么会进行智能拼页算法，尝试自动拼页。</p>
        
        <p style="margin-top: 2.83rem;">1.3、如果自动拼页失败，那么我们也提供了手动拼页的功能，手动拼页可以参考右上角的<span>视频教程</span>。</p>
        
        <p style="margin-top: 2.83rem;">1.4、如果不希望指定网站开启自动翻页功能，也可以将其加入黑名单。</p>
        
        <p></p>
    </div>
</body>
</html>

