<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>视频模式</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <meta name="viewport"
        content="width=device-width, initial-scale=1.0, user-scalable=0, minimum-scale=1.0, maximum-scale=1.0, minimal-ui">
    <meta name="format-detection" content="telephone=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta http-equiv="X-UA-Compatible" content="IE=Edge;chrome=1">
    <meta http-equiv="pragma" content="no-cache" />
    <style>html{font-size: 12px;}</style>
    <script src="http://code.jquery.com/jquery-1.9.1.min.js"></script>
    
    <script type="text/javascript">
        (function () {
            function resetRootSize() {
                var sw = document.documentElement.clientWidth;
                var sh = document.documentElement.clientHeight;
                document.documentElement.style.fontSize = document.documentElement.clientWidth / 30 + 'px';
                if(document.documentElement.clientWidth / 30 > 14.5){
                    document.documentElement.style.fontSize="14.33px";
                }
                
            }
            resetRootSize();
            window.onresize = function () {
                resetRootSize();
            };
        })();
    </script>
    <style>
        * {
            margin     : 0;
            padding    : 0;
            font-family: Microsoft Yahei;
        }

        html {
            height: 100%;
        }

        article,
        aside,
        details,
        figcaption,
        figure,
        footer,
        header,
        main,
        menu,
        nav,
        section,
        summary {
            display: block
        }

        a {
            background-color           : transparent;
            -webkit-tap-highlight-color: transparent;
        }

        a:active,
        a:hover {
            outline-width: 0
        }

        img {
            border-style: none
        }

        article,
        aside,
        details,
        figcaption,
        figure,
        footer,
        header,
        hgroup,
        menu,
        nav,
        section {
            display: block;
        }

        a {
            text-decoration: none
        }

        input,
        textarea,
        button {
            border : 0;
            outline: 0;
            padding: 0;
            margin : 0
        }
        video {
            width  : 100%;
            display: block;
            margin : 0.8rem auto 1.67rem auto;
        }
        html,
        body {
          position: relative;
          height: 100%;
        }
        body{max-width: 1000px;margin: 0 auto;}
        #zh,#en{height: 100%;}
        @media screen and (min-device-width: 481px) {
            #zh,#en{max-width: 460px;
        margin: 0 auto;}
        }


        @media screen and (max-height:667px){
            video {
                width  :87%;
                display: block;
            }
        }
        #mk1{padding: 1.67rem 1.67rem 4.5rem 1.67rem;box-sizing: border-box;}
        #mk1 h2{font-family: PingFangSC-Semibold;
            font-size: 1.67rem;
            color: #000000;
            letter-spacing: 0;
            font-weight: 600;margin-bottom: 0.75rem;}
        #mk1 p{font-family: PingFangSC-Regular;
            font-size: 1.33rem;
            color: #000000;
            letter-spacing: 0;
            line-height: 2rem;
            font-weight: 400; text-align: justify;}
        #mk1 img{width: 70%; display:block;margin:1.67rem auto;}
        @media screen and (min-device-width: 481px) {
            #mk1 img{width: 70%; display:block;margin:1.67rem auto;max-width: 300px;}
        }
        
        
        p span{
            font-family: PingFangSC-Medium;
            font-size: 1.33rem;
            color: #439CFC;
            font-weight: 500;
        }
        .more{
            font-family: PingFangSC-Medium;
            font-size: 1.33rem;
            color: #439CFC;
            font-weight: 500;
            text-align: center;
            display: block;text-decoration: underline;
        }
        @media (prefers-color-scheme: dark) {
            html,body {background: #0F0E1F;}
            #mk1 h2,#mk1 p{color: #fff;}
        }
        a{
            font-size:16px
        }
    </style>
</head>

<body>
    <div id="mk1" style="padding: 1.67rem 1.6rem 4.5rem 1.67rem;">        
        <p>1、打开视频网站站点</p><br>
        
        <img src="focus://video_s1.png" />
        
        <p style="margin-top: 2.83rem;">2.1、方式一: 当Focus检测到可以观看的视频，右下角就会出现播放按钮，点击播放按钮可以进入视频模式。</p>
        
        <img src="focus://video_s2.png" />

        <p style="margin-top: 2.83rem;">2.2、方式二: 可以长按视频区域，当识别到视频，底部会弹窗显示，点击播放可以进入视频模式。</p>
        
        <img src="focus://video_s3.png" />
        
        <p></p>
    </div>
</body>
</html>

