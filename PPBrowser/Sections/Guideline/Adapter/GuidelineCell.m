//
//  GuidelineCell.m
//  PPBrowser
//
//  Created by qingbin on 2022/10/8.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "GuidelineCell.h"

#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "UIView+FrameHelper.h"
#import "NSObject+Helper.h"

#import "ThemeProtocol.h"
#import "PreferenceManager.h"

#import "BrowserUtils.h"

@interface GuidelineCell ()

@property(nonatomic,strong) GuidelineModel* model;

@property(nonatomic, strong) UIView* backView;

@property(nonatomic,strong) UILabel* titleLabel;

@property (nonatomic, strong) UIImageView* arrow;

@property(nonatomic,strong) UIView* line;

@end

@implementation GuidelineCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier])
    {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        self.backgroundColor = UIColor.clearColor;
        [self addSubviews];
        [self defineLayout];
        [self setupObservers];
    }
    
    return self;
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.backView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        self.titleLabel.textColor = UIColor.whiteColor;
        self.line.backgroundColor = [UIColor colorWithHexString:kDarkThemeColorLine];
        self.arrow.tintColor = UIColor.whiteColor;
    } else {
        self.titleLabel.textColor = [UIColor colorWithHexString:@"#434343"];
        self.line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.backView.backgroundColor = UIColor.whiteColor;
        self.arrow.tintColor = [UIColor colorWithHexString:@"#333333"];
    }
}

- (void)updateWithModel:(GuidelineModel*)model
{
    self.model = model;
    self.titleLabel.text = model.content;
    
    [self updateCornerRadius];
    [self applyTheme];
}

- (void)updateCornerRadius
{
    // 圆角
    self.line.hidden = NO;
    self.backView.layer.cornerRadius = iPadValue(20, 10);
    self.backView.layer.masksToBounds = YES;
    
    if(self.model.isFirstInSection && self.model.isLastInSection) {
        //只有一个
        self.backView.layer.masksToBounds = YES;
        self.backView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner | kCALayerMinXMaxYCorner | kCALayerMaxXMaxYCorner;
        self.line.hidden = YES;
    } else {
        if(self.model.isFirstInSection) {
            //添加上圆角
            self.backView.layer.masksToBounds = YES;
            self.backView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
        } else if(self.model.isLastInSection) {
            //添加下圆角
            self.backView.layer.masksToBounds = YES;
            self.backView.layer.maskedCorners = kCALayerMinXMaxYCorner | kCALayerMaxXMaxYCorner;
            self.line.hidden = YES;
        } else {
            self.backView.layer.masksToBounds = NO;
            self.backView.layer.cornerRadius = 0;
        }
    }
}

- (void)setupObservers
{
    UITapGestureRecognizer* tap = [[UITapGestureRecognizer alloc]init];
    @weakify(self)
    [tap.rac_gestureSignal subscribeNext:^(id x) {
        @strongify(self)
        if(self.didSelectAction) {
            self.didSelectAction(self.model);
        }
    }];
    
    [self.contentView addGestureRecognizer:tap];
}

- (void)addSubviews
{
    [self.contentView addSubview:self.backView];
    [self.backView addSubview:self.titleLabel];
    [self.backView addSubview:self.arrow];
}

- (void)defineLayout
{
    float leftOfsset = iPadValue(30, 15);
    [self.backView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(leftOfsset);
        make.right.mas_offset(-leftOfsset);
        make.top.bottom.equalTo(self.contentView);
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.left.mas_offset(leftOfsset);
        make.right.mas_offset(-leftOfsset);
    }];
    
    float size = iPadValue(15, 10);
    [self.arrow mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_offset(-leftOfsset);
        make.centerY.equalTo(self);
        make.size.mas_equalTo(size);
    }];
    
    UIView* line = [UIView new];
    line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    [self.contentView addSubview:line];
    [line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.contentView);
        make.left.mas_offset(leftOfsset);
        make.right.mas_offset(-leftOfsset);
        make.height.mas_equalTo(0.5);
    }];
    
    self.line = line;
}

- (UIView *)backView
{
    if(!_backView) {
        _backView = [UIView new];
    }
    
    return _backView;
}

- (UILabel *)titleLabel
{
    if(!_titleLabel) {
        _titleLabel = [UIView createLabelWithTitle:@""
                                         textColor:[UIColor colorWithHexString:@"#333333"]
                                           bgColor:UIColor.clearColor
                                          fontSize:iPadValue(20, 15)
                                     textAlignment:NSTextAlignmentLeft
                                             bBold:NO];
    }
    
    return _titleLabel;
}

- (UIImageView *)arrow
{
    if(!_arrow) {
        _arrow = [UIImageView new];
        
        UIImage* image = [UIImage imageNamed:@"standard_right_arrow"];
        image = [image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        
        _arrow.image = image;
    }
    
    return _arrow;
}

@end
