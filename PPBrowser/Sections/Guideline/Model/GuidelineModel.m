//
//  GuidelineModel.m
//  PPBrowser
//
//  Created by qingbin on 2022/10/8.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "GuidelineModel.h"

@implementation GuidelineModel

+ (instancetype)videoItem
{
    GuidelineModel* item = [GuidelineModel new];
    item.type = GuideLineTypeVideo;
    item.title = NSLocalizedString(@"guideline.video.title", nil);
    item.content = NSLocalizedString(@"guideline.open.video", nil);
    item.fileUrl = @"help_videoplay";
    
    return item;
}

+ (instancetype)scriptItem
{
    GuidelineModel* item = [GuidelineModel new];
    item.type = GuideLineTypeScript;
    item.title = NSLocalizedString(@"guideline.script.title", nil);
    item.content = NSLocalizedString(@"guideline.install.script", nil);
    item.fileUrl = @"help_userscript";
    item.videoGuideLineFileName = @"video_script";
    
    return item;
}

+ (instancetype)fullscreenItem
{
    GuidelineModel* item = [GuidelineModel new];
    item.type = GuideLineTypeFullscreen;
    item.title = NSLocalizedString(@"guideline.fullscreen.title", nil);
    item.content = NSLocalizedString(@"guideline.open.fullscreen", nil);
    item.fileUrl = @"help_fullscreen";
    
    return item;
}

+ (instancetype)translateItem
{
    GuidelineModel* item = [GuidelineModel new];
    item.type = GuideLineTypeTranslate;
    item.title = NSLocalizedString(@"guideline.translate.title", nil);
    item.content = NSLocalizedString(@"guideline.open.translate", nil);
    item.fileUrl = @"help_translate";
    item.videoGuideLineFileName = @"video_translate";
    
    return item;
}

+ (instancetype)customUrlItem
{
    GuidelineModel* item = [GuidelineModel new];
    item.type = GuideLineTypeCustomUrl;
    item.title = NSLocalizedString(@"guideline.custom.url.title", nil);
    item.content = NSLocalizedString(@"guideline.custom.url", nil);
    item.fileUrl = @"help_custom_url";
    
    return item;
}

+ (instancetype)bookmarkItem
{
    GuidelineModel* item = [GuidelineModel new];
    item.type = GuideLineTypeBookmark;
    item.title = NSLocalizedString(@"guideline.bookmark.title", nil);
    item.content = NSLocalizedString(@"guideline.open.bookmark", nil);
    item.fileUrl = @"help_bookmark";
    
    return item;
}

+ (instancetype)tagitItem
{
    GuidelineModel* item = [GuidelineModel new];
    item.type = GuideLineTypeTagit;
    item.title = NSLocalizedString(@"guideline.tagit.title", nil);
    item.content = NSLocalizedString(@"guideline.open.tagit", nil);
    item.fileUrl = @"help_tagit";
    item.videoGuideLineFileName = @"video_tagit";
    
    return item;
}

+ (instancetype)autoPageItem
{
    GuidelineModel* item = [GuidelineModel new];
    item.type = GuideLineTypeAutoPage;
    item.title = NSLocalizedString(@"guideline.autopage.title", nil);
    item.content = NSLocalizedString(@"guideline.autopage.text", nil);
    item.fileUrl = @"help_autopage";
    item.videoGuideLineFileName = @"video_autopage";
    
    return item;
}

@end
