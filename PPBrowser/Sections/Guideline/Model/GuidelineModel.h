//
//  GuidelineModel.h
//  PPBrowser
//
//  Created by qingbin on 2022/10/8.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>

#import "PPEnums.h"

NS_ASSUME_NONNULL_BEGIN

@interface GuidelineModel : NSObject

@property (nonatomic, strong) NSString *title;

@property (nonatomic, strong) NSString *content;

@property (nonatomic, strong) NSString *fileUrl;

@property (nonatomic, assign) GuideLineType type;
//视频播放的名称
@property (nonatomic, strong) NSString *videoGuideLineFileName;

//辅助字段,主要是卡片化
@property (nonatomic, assign) BOOL isFirstInSection;
@property (nonatomic, assign) BOOL isLastInSection;

+ (instancetype)videoItem;
+ (instancetype)scriptItem;
+ (instancetype)fullscreenItem;
+ (instancetype)translateItem;
+ (instancetype)customUrlItem;
+ (instancetype)bookmarkItem;
+ (instancetype)tagitItem;
+ (instancetype)autoPageItem;


@end

NS_ASSUME_NONNULL_END
