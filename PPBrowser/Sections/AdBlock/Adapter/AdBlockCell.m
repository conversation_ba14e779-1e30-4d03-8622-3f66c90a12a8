//
//  AdBlockCell.m
//  Saber
//
//  Created by qing<PERSON> on 2023/4/7.
//

#import "AdBlockCell.h"

#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "UIView+FrameHelper.h"
#import "NSObject+Helper.h"
#import "ThemeProtocol.h"
#import "BrowserUtils.h"

#import "PPNotifications.h"

#import "UIImage+Extension.h"

@interface AdBlockCell()<ThemeProtocol>

@property (nonatomic, strong) UIStackView *stackView;

@property (nonatomic, strong) UILabel* titleLabel;

@property (nonatomic, strong) UILabel* updateTimeLabel;

@property (nonatomic, strong) UISwitch* switchButton;

@property (nonatomic, strong) UIView* line;

@property (nonatomic, strong) AdBlockModel* model;

@property(nonatomic,strong) UIView* backView;

@end

@implementation AdBlockCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        [self addSubviews];
        [self defineLayout];
        [self setupObservers];
        [self applyTheme];
    }
    
    return self;
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.titleLabel.textColor = [UIColor colorWithHexString:kDarkThemeColorText];
        self.line.backgroundColor = [UIColor colorWithHexString:kDarkThemeColorLine];
        self.backView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
    } else {
        self.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.titleLabel.textColor = [UIColor colorWithHexString:@"#333333"];
        self.line.backgroundColor = [[UIColor colorWithHexString:@"#999999"] colorWithAlphaComponent:0.3];
        
        self.backView.backgroundColor = UIColor.whiteColor;
    }
}

- (void)updateWithModel:(AdBlockModel *)model
{
    self.model = model;
    self.titleLabel.text = model.title;
    
    NSString* updateTime = @"";
    if(model.updateTime.length > 0) {
        updateTime = model.updateTime;
    } else {
        updateTime = model.ctime;
    }
    [self _updateTimeWith:updateTime];
    
    self.switchButton.on = model.isActive;
    
    [self updateCornerRadius];
    [self applyTheme];
}

- (void)_updateTimeWith:(NSString *)updateTime
{
    // 创建一个日期格式化对象
    NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];

    // 获取当前的日历对象
    NSCalendar *calendar = [NSCalendar currentCalendar];
    NSDate *currentDate = [NSDate date]; // 当前日期

    // 比较给定时间戳和当前日期的年、月、日
    NSDate *date = [NSDate dateWithTimeIntervalSince1970:updateTime.doubleValue];
    NSDateComponents *components = [calendar components:NSCalendarUnitYear | NSCalendarUnitMonth | NSCalendarUnitDay fromDate:date];
    NSDateComponents *currentComponents = [calendar components:NSCalendarUnitYear | NSCalendarUnitMonth | NSCalendarUnitDay fromDate:currentDate];

    if ([components year] == [currentComponents year] && [components month] == [currentComponents month] && [components day] == [currentComponents day]) {
        // 如果是今天
        [dateFormatter setDateFormat:@"今天 HH:mm"];
    } else if ([components year] == [currentComponents year] && [components month] == [currentComponents month] && [components day] == ([currentComponents day] - 1)) {
        // 如果是昨天
        [dateFormatter setDateFormat:@"昨天 HH:mm"];
    } else {
        // 其他日期
        [dateFormatter setDateFormat:@"MM月dd日 HH:mm"];
    }

    // 使用日期格式化对象将日期对象转换为字符串
    NSString *formattedDateString = [dateFormatter stringFromDate:date];
    self.updateTimeLabel.text = [NSString stringWithFormat:@"%@%@", @"最近更新于: ", formattedDateString];
}

- (void)updateCornerRadius
{
    // 圆角
    self.line.hidden = NO;
    self.backView.layer.cornerRadius = 10;
    self.backView.layer.masksToBounds = YES;
    
    if(self.model.isFirstInSection && self.model.isLastInSection) {
        //只有一个
        self.backView.layer.masksToBounds = YES;
        self.backView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner | kCALayerMinXMaxYCorner | kCALayerMaxXMaxYCorner;
        self.line.hidden = YES;
    } else {
        if(self.model.isFirstInSection) {
            //添加上圆角
            self.backView.layer.masksToBounds = YES;
            self.backView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
        } else if(self.model.isLastInSection) {
            //添加下圆角
            self.backView.layer.masksToBounds = YES;
            self.backView.layer.maskedCorners = kCALayerMinXMaxYCorner | kCALayerMaxXMaxYCorner;
            self.line.hidden = YES;
        } else {
            self.backView.layer.masksToBounds = NO;
            self.backView.layer.cornerRadius = 0;
        }
    }
}

- (void)setupObservers
{
    @weakify(self)
    [[self.switchButton rac_newOnChannel] subscribeNext:^(id x) {
        @strongify(self)
        self.model.isActive = self.switchButton.isOn;
        if(self.playOrPauseAction) {
            self.playOrPauseAction(self.model);
        }
    }];
}

#pragma mark -- layout
- (void)addSubviews
{
    [self.contentView addSubview:self.backView];
    [self.backView addSubview:self.stackView];
    [self.backView addSubview:self.switchButton];
}

- (void)defineLayout
{
    [self.backView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(iPadValue(20, 15));
        make.right.mas_offset(-iPadValue(20, 15));
        make.top.bottom.equalTo(self.contentView);
    }];
    
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(20);
        make.centerY.equalTo(self.backView);
        make.right.equalTo(self.switchButton.mas_left).offset(-10);
    }];
    
    [self.switchButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_offset(-iPadValue(20, 15));
        make.centerY.equalTo(self.contentView);
    }];
    
    UIView* line = [UIView new];
    [self.backView addSubview:line];
    [line mas_makeConstraints:^(MASConstraintMaker *make) {
       make.left.mas_offset(0);
       make.right.mas_offset(-0);
       make.height.mas_equalTo(0.5);
       make.bottom.mas_offset(0);
    }];
    self.line = line;
}

- (UILabel *)titleLabel
{
    if(!_titleLabel) {
        float font = iPadValue(20, 16);
        _titleLabel = [UIView createLabelWithTitle:nil
                                        textColor:[UIColor colorWithHexString:@"#333333"]
                                          bgColor:UIColor.clearColor
                                         fontSize:font
                                    textAlignment:NSTextAlignmentLeft
                                            bBold:NO];
    }
    
    return _titleLabel;
}

- (UILabel *)updateTimeLabel
{
    if(!_updateTimeLabel) {
        float font = iPadValue(18, 13);
        _updateTimeLabel = [UIView createLabelWithTitle:nil
                                        textColor:[UIColor colorWithHexString:@"#666666"]
                                          bgColor:UIColor.clearColor
                                         fontSize:font
                                    textAlignment:NSTextAlignmentLeft
                                            bBold:NO];
    }
    
    return _updateTimeLabel;
}

- (UIStackView *)stackView
{
    if(!_stackView) {
        _stackView = [[UIStackView alloc]initWithArrangedSubviews:@[
            self.titleLabel,
            self.updateTimeLabel,
        ]];
        _stackView.spacing = 12;
        _stackView.axis = UILayoutConstraintAxisVertical;
    }
    
    return _stackView;
}

- (UISwitch *)switchButton
{
    if(!_switchButton) {
        _switchButton = [UISwitch new];
    }
    
    return _switchButton;
}

- (UIView *)backView
{
    if(!_backView) {
        _backView = [UIView new];
        _backView.backgroundColor = UIColor.whiteColor;
    }
    
    return _backView;
}

@end
