//
//  AdBlockView.m
//  Saber
//
//  Created by q<PERSON><PERSON> on 2023/11/22.
//

#import "AdBlockView.h"

#import "Masonry.h"
#import "ReactiveCocoa.h"

#import "UIColor+Helper.h"
#import "NSObject+Helper.h"
#import "UIView+FrameHelper.h"
#import "MaizyHeader.h"
#import "UIView+Helper.h"
#import "UIImage+Extension.h"
#import "BrowserUtils.h"

#import "NSString+Helper.h"
#import "AdBlockCell.h"

#import "ThemeProtocol.h"

#import "UITableView+HintMessage.h"

#import "AdBlockManager.h"
#import "AdBlockModel.h"
#import "DatabaseUnit+AdBlock.h"

#import "PPNotifications.h"

@interface AdBlockView ()<UITableViewDelegate,UITableViewDataSource>

@property (nonatomic, strong) NSMutableArray* model;
//内置类型
@property (nonatomic, strong) NSMutableArray *builtInArray;
//自定义规则
@property (nonatomic, strong) NSMutableArray *customRuleArray;

@property (nonatomic, strong) UITableView* tableView;

@end

@implementation AdBlockView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    
    if(self) {
        [self addSubviews];
        [self defineLayout];
        [self reloadData];
    }
    
    return self;
}

- (void)reloadData
{
    DatabaseUnit* unit = [DatabaseUnit queryAllAdblocks];
    @weakify(self)
    [unit setCompleteBlock:^(id result, BOOL success) {
        @strongify(self)
        [self _reloadDataWithArray:result];
    }];
    DB_EXEC(unit);
}

- (void)_reloadDataWithArray:(NSArray *)result
{
    [self.builtInArray removeAllObjects];
    [self.customRuleArray removeAllObjects];
    
    for(AdBlockModel* obj in result) {
        if([AdBlockManager isBuiltInRule:obj.ctime]) {
            [self.builtInArray addObject:obj];
        } else {
            [self.customRuleArray addObject:obj];
        }
    }
    
    AdBlockModel* firstObj = self.builtInArray.firstObject;
    firstObj.isFirstInSection = YES;
    AdBlockModel* lastObj = self.builtInArray.lastObject;
    lastObj.isLastInSection = YES;
    
    firstObj = self.customRuleArray.firstObject;
    firstObj.isFirstInSection = YES;
    lastObj = self.customRuleArray.lastObject;
    lastObj.isLastInSection = YES;
    
    [self.tableView reloadData];
}

#pragma mark -- 无数据提醒
- (void)showOrHideHintMessage
{
    if(self.model.count > 0) {
        [self.tableView hideHintMessage];
    } else {
        UIImage* image = [UIImage imageNamed:@"empty_data_logo"];
        [self.tableView showHintMessage:NSLocalizedString(@"tableview.emptyTips", nil)
                                  image:image
                          sectionMargin:iPadValue(150, 100)];
    }
}

#pragma mark -- layout
- (void)addSubviews
{
    [self addSubview:self.tableView];
}

- (void)defineLayout
{
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
}

#pragma mark - UITableViewDataSource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    if(section == 0) {
        return self.builtInArray.count;
    } else {
        return self.customRuleArray.count;
    }
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    if(self.customRuleArray.count > 0) return 2;
    
    return 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    AdBlockModel* item;
    if(indexPath.section == 0) {
        item = self.builtInArray[indexPath.row];
    } else {
        item = self.customRuleArray[indexPath.row];
    }
    
    AdBlockCell *cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass(AdBlockCell.class)];
    [cell updateWithModel:item];
    
    @weakify(self)
    [cell setPlayOrPauseAction:^(AdBlockModel *model) {
        @strongify(self)
        [self _handleSwithActionWithModel:model];
    }];
    
    return cell;
}

- (void)_handleSwithActionWithModel:(AdBlockModel *)model
{
    if(model.isActive) {
        //比较大的文件需要从这里开始loading
        [UIView showLoading:NSLocalizedString(@"tips.handling", nil)];
        
        //检查是否需要下载+编译规则
        if(![[AdBlockManager shareInstance] isExistRuleUrl:model.url]) {
            //下载和编译规则
            [[AdBlockManager shareInstance] addRulesFromUrl:model.url completion:^(BOOL success, NSError *error) {
                if(success) {
                    [UIView showToast:NSLocalizedString(@"tips.add.success", nil)];
                    
                    dispatch_async(dispatch_get_main_queue(), ^{
                        //刷新网页
                        [[NSNotificationCenter defaultCenter] postNotificationName:kReloadCurrentWebViewNotification object:nil];
                    });
                } else {
                    [UIView showToast:NSLocalizedString(@"tips.add.fail", nil)];
                }
            }];
        } else {
            [UIView hideHud:NO];
        }
        
        DatabaseUnit* unit = [DatabaseUnit updateAdblockWithId:model.uuid isActive:true];
        DB_EXEC(unit);
    } else {
        //关闭规则开关
        DatabaseUnit* unit = [DatabaseUnit updateAdblockWithId:model.uuid isActive:false];
        [unit setCompleteBlock:^(id result, BOOL success) {
            //刷新网页
            [[NSNotificationCenter defaultCenter] postNotificationName:kReloadCurrentWebViewNotification object:nil];
        }];
        DB_EXEC(unit);
    }
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section
{
    return iPadValue(80, 60);
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section
{
    UIView* header = [UIView new];
    header.backgroundColor = UIColor.clearColor;
    
    NSString* title = NSLocalizedString(@"adblock.thirdParty.title", nil);
    if(section == 1) {
        title = NSLocalizedString(@"adblock.custom.title", nil);
    }
    
    UILabel* titleLabel = [UIView createLabelWithTitle:title
        textColor:[UIColor colorWithHexString:@"#333333"]
          bgColor:UIColor.clearColor
         fontSize:iPadValue(18, 14)
    textAlignment:NSTextAlignmentLeft
            bBold:NO];
    [header addSubview:titleLabel];
    
    float leftOffset = iPadValue(60, 30);
    float bottomOffset = iPadValue(15, 10);
    [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_offset(-bottomOffset);
        make.left.mas_offset(leftOffset);
    }];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        header.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        titleLabel.textColor = [UIColor colorWithHexString:@"#ffffff"];
    } else {
        header.backgroundColor = UIColor.clearColor;
        titleLabel.textColor = [UIColor colorWithHexString:@"#333333"];
    }
    
    return header;
}

#pragma mark -- TableView
- (BOOL)tableView:(UITableView *)tableView canEditRowAtIndexPath:(NSIndexPath *)indexPath
{
    if(indexPath.section == 0) return NO;
    
    return YES;
}

// 只适用于ios11 以及以后版本
- (UISwipeActionsConfiguration *)tableView:(UITableView *)tableView trailingSwipeActionsConfigurationForRowAtIndexPath:(NSIndexPath *)indexPath  API_AVAILABLE(ios(11.0))
{
    if(indexPath.section == 0) return nil;
    
    AdBlockModel* item = self.customRuleArray[indexPath.row];
    
    // 删除数据
    @weakify(self)
    UIContextualAction *deleteAction = [UIContextualAction contextualActionWithStyle:UIContextualActionStyleDestructive title:NSLocalizedString(@"common.delete", nil) handler:^(UIContextualAction * _Nonnull action, __kindof UIView * _Nonnull sourceView, void (^ _Nonnull completionHandler)(BOOL)) {
        @strongify(self)
        [tableView setEditing:NO animated:YES];//退出编辑模式，隐藏左滑菜单
  
        [self _handleDeleteWithModel:item];
    }];
    [deleteAction setBackgroundColor:UIColor.redColor];

    // 添加
    UISwipeActionsConfiguration *actions = [UISwipeActionsConfiguration configurationWithActions:@[deleteAction]];
    actions.performsFirstActionWithFullSwipe = NO;
    return actions;
}

- (void)_handleDeleteWithModel:(AdBlockModel *)model
{
    //删除
    DatabaseUnit* unit = [DatabaseUnit removeAdblockWithId:model.uuid];
    DB_EXEC(unit);
    
    //删除规则文件
    [[AdBlockManager shareInstance] removeRulesForUrl:model.url];
    
    //刷新页面
    [self reloadData];
    
    //刷新网页
    [[NSNotificationCenter defaultCenter] postNotificationName:kReloadCurrentWebViewNotification object:nil];
}

#pragma mark -- lazy init
- (UITableView *)tableView
{
    if(!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStyleGrouped];

        _tableView.separatorStyle  = UITableViewCellSeparatorStyleNone;
        _tableView.showsHorizontalScrollIndicator = NO;
        _tableView.showsVerticalScrollIndicator   = NO;
        _tableView.delegate   = self;
        _tableView.dataSource = self;
        _tableView.rowHeight = iPadValue(100, 80);
        _tableView.backgroundColor = UIColor.clearColor;
        
        [_tableView registerClass:[AdBlockCell class] forCellReuseIdentifier:NSStringFromClass([AdBlockCell class])];
                
        float height = iPadValue(20, 15);
        UIView* header = [[UIView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, CGFLOAT_MIN)];
        header.backgroundColor = UIColor.clearColor;
        _tableView.tableHeaderView = header;
        
        UIWindow* window = [NSObject normalWindow];
        _tableView.tableFooterView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, height+window.safeAreaInsets.bottom)];
        
        _tableView.sectionFooterHeight = 0.0;
        _tableView.sectionHeaderHeight = 0.0f;
    }
    
    return _tableView;
}

- (NSMutableArray *)model
{
    if(!_model) {
        _model = [NSMutableArray array];
    }
    
    return _model;
}

- (NSMutableArray *)builtInArray
{
    if(!_builtInArray) {
        _builtInArray = [NSMutableArray array];
    }
    
    return _builtInArray;
}

- (NSMutableArray *)customRuleArray
{
    if(!_customRuleArray) {
        _customRuleArray = [NSMutableArray array];
    }
    
    return _customRuleArray;
}

@end
