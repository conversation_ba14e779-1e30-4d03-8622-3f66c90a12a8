//
//  AdBlockConvertHelper.h
//  PPBrowser
//
//  Created by qingbin on 2024/8/24.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>

//AdBlock规则转换为Webkit content rule list
@interface AdBlockConvertHelper : NSObject

//rules: AdBlockPlus规则文件文本
//转换后的规则数组(最大长度50000条规则，多于则分为多个数组)
- (void)convertAdBlockPlusRulesToContentBlockerRules:(NSString *)rules
                                          completion:(void (^)(NSArray<NSString *>*))completion;

@end

