//
//  AdBlockManager.h
//  PPBrowser
//
//  Created by qingbin on 2024/8/25.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>

#import <WebKit/WebKit.h>
#import "AdBlockModel.h"

@interface AdBlockManager : NSObject

+ (instancetype)shareInstance;

//添加规则
- (void)addRulesFromUrl:(NSString *)url completion:(void (^)(BOOL success, NSError *error))completion;

//删除规则
- (void)removeRulesForUrl:(NSString *)url;

//列举所有规则
- (NSArray<NSString *> *)listSavedRuleUrls;

//是否存在指定的规则
- (BOOL)isExistRuleUrl:(NSString *)url;

//加载所有规则到指定webView
- (void)loadSavedRulesWithWebView:(WKWebView *)webView;

//检查某个规则是否存在，不存在则下载
- (void)checkAndLoadBuiltInRules:(NSArray<NSString *> *)urls;
//检查默认内置的规则和默认开启的规则是否已经加载
- (void)checkAndLoadBuiltInRules;

//清理旧规则
- (void)cleanupRulesExcept:(NSArray<NSString *> *)urls;

//保存并且编译生成自定义规则
- (void)saveCustomRules:(NSString *)content completion:(void (^)(BOOL success, NSError *error))completion;
//获取自定义规则
- (NSString *)getCustomRules;

//根据ctime来判断内置类型
+ (BOOL)isBuiltInRule:(NSString *)ctime;

@end


/**
 webkit content block的语法
 https://webkit.org/blog/3476/content-blockers-first-look/
 
 js版本的AdGuard SafariContentBlockerConverterCompiler
 https://github.com/AdguardTeam/SafariContentBlockerConverterCompiler/tree/master
 
 js版本的AdBlockPlus
 https://github.com/adblockplus/abp2blocklist
 
 AdguardTeam的相关开源仓库
 https://github.com/orgs/AdguardTeam/repositories?q=content+block
 
 swift版本的AdGuard
 https://github.com/AdguardTeam/SafariConverterLib
 */
