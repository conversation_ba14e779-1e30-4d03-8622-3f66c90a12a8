//
//  AdBlockConvertHelper.m
//  PPBrowser
//
//  Created by qingbin on 2024/8/24.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import "AdBlockConvertHelper.h"

#include <JavaScriptCore/JavaScript.h>
#include <JavaScriptCore/JSContext.h>
#include <JavaScriptCore/JavaScriptCore.h>

#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "NSObject+Helper.h"

#import "ResourceHelper.h"

@interface AdBlockConvertHelper() {
    JSContext *_context;
    JSValue *_converterFunc;
}

@property (nonatomic, copy) void (^completion)(NSArray<NSString *> *ruleArray);
@property (nonatomic, strong) NSMutableArray<NSString *>* ruleArray;

@end

@implementation AdBlockConvertHelper

- (instancetype)init
{
    self = [super init];
    if(self) {
        [self _setupJsContext];
    }
    
    return self;
}

- (void)_setupJsContext
{
    _context = [[JSContext alloc]init];
    
    //目前还没用到在线转换广告规则的功能
    //因此就不再把JSConverter加入到bundle里了,也不进行加密
//    NSURL* jsURL = [[NSBundle mainBundle] URLForResource:@"AdBlockConvert" withExtension:@"js"];
//    NSString* script = [NSString stringWithContentsOfURL:jsURL encoding:NSUTF8StringEncoding error:NULL];
    
    NSString* script = [ResourceHelper shareInstance].jsAdBlockConvert;
    
    _context[@"window"] = _context.globalObject;
    
    [_context evaluateScript:script];
    _converterFunc = _context[@"parseContentRule"];
    
    if (!_converterFunc || [_converterFunc isUndefined]) {
        NSLog(@"failed to init parseContentRule...");
    }
    
    // 定义回调函数
    @weakify(self)
    _context[@"jsCallback"] = ^(JSValue *result, JSValue *isFinish, JSValue *ruleCount) {
        @strongify(self)
                
        if (![result isNull]) {
            //有结果
            [self.ruleArray addObject:[result toString]];
        } else {
            //无结果
        }
        
        if([isFinish toBool]) {
            NSLog(@"广告过滤，解析了规则数量: %d", [ruleCount toInt32]);
            
            if(self.completion) {
                self.completion(self.ruleArray);
            }
        }
    };
}

- (void)convertAdBlockPlusRulesToContentBlockerRules:(NSString *)rules
                                          completion:(void (^)(NSArray<NSString *>*))completion
{
    if(rules.length == 0) {
        if(completion) {
            completion(nil);
        }
        return;
    }
    
    //设置回调
    self.completion = completion;

    //每个规则文件的最大行数,限制50000条
    [_converterFunc callWithArguments:@[rules, @(50000)]];
}

#pragma mark -- Getters

- (NSMutableArray<NSString *> *)ruleArray
{
    if(!_ruleArray) {
        _ruleArray = [NSMutableArray array];
    }
    
    return _ruleArray;
}

@end
