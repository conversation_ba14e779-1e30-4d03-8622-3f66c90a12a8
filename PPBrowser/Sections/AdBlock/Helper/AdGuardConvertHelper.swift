//
//  AdGuardConvertHelper.swift
//  PPBrowser
//
//  Created by qingbin on 2024/10/14.
//  Copyright © 2024 qingbin. All rights reserved.
//

import Foundation
import ContentBlockerConverter

@objc
class AdGuardConvertHelper: NSObject {
    @objc static func convertArray(ruleText: String) -> [String] {
        let lines: [String] = ruleText.components(separatedBy: "\n")
        
        //按照5万条拆分
        let groupSize = 50000
        var groupedLines: [[String]] = []

        for i in stride(from: 0, to: lines.count, by: groupSize) {
            let group = Array(lines[i..<min(i + groupSize, lines.count)])
            groupedLines.append(group)
        }
        
        var resultArray: [String] = []
        for (_, group) in groupedLines.enumerated() {
            let result: ConversionResult = ContentBlockerConverter().convertArray(rules: group)
            if result.converted.count > 0 {
                resultArray.append(result.converted)
            }
        }
        
        return resultArray
    }
}

