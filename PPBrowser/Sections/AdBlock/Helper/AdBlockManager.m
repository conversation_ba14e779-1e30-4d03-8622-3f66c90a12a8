//
//  AdBlockManager.m
//  PPBrowser
//
//  Created by qingbin on 2024/8/25.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import "AdBlockManager.h"

#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "NSObject+Helper.h"
#import "NSString+MKNetworkKitAdditions.h"

#import "AdBlockConvertHelper.h"
#import "AdBlockModel.h"
#import "DatabaseUnit+AdBlock.h"

#import "PPBrowser-Swift.h"

@interface AdBlockManager ()

@property (nonatomic, strong) NSMutableArray* convertHelperArray;

@property (nonatomic, strong) dispatch_queue_t queue;

@end

@implementation AdBlockManager

+ (instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    static AdBlockManager* obj;
    dispatch_once(&onceToken, ^{
        obj = [[AdBlockManager alloc]init];
    });

    return obj;
}

- (instancetype)init
{
    self = [super init];
    if(self) {
        self.queue = dispatch_queue_create("com.adblock.queue", DISPATCH_QUEUE_SERIAL);
    }
    
    return self;
}

#pragma mark -- 添加规则

- (void)addRulesFromUrl:(NSString *)url completion:(void (^)(BOOL success, NSError *error))completion
{
    NSURLSession *session = [NSURLSession sharedSession];
    @weakify(self)
    NSURLSessionDataTask *task = [session dataTaskWithURL:[NSURL URLWithString:url] completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {
        @strongify(self)
        if (error) {
            dispatch_async(dispatch_get_main_queue(), ^{
                if(completion) {
                    completion(NO, error);
                }
            });
            return;
        }
        
        NSString *rulesString = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
        //删除前后空格和换行符
        NSCharacterSet *whitespaceAndNewline = [NSCharacterSet whitespaceAndNewlineCharacterSet];
        rulesString = [rulesString stringByTrimmingCharactersInSet:whitespaceAndNewline];
        
        //由于AdBlockPlus的转换规则引擎有问题，例如 ||baidu.com^ 本应该是屏蔽baidu.com的打开的，但是真正转换出来的webkit content block却不是对应的意思
        //因此换了AdGuard的转换规则引擎
//        AdBlockConvertHelper* converHelper = [AdBlockConvertHelper new];
//        [self.convertHelperArray addObject:converHelper];
//        
//        @weakify(self)
//        [converHelper convertAdBlockPlusRulesToContentBlockerRules:rulesString completion:^(NSArray<NSString *> *contentBlockerRules) {
//            @strongify(self)
//            if(contentBlockerRules.count > 0) {
//                [self saveRulesToMultipleFiles:contentBlockerRules forUrl:url completion:completion];
//            } else {
//                dispatch_async(dispatch_get_main_queue(), ^{
//                    if(completion) {
//                        completion(NO, nil);
//                    }
//                });
//            }
//            
//            [self.convertHelperArray removeObject:converHelper];
//        }];
        
        NSArray<NSString *>* contentBlockerRules = [AdGuardConvertHelper convertArrayWithRuleText:rulesString];
        if(contentBlockerRules.count > 0) {
            [self saveRulesToMultipleFiles:contentBlockerRules forUrl:url isCustomRule:NO completion:completion];
        } else {
            dispatch_async(dispatch_get_main_queue(), ^{
                if(completion) {
                    completion(NO, nil);
                }
            });
        }
    }];
    
    [task resume];
}

#pragma mark -- 删除规则

- (void)removeRulesForUrl:(NSString *)url
{
    //这个函数需要运行在主线程!!!
    
    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSString *cacheDir = [self cacheDirectory];
    NSString *urlHash = [self hashForURL:url];
    NSError *error;
    
    NSArray *files = [fileManager contentsOfDirectoryAtPath:cacheDir error:&error];
    if (error) {
        return;
    }
    
    WKContentRuleListStore* listStore = WKContentRuleListStore.defaultStore;
    for (NSString *file in files) {
        if ([file hasPrefix:urlHash]) {
            //如果删除正在运行的规则缓存，应用会崩溃
            NSString *fullPath = [cacheDir stringByAppendingPathComponent:file];
            [fileManager removeItemAtPath:fullPath error:&error];
            
            //删除自定义规则，广告过滤规则缓存
            [listStore lookUpContentRuleListForIdentifier:file completionHandler:^(WKContentRuleList *ruleList, NSError *error) {
                if(!error && ruleList) {
                    //真是服了，如果这里没有写completionHandler，而是写了nil，那么毫无疑问，会崩溃!!!
                    [listStore removeContentRuleListForIdentifier:file completionHandler:^(NSError *error) {
                        if(error) {
                            NSLog(@"error = %@", error.localizedDescription);
                        }
                    }];
                }
            }];
        }
    }
}

//列举所有规则
- (NSArray<NSString *> *)listSavedRuleUrls
{
    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSString *cacheDir = [self cacheDirectory];
    NSError *error;
    
    NSArray *files = [fileManager contentsOfDirectoryAtPath:cacheDir error:&error];
    if (error) {
        return nil;
    }
    
    NSMutableSet *urlSet = [NSMutableSet set];
    for (NSString *file in files) {
        NSString *urlStringMd5 = [file componentsSeparatedByString:@"-"].firstObject;
        if (urlStringMd5) {
            [urlSet addObject:urlStringMd5];
        }
    }
    
    return [urlSet allObjects];
}

//是否存在指定的规则
- (BOOL)isExistRuleUrl:(NSString *)url
{
    if(url.length == 0) return false;
    
    NSString* urlMd5 = [url md5];
    for(NSString* urlStringMd5 in [self listSavedRuleUrls]) {
        if([urlMd5 isEqualToString:urlStringMd5]) {
            return true;
        }
    }
    
    return false;
}

#pragma mark -- 加载所有规则(已激活的)
- (void)loadSavedRulesWithWebView:(WKWebView *)webView
{
    //首次打开的时候，由于要请求网络权限，导致没有成功加载到规则，因此在这里重新请求一次
    [[AdBlockManager shareInstance] checkAndLoadBuiltInRules];
    
    @weakify(self)
    DatabaseUnit* unit = [DatabaseUnit queryAllAdblocks];
    [unit setCompleteBlock:^(id result, BOOL success) {
        @strongify(self)
        @weakify(self)
        dispatch_async(self.queue, ^{
            @strongify(self)
            [self _loadSavedRulesWithWebView:webView AdBlockArray:result];
        });
    }];
    DB_EXEC(unit);
}

- (void)_loadSavedRulesWithWebView:(WKWebView *)webView AdBlockArray:(NSArray *)adBlockArray
{
    if(!webView) return;
    
    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSString *cacheDir = [self cacheDirectory];
    NSError *error;
    
    NSArray *files = [fileManager contentsOfDirectoryAtPath:cacheDir error:&error];
    if (error) {
        return;
    }
    
    NSMutableDictionary* urlMd5Map = [NSMutableDictionary dictionary];
    //内置规则
    NSString* easylistChinaUrl = [AdBlockModel easylistChinaUrl];
    urlMd5Map[[easylistChinaUrl md5]] = easylistChinaUrl;
    //打开开关的规则
    for(AdBlockModel* obj in adBlockArray) {
        if(obj.isActive) {
            urlMd5Map[[obj.url md5]] = obj.url;
        }
    }
    
    //自定义规则
    NSString* customRuleFileNameMd5 = [[self customRuleFileName] md5];
    urlMd5Map[customRuleFileNameMd5] = [self customRuleFileName];
    
    @weakify(webView)
    WKContentRuleListStore* listStore = WKContentRuleListStore.defaultStore;
    for (NSString *file in files) {
        NSString *urlStringMd5 = [file componentsSeparatedByString:@"-"].firstObject;
        if(urlMd5Map[urlStringMd5] == NULL) continue;
        
        [listStore lookUpContentRuleListForIdentifier:file completionHandler:^(WKContentRuleList *ruleList, NSError *error) {
            @strongify(webView)
            @weakify(webView)
            if(ruleList && !error) {
                //有缓存
                dispatch_async(dispatch_get_main_queue(), ^{
                    @strongify(webView)
                    if(!webView) return;
                    [webView.configuration.userContentController addContentRuleList:ruleList];
                });
                
            } else {
                //没有缓存
                NSString *fullPath = [cacheDir stringByAppendingPathComponent:file];
                NSData *data = [NSData dataWithContentsOfFile:fullPath];
                NSString *ruleString = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
                
                if(file.length>0 && ruleString.length>0) {
                    [listStore compileContentRuleListForIdentifier:file encodedContentRuleList:ruleString completionHandler:^(WKContentRuleList *contentRuleList, NSError *error) {
                        @strongify(webView)
                        @weakify(webView)
                        if (contentRuleList && !error) {
                            dispatch_async(dispatch_get_main_queue(), ^{
                                @strongify(webView)
                                if(!webView) return;
                                [webView.configuration.userContentController addContentRuleList:contentRuleList];
                            });
                        }
                    }];
                } else {
#if DEBUG
                    NSLog(@"Error: 生成广告规则失败...");
#endif
                }
            }
        }];
    }
}

#pragma mark -- 检查某个规则是否存在，不存在则下载
- (void)checkAndLoadBuiltInRules:(NSArray<NSString *> *)urls
{
    if (urls.count == 0) {
        return;
    }

    for(NSString* url in urls) {
        if(![self isExistRuleUrl:url]) {
            // 规则不存在，下载并保存
            [self addRulesFromUrl:url completion:nil];
        }
    }
}

//检查默认内置的规则和默认开启的规则是否已经加载
- (void)checkAndLoadBuiltInRules
{
    NSArray* loadUrls = @[[AdBlockModel easylistChinaUrl], [AdBlockModel easylistUrl]];
    [[AdBlockManager shareInstance] checkAndLoadBuiltInRules:loadUrls];
}

#pragma mark -- 清理旧规则
- (void)cleanupRulesExcept:(NSArray<NSString *> *)urls
{
    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSString *rulesDir = [self cacheDirectory];
    NSError *error;
    
    NSArray *files = [fileManager contentsOfDirectoryAtPath:rulesDir error:&error];
    if (error) {
        return;
    }
    
    NSMutableSet *urlHashes = [NSMutableSet set];
    for (NSString *url in urls) {
        [urlHashes addObject:[self hashForURL:url]];
    }
    
    for (NSString *file in files) {
        NSString *urlHash = [file componentsSeparatedByString:@"-"].firstObject;
        if (![urlHashes containsObject:urlHash]) {
            NSString *fullPath = [rulesDir stringByAppendingPathComponent:file];
            [fileManager removeItemAtPath:fullPath error:&error];
        }
    }
}

#pragma mark - 自定义规则

- (NSString *)filePathForCustomRule
{
    NSFileManager *fileManager = [NSFileManager defaultManager];
    
    NSString *fileName = [NSString stringWithFormat:@"focusCustomRule.txt"];
    NSString *filePath = [[self customRuleDirectory] stringByAppendingPathComponent:fileName];
    
    if(![fileManager fileExistsAtPath:filePath]) {
        [fileManager createFileAtPath:filePath contents:NULL attributes:nil];
    }
    
    return filePath;
}

//获取自定义规则
- (NSString *)getCustomRules
{
    NSFileManager *fileManager = [NSFileManager defaultManager];
    
    NSString* filePath = [self filePathForCustomRule];
    NSData* data = [fileManager contentsAtPath:filePath];
    NSString* customRules = [[NSString alloc]initWithData:data encoding:NSUTF8StringEncoding];
    
    return customRules;
}

//保存并且编译生成自定义规则
- (void)saveCustomRules:(NSString *)content completion:(void (^)(BOOL success, NSError *error))completion
{
    NSFileManager *fileManager = [NSFileManager defaultManager];
    
    //去除空格和换行符
    NSCharacterSet *whitespaceAndNewline = [NSCharacterSet whitespaceAndNewlineCharacterSet];
    content = [content stringByTrimmingCharactersInSet:whitespaceAndNewline];
    
    NSString* filePath = [self filePathForCustomRule];
    //删除原文件
    if([fileManager fileExistsAtPath:filePath]) {
        [fileManager removeItemAtPath:filePath error:nil];
    }
    
    //删除规则文件
    [self removeRulesForUrl:[self customRuleFileName]];

    //保存
    NSData *jsonData = [content dataUsingEncoding:NSUTF8StringEncoding];
    NSError *error;
    [jsonData writeToFile:filePath options:NSDataWritingAtomic error:&error];
    
    //生成广告过滤规则
    dispatch_async(self.queue, ^{
        NSArray<NSString *>* contentBlockerRules = [AdGuardConvertHelper convertArrayWithRuleText:content];
        if(contentBlockerRules.count > 0) {
            [self saveRulesToMultipleFiles:contentBlockerRules forUrl:nil isCustomRule:YES completion:completion];
        } else {
            if(completion) {
                completion(YES, nil);
            }
        }
    });
}

#pragma mark - Private Methods

- (void)saveRulesToMultipleFiles:(NSArray<NSString *> *)rules
                          forUrl:(NSString *)url
                    isCustomRule:(BOOL)isCustomRule
                      completion:(void (^)(BOOL success, NSError *error))completion
{
    __block NSInteger successCount = 0;
    __block NSError *lastError;
    NSFileManager* fileManager = [NSFileManager defaultManager];
    
    dispatch_group_t group = dispatch_group_create();
    
    for (NSInteger i = 0; i < rules.count; i++) {
        dispatch_group_enter(group);
        NSString *ruleString = rules[i];
        NSString *fileName;
        if(isCustomRule) {
            fileName = [NSString stringWithFormat:@"%@-%ld.json", [self hashForURL:[self customRuleFileName]], (long)i];
        } else {
            //从链接导入
            fileName = [NSString stringWithFormat:@"%@-%ld.json", [self hashForURL:url], (long)i];
        }

        NSString *filePath = [[self cacheDirectory] stringByAppendingPathComponent:fileName];
        
        if([fileManager fileExistsAtPath:filePath]) {
            //删除旧文件
            [fileManager removeItemAtPath:filePath error:nil];
        }
        
        NSData *jsonData = [ruleString dataUsingEncoding:NSUTF8StringEncoding];

        NSError *error;
        if ([jsonData writeToFile:filePath options:NSDataWritingAtomic error:&error]) {
            successCount++;
        } else {
            lastError = error;
        }
        
#if DEBUG
//        NSString* writePath = [NSString stringWithFormat:@"/Users/<USER>/Desktop/%@", fileName];
//        [ruleString writeToFile:writePath atomically:YES encoding:NSUTF8StringEncoding error:&error];
//        if (error) {
//            NSLog(@"导出失败");
//        } else {
//            NSLog(@"导出成功 : %@",writePath);
//        }
#endif
        
        dispatch_group_leave(group);
    }
    
    dispatch_group_notify(group, dispatch_get_main_queue(), ^{
        if(completion) {
            completion(successCount == rules.count, lastError);
        }
    });
}

- (NSString *)cacheDirectory
{
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSLibraryDirectory, NSUserDomainMask, YES);
    NSString *libraryDirectory = [paths objectAtIndex:0];
    NSString *adBlockRulesDir = [libraryDirectory stringByAppendingPathComponent:@"AdBlockRules"];
    
    NSFileManager *fileManager = [NSFileManager defaultManager];
    if (![fileManager fileExistsAtPath:adBlockRulesDir]) {
        [fileManager createDirectoryAtPath:adBlockRulesDir withIntermediateDirectories:YES attributes:nil error:nil];
    }
    
    return adBlockRulesDir;
}

// 自定义规则保存路径(还没转换的规则)
- (NSString *)customRuleDirectory
{
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSLibraryDirectory, NSUserDomainMask, YES);
    NSString *libraryDirectory = [paths objectAtIndex:0];
    NSString *adBlockRulesDir = [libraryDirectory stringByAppendingPathComponent:@"AdBlockCustomRules"];
    
    NSFileManager *fileManager = [NSFileManager defaultManager];
    if (![fileManager fileExistsAtPath:adBlockRulesDir]) {
        [fileManager createDirectoryAtPath:adBlockRulesDir withIntermediateDirectories:YES attributes:nil error:nil];
    }
    
    return adBlockRulesDir;
}

// 自定义规则文件名称
- (NSString *)customRuleFileName
{
    return @"focusCustomRule.txt";
}

- (NSString *)hashForURL:(NSString *)url
{
    // 使用URL的字符串表示进行简单的MD5哈希
    return [url md5];
}

//根据ctime来判断内置类型
+ (BOOL)isBuiltInRule:(NSString *)ctime
{
    if(ctime.length <= 0) return NO;
    
    return ctime.intValue <= 5;
}

#pragma mark -- Getters

- (NSMutableArray *)convertHelperArray
{
    if(!_convertHelperArray) {
        _convertHelperArray = [NSMutableArray array];
    }
    
    return _convertHelperArray;
}

@end
