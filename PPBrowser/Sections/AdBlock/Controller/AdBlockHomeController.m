//
//  AdBlockHomeController.m
//  PPBrowser
//
//  Created by qingbin on 2023/11/28.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import "AdBlockHomeController.h"

#import "Masonry.h"
#import "MaizyHeader.h"
#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "ReactiveCocoa.h"
#import "UIView+FrameHelper.h"
#import "UIViewController+Helper.h"
#import "NSObject+Helper.h"

#import "SettingSwitchView.h"
#import "SettingTextAndArrowView.h"
#import "SettingStepperView.h"
#import "SettingArrowView.h"
#import "SettingSwitchAndTextView.h"

#import "PPNotifications.h"

#import "TagitViewController.h"
#import "WebBlacklistController.h"

#import "BrowserUtils.h"

#import "PaymentManager.h"
#import "VIPController.h"
#import "AdBlockViewController.h"
#import "CustomRuleViewController.h"
#import "UIAlertController+SafePresentation.h"

@interface AdBlockHomeController ()

@property (nonatomic, strong) UIScrollView* scrollView;

@property (nonatomic, strong) UIStackView* stackView;
// 开启广告过滤
@property (nonatomic, strong) SettingSwitchAndTextView *adEnabledView;
// 屏蔽侵入式广告
@property (nonatomic, strong) SettingSwitchAndTextView *enabledFixupAdblockView;
// 网页黑名单
@property (nonatomic, strong) SettingArrowView* blockURLView;
// 标记模式
@property (nonatomic, strong) SettingArrowView* tagitView;
// 订阅更多规则
@property (nonatomic, strong) SettingArrowView* adBlockView;
// 自定义规则
@property (nonatomic, strong) SettingArrowView *customRuleView;

@end

@implementation AdBlockHomeController

- (void)viewDidLoad
{
    [super viewDidLoad];

    self.title = NSLocalizedString(@"adblock.title", nil);
    
    [self addSubviews];
    [self defineLayout];
    [self updateWithModel];
    [self setupObservers];
    [self createCustomLeftBarButtonItem];
    
    [self applyTheme];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
    } else {
        self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    //只有当前的controller会触发一次, 其它已存在的controller走通知
    if(needReload) {
        //修改暗黑模式
        [self applyTheme];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}

- (void)updateWithModel
{    
    BOOL enabledAdblock = [[PreferenceManager shareInstance].items.enabledAdblock boolValue];
    [self.adEnabledView updateWithTitle:NSLocalizedString(@"webSetting.adfilter.text", nil)
                                 detail:NSLocalizedString(@"adblock.enable.adblock.tips", nil)
                                   isOn:enabledAdblock];
    
    BOOL enabledFixupAdblock = [[PreferenceManager shareInstance].items.enabledFixupAdblock boolValue];
    [self.enabledFixupAdblockView updateWithTitle:NSLocalizedString(@"tagit.block.fixup", nil)
                                           detail:NSLocalizedString(@"adblock.enable.fixupadblock.tips", nil)
                                             isOn:enabledFixupAdblock];
    
    [self.blockURLView updateWithTitle:NSLocalizedString(@"web.blacklist", nil)];
    [self.tagitView updateWithTitle:NSLocalizedString(@"tagit.title", nil)];
    
    [self.adBlockView updateWithTitle:NSLocalizedString(@"adblock.subscribe.rule.title", nil)];
    
    [self.customRuleView updateWithTitle:NSLocalizedString(@"adblock.custom.rule.title", nil)];
}

- (void)setupObservers
{
    @weakify(self)
    [self.adEnabledView setDidSwithAction:^(BOOL isOn) {
        [PreferenceManager shareInstance].items.enabledAdblock = @(isOn);
        [[PreferenceManager shareInstance] encode];
        
        [[NSNotificationCenter defaultCenter] postNotificationName:kReloadUserScriptNotification object:nil];
    }];
    
    [self.enabledFixupAdblockView setDidSwithAction:^(BOOL isOn) {
        @strongify(self)
        if(isOn && ![self checkIsVip]) {
            [self.enabledFixupAdblockView.switchView setOn:NO];
            return;
        }
        
        [PreferenceManager shareInstance].items.enabledFixupAdblock = @(isOn);
        [[PreferenceManager shareInstance] encode];
    }];
    
    [self.blockURLView setDidAction:^{
        @strongify(self)
        WebBlacklistController* vc = [WebBlacklistController new];
        [self.navigationController pushViewController:vc animated:YES];
    }];
    
    [self.tagitView setDidAction:^{
        @strongify(self)
        TagitViewController* vc = [TagitViewController new];
        [self.navigationController pushViewController:vc animated:YES];
    }];
    
    [self.adBlockView setDidAction:^{
        @strongify(self)
        AdBlockViewController* vc = [AdBlockViewController new];
        [self.navigationController pushViewController:vc animated:YES];
    }];
    
    [self.customRuleView setDidAction:^{
        @strongify(self)
        CustomRuleViewController* vc = [CustomRuleViewController new];
        [self.navigationController pushViewController:vc animated:YES];
    }];
}

- (BOOL)checkIsVip
{
    BOOL isVip = [[PaymentManager shareInstance] isVip];
    if(!isVip) {
        UIAlertController* alertController = [UIAlertController alertControllerWithTitle:NSLocalizedString(@"vip.alert.title", nil) message:NSLocalizedString(@"vip.alert.tagit.text", nil) preferredStyle:UIAlertControllerStyleAlert];
        UIAlertAction* action = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.cancel", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            [alertController dismissViewControllerAnimated:YES completion:nil];
        }];
        [alertController addAction:action];

        @weakify(self)
        action = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.toKnowMore", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            @strongify(self)
            [VIPController jumpToVipWithController:self completion:nil];
        }];
        [alertController addAction:action];

//        [self presentViewController:alertController animated:YES completion:nil];
        //v2.6.8, 统一present方法，防止崩溃
        [alertController presentSafelyFromViewController:self];
    }
    
    return isVip;
}

#pragma mark -- layout

- (void)addSubviews
{
    //https://stackoverflow.com/questions/33927914/how-can-i-set-the-cornerradius-of-a-uistackview
    //UIStackView添加圆角适配
    
    [self.view addSubview:self.scrollView];
    [self.scrollView addSubview:self.stackView];
}

- (void)defineLayout
{
    float leftOffset = iPadValue(30, 15);
    float topOffset = iPadValue(30, 15);
    
    [self.scrollView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(leftOffset);
        make.right.mas_offset(-leftOffset);
        make.top.equalTo(self.view);
        make.bottom.equalTo(self.view);
    }];
    
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.scrollView).offset(topOffset);
        make.bottom.equalTo(self.scrollView).offset(-topOffset);
        make.left.equalTo(self.view).offset(leftOffset);
        make.right.equalTo(self.view).offset(-leftOffset);
    }];
        
    [self.blockURLView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([SettingArrowView height]);
    }];
    
    [self.tagitView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([SettingArrowView height]);
    }];
    
    [self.adBlockView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([SettingArrowView height]);
    }];
    
    [self.customRuleView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([SettingArrowView height]);
    }];
}

#pragma mark -- Getters

- (UIStackView *)stackView
{
    if(!_stackView) {
        _stackView = [[UIStackView alloc]initWithArrangedSubviews:@[
            self.adEnabledView,
            self.enabledFixupAdblockView,
            
            self.blockURLView,
            self.tagitView,
            self.adBlockView,
            self.customRuleView,
        ]];
        
        _stackView.spacing = 0;
        _stackView.axis = UILayoutConstraintAxisVertical;
        
        _stackView.backgroundColor = UIColor.clearColor;
        
        float offset = iPadValue(30, 20);
        [_stackView setCustomSpacing:offset afterView:self.enabledFixupAdblockView];
        
        _stackView.layer.cornerRadius = 10;
        _stackView.layer.masksToBounds = YES;
    }
    
    return _stackView;
}

- (SettingSwitchAndTextView *)adEnabledView
{
    if(!_adEnabledView) {
        _adEnabledView = [[SettingSwitchAndTextView alloc]initWithShowLine:YES];
        
        _adEnabledView.layer.cornerRadius = iPadValue(20, 10);
        _adEnabledView.layer.maskedCorners = UIRectCornerTopLeft | UIRectCornerTopRight;
    }
    
    return _adEnabledView;
}

- (SettingSwitchAndTextView *)enabledFixupAdblockView
{
    if(!_enabledFixupAdblockView) {
        _enabledFixupAdblockView = [[SettingSwitchAndTextView alloc]initWithShowLine:NO];
        
        _enabledFixupAdblockView.layer.cornerRadius = iPadValue(20, 10);
        _enabledFixupAdblockView.layer.maskedCorners = UIRectCornerBottomLeft | UIRectCornerBottomRight;
    }
    
    return _enabledFixupAdblockView;
}

- (SettingArrowView *)blockURLView
{
    if(!_blockURLView) {
        _blockURLView = [[SettingArrowView alloc] initWithShowLine:YES];
        
        _blockURLView.layer.cornerRadius = iPadValue(20, 10);
        _blockURLView.layer.maskedCorners = UIRectCornerTopLeft | UIRectCornerTopRight;
    }
    
    return _blockURLView;
}

- (SettingArrowView *)tagitView
{
    if(!_tagitView) {
        _tagitView = [[SettingArrowView alloc] initWithShowLine:YES];
    }
    
    return _tagitView;
}

- (SettingArrowView *)adBlockView
{
    if(!_adBlockView) {
        _adBlockView = [[SettingArrowView alloc] initWithShowLine:YES];
    }
    
    return _adBlockView;
}

- (SettingArrowView *)customRuleView
{
    if(!_customRuleView) {
        _customRuleView = [[SettingArrowView alloc] initWithShowLine:NO];
        
        _customRuleView.layer.cornerRadius = iPadValue(20, 10);
        _customRuleView.layer.maskedCorners = UIRectCornerBottomLeft | UIRectCornerBottomRight;
    }
    
    return _customRuleView;
}

- (UIScrollView *)scrollView
{
    if(!_scrollView) {
        _scrollView = [[UIScrollView alloc]init];
        _scrollView.showsVerticalScrollIndicator = NO;
        _scrollView.backgroundColor = UIColor.clearColor;
    }
    
    return _scrollView;
}

@end
