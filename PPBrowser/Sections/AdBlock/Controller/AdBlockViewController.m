//
//  AdBlockViewController.m
//  PPBrowser
//
//  Created by qingbin on 2024/8/25.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import "AdBlockViewController.h"

#import "AdBlockView.h"

#import "Masonry.h"
#import "ReactiveCocoa.h"

#import "UIColor+Helper.h"
#import "NSObject+Helper.h"
#import "UIView+FrameHelper.h"
#import "MaizyHeader.h"

#import "UIView+Helper.h"
#import "UIImage+Extension.h"
#import "BrowserUtils.h"

#import "BaseNavigationController.h"

#import "AdBlockManager.h"
#import "AdBlockModel.h"
#import "DatabaseUnit+AdBlock.h"

#import "PPNotifications.h"
#import "UIAlertController+SafePresentation.h"

@interface AdBlockViewController ()<ThemeProtocol>

@property (nonatomic, strong) UIImageView* rightImageView;

@property (nonatomic, strong) AdBlockView *adBlockView;

@end

@implementation AdBlockViewController

- (void)viewDidLoad 
{
    [super viewDidLoad];
    
    self.title = NSLocalizedString(@"adblock.subscribe.rule.title", nil);

    [self addSubviews];
    [self defineLayout];
    [self setupObservers];
    
    [self createCustomLeftBarButtonItem];
    [self _createCustomRightBarButtonItem];
    
    [self applyTheme];
}

- (void)setupObservers
{
    
}

#pragma mark -- 屏幕旋转
- (void)viewWillTransitionToSize:(CGSize)size withTransitionCoordinator:(id<UIViewControllerTransitionCoordinator>)coordinator
{
    [self.adBlockView reloadData];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.rightImageView.tintColor = UIColor.whiteColor;
    } else {
        self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.rightImageView.tintColor = [UIColor colorWithHexString:@"#222222"];
    }
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    //只有当前的controller会触发一次, 其它已存在的controller走通知
    if(needReload) {
        //修改暗黑模式
        [self applyTheme];
        [self.adBlockView reloadData];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
    [self.adBlockView reloadData];
}

#pragma mark -- layout
- (void)addSubviews
{
    [self.view addSubview:self.adBlockView];
}

- (void)defineLayout
{
    [self.adBlockView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view);
        make.left.right.equalTo(self.view);
        make.bottom.equalTo(self.view);
    }];
}

#pragma mark -- 导航栏
- (void)_createCustomRightBarButtonItem
{
    UIButton* rightButton = [[UIButton alloc] initWithFrame:CGRectMake(0.0, 0.0f, 44.0f, 44.0)];
    [rightButton setBackgroundColor:[UIColor clearColor]];

    UIImageView* imageView = [UIImageView new];
    
    UIImage* image = [UIImage ext_systemImageNamed:@"plus"
                                         pointSize:23
                                        renderMode:UIImageRenderingModeAlwaysTemplate];
    imageView.image = image;
    
    self.rightImageView = imageView;
    
    [rightButton addSubview:imageView];
    [imageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(rightButton);
    }];
    
    [rightButton addTarget:self action:@selector(rightBarbuttonClick:) forControlEvents:UIControlEventTouchUpInside];
    UIBarButtonItem* barItem = [[UIBarButtonItem alloc] initWithCustomView:rightButton];

    UIBarButtonItem *spacer = [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemFixedSpace target:nil action:nil];
    spacer.width = -16.0f; // for example shift right bar button to the right

    self.navigationItem.rightBarButtonItems = @[spacer, barItem];
}

- (void)rightBarbuttonClick:(id)sender
{
    UIAlertController* alertController = [UIAlertController alertControllerWithTitle:nil message:nil preferredStyle:UIAlertControllerStyleActionSheet];

//    UIPopoverPresentationController* popover = alertController.popoverPresentationController;
//    if([BrowserUtils isiPad]) {
//        //iPad
////        UIBarButtonItem* barButton = [[UIBarButtonItem alloc]initWithCustomView:sender];
////        popover.barButtonItem = barButton;
//    } else {
//        //iPhone
//        popover.barButtonItem = sender;
//    }
    
    UIAlertAction *action = [UIAlertAction actionWithTitle:NSLocalizedString(@"adblock.import.from.link", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        //从URL导入
        [self _showAlertView2ImportURL];
    }];
    [alertController addAction:action];
    
    action = [UIAlertAction actionWithTitle:NSLocalizedString(@"adblock.update.rule", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        //一键更新
        [self _updateAllItems];
    }];
    [alertController addAction:action];
    
    action = [UIAlertAction actionWithTitle:NSLocalizedString(@"common.cancel", nil) style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
        [alertController dismissViewControllerAnimated:YES completion:nil];
    }];
    [alertController addAction:action];

//    if([BrowserUtils isiPad]) {
//        //适配iPad
//        UIPopoverPresentationController* popover = alertController.popoverPresentationController;
//        if(popover) {
//            UIView* button = sender ?: self.rightImageView;
//            popover.sourceView = button;
//            
//            float width = CGRectGetWidth(button.frame);
//            float height = CGRectGetHeight(button.frame);
//            popover.sourceRect = CGRectMake(width/2.0,height/2.0+height, 0, 0); //指向的位置
//            popover.permittedArrowDirections = UIPopoverArrowDirectionAny;
//        }
//    }
    
    if([BrowserUtils isiPad]) {
        //iPad
        UIView* button = sender ?: self.rightImageView;
        
        float width = CGRectGetWidth(button.frame);
        float height = CGRectGetHeight(button.frame);
        CGRect sourceRect = CGRectMake(width/2.0,height, 1, 1); //指向的位置
        [alertController presentSafelyFromViewController:self sourceView:button sourceRect:sourceRect];
    } else {
        //iPhone
        //v2.6.8, 统一present方法，防止崩溃
        [alertController presentSafelyFromViewController:self barButtonItem:sender];
    }
    
//    [self presentViewController:alertController animated:YES completion:nil];
}

#pragma mark -- 一键更新
- (void)_updateAllItems
{
    [UIView showLoading:NSLocalizedString(@"tips.handling", nil)];
    
    //只更新打开开关的规则和内置规则
    DatabaseUnit* unit = [DatabaseUnit queryAllAdblocks];
    [unit setCompleteBlock:^(id result, BOOL success) {
        if(success) {
            [self _updateItemsWithArray:result];
        } else {
            [UIView showFailed:NSLocalizedString(@"userscript.update.fail", nil)];
        }
    }];
    DB_EXEC(unit);
}

- (void)_updateItemsWithArray:(NSArray *)array
{
    NSMutableArray* updateObjectArray = [NSMutableArray array];
    
    NSMutableArray* updateRuleArray = [NSMutableArray array];
    [updateRuleArray addObject:[AdBlockModel easylistChinaUrl]];
    
    for(AdBlockModel* obj in array) {
        if(obj.isActive) {
            [updateRuleArray addObject:obj.url];
            [updateObjectArray addObject:obj];
        }
    }
    
    //先删除本地规则文件和规则缓存
    for(NSString* obj in updateRuleArray) {
        [[AdBlockManager shareInstance] removeRulesForUrl:obj];
    }
    
    dispatch_group_t group = dispatch_group_create();
    
    //重新添加
    for(NSString* obj in updateRuleArray) {
        dispatch_group_enter(group);
        
        [[AdBlockManager shareInstance] addRulesFromUrl:obj completion:^(BOOL success, NSError *error) {
            dispatch_group_leave(group);
        }];
    }
    
    //更新更新时间
    for(AdBlockModel* obj in updateObjectArray) {
        DatabaseUnit* unit = [DatabaseUnit updateAdblockWithId:obj.uuid];
        DB_EXEC(unit);
    }
    
    @weakify(self)
    dispatch_group_notify(group, dispatch_get_main_queue(), ^{
        @strongify(self)
        [UIView showSucceed:NSLocalizedString(@"userscript.update.success", nil)];
        
        //刷新时间
        [self.adBlockView reloadData];
        
        //刷新网页
        [[NSNotificationCenter defaultCenter] postNotificationName:kReloadCurrentWebViewNotification object:nil];
    });
}

#pragma mark -- 从链接导入
/// 显示弹窗获取脚本链接
- (void)_showAlertView2ImportURL
{    
    // 创建UIAlertController
    UIAlertController *alertController = [UIAlertController alertControllerWithTitle:@""
                                                                             message:NSLocalizedString(@"common.enter.tips", nil)
                                                                      preferredStyle:UIAlertControllerStyleAlert];

    // 添加标题输入框
    [alertController addTextFieldWithConfigurationHandler:^(UITextField * _Nonnull textField) {
        textField.placeholder = NSLocalizedString(@"common.title", nil);
        textField.text = @"";  // 可以设置默认值
    }];

    // 添加链接输入框
    [alertController addTextFieldWithConfigurationHandler:^(UITextField * _Nonnull textField) {
        textField.placeholder = NSLocalizedString(@"common.link", nil);
        textField.text = @"";  // 可以设置默认值
    }];

    // 添加"确定"按钮
    @weakify(self)
    UIAlertAction *confirmAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.confirm", nil)
                                                            style:UIAlertActionStyleDefault
                                                          handler:^(UIAlertAction * _Nonnull action) {
        @strongify(self)
        // 获取输入框的值
        NSString *title = alertController.textFields[0].text;
        NSString *link = alertController.textFields[1].text;
        
        title = [title stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceCharacterSet]];
        if(title.length == 0) {
            [UIView showToast:NSLocalizedString(@"addCustomTag.titlePlaceholder", nil)];
            return;
        }
        
        link = [link stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceCharacterSet]];
        if(link.length == 0) {
            [UIView showToast:NSLocalizedString(@"adblock.enter.link", nil)];
            return;
        }
        
        [self _importAdRulesFromUrl:link title:title];
    }];

    // 添加"取消"按钮
    UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.cancel", nil)
                                                           style:UIAlertActionStyleCancel
                                                         handler:nil];

    // 将操作按钮添加到UIAlertController
    [alertController addAction:confirmAction];
    [alertController addAction:cancelAction];

    // 显示UIAlertController
//    [self presentViewController:alertController animated:YES completion:nil];
    //v2.6.8, 统一present方法，防止崩溃
    [alertController presentSafelyFromViewController:self];
}

- (void)_importAdRulesFromUrl:(NSString *)url title:(NSString *)title
{
    //检查是否已经存在
    if([[AdBlockManager shareInstance] isExistRuleUrl:url]) {
        [UIView showToast:NSLocalizedString(@"adblock.repeat.tips", nil)];
        return;
    }
    
    //比较大的文件需要从这里开始loading
    [UIView showLoading:NSLocalizedString(@"tips.handling", nil)];
    
    [[AdBlockManager shareInstance] addRulesFromUrl:url completion:^(BOOL success, NSError *error) {
        if(success) {
            [UIView showSucceed:NSLocalizedString(@"common.import.success", nil)];
            
            //插入数据库中
            AdBlockModel* item = [AdBlockModel new];
            item.isActive = true;
            item.title = title;
            item.url = url;
            
            DatabaseUnit* unit = [DatabaseUnit addAdblockWithItem:item];
            @weakify(self)
            [unit setCompleteBlock:^(id result, BOOL success) {
                @strongify(self)
                [self.adBlockView reloadData];
                
                //刷新网页
                [[NSNotificationCenter defaultCenter] postNotificationName:kReloadCurrentWebViewNotification object:nil];
            }];
            DB_EXEC(unit);
        } else {
            [UIView showFailed:NSLocalizedString(@"common.import.fail", nil)];
        }
    }];
}

#pragma mark -- Getters

- (AdBlockView *)adBlockView
{
    if(!_adBlockView) {
        _adBlockView = [AdBlockView new];
    }
    
    return _adBlockView;
}

@end
