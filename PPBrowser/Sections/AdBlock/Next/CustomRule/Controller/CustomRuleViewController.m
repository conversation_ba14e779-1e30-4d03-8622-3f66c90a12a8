//
//  CustomRuleViewController.m
//  PPBrowser
//
//  Created by qingbin on 2024/10/17.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import "CustomRuleViewController.h"

#import "Masonry.h"
#import "MaizyHeader.h"
#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "ReactiveCocoa.h"
#import "UIView+FrameHelper.h"
#import "UIViewController+Helper.h"
#import "NSObject+Helper.h"

#import "PPNotifications.h"
#import "BrowserUtils.h"

#import "UITextView+Placeholder.h"
#import "AdBlockManager.h"

@interface CustomRuleViewController ()<UITextViewDelegate>

@property (nonatomic, strong) UIButton* rightButton;

@property (nonatomic, strong) UITextView *textView;

@property (nonatomic, strong) MASConstraint *bottomConstraint;

@end

@implementation CustomRuleViewController

- (void)viewDidLoad 
{
    [super viewDidLoad];

    self.title = NSLocalizedString(@"adblock.custom.rule.title", nil);
    
    [self addSubviews];
    [self defineLayout];
    [self handleEvents];
    
    [self createCustomLeftBarButtonItem];
    [self _createCustomRightBarButtonItem];
    
    [self updateWithModel];
    [self applyTheme];
}

- (void)updateWithModel
{
    NSString* content = [[AdBlockManager shareInstance] getCustomRules];
    self.textView.text = content;
}

#pragma mark -- handle events

- (void)handleEvents
{
    // 注册键盘通知
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(keyboardWillShow:) name:UIKeyboardWillShowNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(keyboardWillHide:) name:UIKeyboardWillHideNotification object:nil];
}

- (void)keyboardWillShow:(NSNotification *)notification 
{
    NSDictionary *userInfo = notification.userInfo;
    CGRect keyboardFrame = [userInfo[UIKeyboardFrameEndUserInfoKey] CGRectValue];
    CGFloat keyboardHeight = keyboardFrame.size.height;
    CGFloat duration = [userInfo[UIKeyboardAnimationDurationUserInfoKey] floatValue];
    
    [UIView animateWithDuration:duration animations:^{
        [self.bottomConstraint setOffset:-keyboardHeight];
        [self.view layoutIfNeeded];
    }];
}

- (void)keyboardWillHide:(NSNotification *)notification 
{
    NSDictionary *userInfo = notification.userInfo;
    CGFloat duration = [userInfo[UIKeyboardAnimationDurationUserInfoKey] floatValue];
    
    [UIView animateWithDuration:duration animations:^{
        [self.bottomConstraint setOffset:0];
        [self.view layoutIfNeeded];
    }];
}

- (void)dealloc 
{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

#pragma mark - UITextViewDelegate

- (void)textViewDidEndEditing:(UITextView *)textView
{
    
}

#pragma mark -- 导航栏

- (void)_createCustomRightBarButtonItem
{
    UIButton* rightButton = [[UIButton alloc] initWithFrame:CGRectMake(0.0, 0.0f, 44.0f, 44.0)];
    [rightButton setBackgroundColor:[UIColor clearColor]];

    float font = iPadValue(20, 16);
    rightButton.titleLabel.font = [UIFont systemFontOfSize:font];
    [rightButton setTitle:NSLocalizedString(@"addCustomTag.save", nil) forState:UIControlStateNormal];
    [rightButton setTitleColor:[UIColor colorWithHexString:@"#2D7AFE"] forState:UIControlStateNormal];
    [rightButton addTarget:self action:@selector(rightBarbuttonClick) forControlEvents:UIControlEventTouchUpInside];
    UIBarButtonItem* barItem = [[UIBarButtonItem alloc] initWithCustomView:rightButton];
    UIBarButtonItem *spacer = [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemFixedSpace target:nil action:nil];
    spacer.width = -16.0f; // for example shift right bar button to the right

    self.rightButton = rightButton;
    
    self.navigationItem.rightBarButtonItems = @[spacer, barItem];
}

- (void)rightBarbuttonClick
{
    [self.view endEditing:YES];
    
    //比较大的文件需要从这里开始loading
    [UIView showLoading:NSLocalizedString(@"tips.handling", nil)];
    
    @weakify(self)
    [[AdBlockManager shareInstance] saveCustomRules:self.textView.text
                                         completion:^(BOOL success, NSError *error) {
        @strongify(self)
        if(success) {
            [UIView showSucceed:NSLocalizedString(@"common.import.success", nil)];
            
            //刷新网页
            [[NSNotificationCenter defaultCenter] postNotificationName:kReloadCurrentWebViewNotification object:nil];
            //返回上一页
            @weakify(self)
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                @strongify(self)
                [self.navigationController popViewControllerAnimated:YES];
            });
        } else {
            [UIView showFailed:NSLocalizedString(@"common.import.fail", nil)];
        }
    }];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        [self.rightButton setTitleColor:[UIColor colorWithHexString:@"#ffffff"] forState:UIControlStateNormal];
    } else {
        self.view.backgroundColor = [UIColor colorWithHexString:@"#ffffff"];
        [self.rightButton setTitleColor:[UIColor colorWithHexString:@"#2D7AFE"] forState:UIControlStateNormal];
    }
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    //只有当前的controller会触发一次, 其它已存在的controller走通知
    if(needReload) {
        //修改暗黑模式
        [self applyTheme];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}

#pragma mark -- layout

- (void)addSubviews
{
    [self.view addSubview:self.textView];
}

- (void)defineLayout
{
    [self.textView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.mas_offset(iPadValue(20, 10));
        make.right.mas_offset(-iPadValue(20, 10));
        
        self.bottomConstraint = make.bottom.equalTo(self.view.mas_safeAreaLayoutGuideBottom);
    }];
}

#pragma mark -- Getters

- (UITextView *)textView
{
    if(!_textView) {
        _textView = [UITextView new];
        _textView.delegate = self;
        
        float font = iPadValue(20, 15);
        _textView.font = [UIFont systemFontOfSize:font];
        
        _textView.placeholder = NSLocalizedString(@"adblock.custom.input.placeholder", nil);
    }
    
    return _textView;
}

@end
