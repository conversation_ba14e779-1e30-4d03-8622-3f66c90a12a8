//
//  AdBlockModel.m
//  Saber
//
//  Created by qingbin on 2023/4/7.
//

#import "AdBlockModel.h"
#import "DateHelper.h"
#import "NSFileManager+Helper.h"

@implementation AdBlockModel

//默认内置规则
+ (NSString *)easylistChinaUrl
{
    return @"https://easylist-downloads.adblockplus.org/easylistchina.txt";
}
//默认开启规则
+ (NSString *)easylistUrl
{
    return @"https://easylist-downloads.adblockplus.org/easylist.txt";
}

//内置类型
+ (instancetype)easylistModel
{
    AdBlockModel* model = [AdBlockModel new];
    model.uuid = [[NSUUID UUID] UUIDString];
    
    NSString* url = @"https://easylist-downloads.adblockplus.org/easylist.txt";
    model.title = @"Easylist";
    model.url = url;
    //默认开启
    model.isActive = YES;
    
    NSString* ctime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
    model.ctime = @"1";
    model.updateTime = ctime;
    
    return model;
}

//会导致错乱
//+ (instancetype)cjxAnnoyanceModel
//{
//    AdBlockModel* model = [AdBlockModel new];
//    model.uuid = [[NSUUID UUID] UUIDString];
//    
//    NSString* url = @"https://raw.githubusercontent.com/cjx82630/cjxlist/master/cjx-annoyance.txt";
//    model.title = @"CJX-Annoyance";
//    model.url = url;
//    model.isActive = NO;
//    
//    NSString* ctime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
//    model.ctime = @"2";
//    model.updateTime = ctime;
//    
//    return model;
//}

+ (instancetype)easyprivacyModel
{
    AdBlockModel* model = [AdBlockModel new];
    model.uuid = [[NSUUID UUID] UUIDString];
    
    NSString* url = @"https://easylist-downloads.adblockplus.org/easyprivacy.txt";
    model.title = @"Easy Privacy";
    model.url = url;
    model.isActive = NO;
    
    NSString* ctime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
    model.ctime = @"3";
    model.updateTime = ctime;
    
    return model;
}

+ (instancetype)antiadblockfiltersModel
{
    AdBlockModel* model = [AdBlockModel new];
    model.uuid = [[NSUUID UUID] UUIDString];
    
    NSString* url = @"https://easylist-downloads.adblockplus.org/antiadblockfilters.txt";
    model.title = @"Anti-Adblock Filters";
    model.url = url;
    model.isActive = NO;
    
    NSString* ctime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
    model.ctime = @"4";
    model.updateTime = ctime;
    
    return model;
}

//https://bitbucket.org/hacamer/adrules/raw/main/rules/jiekouAD.txt
+ (instancetype)jiekouADModel
{
    AdBlockModel* model = [AdBlockModel new];
    model.uuid = [[NSUUID UUID] UUIDString];
    
    NSString* url = @"https://bitbucket.org/hacamer/adrules/raw/main/rules/jiekouAD.txt";
    model.title = @"jiekouAD";
    model.url = url;
    model.isActive = NO;
    
    NSString* ctime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
    model.ctime = @"5";
    model.updateTime = ctime;
    
    return model;
}

@end

