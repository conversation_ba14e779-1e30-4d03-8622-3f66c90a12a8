//
//  AdBlockModel.h
//  Saber
//
//  Created by q<PERSON><PERSON> on 2023/4/7.
//

#import "BaseModel.h"
#import "PPEnums.h"

@interface AdBlockModel : BaseModel

@property (nonatomic, strong) NSString *uuid;

@property (nonatomic, strong) NSString *title;
//url
@property (nonatomic, strong) NSString *url;
/// 是否正在运行
@property (nonatomic, assign) BOOL isActive;
/// 添加时间
@property (nonatomic, strong) NSString *ctime;
/// 更新时间
@property (nonatomic, strong) NSString *updateTime;

//辅助字段,主要是卡片化
@property (nonatomic, assign) BOOL isFirstInSection;
@property (nonatomic, assign) BOOL isLastInSection;

//内置类型
+ (instancetype)easylistModel;
//+ (instancetype)cjxAnnoyanceModel;
+ (instancetype)easyprivacyModel;
+ (instancetype)antiadblockfiltersModel;
+ (instancetype)jiekouADModel;

//默认内置规则
+ (NSString *)easylistChinaUrl;
//默认开启规则
+ (NSString *)easylistUrl;

@end

@protocol AdBlockModel <NSObject>
@end
