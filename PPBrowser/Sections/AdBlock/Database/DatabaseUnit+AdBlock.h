//
//  DatabaseUnit+AdBlock.h
//  Saber
//
//  Created by q<PERSON><PERSON> on 2023/4/8.
//

#import "DatabaseUnit.h"
#import "AdBlockModel.h"

@interface DatabaseUnit (AdBlock)

// 添加
+ (DatabaseUnit*)addAdblockWithItem:(AdBlockModel*)item;

// 删除
+ (DatabaseUnit*)removeAdblockWithId:(NSString*)uuid;

// 查询所有
+ (DatabaseUnit*)queryAllAdblocks;

// 更新是否激活状态
+ (DatabaseUnit*)updateAdblockWithId:(NSString*)uuid
                            isActive:(NSInteger)isActive;
                
//更新编辑/更新时间
+ (DatabaseUnit*)updateAdblockWithId:(NSString*)uuid;

//批量更新时间
+ (DatabaseUnit*)updateAdblockWithIds:(NSArray*)uuids;

@end

