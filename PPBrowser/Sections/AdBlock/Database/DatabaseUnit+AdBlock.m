//
//  DatabaseUnit+AdBlock.m
//  Saber
//
//  Created by q<PERSON><PERSON> on 2023/4/8.
//

#import "DatabaseUnit+AdBlock.h"
#import "ReactiveCocoa.h"

#import "FMDatabaseQueue.h"
#import "FMDatabase.h"

#import "PPEnums.h"

//CREATE TABLE IF NOT EXISTS t_adblock(uuid TEXT PRIMARY KEY, title TEXT, text TEXT, url TEXT, type INTEGER, isActive INTEGER, ctime TEXT)

@implementation DatabaseUnit (AdBlock)

// 添加一个
+ (DatabaseUnit*)addAdblockWithItem:(AdBlockModel*)item
{
    //解析脚本的时候已经加入到内存缓存, 所以不需要再在这里添加
    DatabaseUnit* unit = [DatabaseUnit new];

    if(item.uuid.length == 0) {
        item.uuid = [[NSUUID UUID] UUIDString];
    }
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* ctime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        NSString* updateTime = ctime;
        item.ctime = ctime;
        item.updateTime = updateTime;
        
        NSString* command = @"INSERT INTO t_adblock(uuid, title, url, isActive, updateTime, ctime) VALUES (?,?,?,?,?,?)";

        BOOL result = [db executeUpdate:command, item.uuid, item.title, item.url, @(item.isActive), updateTime, ctime];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

// 删除一个
+ (DatabaseUnit*)removeAdblockWithId:(NSString*)uuid
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    //删除数据库记录
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
    
        NSString* command = [NSString stringWithFormat:@"DELETE FROM t_adblock WHERE uuid=?;"];
        BOOL result = [db executeUpdate:command, uuid];

        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

// 查询所有
+ (DatabaseUnit*)queryAllAdblocks
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        //先创建在前面
        NSString* command = [NSString stringWithFormat:@"SELECT * FROM t_adblock ORDER BY ctime ASC"];
        FMResultSet* set = [db executeQuery:command];
        
        NSMutableArray *array = [[NSMutableArray alloc] init];
        while([set next]) {
            AdBlockModel* item = [[AdBlockModel alloc]initWithDictionary:[set resultDictionary] error:nil];
            [array addObject:item];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(array,YES);
            }
        });
    };
    
    return unit;
}

// 更新是否激活状态
+ (DatabaseUnit*)updateAdblockWithId:(NSString*)uuid
                            isActive:(NSInteger)isActive
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        
        NSString* command = [NSString stringWithFormat:@"UPDATE t_adblock SET isActive = ? WHERE uuid=?;"];
        BOOL result = [db executeUpdate:command, @(isActive), uuid];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil,result);
            }
        });
    };
    
    return unit;
}

//更新编辑/更新时间
+ (DatabaseUnit*)updateAdblockWithId:(NSString*)uuid
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* updateTime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        
        NSString* command = [NSString stringWithFormat:@"UPDATE t_adblock SET updateTime = ? WHERE uuid=?;"];
        BOOL result = [db executeUpdate:command, updateTime, uuid];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil,result);
            }
        });
    };
    
    return unit;
}

//批量更新时间
+ (DatabaseUnit*)updateAdblockWithIds:(NSArray*)uuids
{
    DatabaseUnit* unit = [DatabaseUnit new];
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        BOOL result = YES;
        NSString* updateTime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        for(int i=0;i<uuids.count;i++) {
            NSString* uuid = uuids[i];
            NSString* command = @"UPDATE t_adblock SET updateTime=? WHERE uuid=?";
            result = [db executeUpdate:command, updateTime, uuid] & result;
        }

        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil,result);
            }
        });
    };
    
    return unit;
}

@end
