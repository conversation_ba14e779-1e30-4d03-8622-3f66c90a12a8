//
//  FileManager.h
//  Reader
//
//  Created by q<PERSON><PERSON> on 2025/1/2.
//

#import <Foundation/Foundation.h>

#import "PPBrowser-Swift.h"

#define kBrowserDownloadIdentifier @"com.focus.browser.downloadmodule"

//浏览器普通文件下载类
@interface FileManager : NSObject

+ (instancetype)shareInstance;

@property (nonatomic, strong, readonly) TRSessionManager *sessionManager;

//浏览器下载文件
- (void)downloadWithUrl:(NSString *)url title:(NSString*)title;

@end

