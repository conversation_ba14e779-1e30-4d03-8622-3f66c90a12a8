//
//  FileManager.m
//  Reader
//
//  Created by qingbin on 2025/1/2.
//

#import "FileManager.h"

#import "ReactiveCocoa.h"
#import "NSString+Helper.h"
#import "UIView+Helper.h"

#import "PPNotifications.h"
#import "NSString+MKNetworkKitAdditions.h"
#import "FileStatusView.h"

@interface FileManager ()

@property (nonatomic, strong) TRSessionManager *sessionManager;

@property (nonatomic, strong) dispatch_queue_t queue;

@end

@implementation FileManager

+ (instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    static FileManager* obj;
    dispatch_once(&onceToken, ^{
        obj = [FileManager new];
    });
    
    return obj;
}

- (instancetype)init
{
    self = [super init];
    if(self) {
        self.queue = dispatch_queue_create("com.task.manager", DISPATCH_QUEUE_SERIAL);
        
        [self reloadSessionManager];
    }
    
    return self;
}

#pragma mark - Public Method

//浏览器下载文件
- (void)downloadWithUrl:(NSString *)url title:(NSString *)title
{
    if(url.length == 0) return;
    
    @weakify(self)
    dispatch_async(self.queue, ^{
        @strongify(self)
        [self _downloadWithUrl:url title:title];
    });
}

- (void)_downloadWithUrl:(NSString*)url title:(NSString*)title
{
    if(!self.sessionManager) {
        [self reloadSessionManager];
    }
        
    TRDownloadTask* task = [self.sessionManager downloadWithUrl:url fileName:title];
    
    //显示进度条
    dispatch_async(dispatch_get_main_queue(), ^{
        FileStatusView* statusView = [[FileStatusView alloc] initWithFileName:title task:task];
        [statusView show];
    });

    //添加监听
    [self addObserverToTask:task];
    
    dispatch_async(dispatch_get_main_queue(), ^{
        //写表后发通知,让列表第一时间刷新
        [[NSNotificationCenter defaultCenter] postNotificationName:kTaskReloadNotification object:task];
    });
}

- (NSArray<TRDownloadTask*>*)allTasks
{
    return self.sessionManager.tasks;
}

- (void)suspendTask:(TRDownloadTask *)task
{
    if(!task) return;
    [self.sessionManager suspendWithUrl:task.url];
}

- (void)startTask:(TRDownloadTask *)task
{
    if(!task) return;
    [self.sessionManager startWithUrl:task.url];
}

- (void)cancelTask:(TRDownloadTask *)task
{
    if(!task) return;
    [self.sessionManager cancelWithUrl:task.url];
}

#pragma mark - Private Method

#pragma mark - 初始化SessionManager

- (void)reloadSessionManager
{
    ///必须是异步的，要不然文件缓存太多的情况，会导致开屏非常慢
    @weakify(self)
    dispatch_async(self.queue, ^{
        @strongify(self)
        [self _reloadSessionManager];
    });
}

- (void)_reloadSessionManager
{
    TRSessionConfiguration *configuraion = [[TRSessionConfiguration alloc] init];
    configuraion.allowsCellularAccess = YES;
    configuraion.maxConcurrentTasksLimit = 10;
    self.sessionManager = [[TRSessionManager alloc] initWithIdentifier:kBrowserDownloadIdentifier configuration:configuraion];
    
    // 删除所有缓存中的文件
    NSArray* allTasks = [self allTasks];
    for(TRDownloadTask* task in allTasks) {
        [self.sessionManager removeWithUrl:task.url];
    }
}

- (void)addObserverToTask:(TRDownloadTask *)task
{
    /// 已经缓存成功的视频，如果重新打开APP，那么也会发一次缓存成功的通知(坑)
    /// 所以已经缓存成功的则不再加入监听
    if(task.status == TRStatusSucceeded) return;
    
    [[[task progressOnMainQueue:YES handler:^(TRDownloadTask * task) {
        [[NSNotificationCenter defaultCenter] postNotificationName:kTaskProgressNotification object:task];
    }] successOnMainQueue:YES handler:^(TRDownloadTask * task) {
        [[NSNotificationCenter defaultCenter] postNotificationName:kTaskSuccessNotification object:task];
        // 移除内存中的任务，否则无法重新下载
        [self.sessionManager removeWithUrl:task.url];
    }] failureOnMainQueue:YES handler:^(TRDownloadTask * task) {
        [[NSNotificationCenter defaultCenter] postNotificationName:kTaskFailureNotification object:task];
    }];
}

@end
