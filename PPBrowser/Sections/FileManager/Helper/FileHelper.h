//
//  FileHelper.h
//  Reader
//
//  Created by q<PERSON><PERSON> on 2025/1/2.
//

#import <Foundation/Foundation.h>
#import <WebKit/WebKit.h>

#import "YYMemoryCache.h"

@interface FileHelper : NSObject

- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)new NS_UNAVAILABLE;

+ (instancetype)shareInstance;

// Keep track of allowed `URLRequest`s from `webView(_:decidePolicyFor:decisionHandler:)` so
// that we can obtain the originating `URLRequest` when a `URLResponse` is received. This will
// allow us to re-trigger the `URLRequest` if the user requests a file to be downloaded.
//@property (nonatomic, strong) YYMemoryCache *pendingRequests;

- (instancetype)initWithResponse:(NSURLResponse *)response
                             url:(NSString *)url;

- (BOOL)isAttachment;

- (void)showSelectView;

- (void)showSelectViewWithViewHandler:(void(^)(void))viewHandler;

@end


