//
//  FileHelper.m
//  Reader
//
//  Created by qing<PERSON> on 2025/1/2.
//

#import "FileHelper.h"

#import "MaizyHeader.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"

#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "NSObject+Helper.h"
#import "FileManager.h"
#import "UIAlertController+SafePresentation.h"

@interface FileHelper ()

//@property (nonatomic, strong) NSURLRequest *request;

@property (nonatomic, strong) NSURLResponse *response;

//@property (nonatomic, strong) WKHTTPCookieStore *cookieStore;
@property (nonatomic, strong) NSString *url;

@property (nonatomic, strong) NSString *fileName;

@property (nonatomic, strong) NSString *mimeType;

@property (nonatomic, assign) NSInteger totalBytesExpected;

@end

@implementation FileHelper

+ (instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    static FileHelper* obj;
    dispatch_once(&onceToken, ^{
        obj = [<PERSON>Helper new];
    });

    return obj;
}

- (instancetype)initWithResponse:(NSURLResponse *)response
                             url:(NSString *)url
{
    self = [super init];
    if(self) {
        self.url = url;
        self.response = response;

        if(self.response.MIMEType.length > 0) {
            self.mimeType = self.response.MIMEType;
        }

        NSString* suggestedFilename = self.response.suggestedFilename;
        if(suggestedFilename.length == 0) {
            self.fileName = [self.url lastPathComponent];
        } else {
            self.fileName = suggestedFilename;
        }

        self.totalBytesExpected = self.response.expectedContentLength;
    }

    return self;
}

- (BOOL)isAttachment
{
    if([self.mimeType isEqualToString:@"text/html"]) return NO;

    if([self.response isMemberOfClass:NSHTTPURLResponse.class]) {
        NSHTTPURLResponse* httpResponse = (NSHTTPURLResponse*)self.response;

        NSString* contentDisposition = httpResponse.allHeaderFields[@"Content-Disposition"];
        if(contentDisposition.length > 0) {
            if([contentDisposition hasPrefix:@"attachment"]) {
                return YES;
            }
        }
    }

    return NO;
}

- (void)showSelectView
{
    [self showSelectViewWithViewHandler:nil];
}

- (void)showSelectViewWithViewHandler:(void(^)(void))viewHandler
{
    NSString* msg;
    NSInteger totalBytesExpected = self.totalBytesExpected;
    NSString* title = NSLocalizedString(@"file.will.save", nil);
    if(totalBytesExpected > 0) {
        NSByteCountFormatter *format = [NSByteCountFormatter new];
        format.countStyle = NSByteCountFormatterCountStyleFile;

        NSString* text = [format stringFromByteCount:totalBytesExpected];
        msg = [NSString stringWithFormat:@"%@: %@ (%@)",title, self.fileName, text];
    } else {
        msg = [NSString stringWithFormat:@"%@: %@",title, self.fileName];
    }

    UIAlertController *alertController = [UIAlertController alertControllerWithTitle:@"" message:msg preferredStyle:UIAlertControllerStyleActionSheet];

    // 如果有查看回调，则添加查看选项
    if (viewHandler) {
        [alertController addAction:([UIAlertAction actionWithTitle:NSLocalizedString(@"common.view", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            viewHandler();
        }])];
    }

    [alertController addAction:([UIAlertAction actionWithTitle:NSLocalizedString(@"common.download", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [[FileManager shareInstance] downloadWithUrl:self.url
                                               title:self.fileName];
    }])];

    [alertController addAction:([UIAlertAction actionWithTitle:NSLocalizedString(@"common.cancel", nil) style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
    }])];

    UIWindow* window = YBIBNormalWindow();
    UINavigationController* navc = (UINavigationController *)window.rootViewController;
//    [navc presentViewController:alertController animated:YES completion:nil];

    //v2.6.8, 统一present方法，防止崩溃
    [alertController presentSafelyFromViewController:navc];
}

#pragma mark - getters

//- (YYMemoryCache *)pendingRequests
//{
//    if(!_pendingRequests) {
//        _pendingRequests = [YYMemoryCache new];
//
//        _pendingRequests.name = @"pendingRequestsCache";
//        _pendingRequests.countLimit = 50;
//    }
//
//    return _pendingRequests;
//}

@end
