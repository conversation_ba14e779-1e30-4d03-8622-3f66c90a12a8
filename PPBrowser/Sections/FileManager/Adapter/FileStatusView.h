//
//  FileStatusView.h
//  PPBrowser
//
//  Created by qingbin on 2025/4/1.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "PPBrowser-Swift.h"

NS_ASSUME_NONNULL_BEGIN

// 下载状态枚举
typedef NS_ENUM(NSInteger, FileStatus) {
    FileStatusDownloading, // 下载中
    FileStatusCompleted,    // 下载完成
    FileStatusFailed,    // 下载失败
    FileStatusPaused,    // 暂停下载
};

@protocol FileStatusViewDelegate <NSObject>

@optional
// 用户点击取消按钮时的回调
- (void)FileStatusViewDidCancel;
// 用户点击打开按钮时的回调
- (void)FileStatusViewDidOpen;
// 暂停/恢复下载
- (void)FileStatusViewDidToggleDownload;

@end

@interface FileStatusView : UIView

@property (nonatomic, weak) id<FileStatusViewDelegate> delegate;

// 初始化方法
- (instancetype)initWithFileName:(NSString *)fileName
                            task:(TRDownloadTask *)task;

//显示
- (void)show;

// 更新下载状态
- (void)updateStatus:(FileStatus)status;

// 更新下载进度 (0.0-1.0)
- (void)updateProgress;

@end

NS_ASSUME_NONNULL_END
