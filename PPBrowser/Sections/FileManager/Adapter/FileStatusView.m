//
//  FileStatusView.m
//  PPBrowser
//
//  Created by qingbin on 2025/4/1.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "FileStatusView.h"
#import "Masonry.h"
#import "UIColor+Helper.h"
#import "ThemeProtocol.h"
#import "UIView+Helper.h"
#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "CustomTitleAndImageView.h"

#import "AppDelegate.h"
#import "BrowserViewController.h"
#import "PPNotifications.h"

#import "NSFileManager+Helper.h"

@interface FileStatusView ()
// 主容器
@property (nonatomic, strong) UIView *containerView;
// 文件图标
@property (nonatomic, strong) CustomTitleAndImageView *statusView;
// 文件信息容器
@property (nonatomic, strong) UIStackView *fileInfoStackView;
@property (nonatomic, strong) UILabel *fileNameLabel;
@property (nonatomic, strong) UILabel *fileSizeLabel;
@property (nonatomic, strong) UILabel *statusLabel;
// 进度相关
@property (nonatomic, strong) UIView *progressView;
// 按钮容器
@property (nonatomic, strong) UIStackView *buttonsStackView;
// 打开文件目录按钮
@property (nonatomic, strong) CustomTitleAndImageView *openFileView;
// 关闭按钮
@property (nonatomic, strong) CustomTitleAndImageView *closeView;

@property (nonatomic, copy) NSString *fileName;
// 当前状态
@property (nonatomic, assign) FileStatus currentStatus;
// 任务
@property (nonatomic, strong) TRDownloadTask *task;
//当前进度
@property (nonatomic, assign) float progress;
//总大小
@property (nonatomic, assign) NSInteger totalUnitCount;
//进入移除状态
@property (nonatomic, assign) BOOL isCountdown2Remove;

@end

@implementation FileStatusView

#pragma mark - Initialization

- (instancetype)initWithFileName:(NSString *)fileName
                            task:(TRDownloadTask *)task {
    self = [super init];
    if (self) {
        self.fileName = fileName;
        self.task = task;
        
        // 设置初始文本
        self.fileNameLabel.text = self.fileName;
        
        [self setupViews];
        [self defineLayout];
        [self applyTheme];
        [self bindData];
        
        // 初始状态为等待下载
        [self updateStatus:FileStatusDownloading];
    }
    return self;
}

- (void)dealloc
{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

#pragma mark - Setup UI

- (void)setupViews {
    self.backgroundColor = UIColor.clearColor;
    
    // 添加视图
    [self addSubview:self.containerView];
    [self.containerView addSubview:self.progressView];
    
    [self.containerView addSubview:self.statusView];
    [self.containerView addSubview:self.fileInfoStackView];
    [self.containerView addSubview:self.openFileView];
    [self.containerView addSubview:self.closeView];
}

- (void)defineLayout {
    // 设置容器视图
    [self.containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
        make.height.mas_equalTo(56); // 固定高度为56px，与HTML一致
    }];
    
    // 设置左侧下载图标
    [self.statusView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.containerView).offset(16);
        make.centerY.equalTo(self.containerView);
        make.size.mas_equalTo(32);
    }];
    
    // 设置中间文件信息
    [self.fileInfoStackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.statusView.mas_right).offset(12);
        make.centerY.equalTo(self.containerView);
        make.right.equalTo(self.openFileView.mas_left).offset(-5);
    }];
    
    // 设置进度条
    [self.progressView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.bottom.equalTo(self.containerView);
        make.width.mas_equalTo(0);
    }];
    
    // 打开下载目录
    [self.openFileView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.closeView.mas_left).offset(-10);
        make.centerY.equalTo(self.containerView);
        make.size.mas_equalTo(32);
    }];
    
    // 设置右侧取消按钮
    [self.closeView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.containerView).offset(-16);
        make.centerY.equalTo(self.containerView);
        make.size.mas_equalTo(32);
    }];
}

#pragma mark - ReactiveObjC Bindings

- (void)bindData {
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(updateWithModel:)
                                                 name:kTaskSuccessNotification
                                               object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(updateWithModel:)
                                                 name:kTaskReloadNotification
                                               object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(updateWithModel:)
                                                 name:kTaskProgressNotification
                                               object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(updateWithModel:)
                                                 name:kTaskFailureNotification
                                               object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(darkThemeDidChangeNotification:)
                                                 name:kDarkThemeDidChangeNotification
                                               object:nil];
    
    @weakify(self)
    [self.closeView setTapAction:^{
        @strongify(self)
        [self removeFromSuperview];
    }];
    
    [self.openFileView setTapAction:^{
        [NSFileManager openFocusDownloadsFolder];
    }];
}

//修改暗黑模式
- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}

#pragma mark -- 缓存成功监听

- (void)updateWithModel:(NSNotification *)notification
{
    TRDownloadTask* task = notification.object;
    if (!task || task != self.task) return;
    
    @weakify(self)
    dispatch_async(dispatch_get_main_queue(), ^{
        @strongify(self)
        // 更新状态
        if(task.status == TRStatusSucceeded || task.status == TRStatusRemoved || task.status == TRStatusWillRemove) {
            //因为是不同的线程，在FileManager中执行了remove操作
            [self updateStatus:FileStatusCompleted];
        } else if(task.status == TRStatusFailed) {
            [self updateStatus:FileStatusFailed];
        } else if(task.status == TRStatusSuspended || task.status == TRStatusWillSuspend) {
            [self updateStatus:FileStatusPaused];
        } else if(task.status == TRStatusWaiting || task.status == TRStatusRunning) {
            [self updateStatus:FileStatusDownloading];
        }
        
        //更新进度条
        [self updateProgress];
    });
}

#pragma mark - Public Methods

//显示
- (void)show
{
    AppDelegate *appDelegate = (AppDelegate*) [UIApplication sharedApplication].delegate;
    BrowserViewController* browser = appDelegate.browser;
    if(!browser) return;
    
    [browser.view addSubview:self];
    [self mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(browser.view);
        make.height.mas_equalTo(56);
        make.bottom.equalTo(browser.bottomToolbar.mas_top);
    }];
}

- (void)updateStatus:(FileStatus)status {
    _currentStatus = status;
    
    // 根据状态更新UI
    switch (status) {
        case FileStatusPaused:
        case FileStatusDownloading: {
            [self.statusView updateWith:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
                UIImage *arrowImage = [UIImage imageNamed:@"arrow_down_icon"];
                imageView.image = [arrowImage imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
                
                view.backgroundColor = [UIColor colorWithHexString:@"#3B82F6"]; // bg-blue-500
            }];
            
            self.fileSizeLabel.hidden = NO;
            self.statusLabel.hidden = YES;
            self.openFileView.hidden = YES;
            
            self.progressView.backgroundColor = [UIColor colorWithHexString:@"#143B82F6"];
        }
            break;
            
        case FileStatusCompleted:{
            [self.statusView updateWith:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
                UIImage *arrowImage = [UIImage imageNamed:@"check_icon"];
                imageView.image = [arrowImage imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
                
                view.backgroundColor = [UIColor colorWithHexString:@"#10B981"]; // bg-green-500
            }];
            
            self.fileSizeLabel.hidden = NO;
            self.statusLabel.hidden = YES;
            self.openFileView.hidden = NO;
            
            self.progressView.backgroundColor = [UIColor colorWithHexString:@"#1410B981"];
            
            //8s后移除
            if (self.isCountdown2Remove) return;
            self.isCountdown2Remove = YES;
            @weakify(self)
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(8 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                @strongify(self)
                [self removeFromSuperview];
            });
        }
            break;
        case FileStatusFailed:{
            [self.statusView updateWith:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
                UIImage *arrowImage = [UIImage imageNamed:@"rotate_right_icon"];
                imageView.image = [arrowImage imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
                
                view.backgroundColor = [UIColor colorWithHexString:@"#EF4444"]; // bg-red-500
            }];
            
            self.fileSizeLabel.hidden = YES;
            self.statusLabel.hidden = NO;
            self.openFileView.hidden = YES;
            
            self.progressView.backgroundColor = [UIColor colorWithHexString:@"#14EF4444"];
        }
            break;
    }
}

- (void)updateProgress {
    float progress = self.task.progress.fractionCompleted;
    
    // 更新进度条
    progress = MIN(1.0, progress);
    progress = MAX(0.0, progress);
    
    float width = CGRectGetWidth(self.frame) * progress;
    [self.progressView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(width);
    }];
    
    // 更新文件大小标签，显示下载进度
    if (self.progress <= progress
        && progress > 0
        && self.currentStatus == FileStatusDownloading) {
        // 计算已下载大小
        self.progress = progress;
        
        NSInteger completedUnitCount = self.task.progress.completedUnitCount;
        NSInteger totalUnitCount = self.task.progress.totalUnitCount;
        self.totalUnitCount = totalUnitCount;
        
        NSByteCountFormatter *format = [NSByteCountFormatter new];
        format.countStyle = NSByteCountFormatterCountStyleFile;
        NSString* completedUnitCountText = [format stringFromByteCount:completedUnitCount];
        NSString* totalUnitCountText = [format stringFromByteCount:totalUnitCount];

        NSString* content = [NSString stringWithFormat:@"%@/%@ (%.1f%%)",completedUnitCountText,totalUnitCountText,progress*100];
        self.fileSizeLabel.text = content;
    }
    
    if (self.progress >= 1.0 || self.currentStatus == FileStatusCompleted) {
        NSByteCountFormatter *format = [NSByteCountFormatter new];
        format.countStyle = NSByteCountFormatterCountStyleFile;
        NSString* completedUnitCountText = [format stringFromByteCount:self.totalUnitCount];
        
        self.fileSizeLabel.text = completedUnitCountText;
    }
}

#pragma mark - Private Methods

- (void)applyTheme {
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    
    if (isDarkTheme) {
        //暗黑模式
        self.containerView.backgroundColor = [UIColor colorWithHexString:@"#1C1C1E"];
        self.fileNameLabel.textColor = UIColor.whiteColor;
        self.fileSizeLabel.textColor = [UIColor colorWithHexString:@"#AEAEB2"];
        
        [self.openFileView updateWith:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            view.backgroundColor = [UIColor colorWithHexString:@"#2C2C2E"];
            imageView.tintColor = [UIColor colorWithHexString:@"#007AFF"];
        }];
        
        [self.closeView updateWith:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            view.backgroundColor = [UIColor colorWithHexString:@"#2C2C2E"];
            imageView.tintColor = [UIColor colorWithHexString:@"#FFFFFF"];
        }];
    } else {
        //白天模式
        self.containerView.backgroundColor = UIColor.whiteColor;
        self.fileNameLabel.textColor = [UIColor colorWithHexString:@"#333333"];
        self.fileSizeLabel.textColor = [UIColor colorWithHexString:@"#666666"];
        
        [self.openFileView updateWith:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            view.backgroundColor = [UIColor colorWithHexString:@"#3B82F6"];
            imageView.tintColor = [UIColor colorWithHexString:@"#FFFFFF"];
        }];
        
        [self.closeView updateWith:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            view.backgroundColor = [UIColor colorWithHexString:@"#0D000000"];
            imageView.tintColor = [UIColor colorWithHexString:@"#6B7280"];
        }];
    }
}

#pragma mark - Lazy Load

- (UIView *)containerView {
    if (!_containerView) {
        _containerView = [[UIView alloc] init];
        _containerView.backgroundColor = UIColor.whiteColor;
//        _containerView.layer.cornerRadius = 8; // 圆角设置为8px，与HTML中的rounded-lg一致
//        _containerView.layer.masksToBounds = NO;
        
        // 添加阴影效果，与HTML中的box-shadow一致
//        _containerView.layer.shadowColor = [UIColor blackColor].CGColor;
//        _containerView.layer.shadowOffset = CGSizeMake(0, 2);
//        _containerView.layer.shadowRadius = 8;
//        _containerView.layer.shadowOpacity = 0.08;
        
        // 添加边框
//        _containerView.layer.borderWidth = 1;
//        _containerView.layer.borderColor = [UIColor colorWithHexString:@"#E5E7EB"].CGColor; // border-gray-200
    }
    return _containerView;
}

- (UIView *)progressView
{
    if (!_progressView) {
        _progressView = [UIView new];
    }
    return _progressView;
}

- (CustomTitleAndImageView *)statusView
{
    if (!_statusView) {
        _statusView = [[CustomTitleAndImageView alloc]initWithLayout:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            [imageView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.center.mas_equalTo(0);
                make.height.mas_equalTo(14);
            }];
            
            UIImage *arrowImage = [UIImage imageNamed:@"arrow_down_icon"];
            imageView.image = [arrowImage imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
            imageView.tintColor = [UIColor whiteColor]; // text-white
            imageView.contentMode = UIViewContentModeScaleAspectFit;
        }];
        
        _statusView.backgroundColor = [UIColor colorWithHexString:@"#3B82F6"]; // bg-blue-500
        _statusView.layer.cornerRadius = 16; // 圆形按钮
        _statusView.clipsToBounds = YES;
    }
    return _statusView;
}

- (UIStackView *)fileInfoStackView {
    if (!_fileInfoStackView) {
        _fileInfoStackView = [[UIStackView alloc] initWithArrangedSubviews:@[
            self.fileNameLabel,
            self.fileSizeLabel,
            self.statusLabel,
        ]];
        _fileInfoStackView.axis = UILayoutConstraintAxisVertical;
        _fileInfoStackView.alignment = UIStackViewAlignmentLeading;
        _fileInfoStackView.distribution = UIStackViewDistributionFill;
        _fileInfoStackView.spacing = 2; // 与HTML中的margin-bottom: 2px一致
    }
    return _fileInfoStackView;
}

- (UILabel *)fileNameLabel {
    if (!_fileNameLabel) {
        _fileNameLabel = [UIView createLabelWithTitle:@""
                                           textColor:[UIColor blackColor]
                                             bgColor:UIColor.clearColor
                                            fontSize:14 // text-sm
                                       textAlignment:NSTextAlignmentLeft
                                               bBold:YES]; // font-weight: 500
        _fileNameLabel.lineBreakMode = NSLineBreakByTruncatingMiddle;
    }
    return _fileNameLabel;
}

- (UILabel *)fileSizeLabel {
    if (!_fileSizeLabel) {
        _fileSizeLabel = [UIView createLabelWithTitle:@""
                                           textColor:[UIColor colorWithHexString:@"#6B7280"] // text-gray-500
                                             bgColor:UIColor.clearColor
                                            fontSize:12 // text-xs
                                       textAlignment:NSTextAlignmentLeft
                                               bBold:NO];
    }
    return _fileSizeLabel;
}

- (UILabel *)statusLabel {
    if (!_statusLabel) {
        _statusLabel = [UIView createLabelWithTitle:NSLocalizedString(@"下载失败，请重试", nil)
                                          textColor:[UIColor systemRedColor]
                                            bgColor:UIColor.clearColor
                                           fontSize:12 // text-xs
                                      textAlignment:NSTextAlignmentLeft
                                              bBold:NO];
        _statusLabel.hidden = YES;
    }
    return _statusLabel;
}

- (CustomTitleAndImageView *)openFileView
{
    if (!_openFileView) {
        _openFileView = [[CustomTitleAndImageView alloc]initWithLayout:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            [imageView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.center.mas_equalTo(0);
                make.height.mas_equalTo(14);
            }];
            
            UIImage *arrowImage = [UIImage imageNamed:@"open_file"];
            imageView.image = [arrowImage imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
            imageView.tintColor = [UIColor whiteColor]; // text-white
            imageView.contentMode = UIViewContentModeScaleAspectFit;
        }];
        
        _openFileView.backgroundColor = [UIColor colorWithHexString:@"#3B82F6"]; // bg-blue-500
        _openFileView.layer.cornerRadius = 16; // 圆形按钮
        _openFileView.clipsToBounds = YES;
    }
    return _openFileView;
}

- (CustomTitleAndImageView *)closeView
{
    if (!_closeView) {
        _closeView = [[CustomTitleAndImageView alloc]initWithLayout:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            [imageView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.center.mas_equalTo(0);
                make.height.mas_equalTo(14);
            }];
            
            UIImage *arrowImage = [UIImage imageNamed:@"xmark_icon"];
            imageView.image = [arrowImage imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
            imageView.tintColor = [UIColor colorWithHexString:@"#6B7280"]; // text-gray-500
            imageView.contentMode = UIViewContentModeScaleAspectFit;
        }];
        
        _closeView.backgroundColor = [UIColor colorWithHexString:@"#08000000"]; // bg-blue-500
        _closeView.layer.cornerRadius = 16; // 圆形按钮
        _closeView.clipsToBounds = YES;
    }
    return _closeView;
}

@end
