//
//  TagitView.m
//  PPBrowser
//
//  Created by qingbin on 2022/12/17.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "TagitView.h"

#import "Masonry.h"
#import "ReactiveCocoa.h"

#import "UIColor+Helper.h"
#import "NSObject+Helper.h"
#import "UIView+FrameHelper.h"
#import "MaizyHeader.h"
#import "PPNotifications.h"
#import "BrowserViewController.h"

#import "CustomButton.h"
#import "BrowserUtils.h"
#import "AppDelegate.h"

#import "TagitHelper.h"
#import "TagitManager.h"
#import "NSURL+Extension.h"

@interface TagitView()

@property(nonatomic,strong) UIStackView* stackView;
//放大
@property(nonatomic,strong) UIButton* expandButton;
//缩小
@property(nonatomic,strong) UIButton* reduceButton;
//屏蔽
@property(nonatomic,strong) UIButton* blockButton;
//撤回
@property(nonatomic,strong) UIButton* withdrawButton;
//关闭按钮
@property(nonatomic,strong) UIButton* closeButton;
//
@property(nonatomic,strong) UITapGestureRecognizer* tapGesture;
//
@property(nonatomic,weak) PandaWebView* webView;

@property (nonatomic, assign) CGFloat originY; // 记录原始Y坐标
@property (nonatomic, strong) MASConstraint *bottomConstraint; // 保存底部约束

@end

@implementation TagitView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if(self) {
        [self addSubviews];
        [self defineLayout];
        [self setupObservers];
        
        self.backgroundColor = [UIColor.blackColor colorWithAlphaComponent:0.3];
        if ([BrowserUtils isiPad]) {
            self.layer.cornerRadius = 10;
            self.layer.masksToBounds = YES;
        }
    }
    
    return self;
}

+ (instancetype)show
{
    AppDelegate *appDelegate = (AppDelegate*) [UIApplication sharedApplication].delegate;
    BrowserViewController* browser = appDelegate.browser;
    if(!browser) return nil;
    
    TagitView* view = [TagitView new];
    [browser.view addSubview:view];
    
    float height = 50 + 36 + 20;
    float maxWidth = iPadValue(500, kScreenWidth);
    
    [view mas_makeConstraints:^(MASConstraintMaker *make) {
        if ([BrowserUtils isiPad]) {
            make.width.mas_equalTo(maxWidth);
            make.centerX.equalTo(browser.view);
        } else {
            make.left.right.equalTo(browser.view);
        }
        // 保存bottom约束以便后续更新
        view.bottomConstraint = make.bottom.equalTo(browser.view.mas_safeAreaLayoutGuideBottom);
        make.height.mas_equalTo(height);
    }];
        
    return view;
}

- (void)close
{
    AppDelegate *appDelegate = (AppDelegate*) [UIApplication sharedApplication].delegate;
    BrowserViewController* browser = appDelegate.browser;
    if(!browser) return;
    
    [TagitHelper cancelHideElementWithWebView:self.webView];
    
//    self.webView.userInteractionEnabled = YES;
    //通过UI结构分析,webview包着一层WKScrollView, WKScrollView包着一层WKContentView
    //所以只需要禁止WKContentView的响应事件即可
    for(UIView* view in self.webView.scrollView.subviews) {
        //重置
        NSLog(@"class = %@", NSStringFromClass(view.class));
        view.userInteractionEnabled = YES;
    }
    
    [browser.view removeGestureRecognizer:self.tapGesture];
    
    [self removeFromSuperview];
}

- (void)setupObservers
{
    AppDelegate *appDelegate = (AppDelegate*) [UIApplication sharedApplication].delegate;
    BrowserViewController* _vc = appDelegate.browser;
    if(!_vc) return;
    
    PandaWebView* webView = _vc.tabManager.selectedTab.webView;
//    webView.userInteractionEnabled = NO;
    self.webView = webView;
    
    //通过UI结构分析,webview包着一层WKScrollView, WKScrollView包着一层WKContentView
    //所以只需要禁止WKContentView的响应事件即可
    for(UIView* view in webView.scrollView.subviews) {
        NSLog(@"class = %@", NSStringFromClass(view.class));
        view.userInteractionEnabled = NO;
    }
    
    @weakify(self)
    [[self.closeButton rac_signalForControlEvents:UIControlEventTouchUpInside]
    subscribeNext:^(id x) {
        @strongify(self)
        [self close];
    }];
    
    [[self.expandButton rac_signalForControlEvents:UIControlEventTouchUpInside]
    subscribeNext:^(id x) {
        //扩大区域
        [TagitHelper showParentElementMaskWithWebView:webView];
    }];
    
    [[self.reduceButton rac_signalForControlEvents:UIControlEventTouchUpInside]
    subscribeNext:^(id x) {
        //缩小区域
        [TagitHelper showTheChildElementMaskWithWebView:webView];
    }];
    
    [[self.blockButton rac_signalForControlEvents:UIControlEventTouchUpInside]
    subscribeNext:^(id x) {
        @strongify(self)
        //屏蔽
        [self _handleAdBlock];
    }];
    
    [[self.withdrawButton rac_signalForControlEvents:UIControlEventTouchUpInside]
    subscribeNext:^(id x) {
        @strongify(self)
        //撤回
        [self _handleWithdraw];
    }];
    
    UITapGestureRecognizer* tap = [UITapGestureRecognizer new];
    [_vc.view addGestureRecognizer:tap];
    [tap.rac_gestureSignal subscribeNext:^(id x) {
        CGPoint pt = [tap locationInView:webView];
        [TagitHelper showElementMaskWithWebView:webView point:pt];
    }];
    self.tapGesture = tap;
    
    // 添加拖动手势
    UIPanGestureRecognizer *pan = [[UIPanGestureRecognizer alloc] initWithTarget:self action:@selector(handlePan:)];
    [self addGestureRecognizer:pan];
    
    //拦截事件点击
    tap = [UITapGestureRecognizer new];
    [self addGestureRecognizer:tap];
}

- (void)handlePan:(UIPanGestureRecognizer *)pan {
    AppDelegate *appDelegate = (AppDelegate*) [UIApplication sharedApplication].delegate;
    BrowserViewController* browser = appDelegate.browser;
    if(!browser) return;
    
    CGPoint translation = [pan translationInView:browser.view];
    
    switch (pan.state) {
        case UIGestureRecognizerStateBegan: {
            // 记录开始拖动时的位置
            self.originY = CGRectGetMinY(self.frame);
            break;
        }
        case UIGestureRecognizerStateChanged: {
            // 计算新的位置
            CGFloat newY = self.originY + translation.y;
            CGFloat maxY = CGRectGetHeight(browser.view.frame) - CGRectGetHeight(self.frame);
            CGFloat minY = browser.view.safeAreaInsets.top;
            
            // 限制移动范围
            newY = MAX(minY, MIN(newY, maxY));
            
            // 更新约束
            [self mas_updateConstraints:^(MASConstraintMaker *make) {
                // 使用bottom约束，这样在键盘弹出时也能正常工作
                CGFloat bottomOffset = CGRectGetHeight(browser.view.frame) - newY - CGRectGetHeight(self.frame);
                self.bottomConstraint.offset(-bottomOffset);
            }];
            
            // 立即更新布局
            [self.superview layoutIfNeeded];
            break;
        }
        default:
            break;
    }
}

//屏蔽
- (void)_handleAdBlock
{
    TagitModel* selectItem = [TagitManager shareInstance].currentSelectItem;
    if(selectItem) {
        [[TagitManager shareInstance] pushElement:selectItem];
        
        [TagitHelper hideElementWithWebView:self.webView xpath:selectItem.xpath];
    }
    
    [TagitManager shareInstance].currentSelectItem = nil;
}

//撤回
- (void)_handleWithdraw
{
    NSURL* URL = self.webView.URL;
    NSArray* list = [[TagitManager shareInstance] getElementsWithHost:[URL normalizedHost]];
    
    if(list.count > 0) {
        TagitModel* lastItem = list.lastObject;
        
        [TagitHelper showElementWithWebView:self.webView xpath:lastItem.xpath];
    
        [[TagitManager shareInstance] removeElement:lastItem];
    }
}

- (void)addSubviews
{
    [self addSubview:self.stackView];
    [self addSubview:self.closeButton];
}

- (void)defineLayout
{
    [self.closeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.right.mas_offset(0);
        make.size.mas_equalTo(44);
    }];
    
    float spacing = iPadValue(30, 15);
    float margin = iPadValue(40, 20);
    float maxWidth = iPadValue(500, kScreenWidth);
    float buttonWidth = (MIN(maxWidth, kScreenWidth)-spacing*3-margin*2) / 4.0;
    float buttonHeight = 36;
    
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_offset(50);
        make.centerX.mas_offset(0);
    }];
    
    NSArray* buttons = @[self.expandButton, self.reduceButton, self.blockButton, self.withdrawButton];
    for(UIButton* button in buttons) {
        [button mas_makeConstraints:^(MASConstraintMaker *make) {
            make.size.mas_equalTo(CGSizeMake(buttonWidth, buttonHeight));
        }];
    }
}

- (UIStackView *)stackView
{
    if(!_stackView) {
        _stackView = [[UIStackView alloc]initWithArrangedSubviews:@[
            self.expandButton,
            self.reduceButton,
            self.blockButton,
            self.withdrawButton
        ]];
        
        _stackView.backgroundColor = UIColor.clearColor;
        _stackView.axis = UILayoutConstraintAxisHorizontal;
        _stackView.spacing = iPadValue(30, 15);
        _stackView.distribution = UIStackViewDistributionEqualCentering;
    }
    
    return _stackView;
}

- (UIButton *)closeButton
{
    if(!_closeButton) {
        _closeButton = [UIButton new];
        
        UIImage* image = [UIImage imageNamed:@"tagit_close_icon"];
        image = [image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        [_closeButton setImage:image forState:UIControlStateNormal];
        _closeButton.tintColor = UIColor.whiteColor;
    }
    
    return _closeButton;
}

- (UIButton *)createButtonWithTitle:(NSString *)title
{
    UIButton* button = [UIButton new];
    [button setTitle:title forState:UIControlStateNormal];
    [button setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
    button.titleLabel.font = [UIFont systemFontOfSize:iPadValue(18, 16)];
    
    button.backgroundColor = [UIColor.blackColor colorWithAlphaComponent:0.6];
    
//    button.layer.cornerRadius = 36/2.0;
    button.layer.cornerRadius = 8.0;
    button.layer.masksToBounds = YES;
    
    return button;
}

- (UIButton *)expandButton
{
    if(!_expandButton) {
        _expandButton = [self createButtonWithTitle:NSLocalizedString(@"tagit.expand", nil)];
    }
    
    return _expandButton;
}

- (UIButton *)reduceButton
{
    if(!_reduceButton) {
        _reduceButton = [self createButtonWithTitle:NSLocalizedString(@"tagit.reduce", nil)];
    }
    
    return _reduceButton;
}

- (UIButton *)blockButton
{
    if(!_blockButton) {
        _blockButton = [self createButtonWithTitle:NSLocalizedString(@"tagit.block", nil)];
    }
    
    return _blockButton;
}

- (UIButton *)withdrawButton
{
    if(!_withdrawButton) {
        _withdrawButton = [self createButtonWithTitle:NSLocalizedString(@"tagit.withdraw", nil)];
    }
    
    return _withdrawButton;
}

@end
