//
//  TagitCell.h
//  PPBrowser
//
//  Created by qing<PERSON> on 2022/12/17.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <UIKit/UIKit.h>
@class TagitModel;

@interface TagitCellModel : NSObject

@property (nonatomic, strong) NSArray<TagitModel*> *items;

@property (nonatomic, strong) NSString *host;

//辅助字段,主要是卡片化
@property (nonatomic, assign) BOOL isFirstInSection;
@property (nonatomic, assign) BOOL isLastInSection;

@end

@interface TagitCell : UITableViewCell

- (void)updateWithModel:(TagitCellModel *)model;

- (void)updateCornerRadius;

@end

