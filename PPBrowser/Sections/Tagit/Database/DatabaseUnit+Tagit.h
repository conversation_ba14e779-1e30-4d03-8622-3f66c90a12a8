//
//  DatabaseUnit+Tagit.h
//  PPBrowser
//
//  Created by qingbin on 2022/12/17.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "DatabaseUnit.h"
#import "TagitModel.h"

@interface DatabaseUnit (Tagit)

+ (DatabaseUnit *)addTagitWithItem:(TagitModel *)item;

//根据tagitId删除
+ (DatabaseUnit *)removeTagitWithItem:(TagitModel *)item;
//根据一组tagitId删除
+ (DatabaseUnit *)removeTagitsWithItems:(NSArray<TagitModel *> *)items;

+ (DatabaseUnit *)queryTagitWithHost:(NSString *)host;

+ (DatabaseUnit *)queryAllTagit;

// CloudKit, 添加多个标记模式
+ (DatabaseUnit*)addTagitArray:(NSArray<TagitModel*>*)items;

// CloudKit, 批量更新多个标记模式
+ (DatabaseUnit*)updateTagitArray:(NSArray<TagitModel*>*)array;

// CloudKit, 批量删除多个标记模式
+ (DatabaseUnit*)removeTagitArray:(NSArray*)tagitIds;

@end

