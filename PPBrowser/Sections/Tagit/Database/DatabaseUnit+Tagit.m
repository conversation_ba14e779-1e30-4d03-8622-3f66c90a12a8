//
//  DatabaseUnit+Tagit.m
//  PPBrowser
//
//  Created by qingbin on 2022/12/17.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "DatabaseUnit+Tagit.h"
#import "ReactiveCocoa.h"

#import "FMDatabaseQueue.h"
#import "FMDatabase.h"

#import "PPEnums.h"
#import "SyncEngine.h"
#import "CloudKitHelper.h"

@implementation DatabaseUnit (Tagit)

+ (DatabaseUnit *)addTagitWithItem:(TagitModel *)item
{
    //https://stackoverflow.com/questions/3634984/insert-if-not-exists-else-update
    DatabaseUnit* unit = [DatabaseUnit new];
    
    //必须有id
    if(item.tagitId.length == 0) {
        item.tagitId = [[NSUUID UUID] UUIDString];
    }
    
    if(item.host.length == 0) item.host = @"";
    if(item.xpath.length == 0) item.xpath = @"";
    NSString* time = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
    item.ctime = time;
    item.updateTime = time;
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        
        NSString* command = @"INSERT INTO t_tagit(tagitId, host, xpath, updateTime, ctime) VALUES (?,?,?,?,?)\
        on CONFLICT(tagitId) DO UPDATE SET host=excluded.host, xpath=excluded.xpath\
        WHERE excluded.tagitId=t_tagit.tagitId;\
        ";

        BOOL result = [db executeUpdate:command, item.tagitId, item.host, item.xpath, item.updateTime, item.ctime];
        
        if(result) {
            //同步到CloudKit中
            [[SyncEngine shareInstance] syncRecordsToCloudKit:@[[item toCKRecord]] recordIDsToDelete:nil completion:nil];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

+ (DatabaseUnit *)removeTagitWithItem:(TagitModel *)item
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = @"DELETE FROM t_tagit WHERE tagitId=?;";
        BOOL result = [db executeUpdate:command, item.tagitId];

        if(result) {
            //同步到CloudKit中
            CKRecordID* recordID = [[CKRecordID alloc]initWithRecordName:item.tagitId zoneID:[CloudKitHelper focusZoneID]];
            [[SyncEngine shareInstance] syncRecordsToCloudKit:nil recordIDsToDelete:@[recordID] completion:nil];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

//根据一组tagitId删除
+ (DatabaseUnit *)removeTagitsWithItems:(NSArray<TagitModel *> *)items
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        
        NSMutableArray* recordIDs = [NSMutableArray array];
        NSMutableString* ids = [NSMutableString new];
        for(int i=0;i<items.count;i++) {
            TagitModel* item = items[i];
            NSString* str = item.tagitId;
            NSString* option = [NSString stringWithFormat:@"\"%@\"",str];
            [ids appendString:option];
            if(i < items.count-1) {
                [ids appendString:@","];
            }
            
            //同步到CloudKit中
            CKRecordID* recordID = [[CKRecordID alloc]initWithRecordName:str zoneID:[CloudKitHelper focusZoneID]];
            [recordIDs addObject:recordID];
        }
    
        NSString* command = [NSString stringWithFormat:@"DELETE FROM t_tagit WHERE tagitId in (%@);",ids];
        BOOL result = [db executeUpdate:command];

        if(result) {
            //同步到CloudKit中
            [[SyncEngine shareInstance] syncRecordsToCloudKit:nil recordIDsToDelete:recordIDs completion:nil];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

+ (DatabaseUnit *)queryTagitWithHost:(NSString *)host
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = [NSString stringWithFormat:@"SELECT * FROM t_tagit WHERE host like '%%%@%%' ORDER BY ctime ASC", host];
        FMResultSet* set = [db executeQuery:command];
        
        NSMutableArray *array = [[NSMutableArray alloc] init];
        while([set next]) {
            TagitModel* item = [[TagitModel alloc]initWithDictionary:[set resultDictionary] error:nil];
            [array addObject:item];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(array,YES);
            }
        });
    };
    
    return unit;
}

+ (DatabaseUnit *)queryAllTagit
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = [NSString stringWithFormat:@"SELECT * FROM t_tagit ORDER BY ctime ASC"];
        FMResultSet* set = [db executeQuery:command];
        
        NSMutableArray *array = [[NSMutableArray alloc] init];
        while([set next]) {
            TagitModel* item = [[TagitModel alloc]initWithDictionary:[set resultDictionary] error:nil];
            [array addObject:item];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(array,YES);
            }
        });
    };
    
    return unit;
}

#pragma mark -- CloudKit相关操作
// CloudKit, 添加多个标记模式
+ (DatabaseUnit*)addTagitArray:(NSArray<TagitModel*>*)items
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = @"INSERT INTO t_tagit(tagitId, host, xpath, updateTime, ctime) VALUES (?,?,?,?,?)";
        BOOL result = YES;
        for(int i=0;i<items.count;i++) {
            TagitModel* item = items[i];
            result = [db executeUpdate:command, item.tagitId, item.host?:@"", item.xpath?:@"", item.updateTime?:@"1", item.ctime];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

// CloudKit, 批量更新多个标记模式
+ (DatabaseUnit*)updateTagitArray:(NSArray<TagitModel*>*)array
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        BOOL result = NO;
        for(TagitModel* item in array) {
            NSString* command = @"UPDATE t_tagit SET host=?, xpath=?, updateTime=?, ctime=? WHERE tagitId=?;";
            result = [db executeUpdate:command, item.host?:@"", item.xpath?:@"", item.updateTime?:@"1", item.ctime, item.tagitId];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

// CloudKit, 批量删除多个标记模式
+ (DatabaseUnit*)removeTagitArray:(NSArray*)tagitIds
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = [NSString stringWithFormat:@"DELETE FROM t_tagit WHERE tagitId=?;"];

        BOOL result = YES;
        for(int i=0;i<tagitIds.count;i++) {
            NSString* tagitId = tagitIds[i];
            result = [db executeUpdate:command, tagitId];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

@end
