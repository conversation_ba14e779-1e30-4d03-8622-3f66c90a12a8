//
//  TagitManager.m
//  PPBrowser
//
//  Created by qingbin on 2022/12/17.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "TagitManager.h"
#import "DatabaseUnit+Tagit.h"
#import "ReactiveCocoa.h"

#import "PPEnums.h"
#import "PPNotifications.h"

@interface TagitManager ()

@property (nonatomic, strong) NSMutableDictionary* map;

@end

@implementation TagitManager

+ (instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    static TagitManager* obj;
    dispatch_once(&onceToken, ^{
        obj = [TagitManager new];
    });
    
    return obj;
}

- (instancetype)init
{
    self = [super init];
    if(self) {
        [self setupObservers];
    }
    
    return self;
}

//提前缓存
- (void)reloadData
{
    [self.map removeAllObjects];
    self.map = nil;
    
    DatabaseUnit* unit = [DatabaseUnit queryAllTagit];
    @weakify(self)
    [unit setCompleteBlock:^(NSArray* result, BOOL success) {
        @strongify(self)
        for(TagitModel *item in result) {
            NSMutableArray* list = self.map[item.host];
            if(!list) {
                list = [NSMutableArray array];
            }
            
            [list addObject:item];
            self.map[item.host] = list;
        }
    }];
    
    DB_EXEC(unit);
}

- (void)setupObservers
{
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(reloadData)
                                                 name:kCloudKitDataDidChangeNotification
                                               object:nil];
}

- (void)pushElement:(TagitModel *)item
{
    if(item.host.length <= 0) return;
    
    NSMutableArray* list = self.map[item.host];
    if(!list) {
        list = [NSMutableArray array];
    }
    
    [list addObject:item];
    self.map[item.host] = list;
    
    DatabaseUnit* unit = [DatabaseUnit addTagitWithItem:item];
    DB_EXEC(unit);
}

- (NSArray<TagitModel*>*)getElementsWithHost:(NSString *)host
{
    if(host.length <= 0) return nil;
    
    NSMutableArray* list = self.map[host];
    if(!list) {
        list = [NSMutableArray array];
        self.map[host] = list;
    }
    
    return list;
}

- (NSArray *)getAllElementsOfHost
{
    NSMutableArray* list = [NSMutableArray array];
    [self.map enumerateKeysAndObjectsUsingBlock:^(NSString*  _Nonnull host, NSArray*  _Nonnull elements, BOOL * _Nonnull stop) {
        if(elements.count > 0) {
            [list addObject:elements];
        }
    }];
    
    return list;
}

- (void)removeElement:(TagitModel *)item
{
    if(item.host.length <= 0) return;
    
    NSMutableArray* list = self.map[item.host];
    if(!list) {
        list = [NSMutableArray array];
        self.map[item.host] = list;
    }
    
    for(int i=0;i<list.count;i++) {
        TagitModel* obj = list[i];
        if([obj.xpath isEqualToString:item.xpath]) {
            [list removeObject:obj];
            break;
        }
    }
    
    self.map[item.host] = list;
    
    DatabaseUnit* unit = [DatabaseUnit removeTagitWithItem:item];
    DB_EXEC(unit);
}

- (void)removeElementWithHost:(NSString *)host
{
    if(host.length <= 0) return;
    
    NSMutableArray* list = self.map[host];
    if(list.count > 0) {
        [list removeAllObjects];
    }
    
    self.map[host] = list;
}

#pragma mark -- Getters
- (NSMutableDictionary *)map
{
    if(!_map) {
        _map = [NSMutableDictionary dictionary];
    }
    
    return _map;
}

@end
