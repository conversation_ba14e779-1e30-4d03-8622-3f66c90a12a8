//
//  TagitModel.h
//  PPBrowser
//
//  Created by qingbin on 2024/3/30.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "BaseModel.h"
#import <CloudKit/CloudKit.h>
#import "SyncProtocol.h"

@interface TagitModel : BaseModel<SyncProtocol>

@property (nonatomic, strong) NSString* tagitId;

@property (nonatomic, strong) NSString* host;

@property (nonatomic, strong) NSString* xpath;

@property (nonatomic, strong) NSString* ctime;
// 更新时间，和iCloud的对比
@property (nonatomic, strong) NSString *updateTime;

//CloudKit同步
//获取一个默认的CKRecord
- (CKRecord *)toDefaultCKRecord;
//转换成CKRecord
- (CKRecord *)toCKRecord;
//从CKRecord转换为TagitModel
- (instancetype)initWithCKRecord:(CKRecord *)record;
//判断两个广告过滤规则是否一致，1、根据uuid,2、判断内容是否相等，如果是URL，那么判断它们的url是否一致
- (BOOL)objectIsEqualTo:(id)obj;
//返回uuid
- (NSString*)getUuid;
//更新时间
- (NSString*)getUpdateTime;

@end

