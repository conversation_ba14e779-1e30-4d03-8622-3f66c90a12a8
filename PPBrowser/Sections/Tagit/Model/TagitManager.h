//
//  TagitManager.h
//  PPBrowser
//
//  Created by qingbin on 2022/12/17.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TagitModel.h"

@interface TagitManager : NSObject

+ (instancetype)shareInstance;

@property (nonatomic, strong) TagitModel* currentSelectItem;

//提前缓存
- (void)reloadData;

- (void)pushElement:(TagitModel *)item;

- (NSArray<TagitModel*>*)getElementsWithHost:(NSString *)host;

- (NSArray *)getAllElementsOfHost;

- (void)removeElement:(TagitModel *)item;

- (void)removeElementWithHost:(NSString *)host;

@end
