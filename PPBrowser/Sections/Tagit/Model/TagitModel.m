//
//  TagitModel.m
//  PPBrowser
//
//  Created by qing<PERSON> on 2024/3/30.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import "TagitModel.h"

#import "OpenUDID.h"
#import "PPEnums.h"
#import "CloudKitHelper.h"

@implementation TagitModel

//返回uuid
- (NSString*)getUuid
{
    return self.tagitId;
}

//更新时间
- (NSString*)getUpdateTime
{
    return self.updateTime;
}

//CloudKit同步
//获取一个默认的CKRecord
- (CKRecord *)toDefaultCKRecord
{
    CKRecordID* recordID = [[CKRecordID alloc]initWithRecordName:self.tagitId zoneID:[CloudKitHelper focusZoneID]];
    CKRecord* record = [[CKRecord alloc]initWithRecordType:NSStringFromClass(TagitModel.class) recordID:recordID];
    
    return record;
}

//转换成CKRecord
- (CKRecord *)toCKRecord
{
    //CREATE TABLE IF NOT EXISTS t_tagit(uuid TEXT PRIMARY KEY, host TEXT, xpath TEXT, originUrl TEXT, isActive INTEGER, ctime TEXT)
    
    CKRecord* record = [self toDefaultCKRecord];
    
    record[@"tagitId"] = self.tagitId;
    record[@"host"] = self.host?:@"";
    record[@"xpath"] = self.xpath?:@"";
    record[@"ctime"] = self.ctime;
    
    //额外添加的字段，用于CloudKit
    //app版本号
    NSString* version = [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleShortVersionString"];
    record[@"appVersion"] = version?:@"";
    //上传到CloudKit创建时间
    record[@"appCtime"] = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
    //用户的uuid
    record[@"appUserID"] = [OpenUDID value]?:@"";
    //添加类型区分
    record[@"appType"] = @(CloudModelTypeTagit);
    
    return record;
}
//从CKRecord转换为TagitModel
- (instancetype)initWithCKRecord:(CKRecord *)record
{
    self = [super init];
    if(self) {
        self.tagitId = record[@"tagitId"];
        self.host = record[@"host"];
        self.xpath = record[@"xpath"];
        self.ctime = record[@"ctime"];
    }
    
    return self;
}

//判断两个标记模式是否一致，1、根据uuid,2、判断内容是否相等，判断它们的originUrl和xpath是否一致
- (BOOL)objectIsEqualTo:(id)obj
{
    TagitModel* item = obj;
    if([self.tagitId isEqualToString:item.tagitId]) return YES;
    
    return [self.host isEqualToString:item.host] && [self.xpath isEqualToString:item.xpath];
}

@end
