//
//  TagitViewController.m
//  PPBrowser
//
//  Created by qingbin on 2022/12/17.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "TagitViewController.h"
#import "DatabaseUnit+Tagit.h"

#import "TagitManager.h"

#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "UIView+FrameHelper.h"
#import "NSObject+Helper.h"
#import "ThemeProtocol.h"
#import "BrowserUtils.h"

#import "PPNotifications.h"
#import "TagitCell.h"

#import "UITableView+HintMessage.h"
#import "SettingSwitchView.h"
#import "PreferenceManager.h"

#import "VIPController.h"
#import "BaseNavigationController.h"
#import "AppDelegate.h"
#import "BrowserViewController.h"

#import "PaymentManager.h"
#import "YYText.h"

@interface TagitViewController ()<UITableViewDelegate,UITableViewDataSource,ThemeProtocol>

@property (nonatomic, strong) UIView *emptyView;

@property (nonatomic, strong) UILabel *tipsLabel;

@property (nonatomic, strong) UITableView* tableView;

@property (nonatomic, strong) NSMutableArray* model;

@end

@implementation TagitViewController

- (void)viewDidLoad
{
    [super viewDidLoad];

    self.title = NSLocalizedString(@"tagit.title", nil);
    self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    
    [self createCustomLeftBarButtonItem];
    
    [self addSubviews];
    [self defineLayout];
    [self setupObservers];
    
    [self applyTheme];
    
    [self requestTagits];
}

#pragma mark -- Network
- (void)requestTagits
{
    NSArray* allElements = [[TagitManager shareInstance] getAllElementsOfHost];
    for(NSArray* objs in allElements) {
        TagitCellModel* item = [TagitCellModel new];
        item.items = objs;
        
        TagitModel* obj = objs.firstObject;
        item.host = obj.host;
        
        [self.model addObject:item];
    }
    
    TagitCellModel* item = self.model.firstObject;
    item.isFirstInSection = YES;
    item = self.model.lastObject;
    item.isLastInSection = YES;
    
    [self.tableView reloadData];
    
    [self showOrHideHintMessage];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.tableView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        
        self.emptyView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        self.tipsLabel.textColor = UIColor.whiteColor;
    } else {
        self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.tableView.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        
        self.emptyView.backgroundColor = UIColor.whiteColor;
        self.tipsLabel.textColor = [UIColor colorWithHexString:@"#333333"];
    }
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    //只有当前的controller会触发一次, 其它已存在的controller走通知
    if(needReload) {
        //修改暗黑模式
        [self applyTheme];
        [self.tableView reloadData];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
    [self.tableView reloadData];
}

#pragma mark -- 无数据提醒
- (void)showOrHideHintMessage
{
    if(self.model.count > 0) {
        self.emptyView.hidden = YES;
    } else {
        self.emptyView.hidden = NO;
    }
}

- (void)setupObservers
{
}

#pragma mark -- layout
- (void)addSubviews
{
    [self.view addSubview:self.tableView];
    
    [self.view addSubview:self.emptyView];
    [self.emptyView addSubview:self.tipsLabel];
}

- (void)defineLayout
{
    float leftOffset = iPadValue(30, 15);
    float topOffset = iPadValue(30, 15);
    
    [self.emptyView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.view);
        make.top.equalTo(self.view).mas_offset(50);
        make.left.mas_offset(leftOffset);
        make.right.mas_offset(-leftOffset);
    }];
    
    [self.tipsLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(leftOffset);
        make.right.mas_offset(-leftOffset);
        make.top.mas_offset(topOffset);
        
        //决定empty高度
        make.bottom.mas_offset(-topOffset);
    }];
    
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(0);
        make.left.mas_offset(0);
        make.right.mas_offset(-0);
        make.bottom.equalTo(self.view);
    }];
}

#pragma mark - UITableViewDataSource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return self.model.count;
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    TagitCellModel* item = self.model[indexPath.row];
    TagitCell *cell =  [tableView dequeueReusableCellWithIdentifier:NSStringFromClass(TagitCell.class)];
    [cell updateWithModel:item];

    return cell;
}

#pragma mark - UITableViewDelegate
- (BOOL)tableView:(UITableView *)tableView canEditRowAtIndexPath:(NSIndexPath *)indexPath
{
    return YES;
}

// 只适用于ios11 以及以后版本
- (UISwipeActionsConfiguration *)tableView:(UITableView *)tableView trailingSwipeActionsConfigurationForRowAtIndexPath:(NSIndexPath *)indexPath  API_AVAILABLE(ios(11.0))
{
    // 删除数据
    @weakify(self)
    UIContextualAction *deleteAction = [UIContextualAction contextualActionWithStyle:UIContextualActionStyleDestructive title:NSLocalizedString(@"tableview.delete", nil) handler:^(UIContextualAction * _Nonnull action, __kindof UIView * _Nonnull sourceView, void (^ _Nonnull completionHandler)(BOOL)) {
        @strongify(self)
        [tableView setEditing:NO animated:YES];//退出编辑模式，隐藏左滑菜单
    
        TagitCellModel* cellModel = self.model[indexPath.row];
        
        //删除数据库数据+暂停脚本
        DatabaseUnit* unit = [DatabaseUnit removeTagitsWithItems:cellModel.items];
        @weakify(self)
        [unit setCompleteBlock:^(id result, BOOL success) {
            @strongify(self)
            if(success) {
                //删缓存
                [[TagitManager shareInstance] removeElementWithHost:cellModel.host];
                
                [tableView beginUpdates];
                [self.model removeObject:cellModel];
                [tableView deleteRowsAtIndexPaths:@[indexPath] withRowAnimation:UITableViewRowAnimationFade];
                [tableView endUpdates];
                
                [self showOrHideHintMessage];
                
                //重新加载脚本
                [[NSNotificationCenter defaultCenter] postNotificationName:kReloadCurrentWebViewNotification object:nil];
            }
        }];
        
        DB_EXEC(unit);
    }];
    [deleteAction setBackgroundColor:UIColor.redColor];

    // 添加
    UISwipeActionsConfiguration *actions = [UISwipeActionsConfiguration configurationWithActions:@[deleteAction]];
    actions.performsFirstActionWithFullSwipe = NO;
    return actions;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    [tableView deselectRowAtIndexPath:indexPath animated:NO];

}

#pragma mark -- lazy init
- (UITableView *)tableView
{
    if(!_tableView)
    {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];

        _tableView.separatorStyle  = UITableViewCellSeparatorStyleNone;
        _tableView.showsHorizontalScrollIndicator = NO;
        _tableView.showsVerticalScrollIndicator   = NO;
        _tableView.delegate   = self;
        _tableView.dataSource = self;
        _tableView.rowHeight = [SettingSwitchView height];
        _tableView.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        
        [_tableView registerClass:[TagitCell class] forCellReuseIdentifier:NSStringFromClass([TagitCell class])];
        
        float offset = iPadValue(30, 20);
        _tableView.tableHeaderView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, offset)];
        
        UIWindow* window = [NSObject normalWindow];
        _tableView.tableFooterView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, offset+window.safeAreaInsets.bottom)];
        
        _tableView.sectionFooterHeight = 0.0;
    }
    
    return _tableView;
}

- (NSMutableArray *)model
{
    if(!_model) {
        _model = [NSMutableArray array];
    }
    
    return _model;
}

- (UIView *)emptyView
{
    if(!_emptyView) {
        _emptyView = [UIView new];
        
        _emptyView.layer.cornerRadius = iPadValue(20, 10);
        _emptyView.layer.masksToBounds = YES;
        
        _emptyView.hidden = YES;
    }
    
    return _emptyView;
}

- (UILabel *)tipsLabel
{
    if(!_tipsLabel) {
        _tipsLabel = [UILabel new];
        _tipsLabel.numberOfLines = 0;
        _tipsLabel.font = [UIFont systemFontOfSize:iPadValue(20, 16)];
        _tipsLabel.textColor = [UIColor colorWithHexString:@"#666666"];
        _tipsLabel.textAlignment = NSTextAlignmentLeft;
        
        NSString* text = NSLocalizedString(@"adblock.tagit.tips", nil);
        NSMutableAttributedString* attributedString = [[NSMutableAttributedString alloc]initWithString:text];
        attributedString.yy_lineSpacing = 5;
        attributedString.yy_paragraphSpacing = 10;
        
        _tipsLabel.attributedText = attributedString;
    }
    
    return _tipsLabel;
}


@end
