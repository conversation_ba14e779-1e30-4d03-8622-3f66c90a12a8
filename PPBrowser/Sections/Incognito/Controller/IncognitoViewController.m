//
//  IncognitoViewController.m
//  PPBrowser
//
//  Created by qingbin on 2025/5/2.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "IncognitoViewController.h"

#import "Masonry.h"
#import "MaizyHeader.h"
#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "ReactiveCocoa.h"
#import "UIView+FrameHelper.h"
#import "UIViewController+Helper.h"
#import "NSObject+Helper.h"

#import "SettingSwitchView.h"
#import "SettingArrowView.h"
#import "SettingTextView.h"
#import "SettingSegmentView.h"
#import "SettingSegmentAndTextView.h"
#import "SettingSwitchAndTextView.h"
#import "PaddingNewLabel.h"

#import "PPNotifications.h"
#import "VIPController.h"
#import "BaseNavigationController.h"
#import "BrowserUtils.h"

#import "BrowserHelper.h"
#import "AppDelegate.h"

#import "PaymentManager.h"
#import "PPEnums.h"

#import "Tab.h"
#import "URIFixup.h"
#import "InternalURL.h"

@interface IncognitoViewController ()
//
@property (nonatomic, strong) UIStackView* stackView;
//开启无痕模式
@property (nonatomic, strong) SettingSegmentAndTextView *enabledIncognitoSegment;
//多个标签页之间同步登录信息
@property (nonatomic, strong) SettingSwitchAndTextView *sharedSessionBetweenTabsSwitch;
//添加分组视图
@property (nonatomic, strong) UIView *groupView;

@end

@implementation IncognitoViewController

- (void)viewDidLoad
{
    [super viewDidLoad];
    
    self.title = NSLocalizedString(@"icognito.title", nil);
    [self applyTheme];
    
    [self addSubviews];
    [self defineLayout];
    [self bindData];
    
    [self createCustomLeftBarButtonItem];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if (isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.stackView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColorLine];
        self.groupView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
    } else {
        self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.stackView.backgroundColor = [UIColor colorWithHexString:@"#eeeeee"];
        self.groupView.backgroundColor = UIColor.whiteColor;
    }
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    //只有当前的controller会触发一次, 其它已存在的controller走通知
    if(needReload) {
        //修改暗黑模式
        [self applyTheme];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}

#pragma mark - 数据绑定

- (void)bindData
{
    // 是否开启无痕模式
    @weakify(self);
    [self.enabledIncognitoSegment setSelectIndexBlock:^(int index) {
        @strongify(self)
        [self handleIncognitoStatusChange:index];
    }];
    
    //多个标签页之间同步登录信息
    [self.sharedSessionBetweenTabsSwitch setDidSwithAction:^(BOOL isOn) {
        @strongify(self)
        [self handleSharedSessionBetweenTabsStatusChange:isOn];
    }];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(darkThemeDidChangeNotification:)
                                                 name:kDarkThemeDidChangeNotification
                                               object:nil];
    
    // 初始化开关状态
    [self updateSwitchStatus];
}

#pragma mark - Actions

- (void)updateSwitchStatus {
    // 是否开启无痕模式
    BOOL isPrivate = [[PreferenceManager shareInstance].items.isPrivate boolValue];
    [self.enabledIncognitoSegment updateWithTitle:NSLocalizedString(@"icognito.enabled.title", nil)
                                           detail:NSLocalizedString(@"icognito.enabled.detail", nil)
                                      selectIndex:isPrivate ? 0 : 1];
    
    //多个标签页之间同步登录信息
    BOOL sharedSession = [[PreferenceManager shareInstance].items.sharedSession boolValue];
    [self.sharedSessionBetweenTabsSwitch updateWithIsOn:sharedSession];
}

- (void)handleIncognitoStatusChange:(int)index
{
    //无痕模式状态发生变化
    BOOL isOn = (index == 0);
    [PreferenceManager shareInstance].items.isPrivate = @(isOn);
    
    if (!isOn) {
        //非无痕模式，需要开启"多个标签页之间同步登录信息"
        [PreferenceManager shareInstance].items.sharedSession = @(YES);
    }
    
    [[PreferenceManager shareInstance] encode];
    
    //无痕模式状态发生变化，关闭当前所有标签页，再重新打开当前选中标签
    [[NSNotificationCenter defaultCenter] postNotificationName:kIncognitoStatusChangedNotification object:nil];
    
    //更新页面状态
    [self updateSwitchStatus];
}

- (void)handleSharedSessionBetweenTabsStatusChange:(BOOL)isOn
{
    [PreferenceManager shareInstance].items.sharedSession = @(isOn);
    if (!isOn) {
        //关闭了"多个标签页之间同步登录信息"，那么则需要开启无痕模式
        [PreferenceManager shareInstance].items.isPrivate = @(YES);
    }
    
    [[PreferenceManager shareInstance] encode];
    
    //无痕模式状态发生变化，关闭当前所有标签页，再重新打开当前选中标签
    [[NSNotificationCenter defaultCenter] postNotificationName:kIncognitoStatusChangedNotification object:nil];
    
    //更新页面状态
    [self updateSwitchStatus];
}

#pragma mark - Layout

- (void)addSubviews {
    [self.view addSubview:self.groupView];
    [self.groupView addSubview:self.stackView];
}

- (void)defineLayout {
    float margin = iPadValue(30, 15);
    float topOffset = iPadValue(30, 20);
    
    [self.groupView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view.mas_safeAreaLayoutGuideTop).offset(topOffset);
        make.left.equalTo(self.view).offset(margin);
        make.right.equalTo(self.view).offset(-margin);
    }];
    
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.groupView);
        make.left.right.bottom.equalTo(self.groupView);
    }];
}

#pragma mark - getters

- (UIStackView *)stackView
{
    if(!_stackView) {
        _stackView = [[UIStackView alloc]initWithArrangedSubviews:@[
            self.enabledIncognitoSegment,
            self.sharedSessionBetweenTabsSwitch
        ]];
        
        _stackView.axis = UILayoutConstraintAxisVertical;
        _stackView.spacing = 0; // 移除spacing
    }
    
    return _stackView;
}

- (UIView *)groupView {
    if (!_groupView) {
        _groupView = [[UIView alloc] init];
//        _groupView.backgroundColor = [UIColor secondarySystemGroupedBackgroundColor];
        
        // 设置圆角和阴影
        _groupView.layer.cornerRadius = 10;
        _groupView.layer.masksToBounds = YES;
        _groupView.layer.shadowColor = [UIColor blackColor].CGColor;
        _groupView.layer.shadowOffset = CGSizeMake(0, 2);
        _groupView.layer.shadowOpacity = 0.1;
        _groupView.layer.shadowRadius = 4;
    }
    return _groupView;
}

- (SettingSegmentAndTextView *)enabledIncognitoSegment
{
    if (!_enabledIncognitoSegment) {
        _enabledIncognitoSegment = [[SettingSegmentAndTextView alloc] initWithTitle:NSLocalizedString(@"icognito.enabled.title", nil)
                                                                           showLine:YES
                                                                           segments:@[NSLocalizedString(@"common.enable", nil), NSLocalizedString(@"common.disable", nil)]];
    }
    return _enabledIncognitoSegment;
}

- (SettingSwitchAndTextView *)sharedSessionBetweenTabsSwitch {
    if (!_sharedSessionBetweenTabsSwitch) {
        _sharedSessionBetweenTabsSwitch = [[SettingSwitchAndTextView alloc] initWithShowLine:NO];
        [_sharedSessionBetweenTabsSwitch updateWithTitle:NSLocalizedString(@"icognito.sharedSession.title", nil)
                                                  detail:NSLocalizedString(@"icognito.sharedSession.detail", nil)
                                                    isOn:YES];
    }
    return _sharedSessionBetweenTabsSwitch;
}

@end
