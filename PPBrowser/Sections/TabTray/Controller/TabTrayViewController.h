//
//  TabTrayViewController.h
//  PandaBrowser
//
//  Created by qingbin on 2022/3/14.
//

#import "BaseViewController.h"
#import "TabTrayCell.h"

@class TabManager;

@interface TabTrayViewController : BaseViewController<UIViewControllerTransitioningDelegate,UIViewControllerAnimatedTransitioning>

- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)new NS_UNAVAILABLE;

- (instancetype)initWithTabManager:(TabManager*)tabManager;

//加载完数据再显示,否则会引起崩溃
- (void)loadingData:(void(^)(void))completedBlock;

@end

