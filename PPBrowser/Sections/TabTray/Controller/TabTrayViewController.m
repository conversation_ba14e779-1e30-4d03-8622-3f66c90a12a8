//
//  TabTrayViewController.m
//  PandaBrowser
//
//  Created by qingbin on 2022/3/14.
//

#import "TabTrayViewController.h"

#import "MaizyHeader.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"

#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "BrowserViewController.h"
#import "TabSettingViewController.h"

#import "TabManager.h"
#import "DatabaseUnit+Helper.h"
#import "NSObject+Helper.h"
#import "BottomToolbar.h"
#import "UIImageView+WebCache.h"

#import "ThemeProtocol.h"
#import "PreferenceManager.h"
#import "BrowserUtils.h"
#import "CustomTitleAndImageView.h"
#import "CustomTextField.h"

#import "HomeHelper.h"
#import "AppDelegate.h"
#import "UIAlertController+SafePresentation.h"

@interface TabTrayViewController ()<UICollectionViewDelegate,UICollectionViewDataSource,ThemeProtocol,UITextFieldDelegate>
@property (nonatomic, strong) NSMutableArray* model;
@property (nonatomic, strong) UICollectionView *collectionView;

// 搜索栏
@property (nonatomic, strong) UIView *searchBarView;
@property (nonatomic, strong) CustomTextField *searchTextField;

// 底部工具栏
@property (nonatomic, strong) UIStackView* toolbarStackView;
@property (nonatomic, strong) CustomTitleAndImageView* backBtn;
@property (nonatomic, strong) CustomTitleAndImageView* newTabBtn;
@property (nonatomic, strong) CustomTitleAndImageView* closeAllBtn;
@property (nonatomic, strong) CustomTitleAndImageView* settingsBtn;

@property (nonatomic, weak) TabManager *tabManager;

@property (nonatomic, assign) int selectedIndex;
@property (nonatomic, assign) BOOL bShow;

@property (nonatomic, assign) BOOL initialScrollCompleted;
@end

@implementation TabTrayViewController

- (instancetype)initWithTabManager:(TabManager*)tabManager
{
    self = [super init];
    if(self) {
        self.tabManager = tabManager;
    }
    
    return self;
}

- (void)viewDidLoad
{
    [super viewDidLoad];
    
    // 设置导航栏
    self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    
    [self addSubviews];
    [self defineLayout];
    [self setupObservers];
    
    if(self.model.count > 0) {
        // 更新order
        NSMutableArray* items = [NSMutableArray array];
        for(int i=0;i<self.model.count;i++) {
            TabModel* item = self.model[i];
            item.ppOrder = i;
            
            [items addObject:item];
        }
        
        DatabaseUnit* unit = [DatabaseUnit updateAllTabsOrder:items];
        DB_EXEC(unit);
    }
    
    [self applyTheme];
}

- (BaseNavigationBarStyle)preferredNavigationBarStyle
{
    return BaseNavigationBarStyleNoneWithDefaultContent;
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.collectionView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        
        // 搜索栏
        self.searchBarView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        self.searchTextField.backgroundColor = [UIColor colorWithHexString:kDarkThemeColorTextFiledBackgroundColor];
        self.searchTextField.textColor = UIColor.whiteColor;
        [self.searchTextField updatePlaceHolder:NSLocalizedString(@"tab.search.placeholder", nil) color:[UIColor colorWithHexString:kDarkThemeColorTextFiledContent]];
        
        // 底部工具栏
        self.toolbarStackView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        
        for(CustomTitleAndImageView* button in self.toolbarStackView.arrangedSubviews) {
            [button updateWith:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
                imageView.tintColor = UIColor.whiteColor;
                titleLabel.textColor = UIColor.whiteColor;
            }];
        }
    } else {
        self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.collectionView.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        
        // 搜索栏
        self.searchBarView.backgroundColor = UIColor.whiteColor;
        self.searchTextField.backgroundColor = [UIColor colorWithHexString:@"#f2f2f7"];
        self.searchTextField.textColor = [UIColor colorWithHexString:@"#333333"];
        [self.searchTextField updatePlaceHolder:NSLocalizedString(@"tab.search.placeholder", nil) color:[UIColor colorWithHexString:@"#999999"]];
        
        // 底部工具栏
        self.toolbarStackView.backgroundColor = UIColor.whiteColor;
        
        for(CustomTitleAndImageView* button in self.toolbarStackView.arrangedSubviews) {
            [button updateWith:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
                imageView.tintColor = [UIColor colorWithHexString:@"#333333"];
                titleLabel.textColor = [UIColor colorWithHexString:@"#333333"];
            }];
        }
    }
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    // 只有当前的controller会触发一次, 其它已存在的controller走通知
    if(needReload) {
        // 修改暗黑模式
        [self applyTheme];
        [self.collectionView reloadData];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}

- (void)viewDidLayoutSubviews
{
    [super viewDidLayoutSubviews];
    
    // 首次打开时滚动到选中的标签页
    if(self.initialScrollCompleted) return;
    self.initialScrollCompleted = YES;
    
    dispatch_async(dispatch_get_main_queue(), ^{
        NSIndexPath* indexpath = [NSIndexPath indexPathForRow:self.selectedIndex inSection:0];
        [self.collectionView scrollToItemAtIndexPath:indexpath atScrollPosition:UICollectionViewScrollPositionCenteredVertically animated:false];
    });
}

#pragma mark -- iPad适配
- (void)viewWillTransitionToSize:(CGSize)size
       withTransitionCoordinator:(id<UIViewControllerTransitionCoordinator>)coordinator
{
    // 屏幕旋转
    [BrowserUtils shareInstance].transitionToSize = size;
    
    [self.collectionView.collectionViewLayout invalidateLayout];
    
    UIWindow* window = YBIBNormalWindow();
    float height = iPadValue(80, 60) + window.safeAreaInsets.bottom;
    if([BrowserUtils isiPhone] && [[BrowserUtils shareInstance]isLandscape]) {
        // iPhone+横屏
        height = 60;
    }
    
    [self.toolbarStackView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(height).priorityHigh();
    }];
}

#pragma mark -- 屏幕旋转,iPad适配
- (void)willTransitionToTraitCollection:(UITraitCollection *)newCollection
              withTransitionCoordinator:(id<UIViewControllerTransitionCoordinator>)coordinator
{
    // During split screen launching on iPad, this callback gets fired before viewDidLoad gets a chance to
    // set things up. Make sure to only update the toolbar state if the view is ready for it.
    if([self isViewLoaded]) {
        
    }
}

- (float)cellMargin
{
    if([BrowserUtils isiPad]) {
        // iPad
        if([[BrowserUtils shareInstance] isLandscape]) {
            // 横屏
            return 30;
        } else {
            // 竖屏
            return 25;
        }
    } else {
        // iPhone
        if([[BrowserUtils shareInstance] isLandscape]) {
            // 横屏
            return 20;
        } else {
            // 竖屏
            return 15;
        }
    }
}

#pragma mark -- 加载数据
- (void)loadingData:(void(^)(void))completedBlock
{
    DatabaseUnit* unit = [DatabaseUnit selectAllTabs];
    @weakify(self)
    unit.completeBlock = ^(NSArray<TabModel*>* result, BOOL success) {
        @strongify(self)
        if(success && result.count>0) {
            self.model = [result mutableCopy];
        
            for(int i=0;i<result.count;i++) {
                TabModel* item = result[i];
                if(item.isSelected) {
                    self.selectedIndex = i;
                }
            }
            
            [self.collectionView reloadData];
        }
        
        if(completedBlock) {
            completedBlock();
        }
    };
    
    DB_EXEC(unit);
}

- (void)setupObservers
{
    // 底部工具栏按钮
    @weakify(self)
    [self.backBtn setTapAction:^{
        @strongify(self)
        //v2.7.3,崩溃修复
        if (self.model.count > 0) {
            self.selectedIndex = (int)MIN(self.selectedIndex, self.model.count-1);
            
            TabModel* tabModel = self.model[self.selectedIndex];
            Tab* tab = [self.tabManager tabForTabModel:tabModel];
            if(!tab) {
                [self.tabManager restoreTab:tabModel];
            } else {
                [self.tabManager selectTab:tab];
            }
            
            [self dismissViewControllerAnimated:YES completion:nil];
        } else {
            //处于筛选模式下，并且没有筛选结果的情况
            //直接无动画关闭
            [self dismissViewControllerAnimated:NO completion:nil];
        }
    }];
    
    [self.newTabBtn setTapAction:^{
        @strongify(self)
        [self addNewTab];
    }];
    
    [self.closeAllBtn setTapAction:^{
        @strongify(self)
        [self closeAllTabsAction];
    }];
    
    [self.settingsBtn setTapAction:^{
        @strongify(self)
        [self openSettings];
    }];
    
    // 搜索框
    [[[[[self.searchTextField rac_textSignal] skip:1] distinctUntilChanged] throttle:0.2]
    subscribeNext:^(id x) {
        @strongify(self)
        [self filterTabsBySearchQuery];
    }];

    UILongPressGestureRecognizer* gesture = [[UILongPressGestureRecognizer alloc]init];
    gesture.minimumPressDuration = 0.2;
    [self.collectionView addGestureRecognizer:gesture];
    [[gesture rac_gestureSignal]
    subscribeNext:^(id x) {
        @strongify(self)
        [self longPressGestureAction:x];
    }];
}

#pragma mark -- 长按拖动
- (void)longPressGestureAction:(UILongPressGestureRecognizer *)longGesture
{
    switch (longGesture.state) {
        case UIGestureRecognizerStateBegan:{
            // 通过手势获取点，通过点获取点击的indexPath， 移动该cell
            NSIndexPath *aIndexPath = [self.collectionView indexPathForItemAtPoint:[longGesture locationInView:self.collectionView]];
            [self.collectionView beginInteractiveMovementForItemAtIndexPath:aIndexPath];
        }
            break;
        case UIGestureRecognizerStateChanged:{
            [self.collectionView updateInteractiveMovementTargetPosition:[longGesture locationInView:self.collectionView]];
        }
            break;
        case UIGestureRecognizerStateEnded:{
            // 移动完成关闭cell移动
            [self.collectionView endInteractiveMovement];
            
            //更新order
            NSMutableArray* items = [NSMutableArray array];
            for(int i=0;i<self.model.count;i++) {
                TabModel* item = self.model[i];
                item.ppOrder = i;
                
                if(item.isSelected) {
                    self.selectedIndex = i;
                }
                
                [items addObject:item];
            }
        
            DatabaseUnit* unit = [DatabaseUnit updateAllTabsOrder:items];
            DB_EXEC(unit);
        }
            break;
        default:
            [self.collectionView endInteractiveMovement];
            break;
    }
}

#pragma mark -- 按钮事件
- (void)addNewTab
{
    //更新标签数量
    int allTabsCount = [BrowserUtils shareInstance].allTabsCount;
    allTabsCount++;
    [self.tabManager.browser.bottomToolbar updateTabCount:allTabsCount];
    [self.tabManager.browser.topToolbar updateTabCount:allTabsCount];
    
    TabModel* tabModel = [HomeHelper getHomeTabModel];
    Tab* tab = [self.tabManager addTabAndSelect:tabModel];
    [[ScreenshotHelper shareInstance] takeScreenshot:tab];
    
    [self.model addObject:tabModel];
    self.selectedIndex = (int)self.model.count-1;
    
    [self dismissViewControllerAnimated:YES completion:nil];
}

- (void)closeAllTabsAction
{
    UIAlertController* alertController = [UIAlertController alertControllerWithTitle:NSLocalizedString(@"tab.alert.title", nil) message:nil preferredStyle:UIAlertControllerStyleAlert];
    UIAlertAction *confirmAction=[UIAlertAction actionWithTitle:NSLocalizedString(@"alert.confirm", nil) style:UIAlertActionStyleDestructive handler:^(UIAlertAction * _Nonnull action) {
        [self.tabManager removeAllTabsFromTabTray];
        [self dismissViewControllerAnimated:YES completion:nil];
    }];
    UIAlertAction *cancleAction=[UIAlertAction actionWithTitle:NSLocalizedString(@"alert.cancel", nil) style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
        [alertController dismissViewControllerAnimated:YES completion:nil];
    }];

    [alertController addAction:confirmAction];
    [alertController addAction:cancleAction];
    
//    [self presentViewController:alertController animated:YES completion:nil];
    //v2.6.8, 统一present方法，防止崩溃
    [alertController presentSafelyFromViewController:self];
}

- (void)openSettings
{
    // 实现设置功能
    TabSettingViewController* vc = [[TabSettingViewController alloc] init];
    BaseNavigationController* navc = [[BaseNavigationController alloc] initWithRootViewController:vc];
    
    if ([BrowserUtils isiPhone] && ![[BrowserUtils shareInstance] isLandscape]) {
        // iPhone竖屏 - 使用半模态弹窗
        if (@available(iOS 15.0, *)) {
            UISheetPresentationController* sheet = navc.sheetPresentationController;
            sheet.detents = @[
                UISheetPresentationControllerDetent.mediumDetent,
                UISheetPresentationControllerDetent.largeDetent
            ];
//            sheet.prefersGrabberVisible = YES;
            sheet.preferredCornerRadius = 10;
        }
        [self presentViewController:navc animated:YES completion:nil];
        
    } else if ([BrowserUtils isiPad]) {
        // iPad
//        navc.modalPresentationStyle = UIModalPresentationFormSheet;
//        vc.preferredContentSize = CGSizeMake(kScreenWidth-90, kScreenHeight-90);
//        
//        [self presentViewController:navc animated:YES completion:nil];
        //v2.6.8,统一present
        [self presentCustomToViewController:navc];
    } else {
        // iPhone横屏 - 使用普通Push
        [self.navigationController pushViewController:vc animated:YES];
    }
}

#pragma mark -- 搜索过滤
- (void)filterTabsBySearchQuery
{
    NSString* query = self.searchTextField.text;
    if (query.length == 0) {
        // 如果搜索框为空，加载所有标签页
        [self loadingData:nil];
        return;
    }
    
    // 过滤标签页
    NSPredicate *predicate = [NSPredicate predicateWithFormat:@"title CONTAINS[cd] %@ OR url CONTAINS[cd] %@", query, query];
    
    DatabaseUnit* unit = [DatabaseUnit selectAllTabs];
    @weakify(self)
    unit.completeBlock = ^(NSArray<TabModel*>* result, BOOL success) {
        @strongify(self)
        if(success) {
            NSArray* filtered = [result filteredArrayUsingPredicate:predicate];
            self.model = [NSMutableArray arrayWithArray:filtered];
            [self.collectionView reloadData];
        }
    };
    
    DB_EXEC(unit);
}

#pragma mark - 布局设置

- (void)addSubviews
{
    [self.view addSubview:self.searchBarView];
    [self.searchBarView addSubview:self.searchTextField];
    
    [self.view addSubview:self.collectionView];
    
    [self.view addSubview:self.toolbarStackView];
    
    //2.6.8 阿拉伯语布局适配
    [NSObject rtlLayoutSupportWithViews:@[self.searchBarView, self.searchTextField, self.collectionView, self.toolbarStackView]];
}

- (void)defineLayout
{
    UIWindow* window = YBIBNormalWindow();
    float offset = iPadValue(30, 15);
    
    // 搜索栏
    float searchBarHeight = 60 + window.safeAreaInsets.top;
    [self.searchBarView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view);
        make.left.right.equalTo(self.view);
        make.height.mas_equalTo(searchBarHeight);
    }];
    
    [self.searchTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.searchBarView).offset(-(60-36)/2.0);
        make.left.equalTo(self.searchBarView).offset(offset);
        make.right.equalTo(self.searchBarView).offset(-offset);
        make.height.mas_equalTo(36);
    }];
        
    // 底部工具栏
    float height = iPadValue(80, 60) + window.safeAreaInsets.bottom;
    if([BrowserUtils isiPhone] && [[BrowserUtils shareInstance]isLandscape]) {
        // iPhone+横屏
        height = 60;
    }
    
    [self.toolbarStackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.left.right.equalTo(self.view);
        make.height.mas_equalTo(height).priorityHigh();
    }];
    
    // 设置集合视图
    [self.collectionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.searchBarView.mas_bottom);
        make.left.right.equalTo(self.view);
        make.bottom.equalTo(self.toolbarStackView.mas_top);
    }];
}

#pragma mark -- 拖动代理
- (BOOL)collectionView:(UICollectionView *)collectionView canMoveItemAtIndexPath:(NSIndexPath *)indexPath
{
    return YES;
}

- (void)collectionView:(UICollectionView *)collectionView moveItemAtIndexPath:(NSIndexPath *)sourceIndexPath toIndexPath:(NSIndexPath *)destinationIndexPath
{
    id objc = [self.model objectAtIndex:sourceIndexPath.item];
    [self.model removeObject:objc];
    [self.model insertObject:objc atIndex:destinationIndexPath.item];
}

#pragma mark -- UICollectionViewDelegate
- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath
{
    return [self cellSizeForCurrentDevice];
}

- (CGSize)cellSizeForCurrentDevice
{
    CGSize size;
    float margin = [self cellMargin];
    
    if([BrowserUtils isiPad]) {
        //iPad
        if([[BrowserUtils shareInstance] isLandscape]) {
            //横屏, 4个cell
            float width = (kScreenWidth-margin*5)/4.0 - 1.0;
            size = CGSizeMake(width, width);
        } else {
            //竖屏, 3个cell
            float width = (kScreenWidth-margin*4)/3.0 - 1.0;
            size = CGSizeMake(width, width);
        }
    } else {
        //iPhone
        if([[BrowserUtils shareInstance] isLandscape]) {
            //横屏, 3个cell
            float width = (kScreenWidth-margin*4)/3.0 - 1.0;
            size = CGSizeMake(width, width);
        } else {
            //竖屏, 2个cell
            float width = (kScreenWidth-margin*3)/2.0 - 1.0;
            size = CGSizeMake(width, width);
        }
    }
    
    return size;
}

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section
{
    return self.model.count;
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath
{
    TabTrayCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:NSStringFromClass(TabTrayCell.class) forIndexPath:indexPath];
    TabModel* item = self.model[indexPath.item];
    [cell updateWithModel:item];
        
    @weakify(self)
    [cell setDidRemoveTabBlock:^(TabModel *model){
        @strongify(self)
        BOOL openNewTab = [self.tabManager removeTabWithModel:model allTabModels:self.model];
        
        if(openNewTab) {
            //更新标签数量
            [self.tabManager.browser.bottomToolbar updateTabCount:1];
            [self.tabManager.browser.topToolbar updateTabCount:1];
            
            //更新选中的下标
            self.selectedIndex = 0;
            
            //打开新首页
            [self dismissViewControllerAnimated:YES completion:nil];
        } else {
            //更新标签数量
            int allTabsCount = [BrowserUtils shareInstance].allTabsCount;
            allTabsCount--;
            if(allTabsCount <= 0) {
                allTabsCount = 1;
            }
            [self.tabManager.browser.bottomToolbar updateTabCount:allTabsCount];
            [self.tabManager.browser.topToolbar updateTabCount:allTabsCount];

            //这段代码会导致崩溃
            //Thread 1: "Invalid batch updates detected: the number of sections and/or items returned by the data source before and/or after performing the batch updates are inconsistent with the updates.\nData source before updates = { 1 section with item counts: [29] }\nData source after updates = { 1 section with item counts: [28] }
//            @weakify(self)
//            [self.collectionView performBatchUpdates:^{
//                @strongify(self)
//                //友盟统计,快速滑动删除导致的崩溃
//                if(indexPath.item < self.model.count) {
//                    [self.model removeObject:item];
//                    [self.collectionView deleteItemsAtIndexPaths:@[indexPath]];
//                }
//            } completion:^(BOOL finished) {
//                //加了这句会引起刷新闪动一下, 因此去掉这句
//                //但是去掉之后,需要实现reloadSections的功能,例如更新选中状态
//                //需要重新遍历找到对应的新的indexPath
//
//                //解决collectionView relodata闪烁的问题 https://www.jianshu.com/p/7fe6d58160dc
//                [UIView performWithoutAnimation:^{
//                    [self.collectionView reloadData];
//                }];
//            }];
            
            //!!!千万不能使用block捕获的变量!
            int currentIndex = 0;
            for(int i=0;i<self.model.count;i++) {
                TabModel* obj = self.model[i];
                currentIndex = i;
                if(obj == model) {
                    [self.model removeObject:model];
                    //这句引起的崩溃太多了
                    //这里千万不能采用通过block绑定的model，否则如果拿到之前的model就有问题了
                    [self.collectionView deleteItemsAtIndexPaths:@[indexPath]];
                    break;
                }
            }
            
            //解决collectionView relodata闪烁的问题 https://www.jianshu.com/p/7fe6d58160dc
            [UIView performWithoutAnimation:^{
                [self.collectionView reloadData];
            }];
            
            //更新order
            NSMutableArray* items = [NSMutableArray array];
            for(int i=0;i<self.model.count;i++) {
                TabModel* item = self.model[i];
                item.ppOrder = i;
                
                //更新selectedIndex
                if(item.isSelected) {
                    self.selectedIndex = i;
                }
                
                [items addObject:item];
            }
            DatabaseUnit* unit = [DatabaseUnit updateAllTabsOrder:items];
            DB_EXEC(unit);
        }
    }];
    
    return cell;
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath
{
    self.selectedIndex = (int)indexPath.item;
    
    TabModel* tabModel = self.model[indexPath.item];
    Tab* tab = [self.tabManager tabForTabModel:tabModel];
    
    //v2.6.3，提前隐藏顶部搜索栏
    if ([tabModel isCurrentTabAtHome]) {
        AppDelegate *appDelegate = (AppDelegate*) [UIApplication sharedApplication].delegate;
        BrowserViewController* browser = appDelegate.browser;
        
        [browser.topToolbar dismiss:NO];
    }
    
    if(!tab) {
        [self.tabManager restoreTab:tabModel];
    } else {
        [self.tabManager selectTab:tab];
    }

    [self dismissViewControllerAnimated:YES completion:nil];
}

#pragma mark -- 新建标签页卡片
- (UICollectionViewCell *)createNewTabCellWithIndexPath:(NSIndexPath *)indexPath
{
    UICollectionViewCell *cell = [self.collectionView dequeueReusableCellWithReuseIdentifier:@"NewTabCell" forIndexPath:indexPath];
    cell.backgroundColor = UIColor.clearColor;
    
    // 清除已有子视图
    for (UIView *subview in cell.contentView.subviews) {
        [subview removeFromSuperview];
    }
    
    // 创建新建标签页卡片
    UIView *cardView = [[UIView alloc] init];
    cardView.backgroundColor = UIColor.clearColor;
    cardView.layer.cornerRadius = 12;
    cardView.layer.borderWidth = 2;
    cardView.layer.borderColor = [UIColor colorWithHexString:@"#e5e7eb"].CGColor;
    [cell.contentView addSubview:cardView];
    
    [cardView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(cell.contentView);
    }];
    
    // 添加+图标
    UIView *iconContainer = [[UIView alloc] init];
    iconContainer.backgroundColor = [UIColor colorWithHexString:@"#4285F4"];
    iconContainer.layer.cornerRadius = 25;
    [cardView addSubview:iconContainer];
    
    [iconContainer mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(cardView);
        make.centerY.equalTo(cardView).offset(-15);
        make.size.mas_equalTo(CGSizeMake(50, 50));
    }];
    
    UIImageView *plusIcon = [[UIImageView alloc] init];
    UIImage *plusImage = [UIImage systemImageNamed:@"plus"];
    if (!plusImage) {
        plusImage = [UIImage imageNamed:@"plus_icon"];
    }
    plusIcon.image = [plusImage imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
    plusIcon.tintColor = UIColor.whiteColor;
    [iconContainer addSubview:plusIcon];
    
    [plusIcon mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(iconContainer);
        make.size.mas_equalTo(CGSizeMake(25, 25));
    }];
    
    // 添加文本标签
    UILabel *newTabLabel = [[UILabel alloc] init];
    newTabLabel.text = @"新建标签页";
    newTabLabel.textColor = [UIColor colorWithHexString:@"#333333"];
    newTabLabel.font = [UIFont systemFontOfSize:14 weight:UIFontWeightMedium];
    newTabLabel.textAlignment = NSTextAlignmentCenter;
    [cardView addSubview:newTabLabel];
    
    [newTabLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(cardView);
        make.top.equalTo(iconContainer.mas_bottom).offset(10);
        make.width.equalTo(cardView);
    }];
    
    // 添加点击手势
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(addNewTab)];
    [cell addGestureRecognizer:tap];
    
    return cell;
}

#pragma mark - UIScrollViewDelegate

- (void)scrollViewWillBeginDragging:(UIScrollView *)scrollView
{
    [self.searchTextField resignFirstResponder];
    [self.view endEditing:YES];
}

#pragma mark -- UITextFieldDelegate
- (BOOL)textFieldShouldReturn:(UITextField *)textField
{
    [textField resignFirstResponder];
    return YES;
}

- (BOOL)textFieldShouldClear:(UITextField *)textField
{
    [self loadingData:nil];
    return YES;
}

#pragma mark - 懒加载
- (UICollectionView *)collectionView
{
    if(!_collectionView) {
        UICollectionViewFlowLayout *layout = [[UICollectionViewFlowLayout alloc] init];
        layout.scrollDirection = UICollectionViewScrollDirectionVertical;
        
        float margin = [self cellMargin];
        layout.minimumLineSpacing = margin;
        layout.minimumInteritemSpacing = margin;
        
        float topMargin = iPadValue(10, 8);
        layout.sectionInset = UIEdgeInsetsMake(topMargin, margin, topMargin, margin);
        
        _collectionView = [[UICollectionView alloc] initWithFrame:CGRectZero collectionViewLayout:layout];
        _collectionView.showsHorizontalScrollIndicator = NO;
        _collectionView.backgroundColor = [UIColor clearColor];
        [_collectionView registerClass:[TabTrayCell class] forCellWithReuseIdentifier:NSStringFromClass(TabTrayCell.class)];
        _collectionView.delegate = self;
        _collectionView.dataSource = self;
        
        _collectionView.showsVerticalScrollIndicator = NO;
        _collectionView.showsHorizontalScrollIndicator = NO;
                
//        _collectionView.dragDelegate = self;
//        _collectionView.dropDelegate = self;
//        _collectionView.dragInteractionEnabled = YES;
    }
    
    return _collectionView;
}

- (NSMutableArray *)model
{
    if(!_model) {
        _model = [NSMutableArray array];
    }
    
    return _model;
}

- (UIView *)searchBarView
{
    if (!_searchBarView) {
        _searchBarView = [UIView new];
        _searchBarView.backgroundColor = [UIColor whiteColor];
    }
    return _searchBarView;
}

- (CustomTextField *)searchTextField
{
    if(!_searchTextField) {
        _searchTextField = [[CustomTextField alloc] init];
        _searchTextField.font = [UIFont systemFontOfSize:14];
        _searchTextField.layer.cornerRadius = iPadValue(10, 8);
        _searchTextField.layer.masksToBounds = YES;
        
        _searchTextField.leftViewMode = UITextFieldViewModeAlways;
        _searchTextField.textColor = [UIColor colorWithHexString:@"#333333"];
        _searchTextField.returnKeyType = UIReturnKeySearch;
        //2.6.8 阿拉伯语布局适配
        _searchTextField.textAlignment = NSTextAlignmentLeft;
        
        [_searchTextField setBackgroundColor:[UIColor colorWithHexString:@"#f2f2f7"]];
        [_searchTextField updatePlaceHolder:NSLocalizedString(@"tab.search.placeholder", nil) color:[UIColor colorWithHexString:@"#999999"]];
        [_searchTextField setClearButtonMode:UITextFieldViewModeAlways];

        [_searchTextField setDelegate:self];

        // 创建搜索图标
        CGRect rect = CGRectMake(0, 0, 36, 36);
        UIView *leftView = [[UIView alloc] initWithFrame:rect];
        UIImageView *searchIcon = [[UIImageView alloc] initWithFrame:CGRectMake(10, 8, 20, 20)];
        searchIcon.image = [UIImage imageNamed:@"navi_search_icon"];
        searchIcon.tintColor = [UIColor colorWithHexString:@"#999999"];
        searchIcon.contentMode = UIViewContentModeScaleAspectFit;
        [leftView addSubview:searchIcon];
        
        _searchTextField.leftView = leftView;
        _searchTextField.leftViewRect = rect;
    }
    
    return _searchTextField;
}

- (UIStackView *)toolbarStackView
{
    if(!_toolbarStackView) {
        _toolbarStackView = [[UIStackView alloc]initWithArrangedSubviews:@[
            self.settingsBtn,
            self.closeAllBtn,
            self.newTabBtn,
            self.backBtn,
        ]];
        
        _toolbarStackView.backgroundColor = UIColor.whiteColor;
        _toolbarStackView.axis = UILayoutConstraintAxisHorizontal;
        _toolbarStackView.distribution = UIStackViewDistributionFillEqually;
    }
    
    return _toolbarStackView;
}

- (CustomTitleAndImageView *)backBtn
{
    if(!_backBtn) {
        @weakify(self)
        _backBtn = [[CustomTitleAndImageView alloc]initWithLayout:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            @strongify(self)
//            [self createButtonWithImageName:@"arrow.left" title:@"返回" imageView:imageView titleLabel:titleLabel];
            [self createButtonWithImageName:@"tabtray_return_icon" title:NSLocalizedString(@"tab.return", nil) imageView:imageView titleLabel:titleLabel];
        }];
    }
    
    return _backBtn;
}

- (CustomTitleAndImageView *)newTabBtn
{
    if(!_newTabBtn) {
        @weakify(self)
        _newTabBtn = [[CustomTitleAndImageView alloc]initWithLayout:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            @strongify(self)
//            [self createButtonWithImageName:@"plus" title:@"新建" imageView:imageView titleLabel:titleLabel];
            [self createButtonWithImageName:@"tabtray_add_icon" title:NSLocalizedString(@"tab.addTab", nil) imageView:imageView titleLabel:titleLabel];
        }];
    }
    
    return _newTabBtn;
}

- (CustomTitleAndImageView *)closeAllBtn
{
    if(!_closeAllBtn) {
        @weakify(self)
        _closeAllBtn = [[CustomTitleAndImageView alloc]initWithLayout:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            @strongify(self)
            [self createButtonWithImageName:@"tab_close_icon" title:NSLocalizedString(@"tab.closeAll", nil) imageView:imageView titleLabel:titleLabel];
        }];
    }
    
    return _closeAllBtn;
}

- (CustomTitleAndImageView *)settingsBtn
{
    if(!_settingsBtn) {
        @weakify(self)
        _settingsBtn = [[CustomTitleAndImageView alloc]initWithLayout:^(CustomTitleAndImageView * _Nonnull view, UIImageView * _Nonnull imageView, UILabel * _Nonnull titleLabel) {
            @strongify(self)
            [self createButtonWithImageName:@"tab_setting_icon" title:NSLocalizedString(@"preference.setting", nil) imageView:imageView titleLabel:titleLabel];
        }];
    }
    
    return _settingsBtn;
}

- (void)createButtonWithImageName:(NSString*)imageName
                             title:(NSString*)title
                         imageView:(UIImageView*)imageView
                        titleLabel:(UILabel*)titleLabel
{
    // 创建底部工具栏按钮，符合tabs.html的样式
    float iconSize = iPadValue(30, 28);
    float spacing = iPadValue(5, 3);
    float fontsize = iPadValue(18, 12);
    float topOffset = iPadValue(15, 8);
        
    // 图标设置
    [imageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.mas_offset(0);
        make.size.mas_equalTo(iconSize);
        make.top.mas_offset(topOffset);
    }];
    
    UIImage* image = [UIImage systemImageNamed:imageName];
    if (!image) {
        image = [UIImage imageNamed:imageName];
    }
    image = [image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
    imageView.image = image;
    imageView.contentMode = UIViewContentModeScaleAspectFit;
    
    imageView.tintColor = [UIColor colorWithHexString:@"#333333"];
    
    // 标签设置
    [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.mas_offset(0);
        make.left.mas_greaterThanOrEqualTo(0);
        make.right.mas_lessThanOrEqualTo(0);
        make.top.equalTo(imageView.mas_bottom).offset(spacing);
    }];
    
    titleLabel.text = title;
    titleLabel.textColor = [UIColor colorWithHexString:@"#333333"];
    titleLabel.font = [UIFont systemFontOfSize:fontsize];
}

#pragma mark -- UIViewControllerAnimatedTransitioning
- (NSTimeInterval)transitionDuration:(nullable id <UIViewControllerContextTransitioning>)transitionContext
{
    return 0.25;
}

- (void)animateTransition:(id <UIViewControllerContextTransitioning>)transitionContext
{
    if(self.bShow) {
        [self animatePresentation:transitionContext];
    } else {
        [self animateDismissal:transitionContext];
    }
}

#pragma mark -- UIViewControllerTransitioningDelegate
- (nullable id <UIViewControllerAnimatedTransitioning>)animationControllerForPresentedController:(UIViewController *)presented presentingController:(UIViewController *)presenting sourceController:(UIViewController *)source
{
    self.bShow = YES;
    return self;
}

- (nullable id <UIViewControllerAnimatedTransitioning>)animationControllerForDismissedController:(UIViewController *)dismissed
{
    self.bShow = NO;
    return self;
}

- (void)animatePresentation:(id<UIViewControllerContextTransitioning>)context
{
    UINavigationController* containerController = [context viewControllerForKey:UITransitionContextFromViewControllerKey];
    BrowserViewController* bvc = (BrowserViewController*)containerController.topViewController;
    
    UINavigationController* destinationController = [context viewControllerForKey:UITransitionContextToViewControllerKey];
    CGRect finalFrame = [context finalFrameForViewController:destinationController];
    UIView* toView = [context viewForKey:UITransitionContextToViewKey];
    toView.frame = finalFrame;
    
    CGRect webViewFrame = self.tabManager.selectedTab.webView.frame;
    CGRect bvcFrame = [self.tabManager.selectedTab.webView convertRect:webViewFrame toView:bvc.view];
    
    UIImageView* tabSnapshot = [UIImageView new];

//    [[ScreenshotHelper shareInstance] ss_setImageWithTabId:self.tabManager.selectedTab.model.tabId
//                                                 imageView:tabSnapshot
//                                                      asyc:NO];
    @weakify(self)
    [[ScreenshotHelper shareInstance] asyncOnQueue:^{
        @strongify(self)
        NSURL* keyUrl = [NSURL URLWithString:[ScreenshotHelper keyForId:self.tabManager.selectedTab.model.tabId]];
        [tabSnapshot sd_setImageWithURL:keyUrl];
    }];

    tabSnapshot.layer.masksToBounds = YES;
    tabSnapshot.contentMode = UIViewContentModeScaleAspectFill;
    tabSnapshot.frame = bvcFrame;
    
    dispatch_async(dispatch_get_main_queue(), ^{
        UIImageView* bvcSnapshot = [UIImageView new];
        bvcSnapshot.layer.masksToBounds = YES;
        bvcSnapshot.contentMode = UIViewContentModeScaleAspectFill;
        bvcSnapshot.image = [bvc.view screenshot];
        bvcSnapshot.frame = bvcFrame;
        
        UIView* backgroundView = [UIView new];
        backgroundView.backgroundColor = [UIColor colorWithWhite:0 alpha:0.3];
        backgroundView.frame = finalFrame; //finalFrame = (origin = (x = 0, y = 0), size = (width = 428, height = 926))
        backgroundView.alpha = 1;
        
        [context.containerView addSubview:toView];
        [context.containerView addSubview:backgroundView];
        [context.containerView addSubview:bvcSnapshot];
        [context.containerView addSubview:tabSnapshot];
        
        [toView setNeedsLayout];
        [toView layoutIfNeeded];
        
        [self.collectionView reloadData];
        [self.collectionView layoutIfNeeded];
        
        //这里需要考虑数据库还没加载完数据的情况
        //因此拿到数据后再present是比较保险的做法
        NSIndexPath* indexPath = [NSIndexPath indexPathForItem:self.selectedIndex inSection:0];
        [self.collectionView scrollToItemAtIndexPath:indexPath atScrollPosition:UICollectionViewScrollPositionCenteredVertically animated:NO];
        [self.collectionView layoutIfNeeded];
        
        TabTrayCell* tabTrayCell = (TabTrayCell*)[self.collectionView cellForItemAtIndexPath:indexPath];
        CGRect cellFrame = [self.collectionView convertRect:tabTrayCell.frame toView:toView];
        tabTrayCell.hidden = YES;
        
        self.collectionView.transform = CGAffineTransformMakeScale(1.2, 1.2);
        self.collectionView.alpha = 0.5;
        UIViewPropertyAnimator* animator = [[UIViewPropertyAnimator alloc]initWithDuration:0.4 dampingRatio:0.825 animations:^{
            self.collectionView.transform = CGAffineTransformIdentity;
            self.collectionView.alpha = 1.0;
            
            tabSnapshot.frame = cellFrame;
            tabSnapshot.layer.cornerRadius = 6;
            bvcSnapshot.frame = cellFrame;
            bvcSnapshot.layer.cornerRadius = 6;
            
            backgroundView.alpha = 0;
        }];
        
        [animator addCompletion:^(UIViewAnimatingPosition finalPosition) {
            tabTrayCell.hidden = NO;
            
            [backgroundView removeFromSuperview];
            [tabSnapshot removeFromSuperview];
            [bvcSnapshot removeFromSuperview];
            [context completeTransition:YES];
        }];
        
        [animator startAnimation];
    });
}

- (void)animateDismissal:(id<UIViewControllerContextTransitioning>)context
{
    UINavigationController* containerController = [context viewControllerForKey:UITransitionContextToViewControllerKey];
    UIView* toView = [context viewForKey:UITransitionContextToViewKey];
    
    CGRect finalFrame = [context finalFrameForViewController:containerController];
    toView.frame = finalFrame;
    
    dispatch_async(dispatch_get_main_queue(), ^{
        UIView* toVCSnapshot = [toView snapshotViewAfterScreenUpdates:YES];
        toVCSnapshot.layer.masksToBounds = YES;
        toVCSnapshot.layer.cornerRadius = 6;
        
        UIView* backgroundView = [UIView new];
        backgroundView.backgroundColor = [UIColor colorWithWhite:0 alpha:0.3];
        backgroundView.frame = finalFrame;
        backgroundView.alpha = 0;
        
        [context.containerView addSubview:toView];
        [context.containerView addSubview:backgroundView];
        [context.containerView addSubview:toVCSnapshot];
        
        [toView setNeedsLayout];
        [toView layoutIfNeeded];
        toView.hidden = YES;
        
        TabTrayCell* tabTrayCell;
        CGRect cellFrame;
        
        UICollectionView* cv = self.collectionView;
        [cv reloadData];
        NSIndexPath* indexPath = [NSIndexPath indexPathForItem:self.selectedIndex inSection:0];
        [cv scrollToItemAtIndexPath:indexPath atScrollPosition:UICollectionViewScrollPositionCenteredVertically animated:NO];
        [cv layoutIfNeeded];
        
        tabTrayCell = (TabTrayCell*)[cv cellForItemAtIndexPath:indexPath];
        cellFrame = [cv convertRect:tabTrayCell.frame toView:self.view];
        toVCSnapshot.frame = cellFrame;
        tabTrayCell.hidden = YES;
        
        UIViewPropertyAnimator* animator = [[UIViewPropertyAnimator alloc]initWithDuration:0.4 dampingRatio:1.0 animations:^{
            cv.transform = CGAffineTransformMakeScale(1.2, 1.2);
            cv.alpha = 0.5;
            
            toVCSnapshot.frame = finalFrame;
            toVCSnapshot.layer.cornerRadius = 0;
            backgroundView.alpha = 1;
        }];
        
        [animator addCompletion:^(UIViewAnimatingPosition finalPosition) {
            tabTrayCell.hidden = NO;
            toView.hidden = NO;
            [self.view removeFromSuperview];
            [backgroundView removeFromSuperview];
            [toVCSnapshot removeFromSuperview];
            [context completeTransition:YES];
        }];
        
        [animator startAnimation];
    });
}


@end
