//
//  TabTrayCell.m
//  PandaBrowser
//
//  Created by qing<PERSON> on 2022/3/14.
//

#import "TabTrayCell.h"

#import "MaizyHeader.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"

#import "UIView+Helper.h"
#import "UIColor+Helper.h"

#import "ScreenshotHelper.h"
#import "UIImageView+WebCache.h"

#import "SwipeAnimator.h"

#import "ThemeProtocol.h"
#import "PreferenceManager.h"
#import "BrowserUtils.h"

#import "PPEnums.h"

@interface TabTrayCell () <SwipeAnimatorDelegate>

@property (nonatomic, strong) UIView* gradientBar;
@property (nonatomic, strong) UILabel* titleLabel;
@property (nonatomic, strong) UIImageView *favicon;

@property (nonatomic, strong) UIButton* closeBtn;
@property (nonatomic, strong) UIImageView* closeImageView;

@property (nonatomic, strong) UIImageView *screenshotView;

@property (nonatomic, strong) TabModel* model;

@property (nonatomic, strong) UIView* shadowView;

@property (nonatomic, strong) UIView* backView;

@property (nonatomic, strong) SwipeAnimator* animator;

@end

@implementation TabTrayCell

- (id)initWithFrame:(CGRect)frame
{
    if (self = [super initWithFrame:frame]) {
        [self addSubviews];
        [self defineLayout];
        [self setupObserver];
    
        self.backgroundColor = UIColor.clearColor;
        
        SwipeCloseTabTrayType swipeCloseTabTrayType = [[PreferenceManager shareInstance].items.swipeCloseTabTrayType intValue];
        if(swipeCloseTabTrayType != SwipeCloseTabTrayTypeNoEffect) {
            //如果不是无效果
            self.animator = [[SwipeAnimator alloc] initWithAnimatingView:self];
            self.animator.delegate = self;
        }
    }
    return self;
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.gradientBar.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        self.backView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        self.titleLabel.textColor = UIColor.whiteColor;
        
        self.favicon.tintColor = UIColor.whiteColor;
        self.closeImageView.tintColor = UIColor.whiteColor;
    } else {
        self.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.gradientBar.backgroundColor = UIColor.whiteColor;
        self.backView.backgroundColor = UIColor.whiteColor;
        self.titleLabel.textColor = [UIColor colorWithHexString:@"#333333"];
        
        self.favicon.tintColor = [UIColor colorWithHexString:@"#333333"];
        self.closeImageView.tintColor = [UIColor colorWithHexString:@"#333333"];
    }
    
    if(self.model.isSelected) {
        self.backView.layer.borderColor = [UIColor colorWithHexString:@"#5DB5FC"].CGColor;
        self.backView.layer.borderWidth = 5;
        
        self.shadowView.layer.shadowColor = UIColor.clearColor.CGColor;
    } else {
        self.backView.layer.borderWidth = 0;
        
        if(isDarkTheme) {
            //暗黑模式不显示阴影
            self.shadowView.layer.shadowColor = UIColor.clearColor.CGColor;
        } else {
            self.shadowView.layer.shadowColor = UIColor.lightGrayColor.CGColor;
        }
    }
}

#pragma mark -- SwipeAnimatorDelegate
- (void)swipeAnimator:(SwipeAnimator *)swipeAnimator willExit:(UIView *)viewWillExitContainerBounds
{
    if(self.didRemoveTabBlock) {
        self.didRemoveTabBlock(self.model);
    }
}

- (void)updateWithModel:(TabModel*)tabModel
{
    self.model = tabModel;

    self.titleLabel.text = tabModel.title;
    
    NSURL* keyUrl = [NSURL URLWithString:[ScreenshotHelper keyForId:tabModel.tabId]];
    [self.screenshotView sd_setImageWithURL:keyUrl placeholderImage:nil options:SDWebImageRetryFailed];
    
    [self.animator reset];
    
    [self applyTheme];
}

- (void)setupObserver
{
    @weakify(self)
    [[self.closeBtn rac_signalForControlEvents:UIControlEventTouchUpInside]
     subscribeNext:^(id x) {
        @strongify(self)
        if(self.didRemoveTabBlock) {
            self.didRemoveTabBlock(self.model);
        }
    }];
    
    
}

- (void)addSubviews
{
    [self.contentView addSubview:self.shadowView];
    [self.shadowView addSubview:self.backView];
    
    [self.backView addSubview:self.screenshotView];
    [self.backView addSubview:self.gradientBar];
    [self.backView addSubview:self.favicon];
    [self.backView addSubview:self.titleLabel];
    [self.backView addSubview:self.closeBtn];
    [self.backView addSubview:self.closeImageView];
}

- (void)defineLayout
{
    [self.shadowView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.contentView);
    }];
    
    [self.backView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.contentView);
    }];
    
    float textHeight = iPadValue(50, 30);
    [self.gradientBar mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.leading.trailing.equalTo(self.contentView);
        make.height.mas_equalTo(textHeight);
    }];
    
    float size = iPadValue(25, 20);
    float leftOffset = iPadValue(15, 6);
    [self.favicon mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).offset(leftOffset);
        make.top.mas_equalTo((textHeight-size)/2.0);
        make.size.mas_equalTo(size);
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.favicon.mas_trailing).offset(leftOffset);
        make.trailing.equalTo(self.closeBtn.mas_leading).offset(-6);
        make.centerY.equalTo(self.favicon);
    }];
    
    float buttonSize = iPadValue(50, 44);
    [self.closeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(buttonSize);
        make.right.top.equalTo(self.contentView);
    }];
    
    [self.closeImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_offset(-leftOffset);
        make.centerY.equalTo(self.titleLabel);
        make.size.mas_equalTo(size);
    }];
    
    [self.screenshotView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(0);
        make.leading.bottom.trailing.equalTo(self.contentView);
    }];
}

- (UIView *)gradientBar
{
    if(!_gradientBar) {
        _gradientBar = [UIView new];
        _gradientBar.backgroundColor = UIColor.whiteColor;
    }
    
    return _gradientBar;
}

- (UILabel *)titleLabel
{
    if(!_titleLabel) {
        float font = iPadValue(20, 15);
        _titleLabel = [UIView createLabelWithTitle:@""
                                              textColor:[UIColor colorWithHexString:@"#333333"]
                                                bgColor:UIColor.clearColor
                                               fontSize:font
                                          textAlignment:NSTextAlignmentLeft
                                                  bBold:NO];
    }
    
    return _titleLabel;
}

- (UIButton *)closeBtn
{
    if(!_closeBtn) {
        _closeBtn = [UIButton new];
        [_closeBtn setImage:[UIImage imageNamed:@""] forState:UIControlStateNormal];
    }
    
    return _closeBtn;
}

- (UIImageView *)favicon
{
    if(!_favicon) {
        _favicon = [UIImageView new];
        _favicon.layer.cornerRadius = 2;
        _favicon.layer.masksToBounds = YES;
        
        NSString* imageName = iPadString(@"ipad_tabtray_favicon_icon", @"tabtray_favicon_icon");
        UIImage* image = [UIImage imageNamed:imageName];
        image = [image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        _favicon.image = image;
    }
    
    return _favicon;
}

- (UIImageView *)closeImageView
{
    if(!_closeImageView) {
        _closeImageView = [UIImageView new];
        
        NSString* imageName = iPadString(@"ipad_tabtray_close_icon", @"tabtray_close_icon");
        UIImage* image = [UIImage imageNamed:imageName];
        image = [image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        _closeImageView.image = image;
    }
    
    return _closeImageView;
}

- (UIImageView *)screenshotView
{
    if(!_screenshotView) {
        _screenshotView = [UIImageView new];
        _screenshotView.contentMode = UIViewContentModeScaleAspectFill;
        _screenshotView.clipsToBounds = YES;
    }
    
    return _screenshotView;
}

- (UIView *)shadowView
{
    if(!_shadowView) {
        _shadowView = [UIView new];
        
        _shadowView.layer.shadowColor = UIColor.lightGrayColor.CGColor;
        _shadowView.layer.shadowOpacity = 0.8;
        _shadowView.layer.shadowOffset = CGSizeZero;
        _shadowView.layer.shadowRadius = 6;
        _shadowView.layer.masksToBounds = NO;
        _shadowView.backgroundColor = UIColor.clearColor;
//        _shadowView.backgroundColor = UIColor.whiteColor;
        
        _shadowView.layer.cornerRadius = 10;
    }
    
    return _shadowView;
}

- (UIView *)backView
{
    if(!_backView) {
        _backView = [UIView new];
        
        _backView.layer.masksToBounds = YES;
        _backView.layer.cornerRadius = 10;
    }
    
    return _backView;
}

@end
