//
//  SwipeAnimator.m
//  PPBrowser
//
//  Created by qingbin on 2022/8/10.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "SwipeAnimator.h"

#import "MaizyHeader.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"

#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "UIView+FrameHelper.h"

#import "ScreenshotHelper.h"
#import "UIImageView+WebCache.h"

#import "PPEnums.h"

@interface SwipeAnimator() <UIGestureRecognizerDelegate>

@property (nonatomic, assign) float totalRotationInDegrees;
@property (nonatomic, assign) float deleteThreshold;
@property (nonatomic, assign) float totalScale;
@property (nonatomic, assign) float totalAlpha;
@property (nonatomic, assign) float minExitVelocity;
@property (nonatomic, assign) float recenterAnimationDuration;

@property (nonatomic, weak) UIView *animatingView;

@property (nonatomic, strong) UIPanGestureRecognizer *pan;

@property (nonatomic, assign) CGPoint prevOffset;

@end

@implementation SwipeAnimator

- (instancetype)initWithAnimatingView:(UIView *)animatingView
{
    self = [super init];
    if(self) {
        self.totalRotationInDegrees = 10;
        self.deleteThreshold = 80;
        self.totalScale = 0.9;
        self.totalAlpha = 0;
        self.minExitVelocity = 800;
        self.recenterAnimationDuration = 0.15;
        self.animatingView = animatingView;
        
        [self setupObservers];
    }
    
    return self;
}

- (void)setupObservers
{
    self.pan = [[UIPanGestureRecognizer alloc]initWithTarget:self action:@selector(didPan:)];
    [self.animatingView addGestureRecognizer:self.pan];
    self.pan.delegate = self;
}

#pragma mark -- UIGestureRecognizerDelegate
- (void)didPan:(UIPanGestureRecognizer *)recognizer
{
    CGPoint translation = [recognizer translationInView:self.animatingView];

    SwipeCloseTabTrayType swipeCloseTabTrayType = [[PreferenceManager shareInstance].items.swipeCloseTabTrayType intValue];
    if (swipeCloseTabTrayType == SwipeCloseTabTrayTypeNoEffect) {
        //无效果则不处理
        return;
    }
    
    switch (recognizer.state) {
        case UIGestureRecognizerStateBegan: {
            self.prevOffset = [self containerCenter];
        }
            break;
            
        case UIGestureRecognizerStateChanged: {
            self.animatingView.transform = [self transformForTranslation:translation.x];
            self.animatingView.alpha = [self alphaForDistanceFromCenter:fabs(translation.x)];
            self.prevOffset = CGPointMake(translation.x, 0);
        }
        break;
        
        case UIGestureRecognizerStateCancelled: {
            [self animateBackToCenter];
        }
            break;
            
        case UIGestureRecognizerStateEnded: {
            CGPoint velocity = [recognizer velocityInView:self.animatingView];
            // Bounce back if the velocity is too low or if we have not reached the threshold yet
            float speed = MAX(fabs(velocity.x), self.minExitVelocity);
            if(speed < self.minExitVelocity || fabs(self.prevOffset.x) < self.deleteThreshold) {
                [self animateBackToCenter];
            } else {
                [self animateAwayWithVelocity:velocity speed:speed];
            }
        }
            break;
            
        default:{
            [self animateBackToCenter];
        }
            break;
    }
}

- (BOOL)gestureRecognizerShouldBegin:(UIGestureRecognizer *)gestureRecognizer
{
    if(![gestureRecognizer isMemberOfClass:UIPanGestureRecognizer.class]) {
        return NO;
    }
    
    UIView* cellView = gestureRecognizer.view;
    CGPoint translation = [((UIPanGestureRecognizer*)gestureRecognizer) translationInView:cellView.superview];
    return fabs(translation.x) > fabs(translation.y);
}

- (void)close:(BOOL)right
{
    float direction;
    if(right) {
        direction = -1;
    } else {
        direction = 1;
    }
    
    [self animateAwayWithVelocity:CGPointMake(-direction * self.minExitVelocity, 0) speed:direction * self.minExitVelocity];
}

- (void)closeWithoutGesture
{
    [self close:NO];
}

- (CGPoint)containerCenter
{
    CGPoint center;
    center = CGPointMake(self.animatingView.width/2.0, self.animatingView.height/2.0);
    
    return center;
}

- (void)animateBackToCenter
{
    [UIView animateWithDuration:self.recenterAnimationDuration animations:^{
        self.animatingView.transform = CGAffineTransformIdentity;
        self.animatingView.alpha = 1;
    }];
}

//防止cell复用
- (void)reset
{
    self.animatingView.transform = CGAffineTransformIdentity;
    self.animatingView.alpha = 1;
}

- (void)animateAwayWithVelocity:(CGPoint)velocity speed:(float)speed
{
    UIView* animatingView = self.animatingView;
    float translation = velocity.x >= 0 ? animatingView.width : -animatingView.width;
    float timeStep = fabsf(translation) / speed;
    
    if(self.delegate && [self.delegate respondsToSelector:@selector(swipeAnimator:willExit:)]) {
        [self.delegate swipeAnimator:self willExit:animatingView];
    }
    
    [UIView animateWithDuration:timeStep animations:^{
        self.animatingView.transform = [self transformForTranslation:translation];
        self.animatingView.alpha = [self alphaForDistanceFromCenter:fabsf(translation)];
    } completion:^(BOOL finished) {
        if(finished) {
            self.animatingView.alpha = 0;
        }
    }];
}

- (CGAffineTransform)transformForTranslation:(float)translation
{
    //无效果不会走到这一步
    
    SwipeCloseTabTrayType swipeCloseTabTrayType = [[PreferenceManager shareInstance].items.swipeCloseTabTrayType intValue];
    if(swipeCloseTabTrayType == SwipeCloseTabTrayTypeHorizontal) {
        //水平滑动关闭
        CGAffineTransform translateTransform = CGAffineTransformTranslate(CGAffineTransformIdentity, translation, 0);
        return translateTransform;
    } else {
        //默认，扇行滑动关闭
        float swipeWidth = self.animatingView.width;
        float totalRotationInRadians = (self.totalRotationInDegrees*1.0) / 180.0 * M_PI;
    
        // Determine rotation / scaling amounts by the distance to the edge
        float rotation = (translation / swipeWidth) * totalRotationInRadians;
        float scale = 1 - (fabsf(translation) / swipeWidth) * (1 - self.totalScale);
    
        CGAffineTransform rotationTransform = CGAffineTransformMakeRotation(rotation);
        CGAffineTransform scaleTransform = CGAffineTransformMakeScale(scale, scale);
        CGAffineTransform translateTransform = CGAffineTransformTranslate(CGAffineTransformIdentity, translation, 0);
    
        return CGAffineTransformConcat(CGAffineTransformConcat(rotationTransform, scaleTransform), translateTransform);
    }
}

- (float)alphaForDistanceFromCenter:(float)distance
{
    float swipeWidth = self.animatingView.width;
    return 1 - (distance / swipeWidth) * (1 - self.totalAlpha);
}

@end
