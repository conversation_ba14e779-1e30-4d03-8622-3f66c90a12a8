//
//  SwipeAnimator.h
//  PPBrowser
//
//  Created by qingbin on 2022/8/10.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
@class SwipeAnimator;

@protocol SwipeAnimatorDelegate <NSObject>

- (void)swipeAnimator:(SwipeAnimator *)swipeAnimator willExit:(UIView *)viewWillExitContainerBounds;

@end

@interface SwipeAnimator : NSObject

@property (nonatomic, weak) id<SwipeAnimatorDelegate> delegate;

- (instancetype)initWithAnimatingView:(UIView *)animatingView;

//防止cell复用
- (void)reset;

@end

