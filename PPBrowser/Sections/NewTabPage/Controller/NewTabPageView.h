//
//  NewTabPageView.h
//  PandaBrowser
//
//  Created by qing<PERSON> on 2022/3/4.
//

#import "BaseViewController.h"

#import "WallpaperModel.h"
#import "ThemeProtocol.h"
#import "PreferenceManager.h"

#import "BaseAbstractView.h"

@class Tab;

@protocol NewTabPageDelegate <NSObject>
@end

@interface NewTabPageView : UIView<ThemeProtocol>

- (void)updateWithTab:(Tab*)tab;

- (void)updateWithWallpaper:(WallpaperModel *)model;

//屏幕旋转
- (void)updateForTransition;

//设置壁纸页,隐藏所有没必要的控件
- (void)hideControlsInSetWallpaper;

@property(nonatomic,weak) id<NewTabPageDelegate> delegate;

@end

