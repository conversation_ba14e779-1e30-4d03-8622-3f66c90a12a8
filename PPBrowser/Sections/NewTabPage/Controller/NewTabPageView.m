//
//  NewTabPageView.m
//  PandaBrowser
//
//  Created by qingbin on 2022/3/4.
//

#import "NewTabPageView.h"

#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"

#import "MaizyHeader.h"
#import "CustomTextField.h"
#import "Tab.h"

#import "TopToolbar.h"
#import "BottomToolbar.h"
#import "NSObject+Helper.h"

#import "PopupBottomView.h"
#import "PopupBottomViewTitleHandler.h"

#import "SearchManager.h"

#import "SearchSuggestionController.h"
#import "BaseNavigationController.h"

#import "CustomTagView.h"
#import "PPNotifications.h"

#import "SDImageCache.h"
#import "SDWebImageManager.h"

#import "BrowserViewController.h"

#import "WallPaperManager.h"
#import "BrowserUtils.h"
#import "CustomTagManager.h"
#import "UIView+FrameHelper.h"
#import "SearchViewController.h"
#import "TopToolbarForiPad.h"
#import "BottomToolbar.h"

#import "ScanViewController.h"
#import "DatabaseUnit+CustomTag.h"

#import "CustomTitleAndImageView.h"
#import "PaymentManager.h"

#import "VIPController.h"
#import "HomeVipView.h"
#import "TrialManager.h"

@interface NewTabPageView ()<UITextFieldDelegate,SearchSuggestionControllerDelegate,ScanDelegate>

//输入框+会员提示
@property (nonatomic, strong) UIStackView *stackView;
//输入框
@property (nonatomic, strong) CustomTextField *textField;
@property (nonatomic, strong) UIImageView* leftLogo;
@property (nonatomic, strong) UIImageView* scanLogo;
//会员提示
@property (nonatomic, strong) HomeVipView *vipView;
//快捷方式
@property (nonatomic, strong) CustomTagView* tagView;
//壁纸
@property (nonatomic, strong) UIImageView* wallpaper;
//
@property (nonatomic, strong) WallpaperModel *model;

@property (nonatomic, weak) Tab *tab;

@end

@implementation NewTabPageView

- (instancetype)init
{
    self = [super init];
    if(self) {
        [self addSubviews];
        [self defineLayout];

        [self setupObservers];
        
        //更新壁纸
        [self _updateWallpaper];
    }
    
    return self;
}

- (void)layoutSubviews
{
    [super layoutSubviews];
    
    //这里重复执行一次,看能不能解决ipad mini导致的位置错乱
    //扫码适配
    //主要是为了适配iPhone横屏启动, 导致宽高错乱的问题
    self.textField.rightViewRect = [self rectForTextFiledRightView];
}

- (void)dealloc
{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    //只有没有设置壁纸的情况下才能使用暗黑模式
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.scanLogo.tintColor = UIColor.whiteColor;
    } else {
        self.backgroundColor = UIColor.whiteColor;
        self.scanLogo.tintColor = [UIColor colorWithHexString:@"#1a1a1a"];
    }
    
    [self.tagView applyTheme];
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self _updateWallpaper];
}

- (void)updateWithTab:(Tab*)tab
{
    self.tab = tab;
}

#pragma mark -- 更新壁纸
- (void)updateWithWallpaper:(WallpaperModel *)model
{
    self.model = model;
    
    //只有没有设置壁纸的情况下才能使用暗黑模式
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    
    if([model.wp_lightColor boolValue]) {
        //浅色图标
        self.textField.layer.borderColor = [UIColor colorWithHexString:@"#ffffff"].CGColor;
        self.scanLogo.tintColor = UIColor.whiteColor;
    } else {
        //深色图标
        if(![model.wp_hasWallpaper boolValue]) {
            //没有设置壁纸
            if(isDarkTheme) {
                //暗黑模式
                self.textField.layer.borderColor = [UIColor colorWithHexString:@"#ffffff"].CGColor;
                self.scanLogo.tintColor = UIColor.whiteColor;
            } else {
                //非暗黑模式
                self.textField.layer.borderColor = [UIColor colorWithHexString:@"#333333"].CGColor;
                self.scanLogo.tintColor = UIColor.blackColor;
            }
        } else {
            //设置了壁纸
            self.textField.layer.borderColor = [UIColor colorWithHexString:@"#333333"].CGColor;
            self.scanLogo.tintColor = UIColor.blackColor;
        }
    }
    
    self.vipView.hidden = ![[TrialManager sharedManager] needShowHomeVip];
    
    [self.tagView reloadData];
    
    self.wallpaper.hidden = ![model.wp_hasWallpaper boolValue];
    if([model.wp_hasWallpaper boolValue]) {
        //有壁纸
        if(model.wallpaper) {
            //说明是正在设置壁纸
            self.wallpaper.image = model.wallpaper;
            self.leftLogo.hidden = NO;
            self.scanLogo.hidden = NO;
        } else {
            //说明是首页, 或者正在设置壁纸
            @weakify(self)
            [[WallPaperManager shareInstance] queryWallPaper:^(UIImage * _Nonnull image) {
                @strongify(self)
                if(image) {
                    //有缓存
                    dispatch_async(dispatch_get_main_queue(), ^{
                        self.wallpaper.image = image;
                        
                        //等壁纸显示出来再显示
                        self.leftLogo.hidden = NO;
                        self.scanLogo.hidden = NO;
                    });
                } else {
                    //没有缓存,有bug,重置壁纸
                    dispatch_async(dispatch_get_main_queue(), ^{
                        [model reset];
                        [PreferenceManager shareInstance].wallpaperModel = model;
                        [[PreferenceManager shareInstance] encode];
                        
                        [self updateWithWallpaper:model];
                    });
                }
            }];
        }
    } else {
        //没有壁纸
        self.leftLogo.hidden = NO;
        self.scanLogo.hidden = NO;
        
        //适配暗黑模式
        [self applyTheme];
    }
}

//设置壁纸页,隐藏所有没必要的控件
- (void)hideControlsInSetWallpaper
{
    self.tagView.hidden = YES;
}

- (void)setupObservers
{
    UITapGestureRecognizer* tap = [UITapGestureRecognizer new];
    [self addGestureRecognizer:tap];
    @weakify(self)
    [tap.rac_gestureSignal subscribeNext:^(id x) {
        @strongify(self)
        [self endEditing:YES];
    }];
    
    [self.tagView setChangeIconAction:^(NSString *url, CustomTagModel* model) {
        @strongify(self)
        //设置长按设置图标
        @weakify(self)
        self.tab.longPressSetIconAction = ^(NSString *iconUrl){
            @strongify(self)
            [self handleSetIconWithModel:model iconUrl:iconUrl];
        };
        
        [self.tab loadRequest:[NSURLRequest requestWithURL:[NSURL URLWithString:url]]];
        
        //长按设置图标的提示语
        [UIView showToast:NSLocalizedString(@"alert.set.icon.tip", nil) delay:3.0];
    }];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(_updateSearchEngineWithNotification:)
                                                 name:kUpdateSearchEngineNotification
                                               object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(_updateWallpaper)
                                                 name:kUpdateWallpaperNotification
                                               object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(darkThemeDidChangeNotification:)
                                                 name:kDarkThemeDidChangeNotification
                                               object:nil];

    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(_handleReloadVip)
                                                 name:kReloadVipNotification
                                               object:nil];
}

#pragma mark -- 打开Vip页面
- (void)jumpToVip
{
    VIPController* vc = [[VIPController alloc]init];
    BaseNavigationController* navc = [[BaseNavigationController alloc]initWithRootViewController:vc];
    navc.view.backgroundColor = UIColor.whiteColor;

    UIWindow* window = YBIBNormalWindow();
    UIViewController* rootViewController = window.rootViewController;
//    if([BrowserUtils isiPad]) {
//        //iPad
//        navc.modalPresentationStyle = UIModalPresentationFormSheet;
//        vc.preferredContentSize = CGSizeMake(kScreenWidth*0.8, kScreenWidth*0.8);
//        
//        [rootViewController presentViewController:navc animated:YES completion:nil];
//    } else {
//        //iPhone
//        [rootViewController presentViewController:navc animated:YES completion:nil];
//    }
    [rootViewController presentCustomToViewController:navc];
}

- (void)_handleReloadVip
{
    self.vipView.hidden = ![[TrialManager sharedManager] needShowHomeVip];
}

- (void)handleSetIconWithModel:(CustomTagModel *)model
                       iconUrl:(NSString *)iconUrl
{
    model.iconUrl = iconUrl;
    //写入数据库中
    DatabaseUnit* unit = [DatabaseUnit updateCustomTagWithId:model.uuid iconUrl:iconUrl customTag:model];
    @weakify(self)
    [unit setCompleteBlock:^(id result, BOOL success) {
        @strongify(self)
        //刷新
        [self.tagView reloadData];
    }];
    
    DB_EXEC(unit);
    
    //重置
    self.tab.longPressSetIconAction = nil;
}

#pragma mark -- 更新搜索引擎
- (void)_updateSearchEngineWithNotification:(NSNotification *)notification
{
    [self _updateSearchEngine];
}

- (void)_updateSearchEngine
{
    SearchModel* engine = [[SearchManager shareInstance] getCurrentSearchEngine];
    self.leftLogo.image = engine.image;
    
    if (engine.type == SearchEngineTypeMetaso) {
        //秘塔AI
        self.leftLogo.layer.cornerRadius = iPadValue(5, 5);
        self.leftLogo.layer.masksToBounds = YES;
        
        [self.leftLogo mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.center.mas_offset(0);
            make.size.mas_equalTo(22);
        }];
    } else {
        self.leftLogo.layer.cornerRadius = 0;
        
        [self.leftLogo mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.center.mas_offset(0);
            make.height.mas_equalTo(22);
        }];
    }
}

#pragma mark -- 更新壁纸
- (void)_updateWallpaper
{
    //友盟bug
    //YBIBNormalWindow()不一定是想要的window
    //从而会导致navc.viewControllers崩溃
    
    UIWindow* window = YBIBNormalWindow();
    UINavigationController* navc = (UINavigationController*)window.rootViewController;

    if([navc isKindOfClass:UINavigationController.class]) {
        for(UIViewController* vc in navc.viewControllers) {
            if([vc isMemberOfClass:BrowserViewController.class]) {
                WallpaperModel* model = [PreferenceManager shareInstance].wallpaperModel;
                [self updateWithWallpaper:model];
                break;
            }
        }
    }
}

#pragma mark -- 选择搜索引擎
- (void)showSearchEngineSelectedView
{
    UIWindow* window = YBIBNormalWindow();
    if([BrowserUtils isiPad]) {
        //iPad
        SearchViewController* vc = [SearchViewController new];
        CGSize size = [BrowserUtils shareInstance].transitionToSize;
        float width = size.width * (1-0.12*2);
        float height = size.height * (1-0.12*2);
        vc.preferredContentSize = CGSizeMake(width, height);
        
        BaseNavigationController* navc = [[BaseNavigationController alloc]initWithRootViewController:vc];
        navc.view.backgroundColor = UIColor.whiteColor;
        navc.modalPresentationStyle = UIModalPresentationFormSheet;
        
        [window.rootViewController presentViewController:navc animated:YES completion:nil];
    } else {
        //iPhone
        SearchViewController* vc = [SearchViewController new];
        
        BaseNavigationController* navc = [[BaseNavigationController alloc]initWithRootViewController:vc];
        navc.view.backgroundColor = UIColor.whiteColor;
        navc.modalPresentationStyle = UIModalPresentationFormSheet;
        
        [window.rootViewController presentViewController:navc animated:YES completion:nil];
    }
}

#pragma mark -- iPad旋转适配
- (void)updateForTransition
{
    //屏幕旋转
    [self defineLayout];
    [self.tagView updateForTransition];
    
    //扫码适配    
    // 根据设备类型和屏幕方向调整搜索框右侧视图的大小和位置
    self.textField.rightViewRect = [self rectForTextFiledRightView];
}

#pragma mark -- layout
- (void)addSubviews
{
    [self addSubview:self.wallpaper];
    [self addSubview:self.stackView];
    [self addSubview:self.tagView];
    
    //2.6.8 阿拉伯语布局适配
    [NSObject rtlLayoutSupportWithViews:@[self.wallpaper, self.stackView,
                                          self.textField, self.textField.leftView, self.textField.rightView,
                                          self.tagView]];
}

- (void)defineLayout
{
    if ([BrowserUtils isiPad]) {
        [self adapterDefineLayout];
    } else {
        [self standardDefineLayout];
    }
    
    [self.wallpaper mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
}

#pragma mark -- iPhone适配
- (void)standardDefineLayout
{
    float offset = [CustomTagManager leftOffsetForTextFiled];
    
    // 根据屏幕方向调整搜索框高度和位置
    BOOL isLandscape = [[BrowserUtils shareInstance] isLandscape];
    float height = [CustomTagManager heightForTextField];
    float padding = isLandscape ? 15 : 20; // 搜索框与标签的间距
    
    // 参考home.html，搜索框离顶部有一定距离，在安全区域下方
    [self.stackView mas_remakeConstraints:^(MASConstraintMaker *make) {
        // 从顶部安全区开始计算位置，类似于content-area的pt-14
        make.top.equalTo(self.mas_safeAreaLayoutGuideTop).offset(isLandscape ? 10 : 16);
        make.left.mas_offset(offset);
        make.right.mas_offset(-offset);
    }];
    
    [self.textField mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(height);
        make.width.equalTo(self.stackView);
    }];
    
    [self.vipView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(self.stackView);
        make.height.mas_equalTo(iPadValue(60, 40));
    }];
    
    float customTagViewLeftOffset = [CustomTagManager customTagViewLeftOffset];
    [self.tagView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.stackView.mas_bottom).offset(padding);
        make.left.mas_offset(customTagViewLeftOffset);
        make.right.mas_offset(-customTagViewLeftOffset);
        make.bottom.equalTo(self).offset(-[BottomToolbar toolbarHeight]);
    }];
}

#pragma mark -- iPad适配
- (void)adapterDefineLayout
{
    float offset = [CustomTagManager leftOffsetForTextFiled];
    // 根据iPad屏幕方向调整搜索框高度和位置
    BOOL isLandscape = [[BrowserUtils shareInstance] isLandscape];
    float height = [CustomTagManager heightForTextField];
    
    RACTuple* tupe = [CustomTagManager widthForTextField];
    BOOL isMax = [tupe.first boolValue];
    float calculatedWidth = [tupe.second floatValue];
    
    // 参考home_ipad.html，搜索框在顶部安全区下方，有更大的内部间距
    [self.stackView mas_remakeConstraints:^(MASConstraintMaker *make) {
        // 从顶部安全区开始计算位置，与home_ipad.html类似
//        make.top.equalTo(self.mas_safeAreaLayoutGuideTop).offset(isLandscape ? 20 : 24);
        make.top.equalTo(self.mas_safeAreaLayoutGuideTop).offset(24);
        
        // 限制搜索框最大宽度并居中
        if (isMax) {
            make.width.mas_equalTo(calculatedWidth);
            make.centerX.equalTo(self);
        } else {
            make.left.mas_offset(offset);
            make.right.mas_offset(-offset);
        }
    }];
    
    [self.textField mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(height);
        make.width.equalTo(self.stackView);
    }];
    
    [self.vipView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(self.stackView);
        make.height.mas_equalTo(iPadValue(60, 40));
    }];
    
    // iPad上搜索框与标签之间的间距更大
    float tagPadding = isLandscape ? 24 : 30;
    
    [self.tagView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.stackView.mas_bottom).offset(tagPadding);
        make.left.mas_offset([CustomTagManager leftOffsetForTextFiled]);
        make.right.mas_offset(-[CustomTagManager leftOffsetForTextFiled]);
        make.bottom.equalTo(self);
    }];
}

#pragma mark -- SearchSuggestionControllerDelegate
- (void)searchSuggestionControllerDidStartLoadRequest
{
    [self removeFromSuperview];
}

#pragma mark  -- ScanDelegate
- (void)scanDidStartLoadRequest
{
    [self removeFromSuperview];
}

#pragma mark -- UITextFieldDelegate
- (BOOL)textFieldShouldBeginEditing:(UITextField *)textField
{
    SearchSuggestionController* vc = [[SearchSuggestionController alloc] initWithTab:self.tab];
    vc.delegate = self;
    
    BaseNavigationController* navc = [[BaseNavigationController alloc]initWithRootViewController:vc];
    navc.modalPresentationStyle = UIModalPresentationFullScreen;
    
    UIWindow* window = YBIBNormalWindow();
    [window.rootViewController presentViewController:navc animated:YES completion:nil];
    
    return NO;
}

#pragma mark - getters

- (UIStackView *)stackView
{
    if(!_stackView) {
        _stackView = [[UIStackView alloc]initWithArrangedSubviews:@[
            self.textField,
            self.vipView,
        ]];
        _stackView.spacing = 16;
        _stackView.axis = UILayoutConstraintAxisVertical;
    }
    
    return _stackView;
}

- (HomeVipView *)vipView
{
    if (!_vipView) {
        _vipView = [HomeVipView new];
        _vipView.hidden = YES;
        
        @weakify(self)
        [_vipView setBannerClickBlock:^{
            @strongify(self)
            [self jumpToVip];
        }];
        
        [_vipView setCloseClickBlock:^{
            @strongify(self)
            //关闭按钮回调
            self.vipView.hidden = YES;
            //记录关闭时间
            [[TrialManager sharedManager] recordHomeVipCloseTime];
        }];
    }

    return _vipView;
}

- (CustomTextField *)textField
{
    if(!_textField) {
        _textField = [CustomTextField new];
        float height = [CustomTagManager heightForTextField];
        
        // 创建圆角效果，参考CSS中的border-radius
        _textField.layer.cornerRadius = [BrowserUtils isiPad] ? 12 : 10;
        _textField.layer.masksToBounds = YES;
        
        // 移除边框，使用半透明背景
        _textField.layer.borderWidth = 0;
        // 使用与html完全一致的背景色
        _textField.backgroundColor = [UIColor colorWithRed:142/255.0 green:142/255.0 blue:147/255.0 alpha:0.12];
        //2.6.8 阿拉伯语布局适配
        _textField.textAlignment = NSTextAlignmentLeft;
        
        _textField.returnKeyType = UIReturnKeySearch;
        _textField.delegate = self;
        
        // 设置占位文字，与HTML一致
        [_textField setPlaceholder:NSLocalizedString(@"textfiled.placeholder", nil)];
        // 字体大小根据设备类型调整，参考HTML中的text-base/text-lg
        _textField.font = [UIFont systemFontOfSize:iPadValue(17, 16)];
        
        //搜索引擎图标（左侧）
        {
            // 参考HTML中的图标布局
            float leftViewWidth = [CustomTagManager widthForTextFiledLeftRightView];
            CGRect leftViewRect = CGRectMake(5, 0, leftViewWidth, height);
            _textField.leftViewRect = leftViewRect;
            UIView* leftView = [[UIView alloc] initWithFrame:leftViewRect];
                        
            // 如果系统图标不可用，使用当前搜索引擎图标
            self.leftLogo = [UIImageView new];
            self.leftLogo.contentMode = UIViewContentModeScaleAspectFit;
        
            //先隐藏,等壁纸加载出来之后再显示
            self.leftLogo.hidden = YES;
            
            [leftView addSubview:self.leftLogo];
            [self _updateSearchEngine];
            
            _textField.leftView = leftView;
            
            UITapGestureRecognizer* tap = [UITapGestureRecognizer new];
            [leftView addGestureRecognizer:tap];
            @weakify(self)
            [tap.rac_gestureSignal subscribeNext:^(id x) {
                @strongify(self)
                [self showSearchEngineSelectedView];
            }];
            
            _textField.leftViewMode = UITextFieldViewModeAlways;
        }
        
        //右侧图标（类似于HTML中的麦克风图标）
        {
            // 右侧图标区域
            CGRect rightViewRect = [self rectForTextFiledRightView];
            _textField.rightViewRect = rightViewRect;
            
            UIView* rightView = [[UIView alloc] initWithFrame:rightViewRect];
            UIImageView* logo = [UIImageView new];
            logo.contentMode = UIViewContentModeScaleAspectFit;
            logo.layer.masksToBounds = YES;
            self.scanLogo = logo;
            
            //先隐藏,等壁纸加载出来之后再显示
            self.scanLogo.hidden = YES;
            
            [rightView addSubview:logo];
            [logo mas_makeConstraints:^(MASConstraintMaker *make) {
                make.center.equalTo(rightView);
                //只限制高度，宽度自适应
//                make.height.mas_equalTo([BrowserUtils isiPad] ? 22 : 20);
                make.height.mas_equalTo(22);
            }];
            
            // 使用扫描图标，但样式与HTML中的麦克风图标类似
            UIImage* image = [[UIImage imageNamed:@"scan_logo"] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
            logo.image = image;
            
            _textField.rightView = rightView;
            
            UITapGestureRecognizer* tap = [UITapGestureRecognizer new];
            [rightView addGestureRecognizer:tap];
            @weakify(self)
            [tap.rac_gestureSignal subscribeNext:^(id x) {
                @strongify(self)
                ScanViewController* vc = [[ScanViewController alloc]initWithTab:self.tab];
                vc.delegate = self;
                UIWindow* window = YBIBNormalWindow();
                UINavigationController* navc = (UINavigationController*)window.rootViewController;
                [navc pushViewController:vc animated:YES];
            }];
            
            _textField.rightViewMode = UITextFieldViewModeAlways;
        }
        
        // 设置内部间距，参考HTML中的px-3/px-4
        float horizontalPadding = [BrowserUtils isiPad] ? 15 : 12;
        _textField.layoutMargins = UIEdgeInsetsMake(0, horizontalPadding, 0, horizontalPadding);
    }
    
    return _textField;
}

- (CGRect)rectForTextFiledRightView
{
    // 右侧图标区域
    float height = [CustomTagManager heightForTextField];
//    float offset = [CustomTagManager leftOffsetForTextFiled];
    
    float rightViewWidth = [CustomTagManager widthForTextFiledLeftRightView];
    RACTuple* tupe = [CustomTagManager widthForTextField];
//    BOOL isMax = [tupe.first boolValue];
    float calculatedWidth = [tupe.second floatValue];
    
    float x = calculatedWidth-5-rightViewWidth;
    CGRect rightViewRect = CGRectMake(x, 0, rightViewWidth, height);
    
    return rightViewRect;
}

- (CustomTagView *)tagView
{
    if(!_tagView) {
        _tagView = [CustomTagView new];
    }
    
    return _tagView;
}

- (UIImageView *)wallpaper
{
    if(!_wallpaper) {
        _wallpaper = [UIImageView new];
        _wallpaper.contentMode = UIViewContentModeScaleAspectFill;
        _wallpaper.hidden = YES;
    }
    
    return _wallpaper;
}

@end
