//
//  WallpaperModel.h
//  PPBrowser
//
//  Created by qingbin on 2022/8/3.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "BaseModel.h"
#import "MJExtension.h"

#define kWallpaperKey @"com.qingbin.wallpaper"
//本地文件壁纸名称
#define kWallPaper @"wallpaper"

//壁纸相关
@interface WallpaperModel : BaseModel

//是否是浅色图标
@property (nonatomic, strong) NSNumber* wp_lightColor;
//是否显示logo
@property (nonatomic, strong) NSNumber* wp_showLogo;
//是否有壁纸
@property (nonatomic, strong) NSNumber* wp_hasWallpaper;
//壁纸, 不会进行序列化, 而是走sdwebimage的逻辑
@property (nonatomic, strong) UIImage *wallpaper;

- (void)reset;

@end
