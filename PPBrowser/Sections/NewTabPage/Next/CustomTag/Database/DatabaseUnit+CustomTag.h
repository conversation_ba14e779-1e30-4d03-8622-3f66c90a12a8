//
//  DatabaseUnit+CustomTag.h
//  PPBrowser
//
//  Created by qing<PERSON> on 2022/5/26.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "DatabaseUnit.h"
#import "CustomTagModel.h"

@interface DatabaseUnit (CustomTag)

// 查询所有节点
+ (DatabaseUnit*)queryAllCustomTags;

// 添加一组节点
+ (DatabaseUnit*)addCustomTagWithItem:(CustomTagModel*)item;

//删除一组节点
+ (DatabaseUnit*)removeCustomTagWithId:(NSString*)uuid;

//批量更新顺序
+ (DatabaseUnit*)updateAllTagsOrder:(NSArray*)allItems;

// 添加多个节点, 同时插入多个节点
+ (DatabaseUnit*)addCustomTagArrayWithItem:(NSArray<CustomTagModel*>*)items;

// 批量删除多个节点
//+ (DatabaseUnit*)removeCustomTagArrayWithItem:(NSArray<CustomTagModel*>*)items;

//更改名称
+ (DatabaseUnit*)updateCustomTagWithId:(NSString*)customTagId 
                                 title:(NSString*)title 
                             customTag:(CustomTagModel *)customTag;

//更改图标
+ (DatabaseUnit*)updateCustomTagWithId:(NSString*)customTagId 
                               iconUrl:(NSString*)iconUrl
                             customTag:(CustomTagModel *)customTag;

///
// CloudKit, 添加多个
+ (DatabaseUnit*)addCustomtagArray:(NSArray<CustomTagModel*>*)items;

// CloudKit, 批量更新多个
+ (DatabaseUnit*)updateCustomtagArray:(NSArray<CustomTagModel*>*)array;

// CloudKit, 批量删除多个
+ (DatabaseUnit*)removeCustomtagArray:(NSArray*)customTagIds;


@end


