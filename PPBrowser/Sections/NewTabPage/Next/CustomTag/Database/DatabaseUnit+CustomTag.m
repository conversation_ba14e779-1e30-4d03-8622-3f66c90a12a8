//
//  DatabaseUnit+CustomTag.m
//  PPBrowser
//
//  Created by qing<PERSON> on 2022/5/26.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "DatabaseUnit+CustomTag.h"
#import "ReactiveCocoa.h"

#import "FMDatabaseQueue.h"
#import "FMDatabase.h"

#import "PPEnums.h"
#import "CommonDataManager.h"

#import "SyncEngine.h"
#import "CloudKitHelper.h"

@implementation DatabaseUnit (CustomTag)

//查询所有节点
+ (DatabaseUnit*)queryAllCustomTags
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = [NSString stringWithFormat:@"SELECT * FROM t_hometags ORDER BY ppOrder ASC, ctime ASC"];
        FMResultSet* set = [db executeQuery:command];
        
        NSMutableArray *array = [[NSMutableArray alloc] init];
        while([set next]) {
            CustomTagModel* item = [[CustomTagModel alloc]initWithDictionary:[set resultDictionary] error:nil];
            if(item.type == 2) continue;
            
            [array addObject:item];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(array,YES);
            }
        });
    };
    
    return unit;
}

// 添加一组节点
+ (DatabaseUnit*)addCustomTagWithItem:(CustomTagModel*)item
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    //必须有id
    if(item.uuid.length == 0) {
        item.uuid = [[NSUUID UUID] UUIDString];
    }
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* time = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        item.ctime = time;
        item.updateTime = time;
        
        NSString* command = @"SELECT * FROM t_hometags WHERE uuid = ?";
        FMResultSet* set = [db executeQuery:command, item.uuid];
        
        BOOL result = YES;
        if([set next]) {
            //有记录，更新
            NSString* command = [NSString stringWithFormat:@"UPDATE t_hometags SET title=?, targetUrl=?, iconUrl=?, type=?, ppOrder=?, updateTime=? WHERE uuid=?"];
            result = [db executeUpdate:command, item.title?:@"", item.targetUrl?:@"", item.iconUrl?:@"", @(item.type), @(item.ppOrder), item.updateTime, item.uuid];
        } else {
            //没有记录，新增
            //uuid, title, targetUrl, type, ppOrder, ctime
            NSString* command = @"INSERT INTO t_hometags(uuid, title, targetUrl, iconUrl, type, ppOrder, updateTime, ctime) VALUES (?,?,?,?,?,?,?,?)";

            result = [db executeUpdate:command, item.uuid, item.title?:@"", item.targetUrl?:@"", item.iconUrl?:@"", @(item.type), @(item.ppOrder), time, time];
        }
        
        if(result) {
            //同步到CloudKit中
            [[SyncEngine shareInstance] syncRecordsToCloudKit:@[[item toCKRecord]] recordIDsToDelete:nil completion:nil];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
            
            [[CommonDataManager shareInstance] reloadCustomTagModels];
        });
    };
    
    return unit;
}

// 添加多个节点, 同时插入多个节点
+ (DatabaseUnit*)addCustomTagArrayWithItem:(NSArray<CustomTagModel*>*)items
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        //uuid, title, targetUrl, type, ppOrder, ctime
        NSString* command = @"INSERT INTO t_hometags(uuid, title, targetUrl, iconUrl, type, ppOrder, updateTime, ctime) VALUES (?,?,?,?,?,?,?,?)";
        
        NSString* time = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        BOOL result = YES;
        NSMutableArray* records = [NSMutableArray array];
        for(int i=0;i<items.count;i++) {
            CustomTagModel* item = items[i];
            item.ctime = time;
            item.updateTime = time;
            
            NSString* iconUrl = @"";
            if(item.iconUrl.length > 0) {
                iconUrl = item.iconUrl;
            }
            result = [db executeUpdate:command, item.uuid, item.title?:@"", item.targetUrl?:@"", iconUrl, @(item.type), @(item.ppOrder), time, time];
            
            if(result) {
                //同步到CloudKit中
                [records addObject:[item toCKRecord]];
            }
        }
        
        if(records.count>0) {
            //同步到CloudKit中
            [[SyncEngine shareInstance] syncRecordsToCloudKit:records recordIDsToDelete:nil completion:nil];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
            
            [[CommonDataManager shareInstance] reloadCustomTagModels];
        });
    };
    
    return unit;
}

//删除一组节点
+ (DatabaseUnit*)removeCustomTagWithId:(NSString*)uuid
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        
        NSString* command = [NSString stringWithFormat:@"DELETE FROM t_hometags WHERE uuid=?"];
        BOOL result = [db executeUpdate:command, uuid];

        if(result) {
            //同步到CloudKit中
            CKRecordID* recordID = [[CKRecordID alloc]initWithRecordName:uuid zoneID:[CloudKitHelper focusZoneID]];
            [[SyncEngine shareInstance] syncRecordsToCloudKit:nil recordIDsToDelete:@[recordID] completion:nil];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
            
            [[CommonDataManager shareInstance] reloadCustomTagModels];
        });
    };
    
    return unit;
}

//批量更新顺序
+ (DatabaseUnit*)updateAllTagsOrder:(NSArray*)allItems
{
    DatabaseUnit* unit = [DatabaseUnit new];
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
//        NSMutableString* command = [NSMutableString new];
//        [command appendString:@"UPDATE t_hometags SET ppOrder = CASE uuid \n"];
//        
//        for(int i=0;i<allItems.count;i++) {
//            CustomTagModel* item = allItems[i];
//            item.ppOrder = i;
//            NSString* str = [NSString stringWithFormat:@"WHEN '%@' THEN %@ \n",item.uuid,@(i)];
//            [command appendString:str];
//            
//            if(i==allItems.count-1) {
//                [command appendString:@"END;"];
//            }
//        }
//        
//        BOOL result = [db executeUpdate:command];
        
        BOOL result = YES;
        NSString* time = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        for(int i=0;i<allItems.count;i++) {
            CustomTagModel* item = allItems[i];
            item.ppOrder = i;
            item.updateTime = time;
            
            NSString* command = @"UPDATE t_hometags SET ppOrder = ?, updateTime = ? WHERE uuid = ?";
            result = [db executeUpdate:command, @(item.ppOrder), time, item.uuid] && result;
        }
        
        if(result) {
            //同步到CloudKit中
            NSMutableArray* records = [NSMutableArray array];
            for(CustomTagModel* item in allItems) {
                CKRecord* record = [item toDefaultCKRecord];
                record[@"ppOrder"] = @(item.ppOrder);
                record[@"updateTime"] = item.updateTime;
                
                [records addObject:record];
            }
            
            if(records.count > 0) {
                [[SyncEngine shareInstance] syncRecordsToCloudKit:records recordIDsToDelete:nil completion:nil];
            }
        }
        
        if(unit.completeBlock) {
            unit.completeBlock(nil, result);
        }
    };
    
    return unit;
}

//更改名称
+ (DatabaseUnit*)updateCustomTagWithId:(NSString*)customTagId title:(NSString*)title customTag:(CustomTagModel *)customTag
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* time = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        
        NSString* command = [NSString stringWithFormat:@"UPDATE t_hometags SET title=?, updateTime=? WHERE uuid=?"];
        BOOL result = [db executeUpdate:command, title, time, customTagId];
        
        if(result) {
            //同步到CloudKit中
            CKRecord* record = [customTag toDefaultCKRecord];
            record[@"title"] = customTag.title?:@"";
            record[@"updateTime"] = time;
            [[SyncEngine shareInstance] syncRecordsToCloudKit:@[record] recordIDsToDelete:nil completion:nil];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

//更改图标
+ (DatabaseUnit*)updateCustomTagWithId:(NSString*)customTagId
                               iconUrl:(NSString*)iconUrl
                             customTag:(CustomTagModel *)customTag
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* time = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        
        NSString* command = [NSString stringWithFormat:@"UPDATE t_hometags SET iconUrl=?, updateTime=? WHERE uuid=?"];
        BOOL result = [db executeUpdate:command, iconUrl, time, customTagId];
        
        if(result) {
            //同步到CloudKit中
            CKRecord* record = [customTag toDefaultCKRecord];
            record[@"iconUrl"] = customTag.iconUrl?:@"";
            record[@"updateTime"] = time;
            [[SyncEngine shareInstance] syncRecordsToCloudKit:@[record] recordIDsToDelete:nil completion:nil];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

#pragma mark -- CloudKit相关操作
// CloudKit, 添加多个
// CREATE TABLE IF NOT EXISTS t_hometags(uuid TEXT PRIMARY KEY, title TEXT, targetUrl TEXT, iconUrl TEXT, type INTEGER, ppOrder INTEGER, ctime TEXT)
+ (DatabaseUnit*)addCustomtagArray:(NSArray<CustomTagModel*>*)items
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = @"INSERT INTO t_hometags(uuid, title, targetUrl, iconUrl, type, ppOrder, updateTime, ctime) VALUES (?,?,?,?,?,?,?,?)";
        
        BOOL result = YES;
        for(int i=0;i<items.count;i++) {
            CustomTagModel* item = items[i];
            if(item.type == 2) {
                continue;
            }
            result = [db executeUpdate:command, item.uuid, item.title?:@"", item.targetUrl?:@"", item.iconUrl?:@"", @(item.type), @(item.ppOrder), item.updateTime?:@"1", item.ctime];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

// CloudKit, 批量更新多个
+ (DatabaseUnit*)updateCustomtagArray:(NSArray<CustomTagModel*>*)array
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        BOOL result = NO;
        for(CustomTagModel* item in array) {
            NSString* command = @"UPDATE t_hometags SET title=?, targetUrl=?, iconUrl=?, type=?, ppOrder=?, updateTime=?, ctime=? WHERE uuid=?;";
            if(item.type == 2) {
                continue;
            }
            result = [db executeUpdate:command, item.title?:@"", item.targetUrl?:@"", item.iconUrl?:@"", @(item.type), @(item.ppOrder), item.updateTime?:@"1", item.ctime, item.uuid];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

// CloudKit, 批量删除多个
+ (DatabaseUnit*)removeCustomtagArray:(NSArray*)customTagIds
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = [NSString stringWithFormat:@"DELETE FROM t_hometags WHERE uuid=?;"];

        BOOL result = YES;
        for(int i=0;i<customTagIds.count;i++) {
            NSString* customTagId = customTagIds[i];
            result = [db executeUpdate:command, customTagId];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

@end
