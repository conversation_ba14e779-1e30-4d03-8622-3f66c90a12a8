//
//  CustomTagFlowLayout.m
//  PPBrowser
//
//  Created by qingbin on 2022/5/26.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "CustomTagFlowLayout.h"

#import "Masonry.h"
#import "ReactiveCocoa.h"

#import "UIColor+Helper.h"
#import "NSObject+Helper.h"
#import "MaizyHeader.h"
#import "NewTabPageView.h"
#import "BottomToolbar.h"
#import "BrowserUtils.h"

#import "CustomTagManager.h"

@interface CustomTagFlowLayout ()

@property (nonatomic, assign) int columns;

@property (nonatomic, assign) int rows;

@property (nonatomic, assign) int pageSize;

@property (nonatomic, strong) NSMutableArray<UICollectionViewLayoutAttributes*> *cache;

@end

@implementation CustomTagFlowLayout

- (instancetype)init
{
    self = [super init];
    if(self) {
    }
    
    return self;
}

- (int)columns
{
    return [CustomTagManager columnCount];
}

- (int)rows
{
    return [[CustomTagManager shareInstance] rowCount];
}

- (int)pageSize
{
    return self.columns * self.rows;
}

#pragma override
- (CGSize)collectionViewContentSize
{
    float verticalCellSpacing = [CustomTagManager verticalCellSpacing];
    float cellContentHeight = [CustomTagManager cellContentHeight];
    
    float contentHeight = (self.rows-1)*verticalCellSpacing + self.rows*cellContentHeight;
    float width = [CustomTagManager customTagViewWidth];
    
    NSInteger numberOfSections = [self.collectionView numberOfSections];
    return CGSizeMake(width*numberOfSections, contentHeight);
}

#pragma override
- (void)prepareLayout
{
//    if(self.cache.count > 0) return;
    
    float width = self.collectionView.bounds.size.width;
//    float height = self.collectionView.bounds.size.height;
    
    NSInteger numberOfSections = [self.collectionView numberOfSections];
    float verticalCellSpacing = [CustomTagManager verticalCellSpacing];
    float horizontalCellSpacing = [CustomTagManager horizontalCellSpacing];
    float cellContentWidth = [CustomTagManager cellContentWidth];
    float cellContentHeight = [CustomTagManager cellContentHeight];
        
    for(int section=0;section<numberOfSections;section++) {
        NSInteger numberOfItems = [self.collectionView numberOfItemsInSection:section];
        for(int row=0;row<numberOfItems;row++) {
            int currentPage = section;
            float sectionOffsetX = width*currentPage;
            
            int columnIndex = row % self.columns;
            int rowIndex = row / self.columns;
            
            // create a frame for the item
            float x = columnIndex * horizontalCellSpacing + (cellContentWidth * columnIndex) + sectionOffsetX;
            float y = rowIndex * verticalCellSpacing + (cellContentHeight * rowIndex);
            
            NSIndexPath* indexPath = [NSIndexPath indexPathForRow:row inSection:section];
            CGRect itemRect = CGRectMake(x, y, cellContentWidth, cellContentHeight);
            UICollectionViewLayoutAttributes* attributes = [UICollectionViewLayoutAttributes layoutAttributesForCellWithIndexPath:indexPath];
            attributes.frame = itemRect;
            [self.cache addObject:attributes];
        }
    }
}

- (UICollectionViewLayoutAttributes *)layoutAttributesForItemAtIndexPath:(NSIndexPath *)indexPath
{
    NSInteger index = self.pageSize*indexPath.section + indexPath.row;
    
    if(self.didGetAllItem) {
        int all = self.didGetAllItem();
        if(index >= all) return self.cache.lastObject;
    }
    
    return self.cache[index];
}

- (NSArray<__kindof UICollectionViewLayoutAttributes *> *)layoutAttributesForElementsInRect:(CGRect)rect
{
    NSMutableArray* layoutAttributes = [NSMutableArray array];
    for(UICollectionViewLayoutAttributes* item in self.cache) {
        CGRect intersect = CGRectIntersection(rect, item.frame);
        if(!CGRectEqualToRect(intersect, CGRectZero)) {
            [layoutAttributes addObject:item];
        }
    }

    return layoutAttributes;
}

- (void)invalidateLayout
{
    [self.cache removeAllObjects];
    self.cache = nil;
    
    [super invalidateLayout];
}

- (NSMutableArray<UICollectionViewLayoutAttributes *> *)cache
{
    if(!_cache) {
        _cache = [NSMutableArray array];
    }
    
    return _cache;
}

@end
