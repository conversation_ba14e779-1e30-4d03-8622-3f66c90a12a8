//
//  CustomTagCell.m
//  PPBrowser
//
//  Created by qingbin on 2022/5/26.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "CustomTagCell.h"

#import "MaizyHeader.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "NSObject+Helper.h"
#import "NSString+Helper.h"

#import "CustomButton.h"
#import "UIImageView+WebCache.h"
#import "IconManager.h"
#import "CustomTagFlowLayout.h"

#import "UIImageView+WebCache.h"
#import "NSURL+Extension.h"

#import "WallpaperModel.h"
#import "PreferenceManager.h"
#import "PaddingNewLabel.h"

#import "CustomTagManager.h"

@interface CustomTagCell ()

@property (nonatomic, strong) UIImageView *logo;

@property (nonatomic, strong) PaddingNewLabel *logoLabel;

@property (nonatomic, strong) UIView *containerView;

@property (nonatomic, strong) UILabel *titleLabel;

@property (nonatomic, strong) CustomTagModel *model;

@property (nonatomic, strong) UIImageView *deleteLogo;
//没有图标时加载的loading
@property (nonatomic, strong) UIActivityIndicatorView* activityIndicator;

@end

@implementation CustomTagCell

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if(self) {
        [self addSubviews];
        [self defineLayout];
        [self setupObservers];
    }
    
    return self;
}

- (void)layoutSubviews
{
    [super layoutSubviews];
    
    //解决滑动过程中，图片偏移顶部显示不正确的问题
    //屏幕旋转适配
//    float systemContentHeight = [CustomTagCell systemContentHeight];
//    float cellContentHeight = [CustomTagFlowLayout cellContentHeight];
//    float topOffset = (cellContentHeight-systemContentHeight)/2.0 + 10;
#warning -- 待测试这个问题
    [self.containerView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.mas_offset([CustomTagManager topOffsetForCell]).priorityHigh();
    }];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.logo.backgroundColor = UIColor.clearColor;
    } else {
        self.logo.backgroundColor = UIColor.whiteColor;
    }
}

- (void)updateWithModel:(CustomTagModel*)model
{
    self.model = model;
    
    self.logoLabel.hidden = YES;
    self.logo.hidden = NO;
    
    [self.logo mas_updateConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(42);
    }];
    
    if(model.type == CustomTagTypeBookMark) {
        self.logo.image = [UIImage imageNamed:@"home_star_icon"];
    } else if(model.type == CustomTagTypeFile) {
        //文件
    } else if(model.type == CustomTagTypeGuideline) {
        //功能引导
        self.logo.image = [UIImage imageNamed:@"home_note_icon"];
    } else if(model.type == CustomTagTypeMetaso) {
        //秘塔AI
        self.logo.image = [UIImage imageNamed:@"mita"];
    }  else {
        if(model.iconUrl.length > 0 && self.model.isInvalidUrl==false) {
            //如果有logo
            // 获取默认的实例
            SDWebImageDownloader *downloader = [SDWebImageDownloader sharedDownloader];
            // 设置请求超时时间（以秒为单位）
            downloader.downloadTimeout = 5.0; // 设置为5秒，您可以根据需要进行调整
            
            [self.activityIndicator startAnimating];
            self.activityIndicator.hidden = NO;
            
            @weakify(self)
            [self.logo sd_setImageWithURL:[NSURL URLWithString:model.iconUrl] completed:^(UIImage *image, NSError *error, SDImageCacheType cacheType, NSURL *imageURL) {
                @strongify(self)
                if(!self) return;
                //复用问题，导致获取到错的icon
                if(![imageURL.absoluteString isEqualToString:self.model.iconUrl]) return;
                
                [self.activityIndicator stopAnimating];
                self.activityIndicator.hidden = YES;
                
                if(error || !image) {
                    //请求失败，那么下次则不再请求，但是不保存到数据库，有可能网络问题
//                    self.model.iconUrl = @"";
                    self.model.isInvalidUrl = YES;
                    
                    //如果获取失败
                    self.logo.hidden = YES;
                    self.logoLabel.backgroundColor = [self getLogoColorWithLetter:self.model.iconUrl];
                    
                    if(model.title.length > 0) {
                        NSString* logo = [model.title substringWithRange:NSMakeRange(0, 1)];
                        self.logoLabel.text = logo;
                    } else {
                        self.logoLabel.text = @"";
                    }
                    
                    self.logoLabel.hidden = NO;
                } else if(image) {
                    self.logo.image = image;
                }
            }];
        } else {
            //如果没有logo
            NSString* url = [[NSURL URLWithString:model.targetUrl] normalizedHost];
            if([url containsString:@"greasyfork.org"]) {
                //greasyfork
                self.logo.image = [UIImage imageNamed:@"greasyfork_logo"];
            } else if([url containsString:@"bilibili.com"]) {
                //bilibili
                self.logo.image = [UIImage imageNamed:@"bilibili_logo"];
            } else if([url containsString:@"xiaohongshu.com"]) {
                //小红书
                self.logo.image = [UIImage imageNamed:@"xiaohongshu_logo"];
            } else if([url containsString:@"reddit.com"]) {
                //Reddit
                self.logo.image = [UIImage imageNamed:@"reddit_logo"];
            } else if([url containsString:@"youtube.com"]) {
                //youtube
                self.logo.image = [UIImage imageNamed:@"youtube2_logo"];
            } else if([url containsString:@"google.com"]) {
                //google
                self.logo.image = [UIImage imageNamed:@"google_logo_2"];
                
                [self.logo mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.size.mas_equalTo(38);
                }];
            } else if([url containsString:@"baidu.com"]) {
                //百度
                self.logo.image = [UIImage imageNamed:@"baidu_logo"];
                
                [self.logo mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.size.mas_equalTo(38);
                }];
            }  else if([url containsString:@"bing.com"]) {
                //bing
                self.logo.image = [UIImage imageNamed:@"bing_logo"];
                
                [self.logo mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.size.mas_equalTo(38);
                }];
            }  else {
                //首字母显示当作logo
//                self.logoLabel.backgroundColor = [self getLogoColorWithLetter:url];
                self.logoLabel.backgroundColor = [UIColor colorWithURL:model.targetUrl];
                self.logo.hidden = YES;
                
                if(model.title.length > 0) {
                    NSString* logo = [model.title substringWithRange:NSMakeRange(0, 1)];
                    self.logoLabel.text = logo;
                } else {
                    self.logoLabel.text = @"";
                }
                
                self.logoLabel.hidden = NO;
            }
        }
    }
        
    self.titleLabel.text = model.title;
    
    //只有没有设置壁纸的情况下才能使用暗黑模式
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    BOOL wp_hasWallpaper = [[PreferenceManager shareInstance].wallpaperModel.wp_hasWallpaper boolValue];
    BOOL wp_lightColor = [[PreferenceManager shareInstance].wallpaperModel.wp_lightColor boolValue];
    
    if(!wp_hasWallpaper) {
        //没有设置过壁纸
        if(isDarkTheme) {
            self.titleLabel.textColor = UIColor.whiteColor;
            self.containerView.backgroundColor = [UIColor colorWithHexString:@"#2f353b"];
        } else {
            self.titleLabel.textColor = [UIColor colorWithHexString:@"#222222"];
            self.containerView.backgroundColor = UIColor.whiteColor;
        }
    } else {
        //设置过壁纸
        if(wp_lightColor) {
            //浅色图标
            self.titleLabel.textColor = UIColor.whiteColor;
        } else {
            //深色图标
            self.titleLabel.textColor = [UIColor colorWithHexString:@"#222222"];
        }
        
        self.containerView.backgroundColor = UIColor.whiteColor;
    }
    
    self.deleteLogo.hidden = !model.isEditting;
    
    if(wp_hasWallpaper) {
        self.containerView.layer.shadowColor = UIColor.clearColor.CGColor;
    } else {
        if(isDarkTheme) {
            self.containerView.layer.shadowColor = UIColor.clearColor.CGColor;
        } else {
            self.containerView.layer.shadowColor = [UIColor colorWithHexString:@"#dcdcdc"].CGColor;
        }
    }
    
    //屏幕旋转适配
//    float systemContentHeight = [CustomTagCell systemContentHeight];
//    float cellContentHeight = [CustomTagFlowLayout cellContentHeight];
//    float topOffset = (cellContentHeight-systemContentHeight)/2.0 + 10;
    
    [self.containerView mas_updateConstraints:^(MASConstraintMaker *make) {
        //往下移动10px,留出删除按钮位置
        make.top.mas_offset([CustomTagManager topOffsetForCell]).priorityHigh();
    }];
    
    [self applyTheme];
}

- (UIColor *)getLogoColorWithLetter:(NSString *)letter
{
    if(letter.length == 0) {
        //设置默认值
        UIColor* color = [UIColor colorWithHexString:@"#945cfc"];
        return color;
    }
    
    NSArray* options = @[@"#945cfc", @"#04d4ac", @"#fc6484",@"#1caffc", @"#5c44f5"];
    char* cString = (char*) [letter UTF8String];
    char ch = cString[0];
    
    NSString* color = options[ch%options.count];
    
    return [UIColor colorWithHexString:color];
}

- (void)setupObservers
{
    UITapGestureRecognizer* tap = [UITapGestureRecognizer new];
    [self.contentView addGestureRecognizer:tap];
    @weakify(self)
    [tap.rac_gestureSignal subscribeNext:^(id x) {
        @strongify(self)
        if(self.didTapAction) {
            self.didTapAction();
        }
    }];
    
    self.deleteLogo.userInteractionEnabled = YES;
    tap = [UITapGestureRecognizer new];
    [self.deleteLogo addGestureRecognizer:tap];
    [tap.rac_gestureSignal subscribeNext:^(id x) {
        @strongify(self)
        if(self.didDeleteAction) {
            self.didDeleteAction();
        }
    }];
}

#pragma mark -- layout
- (void)addSubviews
{
    [self.contentView addSubview:self.containerView];
    [self.containerView addSubview:self.logo];
    [self.containerView addSubview:self.logoLabel];
    
    [self.contentView addSubview:self.titleLabel];
    [self.contentView addSubview:self.deleteLogo];
    
    [self.contentView addSubview:self.activityIndicator];
}

//+ (float)systemContentHeight
//{
//    int font = [CustomTagCell titleLabelFontSize];
//    CGSize titleLabelSize = [NSString sizeWithText:@" " fontSize:font width:kScreenWidth];
//    
//    float spacing = [self spacing];
//    
//    float systemContentHeight = kSystemImageSize + spacing + titleLabelSize.height;
//    return systemContentHeight;
//}

//+ (float)spacing
//{
//    float spacing = 25;
//    if([BrowserUtils isiPad]) {
//        //logo和标题之间的间距,如果是iPad,那么调大一点
//        spacing = 35;
//    }
//    
//    return spacing;
//}

- (void)defineLayout
{
    [self.deleteLogo mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(21);
        make.centerX.equalTo(self.containerView.mas_right).offset(-0);
        make.centerY.equalTo(self.containerView.mas_top).offset(0);
    }];
    
    [self.logoLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.logo);
    }];
    
    [self.activityIndicator mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.logo);
    }];
    
    [self.logo mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.containerView);
        make.size.mas_equalTo(42);
    }];
    
//    float spacing = [CustomTagCell spacing];
//    float systemContentHeight = [CustomTagCell systemContentHeight];
//    float cellContentHeight = [CustomTagFlowLayout cellContentHeight];
//    float topOffset = (cellContentHeight-systemContentHeight)/2.0 + 10;

    [self.containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo([CustomTagManager imageSizeForCell]);
        //往下移动10px,留出删除按钮位置
        make.top.mas_offset([CustomTagManager topOffsetForCell]).priorityHigh();
        make.centerX.equalTo(self.contentView);
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.contentView);
        make.top.equalTo(self.logo.mas_bottom).offset([CustomTagManager spacingBetweenlogoAndLabelForCell]);
    }];
}

#pragma mark -- iPad适配
//+ (float)titleLabelFontSize
//{
//    float font = iPadValue(16, 12);
//    return font;
//}

- (UIView *)containerView
{
    if(!_containerView) {
        _containerView = [UIView new];
        _containerView.backgroundColor = [UIColor colorWithHexString:@"#ffffff"];//f1f2f3
        _containerView.layer.cornerRadius = 10;
        
        _containerView.layer.shadowColor = [UIColor colorWithHexString:@"#dcdcdc"].CGColor;
        _containerView.layer.shadowOpacity = 1;
        _containerView.layer.shadowOffset = CGSizeZero;
        _containerView.layer.shadowRadius = 3;
    }
    
    return _containerView;
}

- (UIImageView *)deleteLogo
{
    if(!_deleteLogo) {
        _deleteLogo = [UIImageView new];
        _deleteLogo.contentMode = UIViewContentModeScaleAspectFit;
        _deleteLogo.image = [UIImage imageNamed:@"home_delete_icon"];
        
        _deleteLogo.hidden = YES;
    }

    return _deleteLogo;
}

- (UIImageView *)logo
{
    if(!_logo) {
        _logo = [UIImageView new];
        _logo.layer.cornerRadius = 10;
        _logo.layer.masksToBounds = YES;
        
        _logo.contentMode = UIViewContentModeScaleAspectFit;
        _logo.backgroundColor = UIColor.whiteColor;
    }
    
    return _logo;
}

- (UILabel *)titleLabel
{
    if(!_titleLabel) {
        int font = [CustomTagManager titleLabelFontSizeForCell];
        _titleLabel = [UIView createLabelWithTitle:@""
                                              textColor:[UIColor colorWithHexString:@"#222222"]
                                                bgColor:UIColor.clearColor
                                               fontSize:font
                                          textAlignment:NSTextAlignmentCenter
                                                  bBold:NO];
    }
    
    return _titleLabel;
}

- (PaddingNewLabel *)logoLabel
{
    if(!_logoLabel) {
        _logoLabel = [PaddingNewLabel new];
        _logoLabel.textColor = [UIColor colorWithHexString:@"#ffffff"];
        _logoLabel.font = [UIFont boldSystemFontOfSize:27];
        _logoLabel.hidden = YES;
        
        _logoLabel.layer.cornerRadius = 10;
        _logoLabel.layer.masksToBounds = YES;
    }
    
    return _logoLabel;
}

- (UIActivityIndicatorView *)activityIndicator
{
    if(!_activityIndicator) {
        _activityIndicator = [[UIActivityIndicatorView alloc]initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleMedium];
        _activityIndicator.hidden = YES;
    }
    
    return _activityIndicator;
}

@end
