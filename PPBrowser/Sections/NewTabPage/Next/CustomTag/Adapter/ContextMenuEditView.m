//
//  ContextMenuEditView.m
//  PPBrowser
//
//  Created by qingbin on 2022/7/14.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "ContextMenuEditView.h"
#import "CustomTagCell.h"

#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"

#import "MaizyHeader.h"
#import "NSObject+Helper.h"
#import "UIImageView+WebCache.h"
#import "DatabaseUnit+CustomTag.h"

#import "PPNotifications.h"
#import "UICollectionViewLeftAlignedLayout.h"

#import "CustomTagManager.h"

@interface ContextMenuEditView()<UICollectionViewDelegate,UICollectionViewDataSource>

@property (nonatomic, strong) NSMutableArray* model;
@property (nonatomic, strong) UICollectionView *collectionView;

@property (nonatomic, strong) UIVisualEffectView *blurView;

@end

@implementation ContextMenuEditView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    
    if(self) {
        [self addSubviews];
        [self defineLayout];
        [self setupObservers];
        [self loadingData];
    }
    
    return self;
}

- (void)loadingData
{
    [self.model removeAllObjects];
    
    DatabaseUnit* unit = [DatabaseUnit queryAllCustomTags];
    @weakify(self)
    [unit setCompleteBlock:^(NSArray* result, BOOL success) {
        @strongify(self)
        if(!self) return;
        if(success) {
            [self.model addObjectsFromArray:result];
            
            for(CustomTagModel* item in self.model) {
                item.isEditting = YES;
            }
            
            [self.collectionView reloadData];
        }
    }];

    DB_EXEC(unit);
}

- (void)setupObservers
{
    @weakify(self)
    UITapGestureRecognizer* tap = [[UITapGestureRecognizer alloc]init];
    [self.collectionView addGestureRecognizer:tap];
    [tap.rac_gestureSignal subscribeNext:^(id x) {
        @strongify(self)
        self.collectionView.hidden = YES;
        [UIView animateWithDuration:0.25 animations:^{
            self.alpha = 0;
        } completion:^(BOOL finished) {
            [self removeFromSuperview];
        }];
    }];
    
    UILongPressGestureRecognizer* gesture = [[UILongPressGestureRecognizer alloc]init];
    gesture.minimumPressDuration = 0.2;
    [self.collectionView addGestureRecognizer:gesture];
    [[gesture rac_gestureSignal]
    subscribeNext:^(id x) {
        @strongify(self)
        [self longPressGestureAction:x];
    }];
    
    [tap requireGestureRecognizerToFail:gesture];
}

- (void)addSubviews
{
    [self addSubview:self.blurView];
    [self addSubview:self.collectionView];
}

- (void)defineLayout
{
    [self.blurView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
    
    [self.collectionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self);
        make.top.equalTo(self.mas_safeAreaLayoutGuideTop);
        make.bottom.equalTo(self.mas_safeAreaLayoutGuideBottom);
    }];
}

#pragma mark -- 长按拖动
- (void)longPressGestureAction:(UILongPressGestureRecognizer *)longGesture
{
    switch (longGesture.state) {
        case UIGestureRecognizerStateBegan:{
            // 通过手势获取点，通过点获取点击的indexPath， 移动该cell
            NSIndexPath *aIndexPath = [self.collectionView indexPathForItemAtPoint:[longGesture locationInView:self.collectionView]];
            [self.collectionView beginInteractiveMovementForItemAtIndexPath:aIndexPath];
        }
            break;
        case UIGestureRecognizerStateChanged:{
            [self.collectionView updateInteractiveMovementTargetPosition:[longGesture locationInView:self.collectionView]];
        }
            break;
        case UIGestureRecognizerStateEnded:{
            // 移动完成关闭cell移动
            [self.collectionView endInteractiveMovement];
            
            //更新order
            NSMutableArray* items = [NSMutableArray array];
            for(int i=0;i<self.model.count;i++) {
                CustomTagModel* item = self.model[i];
                item.ppOrder = i;
                            
                [items addObject:item];
            }
        
            DatabaseUnit* unit = [DatabaseUnit updateAllTagsOrder:items];
            [unit setCompleteBlock:^(id result, BOOL success) {
                if(success) {
                    //需要加一个async,否则容易崩溃
                    dispatch_async(dispatch_get_main_queue(), ^{
                        [[NSNotificationCenter defaultCenter] postNotificationName:kAddHomeCustomTagNotification object:nil];
                    });
                }
            }];
            DB_EXEC(unit);
        }
            break;
        default:
            [self.collectionView endInteractiveMovement];
            break;
    }
}

#pragma mark -- 拖动代理
- (BOOL)collectionView:(UICollectionView *)collectionView canMoveItemAtIndexPath:(NSIndexPath *)indexPath
{
    return YES;
}

- (void)collectionView:(UICollectionView *)collectionView moveItemAtIndexPath:(NSIndexPath *)sourceIndexPath toIndexPath:(NSIndexPath *)destinationIndexPath
{
    id objc = [self.model objectAtIndex:sourceIndexPath.item];
    [self.model removeObject:objc];
    [self.model insertObject:objc atIndex:destinationIndexPath.item];
}

#pragma mark -- UICollectionViewDelegate
- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath
{
    //需要同步CustomTagFlowLayout数据
    
//    int horizontalCellSpacing = 0;
////    int verticalCellSpacing  = 15; /*要同步到高度中*/
//    int width = kScreenWidth - 20 - 20 - 5 - 5;
//    int columns = 4;
//    int cellContentWidth = (width - (horizontalCellSpacing*(columns-1)))/(columns*1.0);
//
//    //适配iPad, 取iPhone13 pro max为最大屏幕尺寸
//    float maxWidth = MIN(428, kScreenWidth);
//
//    int columnCount = columns;
//    float height = maxWidth*1.0 / columnCount;
//    //往下移动10px,留出删除按钮位置
//    int cellContentHeight = height + 10;
    
    float cellContentWidth = [CustomTagManager cellContentWidth];
    float cellContentHeight = [CustomTagManager cellContentHeight];
    
    return CGSizeMake(cellContentWidth, cellContentHeight);
}

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section
{
    return self.model.count;
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath
{
    CustomTagCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:NSStringFromClass(CustomTagCell.class) forIndexPath:indexPath];
    
    CustomTagModel* item = self.model[indexPath.item];
    
    [cell updateWithModel:item];
        
    @weakify(self)
    [cell setDidDeleteAction:^{
        @strongify(self)
        DatabaseUnit* unit = [DatabaseUnit removeCustomTagWithId:item.uuid];
        [unit setCompleteBlock:^(id result, BOOL success) {
            if(success) {
                [[NSNotificationCenter defaultCenter] postNotificationName:kAddHomeCustomTagNotification object:nil];
            }
        }];
        DB_EXEC(unit);

        [self loadingData];
//        @weakify(self)
//        [self.collectionView performBatchUpdates:^{
//            @strongify(self)
//            [self.collectionView deleteItemsAtIndexPaths:@[indexPath]];
//        } completion:^(BOOL finished) {
//            [self.model removeObject:item];
//            [self.collectionView reloadData];
//        }];
    }];
    
    return cell;
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath
{

}

#pragma mark -- lazy init
- (UICollectionView *)collectionView
{
    if(!_collectionView) {
        UICollectionViewLeftAlignedLayout *layout = [[UICollectionViewLeftAlignedLayout alloc] init];
        layout.scrollDirection = UICollectionViewScrollDirectionVertical;
        layout.minimumLineSpacing = 10;
        layout.minimumInteritemSpacing = 0;
        
        float offset = [CustomTagManager leftOffsetForTextFiled] - 2;
        layout.sectionInset = UIEdgeInsetsMake(0, offset, 0, offset);
        
        _collectionView = [[UICollectionView alloc] initWithFrame:CGRectZero collectionViewLayout:layout];
        _collectionView.showsHorizontalScrollIndicator = NO;
        _collectionView.backgroundColor = [UIColor clearColor];
        [_collectionView registerClass:[CustomTagCell class] forCellWithReuseIdentifier:NSStringFromClass(CustomTagCell.class)];
        _collectionView.delegate = self;
        _collectionView.dataSource = self;
    }
    
    return _collectionView;
}

- (UIVisualEffectView *)blurView
{
    if(!_blurView) {
        UIBlurEffect* blurEffect = [UIBlurEffect effectWithStyle:UIBlurEffectStyleLight];
        UIVisualEffectView* blurView = [[UIVisualEffectView alloc]initWithEffect:blurEffect];
        _blurView = blurView;
    }
    
    return _blurView;
}

- (NSMutableArray *)model
{
    if(!_model) {
        _model = [NSMutableArray array];
    }
    
    return _model;
}

@end
