//
//  CustomTagView.m
//  PPBrowser
//
//  Created by qingbin on 2022/5/26.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "CustomTagView.h"

#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"

#import "MaizyHeader.h"

#import "NSObject+Helper.h"
#import "CustomTagFlowLayout.h"
#import "PPEnums.h"

#import "CustomTagCell.h"
#import "DatabaseUnit+CustomTag.h"
#import "DatabaseUnit+BookMark.h"
#import "PPNotifications.h"

#import "ContextMenuController.h"
#import "ContextMenuEditView.h"

#import "WallpaperModel.h"
#import "PreferenceManager.h"

#import "CustomTagFlowLayout.h"
#import "BrowserUtils.h"

#import "CustomTagManager.h"

#import "CustomWarningView.h"
#import "UICollectionViewLeftAlignedLayout.h"
#import "UIAlertController+SafePresentation.h"
#import "CommonDataManager.h"
#import "FavorEditController.h"
#import "BaseNavigationController.h"

@interface CustomTagView () <UICollectionViewDelegate, UICollectionViewDataSource>

@property (nonatomic, strong) UICollectionView *collectionView;

//@property (nonatomic, strong) UIPageControl *pageControl;

@property (nonatomic, strong) NSMutableArray* model;

@property (nonatomic, strong) UICollectionViewLeftAlignedLayout* layout;
//主要用来记录消失后的动画效果view，否则会崩溃
@property (nonatomic, strong) CustomTagCell* dismissCell;
//提示动画view
@property (nonatomic, strong) CustomWarningView* warningView;

@end

@implementation CustomTagView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    
    if(self) {
        self.backgroundColor = UIColor.clearColor;
        
        [self addSubviews];
        [self defineLayout];
        [self setupObservers];
        [self updateWithModel];
    }
    
    return self;
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    //只有没有设置壁纸的情况下才能使用暗黑模式
//    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
//    if(isDarkTheme) {
//        self.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
//    } else {
//        self.backgroundColor = UIColor.whiteColor;
//    }
}

- (void)reloadData
{
    //设置壁纸
//    BOOL wp_lightColor = [[PreferenceManager shareInstance].wallpaperModel.wp_lightColor boolValue];
//    BOOL wp_hasWallpaper = [[PreferenceManager shareInstance].wallpaperModel.wp_hasWallpaper boolValue];
//    
//    if(!wp_hasWallpaper) {
//        //没有设置壁纸, 默认方式(忽略深浅图标)
//        BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
//        if(isDarkTheme) {
//            //暗黑模式
//            self.pageControl.pageIndicatorTintColor = [UIColor colorWithHexString:@"#b0b3b5"];
//            self.pageControl.currentPageIndicatorTintColor = [UIColor colorWithHexString:@"#dfe1e1"];
//        } else {
//            self.pageControl.pageIndicatorTintColor = [UIColor colorWithHexString:@"#dfe1e1"];
//            self.pageControl.currentPageIndicatorTintColor = [UIColor colorWithHexString:@"#b0b3b5"];
//        }
//    } else {
//        if(wp_lightColor) {
//            //浅色图标
//            self.pageControl.pageIndicatorTintColor = [UIColor colorWithHexString:@"#b0b3b5"];
//            self.pageControl.currentPageIndicatorTintColor = [UIColor colorWithHexString:@"#dfe1e1"];
//        } else {
//            //深色图标(默认)
//            self.pageControl.pageIndicatorTintColor = [UIColor colorWithHexString:@"#dfe1e1"];
//            self.pageControl.currentPageIndicatorTintColor = [UIColor colorWithHexString:@"#b0b3b5"];
//        }
//    }

    [self.collectionView reloadData];
}

#pragma mark -- model
- (void)updateWithModel
{
    [self.model removeAllObjects];
    self.model = nil;
    //需要清除缓存,否则不更新约束
    [self.layout invalidateLayout];
    
    DatabaseUnit* unit = [DatabaseUnit queryAllCustomTags];
    @weakify(self)
    [unit setCompleteBlock:^(NSArray* result, BOOL success) {
        @strongify(self)
        if(!self) return;
        if(success) {            
            [self.model addObjectsFromArray:result];
            
            [self.collectionView reloadData];
            
//            [self updatePageControl];
        }
    }];

    DB_EXEC(unit);
}

#pragma mark -- 旋转适配
- (void)updateForTransition
{
    [self defineLayout];
    [self updateWithModel];
    
//    dispatch_async(dispatch_get_main_queue(), ^{
//        //需要更新偏移量,否则位置显示不对
//        int currentPage = (int)self.pageControl.currentPage;
//        self.collectionView.contentOffset = CGPointMake(currentPage*self.collectionView.frame.size.width, 0);
//    });
}

//- (void)updatePageControl
//{
//    int numberOfPages = (int)self.model.count/(int)self.pageSize;
//    if(self.model.count % self.pageSize != 0) {
//        numberOfPages += 1;
//    }
//    self.pageControl.numberOfPages = numberOfPages;
//}

- (void)setupObservers
{
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(_addHomeCustomTag:)
                                                 name:kAddHomeCustomTagNotification
                                               object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(updateWithModel)
                                                 name:kCloudKitDataDidChangeNotification
                                               object:nil];
    
//    @weakify(self)
//    [self.layout setDidGetAllItem:^int{
//        @strongify(self)
//        return (int)self.model.count;
//    }];
}

#pragma mark -- 长按编辑
- (void)longPressGestureAction:(UILongPressGestureRecognizer *)longGesture
{
    for(CustomTagModel* item in self.model) {
        item.isEditting = YES;
    }
    
    [self.collectionView reloadData];
}

- (BOOL)gestureRecognizerShouldBegin:(UIGestureRecognizer *)gestureRecognizer
{
    return YES;
}

#pragma mark -- 添加了首页标签通知
- (void)_addHomeCustomTag:(NSNotification *)notification
{
    //偷懒一波: 增加或者删除都会发这个通知
//    CustomTagModel* item = notification.object;
    [self updateWithModel];
}

#pragma mark -- iPhone
//+ (float)collectionViewHeight
//{
//    int rowCount = [CustomTagFlowLayout rowCount];
//    
//    float cellContentHeight = [CustomTagFlowLayout cellContentHeight];
//    float verticalCellSpacing = [CustomTagFlowLayout verticalCellSpacing];
//    
//    float height = 10/*删除按钮高度*/ + cellContentHeight*rowCount + (rowCount-1)*verticalCellSpacing;
//    return height;
//}

//+ (float)height
//{
//    float collectionViewHeight = [[CustomTagManager shareInstance] customTagContentViewHeight];
//    float pageControlheight = 25;
//    float buffer = 10;
//    float pageControlTopOffset = 25;
//    float height = collectionViewHeight + pageControlTopOffset + pageControlheight + buffer;
//    
//    return height;
//}

#pragma mark -- iPad


//- (CGPoint)contentOffset
//{
//    return self.collectionView.contentOffset;
//}
//
//- (void)updateContentOffset:(CGPoint)offset
//{
//    [self.collectionView setContentOffset:offset animated:NO];
//    [self.collectionView layoutIfNeeded];
//    
//    if(self.collectionView.frame.size.width > 0) {
//        int currentPage = offset.x/self.collectionView.frame.size.width;
//        self.pageControl.currentPage = currentPage;
//    } else {
//        self.pageControl.currentPage = 0;
//    }
//}

#pragma mark -- layout
- (void)addSubviews
{
//    self.backgroundColor = [UIColor colorWithHexString:@"#ffffff"];
    self.backgroundColor = UIColor.clearColor;
    
    [self addSubview:self.collectionView];
//    [self addSubview:self.pageControl];
}

- (void)defineLayout
{
    [self.collectionView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self).offset(0);
        make.left.equalTo(self);
        make.right.equalTo(self);
        make.bottom.equalTo(self);
//        make.height.mas_equalTo([[CustomTagManager shareInstance] customTagContentViewHeight]);
    }];
    
//    [self.pageControl mas_remakeConstraints:^(MASConstraintMaker *make) {
//        make.centerX.equalTo(self);
//        make.top.equalTo(self.collectionView.mas_bottom).offset(10);
//        make.height.mas_equalTo(20.0f);
//    }];
}

#pragma mark - getters

- (CustomWarningView *)warningView
{
    if(!_warningView) {
        _warningView = [CustomWarningView new];
    }
    
    return _warningView;
}

//- (UIPageControl *)pageControl
//{
//    if (!_pageControl) {
//        _pageControl = [[UIPageControl alloc] init];
//        _pageControl.currentPage = 0;
//        _pageControl.hidesForSinglePage = YES;
//    }
//    return _pageControl;
//}

- (NSMutableArray *)model
{
    if(!_model) {
        _model = [NSMutableArray array];
    }
    
    return _model;
}

- (UICollectionView *)collectionView
{
    if (!_collectionView) {
        
//        CustomTagFlowLayout *layout = [[CustomTagFlowLayout alloc] init];
//        self.layout = layout;
        
        UICollectionViewLeftAlignedLayout *layout = [UICollectionViewLeftAlignedLayout new];
        
        layout.scrollDirection = UICollectionViewScrollDirectionVertical;
        layout.sectionInset = UIEdgeInsetsZero;
        layout.minimumInteritemSpacing = 0;
        layout.minimumLineSpacing = 0;
        
        _collectionView = [[UICollectionView alloc] initWithFrame:CGRectZero collectionViewLayout:layout];
        _collectionView.backgroundColor = [UIColor clearColor];
        _collectionView.pagingEnabled = YES;
        [_collectionView registerClass:[CustomTagCell class] forCellWithReuseIdentifier:@"CustomTagCell"];
        _collectionView.delegate = self;
        _collectionView.dataSource = self;
        _collectionView.showsHorizontalScrollIndicator = NO;
        _collectionView.showsVerticalScrollIndicator = NO;
    }
    
    return _collectionView;
}

//- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView
//{
//    //计算当前页数
//    //参考CustomTagFlowLayout
//    
//    CGSize size = [BrowserUtils shareInstance].transitionToSize;
//    
//    float offset = [CustomTagManager customTagViewLeftOffset];
//    float currentPage = (scrollView.contentOffset.x + 5/*加一点偏移量,误差范围内*/) / (size.width - offset*2.0);
//    self.pageControl.currentPage = currentPage;
//}

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath
{
    //需要同步CustomTagFlowLayout数据
    
    float cellContentWidth = [CustomTagManager cellContentWidth];
    float cellContentHeight = [CustomTagManager cellContentHeight];
    
    cellContentWidth = floorf(cellContentWidth);
    cellContentHeight = floorf(cellContentHeight);
    
    return CGSizeMake(cellContentWidth, cellContentHeight);
}

- (NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView
{
//    int numberOfPages = (int)self.model.count/(int)self.pageSize;
//    if(self.model.count % self.pageSize != 0) {
//        numberOfPages += 1;
//    }
//    
//    return numberOfPages;
    
    return 1;
}

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section
{
//    NSInteger less = self.model.count - self.pageSize*section;
//    less = MIN(self.pageSize, less);
//    
//    return less;
    
    return self.model.count;
}

//- (int)pageSize
//{
//    int rows = [[CustomTagManager shareInstance] rowCount];
//    int columns = [CustomTagManager columnCount];
//    int pageSize = rows*columns;
//    
//    return pageSize;
//}
//
//- (CustomTagModel *)getModel:(NSInteger)section row:(NSInteger)row
//{
//    NSInteger index = self.pageSize*section + row;
//    if(index >= self.model.count) return nil;
//    
//    CustomTagModel* model = self.model[index];
//    return model;
//}

#pragma mark -- context menu
- (UIContextMenuConfiguration *)createContextMenuWithIndexPath:(NSIndexPath *)indexPath
{
    NSString* identifier = [NSString stringWithFormat:@"%ld-%ld",(long)indexPath.section, indexPath.row];
    
//    CustomTagModel* model = [self getModel:indexPath.section row:indexPath.row];
    CustomTagModel* model = self.model[indexPath.row];
    
    UIContextMenuConfiguration* configuration = [UIContextMenuConfiguration configurationWithIdentifier:identifier previewProvider:^UIViewController * _Nullable{
        ContextMenuController* vc = [[ContextMenuController alloc]init];
        vc.preferredContentSize = CGSizeMake(270, 70);
        
        [vc updateWithModel:model];
        
        return vc;
    } actionProvider:^UIMenu * _Nullable(NSArray<UIMenuElement *> * _Nonnull suggestedActions) {
        NSMutableArray* actions = [NSMutableArray array];
        
        UIAction* editAction = [UIAction actionWithTitle:NSLocalizedString(@"customtag.edit", nil) image:nil identifier:nil handler:^(__kindof UIAction * _Nonnull action) {
            [self handleEditContextMenu];
        }];
        [actions addObject:editAction];
        
        //内置类型没有更换图标功能
        if (![model isBuiltIn]) {
            //不是内置类型
            UIAction* editCoverAction = [UIAction actionWithTitle:NSLocalizedString(@"customtag.edit.icon", nil) image:nil identifier:nil handler:^(__kindof UIAction * _Nonnull action) {
                [self showSelectCoverWithAlert:model];
            }];
            [actions addObject:editCoverAction];
            
            UIAction* renameAction = [UIAction actionWithTitle:NSLocalizedString(@"customtag.rename", nil) image:nil identifier:nil handler:^(__kindof UIAction * _Nonnull action) {
                [self handleRenameContextMenu:model];
            }];
            [actions addObject:renameAction];
        }

        UIAction* copyAction = [UIAction actionWithTitle:NSLocalizedString(@"customtag.copylink", nil) image:nil identifier:nil handler:^(__kindof UIAction * _Nonnull action) {
            [self handleCopyLinkContextMenu:model];
        }];
        [actions addObject:copyAction];
        
        if (![self hasAddToBookMark:model]) {
            //没有添加到书签
            UIAction* addBookMarkAction = [UIAction actionWithTitle:NSLocalizedString(@"activity.addBookmark", nil) image:nil identifier:nil handler:^(__kindof UIAction * _Nonnull action) {
                [self _handleAddCustomTagToBookMark:model];
            }];
            [actions addObject:addBookMarkAction];
        }
        
        UIAction* deleteAction = [UIAction actionWithTitle:NSLocalizedString(@"customtag.delete", nil) image:nil identifier:nil handler:^(__kindof UIAction * _Nonnull action) {
            [self handleDeleteContextMenu:model];
        }];
        deleteAction.attributes = UIMenuElementAttributesDestructive;
        
        //NSLocalizedString(@"customtag.delete", nil)
        UIMenu* deleteMenu = [UIMenu menuWithTitle:@"" image:nil identifier:nil options:UIMenuOptionsDisplayInline children:@[deleteAction]];
        [actions addObject:deleteMenu];
        
        return [UIMenu menuWithTitle:@"" children:actions];
    }];
    
    return configuration;
}

- (BOOL)hasAddToBookMark:(CustomTagModel *)bookMarkModel
{
    NSArray* bookMarkModels = [CommonDataManager shareInstance].bookMarks;
    for(BookMarkModel* item in bookMarkModels) {
        if([item.url isEqualToString:bookMarkModel.targetUrl]) {
            return true;
        }
    }
    
    return false;
}

- (void)_handleAddCustomTagToBookMark:(CustomTagModel *)customTagModel
{
    // 添加到书签
    BookMarkModel* model = [BookMarkModel new];
    model.bookmarkId = [[NSUUID UUID] UUIDString];
    model.title = customTagModel.title;
    model.url = customTagModel.targetUrl;
    model.ppOrder = INT_MAX;
    model.fileType = BookMarkTypeFile;
    model.parentId = [BookMarkModel rootId];
    FavorEditController* vc = [[FavorEditController alloc]init];
    [vc updateWithBookMarkModel:model tab:nil];
    BaseNavigationController* navc = [[BaseNavigationController alloc]initWithRootViewController:vc];
    
    //15.0的开启sheet模式
    if(@available(iOS 15.0,*)) {
        UISheetPresentationController* sheet = navc.sheetPresentationController;
        sheet.detents = @[
            UISheetPresentationControllerDetent.mediumDetent,
            UISheetPresentationControllerDetent.largeDetent,
        ];
        sheet.preferredCornerRadius = 10;
    }

//    [self presentViewController:navc animated:YES completion:nil];
    //v2.6.8,统一present
    UIWindow* window = YBIBNormalWindow();
    UIViewController* rootViewController = window.rootViewController;
    [rootViewController presentCustomToViewController:navc];
}

- (UIContextMenuConfiguration *)collectionView:(UICollectionView *)collectionView contextMenuConfigurationForItemAtIndexPath:(NSIndexPath *)indexPath point:(CGPoint)point
{
    return [self createContextMenuWithIndexPath:indexPath];
}

- (void)collectionView:(UICollectionView *)collectionView willPerformPreviewActionForMenuWithConfiguration:(UIContextMenuConfiguration *)configuration animator:(id<UIContextMenuInteractionCommitAnimating>)animator
{
    @weakify(self)
    [animator addCompletion:^{
        @strongify(self)
        
        NSString* identifier = (NSString *)configuration.identifier;
        if(identifier.length == 0) return;
        if([identifier rangeOfString:@"-"].location == NSNotFound) return;
        
        NSArray* items = [identifier componentsSeparatedByString:@"-"];
        if(items.count < 2) return;
//        NSInteger section = [items.firstObject integerValue];
        NSInteger row = [items.lastObject integerValue];
        
//        CustomTagModel* model = [self getModel:section row:row];
        CustomTagModel* model = self.model[row];
        
        ContextMenuController* menuVC = [[ContextMenuController alloc]init];
        [menuVC updateWithModel:model];
        
        UIWindow* window = YBIBNormalWindow();
        UIViewController* vc = [window rootViewController];
        [vc presentViewController:menuVC animated:YES completion:nil];
    }];
}

//高亮时选中的view
- (UITargetedPreview *)collectionView:(UICollectionView *)collectionView previewForHighlightingContextMenuWithConfiguration:(UIContextMenuConfiguration *)configuration
{
    NSString* identifier = (NSString *)configuration.identifier;
    if(identifier.length == 0) return nil;
    if([identifier rangeOfString:@"-"].location == NSNotFound) return nil;
    
    NSArray* items = [identifier componentsSeparatedByString:@"-"];
    if(items.count < 2) return nil;
    NSInteger section = [items.firstObject integerValue];
    NSInteger row = [items.lastObject integerValue];
    
    NSIndexPath* indexPath = [NSIndexPath indexPathForRow:row inSection:section];
    CustomTagCell* cell = (CustomTagCell *)[collectionView cellForItemAtIndexPath:indexPath];
    
    self.dismissCell = cell;
    
    return [[UITargetedPreview alloc]initWithView:cell.containerView];
}

//取消时选中的view
- (UITargetedPreview *)collectionView:(UICollectionView *)collectionView previewForDismissingContextMenuWithConfiguration:(UIContextMenuConfiguration *)configuration
{
    if(!self.dismissCell) {
        return nil;
    }
    
    return [[UITargetedPreview alloc]initWithView:self.dismissCell.containerView];
}

#pragma mark -- 编辑
- (void)handleEditContextMenu
{
    ContextMenuEditView* view = [ContextMenuEditView new];
    
    UIWindow *window = YBIBNormalWindow();
    [window addSubview:view];
    
    [view mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(window);
    }];
}

#pragma mark -- 选择封面的方式
- (void)showSelectCoverWithAlert:(CustomTagModel *)model
{
    UIAlertControllerStyle style = UIAlertControllerStyleActionSheet;
    if([BrowserUtils isiPad]) {
        //iPad
        style = UIAlertControllerStyleAlert;
    }
    
    UIAlertController* alertController = [UIAlertController alertControllerWithTitle:NSLocalizedString(@"customtag.select.option", nil) message:nil preferredStyle:style];
    
    UIAlertAction *inputCoverUrlAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"adblock.import.from.link", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [self showEditCoverWithAlert:model];
    }];
    UIAlertAction *browserAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"customtag.select.url", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [self showBrowserCoverWithModel:model];
    }];
    UIAlertAction *cancleAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.cancel", nil) style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
        [alertController dismissViewControllerAnimated:YES completion:nil];
    }];

    [alertController addAction:inputCoverUrlAction];
    [alertController addAction:browserAction];
    [alertController addAction:cancleAction];
    
    UIWindow* window = YBIBNormalWindow();
//    UIViewController* root = [window rootViewController];
//    [root presentViewController:alertController animated:YES completion:nil];
    //v2.6.8, 统一present方法，防止崩溃
    [alertController presentSafelyFromViewController:window.rootViewController];
}

#pragma mark -- 编辑封面弹窗
- (void)showEditCoverWithAlert:(CustomTagModel *)model
{
    UIAlertController* alertController = [UIAlertController alertControllerWithTitle:NSLocalizedString(@"customtag.enter.url<##>", nil) message:@"" preferredStyle:UIAlertControllerStyleAlert];
    UIAlertAction *confirmAction=[UIAlertAction actionWithTitle:NSLocalizedString(@"alert.confirm<##>", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        UITextField* textField = alertController.textFields.firstObject;
        NSString* coverUrl = textField.text;
        
        coverUrl = [coverUrl stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceCharacterSet]];
        if(coverUrl.length == 0) {
            [UIView showToast:NSLocalizedString(@"adblock.enter.link", nil)];
            return;
        }
        
        //注意，有可能是base64的编码数据
        DatabaseUnit* unit = [DatabaseUnit updateCustomTagWithId:model.uuid iconUrl:coverUrl customTag:model];
        DB_EXEC(unit);
    }];
    UIAlertAction *cancleAction=[UIAlertAction actionWithTitle:NSLocalizedString(@"alert.cancel", nil) style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
        [alertController dismissViewControllerAnimated:YES completion:nil];
    }];

    [alertController addTextFieldWithConfigurationHandler:^(UITextField *textField) {
        BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
        if(isDarkTheme) {
            textField.textColor = [UIColor colorWithHexString:@"#FFFFFF"];
        } else {
            textField.textColor = [UIColor colorWithHexString:@"#333333"];
        }
        
        textField.clearButtonMode = UITextFieldViewModeAlways;
    }];
    
    [alertController addAction:confirmAction];
    [alertController addAction:cancleAction];
    
    UIWindow* window = YBIBNormalWindow();
//    UIViewController* root = [window rootViewController];
//    [root presentViewController:alertController animated:YES completion:nil];
    //v2.6.8, 统一present方法，防止崩溃
    [alertController presentSafelyFromViewController:window.rootViewController];
}

#pragma mark -- 从浏览器选择图片作为封面
- (void)showBrowserCoverWithModel:(CustomTagModel *)model
{
    NSString* title = model.title;
    
    //默认选择必应的图片搜索
    NSString* url = [NSString stringWithFormat:@"https://cn.bing.com/images/search?q=%@&form=HDRSC2&first=1", title?:@""];
    NSCharacterSet *allowedCharacterSet = [NSCharacterSet URLQueryAllowedCharacterSet];
    NSString *encodedUrl = [url stringByAddingPercentEncodingWithAllowedCharacters:allowedCharacterSet];
    
    if(self.changeIconAction) {
        self.changeIconAction(encodedUrl, model);
    }
}

#pragma mark -- 更改名称
- (void)handleRenameContextMenu:(CustomTagModel *)model
{
    UIAlertController* alertController = [UIAlertController alertControllerWithTitle:NSLocalizedString(@"customtag.rename.text", nil) message:nil preferredStyle:UIAlertControllerStyleAlert];
    UIAlertAction *confirmAction=[UIAlertAction actionWithTitle:NSLocalizedString(@"alert.confirm", nil) style:UIAlertActionStyleDestructive handler:^(UIAlertAction * _Nonnull action) {
        UITextField* textField = alertController.textFields.firstObject;
        if(textField.text.length == 0
           || [textField.text stringByReplacingOccurrencesOfString:@" " withString:@""].length == 0) {
            //弹toast
            [UIView showWarning:NSLocalizedString(@"customtag.rename.text", nil)];
            return;
        } else {
            model.title = textField.text;
            
            DatabaseUnit* unit = [DatabaseUnit updateCustomTagWithId:model.uuid title:model.title customTag:model];
            DB_EXEC(unit);
        
            [self.collectionView reloadData];
        }
    }];
    UIAlertAction *cancleAction=[UIAlertAction actionWithTitle:NSLocalizedString(@"alert.cancel", nil) style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
        [alertController dismissViewControllerAnimated:YES completion:nil];
    }];

    [alertController addTextFieldWithConfigurationHandler:^(UITextField *textField) {
        textField.textColor = [UIColor colorWithHexString:@"#333333"];
        textField.clearButtonMode = UITextFieldViewModeAlways;
        textField.text = model.title;
    }];
    
    [alertController addAction:confirmAction];
    [alertController addAction:cancleAction];
    
    UIWindow* window = YBIBNormalWindow();
//    UIViewController* vc = [window rootViewController];
//    [vc presentViewController:alertController animated:YES completion:nil];
    //v2.6.8, 统一present方法，防止崩溃
    [alertController presentSafelyFromViewController:window.rootViewController];
}

#pragma mark -- 复制链接
- (void)handleCopyLinkContextMenu:(CustomTagModel *)model
{
    //复制到剪贴板
    if (model.targetUrl.length > 0) {
        UIPasteboard *pasteboard = [UIPasteboard generalPasteboard];
        pasteboard.string = model.targetUrl;
        
        [UIView showSucceed:NSLocalizedString(@"tips.pasteboard.success", nil)];
    }
}

#pragma mark -- 删除
- (void)handleDeleteContextMenu:(CustomTagModel *)model
{
    //删除时的动画效果很难看，即消失的时候
    //因此如果是删除动作，那么去掉动画效果
    self.dismissCell = nil;
    
    DatabaseUnit* unit = [DatabaseUnit removeCustomTagWithId:model.uuid];
    [unit setCompleteBlock:^(id result, BOOL success) {
        if(success) {
        }
    }];
    DB_EXEC(unit);
    
    [self.model removeObject:model];
    [self.collectionView reloadData];
    
//    [self updatePageControl];
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath
{
    CustomTagCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"CustomTagCell" forIndexPath:indexPath];
    
//    NSInteger index = self.pageSize*indexPath.section + indexPath.row;
    NSInteger index = indexPath.row;
    if(index < self.model.count) {
        //处理刷新时的异常状态
        CustomTagModel* model = self.model[index];
        model.index = index;
        [cell updateWithModel:model];
        
        [cell setDidTapAction:^{
            //普通状态
            if(model.type == CustomTagTypeBookMark) {
                //书签
                [[NSNotificationCenter defaultCenter] postNotificationName:kOpenBookMarkNotification object:nil];
            } else if(model.type == 2) {
                //文件
            } else {
                //其它
                [[NSNotificationCenter defaultCenter] postNotificationName:kOpenWebViewNotification object:model];
            }
        }];
    }
    
    return cell;
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath
{
//    NSInteger index = self.pageSize*indexPath.section + indexPath.row;
//    CustomTagModel* model = self.model[index];
}

@end
