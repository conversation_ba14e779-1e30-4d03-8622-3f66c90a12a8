//
//  ContextMenuController.m
//  PPBrowser
//
//  Created by qingbin on 2022/7/14.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "ContextMenuController.h"

#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"

#import "MaizyHeader.h"
#import "NSObject+Helper.h"
#import "UIImageView+WebCache.h"
#import "NSURL+Extension.h"
#import "PaddingNewLabel.h"

@interface ContextMenuController ()

@property (nonatomic, strong) UILabel *logoLabel;

@property (nonatomic, strong) UIView* shadowView;

@property (nonatomic, strong) UIView *containerView;

@property (nonatomic, strong) UIImageView *logo;

@property (nonatomic, strong) UIStackView* stackView;

@property (nonatomic, strong) UILabel *titleLabel;

@property (nonatomic, strong) UILabel *urlLabel;

@property (nonatomic, strong) CustomTagModel *model;

@end

@implementation ContextMenuController

- (void)viewDidLoad
{
    [super viewDidLoad];

    [self addSubviews];
    [self defineLayout];
}

- (void)updateWithModel:(CustomTagModel*)model
{
    self.model = model;
        self.logo.backgroundColor = UIColor.whiteColor;
    self.logoLabel.hidden = YES;
    self.logo.hidden = NO;
    
    [self.logo mas_updateConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(42);
    }];
    
    if(model.type == CustomTagTypeBookMark) {
        self.logo.image = [UIImage imageNamed:@"home_star_icon"];
    } else if(model.type == CustomTagTypeFile) {
        //文件
    } else if(model.type == CustomTagTypeGuideline) {
        self.logo.image = [UIImage imageNamed:@"home_note_icon"];
    } else if(model.type == CustomTagTypeMetaso) {
        //秘塔AI
        self.logo.image = [UIImage imageNamed:@"mita"];
    } else {
        if(model.iconUrl.length > 0 && self.model.isInvalidUrl==false) {
            //如果有logo
            // 获取默认的实例
            SDWebImageDownloader *downloader = [SDWebImageDownloader sharedDownloader];
            // 设置请求超时时间（以秒为单位）
            downloader.downloadTimeout = 5.0; // 设置为5秒，您可以根据需要进行调整
            
            @weakify(self)
            [self.logo sd_setImageWithURL:[NSURL URLWithString:model.iconUrl] completed:^(UIImage *image, NSError *error, SDImageCacheType cacheType, NSURL *imageURL) {
                @strongify(self)
                if(!self) return;
                //复用问题，导致获取到错的icon
                if(![imageURL.absoluteString isEqualToString:self.model.iconUrl]) return;
                
                if(error || !image) {
                    //请求失败，那么下次则不再请求，但是不保存到数据库，有可能网络问题
//                    self.model.iconUrl = @"";
                    self.model.isInvalidUrl = YES;
                    
                    //如果获取失败
                    self.logo.hidden = YES;
                    self.logoLabel.backgroundColor = [self getLogoColorWithLetter:self.model.iconUrl];
                    
                    if(model.title.length > 0) {
                        NSString* logo = [model.title substringWithRange:NSMakeRange(0, 1)];
                        self.logoLabel.text = logo;
                    } else {
                        self.logoLabel.text = @"";
                    }
                    
                    self.logoLabel.hidden = NO;
                } else if(image) {
                    self.logo.image = image;
                }
            }];
        } else {
            //如果没有logo
            NSString* url = [[NSURL URLWithString:model.targetUrl] normalizedHost];
            if([url containsString:@"greasyfork.org"]) {
                //greasyfork
                self.logo.image = [UIImage imageNamed:@"greasyfork_logo"];
            } else if([url containsString:@"bilibili.com"]) {
                //bilibili
                self.logo.image = [UIImage imageNamed:@"bilibili_logo"];
            } else if([url containsString:@"xiaohongshu.com"]) {
                //小红书
                self.logo.image = [UIImage imageNamed:@"xiaohongshu_logo"];
            } else if([url containsString:@"reddit.com"]) {
                //Reddit
                self.logo.image = [UIImage imageNamed:@"reddit_logo"];
            } else if([url containsString:@"youtube.com"]) {
                //youtube
                self.logo.image = [UIImage imageNamed:@"youtube2_logo"];
            } else if([url containsString:@"google.com"]) {
                //google
                self.logo.image = [UIImage imageNamed:@"google_logo_2"];
                
                [self.logo mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.size.mas_equalTo(38);
                }];
            } else if([url containsString:@"baidu.com"]) {
                //百度
                self.logo.image = [UIImage imageNamed:@"baidu_logo"];
                
                [self.logo mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.size.mas_equalTo(38);
                }];
            }  else if([url containsString:@"bing.com"]) {
                //bing
                self.logo.image = [UIImage imageNamed:@"bing_logo"];
                
                [self.logo mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.size.mas_equalTo(38);
                }];
            }  else {
                //首字母显示当作logo
//                self.logoLabel.backgroundColor = [self getLogoColorWithLetter:url];
                self.logoLabel.backgroundColor = [UIColor colorWithURL:model.targetUrl];
                self.logo.hidden = YES;
                
                if(model.title.length > 0) {
                    NSString* logo = [model.title substringWithRange:NSMakeRange(0, 1)];
                    self.logoLabel.text = logo;
                } else {
                    self.logoLabel.text = @"";
                }
                
                self.logoLabel.hidden = NO;
            }
        }
    }
        
    self.titleLabel.text = model.title;
    self.urlLabel.text = model.targetUrl;
}

- (UIColor *)getLogoColorWithLetter:(NSString *)letter
{
    if(letter.length == 0) {
      //友盟崩溃
       return [UIColor colorWithHexString:@"#945cfc"];
    }
    
    NSArray* options = @[@"#945cfc", @"#04d4ac", @"#fc6484",@"#1caffc", @"#5c44f5"];
    char* cString = (char*) [letter UTF8String];
    char ch = cString[0];
    
    NSString* color = options[ch%options.count];
    
    return [UIColor colorWithHexString:color];
}

- (void)addSubviews
{
    [self.view addSubview:self.shadowView];
    [self.shadowView addSubview:self.containerView];
    [self.containerView addSubview:self.logo];
    [self.containerView addSubview:self.logoLabel];
    
    [self.view addSubview:self.stackView];
//    [self.view addSubview:self.titleLabel];
//    [self.view addSubview:self.urlLabel];
}

- (void)defineLayout
{
    [self.shadowView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(40);
        make.centerY.equalTo(self.view);
        make.left.mas_offset(10);
    }];
    
    [self.containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.shadowView);
    }];
    
    [self.logoLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.logo);
    }];
    
    [self.logo mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.containerView);
        make.size.mas_equalTo(42);
    }];
    
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.containerView.mas_right).offset(10);
        make.right.equalTo(self.view);
        make.centerY.equalTo(self.view);
    }];
}

- (UIView *)shadowView
{
    if(!_shadowView) {
        _shadowView = [UIView new];
        
        //UIColor.lightGrayColor.CGColor;
        _shadowView.layer.shadowColor = [UIColor colorWithHexString:@"#dcdcdc"].CGColor;
        _shadowView.layer.shadowOpacity = 1;
        _shadowView.layer.shadowOffset = CGSizeZero;
        _shadowView.layer.shadowRadius = 3;
        _shadowView.layer.masksToBounds = NO;
        _shadowView.backgroundColor = UIColor.whiteColor;
        
        _shadowView.layer.cornerRadius = 10;
    }
    
    return _shadowView;
}

- (UIView *)containerView
{
    if(!_containerView) {
        _containerView = [UIView new];
        _containerView.backgroundColor = [UIColor colorWithHexString:@"#ffffff"];//f1f2f3
        _containerView.layer.cornerRadius = 10;
    }
    
    return _containerView;
}

- (UIImageView *)logo
{
    if(!_logo) {
        _logo = [UIImageView new];
        _logo.layer.cornerRadius = 10;
        _logo.layer.masksToBounds = YES;
        
        _logo.contentMode = UIViewContentModeScaleAspectFit;
        _logo.backgroundColor = UIColor.whiteColor;
    }
    
    return _logo;
}

- (UILabel *)titleLabel
{
    if(!_titleLabel) {
        _titleLabel = [UIView createLabelWithTitle:@""
                                              textColor:[UIColor colorWithHexString:@"#222222"]
                                                bgColor:UIColor.clearColor
                                               fontSize:12
                                          textAlignment:NSTextAlignmentLeft
                                                  bBold:NO];
    }
    
    return _titleLabel;
}

- (UILabel *)urlLabel
{
    if(!_urlLabel) {
        _urlLabel = [UIView createLabelWithTitle:@""
                                              textColor:[UIColor colorWithHexString:@"#999999"]
                                                bgColor:UIColor.clearColor
                                               fontSize:12
                                          textAlignment:NSTextAlignmentLeft
                                                  bBold:NO];
    }
    
    return _urlLabel;
}

- (PaddingNewLabel *)logoLabel
{
    if(!_logoLabel) {
        _logoLabel = [PaddingNewLabel new];
        _logoLabel.textColor = [UIColor colorWithHexString:@"#ffffff"];
        _logoLabel.font = [UIFont boldSystemFontOfSize:27];
        _logoLabel.hidden = YES;
        
        _logoLabel.layer.cornerRadius = 10;
        _logoLabel.layer.masksToBounds = YES;
    }
    
    return _logoLabel;
}

- (UIStackView *)stackView
{
    if(!_stackView) {
        _stackView = [[UIStackView alloc]initWithArrangedSubviews:@[
            self.titleLabel,
            self.urlLabel
        ]];
        
        _stackView.axis = UILayoutConstraintAxisVertical;
        _stackView.spacing = 5;
    }
    
    return _stackView;
}

@end
