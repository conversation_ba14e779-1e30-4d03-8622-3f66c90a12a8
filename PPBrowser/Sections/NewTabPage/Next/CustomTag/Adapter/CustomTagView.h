//
//  CustomTagView.h
//  PPBrowser
//
//  Created by qingbin on 2022/5/26.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <UIKit/UIKit.h>

#import "CustomTagModel.h"
#import "ThemeProtocol.h"
#import "PreferenceManager.h"

#import "BaseAbstractView.h"

//拖拽逻辑参考:
//https://zhuanlan.zhihu.com/p/246330810
@interface CustomTagView : UIView<ThemeProtocol>

//+ (float)height;

- (void)reloadData;

//主要为了打开新标签动画不一致的bug
//- (CGPoint)contentOffset;

//- (void)updateContentOffset:(CGPoint)offset;

// 旋转适配
- (void)updateForTransition;

@property (readonly) UICollectionView *collectionView;

@property (nonatomic, copy) void (^changeIconAction)(NSString *url, CustomTagModel* model);

@end

