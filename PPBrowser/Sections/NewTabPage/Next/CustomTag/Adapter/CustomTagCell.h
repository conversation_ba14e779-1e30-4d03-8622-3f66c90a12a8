//
//  CustomTagCell.h
//  PPBrowser
//
//  Created by qingbin on 2022/5/26.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "CustomTagModel.h"
#import "ThemeProtocol.h"
#import "PreferenceManager.h"

NS_ASSUME_NONNULL_BEGIN

@interface CustomTagCell : UICollectionViewCell<ThemeProtocol>

- (void)updateWithModel:(CustomTagModel*)model;

@property (nonatomic, copy) void (^didTapAction)(void);

@property (nonatomic, copy) void (^didDeleteAction)(void);

@property (readonly) UIView *containerView;

@end

NS_ASSUME_NONNULL_END
