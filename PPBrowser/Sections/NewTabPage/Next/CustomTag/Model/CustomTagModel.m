//
//  CustomTagModel.m
//  PPBrowser
//
//  Created by qingbin on 2022/5/26.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "CustomTagModel.h"
#import "URIFixup.h"

#import "OpenUDID.h"
#import "PPEnums.h"
#import "CloudKitHelper.h"

@implementation CustomTagModel

//添加
//+ (instancetype)add
//{
//    CustomTagModel* item = [CustomTagModel new];
//    item.uuid = [[NSUUID UUID] UUIDString];
//    item.title = @"添加";
//    item.type = CustomTagTypeAdd;
//    item.ppOrder = INT_MAX;
//    item.targetUrl = @"";
//    item.iconUrl = @"";
//
//    return item;
//}

//书签
//https://igoutu.cn/icon/48142/%E4%B9%A6%E7%AD%BE%E5%8A%9F%E8%83%BD%E5%8C%BA
//背景色设置为white
+ (instancetype)bookMark
{
    CustomTagModel* item = [CustomTagModel new];
    item.uuid = [[NSUUID UUID] UUIDString];
    item.title = NSLocalizedString(@"home.bookMark", nil);
    item.type = CustomTagTypeBookMark;
    item.ppOrder = 1;
    item.targetUrl = @"";
    item.iconUrl = @"";
    
    return item;
}

//用户指南
//背景色设置为white
+ (instancetype)guideline
{
    CustomTagModel* item = [CustomTagModel new];
    item.uuid = [[NSUUID UUID] UUIDString];
    item.title = NSLocalizedString(@"home.userGuide", nil);
    item.type = CustomTagTypeGuideline;
    item.ppOrder = 6;
    item.targetUrl = @"";
    item.iconUrl = @"";
    
    return item;
}

//Wikipedia
+ (instancetype)wikipedia
{
    CustomTagModel* item = [CustomTagModel new];
    item.uuid = [[NSUUID UUID] UUIDString];
    item.title = @"Wikipedia";
    item.type = CustomTagTypeWeb;
    item.ppOrder = 7;
    item.targetUrl = @"https://www.wikipedia.org/";
    item.iconUrl = @"https://www.wikipedia.org/static/apple-touch/wikipedia.png";
    
    return item;
}

//秘塔AI
+ (instancetype)metaso
{
    CustomTagModel* item = [CustomTagModel new];
    item.uuid = [[NSUUID UUID] UUIDString];
    item.title = @"秘塔AI"; //只在中国大陆地区，因此不国际化了
    item.type = CustomTagTypeMetaso;
    item.ppOrder = 7;
    item.targetUrl = @"";
    //用本地logo
    item.iconUrl = @"";
    
    return item;
}

//YouTube
+ (instancetype)youtube
{
    CustomTagModel* item = [CustomTagModel new];
    item.uuid = [[NSUUID UUID] UUIDString];
    item.title = @"YouTube";
    item.type = CustomTagTypeWeb;
    item.ppOrder = 8;
    item.targetUrl = @"https://m.youtube.com/";
    //用本地logo
    item.iconUrl = @"";
    
    return item;
}

//Twitter
+ (instancetype)twitter
{
    CustomTagModel* item = [CustomTagModel new];
    item.uuid = [[NSUUID UUID] UUIDString];
    item.title = @"Twitter";
    item.type = CustomTagTypeWeb;
    item.ppOrder = 9;
    item.targetUrl = @"https://mobile.twitter.com/";
    item.iconUrl = @"https://abs.twimg.com/responsive-web/client-web/icon-ios.b1fc727a.png";
    
    return item;
}

//reddit
+ (instancetype)reddit
{
    CustomTagModel* item = [CustomTagModel new];
    item.uuid = [[NSUUID UUID] UUIDString];
    item.title = @"Reddit";
    item.type = CustomTagTypeWeb;
    item.ppOrder = 10;
    item.targetUrl = @"https://www.reddit.com/";
    //太模糊,使用本地图片作为logo
//    item.iconUrl = @"https://www.redditstatic.com/mweb2x/favicon/76x76.png";
    
    return item;
}

//temu
+ (instancetype)temu
{
    CustomTagModel* item = [CustomTagModel new];
    item.uuid = [[NSUUID UUID] UUIDString];
    item.title = @"Temu";
    item.type = CustomTagTypeWeb;
    item.ppOrder = 11;
    item.targetUrl = @"https://www.temu.com/";
    item.iconUrl = @"https://www.temu.com/favicon.ico";
    
    return item;
}

//greasyfork
+ (instancetype)greasyfork
{
    CustomTagModel* item = [CustomTagModel new];
    item.uuid = [[NSUUID UUID] UUIDString];
    item.title = @"Greasyfork";
    item.type = CustomTagTypeWeb;
    item.ppOrder = 12;
    item.targetUrl = @"https://greasyfork.org";
    item.iconUrl = @"";
    
    return item;
}

//今日热榜
+ (instancetype)tophub
{
    CustomTagModel* item = [CustomTagModel new];
    item.uuid = [[NSUUID UUID] UUIDString];
    item.title = @"今日热榜";
    item.type = CustomTagTypeWeb;
    item.ppOrder = 13;
    item.targetUrl = @"https://tophub.today/";
    item.iconUrl = @"https://file.ipadown.com/tophub/assets/images/favicon/apple-icon-57x57.png";
    
    return item;
}

//gfmirror
+ (instancetype)gfmirror
{
    CustomTagModel* item = [CustomTagModel new];
    item.uuid = [[NSUUID UUID] UUIDString];
    item.title = @"GFMirror";
    item.type = CustomTagTypeWeb;
    item.ppOrder = 14;
    item.targetUrl = @"https://6jj.me/";
    item.iconUrl = @"";
    
    return item;
}

#pragma mark -- CloudKit相关操作
//CloudKit同步
//获取一个默认的CKRecord
- (CKRecord *)toDefaultCKRecord
{
    CKRecordID* recordID = [[CKRecordID alloc]initWithRecordName:self.uuid zoneID:[CloudKitHelper focusZoneID]];
    CKRecord* record = [[CKRecord alloc]initWithRecordType:NSStringFromClass(CustomTagModel.class) recordID:recordID];
    
    return record;
}

//转换成CKRecord
- (CKRecord *)toCKRecord
{
    CKRecord* record = [self toDefaultCKRecord];
    
    record[@"uuid"] = self.uuid;
    record[@"title"] = self.title?:@"";
    record[@"targetUrl"] = self.targetUrl?:@"";
    record[@"type"] = @(self.type);
    record[@"ppOrder"] = @(self.ppOrder);
    record[@"iconUrl"] = self.iconUrl?:@"";
    record[@"ctime"] = self.ctime;
    record[@"updateTime"] = self.updateTime?:@"1";
    
    //额外添加的字段，用于CloudKit
    //app版本号
    NSString* version = [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleShortVersionString"];
    record[@"appVersion"] = version?:@"";
    //上传到CloudKit创建时间
    record[@"appCtime"] = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
    //用户的uuid
    record[@"appUserID"] = [OpenUDID value]?:@"";
    //添加类型区分
    record[@"appType"] = @(CloudModelTypeCustomTag);
    
    return record;
}
//从CKRecord转换为CustomTagModel
- (instancetype)initWithCKRecord:(CKRecord *)record
{
    self = [super init];
    if(self) {
        self.uuid = record[@"uuid"];
        self.title = record[@"title"];
        self.targetUrl = record[@"targetUrl"];
        self.type = [[record objectForKey:@"type"] intValue];
        self.ppOrder = [[record objectForKey:@"ppOrder"] intValue];
        self.iconUrl = record[@"iconUrl"];
        self.ctime = record[@"ctime"];
        
        NSString* updateTimeText = record[@"updateTime"];
        NSInteger updateTime = [updateTimeText integerValue];
        if(updateTime == 0 || updateTime == 1) {
            //初始化
            updateTime = [record.modificationDate timeIntervalSince1970];
        }
        self.updateTime = [NSString stringWithFormat:@"%ld", updateTime];
    }
    
    return self;
}

//判断两个是否相同
- (BOOL)objectIsEqualTo:(id)obj
{
    CustomTagModel* item = obj;
    if(self.type != item.type) return NO;
    if([self.uuid isEqualToString:item.uuid]) return YES;
    
    if(self.type == CustomTagTypeWeb) {
        return [self.targetUrl isEqualToString:item.targetUrl];
    } else {
        return YES;
    }
}

//返回uuid
- (NSString*)getUuid
{
    return self.uuid;
}

//更新时间
- (NSString*)getUpdateTime
{
    return self.updateTime;
}

//v2.6.8, 是否是内置类型
- (BOOL)isBuiltIn
{
    return self.type == CustomTagTypeFile
    || self.type == CustomTagTypeGuideline
    || self.type == CustomTagTypeBookMark
    || self.type == CustomTagTypeMetaso;
}

@end
