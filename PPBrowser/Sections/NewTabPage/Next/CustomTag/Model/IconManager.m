//
//  IconManager.m
//  PPBrowser
//
//  Created by qingbin on 2022/5/31.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "IconManager.h"

@interface IconManager ()

@property (nonatomic, strong) NSMutableArray *icons;

@end

@implementation IconManager

+ (instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    static IconManager* obj;
    dispatch_once(&onceToken, ^{
        obj = [IconManager new];
    });
    
    return obj;
}

- (instancetype)init
{
    self = [super init];
    if(self) {
        [self generateIcons];
    }
    
    return self;
}

- (void)generateIcons
{
    //方向参考
    //https://blog.csdn.net/Asong_ge/article/details/63697878
    
    NSMutableArray* iconModels = [NSMutableArray array];
    
    //45deg
    IconModel* item = [IconModel new];
    item.colors = @[
        (__bridge id)[UIColor colorWithHexString:@"#ff9a9e"].CGColor,
        (__bridge id)[UIColor colorWithHexString:@"#fad0c4"].CGColor
    ];
    item.startPoint = CGPointMake(0, 0);
    item.endPoint = CGPointMake(1, 1);
    [iconModels addObject:item];
    
    //to top
    item = [IconModel new];
    item.colors = @[
        (__bridge id)[UIColor colorWithHexString:@"#a18cd1"].CGColor,
        (__bridge id)[UIColor colorWithHexString:@"#fbc2eb"].CGColor
    ];
    item.startPoint = CGPointMake(0, 0);
    item.endPoint = CGPointMake(1, 1);
    [iconModels addObject:item];
    
    //background-image: linear-gradient(120deg, #d4fc79 0%, #96e6a1 100%);
    item = [IconModel new];
    item.colors = @[
        (__bridge id)[UIColor colorWithHexString:@"#d4fc79"].CGColor,
        (__bridge id)[UIColor colorWithHexString:@"#96e6a1"].CGColor
    ];
    item.startPoint = CGPointMake(0, 0);
    item.endPoint = CGPointMake(1, 1);
    [iconModels addObject:item];
    
    //to right
    item = [IconModel new];
    item.colors = @[
        (__bridge id)[UIColor colorWithHexString:@"#ffecd2"].CGColor,
        (__bridge id)[UIColor colorWithHexString:@"#fcb69f"].CGColor
    ];
    item.startPoint = CGPointMake(0, 0);
    item.endPoint = CGPointMake(1, 1);
    [iconModels addObject:item];
    
    //to top
    item = [IconModel new];
    item.colors = @[
        (__bridge id)[UIColor colorWithHexString:@"#fad0c4"].CGColor,
        (__bridge id)[UIColor colorWithHexString:@"#ffd1ff"].CGColor
    ];
    item.startPoint = CGPointMake(0, 0);
    item.endPoint = CGPointMake(1, 1);
    [iconModels addObject:item];
    
    //background-image: linear-gradient(120deg, #a1c4fd 0%, #c2e9fb 100%);
    item = [IconModel new];
    item.colors = @[
        (__bridge id)[UIColor colorWithHexString:@"#a1c4fd"].CGColor,
        (__bridge id)[UIColor colorWithHexString:@"#c2e9fb"].CGColor
    ];
    item.startPoint = CGPointMake(0, 0);
    item.endPoint = CGPointMake(1, 1);
    [iconModels addObject:item];
    
    //background-image: linear-gradient(120deg, #84fab0 0%, #8fd3f4 100%);
    item = [IconModel new];
    item.colors = @[
        (__bridge id)[UIColor colorWithHexString:@"#84fab0"].CGColor,
        (__bridge id)[UIColor colorWithHexString:@"#8fd3f4"].CGColor
    ];
    item.startPoint = CGPointMake(0, 0);
    item.endPoint = CGPointMake(1, 1);
    [iconModels addObject:item];
    
    //background-image: linear-gradient(120deg, #e0c3fc 0%, #8ec5fc 100%);
    item = [IconModel new];
    item.colors = @[
        (__bridge id)[UIColor colorWithHexString:@"#e0c3fc"].CGColor,
        (__bridge id)[UIColor colorWithHexString:@"#8ec5fc"].CGColor
    ];
    item.startPoint = CGPointMake(0, 0);
    item.endPoint = CGPointMake(1, 1);
    [iconModels addObject:item];
    
    //background-image: linear-gradient(120deg, #f093fb 0%, #f5576c 100%);
    item = [IconModel new];
    item.colors = @[
        (__bridge id)[UIColor colorWithHexString:@"#f093fb"].CGColor,
        (__bridge id)[UIColor colorWithHexString:@"#f5576c"].CGColor
    ];
    item.startPoint = CGPointMake(0, 0);
    item.endPoint = CGPointMake(1, 1);
    [iconModels addObject:item];
    
    //background-image: linear-gradient(to right, #4facfe 0%, #00f2fe 100%);
    item = [IconModel new];
    item.colors = @[
        (__bridge id)[UIColor colorWithHexString:@"#4facfe"].CGColor,
        (__bridge id)[UIColor colorWithHexString:@"#00f2fe"].CGColor
    ];
    item.startPoint = CGPointMake(0, 0);
    item.endPoint = CGPointMake(1, 1);
    [iconModels addObject:item];
    
    //background-image: linear-gradient(to right, #43e97b 0%, #38f9d7 100%);
    item = [IconModel new];
    item.colors = @[
        (__bridge id)[UIColor colorWithHexString:@"#43e97b"].CGColor,
        (__bridge id)[UIColor colorWithHexString:@"#38f9d7"].CGColor
    ];
    item.startPoint = CGPointMake(0, 0);
    item.endPoint = CGPointMake(1, 1);
    [iconModels addObject:item];
    
    //background-image: linear-gradient(to right, #fa709a 0%, #fee140 100%);
    item = [IconModel new];
    item.colors = @[
        (__bridge id)[UIColor colorWithHexString:@"#fa709a"].CGColor,
        (__bridge id)[UIColor colorWithHexString:@"#fee140"].CGColor
    ];
    item.startPoint = CGPointMake(0, 0);
    item.endPoint = CGPointMake(1, 1);
    [iconModels addObject:item];
    
    //background-image: linear-gradient(to top, #00c6fb 0%, #005bea 100%);
    item = [IconModel new];
    item.colors = @[
        (__bridge id)[UIColor colorWithHexString:@"#00c6fb"].CGColor,
        (__bridge id)[UIColor colorWithHexString:@"#005bea"].CGColor
    ];
    item.startPoint = CGPointMake(0, 0);
    item.endPoint = CGPointMake(1, 1);
    [iconModels addObject:item];
    
    //background-image: linear-gradient(-225deg, #7DE2FC 0%, #B9B6E5 100%);
    item = [IconModel new];
    item.colors = @[
        (__bridge id)[UIColor colorWithHexString:@"#7DE2FC"].CGColor,
        (__bridge id)[UIColor colorWithHexString:@"#B9B6E5"].CGColor
    ];
    item.startPoint = CGPointMake(0, 0);
    item.endPoint = CGPointMake(1, 1);
    [iconModels addObject:item];
    
    //background-image: linear-gradient(to top, #209cff 0%, #68e0cf 100%);
    item = [IconModel new];
    item.colors = @[
        (__bridge id)[UIColor colorWithHexString:@"#209cff"].CGColor,
        (__bridge id)[UIColor colorWithHexString:@"#68e0cf"].CGColor
    ];
    item.startPoint = CGPointMake(0, 0);
    item.endPoint = CGPointMake(1, 1);
    [iconModels addObject:item];
    
    for(IconModel* item in iconModels) {
        UIImage* image = [self gradientImageWithIcon:item];
        [self.icons addObject:image];
    }
    
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
    NSString *documentsDirectory = [paths objectAtIndex:0];
    NSLog(@"保存目录: %@", documentsDirectory);
    
    for(int i=0;i<self.icons.count;i++) {
        NSString* iconName = [NSString stringWithFormat:@"<EMAIL>",i];
        
        UIImage* image = self.icons[i];
        NSData* imageData = UIImagePNGRepresentation(image);
        
        NSString *filePath = [documentsDirectory stringByAppendingPathComponent:iconName]; //Add the file name
        [imageData writeToFile:filePath atomically:YES];
    }
}

- (UIImage *)gradientImageWithIcon:(IconModel *)icon
{
    CAGradientLayer *gradientLayer = [CAGradientLayer new];
    gradientLayer.colors = icon.colors;
    gradientLayer.startPoint = icon.startPoint;
    gradientLayer.endPoint = icon.endPoint;
    gradientLayer.frame = CGRectMake(0, 0, 120, 120);
    
//    UIView* paddingView = [UIView new];
//    paddingView.frame = CGRectMake(0, 0, 120, 120);
//    [paddingView.layer addSublayer:gradientLayer];
//
//    float height = 160;
//    float width = height;
//    float offsetX = (200 - height)/2.0;
//    float offsetY = offsetX;
//    gradientLayer.frame = CGRectMake(offsetX, offsetY, width, height);
    
    // 根据gradient转化为image
    UIImage *gradientImage;
    UIGraphicsBeginImageContext(gradientLayer.frame.size);
    [gradientLayer renderInContext:UIGraphicsGetCurrentContext()];
    gradientImage = [UIGraphicsGetImageFromCurrentImageContext() resizableImageWithCapInsets:UIEdgeInsetsZero resizingMode:UIImageResizingModeStretch];
    UIGraphicsEndImageContext();
    
    return gradientImage;
}

- (NSArray*)allIcons
{
    return self.icons;
}

- (NSMutableArray *)icons
{
    if(!_icons) {
        _icons = [NSMutableArray array];
    }
    
    return _icons;
}

@end
