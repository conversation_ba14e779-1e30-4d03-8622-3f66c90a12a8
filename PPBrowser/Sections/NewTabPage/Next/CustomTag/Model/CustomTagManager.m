//
//  CustomTagManager.m
//  PPBrowser
//
//  Created by qingbin on 2024/8/3.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import "CustomTagManager.h"

#import "DatabaseUnit+CustomTag.h"
#import "UIView+FrameHelper.h"

#import "BrowserUtils.h"
#import "ReactiveCocoa.h"

#import "TopToolbarForiPad.h"
#import "BottomToolbar.h"

@interface CustomTagManager ()

//@property (nonatomic, strong) NSArray* allTags;

@end

@implementation CustomTagManager

+ (instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    static CustomTagManager* obj;
    dispatch_once(&onceToken, ^{
        obj = [CustomTagManager new];
    });
    
    return obj;
}

- (instancetype)init
{
    self = [super init];
    if(self) {

    }
    
    return self;
}

#pragma mark -- 各种高度

//编辑框距离左边间距
+ (float)leftOffsetForTextFiled
{
    float offset = 0;
    CGSize transitionToSize = [BrowserUtils shareInstance].transitionToSize;
    float width = transitionToSize.width;
    
    if([BrowserUtils isiPad]) {
        RACTuple* tupe = [CustomTagManager widthForTextField];
        BOOL isMax = [tupe.first boolValue];
        float calculatedWidth = [tupe.second floatValue];
        
        if (isMax) {
            //超过了最大宽度，居中对齐
            offset = (width - calculatedWidth) / 2.0f;
        } else {
            //没有超出最大宽度
            offset = width*0.12;
        }
    } else {
        //iPhone
        if([[BrowserUtils shareInstance] isLandscape]) {
            //横屏,限定最大的宽度
            offset = width*0.18;
        } else {
            //竖屏
            offset = 20;
        }
    }
    
    return offset;
}

//编辑框距离左边间距(理论版，没有考虑iPad居中对齐的情况，主要是为了widthForTextField方法计算)
//解决嵌套调用的问题
+ (float)_leftOffsetForTextFiled
{
    float offset = 0;
    CGSize transitionToSize = [BrowserUtils shareInstance].transitionToSize;
    float width = transitionToSize.width;
    
    if([BrowserUtils isiPad]) {
        offset = width*0.12;
    } else {
        //iPhone
        if([[BrowserUtils shareInstance] isLandscape]) {
            //横屏,限定最大的宽度
            offset = width*0.18;
        } else {
            //竖屏
            offset = 20;
        }
    }
    
    return offset;
}

//编辑框高度
+ (float)heightForTextField
{
    BOOL isLandscape = [[BrowserUtils shareInstance] isLandscape];
    BOOL isiPhone = [BrowserUtils isiPhone];
    if (isiPhone) {
        if (isLandscape) {
            return 40;
        } else {
            return 48;
        }
    } else {
        if (isLandscape) {
            return 48;
        } else {
            return 48;
        }
    }
}

//编辑框宽度, 第一个返回值，是否超出了最大值
+ (RACTuple *)widthForTextField
{
    float maxWidth = 720; // 最大宽度限制，类似于home_ipad.html
    
    //offset为理论值，解决前套调用的问题
    float offset = [CustomTagManager _leftOffsetForTextFiled];
    CGSize screenSize = [[BrowserUtils shareInstance] transitionToSize];
    float width = screenSize.width - offset*2;
    
    if (width > maxWidth) {
        return [RACTuple tupleWithObjects:@(YES), @(maxWidth), nil];
    } else {
        return RACTuplePack(@(NO), @(width));
    }
}

//编辑框左右view的宽度
+ (float)widthForTextFiledLeftRightView
{
    return [BrowserUtils isiPad] ? 50 : 45;
}

//一行显示多少个
+ (int)columnCount
{
    if([BrowserUtils isiPad]) {
        //iPad
        return 5;
    } else {
        //iPhone
        if([[BrowserUtils shareInstance] isLandscape]) {
            return 5;
        } else {
            return 4;
        }
    }
}


//一页显示多少行
- (int)rowCount
{
    
    return 2;
//    int lineCount = 2;
//    if(self.allTags.count > 0) {
//        //竖屏才有的逻辑
//        //横屏空间太小，只显示2行
//        //最多显示3行，最少显示2行
//        int columnCount = [CustomTagManager columnCount];
//        lineCount = (int)self.allTags.count / columnCount;
//        
//        lineCount = MAX(2, lineCount);
//        lineCount = MIN(3, lineCount);
//    }
//    
//    if([BrowserUtils isiPad]) {
//        if([[BrowserUtils shareInstance] isLandscape]) {
//            //横屏
//            return 2;
//        } else {
//            //竖屏(黄金分割比)
//            return lineCount;
//        }
//    } else {
//        //iPhone
//        if([[BrowserUtils shareInstance] isLandscape]) {
//            //横屏
//            return 2;
//        } else {
//            //竖屏(黄金分割比)
//            return lineCount;
//        }
//    }
//    
//    //默认2行
//    return 2;
}

//竖直方向上，行与行之间的间距
+ (float)verticalCellSpacing
{
    CGSize transitionToSize = [BrowserUtils shareInstance].transitionToSize;
    
    if([BrowserUtils isiPad]) {
        //iPad
        if(transitionToSize.width > transitionToSize.height) {
            //横屏,适配iPadmini
            return 0;
        }
        return 20;
    } else {
        //iPhone
        if([[BrowserUtils shareInstance] isLandscape]) {
            return 0.0f;
        } else {
            return 10;
        }
    }
}

//水平方向上，列与列之间的间距
+ (float)horizontalCellSpacing
{
    return 0;
}

//整个标签view左右的偏移
+ (float)customTagViewLeftOffset
{
    float offset = [self leftOffsetForTextFiled];
    if([BrowserUtils isiPad]) {
        //iPad
        return 25 + offset;
    } else {
        //iPhone
        return 5 + offset;
    }
}

//整个标签view的高度
//- (float)customTagContentViewHeight
//{
//    int rowCount = [[CustomTagManager shareInstance] rowCount];
//    
//    float cellContentHeight = [CustomTagManager cellContentHeight];
//    float verticalCellSpacing = [CustomTagManager verticalCellSpacing];
//    
//    float height = cellContentHeight*rowCount + (rowCount-1)*verticalCellSpacing;
//    return height;
//}

//整个标签view的宽度
+ (float)customTagViewWidth
{
    CGSize size = [BrowserUtils shareInstance].transitionToSize;
    return ceil(size.width - [self customTagViewLeftOffset]*2);
}

//3行时的整个标签view的高度
+ (float)customTagViewMaxHeight
{
    float cellContentHeight = [CustomTagManager cellContentHeight];
    float verticalCellSpacing = [CustomTagManager verticalCellSpacing];
    
    float height = cellContentHeight*3 + (3-1)*verticalCellSpacing;
    
    //加上page control的高度
    float pageControlheight = 25;
    float buffer = 10;
    float pageControlTopOffset = 25;
    height = height + pageControlTopOffset + pageControlheight + buffer;
    
    return height;
}

//每个标签的宽度
+ (float)cellContentWidth
{
    CGSize size = [BrowserUtils shareInstance].transitionToSize;
    float width = size.width - [self customTagViewLeftOffset]*2;
    float horizontalCellSpacing = [self horizontalCellSpacing];
    int columnCount = [self columnCount];
    return floor((width - (horizontalCellSpacing*(columnCount-1)))/(columnCount*1.0));
}

//每个标签的高度
+ (float)cellContentHeight
{
    //正方形+10,cell高度的计算这里保持差异性
    //iPad和iPhone的计算不一样, 主要是样式上好看一点

    int font = [CustomTagManager titleLabelFontSizeForCell];
    CGSize titleLabelSize = [NSString sizeWithText:@" " fontSize:font width:kScreenWidth];
    
    float spacing = [CustomTagManager spacingBetweenlogoAndLabelForCell];
    
    float topOffset = [CustomTagManager topOffsetForCell];
    float bottomOffset = 5;
    
    float height = topOffset + [CustomTagManager imageSizeForCell] + spacing + titleLabelSize.height + bottomOffset;
    return height;
}

//标签的标题字体大小
+ (float)titleLabelFontSizeForCell
{
    float font = iPadValue(16, 12);
    return font;
}

//标签中图片和标题之间的间距
+ (float)spacingBetweenlogoAndLabelForCell
{
    float spacing = 25;
    if([BrowserUtils isiPad]) {
        //logo和标题之间的间距,如果是iPad,那么调大一点
        spacing = 35;
    }
    
    return spacing;
}

//标签中图标的大小
+ (float)imageSizeForCell
{
    return 60;
}

//标签中，图标距离顶部偏移距离
+ (float)topOffsetForCell
{
    return 15;
}


@end
