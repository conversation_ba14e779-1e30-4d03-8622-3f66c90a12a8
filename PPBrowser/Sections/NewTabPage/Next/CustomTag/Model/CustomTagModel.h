//
//  CustomTagModel.h
//  PPBrowser
//
//  Created by qingbin on 2022/5/26.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import "PPEnums.h"
#import "BaseModel.h"

#import <CloudKit/CloudKit.h>
#import "SyncProtocol.h"

NS_ASSUME_NONNULL_BEGIN

@interface CustomTagModel : BaseModel<SyncProtocol>

@property (nonatomic, strong) NSString *uuid;

@property (nonatomic, strong) NSString *title;

@property (nonatomic, strong) NSString *targetUrl;

@property (nonatomic, assign) CustomTagType type;

@property (nonatomic, assign) int ppOrder;
//图标
@property (nonatomic, strong) NSString* iconUrl;
//添加时间
@property (nonatomic, strong) NSString *ctime;
// 更新时间，和iCloud的对比
@property (nonatomic, strong) NSString *updateTime;

//辅助
//主要用来获取icon
@property (nonatomic, assign) NSInteger index;

@property (nonatomic, assign) BOOL isEditting;
//请求失败，那么下次则不再请求，但是不保存到数据库，有可能网络问题
@property (nonatomic, assign) BOOL isInvalidUrl;

//v2.6.8, 是否是内置类型
- (BOOL)isBuiltIn;

//添加
//+ (instancetype)add;
//书签
+ (instancetype)bookMark;
//via插件
//+ (instancetype)via;
//greasyfork插件
//+ (instancetype)greasyfork;
//cupfox
//+ (instancetype)cupfox;
//用户指南
+ (instancetype)guideline;
//秘塔AI
+ (instancetype)metaso;
//Wikipedia
+ (instancetype)wikipedia;
//YouTube
+ (instancetype)youtube;
//Twitter
+ (instancetype)twitter;
//reddit
+ (instancetype)reddit;
//temu
+ (instancetype)temu;
//greasyfork
+ (instancetype)greasyfork;
//今日热榜
+ (instancetype)tophub;
//gfmirror, v2.6.6
+ (instancetype)gfmirror;

//CloudKit同步
//获取一个默认的CKRecord
- (CKRecord *)toDefaultCKRecord;
//转换成CKRecord
- (CKRecord *)toCKRecord;
//从CKRecord转换为CustomTagModel
- (instancetype)initWithCKRecord:(CKRecord *)record;
//判断两个是否相同
- (BOOL)objectIsEqualTo:(id)obj;
//返回uuid
- (NSString*)getUuid;
//更新时间
- (NSString*)getUpdateTime;

@end

NS_ASSUME_NONNULL_END
