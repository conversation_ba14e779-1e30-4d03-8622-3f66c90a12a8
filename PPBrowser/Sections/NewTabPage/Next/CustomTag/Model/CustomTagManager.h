//
//  CustomTagManager.h
//  PPBrowser
//
//  Created by qingbin on 2024/8/3.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "ReactiveCocoa.h"

/// 首页标签显示的管理类（负责判断显示2行还是3行的逻辑）
@interface CustomTagManager : NSObject

+ (instancetype)shareInstance;

//编辑框高度
+ (float)heightForTextField;
//编辑框宽度, 第一个返回值，是否超出了最大值
+ (RACTuple *)widthForTextField;
//编辑框距离顶部间距
//- (float)topOffsetForTextField;
//编辑框距离左边间距(注意iPad版，有居中对齐的逻辑)
+ (float)leftOffsetForTextFiled;
//编辑框左右view的宽度
+ (float)widthForTextFiledLeftRightView;


//一行显示多少个
+ (int)columnCount;
//一页显示多少行
- (int)rowCount;

//竖直方向上，行与行之间的间距
+ (float)verticalCellSpacing;
//水平方向上，列与列之间的间距
+ (float)horizontalCellSpacing;

//每个标签的宽度
+ (float)cellContentWidth;
//每个标签的高度
+ (float)cellContentHeight;
//标签的标题字体大小
+ (float)titleLabelFontSizeForCell;
//标签中图片和标题之间的间距
+ (float)spacingBetweenlogoAndLabelForCell;
//标签中图标的大小
+ (float)imageSizeForCell;
//标签中，图标距离顶部偏移距离
+ (float)topOffsetForCell;

//整个标签view左右的偏移
+ (float)customTagViewLeftOffset;
//整个标签view的宽度
+ (float)customTagViewWidth;
//整个标签view的高度
//- (float)customTagContentViewHeight;


@end


