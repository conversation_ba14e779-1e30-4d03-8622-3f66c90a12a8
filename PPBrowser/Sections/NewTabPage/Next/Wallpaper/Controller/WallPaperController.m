//
//  WallPaperController.m
//  PPBrowser
//
//  Created by qingbin on 2022/7/30.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "WallPaperController.h"

#import "MaizyHeader.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"

#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "NSObject+Helper.h"
#import "NSString+Helper.h"
#import "UIView+FrameHelper.h"
#import "CustomButton.h"

#import "NewTabPageView.h"
//#import "BottomToolbar.h"
//#import "TopToolbar.h"

#import "NSObject+Helper.h"
#import "WallpaperModel.h"

#import "PreferenceManager.h"

//#import "SDImageCache.h"
//#import "SDWebImageManager.h"

#import "PPNotifications.h"
#import "ThemeProtocol.h"
#import "WallPaperManager.h"

#import "BrowserUtils.h"

@interface WallPaperController ()<UINavigationControllerDelegate,UIImagePickerControllerDelegate,ThemeProtocol>

@property (nonatomic, strong) UIView *contentView;
@property (nonatomic, strong) NewTabPageView *ntpView;
//@property (nonatomic, strong) BottomToolbar *toolBar;

//底部工具栏
@property (nonatomic, strong) UIStackView* stackView;
@property (nonatomic, strong) CustomButton* colorBtn;
@property (nonatomic, strong) CustomButton* resetBtn;
//@property (nonatomic, strong) CustomButton* logoBtn;
@property (nonatomic, strong) CustomButton* inputBtn;
@property (nonatomic, strong) UIView* customMaskView;

@property (nonatomic, strong) WallpaperModel* model;

@property (nonatomic, strong) WallpaperModel* originalModel;

@property (nonatomic, strong) UIButton* rightButton;

@end

@implementation WallPaperController

- (void)viewDidLoad
{
    [super viewDidLoad];

    self.title = NSLocalizedString(@"homeSetting.wallpaper.text", nil);
    self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    
    self.model = [PreferenceManager shareInstance].wallpaperModel;
    [self generateOriginalModel];
    
    [self addSubviews];
    [self defineLayout];
    [self setupObservers];
    
    [self createCustomLeftBarButtonItem];
    [self _createCustomRightBarButtonItem];
    
    [self updateWithModel];
    
    [self applyTheme];
}

- (void)viewWillLayoutSubviews
{
    [super viewWillLayoutSubviews];
    
    [self.contentView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.view);
        make.width.mas_equalTo(self.view.width);
        
        make.top.equalTo(self.view);
        make.bottom.equalTo(self.stackView.mas_top);
    }];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        [self.rightButton setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
        self.customMaskView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
                
        self.colorBtn.tintColor = UIColor.whiteColor;
        [self.colorBtn setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
        
        self.resetBtn.tintColor = UIColor.whiteColor;
        [self.resetBtn setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
        
        self.inputBtn.tintColor = UIColor.whiteColor;
        [self.inputBtn setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
    } else {
        self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        [self.rightButton setTitleColor:[UIColor colorWithHexString:@"#2D7AFE"] forState:UIControlStateNormal];
        self.customMaskView.backgroundColor = UIColor.whiteColor;
        
        self.colorBtn.tintColor = [UIColor colorWithHexString:@"#333333"];
        [self.colorBtn setTitleColor:[UIColor colorWithHexString:@"#333333"] forState:UIControlStateNormal];
        
        self.resetBtn.tintColor = [UIColor colorWithHexString:@"#333333"];
        [self.resetBtn setTitleColor:[UIColor colorWithHexString:@"#333333"] forState:UIControlStateNormal];
        
        self.inputBtn.tintColor = [UIColor colorWithHexString:@"#333333"];
        [self.inputBtn setTitleColor:[UIColor colorWithHexString:@"#333333"] forState:UIControlStateNormal];
    }
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    //只有当前的controller会触发一次, 其它已存在的controller走通知
    if(needReload) {
        //修改暗黑模式
        [self applyTheme];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}

#pragma mark -- 屏幕旋转适配
- (void)viewWillTransitionToSize:(CGSize)size
       withTransitionCoordinator:(id<UIViewControllerTransitionCoordinator>)coordinator
{
    //屏幕旋转
    [BrowserUtils shareInstance].transitionToSize = size;
    [BrowserUtils shareInstance].transitionToSize = size;
    
    //屏幕旋转
    [self.ntpView updateForTransition];
}

#pragma mark -- 生成备份model,一切的修改都要等确定之后才会生效
- (void)generateOriginalModel
{
    self.originalModel = [WallpaperModel new];
    self.originalModel.wp_showLogo = self.model.wp_showLogo;
    self.originalModel.wp_lightColor = self.model.wp_lightColor;
    self.originalModel.wp_hasWallpaper = self.model.wp_hasWallpaper;
//    self.originalModel.wallpaper = self.model.wallpaper;
}

#pragma mark -- 根据model更新状态
- (void)updateWithModel
{
    //深色/浅色图标
    BOOL wp_lightColor = [self.model.wp_lightColor boolValue];
    if(wp_lightColor) {
        //切换到白色图标
        [self.colorBtn setTitle:NSLocalizedString(@"wallpaper.color.dark.text", nil) forState:UIControlStateNormal];
    } else {
        //切换到深色图标
        [self.colorBtn setTitle:NSLocalizedString(@"wallpaper.color.white.text", nil) forState:UIControlStateNormal];
    }
    
    //隐藏logo
//    BOOL wp_showLogo = [self.model.wp_showLogo boolValue];
//    if(!wp_showLogo) {
//        //隐藏logo
//        [self.logoBtn setImage:[UIImage imageNamed:@"wallpaper_showlogo_icon"] forState:UIControlStateNormal];
//        [self.logoBtn setTitle:@"显示logo" forState:UIControlStateNormal];
//    } else {
//        //显示logo
//        [self.logoBtn setImage:[UIImage imageNamed:@"wallpaper_hidelogo_icon"] forState:UIControlStateNormal];
//        [self.logoBtn setTitle:@"隐藏logo" forState:UIControlStateNormal];
//    }
    
    [self.ntpView updateWithWallpaper:self.model];
//    [self.toolBar updateWithWallpaper:self.model];
    
    //假数据
//    [self.toolBar updateTabCount:10];
}

#pragma mark -- 重置model
- (void)restoreModel
{
    [PreferenceManager shareInstance].wallpaperModel = self.originalModel;
}

#pragma mark -- 导航栏

- (void)leftBarbuttonClick
{
    //没有点击确定，重置model
    [self restoreModel];
    
    [self.navigationController popViewControllerAnimated:YES];
}

- (void)_createCustomRightBarButtonItem
{
    UIButton* rightButton = [[UIButton alloc] initWithFrame:CGRectMake(0.0, 0.0f, 44.0f, 44.0)];
    [rightButton setBackgroundColor:[UIColor clearColor]];

    rightButton.titleLabel.font = [UIFont systemFontOfSize:16.0];
    [rightButton setTitle:NSLocalizedString(@"wallpaper.navc.confirm.text", nil) forState:UIControlStateNormal];
    [rightButton setTitleColor:[UIColor colorWithHexString:@"#2D7AFE"] forState:UIControlStateNormal];
    [rightButton addTarget:self action:@selector(rightBarbuttonClick) forControlEvents:UIControlEventTouchUpInside];
    UIBarButtonItem* barItem = [[UIBarButtonItem alloc] initWithCustomView:rightButton];
    UIBarButtonItem *spacer = [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemFixedSpace target:nil action:nil];
    spacer.width = -16.0f; // for example shift right bar button to the right

    self.rightButton = rightButton;
    
    self.navigationItem.rightBarButtonItems = @[spacer, barItem];
}

- (void)rightBarbuttonClick
{
    //重新选择了壁纸
    if([self.model.wp_hasWallpaper boolValue] && self.model.wallpaper) {
        //清缓存
//        [[SDWebImageManager sharedManager].imageCache removeImageForKey:kWallpaperKey];
//        [[SDWebImageManager sharedManager] saveImageToCache:self.model.wallpaper forURL:[NSURL URLWithString:kWallpaperKey]];
        
        [[WallPaperManager shareInstance] saveImage:self.model.wallpaper];
    }
        
    //确定,保存设置
    [[PreferenceManager shareInstance] encode];
    
    [[NSNotificationCenter defaultCenter] postNotificationName:kUpdateWallpaperNotification object:nil];
    
    [self.navigationController popViewControllerAnimated:YES];
}

- (void)setupObservers
{
    @weakify(self)
    [[self.resetBtn rac_signalForControlEvents:UIControlEventTouchUpInside]
    subscribeNext:^(id x) {
        @strongify(self)
        //重置
        [self resetStatus];
    }];
    
    [[[self.colorBtn rac_signalForControlEvents:UIControlEventTouchUpInside] throttle:0.2]
    subscribeNext:^(id x) {
        @strongify(self)
        //深色/浅色图标
        BOOL wp_lightColor = [self.model.wp_lightColor boolValue];
        wp_lightColor = !wp_lightColor;
        
        if(wp_lightColor) {
            //切换到白色图标
            [self.colorBtn setTitle:NSLocalizedString(@"wallpaper.color.dark.text", nil) forState:UIControlStateNormal];
        } else {
            //切换到深色图标
            [self.colorBtn setTitle:NSLocalizedString(@"wallpaper.color.white.text", nil) forState:UIControlStateNormal];
        }
        
        self.model.wp_lightColor = @(wp_lightColor);
        
        [self.ntpView updateWithWallpaper:self.model];
//        [self.toolBar updateWithWallpaper:self.model];
    }];
    
//    [[[self.logoBtn rac_signalForControlEvents:UIControlEventTouchUpInside] throttle:0.2]
//    subscribeNext:^(id x) {
//        @strongify(self)
//        //隐藏logo
//        BOOL wp_showLogo = [self.model.wp_showLogo boolValue];
//        wp_showLogo = !wp_showLogo;
//
//        if(!wp_showLogo) {
//            //隐藏logo
//            [self.logoBtn setImage:[UIImage imageNamed:@"wallpaper_showlogo_icon"] forState:UIControlStateNormal];
//            [self.logoBtn setTitle:@"显示logo" forState:UIControlStateNormal];
//        } else {
//            //显示logo
//            [self.logoBtn setImage:[UIImage imageNamed:@"wallpaper_hidelogo_icon"] forState:UIControlStateNormal];
//            [self.logoBtn setTitle:@"隐藏logo" forState:UIControlStateNormal];
//        }
//
//        self.model.wp_showLogo = @(wp_showLogo);
//        [self.ntpView updateWithWallpaper:self.model];
//    }];
    
    [[self.inputBtn rac_signalForControlEvents:UIControlEventTouchUpInside]
    subscribeNext:^(id x) {
        @strongify(self)
        //打开相册
        [self openAlbum];
    }];
}

- (void)resetStatus
{
    self.model.wp_lightColor = @(NO);
    [self.colorBtn setTitle:NSLocalizedString(@"wallpaper.color.white.text", nil) forState:UIControlStateNormal];
    
//    self.model.wp_showLogo = @(YES);
//    [self.logoBtn setImage:[UIImage imageNamed:@"wallpaper_hidelogo_icon"] forState:UIControlStateNormal];
//    [self.logoBtn setTitle:@"隐藏logo" forState:UIControlStateNormal];
    
    self.model.wp_hasWallpaper = @(NO);
    self.model.wallpaper = nil;
    
    [self.ntpView updateWithWallpaper:self.model];
//    [self.toolBar updateWithWallpaper:self.model];
}

- (void)openAlbum
{
    UIImagePickerController *imagePickerController = [UIImagePickerController new];
    imagePickerController.delegate = self;
    imagePickerController.navigationBar.translucent = NO;
    imagePickerController.sourceType = UIImagePickerControllerSourceTypePhotoLibrary;
    
    @weakify(self)
    [NSObject requestAuthorizationWithAuthorizedBlock:^{
        // 点击拍照或从相册选择
        @strongify(self)
        
        @weakify(self)
        [[NSOperationQueue mainQueue] addOperationWithBlock:^{
            @strongify(self)
            [self presentViewController:imagePickerController animated:YES completion:nil];
        }];
    } rejectBlock:nil];
}

#pragma mark UIImagePickerControllerDelegate
- (void)imagePickerController:(UIImagePickerController *)picker didFinishPickingMediaWithInfo:(NSDictionary<NSString *, id> *)info
{
    UIImage *originImage = [info valueForKey:UIImagePickerControllerOriginalImage];
    [picker dismissViewControllerAnimated:YES completion:nil];
    
    self.model.wp_hasWallpaper = @(YES);
    self.model.wallpaper = originImage;
    
    [self.ntpView updateWithWallpaper:self.model];
//    [self.toolBar updateWithWallpaper:self.model];
}

#pragma mark -- layout
- (void)addSubviews
{
    [self.view addSubview:self.contentView];
    [self.contentView addSubview:self.ntpView];
//    [self.contentView addSubview:self.toolBar];
    
    [self.view addSubview:self.customMaskView];
    [self.view addSubview:self.stackView];
}

- (void)defineLayout
{
//    UIWindow* window = YBIBNormalWindow();
    
    float width = self.view.width-30-30;
    
    float scale = width / (self.view.width*1.0);
    self.contentView.transform = CGAffineTransformMakeScale(scale, scale);
    
    [self.contentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.view);
        make.width.mas_equalTo(self.view.width);
        
        make.top.equalTo(self.view);
        make.bottom.equalTo(self.stackView.mas_top);
    }];
    
    [self.ntpView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.contentView);
    }];
    
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.view);
        make.height.mas_equalTo(iPadValue(80, 60));
        make.bottom.equalTo(self.view.mas_safeAreaLayoutGuideBottom);
    }];
    
    [self.customMaskView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.left.right.equalTo(self.view);
        make.top.equalTo(self.stackView.mas_top);
    }];
}

- (UIView *)contentView
{
    if(!_contentView) {
        _contentView = [UIView new];
        _contentView.backgroundColor = UIColor.whiteColor;
        _contentView.layer.cornerRadius = 10;
        _contentView.layer.masksToBounds = YES;
    }
    
    return _contentView;
}

- (NewTabPageView *)ntpView
{
    if(!_ntpView) {
        _ntpView = [[NewTabPageView alloc]init];
        _ntpView.userInteractionEnabled = NO;
        [_ntpView hideControlsInSetWallpaper];
    }
    
    return _ntpView;
}

- (UIView *)customMaskView
{
    if(!_customMaskView) {
        _customMaskView = [UIView new];
        _customMaskView.backgroundColor = UIColor.whiteColor;
    }
    
    return _customMaskView;
}
- (UIStackView *)stackView
{
    if(!_stackView) {
        _stackView = [[UIStackView alloc]initWithArrangedSubviews:@[
            self.resetBtn,
            self.colorBtn,
//            self.logoBtn,
            self.inputBtn
        ]];
        
        _stackView.backgroundColor = UIColor.clearColor;
        _stackView.axis = UILayoutConstraintAxisHorizontal;
        _stackView.distribution = UIStackViewDistributionFillEqually;
        
//        UIView* line = [UIView new];
//        line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
//        [_stackView addSubview:line];
//        [line mas_makeConstraints:^(MASConstraintMaker *make) {
//            make.left.right.top.mas_offset(0);
//            make.height.mas_equalTo(0.5);
//        }];
    }
    
    return _stackView;
}

- (CustomButton *)resetBtn
{
    if(!_resetBtn) {
        _resetBtn = [self createButtonWithImageName:@"wallpaper_reset_icon" title:NSLocalizedString(@"wallpaper.toolbar.reset.text", nil)];
    }
    
    return _resetBtn;
}

- (CustomButton *)colorBtn
{
    if(!_colorBtn) {
        _colorBtn = [self createButtonWithImageName:@"wallpaper_color_icon" title:NSLocalizedString(@"wallpaper.color.white.text", nil)];
    }
    
    return _colorBtn;
}

//- (CustomButton *)logoBtn
//{
//    if(!_logoBtn) {
//        _logoBtn = [self createButtonWithImageName:@"wallpaper_hidelogo_icon" title:@"隐藏logo"];
//        [_logoBtn setTitleColor:[UIColor colorWithHexString:@"#333333"] forState:UIControlStateNormal];
//        _logoBtn.tintColor = [UIColor colorWithHexString:@"#333333"];
//    }
//
//    return _logoBtn;
//}

- (CustomButton *)inputBtn
{
    if(!_inputBtn) {
        _inputBtn = [self createButtonWithImageName:@"wallpaper_album_icon" title:NSLocalizedString(@"wallpaper.toolbar.importPhoto.text", nil)];
        
        [_inputBtn setTitleColor:[UIColor colorWithHexString:@"#333333"] forState:UIControlStateNormal];
        _inputBtn.tintColor = [UIColor colorWithHexString:@"#333333"];
    }
    
    return _inputBtn;
}

- (CustomButton*)createButtonWithImageName:(NSString*)imageName
                   title:(NSString*)title
{
    CustomButton* button = [CustomButton new];
    UIImage* image = [[UIImage imageNamed:imageName] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
    [button setTitle:title forState:UIControlStateNormal];
    [button setImage:image forState:UIControlStateNormal];
    
    float font = iPadValue(18, 12);
    button.titleLabel.font = [UIFont systemFontOfSize:font];
    button.titleLabel.textAlignment = NSTextAlignmentCenter;
    [button setTitleColor:[UIColor colorWithHexString:@"#333333"] forState:UIControlStateNormal];
    button.tintColor = [UIColor colorWithHexString:@"#333333"];
    button.backgroundColor = UIColor.clearColor;
    
    float width = kScreenWidth/3.0;
    
    float spacing = iPadValue(10, 6);
    float imageW  = 24;
    float labelW = [NSString sizeWithText:title fontSize:font width:kScreenWidth].width;
    float labelH = [NSString sizeWithText:title fontSize:font width:kScreenWidth].height;
    float imageX = (width-imageW)/2.0;
    float labelX = (width-labelW)/2.0;
    button.imageRect = CGRectMake(imageX, iPadValue(15, 10), imageW, imageW);
    button.titleRect = CGRectMake(labelX, CGRectGetMaxY(button.imageRect)+spacing, labelW, labelH);

    return button;
}

@end
