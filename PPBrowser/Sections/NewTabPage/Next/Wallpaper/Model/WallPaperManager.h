//
//  WallPaperManager.h
//  PPBrowser
//
//  Created by qingbin on 2022/8/28.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import "WallpaperModel.h"

#import "SDImageCache.h"
#import "SDWebImageManager.h"

NS_ASSUME_NONNULL_BEGIN

@interface WallPaperManager : NSObject

+ (instancetype)shareInstance;

- (void)saveImage:(UIImage *)image;

- (void)queryWallPaper:(void (^)(UIImage* image))completionBlock;

@end

NS_ASSUME_NONNULL_END
