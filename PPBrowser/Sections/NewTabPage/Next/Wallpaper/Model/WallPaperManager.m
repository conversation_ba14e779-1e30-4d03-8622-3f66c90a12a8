//
//  WallPaperManager.m
//  PPBrowser
//
//  Created by qingbin on 2022/8/28.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "WallPaperManager.h"
#import "UIImage+MultiFormat.h"
#import "SDWebImageDecoder.h"
#import "SDWebImageCompat.h"

@interface WallPaperManager ()

@property (nonatomic, strong) dispatch_queue_t queue;

@property (nonatomic, strong) NSString *diskCachePath;

@end

@implementation WallPaperManager

+ (instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    static WallPaperManager* obj;
    dispatch_once(&onceToken, ^{
        obj = [WallPaperManager new];
    });
    
    return obj;
}

- (instancetype)init
{
    self = [super init];
    if(self) {
        self.queue = dispatch_queue_create("com.wallpaper.queue", DISPATCH_QUEUE_SERIAL);
        
        // Init the disk cache
        NSArray *paths = NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES);
        self.diskCachePath = [paths[0] stringByAppendingPathComponent:@"com.focus.wallpaper"];
    }
    
    return self;
}

- (void)queryWallPaper:(void (^)(UIImage* image))completionBlock
{
    [[SDWebImageManager sharedManager].imageCache queryDiskCacheForKey:kWallpaperKey done:^(UIImage *image, SDImageCacheType cacheType) {
        if(image) {
            if(completionBlock) {
                completionBlock(image);
            }
            
            [self _saveToDiskIfNeeded:image];
        } else {
            //sdwebimage缓存中数据不存在，从本地数据拿
            dispatch_async(self.queue, ^{
                NSString* filePath = [self filePath];
                NSFileManager* fileManager = [NSFileManager new];
                    
                if([fileManager fileExistsAtPath:filePath]) {
                    //本地文件缓存
                    NSData *data = [NSData dataWithContentsOfFile:filePath];
                    if (data) {
                        UIImage *image = [UIImage sd_imageWithData:data];
                        //缓存到sdWebimage中
                        [self saveImage:image];
                        
                        image = SDScaledImageForKey(kWallpaperKey,image);
                        image = [UIImage decodedImageWithImage:image];
                        
                        if(completionBlock) {
                            completionBlock(image);
                        }
                    }
                } else {
                    //本地文件没有缓存, 需要重置壁纸
                    if(completionBlock) {
                        completionBlock(nil);
                    }
                }
            });
        }
    }];
}

- (void)saveImage:(UIImage *)image
{
    dispatch_async(self.queue, ^{
        //清sdwebimage缓存
        [[SDWebImageManager sharedManager].imageCache removeImageForKey:kWallpaperKey];
        //保存到sdwebimage中
        [[SDWebImageManager sharedManager] saveImageToCache:image forURL:[NSURL URLWithString:kWallpaperKey]];
        
        //保存到本地文件中
        NSData* data;
        NSFileManager* fileManager = [NSFileManager new];
#if TARGET_OS_IPHONE
        data = UIImageJPEGRepresentation(image, (CGFloat)1.0);
#else
        data = [NSBitmapImageRep representationOfImageRepsInArray:image.representations usingType: NSJPEGFileType properties:nil];
#endif

        if (data) {
            if (![fileManager fileExistsAtPath:self.diskCachePath]) {
                [fileManager createDirectoryAtPath:self.diskCachePath withIntermediateDirectories:YES attributes:nil error:NULL];
            }
            
            NSString* filePath = [self filePath];
            
            NSLog(@"壁纸保存路径: %@", filePath);
            
            //清除本地缓存
            if([fileManager fileExistsAtPath:filePath]) {
                [fileManager removeItemAtPath:filePath error:nil];
            }
            
            [fileManager createFileAtPath:filePath contents:data attributes:nil];
        }
    });
}

- (void)_saveToDiskIfNeeded:(UIImage *)image
{
    dispatch_async(self.queue, ^{
        NSString* filePath = [self filePath];
        NSFileManager* fileManager = [NSFileManager new];
        
        //本地文件没有缓存
        if(![fileManager fileExistsAtPath:filePath]) {
            NSData* data;
    #if TARGET_OS_IPHONE
            data = UIImageJPEGRepresentation(image, (CGFloat)1.0);
    #else
            data = [NSBitmapImageRep representationOfImageRepsInArray:image.representations usingType: NSJPEGFileType properties:nil];
    #endif
            [fileManager createFileAtPath:filePath contents:data attributes:nil];
        }
    });
}

- (NSString *)filePath
{
    return [self.diskCachePath stringByAppendingPathComponent:kWallPaper];
}

@end
