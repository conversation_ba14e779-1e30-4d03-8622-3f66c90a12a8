//
//  AddCustomTagController.m
//  PPBrowser
//
//  Created by qingbin on 2022/5/31.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "AddCustomTagController.h"

#import "MaizyHeader.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"

#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "NSObject+Helper.h"
#import "UIView+FrameHelper.h"

#import "DatabaseUnit+CustomTag.h"
#import "PPNotifications.h"

#import "MetadataHelper.h"
#import "NSURL+Extension.h"

#import "DatabaseUtil.h"
#import "UIImageView+WebCache.h"

#import "ThemeProtocol.h"
#import "PreferenceManager.h"
#import "CustomClearButtonTextField.h"

#import "BrowserUtils.h"
#import "DatabaseUnit+BookMark.h"

@interface AddCustomTagController ()<UITextFieldDelegate,ThemeProtocol>

@property (nonatomic, strong) CustomClearButtonTextField *titleTextField;

@property (nonatomic, strong) CustomClearButtonTextField *urlTextField;

@property (nonatomic, strong) CustomTagModel* model;

@property (nonatomic, weak) WKWebView *webView;

@property (nonatomic, strong) UIButton* rightButton;

/// 编辑书签
@property (nonatomic, strong) BookMarkModel* bookMarkModel;
//
@property (nonatomic, copy) void(^completion)(void);
@end

@implementation AddCustomTagController

/// 编辑书签
- (instancetype)initWithBookMark:(BookMarkModel *)model completion:(void(^)(void))completion
{
    self = [super init];
    if(self) {
        self.bookMarkModel = model;
        self.completion = completion;
    }
    
    return self;
}

- (instancetype)initWithCustomTag:(CustomTagModel*)model
                          webView:(WKWebView*)webView
{
    self = [super init];
    if(self) {
        self.model = model;
        self.webView = webView;
    
        [UIView showLoading:NSLocalizedString(@"tips.handling", nil)];
        
        [[NSNotificationCenter defaultCenter] addObserver:self
                                                 selector:@selector(getMetadataNotification:)
                                                     name:kGetMetadataNotification
                                                   object:nil];
        
        [MetadataHelper getMetadataWithWebView:webView];
    }
    
    return self;
}

- (void)viewDidLoad
{
    [super viewDidLoad];

    if(self.model) {
        //添加到首页
        self.title = NSLocalizedString(@"addCustomTag.title", nil);
        
        self.titleTextField.text = self.model.title;
        self.urlTextField.text = self.model.targetUrl;
    } else if(self.bookMarkModel) {
        //编辑书签
        self.title = NSLocalizedString(@"addCustomTag.bookmark.title", nil);
        
        self.titleTextField.text = self.bookMarkModel.title;
        self.urlTextField.text = self.bookMarkModel.url;
    }
    
    [self addSubviews];
    [self defineLayout];
    [self setupObservers];
    
    [self createCustomLeftBarButtonItem];
    [self _createCustomRightBarButtonItem];
    
    [self applyTheme];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        [self.leftButton setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
        [self.rightButton setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
        
        self.titleTextField.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        self.titleTextField.textColor = UIColor.whiteColor;
        
        self.urlTextField.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
        self.urlTextField.textColor = UIColor.whiteColor;
    } else {
        self.view.backgroundColor = [UIColor colorWithHexString:@"#ffffff"];
        [self.leftButton setTitleColor:[UIColor colorWithHexString:@"#2D7AFE"] forState:UIControlStateNormal];
        [self.rightButton setTitleColor:[UIColor colorWithHexString:@"#2D7AFE"] forState:UIControlStateNormal];
        
        self.titleTextField.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.titleTextField.textColor = [UIColor colorWithHexString:@"#333333"];
        
        self.urlTextField.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        self.urlTextField.textColor = [UIColor colorWithHexString:@"#333333"];
    }
}

#pragma mark -- 收到通知
- (void)getMetadataNotification:(NSNotification*)notification
{
    [UIView hideHud:NO];
    
    WKWebView* webView = notification.userInfo[@"webView"];
    if(webView != self.webView) return;
    
    NSDictionary* params = notification.object;
    
    NSString* iconUrl = params[@"url"];
    if(iconUrl.length > 0) {
        self.model.iconUrl = iconUrl;
    }
    
    if(self.model.iconUrl.length > 0) {
        [[DatabaseUtil shareInstance] asyncOnQueue:^{
            //同步
            NSURL* url = [NSURL URLWithString:self.model.iconUrl];
            NSURLRequest *request = [[NSURLRequest alloc]initWithURL:url cachePolicy:NSURLRequestUseProtocolCachePolicy timeoutInterval:30];
            NSError *error;
            NSData *receiveData = [NSURLConnection sendSynchronousRequest:request returningResponse:nil error:&error];
            if(!error && receiveData) {
                UIImage* image = [UIImage imageWithData:receiveData];
                if(image.size.width<42 || image.size.height<42) {
                    //图片质量太差,放弃
                    self.model.iconUrl = @"";
                } else {
                    //保存图片到sdwebimage缓存中
                    if(image) {
                        [[SDImageCache sharedImageCache] storeImage:image forKey:self.model.iconUrl];
                    }
                }
            } else {
                //出错,那么放弃这个logo
                self.model.iconUrl = @"";
            }
        }];
    }
}

- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event
{
    [self.view endEditing:YES];
}

#pragma mark -- UITextFieldDelegate
- (void)textFieldDidEndEditing:(UITextField *)textField
{
    if(textField == self.urlTextField) {
        self.model.targetUrl = textField.text;
        self.bookMarkModel.url = textField.text;
    } else if(textField == self.titleTextField) {
        self.model.title = textField.text;
        self.bookMarkModel.title = textField.text;
    }
}

#pragma mark -- layout
- (void)addSubviews
{
    [self.view addSubview:self.titleTextField];
    [self.view addSubview:self.urlTextField];
}

- (void)defineLayout
{
    [self.view addSubview:self.titleTextField];
    [self.view addSubview:self.urlTextField];
}

- (void)setupObservers
{
    float height = iPadValue(60, 44);
    float topOffset = iPadValue(30, 20);
    float leftOffset = iPadValue(30, 15);
    
    [self.titleTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_offset(topOffset);
        make.left.mas_offset(leftOffset);
        make.right.mas_offset(-leftOffset);
        make.height.mas_equalTo(height);
    }];
    
    [self.urlTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.titleTextField.mas_bottom).offset(leftOffset);
        make.left.mas_offset(leftOffset);
        make.right.mas_offset(-leftOffset);
        make.height.mas_equalTo(height);
    }];
}

#pragma mark -- 导航栏

- (void)_createCustomRightBarButtonItem
{
    UIButton* rightButton = [[UIButton alloc] initWithFrame:CGRectMake(0.0, 0.0f, 44.0f, 44.0)];
    [rightButton setBackgroundColor:[UIColor clearColor]];

    rightButton.titleLabel.font = [UIFont systemFontOfSize:16.0];
    [rightButton setTitle:NSLocalizedString(@"addCustomTag.save", nil) forState:UIControlStateNormal];
    [rightButton setTitleColor:[UIColor colorWithHexString:@"#2D7AFE"] forState:UIControlStateNormal];
    [rightButton addTarget:self action:@selector(rightBarbuttonClick) forControlEvents:UIControlEventTouchUpInside];
    UIBarButtonItem* barItem = [[UIBarButtonItem alloc] initWithCustomView:rightButton];
    UIBarButtonItem *spacer = [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemFixedSpace target:nil action:nil];
    spacer.width = -16.0f; // for example shift right bar button to the right

    self.rightButton = rightButton;
    
    self.navigationItem.rightBarButtonItems = @[spacer, barItem];
}

- (void)rightBarbuttonClick
{
    [self.view endEditing:YES];
    
    if(self.model) {
        //添加到首页
        //保存
        DatabaseUnit* unit = [DatabaseUnit addCustomTagWithItem:self.model];
        DB_EXEC(unit);

        [[NSNotificationCenter defaultCenter] postNotificationName:kAddHomeCustomTagNotification object:self.model];
        
        [UIView showSucceed:NSLocalizedString(@"addCustomTag.addSucceed", nil)];
        
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self dismissViewControllerAnimated:YES completion:nil];
        });
    } else if(self.bookMarkModel) {
        
        if(self.bookMarkModel.title.length == 0) {
            [UIView showWarning:NSLocalizedString(@"bookmark.renameTips", nil)];
            return;
        }
        
        //编辑书签
        DatabaseUnit* unit = [DatabaseUnit updateBookMarkWithId:self.bookMarkModel.bookmarkId 
                                                          title:self.bookMarkModel.title
                                                            url:self.bookMarkModel.url
                                                       bookMark:self.bookMarkModel];
        DB_EXEC(unit);
        
        if(self.completion) {
            self.completion();
        }
        
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self dismissViewControllerAnimated:YES completion:nil];
        });
    }
}

- (void)leftBarbuttonClick
{
    [self dismissViewControllerAnimated:YES completion:nil];
}

#pragma mark -- lazy init
- (CustomClearButtonTextField *)titleTextField
{
    if(!_titleTextField) {
        float height = iPadValue(60, 44);
        float font = iPadValue(18, 14);
        
        CustomClearButtonTextField* textField = [[CustomClearButtonTextField alloc] init];
        textField.font = [UIFont systemFontOfSize:font];
        textField.leftViewRect = CGRectMake(7.0, 0, height, height);
        textField.layer.cornerRadius = height/2.0;
        textField.layer.masksToBounds = YES;
        
        textField.leftViewMode = UITextFieldViewModeAlways;
        textField.textColor = [UIColor colorWithHexString:@"#333333"];
        textField.returnKeyType = UIReturnKeySearch;
        
        [textField setBackgroundColor:[UIColor colorWithHexString:@"#f5f5f5"]];
        [textField updatePlaceHolder:NSLocalizedString(@"addCustomTag.titlePlaceholder", nil) color:[UIColor colorWithHexString:@"#999999"]];
        [textField setClearButtonMode:UITextFieldViewModeAlways];

        [textField setDelegate:self];

        UIButton* scanBtn = [[UIButton alloc] initWithFrame:CGRectMake(0.0, 0.0, height, height)];
        scanBtn.contentMode = UIViewContentModeScaleAspectFit;
        [scanBtn setImage:[UIImage imageNamed:@"navi_search_icon"] forState:UIControlStateNormal];
        
        textField.leftView = scanBtn;
        
        _titleTextField = textField;
    }
    
    return _titleTextField;
}

- (CustomClearButtonTextField *)urlTextField
{
    if(!_urlTextField) {
        float height = iPadValue(60, 44);
        float font = iPadValue(18, 14);
        
        CustomClearButtonTextField* textField = [[CustomClearButtonTextField alloc] init];
        textField.font = [UIFont systemFontOfSize:font];
        textField.leftViewRect = CGRectMake(7.0, 0, height, height);
        textField.layer.cornerRadius = height/2.0;
        textField.layer.masksToBounds = YES;
        
        textField.leftViewMode = UITextFieldViewModeAlways;
        textField.textColor = [UIColor colorWithHexString:@"#333333"];
        textField.returnKeyType = UIReturnKeySearch;
        
        [textField setBackgroundColor:[UIColor colorWithHexString:@"#f5f5f5"]];
        [textField updatePlaceHolder:NSLocalizedString(@"addCustomTag.urlPlaceholder", nil) color:[UIColor colorWithHexString:@"#999999"]];
        [textField setClearButtonMode:UITextFieldViewModeAlways];

        [textField setDelegate:self];

        UIButton* scanBtn = [[UIButton alloc] initWithFrame:CGRectMake(0.0, 0.0, height, height)];
        scanBtn.contentMode = UIViewContentModeScaleAspectFit;
        [scanBtn setImage:[UIImage imageNamed:@"navi_search_icon"] forState:UIControlStateNormal];
        
        textField.leftView = scanBtn;
        
        _urlTextField = textField;
    }
    
    return _urlTextField;
}

@end
