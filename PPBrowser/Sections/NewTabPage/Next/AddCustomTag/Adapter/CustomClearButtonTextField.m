//
//  CustomClearButtonTextField.m
//  PPBrowser
//
//  Created by qing<PERSON> on 2022/8/24.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "CustomClearButtonTextField.h"

@implementation CustomClearButtonTextField

- (CGRect)textRectForBounds:(CGRect)bounds
{
    CGRect rect = [super textRectForBounds:bounds];
    rect.size.width -= 10;
    
    return rect;
}

- (CGRect)clearButtonRectForBounds:(CGRect)bounds
{
    CGRect rect = [super clearButtonRectForBounds:bounds];
    rect.origin.x -= 10;
    
    return rect;
}

@end
