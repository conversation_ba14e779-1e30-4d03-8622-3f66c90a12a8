//
//  FavorEditController.h
//  Reader
//
//  Created by q<PERSON><PERSON> on 2023/9/28.
//

#import "BaseViewController.h"

#import "CustomTagModel.h"
#import "BookMarkModel.h"
#import "Tab.h"

@interface FavorEditController : BaseViewController

- (void)updateWithFavorModel:(CustomTagModel *)model
                         tab:(Tab *)tab;

- (void)updateWithBookMarkModel:(BookMarkModel *)model
                            tab:(Tab *)tab;

//选择了移动
@property (nonatomic, copy) void (^finishBlock)(BookMarkModel *selectTarget);

@end

