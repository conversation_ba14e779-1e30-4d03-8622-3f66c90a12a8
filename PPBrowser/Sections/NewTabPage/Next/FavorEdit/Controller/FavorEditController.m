//
//  FavorEditController.m
//  Reader
//
//  Created by qingbin on 2023/9/28.
//

#import "FavorEditController.h"

#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "UIView+FrameHelper.h"
#import "NSObject+Helper.h"
#import "NSFileManager+Helper.h"

#import "CustomTextField.h"
#import "AppButton.h"

#import "DatabaseUnit+CustomTag.h"
#import "DatabaseUnit+BookMark.h"
#import "CommonDataManager.h"

#import "BookMarkMoveController.h"
#import "MetadataHelper.h"

#import "PPNotifications.h"
#import "UIAlertController+SafePresentation.h"

@interface FavorEditController ()<UITextFieldDelegate, ThemeProtocol>
//
@property (nonatomic, strong) Tab *tab;
//首页标签
@property (nonatomic, strong) CustomTagModel *favorModel;
//书签
@property (nonatomic, strong) BookMarkModel *bookMarkModel;
//导航栏
@property (nonatomic, strong) UIButton *rightButton;
//
@property (nonatomic, strong) UIStackView *stackView;
//标题
@property (nonatomic, strong) CustomTextField *titleTextField;
//网址
@property (nonatomic, strong) CustomTextField *urlTextField;
//书签移动
@property (nonatomic, strong) CustomTextField *moveTextField;
//箭头
@property (nonatomic, strong) UIImageView *arrow;

//确认
@property (nonatomic, strong) UIButton *deleteButton;

@end

@implementation FavorEditController

- (void)viewDidLoad
{
    [super viewDidLoad];
    
    self.title = NSLocalizedString(@"customtag.edit", nil);
    
    [self addSubviews];
    [self defineLayout];
    [self setupObservers];
    
    [self createCustomLeftBarButtonItem];
    [self _createCustomRightBarButtonItem];
    
    [self updateWithModel];
    
    [self applyTheme];
}

- (void)dealloc
{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        
        NSArray* textFileds = @[self.titleTextField, self.urlTextField, self.moveTextField];
        for(CustomTextField* textField in textFileds) {
            textField.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
            AppButton* button = (AppButton*)textField.leftView;
            button.titleLabel.textColor = [UIColor colorWithHexString:@"#ffffff"];
        }
        
        self.arrow.tintColor = [UIColor colorWithHexString:@"#ffffff"];
    } else {
        self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        
        NSArray* textFileds = @[self.titleTextField, self.urlTextField, self.moveTextField];
        for(CustomTextField* textField in textFileds) {
            textField.backgroundColor = UIColor.whiteColor;
            AppButton* button = (AppButton*)textField.leftView;
            button.titleLabel.textColor = [UIColor colorWithHexString:@"#333333"];
        }
        
        self.arrow.tintColor = [UIColor colorWithHexString:@"#333333"];
    }
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    //只有当前的controller会触发一次, 其它已存在的controller走通知
    if(needReload) {
        //修改暗黑模式
        [self applyTheme];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}

- (void)updateWithFavorModel:(CustomTagModel *)model
                         tab:(Tab *)tab
{
    self.favorModel = model;
    
    //获取网页相关信息
    //有可能是编辑或者添加，编辑不需要获取icon
    if(tab) {
        [MetadataHelper getMetadataWithWebView:tab.webView];
    }
}

- (void)updateWithBookMarkModel:(BookMarkModel *)model
                            tab:(Tab *)tab
{
    self.bookMarkModel = model;
    
    if(tab) {
        [MetadataHelper getMetadataWithWebView:tab.webView];
    }
}

- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event
{
    [self.view endEditing:YES];
}

- (void)updateWithModel
{
    if(self.favorModel) {
        //首页标签
        self.titleTextField.text = self.favorModel.title;
        self.urlTextField.text = self.favorModel.targetUrl;
    } else if(self.bookMarkModel) {
        //书签
        self.titleTextField.text = self.bookMarkModel.title;
        self.moveTextField.hidden = NO;
        
        if(self.bookMarkModel.fileType == BookMarkTypeFolder) {
            //文件夹
            self.urlTextField.hidden = YES;
        } else {
            //文件
            self.urlTextField.text = self.bookMarkModel.url;
            self.urlTextField.hidden = NO;
        }
        
        if([self.bookMarkModel.parentId isEqualToString:[BookMarkModel rootId]]) {
            self.moveTextField.text = NSLocalizedString(@"home.bookMark", nil);
        } else {
            //获取parentId对应的标题
            DatabaseUnit* unit = [DatabaseUnit queryBookMarkWithId:self.bookMarkModel.parentId];
            @weakify(self)
            [unit setCompleteBlock:^(BookMarkModel* result, BOOL success) {
                @strongify(self)
                if(success) {
                    self.moveTextField.text = result.title;
                }
            }];
            DB_EXEC(unit);
        }
    }
}

- (void)setupObservers
{
    @weakify(self)
    [[self.deleteButton rac_signalForControlEvents:UIControlEventTouchUpInside]
    subscribeNext:^(id x) {
        @strongify(self)
        [self removeWithAlert];
    }];
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(handleUpdateMeta:) name:kGetMetadataNotification object:nil];
}

- (void)handleUpdateMeta:(NSNotification *)notification
{
    NSDictionary* infos = [notification object];
    self.favorModel.iconUrl = infos[@"url"];
}

#pragma mark -- 完成
- (void)finishAction
{
    [self.view endEditing:YES];
    
    //检查
    if(!self.titleTextField.hidden && self.titleTextField.text.length == 0) {
        [UIView showToast:NSLocalizedString(@"addCustomTag.titlePlaceholder", nil)];
        return;
    }
    
    if(!self.urlTextField.hidden && self.urlTextField.text.length == 0) {
        [UIView showToast:NSLocalizedString(@"addCustomTag.urlPlaceholder", nil)];
        return;
    }
    if(self.favorModel) {
        //首页标签
        self.favorModel.title = self.titleTextField.text;
        self.favorModel.targetUrl = self.urlTextField.text;
        
        DatabaseUnit* unit = [DatabaseUnit addCustomTagWithItem:self.favorModel];
        DB_EXEC(unit);
        
        //重新加载
        [[CommonDataManager shareInstance] reloadCustomTagModels];
        //刷新首页标签
        [[NSNotificationCenter defaultCenter] postNotificationName:kAddHomeCustomTagNotification object:self.favorModel];
    } else if(self.bookMarkModel) {
        //书签
        self.bookMarkModel.title = self.titleTextField.text;
        self.bookMarkModel.url = self.urlTextField.text;
        
        /// 插入数据或者更新
        DatabaseUnit* unit = [DatabaseUnit addBookMarkWithItem:self.bookMarkModel];
        
        [unit setCompleteBlock:^(id result, BOOL success) {
            [[NSNotificationCenter defaultCenter] postNotificationName:kBookMarkDidChangeNotification object:nil];
        }];
        
        DB_EXEC(unit);
        
        //重新加载
        [[CommonDataManager shareInstance] reloadBookMarks];
        
        if(self.finishBlock) {
            self.finishBlock(nil);
        }
    }
    
    [UIView showToast:NSLocalizedString(@"addCustomTag.addSucceed", nil)];
}

#pragma mark -- 删除
- (void)removeWithAlert
{
    UIAlertController* alertController = [UIAlertController alertControllerWithTitle:NSLocalizedString(@"customtag.delete.alert.title", nil) message:nil preferredStyle:UIAlertControllerStyleAlert];
    UIAlertAction* action = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.cancel", nil) style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
        
    }];
    [alertController addAction:action];
    
    action = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.confirm", nil) style:UIAlertActionStyleDestructive handler:^(UIAlertAction * _Nonnull action) {
        [self removeAction];
        [self dismissViewControllerAnimated:YES completion:nil];
    }];
    [alertController addAction:action];
    
//    [self presentViewController:alertController animated:YES completion:nil];
    //v2.6.8, 统一present方法，防止崩溃
    [alertController presentSafelyFromViewController:self];
}

- (void)removeAction
{
    [self.view endEditing:YES];
    
    if(self.favorModel) {
        //首页标签
        DatabaseUnit* unit = [DatabaseUnit removeCustomTagWithId:self.favorModel.uuid];
        DB_EXEC(unit);
        
        [UIView showToast:NSLocalizedString(@"tips.delete.success", nil)];
        
        //重新加载
        [[CommonDataManager shareInstance] reloadCustomTagModels];
        //刷新首页标签
        [[NSNotificationCenter defaultCenter] postNotificationName:kAddHomeCustomTagNotification object:self.favorModel];
    } else if(self.bookMarkModel) {
        //书签
        DatabaseUnit* unit = [DatabaseUnit removeBookMarkTreeWithIds:@[self.bookMarkModel.bookmarkId]];
        DB_EXEC(unit);
        
        [UIView showToast:NSLocalizedString(@"tips.delete.success", nil)];
        
        //重新加载
        [[CommonDataManager shareInstance] reloadBookMarks];
        
        [[NSNotificationCenter defaultCenter] postNotificationName:kBookMarkDidChangeNotification object:nil];
    }
}

#pragma mark -- 导航栏

- (void)_createCustomRightBarButtonItem
{
    UIButton* rightButton = [[UIButton alloc] initWithFrame:CGRectMake(0.0, 0.0f, 44.0f, 44.0)];
    [rightButton setBackgroundColor:[UIColor clearColor]];

    rightButton.titleLabel.font = [UIFont systemFontOfSize:16.0];
    [rightButton setTitle:NSLocalizedString(@"alert.finish", nil) forState:UIControlStateNormal];
    [rightButton setTitleColor:[UIColor colorWithHexString:@"#2D7AFE"] forState:UIControlStateNormal];
    
    [rightButton addTarget:self action:@selector(rightBarbuttonClick) forControlEvents:UIControlEventTouchUpInside];
    self.rightButton = rightButton;
    
    UIBarButtonItem* barItem = [[UIBarButtonItem alloc] initWithCustomView:rightButton];

    UIBarButtonItem *spacer = [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemFixedSpace target:nil action:nil];
    spacer.width = -16.0f; // for example shift right bar button to the right

    self.navigationItem.rightBarButtonItems = @[spacer, barItem];
}

#pragma mark -- 点击保存
- (void)rightBarbuttonClick
{
    [self finishAction];
    
    [self dismissViewControllerAnimated:YES completion:nil];
}

- (void)createCustomLeftBarButtonItem
{
    [self.view endEditing:YES];
    
    UIButton* leftButton = [[UIButton alloc] initWithFrame:CGRectMake(0.0, 0.0f, 20.0f, 44.0)];
    [leftButton setBackgroundColor:[UIColor clearColor]];
    
    leftButton.titleLabel.font = [UIFont systemFontOfSize:16.0];
    [leftButton setTitle:NSLocalizedString(@"alert.cancel", nil) forState:UIControlStateNormal];
    [leftButton setTitleColor:[UIColor colorWithHexString:@"#2D7AFE"] forState:UIControlStateNormal];
    
    [leftButton addTarget:self action:@selector(leftBarbuttonClick) forControlEvents:UIControlEventTouchUpInside];
    self.leftButton = leftButton;
    
    UIBarButtonItem* barItem = [[UIBarButtonItem alloc] initWithCustomView:leftButton];
    
    self.navigationItem.leftBarButtonItems = @[barItem];
}

- (void)leftBarbuttonClick
{
    if(self.navigationController.viewControllers.count > 1) {
        [self.navigationController popViewControllerAnimated:YES];
    } else {
        [self dismissViewControllerAnimated:YES completion:nil];
    }
}

#pragma mark -- UITextFieldDelegate
- (BOOL)textFieldShouldBeginEditing:(UITextField *)textField
{
    if(self.moveTextField == textField) {
        BookMarkMoveController* vc = [BookMarkMoveController new];
//        vc.currentBookMarkItem = self.bookMarkModel;
        vc.items = @[self.bookMarkModel];
        
        @weakify(self)
        [vc setFinishBlock:^(BookMarkModel *selectTarget){
            @strongify(self)
            self.bookMarkModel.parentId = selectTarget.bookmarkId;
            [self updateWithModel];
        }];
        
        [self.navigationController pushViewController:vc animated:YES];
        
        return NO;
    }
    
    return YES;
}

#pragma mark -- layout

- (void)addSubviews
{
    [self.view addSubview:self.stackView];
    [self.view addSubview:self.deleteButton];
}

- (void)defineLayout
{
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(15);
        make.right.mas_offset(-15);
        make.top.mas_offset(35);
    }];
    
    [self.titleTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(50);
        make.width.equalTo(self.stackView);
    }];
    
    [self.urlTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(50);
        make.width.equalTo(self.stackView);
    }];
    
    [self.moveTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(50);
        make.width.equalTo(self.stackView);
    }];
    
    [self.deleteButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.view.mas_safeAreaLayoutGuideBottom).offset(-30);
//        make.left.mas_offset(15);
//        make.right.mas_offset(-15);
        make.centerX.mas_offset(0);
        make.height.mas_equalTo(44);
    }];
}

#pragma mark -- Getters

- (UIStackView *)stackView
{
    if(!_stackView) {
        _stackView = [[UIStackView alloc]initWithArrangedSubviews:@[
            self.titleTextField,
            self.moveTextField,
            self.urlTextField,
        ]];
        _stackView.spacing = 0;
        _stackView.axis = UILayoutConstraintAxisVertical;
        
        _stackView.layer.cornerRadius = 10;
        _stackView.layer.masksToBounds = YES;
    }
    
    return _stackView;
}

- (UIButton *)deleteButton
{
    if(!_deleteButton) {
        _deleteButton = [UIButton new];
        [_deleteButton setTitle:NSLocalizedString(@"customtag.delete", nil) forState:UIControlStateNormal];
        [_deleteButton setTitleColor:UIColor.systemRedColor forState:UIControlStateNormal];
        _deleteButton.titleLabel.font = [UIFont systemFontOfSize:16];
        
        [_deleteButton setBackgroundColor:UIColor.clearColor];
    }
    
    return _deleteButton;
}

- (CustomTextField *)titleTextField
{
    if(!_titleTextField) {
        _titleTextField = [self createTextFieldWithTitle:NSLocalizedString(@"common.title", nil)
                                             placeholder:NSLocalizedString(@"addCustomTag.titlePlaceholder", nil)
                                              appendLine:YES
                                             appendArrow:NO];
    }
    
    return _titleTextField;
}

- (CustomTextField *)urlTextField
{
    if(!_urlTextField) {
        _urlTextField = [self createTextFieldWithTitle:NSLocalizedString(@"common.website.url", nil)
                                           placeholder:NSLocalizedString(@"addCustomTag.urlPlaceholder", nil)
                                            appendLine:NO
                                           appendArrow:NO];
    }
    
    return _urlTextField;
}

- (CustomTextField *)moveTextField
{
    if(!_moveTextField) {
        _moveTextField = [self createTextFieldWithTitle:NSLocalizedString(@"common.folder", nil)
                                           placeholder:@""
                                            appendLine:YES 
                                            appendArrow:YES];
        _moveTextField.hidden = YES;
    }
    
    return _moveTextField;
}

- (CustomTextField *)createTextFieldWithTitle:(NSString *)title
                                  placeholder:(NSString *)placeholder
                                   appendLine:(BOOL)appendLine
                                  appendArrow:(BOOL)appendArrow
{
    CustomTextField* textField = [CustomTextField new];
    textField.placeholder = placeholder;
            
    CGRect leftRect = CGRectMake(15, 0, 50, 50);
    AppButton* leftButton = [[AppButton alloc]initWithFrame:leftRect];
    UILabel* label = leftButton.titleLabel;
    [leftButton.stackView addArrangedSubview:label];
    label.text = title;
    label.textColor = [UIColor colorWithHexString:@"#333333"];
    label.font = [UIFont systemFontOfSize:15];
        
    [leftButton.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_offset(0);
    }];
    
    textField.leftViewRect = leftRect;
    textField.leftView = leftButton;
    textField.leftViewMode = UITextFieldViewModeAlways;

    if(appendArrow) {
        CGRect rightRect = CGRectMake(0, 0, 30, 50);
        textField.rightView = [[UIView alloc]initWithFrame:rightRect];
        textField.rightViewRect = rightRect;
        
        UIImageView* rightView = [[UIImageView alloc]initWithFrame:rightRect];
        rightView.image = [[UIImage imageNamed:@"standard_right_arrow"] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        rightView.contentMode = UIViewContentModeScaleAspectFit;
        [textField addSubview:rightView];
        [rightView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.mas_offset(-15);
            make.size.mas_equalTo(10);
            make.centerY.mas_offset(0);
        }];
        self.arrow = rightView;
    } else {
        CGRect rightRect = CGRectMake(0, 0, 15, 50);
        UIView* rightView = [[UIView alloc]initWithFrame:rightRect];
        textField.rightView = rightView;
        textField.rightViewRect = rightRect;
    }
    textField.rightViewMode = UITextFieldViewModeUnlessEditing;
    
    textField.font = [UIFont systemFontOfSize:15];
    textField.clearButtonMode = UITextFieldViewModeWhileEditing;
    textField.textAlignment = NSTextAlignmentRight;
    
    textField.delegate = self;
            
    if(appendLine) {
        UIView* line = [UIView new];
        line.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
        [textField addSubview:line];
        [line mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.bottom.mas_offset(0);
            make.height.mas_equalTo(0.5);
        }];
        line.hidden = YES;
    }

    return textField;
}


@end
