//
//  ScanViewController.m
//  PPBrowser
//
//  Created by qingbin on 2022/11/17.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "ScanViewController.h"

#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "NSString+Helper.h"
#import "NSObject+Helper.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"

#import "MaizyHeader.h"
#import "CustomButton.h"
#import "UIView+FrameHelper.h"

#import "ScanView.h"
#import "URIFixup.h"

#import "HistoryModel.h"
#import "DatabaseUnit+History.h"

#import "SearchManager.h"

@interface ScanViewController ()<ScanViewDelegate>

@property (nonatomic, strong) ScanView* scanView;

@property (nonatomic, weak) Tab* tab;

@end

@implementation ScanViewController

- (instancetype)initWithTab:(Tab*)tab
{
    self = [super init];
    if(self) {
        self.tab = tab;
    }
    
    return self;
}

- (void)viewDidLoad
{
    [super viewDidLoad];

    [self createCustomLeftBarButtonItem];
    
    [self addSubviews];
    [self defineLayout];
}

- (void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:animated];
    [self.scanView resume];
}

- (void)dealloc
{
    if(self.scanView) {
        [self.scanView stop];
    }
}

- (BaseNavigationBarStyle)preferredNavigationBarStyle
{
    return BaseNavigationBarStyleNoneWithLightContent;
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    [super applyTheme];
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    //只有当前的controller会触发一次, 其它已存在的controller走通知
    if(needReload) {
        //修改暗黑模式
        [self applyTheme];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}

- (void)addSubviews
{
    [self.view addSubview:self.scanView];
    [self.view addSubview:self.leftButton];
}

- (void)defineLayout
{
    [self.leftButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(20);
        make.top.equalTo(self.view.mas_safeAreaLayoutGuideTop).offset(40);
    }];
    
    [self.scanView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
    }];
}

#pragma mark -- ScanViewDelegate
- (void)scanView:(ScanView *)view result:(NSString *)scanString
{
    if (scanString == nil || scanString.length <= 0) {
        return;
    }
    
    [view pause];
    
    NSLog(@"识别出来的结果是: %@", scanString);
    
    HistoryModel* item = [HistoryModel new];
    item.keyword = scanString;
    item.historyId = [NSUUID UUID].UUIDString;
    item.searchType = HistorySearchTypeUnknown;
    
    [self selectItem:item];
}

- (void)selectItem:(HistoryModel*)item
{
    //通过搜索引擎生成的url不记录在表中,
    //因为有可能会切换搜索引擎,那么会引起搜索错误
    //不在这里保存到历史表了, 当触发url/title/didFinish时再保存
    
    NSString* content;
    if(item.searchType == HistorySearchTypePasteboard) {
        content = item.keyword;
    } else if(item.searchType == HistorySearchTypeUnknown) {
        content = item.keyword;
    } else if(item.searchType == HistorySearchTypeKeyword) {
        content = item.keyword;
    } else {
        content = item.url;
    }
    
    self.tab.currentHistoryModel = item;
    
    //不管是否是url,都要判断一下
    NSURL* fixupURL = [URIFixup getURL:content];
    if(fixupURL) {
        item.searchType = HistorySearchTypeUrl;
        
        // The user entered a URL, so use it.
        [self.tab loadRequest:[NSURLRequest requestWithURL:fixupURL]];
    } else {
        item.searchType = HistorySearchTypeKeyword;
        
        // We couldn't build a URL, so pass it on to the search engine.
        NSURL* url = [[SearchManager shareInstance] searchURLForQuery:content];
        [self.tab loadRequest:[NSURLRequest requestWithURL:url]];
    }
    
    //保存到历史表中
    DatabaseUnit* unit = [DatabaseUnit addHistoryWithItem:item];
    DB_EXEC(unit);
    
    //移除首页,进入webview加载页
    if(self.delegate && [self.delegate respondsToSelector:@selector(scanDidStartLoadRequest)]) {
        [self.delegate scanDidStartLoadRequest];
    }
    
    [self.navigationController popViewControllerAnimated:NO];
}

- (void)scanViewAskAuth:(ScanView *)view
{
    [self.navigationController popViewControllerAnimated:YES];
}

- (ScanView *)scanView
{
    if(!_scanView) {
        _scanView = [[ScanView alloc]init];
        _scanView.delegate = self;
    }
    
    return _scanView;
}

@end
