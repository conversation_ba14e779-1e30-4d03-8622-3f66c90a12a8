//
//  ScanView.m
//  PPBrowser
//
//  Created by qingbin on 2022/11/17.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "ScanView.h"
#import "UIView+FrameHelper.h"
#import <AVFoundation/AVFoundation.h>
#import "NSObject+Helper.h"

#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "NSString+Helper.h"
#import "NSObject+Helper.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"

#import "MaizyHeader.h"
#import "CustomButton.h"
#import "UIView+FrameHelper.h"

#import "BrowserUtils.h"

@interface ScanView () <AVCaptureMetadataOutputObjectsDelegate,UINavigationControllerDelegate, UIImagePickerControllerDelegate> {
    BOOL _bStopScroll;
    BOOL _currentRun;
}

@property (nonatomic, strong) AVCaptureSession* captureSession;
@property (nonatomic, strong) AVCaptureSession* backSession;
@property (nonatomic, strong) AVCaptureVideoPreviewLayer* videoPreviewLayer;
@property (nonatomic, strong) AVCaptureVideoPreviewLayer* backPreviewLayer;

@property (nonatomic, strong) UIView* renderView;

@property (nonatomic, strong) UIView* customMaskView;
@property (nonatomic, strong) CAShapeLayer *maskLayer;

@property (nonatomic, strong) UIView* contentView;

@property (nonatomic, strong) UIImageView* scanBgView;
@property (nonatomic, strong) UIImageView* scanLine;

@property (nonatomic, strong) CustomButton* flashBtn;
@property (nonatomic, strong) CustomButton* photoAlbumBtn;

@end

@implementation ScanView

- (void)dealloc
{
    [[NSNotificationCenter defaultCenter] removeObserver:self];

    [self turnOffLed];
}

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = [UIColor blackColor];

        [self addSubviews];
        [self defineLayout];
        [self setupcustomMaskView];
        
        [self _createVideoCaptureViews];

        _currentRun = YES;
        
        [self _movescanLine:YES];

        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(_willEnterForeground) name:UIApplicationWillEnterForegroundNotification object:nil];
    }
    
    return self;
}

// 处理旋转屏幕等布局变化
- (void)layoutSubviews
{
    [super layoutSubviews];
    
    [self updateMaskLayer];
    
    [self.videoPreviewLayer setFrame:CGRectMake(0, 0, kScreenWidth, kScreenHeight)];
}

- (void)updateMaskLayer {
    self.maskLayer.frame = self.customMaskView.bounds;
    
    UIBezierPath *path = [UIBezierPath bezierPathWithRect:self.customMaskView.bounds];
    CGRect frame = [self.scanBgView convertRect:self.scanBgView.frame toView:self];
    [path appendPath:[UIBezierPath bezierPathWithRect:frame]];
    path.usesEvenOddFillRule = YES;
    
    self.maskLayer.path = path.CGPath;
}

- (void)_willEnterForeground
{
    if (_currentRun) {
        [_captureSession startRunning];
    }
}

- (void)pressedOnFlashLightBtn:(UIButton*)sender
{
    [sender setSelected:!sender.selected];

    if (sender.isSelected) {
        [self turnOnLed];
    }else{
        [self turnOffLed];
    }
}
- (void)turnOffLed
{
    AVCaptureDevice* device = [AVCaptureDevice defaultDeviceWithMediaType:AVMediaTypeVideo];
    if ([device hasTorch]) {
        [device lockForConfiguration:nil];
        [device setTorchMode:AVCaptureTorchModeOff];
        [device unlockForConfiguration];
    }
}

- (void)turnOnLed
{
    AVCaptureDevice* device = [AVCaptureDevice defaultDeviceWithMediaType:AVMediaTypeVideo];
    if ([device hasTorch]) {
        [device lockForConfiguration:nil];
        [device setTorchMode:AVCaptureTorchModeOn];
        [device unlockForConfiguration];
    }
}

#pragma mark -- 打开相册
- (void)openPhotoAlbum:(UIButton*)sender
{
    UIImagePickerController *imagePickerController = [UIImagePickerController new];
    imagePickerController.delegate = self;
    imagePickerController.navigationBar.translucent = NO;
    [NSObject requestAuthorizationWithAuthorizedBlock:^{
        // 点击拍照或从相册选择
        imagePickerController.sourceType = UIImagePickerControllerSourceTypePhotoLibrary;
        [[NSOperationQueue mainQueue] addOperationWithBlock:^{
            UIWindow* window = YBIBNormalWindow();
            UIViewController* root = window.rootViewController;
            [root presentViewController:imagePickerController animated:YES completion:nil];
        }];
    } rejectBlock:nil];
}

- (void)addSubviews
{
    //Thread 1: "Set `customMaskView` (<UIView: 0x128e2ce00; frame = (0 0; 0 0); layer = <CALayer: 0x303aa9ec0>>) to `nil` before adding it as a subview of <ScanView: 0x128e24c80; frame = (0 0; 0 0); backgroundColor = UIExtendedGrayColorSpace 0 1; layer = <CALayer: 0x303aa9e00>>"
    [self addSubview:self.renderView];
    
    [self addSubview:self.customMaskView];
    
    [self addSubview:self.contentView];
    [self.contentView addSubview:self.scanBgView];
    [self.contentView addSubview:self.scanLine];
    
    [self.contentView addSubview:self.flashBtn];
    [self.contentView addSubview:self.photoAlbumBtn];
}

- (void)defineLayout
{
    [self.renderView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
    
    [self.customMaskView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
    
    [self.contentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self);
        make.top.left.right.equalTo(self.scanBgView);
        make.bottom.equalTo(self.flashBtn);
    }];
    
    [self.scanBgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.mas_offset(0);
        make.size.mas_equalTo(250);
    }];
    
    [self.scanLine mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.scanBgView);
        make.height.mas_equalTo(1.0);
        make.top.equalTo(self.scanBgView).offset(0).priorityHigh();
    }];
    
    [self.flashBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(_scanBgView.mas_bottom).offset(88);
        make.right.equalTo(self.mas_centerX).offset(-28);
        make.size.mas_equalTo(CGSizeMake(50, 77));
    }];
    
    [self.photoAlbumBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(_flashBtn);
        make.left.equalTo(self.mas_centerX).offset(28);
        make.size.mas_equalTo(CGSizeMake(50, 77));
    }];
}

- (void)_createVideoCaptureViews
{
    NSError* error;

    AVCaptureDevice* captureDevice = [AVCaptureDevice defaultDeviceWithMediaType:AVMediaTypeVideo];

    AVCaptureDeviceInput* input = [AVCaptureDeviceInput deviceInputWithDevice:captureDevice error:&error];
    if (!input) {
        return;
    }

    _captureSession = [[AVCaptureSession alloc] init];
    [_captureSession addInput:input];

    AVCaptureMetadataOutput* captureMetadataOutput = [[AVCaptureMetadataOutput alloc] init];
    [_captureSession addOutput:captureMetadataOutput];

    if ([_captureSession canSetSessionPreset:AVAssetExportPreset1920x1080]) {
        [_captureSession setSessionPreset:AVCaptureSessionPreset1920x1080];
    }
    else {
        [_captureSession setSessionPreset:AVCaptureSessionPreset1280x720];
    }

    dispatch_queue_t dispatchQueue;
    dispatchQueue = dispatch_queue_create("captureQueue", NULL);
    [captureMetadataOutput setMetadataObjectsDelegate:self queue:dispatchQueue];
    //    AVMetadataObjectTypeEAN13Code

    [captureMetadataOutput setMetadataObjectTypes:@[ AVMetadataObjectTypeCode128Code,
                                                     AVMetadataObjectTypeEAN13Code,
                                                     AVMetadataObjectTypeUPCECode,
                                                     AVMetadataObjectTypeCode39Code,
                                                     AVMetadataObjectTypeCode39Mod43Code,
                                                     AVMetadataObjectTypeEAN8Code,
                                                     AVMetadataObjectTypeCode93Code,
                                                     AVMetadataObjectTypePDF417Code,
                                                     AVMetadataObjectTypeQRCode,
                                                     AVMetadataObjectTypeAztecCode]];
    //rectofInterest 是基于图像裁剪的
    CGSize size = self.bounds.size;
    CGRect cropRect = _scanBgView.frame;
    CGFloat p1 = size.height / size.width;
    CGFloat p2 = 1920. / 1080.; //使用了1080p的图像输出
    if (p1 < p2) {
        CGFloat fixHeight = self.bounds.size.width * 1920. / 1080.;
        CGFloat fixPadding = (fixHeight - size.height) / 2;
        captureMetadataOutput.rectOfInterest = CGRectMake((cropRect.origin.y + fixPadding) / fixHeight,
                                                          cropRect.origin.x / size.width,
                                                          cropRect.size.height / fixHeight,
                                                          cropRect.size.width / size.width);
    }
    else {
        CGFloat fixWidth = self.bounds.size.height * 1080. / 1920.;
        CGFloat fixPadding = (fixWidth - size.width) / 2;
        captureMetadataOutput.rectOfInterest = CGRectMake(cropRect.origin.y / size.height,
                                                          (cropRect.origin.x + fixPadding) / fixWidth,
                                                          cropRect.size.height / size.height,
                                                          cropRect.size.width / fixWidth);
    }

    _videoPreviewLayer = [[AVCaptureVideoPreviewLayer alloc] initWithSession:_captureSession];

    [_videoPreviewLayer setVideoGravity:AVLayerVideoGravityResizeAspectFill];
    [_videoPreviewLayer setFrame:CGRectMake(0, 0, kScreenWidth, kScreenHeight)];
    [self.renderView.layer addSublayer:_videoPreviewLayer];

    dispatch_async(dispatch_queue_create("com.capture.session", 0), ^{
        [self->_captureSession startRunning];
    });
}

// 移动扫描线
- (void)_movescanLine:(BOOL)direction
{
    if (!_bStopScroll) {
        [self layoutIfNeeded];
        
        @weakify(self)
        [UIView animateWithDuration:3.0f animations:^{
            @strongify(self)
            if(!direction) {
                [self.scanLine mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.top.equalTo(self.scanBgView).offset(250).priorityHigh();
                }];
            } else {
                [self.scanLine mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.top.equalTo(self.scanBgView).offset(0).priorityHigh();
                }];
            }
            
            [self layoutIfNeeded];
        } completion:^(BOOL finished) {
            [self _movescanLine:!direction];
        }];
    }
}

- (void)captureOutput:(AVCaptureOutput*)captureOutput didOutputMetadataObjects:(NSArray*)metadataObjects fromConnection:(AVCaptureConnection*)connection
{
    if (nil == metadataObjects || metadataObjects.count <= 0) {
        return;
    }

    AVMetadataMachineReadableCodeObject* metadataObj = metadataObjects[0];
    if ([metadataObj isKindOfClass:[AVMetadataMachineReadableCodeObject class]]) {
        NSString* metaString = metadataObj.stringValue;
        // 出问题的码是upc-a码（苹果只识别upc-e码），苹果上是用EAN-13去识别upc-a，前面补零
        if([metadataObj.type isEqualToString:AVMetadataObjectTypeEAN13Code]){
            if ([metaString hasPrefix:@"0"] && [metaString length] > 1)
                metaString = [metaString substringFromIndex:1];
        }
        
        // 这个回主线程灰常灰常关键
        dispatch_async(dispatch_get_main_queue(), ^{
            // 小震动
            UIImpactFeedbackGenerator* feedback = [[UIImpactFeedbackGenerator alloc]initWithStyle:UIImpactFeedbackStyleMedium];
            [feedback prepare];
            [feedback impactOccurred];
            
            if (_delegate && [_delegate respondsToSelector:@selector(scanView:result:)]) {
                [_delegate scanView:self result:metaString];
            }
        });
    }
}

#pragma mark UIImagePickerControllerDelegate
- (void)imagePickerController:(UIImagePickerController *)picker didFinishPickingMediaWithInfo:(NSDictionary<NSString *, id> *)info
{
    UIImage *originImage = [info valueForKey:UIImagePickerControllerOriginalImage];
    [picker dismissViewControllerAnimated:YES completion:nil];
    
    NSArray* features = [self messageFromQRCode:originImage];
    if(features.count == 0) return;
    
    CIQRCodeFeature* feature = features.firstObject;
    NSString* messageString = feature.messageString;
    dispatch_async(dispatch_get_main_queue(), ^{
        // 小震动
        UIImpactFeedbackGenerator* feedback = [[UIImpactFeedbackGenerator alloc]initWithStyle:UIImpactFeedbackStyleMedium];
        [feedback prepare];
        [feedback impactOccurred];
        
        if (_delegate && [_delegate respondsToSelector:@selector(scanView:result:)]) {
            [_delegate scanView:self result:messageString];
        }
    });
}

#pragma mark -- 图片二维码识别
- (NSArray*)messageFromQRCode:(UIImage*)image
{
    if(!self) return nil;
    
    //创建上下文
    CIContext *context = [CIContext contextWithOptions:nil];
    //识别类型设置为二维码，精度设为高
    CIDetector *detector = [CIDetector detectorOfType:CIDetectorTypeQRCode
                                              context:context
                                              options:@{CIDetectorAccuracy:CIDetectorAccuracyHigh}];
    //转换image
    CIImage *ciImage = [CIImage imageWithCGImage:image.CGImage];
    //获取识别结果
    NSArray *features = [detector featuresInImage:ciImage];
    
    return features;
}

- (void)stop
{
    [_captureSession stopRunning];
    _captureSession = nil;
    _bStopScroll = YES;
    _currentRun = NO;
}

- (void)pause
{
    _currentRun = NO;
    [self turnOffLed];
    [_captureSession stopRunning];
    
}

- (void)resume
{
    _currentRun = YES;
    
    dispatch_async(dispatch_queue_create("com.capture.session", 0), ^{
        [self->_captureSession startRunning];
    });
}

#pragma mark -- 加入药店闪光灯/相册
- (CustomButton*)createJoinInStoreButtonWithImage:(NSString*)imageName
                                            title:(NSString*)title
{
    CustomButton* button = [CustomButton new];
    [button setTitle:title forState:UIControlStateNormal];
    [button setImage:[UIImage imageNamed:imageName] forState:UIControlStateNormal];
    
    button.titleLabel.font = [UIFont systemFontOfSize:12];
    button.titleLabel.textAlignment = NSTextAlignmentCenter;
    [button setTitleColor:[UIColor colorWithHexString:@"#ffffff"] forState:UIControlStateNormal];
    
    float width = 50;
    
    float spacing = 10;
    float imageW  = 50;
    float labelW = [NSString sizeWithText:title fontSize:15 width:kScreenWidth].width;
    float labelH = [NSString sizeWithText:title fontSize:15 width:kScreenWidth].height;
    float imageX = (width-imageW)/2.0;
    float labelX = (width-labelW)/2.0;
    button.imageRect = CGRectMake(imageX, 0, imageW, imageW);
    button.titleRect = CGRectMake(labelX, CGRectGetMaxY(button.imageRect)+spacing, labelW, labelH);
    
    return button;
}

#pragma mark - getters

- (UIImageView *)scanBgView
{
    if(!_scanBgView) {
        _scanBgView = [UIImageView new];
        _scanBgView.image = [UIImage imageNamed:@"transation_scan_frame"];
    }
    
    return _scanBgView;
}

- (UIImageView *)scanLine
{
    if(!_scanLine) {
        _scanLine = [UIImageView new];
        _scanLine.image = [UIImage imageNamed:@"transation_scan_scrollingline"];
    }
    
    return _scanLine;
}

- (CustomButton *)flashBtn
{
    if(!_flashBtn) {
        _flashBtn = [self createJoinInStoreButtonWithImage:@"joininstore_flashclose_icon" title:NSLocalizedString(@"scan.flashlight", nil)];
        [_flashBtn setImage:[UIImage imageNamed:@"joininstore_flashopen_icon"] forState:UIControlStateSelected];
        [_flashBtn addTarget:self action:@selector(pressedOnFlashLightBtn:) forControlEvents:UIControlEventTouchUpInside];
    }
    
    return _flashBtn;
}

- (CustomButton *)photoAlbumBtn
{
    if(!_photoAlbumBtn) {
        _photoAlbumBtn = [self createJoinInStoreButtonWithImage:@"joininstore_images_icon" title:NSLocalizedString(@"scan.album", nil)];
        [_photoAlbumBtn addTarget:self action:@selector(openPhotoAlbum:) forControlEvents:UIControlEventTouchUpInside];
    }
    
    return _photoAlbumBtn;
}

- (UIView *)renderView
{
    if(!_renderView) {
        _renderView = [UIView new];
    }
    
    return _renderView;
}

- (UIView *)contentView
{
    if(!_contentView) {
        _contentView = [UIView new];
    }
    
    return _contentView;
}

- (UIView *)customMaskView
{
    if(!_customMaskView) {
        _customMaskView = [UIView new];
    }
    
    return _customMaskView;
}

- (void)setupcustomMaskView
{
    // 等待视图布局完成后创建遮罩层
    [self layoutIfNeeded];
    
    // 创建遮罩层
    CAShapeLayer *maskLayer = [CAShapeLayer layer];
    self.maskLayer = maskLayer;
    maskLayer.frame = self.customMaskView.bounds;
    
    // 创建路径
    UIBezierPath *path = [UIBezierPath bezierPathWithRect:self.customMaskView.bounds];
    CGRect frame = [self.scanBgView convertRect:self.scanBgView.frame toView:self];
    [path appendPath:[UIBezierPath bezierPathWithRect:frame]];
    path.usesEvenOddFillRule = YES;
    
    // 设置遮罩层属性
    maskLayer.path = path.CGPath;
    maskLayer.fillRule = kCAFillRuleEvenOdd;
    maskLayer.fillColor = [[UIColor blackColor] colorWithAlphaComponent:0.5].CGColor;
    
    // 将遮罩层添加到遮罩视图
    [self.customMaskView.layer addSublayer:maskLayer];
}

@end
