//
//  HomeVipView.m
//  PPBrowser
//
//  Created by qingbin on 2025/3/11.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "HomeVipView.h"

#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "UIColor+Helper.h"
#import "NSObject+Helper.h"

#import "CustomTitleAndImageView.h"
#import "PaymentManager.h"

#import "VIPController.h"
#import "BrowserUtils.h"

@interface HomeVipView()
// 整体容器
@property (nonatomic, strong) UIView *containerView;
// 渐变层
@property (nonatomic, strong) CAGradientLayer *gradientLayer;
// 图标容器
@property (nonatomic, strong) UIView *iconView;
// 图标
@property (nonatomic, strong) UIImageView *crownIcon;
// 内容视图
@property (nonatomic, strong) UIView *contentView;
// 标题
@property (nonatomic, strong) UILabel *titleLabel;
// 副标题
@property (nonatomic, strong) UILabel *subtitleLabel;
// 关闭按钮(关闭后5天后再显示)
@property (nonatomic, strong) UIButton *closeButton;
// 按钮
@property (nonatomic, strong) UIButton *actionButton;

@end

@implementation HomeVipView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    
    if(self) {
        [self addSubviews];
        [self defineLayout];
        [self handleEvents];
        
        [self updateWithModel];
    }
    
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    // 更新渐变层的尺寸
    self.gradientLayer.frame = self.containerView.bounds;
}

- (void)updateWithModel
{
    //英语太长了，隐藏maintitle
    int local = [NSLocalizedString(@"opensearch.value", nil) intValue];
    if (local == 2) {
        //英语环境
        self.subtitleLabel.hidden = YES;
        //主标题显示副标题文案
        self.titleLabel.text = NSLocalizedString(@"home.vip.sub.title", nil);
    }
}

#pragma mark - Handle Events

- (void)handleEvents
{
    // 添加点击事件
    UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] init];
    [self.containerView addGestureRecognizer:tapGesture];
    @weakify(self)
    [[tapGesture rac_gestureSignal]subscribeNext:^(id x) {
        @strongify(self)
        if (self.bannerClickBlock) {
            self.bannerClickBlock();
        }
    }];
    
    [[self.closeButton rac_signalForControlEvents:UIControlEventTouchUpInside]
        subscribeNext:^(id x) {
        @strongify(self)
        //记录关闭时间，5天后再显示
        if (self.closeClickBlock) {
            self.closeClickBlock();
        }
    }];
}

#pragma mark - layout

- (void)addSubviews
{
    // 添加子视图
    [self addSubview:self.containerView];
    [self.containerView addSubview:self.contentView];
    [self.containerView addSubview:self.actionButton];
    [self.containerView addSubview:self.closeButton];
    
    // 添加图标视图
    [self.contentView addSubview:self.iconView];
    [self.iconView addSubview:self.crownIcon];
    
    // 添加文本标签
    [self.contentView addSubview:self.titleLabel];
    [self.contentView addSubview:self.subtitleLabel];
    
    // 设置渐变层
    [self.containerView.layer insertSublayer:self.gradientLayer atIndex:0];
}

- (void)defineLayout
{
    float offset = iPadValue(30, 15);
    
    // 容器视图约束
    [self.containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
    
    // 内容视图约束
    [self.contentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.containerView).offset(offset);
        make.centerY.equalTo(self.containerView);
        make.right.lessThanOrEqualTo(self.actionButton.mas_left).offset(-5);
    }];
    
    // 图标容器约束
    [self.iconView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView);
        make.centerY.equalTo(self.contentView);
        make.width.height.mas_equalTo(iPadValue(24, 22));
    }];
    
    // 图标约束
    [self.crownIcon mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.iconView);
        make.height.mas_equalTo(iPadValue(15, 12));
    }];
    
    // 标题约束
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.iconView.mas_right).offset(iPadValue(10, 8));
        make.centerY.equalTo(self.contentView);
    }];
    
    // 副标题约束
    [self.subtitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.titleLabel.mas_right).offset(iPadValue(8, 6));
        make.centerY.equalTo(self.titleLabel);
    }];
    
    // 按钮约束
    [self.actionButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.closeButton.mas_left).offset(-iPadValue(8, 5));
        make.centerY.equalTo(self.containerView);
        make.height.mas_equalTo(iPadValue(36, 24));
    }];
    
    // 关闭按钮
    [self.closeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.containerView).offset(-iPadValue(20, 15));
        make.centerY.equalTo(self.containerView);
        make.size.mas_equalTo(iPadValue(36, 24));
    }];
}

#pragma mark - getters

- (UIView *)containerView {
    if (!_containerView) {
        _containerView = [[UIView alloc] init];
        _containerView.backgroundColor = [UIColor clearColor];
        _containerView.layer.cornerRadius = iPadValue(15, 10);
        _containerView.layer.masksToBounds = NO;
        
        // 设置阴影
        _containerView.layer.shadowColor = [UIColor colorWithRed:0 green:0 blue:0 alpha:0.05].CGColor;
        _containerView.layer.shadowOffset = CGSizeMake(0, 2);
        _containerView.layer.shadowOpacity = 1.0;
        _containerView.layer.shadowRadius = 4.0;
    }
    return _containerView;
}

- (CAGradientLayer *)gradientLayer {
    if (!_gradientLayer) {
        _gradientLayer = [CAGradientLayer layer];
        _gradientLayer.colors = @[
            (id)[UIColor colorWithRed:1.0 green:0.93 blue:0.74 alpha:1.0].CGColor,
            (id)[UIColor colorWithRed:1.0 green:0.84 blue:0.44 alpha:1.0].CGColor
        ];
        _gradientLayer.locations = @[@0.0, @1.0];
        _gradientLayer.startPoint = CGPointMake(0.0, 0.5);
        _gradientLayer.endPoint = CGPointMake(1.0, 0.5);
        _gradientLayer.cornerRadius = iPadValue(15, 10);
        _gradientLayer.masksToBounds = YES;
    }
    return _gradientLayer;
}

- (UIView *)contentView {
    if (!_contentView) {
        _contentView = [[UIView alloc] init];
        _contentView.backgroundColor = [UIColor clearColor];
    }
    return _contentView;
}

- (UIView *)iconView {
    if (!_iconView) {
        _iconView = [[UIView alloc] init];
        _iconView.backgroundColor = [UIColor colorWithRed:1.0 green:0.74 blue:0.09 alpha:1.0]; // #FFBD17
        _iconView.layer.cornerRadius = iPadValue(12, 11);
        _iconView.layer.masksToBounds = YES;
    }
    return _iconView;
}

- (UIImageView *)crownIcon {
    if (!_crownIcon) {
        _crownIcon = [UIImageView new];
        _crownIcon.image = [[UIImage imageNamed:@"setting_crown"] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        _crownIcon.tintColor = UIColor.whiteColor;
        _crownIcon.contentMode = UIViewContentModeScaleAspectFit;
    }
    return _crownIcon;
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.font = [UIFont systemFontOfSize:iPadValue(16, 13) weight:UIFontWeightSemibold];
        _titleLabel.text = NSLocalizedString(@"home.vip.main.title", nil);
        _titleLabel.textColor = [UIColor colorWithRed:0.59 green:0.33 blue:0.0 alpha:1.0]; // #975500
    }
    return _titleLabel;
}

- (UILabel *)subtitleLabel {
    if (!_subtitleLabel) {
        _subtitleLabel = [[UILabel alloc] init];
        _subtitleLabel.font = [UIFont systemFontOfSize:iPadValue(14, 11)];
        _subtitleLabel.text = NSLocalizedString(@"home.vip.sub.title", nil);
        _subtitleLabel.textColor = [UIColor colorWithRed:0.59 green:0.33 blue:0.0 alpha:0.8]; // #975500 with 0.8 opacity
    }
    return _subtitleLabel;
}

- (UIButton *)closeButton {
    if(!_closeButton) {
        _closeButton = [UIButton new];
        UIImage* image = [[UIImage imageNamed:@"common_close2_icon"] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        [_closeButton setImage:image forState:UIControlStateNormal];
        _closeButton.tintColor = [UIColor colorWithHexString:@"#975500"];
        float padding = iPadValue(10, 7);
        _closeButton.imageEdgeInsets = UIEdgeInsetsMake(padding, padding, padding, padding);
    }
    return _closeButton;
}

- (UIButton *)actionButton {
    if (!_actionButton) {
        _actionButton = [UIButton buttonWithType:UIButtonTypeCustom];
        _actionButton.backgroundColor = [UIColor colorWithRed:1.0 green:0.58 blue:0.0 alpha:1.0]; // #FF9500
        [_actionButton setTitle:NSLocalizedString(@"home.vip.button.title", nil) forState:UIControlStateNormal];
        [_actionButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        _actionButton.titleLabel.font = [UIFont systemFontOfSize:11.0 weight:UIFontWeightMedium];
        _actionButton.layer.cornerRadius = iPadValue(18, 12);
        _actionButton.contentEdgeInsets = UIEdgeInsetsMake(3, iPadValue(15, 8), 3, iPadValue(15, 8));
        _actionButton.userInteractionEnabled = NO;
    }
    return _actionButton;
}
     
@end

