//
//  MitaAIInputDialog.h
//  PPBrowser
//
//  Created by qing<PERSON> on 2025/4/1.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@protocol MitaAIInputDialogDelegate <NSObject>

@optional
// 用户点击提交按钮时的回调，返回用户输入的问题
- (void)aiInputDialogDidSubmitWithQuestion:(NSString *)question;
// 用户点击取消按钮时的回调
- (void)aiInputDialogDidCancel;

@end

@interface MitaAIInputDialog : UIView

@property (nonatomic, weak) id<MitaAIInputDialogDelegate> delegate;

// 显示弹窗
- (void)showInView:(UIView *)view;
// 隐藏弹窗
- (void)hide;
// 设置最大输入字数限制
//- (void)setMaxInputLength:(NSInteger)maxLength;

@end

NS_ASSUME_NONNULL_END 
