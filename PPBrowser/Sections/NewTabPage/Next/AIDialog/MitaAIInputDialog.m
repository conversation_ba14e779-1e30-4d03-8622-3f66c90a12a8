//
//  MitaAIInputDialog.m
//  PPBrowser
//
//  Created by qingbin on 2025/4/1.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "MitaAIInputDialog.h"
#import "Masonry.h"
#import "UIColor+Helper.h"
#import "ThemeProtocol.h"
#import "UIView+Helper.h"
#import "CommonGradientView.h"
#import "BrowserUtils.h"

@interface MitaAIInputDialog () <UITextViewDelegate>

// 主视图
@property (nonatomic, strong) UIView *containerView;
// 背景蒙层
@property (nonatomic, strong) UIView *overlayView;
// 标题栏渐变背景
@property (nonatomic, strong) CommonGradientView *headerView;
// 标题
@property (nonatomic, strong) UILabel *titleLabel;
// 提示文本
@property (nonatomic, strong) UILabel *promptLabel;
// 输入框
@property (nonatomic, strong) UITextView *inputTextView;
// 字数统计
//@property (nonatomic, strong) UILabel *counterLabel;
// 取消按钮
@property (nonatomic, strong) UIButton *cancelButton;
// 提交按钮
@property (nonatomic, strong) UIView *submitButton;
// 提交按钮渐变背景
@property (nonatomic, strong) CommonGradientView *submitButtonGradient;
// 居中view
@property (nonatomic, strong) UIView *submitCenterView;
// 提交按钮文本
@property (nonatomic, strong) UILabel *submitButtonLabel;
// 提交按钮图标
@property (nonatomic, strong) UIImageView *submitButtonIcon;
// 最大输入长度
//@property (nonatomic, assign) NSInteger maxInputLength;
// 记录containerView的原始中心Y值
@property (nonatomic, assign) CGFloat originalContainerCenterY;

@end

@implementation MitaAIInputDialog

#pragma mark - Initialization

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
//        self.maxInputLength = 200; // 默认最大输入长度
        [self setupViews];
        [self defineLayout];
        [self applyTheme];
    }
    return self;
}

#pragma mark - Setup UI

- (void)setupViews {
    self.backgroundColor = UIColor.clearColor;
    
    // 添加视图
    [self addSubview:self.overlayView];
    [self addSubview:self.containerView];
    [self.containerView addSubview:self.headerView];
    [self.headerView addSubview:self.titleLabel];
    [self.containerView addSubview:self.promptLabel];
    [self.containerView addSubview:self.inputTextView];
//    [self.containerView addSubview:self.counterLabel];
    [self.containerView addSubview:self.cancelButton];
    [self.containerView addSubview:self.submitButton];
    [self.submitButton addSubview:self.submitButtonGradient];
    [self.submitButton addSubview:self.submitCenterView];
    [self.submitCenterView addSubview:self.submitButtonLabel];
    [self.submitCenterView addSubview:self.submitButtonIcon];
    
    // 设置手势
    UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(handleOverlayTap:)];
    [self.overlayView addGestureRecognizer:tapGesture];
    
    // 为提交按钮添加点击手势
    UITapGestureRecognizer *submitTapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(submitButtonTapped:)];
    [self.submitButton addGestureRecognizer:submitTapGesture];
    
    // 注册键盘通知
    [[NSNotificationCenter defaultCenter] addObserver:self
                                           selector:@selector(keyboardWillShow:)
                                               name:UIKeyboardWillShowNotification
                                             object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                           selector:@selector(keyboardWillHide:)
                                               name:UIKeyboardWillHideNotification
                                             object:nil];
}

- (void)defineLayout {
    // 计算适合iPad和iPhone的尺寸
    float margin = iPadValue(24, 20);
    float cornerRadius = iPadValue(16, 12);
    float headerHeight = iPadValue(56, 48);
    
    // 设置容器视图
    self.containerView.layer.cornerRadius = cornerRadius;
    
    // 设置背景蒙层
    [self.overlayView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
    
    // 设置容器视图
    [self.containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self);
        make.width.equalTo(self).multipliedBy(0.9).priorityHigh();
        make.width.lessThanOrEqualTo(@450);
        make.left.greaterThanOrEqualTo(self).offset(15);
        make.right.lessThanOrEqualTo(self).offset(-15);
    }];
    
    // 设置标题栏
    [self.headerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.right.equalTo(self.containerView);
        make.height.mas_equalTo(headerHeight);
    }];
    
    // 设置标题
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.headerView);
        make.left.equalTo(self.headerView).offset(margin);
        make.right.equalTo(self.headerView).offset(-margin);
    }];
    
    // 设置提示文本
    [self.promptLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.headerView.mas_bottom).offset(margin);
        make.left.equalTo(self.containerView).offset(margin);
        make.right.equalTo(self.containerView).offset(-margin);
    }];
    
    // 设置输入框
    [self.inputTextView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.promptLabel.mas_bottom).offset(12);
        make.left.equalTo(self.containerView).offset(margin);
        make.right.equalTo(self.containerView).offset(-margin);
        make.height.mas_equalTo(iPadValue(150, 120));
    }];
    
    // 设置计数器
//    [self.counterLabel mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.bottom.equalTo(self.inputTextView).offset(-6);
//        make.right.equalTo(self.inputTextView).offset(-8);
//    }];
    
    // 设置按钮
    float buttonHeight = iPadValue(50, 44);
    [self.cancelButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.inputTextView.mas_bottom).offset(20);
        make.left.equalTo(self.containerView).offset(margin);
        make.bottom.equalTo(self.containerView).offset(-margin);
        make.height.mas_equalTo(buttonHeight);
    }];
    
    [self.submitButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.cancelButton);
        make.left.equalTo(self.cancelButton.mas_right).offset(12);
        make.right.equalTo(self.containerView).offset(-margin);
        make.width.equalTo(self.cancelButton);
        make.height.equalTo(self.cancelButton);
    }];
    
    [self.submitButtonGradient mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.submitButton);
    }];
    
    [self.submitButtonLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.submitCenterView);
        make.left.top.bottom.equalTo(self.submitCenterView);
    }];
    
    [self.submitButtonIcon mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.submitCenterView);
        make.centerY.equalTo(self.submitCenterView);
        make.left.equalTo(self.submitButtonLabel.mas_right).offset(5);
        make.size.mas_equalTo(18);
    }];
    
    [self.submitCenterView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.mas_offset(0);
    }];
}

#pragma mark - Public Methods

- (void)showInView:(UIView *)view {
    // 添加到父视图
    [view addSubview:self];
    [self mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(view);
    }];
    
    // 设置初始状态
    self.alpha = 0;
    self.overlayView.alpha = 0;
    self.containerView.transform = CGAffineTransformMakeScale(0.95, 0.95);
    
    // 显示动画
    [UIView animateWithDuration:0.25 animations:^{
        self.alpha = 1;
        self.overlayView.alpha = 1;
        self.containerView.transform = CGAffineTransformIdentity;
    } completion:^(BOOL finished) {
        // 记录原始位置
        self.originalContainerCenterY = self.containerView.center.y;
        [self.inputTextView becomeFirstResponder];
    }];
}

- (void)hide {
    [self.inputTextView resignFirstResponder];
    
    // 隐藏动画
    [UIView animateWithDuration:0.2 animations:^{
        self.alpha = 0;
        self.overlayView.alpha = 0;
        self.containerView.transform = CGAffineTransformMakeScale(0.95, 0.95);
    } completion:^(BOOL finished) {
        [[NSNotificationCenter defaultCenter] removeObserver:self];
        [self removeFromSuperview];
    }];
}

//- (void)setMaxInputLength:(NSInteger)maxLength {
//    _maxInputLength = maxLength;
//    
//    // 更新计数器文本
//    [self updateCounterWithLength:self.inputTextView.text.length];
//}

#pragma mark - Private Methods

- (void)applyTheme {
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    
    // 更新容器视图
    if (isDarkTheme) {
        self.containerView.backgroundColor = [UIColor colorWithHexString:@"#1C1C1E"];
    } else {
        self.containerView.backgroundColor = UIColor.whiteColor;
    }
    
    // 更新标题栏渐变色
    [self.headerView updateFromColor:[UIColor colorWithHexString:@"#6C5CE7"]
                             toColor:[UIColor colorWithHexString:@"#4834D4"]
                           direction:CommonGradientDirectionHorizontal];
    
    // 更新提交按钮渐变色
    [self.submitButtonGradient updateFromColor:[UIColor colorWithHexString:@"#6C5CE7"]
                                      toColor:[UIColor colorWithHexString:@"#4834D4"]
                                    direction:CommonGradientDirectionHorizontal];
    
    // 更新提示文本颜色
    if (isDarkTheme) {
        self.promptLabel.textColor = [UIColor colorWithHexString:@"#AEAEB2"];
    } else {
        self.promptLabel.textColor = [UIColor colorWithHexString:@"#666666"];
    }
    
    // 更新输入框样式
    if (isDarkTheme) {
        self.inputTextView.backgroundColor = [UIColor colorWithHexString:@"#2C2C2E"];
        self.inputTextView.textColor = UIColor.whiteColor;
    } else {
        self.inputTextView.backgroundColor = [UIColor colorWithHexString:@"#F2F2F7"];
        self.inputTextView.textColor = [UIColor colorWithHexString:@"#333333"];
    }
    
    // 更新取消按钮样式
    if (isDarkTheme) {
        [self.cancelButton setBackgroundColor:[UIColor colorWithHexString:@"#2C2C2E"]];
        [self.cancelButton setTitleColor:[UIColor colorWithHexString:@"#EEEEEE"] forState:UIControlStateNormal];
    } else {
        [self.cancelButton setBackgroundColor:[UIColor colorWithHexString:@"#F2F2F7"]];
        [self.cancelButton setTitleColor:[UIColor colorWithHexString:@"#333333"] forState:UIControlStateNormal];
    }
}

- (void)updateCounterWithLength:(NSInteger)length {
//    self.counterLabel.text = [NSString stringWithFormat:@"%ld/%ld", (long)length, (long)self.maxInputLength];
//    
//    // 当超过最大长度时，显示红色
//    if (length > self.maxInputLength) {
//        self.counterLabel.textColor = [UIColor systemRedColor];
//    } else {
//        BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
//        self.counterLabel.textColor = isDarkTheme ?
//            [UIColor colorWithHexString:@"#8E8E93"] :
//            [UIColor colorWithHexString:@"#8E8E93"];
//    }
//    
//    // 显示/隐藏计数器
//    self.counterLabel.hidden = (length == 0);
}

#pragma mark - Event Handlers

- (void)handleOverlayTap:(UITapGestureRecognizer *)gesture {
    [self hide];
    if ([self.delegate respondsToSelector:@selector(aiInputDialogDidCancel)]) {
        [self.delegate aiInputDialogDidCancel];
    }
}

- (void)cancelButtonTapped:(UIButton *)button {
    [self hide];
    if ([self.delegate respondsToSelector:@selector(aiInputDialogDidCancel)]) {
        [self.delegate aiInputDialogDidCancel];
    }
}

- (void)submitButtonTapped:(UITapGestureRecognizer *)gesture {
    // 检查输入是否为空
    NSString *question = [self.inputTextView.text stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
    if (question.length == 0) {
        [UIView showToast:@"请输入您想咨询的问题" endEditing:NO];
        return;
    }
    
    // 通知代理
    if ([self.delegate respondsToSelector:@selector(aiInputDialogDidSubmitWithQuestion:)]) {
        [self.delegate aiInputDialogDidSubmitWithQuestion:question];
    }
    
    [self hide];
}

#pragma mark - UITextViewDelegate

- (BOOL)textView:(UITextView *)textView shouldChangeTextInRange:(NSRange)range replacementText:(NSString *)text {
    // 计算新文本的长度
    NSString *newText = [textView.text stringByReplacingCharactersInRange:range withString:text];
    [self updateCounterWithLength:newText.length];
    
    return YES;
}

- (void)textViewDidChange:(UITextView *)textView {
    [self updateCounterWithLength:textView.text.length];
}

#pragma mark - Keyboard Handling

- (void)keyboardWillShow:(NSNotification *)notification {
    NSDictionary *userInfo = notification.userInfo;
    CGRect keyboardFrame = [userInfo[UIKeyboardFrameEndUserInfoKey] CGRectValue];
    NSTimeInterval duration = [userInfo[UIKeyboardAnimationDurationUserInfoKey] doubleValue];
    UIViewAnimationCurve curve = [userInfo[UIKeyboardAnimationCurveUserInfoKey] integerValue];
    
    // 计算containerView底部到屏幕底部的距离
    CGRect containerFrameInWindow = [self.containerView convertRect:self.containerView.bounds toView:nil];
    CGFloat containerBottom = CGRectGetMaxY(containerFrameInWindow);
    CGFloat screenHeight = [UIScreen mainScreen].bounds.size.height;
    CGFloat bottomInset = screenHeight - containerBottom;
    
    // 如果键盘会遮挡到containerView
    if (bottomInset < keyboardFrame.size.height) {
        CGFloat offsetY = keyboardFrame.size.height - bottomInset + 20; // 额外20点空隙
        
        [UIView animateWithDuration:duration
                              delay:0
                            options:(curve << 16 | UIViewAnimationOptionBeginFromCurrentState)
                         animations:^{
            self.containerView.center = CGPointMake(self.containerView.center.x,
                                                  self.originalContainerCenterY - offsetY);
        } completion:nil];
    }
}

- (void)keyboardWillHide:(NSNotification *)notification {
    NSDictionary *userInfo = notification.userInfo;
    NSTimeInterval duration = [userInfo[UIKeyboardAnimationDurationUserInfoKey] doubleValue];
    UIViewAnimationCurve curve = [userInfo[UIKeyboardAnimationCurveUserInfoKey] integerValue];
    
    [UIView animateWithDuration:duration
                          delay:0
                        options:(curve << 16 | UIViewAnimationOptionBeginFromCurrentState)
                     animations:^{
        self.containerView.center = CGPointMake(self.containerView.center.x,
                                              self.originalContainerCenterY);
    } completion:nil];
}

#pragma mark - Lazy Load

- (UIView *)overlayView {
    if (!_overlayView) {
        _overlayView = [[UIView alloc] init];
        _overlayView.backgroundColor = [UIColor colorWithWhite:0 alpha:0.5];
    }
    return _overlayView;
}

- (UIView *)containerView {
    if (!_containerView) {
        _containerView = [[UIView alloc] init];
        _containerView.backgroundColor = UIColor.whiteColor;
        _containerView.layer.cornerRadius = iPadValue(16, 12);
        _containerView.layer.masksToBounds = YES;
        _containerView.clipsToBounds = YES;
    }
    return _containerView;
}

- (CommonGradientView *)headerView {
    if (!_headerView) {
        _headerView = [[CommonGradientView alloc] init];
    }
    return _headerView;
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [UIView createLabelWithTitle:@""
                                        textColor:UIColor.whiteColor
                                          bgColor:UIColor.clearColor
                                         fontSize:iPadValue(18, 16)
                                    textAlignment:NSTextAlignmentLeft
                                            bBold:YES];
        
        // 创建标题的富文本
        NSMutableAttributedString *attributedTitle = [[NSMutableAttributedString alloc] init];
        
        // 添加图标
        NSTextAttachment *attachment = [[NSTextAttachment alloc] init];
        attachment.image = [[UIImage imageNamed:@"setting_robot"] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        float width = iPadValue(20, 20);
        attachment.bounds = CGRectMake(0, -1, width, 0.8*width);
        NSAttributedString *imageString = [NSAttributedString attributedStringWithAttachment:attachment];
        [attributedTitle appendAttributedString:imageString];
        
        // 添加间距
        [attributedTitle appendAttributedString:[[NSAttributedString alloc] initWithString:@"  "]];
        
        // 添加文本
        NSAttributedString *textString = [[NSAttributedString alloc] initWithString:@"秘塔AI助手"
                                                                         attributes:@{
            NSFontAttributeName: [UIFont boldSystemFontOfSize:iPadValue(17, 16)],
            NSForegroundColorAttributeName: UIColor.whiteColor
        }];
        [attributedTitle appendAttributedString:textString];
        
        _titleLabel.attributedText = attributedTitle;
    }
    return _titleLabel;
}

- (UILabel *)promptLabel {
    if (!_promptLabel) {
        _promptLabel = [UIView createLabelWithTitle:@"请输入您想要咨询的问题:"
                                        textColor:[UIColor colorWithHexString:@"#666666"]
                                          bgColor:UIColor.clearColor
                                         fontSize:iPadValue(16, 14)
                                    textAlignment:NSTextAlignmentLeft
                                            bBold:NO];
    }
    return _promptLabel;
}

- (UITextView *)inputTextView {
    if (!_inputTextView) {
        _inputTextView = [[UITextView alloc] init];
        _inputTextView.font = [UIFont systemFontOfSize:iPadValue(18, 15)];
        _inputTextView.textColor = [UIColor colorWithHexString:@"#333333"];
        _inputTextView.backgroundColor = [UIColor colorWithHexString:@"#F2F2F7"];
        _inputTextView.layer.cornerRadius = 8;
        _inputTextView.textContainerInset = UIEdgeInsetsMake(12, 8, 12, 8);
        _inputTextView.returnKeyType = UIReturnKeySend;
//        _inputTextView.placeholder = @"例如：帮我写一封申请信...";
        _inputTextView.delegate = self;
    }
    return _inputTextView;
}

//- (UILabel *)counterLabel {
//    if (!_counterLabel) {
//        _counterLabel = [UIView createLabelWithTitle:@"0/200"
//                                         textColor:[UIColor colorWithHexString:@"#8E8E93"]
//                                           bgColor:[UIColor colorWithWhite:1 alpha:0.8]
//                                          fontSize:iPadValue(12, 11)
//                                     textAlignment:NSTextAlignmentCenter
//                                             bBold:NO];
//        _counterLabel.hidden = YES;
//        _counterLabel.layer.cornerRadius = 7;
//        _counterLabel.clipsToBounds = YES;
//        _counterLabel.layer.masksToBounds = YES;
//    }
//    return _counterLabel;
//}

- (UIButton *)cancelButton {
    if (!_cancelButton) {
        _cancelButton = [UIButton buttonWithType:UIButtonTypeSystem];
        [_cancelButton setTitle:@"取消" forState:UIControlStateNormal];
        [_cancelButton setTitleColor:[UIColor colorWithHexString:@"#333333"] forState:UIControlStateNormal];
        _cancelButton.titleLabel.font = [UIFont systemFontOfSize:iPadValue(16, 15) weight:UIFontWeightMedium];
        _cancelButton.backgroundColor = [UIColor colorWithHexString:@"#F2F2F7"];
        _cancelButton.layer.cornerRadius = iPadValue(10, 8);
        [_cancelButton addTarget:self action:@selector(cancelButtonTapped:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _cancelButton;
}

- (UIView *)submitButton {
    if (!_submitButton) {
        _submitButton = [[UIView alloc] init];
        _submitButton.layer.cornerRadius = iPadValue(10, 8);
        _submitButton.clipsToBounds = YES;
        _submitButton.userInteractionEnabled = YES;
    }
    return _submitButton;
}

- (CommonGradientView *)submitButtonGradient {
    if (!_submitButtonGradient) {
        _submitButtonGradient = [[CommonGradientView alloc] init];
        _submitButtonGradient.userInteractionEnabled = NO;
    }
    return _submitButtonGradient;
}

- (UILabel *)submitButtonLabel {
    if (!_submitButtonLabel) {
        _submitButtonLabel = [UIView createLabelWithTitle:@"提问"
                                              textColor:UIColor.whiteColor
                                                bgColor:UIColor.clearColor
                                               fontSize:iPadValue(18, 15)
                                          textAlignment:NSTextAlignmentCenter
                                                  bBold:YES];
        _submitButtonLabel.userInteractionEnabled = NO;
    }
    return _submitButtonLabel;
}

- (UIImageView *)submitButtonIcon {
    if (!_submitButtonIcon) {
        _submitButtonIcon = [[UIImageView alloc] init];
        UIImage *paperPlaneImage = [UIImage systemImageNamed:@"paperplane.fill"];
        _submitButtonIcon.image = [paperPlaneImage imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        
        _submitButtonIcon.tintColor = UIColor.whiteColor;
        _submitButtonIcon.contentMode = UIViewContentModeScaleAspectFit;
        _submitButtonIcon.userInteractionEnabled = NO;
    }
    return _submitButtonIcon;
}

- (UIView *)submitCenterView
{
    if (!_submitCenterView) {
        _submitCenterView = [UIView new];
    }
    
    return _submitCenterView;
}

@end
