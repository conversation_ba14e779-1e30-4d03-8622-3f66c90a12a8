//
//  SystemScriptManager.m
//  PandaBrowser
//
//  Created by q<PERSON><PERSON> on 2022/3/2.
//

#import "SystemScriptManager.h"
#import "PreferenceManager.h"

#import "UserScript.h"
#import "Tampermonkey.h"
#import "DatabaseUnit+UserScript.h"

#import "ReactiveCocoa.h"
#import "NSString+Helper.h"
#import "PPNotifications.h"

#import "ResourceHelper.h"
#import "ThemeProtocol.h"
#import "FontModel.h"
#import "SnifferHelper.h"

#import "BlobHelper.h"
#import "ReadabilityHelper.h"

@interface SystemScriptManager ()

@property(nonatomic,strong) NSString* securityToken;
@property(nonatomic,strong) NSMutableDictionary<NSString*,WKUserScript*>* compiledUserScripts;

@end

@implementation SystemScriptManager

+ (instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    static SystemScriptManager* obj;
    dispatch_once(&onceToken, ^{
        obj = [SystemScriptManager new];
    });
    
    return obj;
}

- (instancetype)init
{
    self = [super init];
    if(self) {
        [self loadAllUserScripts];
    }
    
    return self;
}

#pragma mark -- 预加载(还没注入到webView)
- (void)loadAllUserScripts
{
    // Ensure the first script loaded at document start is __firefox__.js
    // since it defines the `window.__firefox__` global.
    [self loadUserScriptWithFileName:@"__firefox__" injectTime:WKUserScriptInjectionTimeAtDocumentStart mainFrameOnly:NO];
    
    // Ensure the first script loaded at document end is __firefox__.js
    // since it also defines the `window.__firefox__` global because PDF
    // content does not execute user scripts designated to run at document
    // start for some reason. ¯\_(ツ)_/¯
    [self loadUserScriptWithFileName:@"__firefox__" injectTime:WKUserScriptInjectionTimeAtDocumentEnd mainFrameOnly:NO];
    
    //视频播放
    [self loadEncryptionUserScriptWithFileName:@"PlaylistDetector" injectTime:WKUserScriptInjectionTimeAtDocumentStart mainFrameOnly:NO];
    
    //长按弹窗
    [self loadEncryptionUserScriptWithFileName:@"ContextMenuHelper" injectTime:WKUserScriptInjectionTimeAtDocumentStart mainFrameOnly:NO];
    
    //暗黑模式
    [self loadEncryptionUserScriptWithFileName:@"NightModeHelper" injectTime:WKUserScriptInjectionTimeAtDocumentStart mainFrameOnly:YES];
        
    //无图模式
    [self loadUserScriptWithFileName:@"NoImageModeHelper" injectTime:WKUserScriptInjectionTimeAtDocumentStart mainFrameOnly:YES];
    
    //网页内查找
    [self loadUserScriptWithFileName:@"FindInPage" injectTime:WKUserScriptInjectionTimeAtDocumentEnd mainFrameOnly:YES];
    
    //看图模式
    [self loadUserScriptWithFileName:@"GetAllImageHelper" injectTime:WKUserScriptInjectionTimeAtDocumentEnd mainFrameOnly:YES];
    
    //获取网页元素
    [self loadUserScriptWithFileName:@"MetadataHelper" injectTime:WKUserScriptInjectionTimeAtDocumentEnd mainFrameOnly:YES];
    
    //标记模式
    [self loadEncryptionUserScriptWithFileName:@"tagitHelper" injectTime:WKUserScriptInjectionTimeAtDocumentEnd mainFrameOnly:YES];
    
    //翻译脚本
    [self loadEncryptionUserScriptWithFileName:@"TranslateHelper" injectTime:WKUserScriptInjectionTimeAtDocumentEnd mainFrameOnly:YES];
    
    // v2.6.2 Blob加密链接解析，因为影响比较大，如果关闭了视频模式，那么吧解析脚本也关了
    BOOL enabledPlayer = [[PreferenceManager shareInstance].items.enabledPlayer boolValue];
    if (enabledPlayer) {
        [self loadEncryptionUserScriptWithFileName:@"BlobHelper" injectTime:WKUserScriptInjectionTimeAtDocumentStart mainFrameOnly:NO];
    }
    
    // v2.6.3 智能翻页
    BOOL isAutoPage = [[PreferenceManager shareInstance].items.isAutoPage boolValue];
    if (isAutoPage) {
        [self loadEncryptionUserScriptWithFileName:@"AutoPage" injectTime:WKUserScriptInjectionTimeAtDocumentEnd mainFrameOnly:YES];
    }
    
    // v2.6.3 标记元素
    [self loadEncryptionUserScriptWithFileName:@"MarkHelper" injectTime:WKUserScriptInjectionTimeAtDocumentEnd mainFrameOnly:YES];
    
    // v2.7.0 网页滑动手势
    [self loadEncryptionUserScriptWithFileName:@"SwipeHelper" injectTime:WKUserScriptInjectionTimeAtDocumentEnd mainFrameOnly:YES];
    
    BOOL enabledReader = [[PreferenceManager shareInstance].items.enabledReader boolValue];
    if (enabledReader) {
        // v2.7.6 阅读模式
        [self loadEncryptionUserScriptWithFileName:@"readability" injectTime:WKUserScriptInjectionTimeAtDocumentEnd mainFrameOnly:YES];
        
        // v2.7.6 阅读模式
        [self loadEncryptionUserScriptWithFileName:@"reader" injectTime:WKUserScriptInjectionTimeAtDocumentEnd mainFrameOnly:YES];
    }
}

- (WKUserScript*)fontScript
{
    NSNumber* fontSize = [PreferenceManager shareInstance].items.fontSize;
    if(!fontSize) return nil;
    
    int font = [FontModel fontValueWithSize:fontSize.intValue];
    //字体大小
    NSString* fontCSS = [NSString stringWithFormat:@"document.getElementsByTagName('body')[0].style.webkitTextSizeAdjust= '%d%%'",font];
    WKUserScript* script = [[WKUserScript alloc] initWithSource:fontCSS injectionTime:WKUserScriptInjectionTimeAtDocumentEnd forMainFrameOnly:YES];
    
    return script;
}

- (WKUserScript*)testScript
{
    NSString* js = [self _loadJsFromBundleWithFileName:@"SwipeNavigation"];
    WKUserScript* script = [[WKUserScript alloc] initWithSource:js injectionTime:WKUserScriptInjectionTimeAtDocumentEnd forMainFrameOnly:YES];
    
    return script;
}

#pragma mark -- 从文件加载脚本
- (WKUserScript*)loadUserScriptWithFileName:(NSString*)fileName
                        injectTime:(WKUserScriptInjectionTime)injectTime
                     mainFrameOnly:(BOOL)mainFrameOnly
{
    NSString* key = [self keyForFileName:fileName injectTime:injectTime mainFrameOnly:mainFrameOnly];
    if(self.compiledUserScripts[key]) return self.compiledUserScripts[key];
    
    NSString* path = [[NSBundle mainBundle]pathForResource:fileName ofType:@"js"];
    NSError* error = nil;
    NSString* source = [NSString stringWithContentsOfFile:path encoding:NSUTF8StringEncoding error:&error];
    if(error) {
        NSLog(@"error = %@", error.localizedDescription);
    }
    
    WKUserScript* script = [[WKUserScript alloc] initWithSource:source injectionTime:injectTime forMainFrameOnly:mainFrameOnly];

    self.compiledUserScripts[key] = script;
    
    return script;
}

#pragma mark -- 从加密类中加载脚本
- (WKUserScript*)loadEncryptionUserScriptWithFileName:(NSString*)fileName
                                           injectTime:(WKUserScriptInjectionTime)injectTime
                                        mainFrameOnly:(BOOL)mainFrameOnly
{
    NSString* key = [self keyForFileName:fileName injectTime:injectTime mainFrameOnly:mainFrameOnly];
    if(self.compiledUserScripts[key]) return self.compiledUserScripts[key];
    
    NSString* source;
    NSString* lowercaseFileName = fileName.lowercaseString;
    if([lowercaseFileName isEqualToString:@"playlistdetector".lowercaseString]) {
        source = [ResourceHelper shareInstance].playlistDetector;
    } else if([lowercaseFileName isEqualToString:@"contextmenuhelper".lowercaseString]) {
        source = [ResourceHelper shareInstance].contextMenuHelper;
    } else if([lowercaseFileName isEqualToString:@"nightmodehelper".lowercaseString]) {
        source = [ResourceHelper shareInstance].nightModeHelper;
    } else if([lowercaseFileName isEqualToString:@"tagithelper".lowercaseString]) {
        source = [ResourceHelper shareInstance].tagitHelper;
    } else if([lowercaseFileName isEqualToString:@"translatehelper".lowercaseString]) {
        source = [ResourceHelper shareInstance].translateHelper;
    } else if([lowercaseFileName isEqualToString:@"blobhelper".lowercaseString]) {
        source = [ResourceHelper shareInstance].blobHelper;
    } else if([lowercaseFileName isEqualToString:@"markhelper".lowercaseString]) {
        source = [ResourceHelper shareInstance].markHelper;
    } else if([lowercaseFileName isEqualToString:@"autopagehelper".lowercaseString]) {
        source = [ResourceHelper shareInstance].autoPageHelper;
    } else if([lowercaseFileName isEqualToString:@"swipehelper".lowercaseString]) {
        source = [ResourceHelper shareInstance].swipeHelper;
    } else if([lowercaseFileName isEqualToString:@"readability".lowercaseString]) {
        source = [ResourceHelper shareInstance].readabilityHelper;
    } else if([lowercaseFileName isEqualToString:@"reader".lowercaseString]) {
        source = [ResourceHelper shareInstance].readerHelper;
    } else {
        return nil;
    }
    
    WKUserScript* script = [[WKUserScript alloc] initWithSource:source injectionTime:injectTime forMainFrameOnly:mainFrameOnly];

    self.compiledUserScripts[key] = script;
    
    return script;
}

- (NSString*)keyForFileName:(NSString*)fileName
                 injectTime:(WKUserScriptInjectionTime)injectTime
              mainFrameOnly:(BOOL)mainFrameOnly
{
    return [NSString stringWithFormat:@"key-%@-%ld-%d",fileName,(long)injectTime,mainFrameOnly];
}

#pragma mark -- 注入脚本
- (void)injectUserSciptsToWebView:(WKWebView*)webView
                       completion:(void(^)(void))completion
{
    // Start off by ensuring that any previously-added user scripts are
    // removed to prevent the same script from being injected twice.
    [webView.configuration.userContentController removeAllUserScripts];

    WKUserScript* script;

    // Inject all pre-compiled user scripts.
    script = [self loadUserScriptWithFileName:@"__firefox__"
                                   injectTime:WKUserScriptInjectionTimeAtDocumentStart
                                mainFrameOnly:NO];
    [webView.configuration.userContentController addUserScript:script];

    script = [self loadUserScriptWithFileName:@"__firefox__"
                                   injectTime:WKUserScriptInjectionTimeAtDocumentEnd
                                mainFrameOnly:NO];
    [webView.configuration.userContentController addUserScript:script];
    
#warning -- 测试
//    //视频播放
//    script = [self loadEncryptionUserScriptWithFileName:@"PlaylistDetector" injectTime:WKUserScriptInjectionTimeAtDocumentStart mainFrameOnly:NO];
//    [webView.configuration.userContentController addUserScript:script];
//    
//    //长按弹窗
//    script = [self loadEncryptionUserScriptWithFileName:@"ContextMenuHelper" injectTime:WKUserScriptInjectionTimeAtDocumentStart mainFrameOnly:NO];
//    [webView.configuration.userContentController addUserScript:script];
//
//    //暗黑模式
//    script = [self loadEncryptionUserScriptWithFileName:@"NightModeHelper" injectTime:WKUserScriptInjectionTimeAtDocumentStart mainFrameOnly:YES];
//    [webView.configuration.userContentController addUserScript:script];
//
//    //暗黑模式
//    // If Night Mode is enabled, inject a small user script to ensure
//    // that it gets enabled immediately when the DOM loads.
//    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
//    if(isDarkTheme) {
//        WKUserScript* script = [[WKUserScript alloc] initWithSource:@"window.__firefox__.NightMode.setEnabled(true)" injectionTime:WKUserScriptInjectionTimeAtDocumentStart forMainFrameOnly:YES];
//        [webView.configuration.userContentController addUserScript:script];
//    } else {
//        WKUserScript* script = [[WKUserScript alloc] initWithSource:@"window.__firefox__.NightMode.setEnabled(false)" injectionTime:WKUserScriptInjectionTimeAtDocumentStart forMainFrameOnly:YES];
//        [webView.configuration.userContentController addUserScript:script];
//    }
//
//    //无图模式
//    script = [self loadUserScriptWithFileName:@"NoImageModeHelper" injectTime:WKUserScriptInjectionTimeAtDocumentStart mainFrameOnly:YES];
//    [webView.configuration.userContentController addUserScript:script];
//
//    //网页内查找
//    script = [self loadUserScriptWithFileName:@"FindInPage" injectTime:WKUserScriptInjectionTimeAtDocumentEnd mainFrameOnly:YES];
//    [webView.configuration.userContentController addUserScript:script];
//
//    //看图模式
//    script = [self loadUserScriptWithFileName:@"GetAllImageHelper" injectTime:WKUserScriptInjectionTimeAtDocumentEnd mainFrameOnly:YES];
//    [webView.configuration.userContentController addUserScript:script];
//
//    //获取网页元素
//    script = [self loadUserScriptWithFileName:@"MetadataHelper" injectTime:WKUserScriptInjectionTimeAtDocumentEnd mainFrameOnly:YES];
//    [webView.configuration.userContentController addUserScript:script];
//
//    //字体
//    script = [self fontScript];
//    if(script) {
//        [webView.configuration.userContentController addUserScript:script];
//    }
//    
//    //标记模式
//    script = [self loadEncryptionUserScriptWithFileName:@"tagitHelper" injectTime:WKUserScriptInjectionTimeAtDocumentEnd mainFrameOnly:YES];
//    [webView.configuration.userContentController addUserScript:script];
//    
//    //翻译脚本
//    script = [self loadEncryptionUserScriptWithFileName:@"TranslateHelper" injectTime:WKUserScriptInjectionTimeAtDocumentEnd mainFrameOnly:YES];
//    [webView.configuration.userContentController addUserScript:script];
//    
//    //2.6.2 Blob加密链接解析，因为影响比较大，如果关闭了视频模式，那么把解析脚本也关了
//    BOOL enabledPlayer = [[PreferenceManager shareInstance].items.enabledPlayer boolValue];
//    if (enabledPlayer) {
//        script = [self loadEncryptionUserScriptWithFileName:@"BlobHelper" injectTime:WKUserScriptInjectionTimeAtDocumentStart mainFrameOnly:NO];
//        [webView.configuration.userContentController addUserScript:script];
//    }
//        
//    //2.6.3 智能翻页
//    BOOL isAutoPage = [[PreferenceManager shareInstance].items.isAutoPage boolValue];
//    if (isAutoPage) {
//        script = [self loadEncryptionUserScriptWithFileName:@"AutoPageHelper" injectTime:WKUserScriptInjectionTimeAtDocumentEnd mainFrameOnly:YES];
//        [webView.configuration.userContentController addUserScript:script];
//    }
//    
//    //2.6.3 标记元素
//    script = [self loadEncryptionUserScriptWithFileName:@"MarkHelper" injectTime:WKUserScriptInjectionTimeAtDocumentEnd mainFrameOnly:YES];
//    [webView.configuration.userContentController addUserScript:script];
//    
//    //2.7.0 网页滑动手势
//    script = [self loadEncryptionUserScriptWithFileName:@"SwipeHelper" injectTime:WKUserScriptInjectionTimeAtDocumentEnd mainFrameOnly:YES];
//    [webView.configuration.userContentController addUserScript:script];
    
    BOOL enabledReader = [[PreferenceManager shareInstance].items.enabledReader boolValue];
    if (enabledReader) {
        //2.7.6，阅读模式
        script = [self loadEncryptionUserScriptWithFileName:@"readability" injectTime:WKUserScriptInjectionTimeAtDocumentEnd mainFrameOnly:YES];
        [webView.configuration.userContentController addUserScript:script];
        
        //2.7.6，阅读模式
        script = [self loadEncryptionUserScriptWithFileName:@"reader" injectTime:WKUserScriptInjectionTimeAtDocumentEnd mainFrameOnly:YES];
        [webView.configuration.userContentController addUserScript:script];
    }

    //添加js引擎
    [self loadJsEngine:webView completion:^{
        if(completion) {
            completion();
        }
    }];
}

#pragma mark -- 加载js引擎
- (void)loadJsEngine:(WKWebView*)webView
          completion:(void(^)(void))completion
{
    //加载自动添加js脚本的脚本
    [[Tampermonkey shareInstance] loadInstallJsHelperWithCompletion:^(UserScript *userScript) {
        if(userScript) {
            dispatch_async(dispatch_get_main_queue(), ^{
                WKUserScript* script = [[WKUserScript alloc] initWithSource:userScript.executorJs injectionTime:WKUserScriptInjectionTimeAtDocumentStart forMainFrameOnly:YES];
                [webView.configuration.userContentController addUserScript:script];
            });
        }
    }];
    
#if DEBUG
#if FOCUS_UNIT_TEST
    //测试脚本
    [[Tampermonkey shareInstance] loadTestJsHelperWithCompletion:^(UserScript *userScript) {
        if(userScript) {
            dispatch_async(dispatch_get_main_queue(), ^{
                WKUserScript* script = [[WKUserScript alloc] initWithSource:userScript.executorJs injectionTime:WKUserScriptInjectionTimeAtDocumentStart forMainFrameOnly:YES];
                [webView.configuration.userContentController addUserScript:script];
            });
        }
    }];
#endif
#endif
    
    //加载已存在的脚本
    DatabaseUnit* unit = [DatabaseUnit queryAllUserScripts];
    [unit setCompleteBlock:^(NSArray* result, BOOL success) {
        for(UserScript* userScript in result) {            
            if(!userScript.isActive) continue;
            
            BOOL isMainFrameOnly = [userScript isRunAtMainFrame];

            NSString* sourceCode = userScript.executorJs;
            if(sourceCode.length == 0) continue;
            
//            NSLog(@"调用了脚本: %@，源码长度: %ld", userScript.name, sourceCode.length);
            
            WKUserScript* script = [[WKUserScript alloc] initWithSource:sourceCode injectionTime:WKUserScriptInjectionTimeAtDocumentStart forMainFrameOnly:isMainFrameOnly];
            [webView.configuration.userContentController addUserScript:script];
        }
        
        if(completion) {
            completion();
        }
    }];
    
    DB_EXEC(unit);
}

#pragma mark -- 从bundle中加载文件

- (NSString *)_loadJsFromBundleWithFileName:(NSString *)scriptFileName
{
    NSString* path = [[NSBundle mainBundle]pathForResource:scriptFileName ofType:@"js"];
    NSError* error = nil;
    NSString* source = [NSString stringWithContentsOfFile:path encoding:NSUTF8StringEncoding error:&error];
    if(error) {
        NSLog(@"error = %@", error.localizedDescription);
    }

    return source;
}

#pragma mark -- lazy init

- (NSMutableDictionary<NSString *,WKUserScript *> *)compiledUserScripts
{
    if(!_compiledUserScripts) {
        _compiledUserScripts = [NSMutableDictionary dictionary];
    }
    
    return _compiledUserScripts;
}

@end
