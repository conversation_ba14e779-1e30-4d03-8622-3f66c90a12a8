//
//  UserScriptHelper.m
//  PPBrowser
//
//  Created by qingbin on 2022/5/19.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "UserScriptHelper.h"
#import "PPEnums.h"

#import "Tab.h"
#import "PPNotifications.h"
#import "ReactiveCocoa.h"
#import "Tampermonkey.h"
#import "DatabaseUnit+UserScript.h"
#import "UserScriptController.h"
#import "BaseNavigationController.h"
#import "NSObject+Helper.h"
#import "UIView+Helper.h"
#import "ViaModel.h"

#import "NetworkUtil.h"
#import "NSString+Helper.h"

#import "PaymentManager.h"
#import "VipController.h"
#import "CommonDataManager.h"
#import "UIAlertController+SafePresentation.h"

@interface UserScriptHelper ()

@property(nonatomic,weak) Tab *tab;

@end

@implementation UserScriptHelper

- (instancetype)initWithTab:(Tab*)tab
{
    self = [super init];
    if(self) {
        self.tab = tab;
    }
    
    return self;
}

- (void)handleResourceData:(NSString *)jsonResult
                  scriptId:(NSString *)scriptId
{
    //处理@resource
    PandaWebView* webView = self.tab.webView;
    NSString* encodedJson = [jsonResult encodeURIComponent];
    
    NSString* js = [NSString stringWithFormat:@"window.__handleResourceData__['%@']('%@')", scriptId, encodedJson];
    [webView evaluateJavaScript:js completionHandler:^(id _Nullable obj, NSError * _Nullable error) {
        if(error) {
            NSLog(@"error = %@", error.localizedDescription);
        }
    }];
}

- (void)handleGetDataValue:(NSString *)jsonResult
                  scriptId:(NSString *)scriptId
{
    //处理getData
    PandaWebView* webView = self.tab.webView;
    NSString* encodedJson = [jsonResult encodeURIComponent];
    //window.__handleGetDataValue__('%7B%0A%20%20%22preCode%22%20%3A%20%22%5C%22%5C%22%22%2C%0A%20%20%22fisrtUse%22%20%3A%20%22false%22%0A%7D')
    //用window.GM.handleGetDataValue报错?
    
    //如果不添加__GetDataFunc__，那么会报错:
    //Cannot execute JavaScript in this document
    NSString *js = [NSString stringWithFormat:@"window.__handleGetDataValue__['%@']('%@')", scriptId, encodedJson];
    [webView evaluateJavaScript:js completionHandler:^(id _Nullable obj, NSError * _Nullable error) {
        if(error) {
            NSLog(@"error = %@", error);
        }
    }];
}

- (void)handleRequestSuccessAction:(UserComandModel *)item
                          scriptId:(NSString *)scriptId
{
    //网络请求成功处理
    PandaWebView* webView = self.tab.webView;
    
    NSString* jsonResult = [item toJSONString];
    NSString* encodedJson = [jsonResult encodeURIComponent];
    
    NSString* js = [NSString stringWithFormat:@"window.__handleResponse__['%@']('%@')", scriptId, encodedJson];
    [webView evaluateJavaScript:js completionHandler:^(id _Nullable obj, NSError * _Nullable error) {
        if(error) {
            NSLog(@"error = %@", error.localizedDescription);
        }
    }];
}

- (void)handleRequestErrorAction:(UserComandModel *)item
                        scriptId:(NSString *)scriptId
{
    //网络请求出错处理
    PandaWebView* webView = self.tab.webView;
    
    NSString* jsonResult = [item toJSONString];
    NSString* encodedJson = [jsonResult encodeURIComponent];
    
    NSString* js = [NSString stringWithFormat:@"window.__handleResponse__['%@']('%@')", scriptId, encodedJson];
    [webView evaluateJavaScript:js completionHandler:^(id _Nullable obj, NSError * _Nullable error) {
        if(error) {
            NSLog(@"error = %@", error.localizedDescription);
        }
    }];
}

- (void)handleRequestTimeoutAction:(UserComandModel *)item
                          scriptId:(NSString *)scriptId
{
    //网络请求超时处理
    PandaWebView* webView = self.tab.webView;
    
    NSString* jsonResult = [item toJSONString];
    NSString* encodedJson = [jsonResult encodeURIComponent];
    
    //uuid, randomId
    NSString* js = [NSString stringWithFormat:@"window.__handleTimeoutResponse__['%@']('%@')", scriptId, encodedJson];
    [webView evaluateJavaScript:js completionHandler:^(id _Nullable obj, NSError * _Nullable error) {
        if(error) {
            NSLog(@"error = %@", error.localizedDescription);
        }
    }];
}

- (void)handleCommandAction:(UserComandModel *)item
                   scriptId:(NSString *)scriptId
{
    //菜单栏点击处理
    PandaWebView* webView = self.tab.webView;
    
    NSString* jsonResult = [item toJSONString];
    NSString* encodedJson = [jsonResult encodeURIComponent];
    
    //uuid, randomId
    NSString* js = [NSString stringWithFormat:@"window.__handleMenuCommand__['%@']('%@')", scriptId, encodedJson];
    [webView evaluateJavaScript:js completionHandler:^(id _Nullable obj, NSError * _Nullable error) {
        if(error) {
            NSLog(@"error = %@", error.localizedDescription);
        }
    }];
}

- (void)handleFullscreenAction:(UserComandModel *)item
                      scriptId:(NSString *)scriptId
{
    //菜单栏点击处理
    PandaWebView* webView = self.tab.webView;
    
    NSString* jsonResult = [item toJSONString];
    NSString* encodedJson = [jsonResult encodeURIComponent];
    
    //uuid, randomId
    NSString* js = [NSString stringWithFormat:@"window.__GMFunc__ && window.__GMFunc__['%@']['%@']('%@')", scriptId, item.randomId, encodedJson];
    [webView evaluateJavaScript:js completionHandler:^(id _Nullable obj, NSError * _Nullable error) {
        if(error) {
            NSLog(@"error = %@", error.localizedDescription);
        }
    }];
}

#pragma mark - 点击了菜单栏

- (void)handleMenuCommandAction:(UserComandModel *)item
{
    PandaWebView* webView = self.tab.webView;
    
    NSString* jsonResult = [item toJSONString];
    NSString* encodedJson = [jsonResult encodeURIComponent];
    
    //uuid, randomId
    NSString* js = [NSString stringWithFormat:@"window.__handleMenuCommand__('%@')", encodedJson];
    [webView evaluateJavaScript:js completionHandler:^(id _Nullable obj, NSError * _Nullable error) {
        if(error) {
            NSLog(@"error = %@", error.localizedDescription);
        }
    }];
}

#pragma mark - TabContentScript

- (NSString*)name
{
    return @"userScriptHelper";
}

- (NSString*)scriptMessageHandlerName
{
    return @"userScriptHelper";
}

- (void)userContentController:(WKUserContentController*)userContentController didReceiveScriptMessage:(WKScriptMessage*)message
{
    NSDictionary* params = message.body;
    UserCommandManager* manager = self.tab.commandManager;
    @weakify(self)
    manager.triggerRequestSuccessAction = ^(UserComandModel *item) {
        @strongify(self)
        //网络请求成功
        NSString* json = [item toJSONString];
        LOG_DEBUG(@"脚本请求成功：%@", json);
        
        [self handleRequestSuccessAction:item scriptId:item.uuid];
    };
    
    manager.triggerRequestErrorAction = ^(UserComandModel *item) {
        @strongify(self)
        //网络请求失败
        NSString* json = [item toJSONString];
        LOG_DEBUG(@"脚本请求失败：%@", json);
        
        [self handleRequestErrorAction:item scriptId:item.uuid];
    };
    
    manager.triggerRequestTimeoutAction = ^(UserComandModel *item) {
        @strongify(self)
        //网络请求超时
        NSString* json = [item toJSONString];
        LOG_DEBUG(@"脚本请求超时：%@", json);
        
        [self handleRequestTimeoutAction:item scriptId:item.uuid];
    };
    
    manager.triggerCommandAction = ^(UserComandModel *item) {
        @strongify(self)
        //点击菜单栏
        [self handleCommandAction:item scriptId:item.uuid];
    };
    
    NSError *error;
    UserComandModel* item;
    @try {
        //postMessage入参注意不是字符串
        item = [[UserComandModel alloc]initWithDictionary:params error:&error];
                
        //脚本很多不规范，因此将其包起来
        if(!error && item) {
            if(item.command == UserScriptCommand_setValue) {
                //设值,只有有值情况下,而且值改变了才会触发kvos
                [manager setValue:item];
            } else if(item.command == UserScriptCommand_deleteValue) {
                //删除值,会触发kvo
                [manager deleteValue:item];
            } else if(item.command == UserScriptCommand_xmlhttpRequest) {
                //http请求
                [manager xmlhttpRequest:item];
            } else if(item.command == UserScriptCommand_setClipboard) {
                //复制到剪贴板
                UIPasteboard *pasteboard = [UIPasteboard generalPasteboard];
                if (item.value.length > 0) {
                    pasteboard.string = item.value;
                }
            } else if(item.command == UserScriptCommand_installScript
                      || item.command == UserScriptCommand_installViaScript) {
                //安装脚本
                [UserScriptHelper _installScriptWithItem:item tab:self.tab];
            } else if(item.command == UserScriptCommand_openInTab) {
                //打开新标签页
                [[NSNotificationCenter defaultCenter] postNotificationName:kOpenInTabByUserScriptNotification object:item];
            } else if(item.command == UserScriptCommand_registerMenuCommand) {
                //注册菜单栏
                [manager registerMenuCommand:item];
            } else if(item.command == UserScriptCommand_unregisterMenuCommand) {
                //解绑菜单栏
                [manager unregisterMenuCommand:item];
            } else if(item.command == UserScriptCommand_getResource) {
                //获取@resource
                //资源请求
                @weakify(self)
                [manager handleGetResourceData:item completed:^(NSString *result) {
                    @strongify(self)
                    [self handleResourceData:result scriptId:item.uuid];
                }];
            } else if(item.command == UserScriptCommand_getData) {
                //获取所有数据
                NSString* jsonResult = [manager getData:item];
                [self handleGetDataValue:jsonResult scriptId:item.uuid];
            } else if(item.command == UserScriptCommand_listCookie) {
                //v2.6.2列举所有cookie
                // 处理cookie list请求
                [self handleCookieList:params uuid:item.uuid randomId:item.randomId];
            } else if(item.command == UserScriptCommand_setCookie) {
                //v2.6.2添加cookie
                [self handleCookieSet:params uuid:item.uuid randomId:item.randomId];
            } else if(item.command == UserScriptCommand_deleteCookie) {
                //v2.6.2删除cookie
                [self handleCookieDelete:params uuid:item.uuid randomId:item.randomId];
            } else if(item.command == UserScriptCommand_setFullscreen) {
                // 设置全屏状态
                BOOL isFullScreen = [item.value boolValue];
                // 触发set方法
                if (isFullScreen) {
                    //切换到全屏
                    [PreferenceManager shareInstance].items.isFullScreen = @(YES);
                    [[PreferenceManager shareInstance] encode];
                }
                
                [CommonDataManager shareInstance].isLockFullScreen = isFullScreen;
            } else if(item.command == UserScriptCommand_getFullscreenStatus) {
                // 获取全屏状态
                BOOL isFullScreen = [CommonDataManager shareInstance].isLockFullScreen;
                
                // 返回状态
                if(item.randomId) {
                    UserComandModel *result = [[UserComandModel alloc] init];
                    result.uuid = item.uuid;
                    result.randomId = item.randomId;
                    result.data = [NSString convertToJsonString:@{@"isFullScreen": @(isFullScreen)} options:NO];
                    
                    [self handleFullscreenAction:result scriptId:item.uuid];
                }
            }
        }
    } @catch (NSException *exception) {
        
    } @finally {}
}

#pragma mark -- Cookie相关操作

- (BOOL)isValidString:(id)value {
    if (!value || value == nil || [value isKindOfClass:[NSNull class]]) {
        return NO;
    }
    if (![value isKindOfClass:[NSString class]]) {
        return NO;
    }
    return [(NSString *)value length] > 0;
}

- (void)handleCookieList:(NSDictionary *)messageBody uuid:(NSString *)uuid randomId:(NSString *)randomId {
    @try {
        // 参数校验
        if (!messageBody || ![messageBody isKindOfClass:[NSDictionary class]]) {
            [self sendCookieListResponse:@[] uuid:uuid randomId:randomId];
            return;
        }
        
        NSDictionary *details = [messageBody objectForKey:@"details"];
        if (!details || ![details isKindOfClass:[NSDictionary class]]) {
            [self sendCookieListResponse:@[] uuid:uuid randomId:randomId];
            return;
        }
        
        // 提取并验证参数
        NSString *domain = [details objectForKey:@"domain"];
        NSString *name = [details objectForKey:@"name"];
        NSString *path = [details objectForKey:@"path"];
        
        // 获取所有cookie
        NSHTTPCookieStorage *cookieStorage = [NSHTTPCookieStorage sharedHTTPCookieStorage];
        NSArray *cookies = [cookieStorage cookies];
        if (!cookies) {
            [self sendCookieListResponse:@[] uuid:uuid randomId:randomId];
            return;
        }
        
        NSMutableArray *resultCookies = [NSMutableArray array];
        for (NSHTTPCookie *cookie in cookies) {
            // 按条件过滤
            if ([self isValidString:domain] && ![cookie.domain hasSuffix:domain]) {
                continue;
            }
            if ([self isValidString:name] && ![cookie.name isEqualToString:name]) {
                continue;
            }
            if ([self isValidString:path] && ![cookie.path isEqualToString:path]) {
                continue;
            }
            
            // 构建cookie信息
            NSMutableDictionary *cookieInfo = [NSMutableDictionary dictionary];
            [cookieInfo setObject:cookie.domain ?: @"" forKey:@"domain"];
            [cookieInfo setObject:cookie.name ?: @"" forKey:@"name"];
            [cookieInfo setObject:cookie.value ?: @"" forKey:@"value"];
            [cookieInfo setObject:cookie.path ?: @"/" forKey:@"path"];
            [cookieInfo setObject:@(cookie.isSecure) forKey:@"secure"];
            [cookieInfo setObject:@(cookie.isHTTPOnly) forKey:@"httpOnly"];
            [cookieInfo setObject:@(cookie.isSessionOnly) forKey:@"session"];
            
            if (cookie.expiresDate) {
                [cookieInfo setObject:@([cookie.expiresDate timeIntervalSince1970]) forKey:@"expiresDate"];
            } else {
                [cookieInfo setObject:[NSNull null] forKey:@"expirationDate"];
            }
            
            [resultCookies addObject:cookieInfo];
        }
        
        [self sendCookieListResponse:resultCookies uuid:uuid randomId:randomId];
    } @catch (NSException *exception) {
        NSLog(@"handleCookieList exception: %@", exception);
        [self sendCookieListResponse:@[] uuid:uuid randomId:randomId];
    }
}

- (void)handleCookieSet:(NSDictionary *)messageBody uuid:(NSString *)uuid randomId:(NSString *)randomId {
    @try {
        // 参数校验
        if (!messageBody || ![messageBody isKindOfClass:[NSDictionary class]]) {
            [self sendCookieSetResponse:NO uuid:uuid randomId:randomId];
            return;
        }
        
        NSDictionary *details = [messageBody objectForKey:@"details"];
        if (!details || ![details isKindOfClass:[NSDictionary class]]) {
            [self sendCookieSetResponse:NO uuid:uuid randomId:randomId];
            return;
        }
        
        // 验证必要参数
        NSString *domain = [details objectForKey:@"domain"];
        NSString *name = [details objectForKey:@"name"];
        
        if (![self isValidString:domain] || ![self isValidString:name]) {
            [self sendCookieSetResponse:NO uuid:uuid randomId:randomId];
            return;
        }
        
        // 处理可选参数
        NSString *value = [details objectForKey:@"value"];
        if (![self isValidString:value]) {
            value = @"";
        }
        
        NSString *path = [details objectForKey:@"path"];
        if (![self isValidString:path]) {
            path = @"/";
        }
        
        // 构建cookie属性
        NSMutableDictionary *cookieProperties = [NSMutableDictionary dictionary];
        [cookieProperties setObject:name forKey:NSHTTPCookieName];
        [cookieProperties setObject:value forKey:NSHTTPCookieValue];
        [cookieProperties setObject:domain forKey:NSHTTPCookieDomain];
        [cookieProperties setObject:path forKey:NSHTTPCookiePath];
        
        // 处理secure属性
        id secure = [details objectForKey:@"secure"];
        if (secure && ![secure isKindOfClass:[NSNull class]]) {
            if ([secure isKindOfClass:[NSNumber class]] && [secure boolValue]) {
                [cookieProperties setObject:@"Secure" forKey:NSHTTPCookieSecure];
            }
        }
        
        // 处理过期时间
        id expiresDate = [details objectForKey:@"expiresDate"];
        if (expiresDate && ![expiresDate isKindOfClass:[NSNull class]]) {
            if ([expiresDate isKindOfClass:[NSNumber class]]) {
                NSDate *date = [NSDate dateWithTimeIntervalSince1970:[expiresDate doubleValue]];
                if (date) {
                    [cookieProperties setObject:date forKey:NSHTTPCookieExpires];
                }
            }
        }
        
        // 创建并设置cookie
        NSHTTPCookie *cookie = [NSHTTPCookie cookieWithProperties:cookieProperties];
        if (!cookie) {
            [self sendCookieSetResponse:NO uuid:uuid randomId:randomId];
            return;
        }
        
        [[NSHTTPCookieStorage sharedHTTPCookieStorage] setCookie:cookie];
        [self sendCookieSetResponse:YES uuid:uuid randomId:randomId];
        
    } @catch (NSException *exception) {
        NSLog(@"handleCookieSet exception: %@", exception);
        [self sendCookieSetResponse:NO uuid:uuid randomId:randomId];
    }
}

- (void)handleCookieDelete:(NSDictionary *)messageBody uuid:(NSString *)uuid randomId:(NSString *)randomId {
    @try {
        // 参数校验
        if (!messageBody || ![messageBody isKindOfClass:[NSDictionary class]]) {
            [self sendCookieDeleteResponse:NO uuid:uuid randomId:randomId];
            return;
        }
        
        NSDictionary *details = [messageBody objectForKey:@"details"];
        if (!details || ![details isKindOfClass:[NSDictionary class]]) {
            [self sendCookieDeleteResponse:NO uuid:uuid randomId:randomId];
            return;
        }
        
        // 验证必要参数
        NSString *domain = [details objectForKey:@"domain"];
        NSString *name = [details objectForKey:@"name"];
        
        if (![self isValidString:domain] || ![self isValidString:name]) {
            [self sendCookieDeleteResponse:NO uuid:uuid randomId:randomId];
            return;
        }
        
        // 获取可选参数
        NSString *path = [details objectForKey:@"path"];
        if (![self isValidString:path]) {
            path = nil;
        }
        
        // 查找并删除cookie
        NSHTTPCookieStorage *cookieStorage = [NSHTTPCookieStorage sharedHTTPCookieStorage];
        NSArray *cookies = [cookieStorage cookies];
        if (!cookies) {
            [self sendCookieDeleteResponse:NO uuid:uuid randomId:randomId];
            return;
        }
        
        BOOL deleted = NO;
        for (NSHTTPCookie *cookie in cookies) {
            if ([cookie.domain hasSuffix:domain] &&
                [cookie.name isEqualToString:name] &&
                (!path || [cookie.path isEqualToString:path])) {
                [cookieStorage deleteCookie:cookie];
                deleted = YES;
            }
        }
        
        [self sendCookieDeleteResponse:deleted uuid:uuid randomId:randomId];
        
    } @catch (NSException *exception) {
        NSLog(@"handleCookieDelete exception: %@", exception);
        [self sendCookieDeleteResponse:NO uuid:uuid randomId:randomId];
    }
}

#pragma mark -- Cookie响应处理

/*
 Tampermonkey的API规范:
 list操作失败返回空数组
 set操作失败返回null
 delete操作失败返回null
 成功的情况保持不变,仍然返回{success: true}。
 */

- (void)sendCookieListResponse:(NSArray *)cookies uuid:(NSString *)uuid randomId:(NSString *)randomId {
    if (!uuid || !randomId) return;
    
    NSDictionary *response = @{
        @"cookies": cookies?:@[],
        @"randomId": randomId?:@""
    };
    
    PandaWebView* webView = self.tab.webView;
    
    NSString* jsonString = [NSString convertToJsonString:response options:NO];
    NSString* encodedJson = [jsonString encodeURIComponent];
    
    //uuid, randomId
    NSString* js = [NSString stringWithFormat:@"window.__handleCookieList__['%@']('%@')", uuid, encodedJson];
    [webView evaluateJavaScript:js completionHandler:^(id _Nullable obj, NSError * _Nullable error) {
        if(error) {
            NSLog(@"error = %@", error.localizedDescription);
        }
    }];
}

- (void)sendCookieSetResponse:(BOOL)success uuid:(NSString *)uuid randomId:(NSString *)randomId {
    if (!uuid || !randomId) return;
    
    NSDictionary *response = @{
        @"success": @(success),
        @"randomId": randomId?:@""
    };
    
    NSString *jsonString = [NSString convertToJsonString:response options:NO];
    PandaWebView* webView = self.tab.webView;

    NSString* encodedJson = [jsonString encodeURIComponent];
    
    //uuid, randomId
    NSString* js = [NSString stringWithFormat:@"window.__handleCookieSet__['%@']('%@')", uuid, encodedJson];
    [webView evaluateJavaScript:js completionHandler:^(id _Nullable obj, NSError * _Nullable error) {
        if(error) {
            NSLog(@"error = %@", error.localizedDescription);
        }
    }];
}

- (void)sendCookieDeleteResponse:(BOOL)success uuid:(NSString *)uuid randomId:(NSString *)randomId {
    if (!uuid || !randomId) return;
    
    NSDictionary *response = @{
        @"success": @(success),
        @"randomId": randomId
    };
    
    NSString *jsonString = [NSString convertToJsonString:response options:NO];
    PandaWebView* webView = self.tab.webView;

    NSString* encodedJson = [jsonString encodeURIComponent];
    
    //uuid, randomId
    NSString* js = [NSString stringWithFormat:@"window.__handleCookieDelete__['%@']('%@')", uuid, encodedJson];
    [webView evaluateJavaScript:js completionHandler:^(id _Nullable obj, NSError * _Nullable error) {
        if(error) {
            NSLog(@"error = %@", error.localizedDescription);
        }
    }];
}

#pragma mark -- 安装脚本

+ (void)_installScriptWithItem:(UserComandModel *)item tab:(Tab *)tab
{
    [UIView showLoading:NSLocalizedString(@"tips.installing", nil)];
    
    //installHelper, 安装脚本
    if(item.command == UserScriptCommand_installScript) {
        //需要脚本
        [NetworkUtil requestGet:item.originalUrl completion:^(BOOL succ, id responseObject) {
            if(succ) {
                NSString* jsContent = [[NSString alloc] initWithData:responseObject encoding:NSUTF8StringEncoding];
                item.value = jsContent;
                
                [UserScriptHelper installScriptWithItem:item tab:tab];
            }
            
            [UIView hideHud:NO];
        }];
    } else if(item.command == UserScriptCommand_installViaScript) {
        //via脚本
        [UserScriptHelper installScriptWithItem:item tab:tab];
        
        [UIView hideHud:NO];
    }
}

#pragma mark -- greasyfork安装脚本

+ (void)installScriptWithItem:(UserComandModel *)item tab:(Tab *)tab
{
    //加载脚本
    NSString* scriptContent = item.value;
                
    if(item.command == UserScriptCommand_installViaScript) {
        //via插件管理, 不和油猴脚本混在一起处理, 增加代码的维护难度
        //via中url、originalUrl和code是必有的
        //name和autor可能没有, 而且via插件有可能是油猴的格式
        
        //via插件需要特殊处理
        //也会出现via插件中出现油猴的格式
        ViaModel* via = [ViaModel parseSourceCodeFromCommand:item.value];
        if([via.code rangeOfString:@"==UserScript=="].location != NSNotFound) {
            //该via插件的描述信息是油猴脚本格式
            scriptContent = via.code; //直接赋值
            //via轻插件-通用VIP脚本特殊处理
            //@name/@description有可能出现冒号
            scriptContent = [scriptContent stringByReplacingOccurrencesOfString:@"@name: " withString:@"@name "];
            scriptContent = [scriptContent stringByReplacingOccurrencesOfString:@"@description: " withString:@"@description "];
            scriptContent = [scriptContent stringByReplacingOccurrencesOfString:@"@version: " withString:@"@version "];
        } else {
            ViaModel* metaModel = [[Tampermonkey shareInstance] parseViaWithSourceCode:via.code];
                            
            NSString* meta = @"// ==UserScript== \n// @name         {{name}}\n// @version      {{version}}\n// @description  {{description}}\n// @match        {{match}}\n// @grant        none\n// ==/UserScript== \n";
            
            meta = [meta stringByReplacingOccurrencesOfString:@"{{name}}" withString:metaModel.scriptName];
            meta = [meta stringByReplacingOccurrencesOfString:@"{{version}}" withString:metaModel.version];
            meta = [meta stringByReplacingOccurrencesOfString:@"{{description}}" withString:metaModel.desc];
            meta = [meta stringByReplacingOccurrencesOfString:@"{{match}}" withString:metaModel.includes];
            
            NSString* source = via.code;
        
            NSMutableString* combineString = [NSMutableString new];
            [combineString appendFormat:@"%@", meta];
            [combineString appendString:@"\n"];
            [combineString appendString:source];
            
            scriptContent = [combineString copy];
        }
    }
    
    //1、解析脚本    
    [UIView showLoading:NSLocalizedString(@"tips.installing", nil)];
    
    NSString* uuid = [[NSUUID UUID] UUIDString];
    [[Tampermonkey shareInstance] loadJsWithUUID:uuid
                                      sourceCode:scriptContent
                                      completion:^(UserScript *userScript) {
        dispatch_async(dispatch_get_main_queue(), ^{
            if(!userScript) {
                [UIView showFailed:NSLocalizedString(@"tips.adduserscript.fail", nil)];
                return;
            }
            
            [self updateDefaultValueWithUserScript:userScript];
            
            [self _addUserScipt:userScript tab:tab];
        });
    }];
}

#pragma mark -- 根据URL安装脚本(https://)

+ (void)installScriptWithURL:(NSURL *)URL tab:(Tab *)tab
{
    dispatch_async(dispatch_get_global_queue(0, DISPATCH_QUEUE_PRIORITY_DEFAULT),^{
        NSURLRequest *request = [[NSURLRequest alloc]initWithURL:URL cachePolicy:NSURLRequestUseProtocolCachePolicy timeoutInterval:30];
        NSError *error;
        NSData *data = [NSURLConnection sendSynchronousRequest:request returningResponse:nil error:&error];
        if(!error && data != nil ) {
            NSString *scriptContent = [[NSString alloc]initWithData:data encoding:NSUTF8StringEncoding];
            dispatch_async(dispatch_get_main_queue(),^{
                if(scriptContent.length == 0) {
                    [UIView showFailed:NSLocalizedString(@"tips.adduserscript.fail", nil)];
                    return;
                }
                //点击添加用户脚本
                [UIView showLoading:NSLocalizedString(@"tips.installing", nil)];
                
                NSString* uuid = [[NSUUID UUID] UUIDString];
                [[Tampermonkey shareInstance] loadJsWithUUID:uuid
                                                  sourceCode:scriptContent
                                                  completion:^(UserScript *userScript) {
                    dispatch_async(dispatch_get_main_queue(), ^{
                        if(!userScript) {
                            [UIView showFailed:NSLocalizedString(@"tips.adduserscript.fail", nil)];
                            return;
                        }
                        
                        [self updateDefaultValueWithUserScript:userScript];
                        
                        [self _addUserScipt:userScript tab:tab];
                    });
                }];
            });
        }
    });
}

#pragma mark -- 通过分享过来的脚本文件(file://)
+ (void)installScriptFromFileWithURL:(NSURL *)URL tab:(Tab *)tab
{
    dispatch_async(dispatch_get_global_queue(0, DISPATCH_QUEUE_PRIORITY_DEFAULT),^{
        //添加用户脚本
        NSString *htmlString = [NSString stringWithContentsOfURL:URL encoding:NSUTF8StringEncoding error:nil];
        if(htmlString.length <= 0) {
            //没有脚本
            dispatch_async(dispatch_get_main_queue(), ^{
                [UIView showFailed:NSLocalizedString(@"tips.adduserscript.fail", nil)];
            });
            
            return;
        }
        
        NSString* scriptContent = htmlString;
        
        [UIView showLoading:NSLocalizedString(@"tips.installing", nil)];
        NSString* uuid = [[NSUUID UUID] UUIDString];
        [[Tampermonkey shareInstance] loadJsWithUUID:uuid
                                          sourceCode:scriptContent
                                          completion:^(UserScript *userScript) {
            dispatch_async(dispatch_get_main_queue(), ^{
                if(!userScript) {
                    [UIView showFailed:NSLocalizedString(@"tips.adduserscript.fail", nil)];
                    return;
                }
                
                [self updateDefaultValueWithUserScript:userScript];
                
                [self _addUserScipt:userScript tab:tab];
                
//                [UIView hideHud:NO];
            });
        }];
    });
}

#pragma mark -- 通过ActionExtension分享过来的脚本文件(file://) / 或者通过导入文件的脚本文件
+ (void)installScriptFromFileWithJsContent:(NSString *)jsContent tab:(Tab *)tab
{
    [UIView showLoading:NSLocalizedString(@"tips.installing", nil)];
    NSString* uuid = [[NSUUID UUID] UUIDString];
    [[Tampermonkey shareInstance] loadJsWithUUID:uuid 
                                      sourceCode:jsContent
                                      completion:^(UserScript *userScript) {
        dispatch_async(dispatch_get_main_queue(), ^{
            if(!userScript) {
                [UIView showFailed:NSLocalizedString(@"tips.adduserscript.fail", nil)];
                return;
            }
            
            [self updateDefaultValueWithUserScript:userScript];
            
            [self _addUserScipt:userScript tab:tab];
        });
    }];
}

+ (void)_addUserScipt:(UserScript *)userScript tab:(Tab *)tab
{
    if(!userScript) return;
    
    //0、判断脚本是否存在
    UserScript* oldUserScript = [[Tampermonkey shareInstance] isUserScriptExist:userScript];
    
    if(oldUserScript) {
        //已存在
        [UIView hideHud:NO];
        
        @weakify(self)
        [self _showAlertWithCompetion:^{
            //删除旧版本，添加新脚本
            @strongify(self)
            [self handleRemoveOld:oldUserScript latest:userScript tab:tab];
        }];
    } else {
        //1、解析脚本
        //2、保存到内存缓存
        [[Tampermonkey shareInstance] saveUserScriptToCache:userScript];
        
        //3、保存脚本相关信息到数据库
        DatabaseUnit* unit = [DatabaseUnit addUserScriptWithItem:userScript];
        DB_EXEC(unit);
        
        //5、加载@require和@resource相关资源
        //@require的加载已改成同步的方式
        [[Tampermonkey shareInstance] loadResourceWithUserScript:userScript completion:nil];
        
        //6、打开脚本管理器
        UserScriptController* vc = [[UserScriptController alloc]initWithTab:tab];
        BaseNavigationController* navc = [[BaseNavigationController alloc]initWithRootViewController:vc];
        navc.view.backgroundColor = UIColor.whiteColor;

        UIWindow* window = YBIBNormalWindow();
        UIViewController* rootViewController = window.rootViewController;
//        if([BrowserUtils isiPad]) {
//            //iPad
//            navc.modalPresentationStyle = UIModalPresentationFormSheet;
//            vc.preferredContentSize = CGSizeMake(kScreenWidth-90, kScreenHeight-90);
//            
//            [rootViewController presentViewController:navc animated:YES completion:nil];
//        } else {
//            //iPhone
//            [rootViewController presentViewController:navc animated:YES completion:nil];
//        }
        //v2.6.8,统一present
        [rootViewController presentCustomToViewController:navc];
        
        //开启脚本
        [[NSNotificationCenter defaultCenter] postNotificationName:kAddUserScriptNotification object:userScript];
        
        [UIView hideHud:NO];
    }
}

//弹窗询问
+ (void)_showAlertWithCompetion:(void(^)(void))completion
{
    UIAlertController *alertController = [UIAlertController alertControllerWithTitle:NSLocalizedString(@"script.repeat.title", nil) message:nil preferredStyle:UIAlertControllerStyleAlert];
    [alertController addAction:([UIAlertAction actionWithTitle:NSLocalizedString(@"alert.cancel", nil) style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
        
    }])];
    [alertController addAction:([UIAlertAction actionWithTitle:NSLocalizedString(@"alert.confirm", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        if(completion) {
            completion();
        }
    }])];
    
    UIWindow* window = YBIBNormalWindow();
    UIViewController* rootViewController = window.rootViewController;
    
//    [rootViewController presentViewController:alertController animated:YES completion:nil];
    //v2.6.8, 统一present方法，防止崩溃
    [alertController presentSafelyFromViewController:rootViewController];
}

//删除旧脚本，添加新脚本
+ (void)handleRemoveOld:(UserScript *)old latest:(UserScript *)latest tab:(Tab *)tab
{
    //删除旧脚本，添加新脚本
    
    //删除数据库数据+暂停脚本
    DatabaseUnit* unit = [DatabaseUnit removeUserScriptWithId:old.uuid];
    DB_EXEC(unit);
    
    UserScript* userScript= latest;
    
    //2、保存到内存缓存
    [[Tampermonkey shareInstance] saveUserScriptToCache:userScript];
    
    //3、保存脚本相关信息到数据库
    unit = [DatabaseUnit addUserScriptWithItem:userScript];
    DB_EXEC(unit);
    
    //5、加载@require和@resource相关资源
    //@require的加载已经在loadjs中进行
    [[Tampermonkey shareInstance] loadResourceWithUserScript:userScript completion:nil];
    
    //6、打开脚本管理器
    UserScriptController* vc = [[UserScriptController alloc]initWithTab:tab];
    BaseNavigationController* navc = [[BaseNavigationController alloc]initWithRootViewController:vc];
    navc.view.backgroundColor = UIColor.whiteColor;

    UIWindow* window = YBIBNormalWindow();
    UIViewController* rootViewController = window.rootViewController;
//    if([BrowserUtils isiPad]) {
//        //iPad
//        navc.modalPresentationStyle = UIModalPresentationFormSheet;
//        vc.preferredContentSize = CGSizeMake(kScreenWidth-90, kScreenHeight-90);
//        
//        [rootViewController presentViewController:navc animated:YES completion:nil];
//    } else {
//        //iPhone
//        [rootViewController presentViewController:navc animated:YES completion:nil];
//    }
    //v2.6.8,统一present
    [rootViewController presentCustomToViewController:navc];
    
    //开启脚本
    [[NSNotificationCenter defaultCenter] postNotificationName:kAddUserScriptNotification object:userScript];
}

#pragma mark -- 给从文件中读取的脚本解析出来的UserScript，添加默认值
+ (void)updateDefaultValueWithUserScript:(UserScript *)userScript
{
    userScript.isActive = true;
    userScript.frameOption = UserScriptFrameOptionDefault;
    
    BOOL isVip = [[PaymentManager shareInstance] isVip];
    if(isVip) {
        //如果是会员，那么默认设置为自动更新
        userScript.isAutoUpdate = YES;
    }
}

@end
