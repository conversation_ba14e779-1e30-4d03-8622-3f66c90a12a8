//
//  FindInPageHelper.h
//  PPBrowser
//
//  Created by qingbin on 2022/4/2.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TabContentScript.h"
@class Tab;

@interface FindInPageHelper : NSObject<TabContentScript>

- (instancetype)initWithTab:(Tab*)tab;

//网页内查找
+ (void)findInPageInWebView:(WKWebView*)webView keyword:(NSString*)keyword;

+ (void)findNextInWebView:(WKWebView*)webView keyword:(NSString*)keyword;

+ (void)findPreviousInWebView:(WKWebView*)webView keyword:(NSString*)keyword;

+ (void)findDoneWithWebView:(WKWebView*)webView;

@end

