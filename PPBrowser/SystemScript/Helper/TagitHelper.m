//
//  TagitHelper.m
//  PPBrowser
//
//  Created by qingbin on 2022/12/16.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "TagitHelper.h"

#import "Tab.h"
#import "PPNotifications.h"
#import "BaseNavigationController.h"

#import "UIView+Helper.h"
#import "NSObject+Helper.h"

#import "TagitManager.h"
#import "CopyrightHelper.h"

#import "NSURL+Extension.h"
#import "NSString+Helper.h"

#import "PreferenceManager.h"

@interface TagitHelper ()

@property(nonatomic,weak) Tab *tab;

@end

@implementation TagitHelper

- (instancetype)initWithTab:(Tab*)tab
{
    self = [super init];
    if(self) {
        self.tab = tab;
    }
    
    return self;
}

+ (void)showElementMaskWithWebView:(WKWebView*)webView point:(CGPoint)point
{
    NSString* js = [NSString stringWithFormat:@"window.__firefox__.tagitHelper.showElementMaskWithPoint(\"%f\",\"%f\")",point.x, point.y];
    [webView evaluateJavaScript:js completionHandler:^(id _Nullable obj, NSError * _Nullable error) {
        if(error) {
            NSLog(@"error = %@", error.localizedDescription);
        }
    }];
}

+ (void)showParentElementMaskWithWebView:(WKWebView*)webView
{
    NSString* js = [NSString stringWithFormat:@"window.__firefox__.tagitHelper.showParentElementMask()"];
    [webView evaluateJavaScript:js completionHandler:^(id _Nullable obj, NSError * _Nullable error) {
        if(error) {
            NSLog(@"error = %@", error.localizedDescription);
        }
    }];
}

+ (void)showTheChildElementMaskWithWebView:(WKWebView*)webView
{
    NSString* js = [NSString stringWithFormat:@"window.__firefox__.tagitHelper.showTheChildElementMask()"];
    [webView evaluateJavaScript:js completionHandler:^(id _Nullable obj, NSError * _Nullable error) {
        if(error) {
            NSLog(@"error = %@", error.localizedDescription);
        }
    }];
}

+ (void)cancelHideElementWithWebView:(WKWebView*)webView
{
    NSString* js = [NSString stringWithFormat:@"window.__firefox__.tagitHelper.cancelElementMask()"];
    [webView evaluateJavaScript:js completionHandler:^(id _Nullable obj, NSError * _Nullable error) {
        if(error) {
            NSLog(@"error = %@", error.localizedDescription);
        }
    }];
}

+ (void)hideElementWithWebView:(WKWebView*)webView xpath:(NSString *)xpath
{
    NSString* js = [NSString stringWithFormat:@"window.__firefox__.tagitHelper.hideElementByXpath(\"%@\")", xpath];
    [webView evaluateJavaScript:js completionHandler:^(id _Nullable obj, NSError * _Nullable error) {
        if(error) {
            NSLog(@"error = %@", error.localizedDescription);
        }
    }];
}

+ (void)showElementWithWebView:(WKWebView*)webView xpath:(NSString *)xpath
{
    NSString* js = [NSString stringWithFormat:@"window.__firefox__.tagitHelper.showElementByXpath(\"%@\")", xpath];
    [webView evaluateJavaScript:js completionHandler:^(id _Nullable obj, NSError * _Nullable error) {
        if(error) {
            NSLog(@"error = %@", error.localizedDescription);
        }
    }];
}

+ (void)hideTheFixedAdsWithWebView:(WKWebView*)webView
{
    NSString* js = [NSString stringWithFormat:@"window.__firefox__.tagitHelper.hideTheFixedAds()"];
    [webView evaluateJavaScript:js completionHandler:^(id _Nullable obj, NSError * _Nullable error) {
        if(error) {
            NSLog(@"error = %@", error.localizedDescription);
        }
    }];
}

+ (void)updateNeedObserveNodeWithWebView:(WKWebView*)webView needObserveNode:(BOOL)needObserveNode
{
    NSString* js = [NSString stringWithFormat:@"window.__firefox__.tagitHelper.updateNeedObserveNode(%d)", needObserveNode];
    [webView evaluateJavaScript:js completionHandler:^(id _Nullable obj, NSError * _Nullable error) {
        if(error) {
            NSLog(@"error = %@", error.localizedDescription);
        }
    }];
}

#pragma mark -- TabContentScript
- (NSString*)name
{
    return @"tagitHelper";
}

- (NSString*)scriptMessageHandlerName
{
    return @"tagitHelper";
}

- (void)userContentController:(WKUserContentController*)userContentController didReceiveScriptMessage:(WKScriptMessage*)message
{
    if(!self.tab || !self.tab.webView) return;
    PandaWebView* webView = self.tab.webView;
    
    NSDictionary* params = message.body;
    int type = [params[@"type"] intValue];
    
    if(type == 1 || type == 2 || type == 3) {
        NSString* xpath = params[@"xpath"];
        
        TagitModel* item = [TagitModel new];
        item.xpath = xpath;
        item.host = [webView.URL normalizedHost];
        
        [TagitManager shareInstance].currentSelectItem = item;
    } else if(type == 4 || type == 5 || type == 6) {
        //注意, DOMNodeInserted有可能一直调用, 这里不知道有没有性能损耗
        
        //window load
        NSString* host = [webView.URL normalizedHost];
        //webView.URL = about:blank时, host = nil
        if(host.length <= 0) return;
        
        //忽略版权控制
        BOOL enabledTagit = [[PreferenceManager shareInstance].items.enabledTagit boolValue];
        if([[CopyrightHelper shareInstance] validAdBlockUrl:webView.URL] || enabledTagit) {
            BOOL enabledFixupAdblock = [[PreferenceManager shareInstance].items.enabledFixupAdblock boolValue];
            if(enabledFixupAdblock) {
                //可以屏蔽
                [TagitHelper hideTheFixedAdsWithWebView:webView];
            }
            
            NSArray* items = [[TagitManager shareInstance] getElementsWithHost:host];
            for(TagitModel* item in items) {
                if(item.xpath.length > 0) {
                    [TagitHelper hideElementWithWebView:webView xpath:item.xpath];
                }
            }
            
            BOOL needObserveNode = items.count > 0;
            [TagitHelper updateNeedObserveNodeWithWebView:webView needObserveNode:needObserveNode];
        }
    }
}

@end

