//
//  TagitHelper.h
//  PPBrowser
//
//  Created by qingbin on 2022/12/16.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>

#import "TabContentScript.h"
@class Tab;

@interface TagitHelper : NSObject<TabContentScript>

- (instancetype)initWithTab:(Tab*)tab;

+ (void)showElementMaskWithWebView:(WKWebView*)webView point:(CGPoint)point;
+ (void)showParentElementMaskWithWebView:(WKWebView*)webView;
+ (void)showTheChildElementMaskWithWebView:(WKWebView*)webView;
+ (void)cancelHideElementWithWebView:(WKWebView*)webView;

+ (void)hideElementWithWebView:(WKWebView*)webView xpath:(NSString *)xpath;
+ (void)showElementWithWebView:(WKWebView*)webView xpath:(NSString *)xpath;

@end

/*
 标记模式会导致下面网站一直刷新
 https://ankiweb.net/shared/info/1104981491
 */
