//
//  FindInPageHelper.m
//  PPBrowser
//
//  Created by qingbin on 2022/4/2.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "FindInPageHelper.h"
#import "Tab.h"
#import "PPNotifications.h"

@interface FindInPageHelper ()

@property(nonatomic,weak) Tab *tab;

@end

@implementation FindInPageHelper

- (instancetype)initWithTab:(Tab*)tab
{
    self = [super init];
    if(self) {
        self.tab = tab;
    }
    
    return self;
}

#pragma mark -- oc to js
//网页内查找
+ (void)findInPageInWebView:(WKWebView*)webView keyword:(NSString*)keyword
{
    [self findText:keyword function:@"find" webView:webView];
}

+ (void)findNextInWebView:(WKWebView*)webView keyword:(NSString*)keyword
{
    [self findText:keyword function:@"findNext" webView:webView];
}

+ (void)findPreviousInWebView:(WKWebView*)webView keyword:(NSString*)keyword
{
    [self findText:keyword function:@"findPrevious" webView:webView];
}

+ (void)findText:(NSString*)keyword function:(NSString*)function webView:(WKWebView*)webView
{
    if(keyword.length == 0) return;
    
    NSString* js = [NSString stringWithFormat:@"window.__firefox__.%@(\"%@\")", function, keyword];
    [webView evaluateJavaScript:js completionHandler:^(id _Nullable obj, NSError * _Nullable error) {
        if(error) {
            NSLog(@"error = %@", error.localizedDescription);
        }
    }];
}

+ (void)findDoneWithWebView:(WKWebView*)webView
{
    NSString* js = [NSString stringWithFormat:@"window.__firefox__.findDone()"];
    [webView evaluateJavaScript:js completionHandler:^(id _Nullable obj, NSError * _Nullable error) {
        if(error) {
            NSLog(@"error = %@", error.localizedDescription);
        }
    }];
}

#pragma mark -- TabContentScript
- (NSString*)name
{
    return @"findInPageHandler";
}

- (NSString*)scriptMessageHandlerName
{
    return @"findInPageHandler";
}

- (void)userContentController:(WKUserContentController*)userContentController didReceiveScriptMessage:(WKScriptMessage*)message
{
    //{ currentResult: 0, totalResults: 0 }
    //{ totalResults: totalResults }
    //{ currentResult: activeHighlightIndex + 1 }
    //{ currentResult: 0 }
    NSDictionary* params = message.body;
    [[NSNotificationCenter defaultCenter] postNotificationName:kFindInPageHelperNotification object:nil userInfo:params];
}

@end
