//
//  ContextMenuHelper.m
//  PPBrowser
//
//  Created by qingbin on 2022/8/25.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "ContextMenuHelper.h"

#import "Tab.h"
#import "PPNotifications.h"

#import "ReactiveCocoa.h"
#import "ContextMenuModel.h"
#import "NSURL+Extension.h"

#import "NSObject+Helper.h"

@interface ContextMenuHelper ()<UIGestureRecognizerDelegate>

@property (nonatomic, weak) Tab *tab;

@property (nonatomic, strong) ContextMenuModel* model;

@end

@implementation ContextMenuHelper

- (instancetype)initWithTab:(Tab*)tab
{
    self = [super init];
    if(self) {
        self.tab = tab;
        
        [self setupObservers];
    }
    
    return self;
}

- (void)setupObservers
{
    //BUG https://www.jianshu.com/p/f97ba2d183c5
    //长按事件由于手势冲突，只有松手的时候才触发
//    UILongPressGestureRecognizer* longPressGesture = [[UILongPressGestureRecognizer alloc]init];
//    longPressGesture.minimumPressDuration = 0.5;
//    longPressGesture.delegate = self;
//
//    [self.tab.webView addGestureRecognizer:longPressGesture];
//
//    @weakify(self)
//    [[longPressGesture rac_gestureSignal] subscribeNext:^(id x) {
//        @strongify(self)
//        [self handleOnLongPressedWebView:x];
//    }];
}

//- (void)handleOnLongPressedWebView:(UILongPressGestureRecognizer *)gestureRecognizer
//{
//    if(gestureRecognizer.state == UIGestureRecognizerStateBegan) {
//        if(self.model) {
//            [[NSNotificationCenter defaultCenter] postNotificationName:kContextMenuNotification object:self.model];
//        }
//    }
//}

- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldRecognizeSimultaneouslyWithGestureRecognizer:(UIGestureRecognizer *)otherGestureRecognizer
{
    if([otherGestureRecognizer isKindOfClass:UILongPressGestureRecognizer.class]) {
        return YES;
    }

    return NO;
}

- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldBeRequiredToFailByGestureRecognizer:(UIGestureRecognizer *)otherGestureRecognizer
{
    return NO;
}

#pragma mark -- TabContentScript
- (NSString*)name
{
    return @"contextMenuHandler";
}

- (NSString*)scriptMessageHandlerName
{
    return @"contextMenuHandler";
}

- (void)userContentController:(WKUserContentController*)userContentController didReceiveScriptMessage:(WKScriptMessage*)message
{    
    NSDictionary* params = message.body;
    self.model = [[ContextMenuModel alloc]initWithDictionary:params error:nil];
    
    if(self.model.src.length == 0) return;
    
    NSURL* URL = [NSURL URLWithString:self.model.src];
    if(!URL) {
        // 解码已编码的部分
        NSString* url = [self.model.src stringByRemovingPercentEncoding];
        // url编码
        url = [url stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
        URL = [NSURL URLWithString:url];
        if(!URL) return;
        
        self.model.src = url;
    }
    
    ///长按弹窗直接在WebView中实现
    [[NSNotificationCenter defaultCenter] postNotificationName:kContextMenuNotification object:self.model];
    
    NSLog(@"context ...... %d, %s, context = %@",__LINE__, __func__, params);
}

@end
