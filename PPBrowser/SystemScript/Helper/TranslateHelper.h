//
//  TranslateHelper.h
//  PPBrowser
//
//  Created by qingbin on 2024/8/5.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TranslateManager.h"

#import "TabContentScript.h"
@class Tab;

/**
 测试网站：
 1、https://en.m.wikipedia.org/wiki/Dog
 2、https://easylist.to/index.html
 3、https://gist.github.com/haxpor/8c55ce4fcc74167e26983d192d53160e
 4、https://news.ycombinator.com/
 5、https://www.hellomagazine.com/royalty/711543/king-charles-daily-updates-widespread-uk-riots/
 //shadow root的测试在网站的“推广内容”
 //innerHTML和textContent不匹配的内容在作者介绍, &amp
 */

/**
 参考的第三方库:
 https://github.com/chunibyocola/sc-translator-crx
 主要用到了它的google翻译的第三方API调用方式、获取每个段落的算法(结合claude)、还有拿到翻译结果后赋值回去节点的算法
 https://github.com/fishjar/kiss-translator
 主要用到了它监控节点和节点树改变时的算法，还有获取微软翻译API的方法
 */
@interface TranslateHelper : NSObject<TabContentScript>

- (instancetype)initWithTab:(Tab*)tab;

@end

