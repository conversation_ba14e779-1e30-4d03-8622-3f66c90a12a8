//
//  ContextMenuHelper.h
//  PPBrowser
//
//  Created by qingbin on 2022/8/25.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TabContentScript.h"
@class Tab;

//长按事件参考https://juejin.cn/post/6976133465800441869
//源码来源于kode ipa包
//不能设置webkitUserSelect和webkitTouchCallout，否则长按文本复制失效，例如bilibili视频
@interface ContextMenuHelper : NSObject<TabContentScript>

- (instancetype)initWithTab:(Tab*)tab;

@end

