//
//  PlaylistDetectorHelper.m
//  PPBrowser
//
//  Created by qingbin on 2022/4/16.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "PlaylistDetectorHelper.h"

#import "Tab.h"
#import "PPNotifications.h"
#import "PlayModel.h"
#import "NSURL+Extension.h"
#import "SnifferHelper.h"

@interface PlaylistDetectorHelper ()

@property(nonatomic,weak) Tab *tab;

@end

@implementation PlaylistDetectorHelper

- (instancetype)initWithTab:(Tab*)tab
{
    self = [super init];
    if(self) {
        self.tab = tab;
    }
    
    return self;
}

+ (void)pauseElementWithWebView:(WKWebView*)webView xpath:(NSString *)xpath
{
    NSString* js = [NSString stringWithFormat:@"window.__firefox__.playlistDetector.pauseWithXpath(\"%@\")", xpath];
    [webView evaluateJavaScript:js completionHandler:^(id _Nullable obj, NSError * _Nullable error) {
        if(error) {
            NSLog(@"error = %@", error.localizedDescription);
        }
    }];
}

#pragma mark -- TabContentScript
- (NSString*)name
{
    return @"playlistDetector";
}

- (NSString*)scriptMessageHandlerName
{
    return @"playlistDetector";
}

- (void)userContentController:(WKUserContentController*)userContentController didReceiveScriptMessage:(WKScriptMessage*)message
{
    if(!self.tab || !self.tab.webView) return;
    
    UIApplicationState state = [UIApplication sharedApplication].applicationState;
    if(state == UIApplicationStateBackground) {
        //切换到后台之后,如果检测到其它链接,也很触发这里的回调
        //从而导致画中画模式失败
        return;
    }
    
    NSDictionary* params = message.body;
    PlayModel* model = [[PlayModel alloc]initWithDictionary:params error:nil];
    if(self.tab.webView.title.length > 0) {
        //探测的标题不准确,如果webview有标题,优先采用
        model.pageTitle = self.tab.webView.title;
    }

    //保存网址的url
    model.originUrl = [self.tab.webView.URL absoluteString];
    model.status = PlayModelItemStatusPlaylistDetector;
    
    if(model.src.length == 0) return;
    
    //加密链接直接返回
    if([model.src.lowercaseString hasPrefix:@"blob:http"]) {
        return;
    }
    
//    NSURL* URL = [NSURL URLWithString:model.src];
//    if(!URL) {
//        // 解码已编码的部分
//        NSString* url = [model.src stringByRemovingPercentEncoding];
//        // url编码
//        url = [url stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
//        URL = [NSURL URLWithString:url];
//        if(!URL) return;
//        
//        model.src = url;
//    }
    
    NSString* url = [model.src stringByRemovingPercentEncoding];
    // url编码
    url = [url stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];    
    model.src = url;
    
    [[NSNotificationCenter defaultCenter] postNotificationName:kPlaylistDetectorNotification object:model userInfo:@{@"webView":self.tab.webView}];
    
//    NSLog(@"detector ...... %d, %s, video = %@, title = %@",__LINE__, __func__, params, self.tab.webView.title);
}

@end
