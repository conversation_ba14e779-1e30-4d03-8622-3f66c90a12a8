//
//  TranslateHelper.m
//  PPBrowser
//
//  Created by qingbin on 2024/8/5.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import "TranslateHelper.h"

#import "Tab.h"
#import "PPNotifications.h"
#import "BaseNavigationController.h"

#import "UIView+Helper.h"
#import "NSObject+Helper.h"

#import "TagitManager.h"
#import "CopyrightHelper.h"

#import "NSURL+Extension.h"
#import "NSString+Helper.h"

#import "PreferenceManager.h"
#import "CommonDataManager.h"
#import "TranslateManager.h"

@interface TranslateHelper ()

@property(nonatomic,weak) Tab *tab;

@end

@implementation TranslateHelper

- (instancetype)initWithTab:(Tab*)tab
{
    self = [super init];
    if(self) {
        self.tab = tab;
    }
    
    return self;
}

#pragma mark -- TabContentScript

- (NSString *)name
{
    return @"translateHelper";
}

- (NSString*)scriptMessageHandlerName
{
    return @"translateHelper";
}

- (void)userContentController:(WKUserContentController*)userContentController didReceiveScriptMessage:(WKScriptMessage*)message
{
    if(!self.tab || !self.tab.webView) return;
    
    NSDictionary* params = message.body;
    [self handleHttpRequest:params];
}

- (void)handleHttpRequest:(NSDictionary *)requestInfo
{
    BOOL hasFinishTranslated = [requestInfo[@"hasFinishTranslated"] boolValue];
    if(hasFinishTranslated) {
        //翻译状态变更
        [CommonDataManager shareInstance].translateStatus = TranslateStatusRestore;
        [[NSNotificationCenter defaultCenter] postNotificationName:kTranslateStatusDidChangedNotification object:nil];
        return;
    }
    
    BOOL fetchTranslateStatus = [requestInfo[@"fetchTranslateStatus"] boolValue];
    if(fetchTranslateStatus) {
        //获取当前是否打开了网页翻译功能
        TranslateStatus translateStatus = [CommonDataManager shareInstance].translateStatus;
        if(translateStatus == TranslateStatusLoading || translateStatus == TranslateStatusRestore) {
            //正在翻译中
            [TranslateManager startTranslatePageWithWebView:self.tab.webView];
            return;
        } else {
            //没有打开翻译功能/或者没有点击翻译按钮
        }
    }
    
    NSString *requestId = requestInfo[@"requestId"];
    NSString *urlString = requestInfo[@"url"];
    NSString *method = requestInfo[@"method"];
    NSString *body = requestInfo[@"body"];
    NSDictionary *header = requestInfo[@"headers"];
    
    if (!requestId || !urlString || !method) {
        return;
    }
    
    NSLog(@"请求体: %@", body);
    
    NSURL *url = [NSURL URLWithString:urlString];
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:url];
    request.HTTPMethod = method;
    
    if (body.length > 0) {
        NSData *jsonData = [body dataUsingEncoding:NSUTF8StringEncoding];
        request.HTTPBody = jsonData;
        [request setValue:@"application/json" forHTTPHeaderField:@"Content-Type"];
    }
    
    [header enumerateKeysAndObjectsUsingBlock:^(NSString*  _Nonnull key, NSString*  _Nonnull obj, BOOL * _Nonnull stop) {
        [request setValue:obj forHTTPHeaderField:key];
    }];
    
    NSURLSessionDataTask *task = [[NSURLSession sharedSession] dataTaskWithRequest:request completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
        BOOL succ = YES;
        NSString* responseText = @"";
        if (error) {
            succ = NO;
        } else if (data) {
            succ = YES;
            responseText = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
            
            NSDictionary* dict = [NSString jsonConvertToObject:responseText];
            if(dict) {
                responseText = [NSString convertToJsonString:dict options:YES];
            }
        }
        
        NSLog(@"%@", responseText);
        
        dispatch_async(dispatch_get_main_queue(), ^{
            [self sendResponseToJavaScript:requestId succ:succ responseText:responseText];
        });
    }];
    
    [task resume];
}

- (void)sendResponseToJavaScript:(NSString *)requestId
                            succ:(BOOL)succ
                    responseText:(NSString *)responseText
{
    //使用单引号解决json字符串转义失败的问题
    //解决转义失败的问题?
    /**
     例如：
     let jsonString = '[{"detectedLanguage":{"language":"en","score":0.0},"translations":[{"text":"\".","to":"zh-Hans"}]}]';
     JSON.parse(jsonString);
     */

    //有可能是接口延迟，先检查一下状态
    TranslateStatus translateStatus = [CommonDataManager shareInstance].translateStatus;
    if(translateStatus == TranslateStatusDefault) {
        //已经恢复到默认状态，那么则不再翻译
        return;
    }
    
    if(responseText.length > 0) {
        //遇到了特殊字符 '  导致入参失败
        NSString* encodedText = [responseText encodeURIComponent];
        NSString* js = [NSString stringWithFormat:@"window.translatorHttpCallback_%@(%d, '%@')", requestId, succ, encodedText];
        [self.tab.webView evaluateJavaScript:js completionHandler:nil];
    }
}


@end
