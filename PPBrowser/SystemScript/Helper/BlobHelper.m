//
//  BlobHelper.m
//  PPBrowser
//
//  Created by qingbin on 2025/1/18.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "BlobHelper.h"

#import "Tab.h"
#import "PPNotifications.h"
#import "BaseNavigationController.h"

#import "UIView+Helper.h"
#import "NSObject+Helper.h"

#import "TagitManager.h"
#import "CopyrightHelper.h"

#import "NSURL+Extension.h"
#import "NSString+Helper.h"

@interface BlobHelper()

@property(nonatomic,weak) Tab *tab;

@end

@implementation BlobHelper

- (instancetype)initWithTab:(Tab*)tab
{
    self = [super init];
    if(self) {
        self.tab = tab;
    }
    
    return self;
}

#pragma mark -- TabContentScript

- (NSString *)name
{
    return @"blobHelper";
}

- (NSString*)scriptMessageHandlerName
{
    return @"blobHelper";
}

- (void)userContentController:(WKUserContentController*)userContentController didReceiveScriptMessage:(WKScriptMessage*)message
{
    if(!self.tab || !self.tab.webView) return;
    
    UIApplicationState state = [UIApplication sharedApplication].applicationState;
    if(state == UIApplicationStateBackground) {
        //切换到后台之后,如果检测到其它链接,也很触发这里的回调
        //从而导致画中画模式失败
        return;
    }
    
    NSDictionary* params = message.body;
    
    PlayModel* model = [[PlayModel alloc]initWithDictionary:params error:nil];
    if(self.tab.webView.title.length > 0) {
        //探测的标题不准确,如果webview有标题,优先采用
        model.pageTitle = self.tab.webView.title;
    }

    //保存网址的url
    model.originUrl = [self.tab.webView.URL absoluteString];
    model.pageSrc = model.originUrl;
    model.status = PlayModelItemStatusBlobDetector;
    
    if(model.src.length == 0) return;
    
    NSURL* URL = [NSURL URLWithString:model.src];
    if(!URL) {
        // 解码已编码的部分
        NSString* url = [model.src stringByRemovingPercentEncoding];
        // url编码
        url = [url stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
        URL = [NSURL URLWithString:url];
        if(!URL) return;
        
        model.src = url;
    }
    
    //只更新视频数量，显示的逻辑还是由Playlist来决定
    [[NSNotificationCenter defaultCenter] postNotificationName:kPlaylistDetectorNotification object:model userInfo:@{@"webView":self.tab.webView}];
    
//    NSLog(@"Blob ...... %d, %s, video = %@, title = %@",__LINE__, __func__, params, self.tab.webView.title);
    
}

@end
