//
//  ScreenshotHelper.m
//  PandaBrowser
//
//  Created by qing<PERSON> on 2022/3/4.
//

#import "ScreenshotHelper.h"

#import "InternalURL.h"
#import "Tab.h"
#import "UIView+Helper.h"

#import "SDImageCache.h"
#import "SDWebImageManager.h"
#import "UIImageView+WebCache.h"

@interface ScreenshotHelper ()

@property (nonatomic, strong) dispatch_queue_t queue;

@end

@implementation ScreenshotHelper

+ (instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    static ScreenshotHelper* obj;
    dispatch_once(&onceToken, ^{
        obj = [ScreenshotHelper new];
    });
    
    return obj;
}

- (instancetype)init
{
    self = [super init];
    if(self) {
        self.queue = dispatch_queue_create("com.screenshothelper", DISPATCH_QUEUE_SERIAL);
    }
    
    return self;
}

- (void)takeScreenshot:(Tab *)tab
{
    if(!tab) return;
    
#if DEBUG
    NSLog(@"截图 tabId = %@, title = %@, %d, %s", tab.model.tabId, tab.model.title?:@"", __LINE__, __func__);
#endif
    
    NSURL* URL = tab.webView.URL;
    //URL为空，直接返回
    if (!URL) return;
    
    NSString* url = [tab.webView.URL absoluteString];
    NSString* key = [ScreenshotHelper keyForId:tab.model.tabId];
    NSURL* keyUrl = [NSURL URLWithString:key];
    
    if ([InternalURL isAboutHomeURL:url]) {
        //首页截图
        if(!tab || !tab.ntpView) return;
        UIView* homePanel = tab.ntpView;
        
        __block UIImage* screenshot;
        
        screenshot = [homePanel screenshotWithAspectRatio:0 offset:CGPointZero quality:1];
        
        if(screenshot) {
            //清缓存
            [[SDWebImageManager sharedManager].imageCache removeImageForKey:key];
            //保存图片
            [[SDWebImageManager sharedManager] saveImageToCache:screenshot forURL:keyUrl];
        } else {
            //截图失败
            NSLog(@"首页截图生成失败.....");
        }
    } else {
        //如果没有webView，那么也不截图
        if (!tab || !tab.webView || !tab.webView.superview) return;
        
        WKSnapshotConfiguration* configuration = [WKSnapshotConfiguration new];
        //v2.7.2，指定截图范围，针对比较长的网页，这里可以减小一点时间
        CGRect rect = CGRectMake(0, 0, kScreenWidth, kScreenHeight);
        configuration.rect = rect;
        configuration.afterScreenUpdates = NO;
        //v2.7.3，倒数第二个Tab白屏的复现：在Tabtray页，删除最后一个Tab，然后新增一个Tab，重新打开APP，那么必白屏。
        
        [tab.webView takeSnapshotWithConfiguration:configuration completionHandler:^(UIImage * _Nullable screenshot, NSError * _Nullable error) {
            if(screenshot && !error) {
                //清缓存
                [[SDWebImageManager sharedManager].imageCache removeImageForKey:key];
                //保存图片
                [[SDWebImageManager sharedManager] saveImageToCache:screenshot forURL:keyUrl];
            } else {
                //截图失败
                NSLog(@"截图生成失败.....");
            }
        }];
    }
}

#pragma mark - 清理截图

+ (void)removeScreenshot:(NSString *)tabId
{
    if (tabId.length == 0) return;
    
    NSString* key = [ScreenshotHelper keyForId:tabId];
    [[SDImageCache sharedImageCache] removeImageForKey:key];
}

- (void)asyncOnQueue:(void(^)(void))executeBlock
{
    dispatch_async(self.queue, ^{
        if(executeBlock) {
            executeBlock();
        }
    });
}

+ (NSString*)keyForId:(NSString*)tabId
{
    NSString* key = [NSString stringWithFormat:@"sdimage://%@",tabId];
    return key;
}

@end
