//
//  ScreenshotHelper.h
//  PandaBrowser
//
//  Created by q<PERSON><PERSON> on 2022/3/4.
//

#import <Foundation/Foundation.h>
#import "TabManager.h"

@class Tab;

@interface ScreenshotHelper : NSObject

+ (instancetype)shareInstance;

- (void)takeScreenshot:(Tab*)tab;

+ (void)removeScreenshot:(NSString *)tabId;

+ (NSString *)keyForId:(NSString*)tabId;

- (void)asyncOnQueue:(void(^)(void))executeBlock;

//- (void)ss_setImageWithTabId:(NSString*)tabId
//                   imageView:(UIImageView*)targetView
//                        asyc:(BOOL)async;

@property(nonatomic,weak) TabManager *tabManager;

@end

