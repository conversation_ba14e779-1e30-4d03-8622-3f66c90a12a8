//
//  GetAllImageHelper.m
//  PPBrowser
//
//  Created by qingbin on 2022/4/26.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "GetAllImageHelper.h"

#import "Tab.h"
#import "PPNotifications.h"
#import "PPBrowser-Swift.h"

@interface GetAllImageHelper ()

@property(nonatomic,weak) Tab *tab;

@end

@implementation GetAllImageHelper

- (instancetype)initWithTab:(Tab*)tab
{
    self = [super init];
    if(self) {
        self.tab = tab;
    }
    
    return self;
}

+ (void)getAllImageWithWebView:(WKWebView*)webView
{
    NSString* js = [NSString stringWithFormat:@"window.__firefox__.getAllImageHandler()"];
    [webView evaluateJavaScript:js completionHandler:^(id _Nullable obj, NSError * _Nullable error) {
        if(error) {
            NSLog(@"error = %@", error.localizedDescription);
        }
    }];
}

#pragma mark -- TabContentScript
- (NSString*)name
{
    return @"getAllImageHelper";
}

- (NSString*)scriptMessageHandlerName
{
    return @"getAllImageHelper";
}

- (void)userContentController:(WKUserContentController*)userContentController didReceiveScriptMessage:(WKScriptMessage*)message
{
    NSDictionary* params = message.body;
    NSString* res = params[@"result"];
    NSData* data = [res dataUsingEncoding:NSUTF8StringEncoding];
    NSError* error;
    NSArray* images = [NSJSONSerialization JSONObjectWithData:data options:kNilOptions error:&error];
    
    [[NSNotificationCenter defaultCenter] postNotificationName:kGetAllImageHelperNotification object:images userInfo:@{@"webView":self.tab.webView}];
    
//    NSLog(@"detector ...... %d, %s, video = %@",__LINE__, __func__, params);
}

@end
