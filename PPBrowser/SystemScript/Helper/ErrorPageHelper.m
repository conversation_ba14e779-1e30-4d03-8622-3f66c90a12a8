//
//  ErrorPageHelper.m
//  PPBrowser
//
//  Created by qingbin on 2022/5/9.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "ErrorPageHelper.h"

#import "Tab.h"
#import "PPNotifications.h"
#import "InternalURL.h"

@interface ErrorPageHelper ()

@property(nonatomic,weak) Tab *tab;

@end

@implementation ErrorPageHelper

- (instancetype)initWithTab:(Tab*)tab
{
    self = [super init];
    if(self) {
        self.tab = tab;
    }
    
    return self;
}

#pragma mark -- TabContentScript
- (NSString*)name
{
    return @"errorPageHelper";
}

- (NSString*)scriptMessageHandlerName
{
    return @"errorPageHelper";
}

- (void)userContentController:(WKUserContentController*)userContentController didReceiveScriptMessage:(WKScriptMessage*)message
{
    if(!self.tab || !self.tab.webView) return;
    
    NSDictionary* params = message.body;
    BOOL res = [params[@"reload"] boolValue];
    if(res) {
        NSString* urlString = params[@"url"];
        
//        NSRange range = [urlString rangeOfString:@"errorUrl="];
//        NSInteger location = range.location+range.length;
//        NSInteger length = urlString.length - (range.location+range.length);
//        NSString* errorUrl = [urlString substringWithRange:NSMakeRange(location, length)];
        
        NSString* errorUrl = [InternalURL extractErrorUrlFromUrlString:urlString];
        
        NSString* js = [NSString stringWithFormat:@"window.location.replace(\'%@\')",errorUrl];
        [self.tab.webView evaluateJavaScript:js completionHandler:^(id _Nullable obj, NSError * _Nullable error) {
            if(error) {
                NSLog(@"error = %@", error.localizedDescription);
            }
        }];
        
    } else {
        //返回
        [self.tab goBack];
    }
}

//record errorUrl
//var index = url.search("errorUrl");
//var errorUrl = JSON.parse(unescape(pageUrl.substring(index + "errorUrl=".length)));
//url.searchParams.set("errorUrl", errorUrl)

@end
