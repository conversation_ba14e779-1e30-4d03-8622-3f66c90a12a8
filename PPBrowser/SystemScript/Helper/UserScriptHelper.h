//
//  UserScriptHelper.h
//  PPBrowser
//
//  Created by qingbin on 2022/5/19.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TabContentScript.h"
#import "UserComandModel.h"
#import "UserCommandManager.h"

#import "UserScript.h"

@class Tab;

@interface UserScriptHelper : NSObject<TabContentScript>

- (instancetype)initWithTab:(Tab*)tab;

//greasyfork安装脚本
//+ (void)installScriptWithItem:(UserComandModel *)item tab:(Tab *)tab;

//根据URL安装脚本(https://)
+ (void)installScriptWithURL:(NSURL *)URL tab:(Tab *)tab;

//通过分享过来的脚本文件(file://)
+ (void)installScriptFromFileWithURL:(NSURL *)URL tab:(Tab *)tab;

//通过ActionExtension分享过来的脚本文件(file://) 或者通过导入文件的脚本文件
+ (void)installScriptFromFileWithJsContent:(NSString *)jsContent tab:(Tab *)tab;

//给从文件中读取的脚本解析出来的UserScript，添加默认值
+ (void)updateDefaultValueWithUserScript:(UserScript *)userScript;

@end
