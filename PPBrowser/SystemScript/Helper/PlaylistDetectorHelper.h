//
//  PlaylistDetectorHelper.h
//  PPBrowser
//
//  Created by qingbin on 2022/4/16.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>

#import "TabContentScript.h"
@class Tab;

@interface PlaylistDetectorHelper : NSObject<TabContentScript>

- (instancetype)initWithTab:(Tab*)tab;

+ (void)pauseElementWithWebView:(WKWebView*)webView xpath:(NSString *)xpath;

@end


