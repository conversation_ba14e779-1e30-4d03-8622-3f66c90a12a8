//
//  MetadataHelper.m
//  PPBrowser
//
//  Created by qingbin on 2022/5/26.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "MetadataHelper.h"

#import "Tab.h"
#import "PPNotifications.h"

@interface MetadataHelper ()

@property(nonatomic,weak) Tab *tab;

@end

@implementation MetadataHelper

- (instancetype)initWithTab:(Tab*)tab
{
    self = [super init];
    if(self) {
        self.tab = tab;
    }
    
    return self;
}

+ (void)getMetadataWithWebView:(WKWebView *)webView
{
    //MetadataHelper.js，这里有个神奇的逻辑，搞不懂，必须要加webkit.messageHandlers.metadataHelper.postMessage(message);
    //否则程序不调用
    NSString* js = [NSString stringWithFormat:@"window.__firefox__.metadataHelper.findMetaData()"];
    [webView evaluateJavaScript:js completionHandler:^(id _Nullable obj, NSError * _Nullable error) {
        if(error) {
            NSLog(@"error = %@", error.localizedDescription);
        }
    }];
}

#pragma mark -- TabContentScript
- (NSString*)name
{
    return @"metadataHelper";
}

- (NSString*)scriptMessageHandlerName
{
    return @"metadataHelper";
}

- (void)userContentController:(WKUserContentController*)userContentController didReceiveScriptMessage:(WKScriptMessage*)message
{
    NSDictionary* params = message.body;
    
    [[NSNotificationCenter defaultCenter] postNotificationName:kGetMetadataNotification
                                                        object:params
                                                      userInfo:@{@"webView":self.tab.webView}];
    
    NSLog(@"metadata ...... %d, %s, meta = %@",__LINE__, __func__, params);
}

@end
