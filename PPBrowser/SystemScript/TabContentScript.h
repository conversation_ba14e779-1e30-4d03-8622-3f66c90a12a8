//
//  TabContentScript.h
//  PandaBrowser
//
//  Created by q<PERSON><PERSON> on 2022/3/10.
//

#import <Foundation/Foundation.h>
#import <WebKit/WebKit.h>

@protocol TabContentScript <WKScriptMessageHandler>

- (NSString*)name;
- (NSString*)scriptMessageHandlerName;
- (void)userContentController:(WKUserContentController*)userContentController didReceiveScriptMessage:(WKScriptMessage*)message;

@end

@interface TabContentScript : NSObject

@end
