//
//  PreferenceManager.m
//  PPBrowser
//
//  Created by qingbin on 2022/3/20.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "PreferenceManager.h"
#import "BrowserUtils.h"

@interface PreferenceManager ()

@end

@implementation PreferenceManager

+ (instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    static PreferenceManager* obj;
    dispatch_once(&onceToken, ^{
        obj = [PreferenceManager new];
    });
    
    return obj;
}

- (instancetype)init
{
    self = [super init];
    if(self) {
        //不保存isNoImage到plist
        [PreferenceModel mj_setupIgnoredCodingPropertyNames:^NSArray *{
            return @[@"isNoImage"];
        }];
        
        //不保存壁纸
        [WallpaperModel mj_setupIgnoredCodingPropertyNames:^NSArray *{
            return @[@"wallpaper"];
        }];
    }
    
    return self;
}

// 编码保存
- (void)encode
{
    if(self.items) {
        NSData* data = [NSKeyedArchiver archivedDataWithRootObject:self.items];
        [[NSUserDefaults standardUserDefaults] setObject:data forKey:@"key_preferencemanager_commonconfig"];
    }
    
    if(self.wallpaperModel) {
        NSData* data = [NSKeyedArchiver archivedDataWithRootObject:self.wallpaperModel];
        [[NSUserDefaults standardUserDefaults] setObject:data forKey:@"key_preferencemanager_wallpaper"];
    }
}

// 解码读数据
- (void)decode
{
    NSData* data = [[NSUserDefaults standardUserDefaults] objectForKey:@"key_preferencemanager_commonconfig"];
    self.items = [NSKeyedUnarchiver unarchiveObjectWithData:data];
        
    data = [[NSUserDefaults standardUserDefaults] objectForKey:@"key_preferencemanager_wallpaper"];
    self.wallpaperModel = [NSKeyedUnarchiver unarchiveObjectWithData:data];
    
    if(!self.items) {
        self.items = [PreferenceModel new];
    }
    
    LocalizableOption localOption = [BrowserUtils localizableOption];
    
    //根据不同的语言，设置不同的搜索引擎(有外国用户反馈这个问题了)
    if(!self.items.searchEngineType) {
        if(localOption == LocalizableOptionZh_Hans) {
            //大陆地区设置为秘塔AI,v2.7.6
            if (!self.items.searchEngineType) {
                self.items.searchEngineType = @(SearchEngineTypeMetaso);
            }
        } else {
            //其它地区设置为谷歌搜索
            self.items.searchEngineType = @(SearchEngineTypeGoogle);
        }
    }
    
    //v2.7.6,秘塔AI为默认搜索引擎
    if(localOption == LocalizableOptionZh_Hans) {
        //大陆地区设置为秘塔AI
        //对于存量用户，检测是否已经初始化过一次秘塔AI
        if (self.items.hasUpdateMetaso) {
            //已经初始化了，那么则用用户所选的
        } else {
            //还没初始化，先重置为秘塔AI
            self.items.searchEngineType = @(SearchEngineTypeMetaso);
            self.items.hasUpdateMetaso = @(YES);
        }
    }
    
    //v2.7.0，重新设置回百度搜索
//    if(localOption == LocalizableOptionZh_Hans) {
//        //大陆地区重置为百度搜索
//        if(!self.items.resetBaiduSearch) {
//            self.items.searchEngineType = @(SearchEngineTypeBaidu);
//        }
//    }
//    self.items.resetBaiduSearch = @(YES);
    
    if(!self.items.isPrivate) {
        self.items.isPrivate = @(NO);
    }
    
    if(!self.items.isNoImage) {
        self.items.isNoImage = @(NO);
    }
    
    //视频模式
    if(!self.items.enabledPlayer) {
        self.items.enabledPlayer = @(YES);
    }
    
    //阅读模式
    if(!self.items.enabledReader) {
        self.items.enabledReader = @(YES);
    }
    
    if(!self.items.enabledLongPressPlayer) {
        self.items.enabledLongPressPlayer = @(YES);
    }
    
    if(!self.items.enabledAdblock) {
        self.items.enabledAdblock = @(YES);
    }
    
    if(!self.items.isAgreedUMengPermission) {
        self.items.isAgreedUMengPermission = @(NO);
    }
    
    //点击UA弹窗直接收起弹窗
    if(!self.items.isHideUAViewAfterTapAction) {
        self.items.isHideUAViewAfterTapAction = @(NO);
    }
    
    //默认不显示进度条,太丑了
    if(!self.items.isShowSliderWhenLandscape) {
        self.items.isShowSliderWhenLandscape = @(NO);
    }
    
    if(!self.items.reviewCount) {
        self.items.reviewCount = @(0);
    }
    
    if(!self.items.isFullScreen) {
        self.items.isFullScreen = @(NO);
    }
    
    if(!self.items.isDarkTheme) {
        self.items.isDarkTheme = @(NO);
    }
    
    if(!self.items.toolBarOption) {
        self.items.toolBarOption = @(ToolbarOptionDefault);
    }
    
    if(!self.items.darkModeStatus) {
        self.items.darkModeStatus = @(DarkModeStatusAuto);
    }
    
    if(!self.items.isDarkTheme) {
        self.items.isDarkTheme = @(NO);
    }
    
    if(!self.items.enablediCloud2) {
        self.items.enablediCloud2 = @(NO);
    }

    if(!self.items.openLastWindow) {
        self.items.openLastWindow = @(YES);
    }
    
    if(!self.items.enabledFixupAdblock) {
        self.items.enabledFixupAdblock = @(NO);
    }
    
    //默认行为,否则jable.tv打不开,有点坑爹
    if(!self.items.popupWindowOption) {
        self.items.popupWindowOption = @(PopupWindowOptionDefault);
    }
    
    if(!self.items.isOpenBiometricVerify) {
        //默认关闭
        self.items.isOpenBiometricVerify = @(NO);
    }
    
    if(!self.items.enabledTagit) {
        //默认关闭
        self.items.enabledTagit = @(NO);
    }
    
    if(!self.items.fontSize) {
        //感觉大多数都不会设置字体
        //没有则不需要嵌入脚本,没必要浪费性能
    }
        
    if(!self.items.enabledPullToRefesh) {
        self.items.enabledPullToRefesh = @(YES);
    }
    
    //默认关闭，容易误触
    if(!self.items.enabledDetectPhoto) {
        self.items.enabledDetectPhoto = @(NO);
    }
    
    if(!self.items.enabledPreview) {
        self.items.enabledPreview = @(YES);
    }
    
    if(!self.items.today) {
        self.items.today = [NSDate date];
    }
    
    //播放设置
    if(!self.items.playStatus) {
        self.items.playStatus = @(PlayModeStatusDefautl);
    }
    
    //壁纸
    if(!self.wallpaperModel) {
        self.wallpaperModel = [WallpaperModel new];
    }
    
    if(!self.wallpaperModel.wp_lightColor) {
        self.wallpaperModel.wp_lightColor = @(NO);
    }
    
    if(!self.wallpaperModel.wp_showLogo) {
        self.wallpaperModel.wp_showLogo = @(YES);
    }

    if(!self.wallpaperModel.wp_hasWallpaper) {
        self.wallpaperModel.wp_hasWallpaper = @(NO);
    }

    //翻译引擎
    if(!self.items.translateEngine) {        
        if(localOption == LocalizableOptionZh_Hans) {
            self.items.translateEngine = @(TranslateEngineBing);
        } else if(localOption == LocalizableOptionZh_Hant) {
            self.items.translateEngine = @(TranslateEngineGoogle);
        } else {
            self.items.translateEngine = @(TranslateEngineGoogle);
        }
    }
    //翻译的目标语言
    if(!self.items.tranlsateTargetLanguage) {
        if(localOption == LocalizableOptionZh_Hans) {
            self.items.tranlsateTargetLanguage = @(LanguageSimplifiedChinese);
        } else if(localOption == LocalizableOptionZh_Hant) {
            self.items.tranlsateTargetLanguage = @(LanguageTraditionalChinese);
        } else {
            self.items.tranlsateTargetLanguage = @(LanguageEnglish);
        }
    }
    
    //书签显示的顺序
    if(!self.items.bookMarkSortType) {
        //默认时间降序
        self.items.bookMarkSortType = @(FileSortTypeTimeDescend);
    }
    
    if(!self.items.customUrl) {
        self.items.customUrl = @"";
    }
    
    if(!self.items.swipeCloseTabTrayType) {
        self.items.swipeCloseTabTrayType = @(SwipeCloseTabTrayTypeFan);
    }
        
    //2.6.3 智能拼页，默认关闭
    //2.7.1, 默认开启，让用户可以感受到自动翻页功能
    if(!self.items.isAutoPage) {
        self.items.isAutoPage = @(YES);
    }
    
    //默认显示分割线
    if(!self.items.isShowSeparatorLine) {
        self.items.isShowSeparatorLine = @(YES);
    }
    
    //2.6.3 智能拼页，开启智能检测
    //2.7.1,默认关闭，容易出错
    if(!self.items.isEnabledAutoPageAutoDetect) {
        self.items.isEnabledAutoPageAutoDetect = @(NO);
    }
    
    //2.6.3 标签页排列方式，默认从上到下
//    if(!self.items.tabTrayArrangeType) {
//        self.items.tabTrayArrangeType = @(TabTrayArrangeTypeTopToBottom);
//    }
    
    //2.6.8 地址栏显示内容，默认标题
    if(!self.items.toptoolDisplayMode) {
        self.items.toptoolDisplayMode = @(AddressBarDisplayModeTitle);
    }
    
    //2.6.8 开启前进后退手势
    if(!self.items.enableSwipeNavigation) {
        //默认开启
        self.items.enableSwipeNavigation = @(YES);
    }
    
    //2.6.8 开启手势震动反馈
    if(!self.items.enableGestureHaptics) {
        //默认开启
        self.items.enableGestureHaptics = @(YES);
    }
    
    //2.7.1 搜索设置
    if(!self.items.isShowSearchHistory) {
        self.items.isShowSearchHistory = @(YES);
    }
    
    if(!self.items.isReadPasteboard) {
        self.items.isReadPasteboard = @(YES);
    }
    
    if(!self.items.isShowSearchSuggestion) {
        self.items.isShowSearchSuggestion = @(YES);
    }
    
//    if(!self.items.isShowSearchEngineBar) {
//        self.items.isShowSearchEngineBar = @(YES);
//    }
    
    //2.7.4, 隐私模式下是否共享登录状态
    if (!self.items.sharedSession) {
        //默认共享登录状态
        self.items.sharedSession = @(YES);
    }
 }

@end
