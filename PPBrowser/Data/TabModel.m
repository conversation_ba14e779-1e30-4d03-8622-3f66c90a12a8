//
//  TabModel.m
//  PandaBrowser
//
//  Created by q<PERSON><PERSON> on 2022/3/4.
//

#import "TabModel.h"
#import "SDImageCache.h"
#import "SDWebImageManager.h"
#import "DatabaseUnit+Helper.h"

#import "InternalURL.h"

@interface TabModel ()

@end

@implementation TabModel

//新开一页
+ (instancetype)tabModelForNewHomePanel
{
    NSString* url = [InternalURL homeUrl];
    TabModel* tabModel = [TabModel new];
    tabModel.tabId = [NSUUID UUID].UUIDString;
    tabModel.url = url;
    
    return tabModel;
}

+ (instancetype)tabModelWithUrl:(NSString *)url
{
    TabModel* tabModel = [TabModel new];
    tabModel.tabId = [NSUUID UUID].UUIDString;
    tabModel.url = url;
    
    return tabModel;
}

- (void)updateTitleWithWebViewTitle:(NSString*)webViewTitle
                                url:(NSURL*)url
                             toDisk:(BOOL)toDisk
{
    NSString* title = webViewTitle;
    if(title.length > 0) {
        if([title containsString:@"localhost"]) {
            title = @"";
        }
    } else {
        NSString* urlString = [url absoluteString];
        if([InternalURL isAboutHomeURL:urlString]) {
            title = NSLocalizedString(@"tab.newTab", nil);
        } else {
            title = @"";
        }
    }
    
    if(toDisk) {
        DatabaseUnit* unit = [DatabaseUnit updateTabTitleWithTabId:self.tabId title:title];
        DB_EXEC(unit);
    }
    
    self.title = title;
}

//当前index下是不是首页，需要判断历史url
- (BOOL)isCurrentTabAtHome
{
    if (self.url.length == 0) {
        return NO;
    }
    
    if([InternalURL isAboutHomeURL:self.url]) {
        //首页
        return YES;
    }
    
    return NO;
}

@end


@implementation TabSessionRestoreModel
@end
