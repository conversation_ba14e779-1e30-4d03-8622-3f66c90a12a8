//
//  PreferenceModel.h
//  PPBrowser
//
//  Created by qingbin on 2022/6/6.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>

#import "PPEnums.h"
#import "MJExtension.h"

@interface PreferenceModel : NSObject

//UserAgentType
//@property (nonatomic, strong) NSNumber* userAgent;

//当前选中的搜索引擎
@property (nonatomic, strong) NSNumber* searchEngineType;
//v2.7.0,还是设置百度搜索为默认搜索引擎
//v2.7.6,初始化时，设置秘塔AI为默认的搜索引擎
@property (nonatomic, strong) NSNumber *hasUpdateMetaso;
//v2.7.0，重新设置回百度搜索
//@property (nonatomic, strong) NSNumber *resetBaiduSearch;
// 隐私模式
@property (nonatomic, strong) NSNumber* isPrivate;
// 隐私模式下是否共享登录状态
@property (nonatomic, strong) NSNumber *sharedSession;
// 无图模式
@property (nonatomic, strong) NSNumber* isNoImage;
//视频模式
@property (nonatomic, strong) NSNumber* enabledPlayer;
//是否开启长按识别视频
@property (nonatomic, strong) NSNumber* enabledLongPressPlayer;
//是否开启长按识别图片(默认关闭，容易误触)
@property (nonatomic, strong) NSNumber* enabledDetectPhoto;

@property (nonatomic, strong) NSNumber* enabledReader;

@property (nonatomic, strong) NSNumber* enabledAdblock;

@property (nonatomic, strong) NSNumber* isShowSliderWhenLandscape;

@property (nonatomic, strong) NSNumber* isAgreedUMengPermission;
//是否是会员
//@property (nonatomic, strong) NSNumber* isVip;
//暗黑模式, 0-跟随系统, 1-浅色, 2-深色
@property (nonatomic, strong) NSNumber* darkModeStatus;

//开启iCloud
@property (nonatomic, strong) NSNumber* enablediCloud2;

//iCloud同步时间戳
@property (nonatomic, strong) NSNumber *iCloudSyncTimestamp;

//大于2次之后则弹评分弹窗
@property (nonatomic, strong) NSNumber *reviewCount;

//全屏模式
@property (nonatomic, strong) NSNumber* isFullScreen;

//暗黑模式
@property (nonatomic, strong) NSNumber* isDarkTheme;

//底部工具栏的样式
@property (nonatomic, strong) NSNumber* toolBarOption;

//启动时恢复窗口
@property (nonatomic, strong) NSNumber* openLastWindow;

//侵入式广告
@property (nonatomic, strong) NSNumber* enabledFixupAdblock;

//弹出式窗口选项, 1每次都询问,2总是当前窗口打开,3总是新窗口打开,4总是后台窗口打开,5默认行为
@property (nonatomic, strong) NSNumber* popupWindowOption;

//是否开启面容识别
@property (nonatomic, strong) NSNumber *isOpenBiometricVerify;

//解锁标记模式
@property (nonatomic, strong) NSNumber *enabledTagit;

//字体大小
@property (nonatomic, strong) NSNumber *fontSize;

//是否显示搜索历史
@property (nonatomic, strong) NSNumber* isShowSearchHistory;

//点击UA弹窗直接收起弹窗
@property (nonatomic, strong) NSNumber* isHideUAViewAfterTapAction;

//开启下拉刷新
@property (nonatomic, strong) NSNumber* enabledPullToRefesh;

//播放方式(列表循环/单曲循环)
@property (nonatomic, strong) NSNumber* playStatus;

//长按开启预览，默认开启
@property (nonatomic, strong) NSNumber* enabledPreview;
//
@property (nonatomic, strong) NSDate* today;

//翻译引擎(根据所在地区设置默认值)
@property (nonatomic, strong) NSNumber *translateEngine;
//翻译的目标语言(根据所在地区设置默认值)
@property (nonatomic, strong) NSNumber *tranlsateTargetLanguage;

//书签升序/降序, 0-降序, 1-升序 BookMarkSortType
@property (nonatomic, strong) NSNumber *bookMarkSortType;

//自定义首页url
@property (nonatomic, strong) NSString *customUrl;

//关闭网页标签方式，扇形滑动，水平滑动，无效果, SwipeCloseTabTrayType
@property (nonatomic, strong) NSNumber *swipeCloseTabTrayType;

//搜索历史读取粘贴板
@property (nonatomic, strong) NSNumber* isReadPasteboard;

//2.6.3 是否开启智能拼页
@property (nonatomic, strong) NSNumber *isAutoPage;
//2.6.3 是否开启智能检测
@property (nonatomic, strong) NSNumber *isEnabledAutoPageAutoDetect;
//2.6.3 是否显示智能拼页分割线
@property (nonatomic, strong) NSNumber *isShowSeparatorLine;

// 标签页排列方式 (0:从上到下 1:从下到上)
//@property (nonatomic, strong) NSNumber *tabTrayArrangeType;

//2.6.8 地址栏显示内容
@property (nonatomic, strong) NSNumber *toptoolDisplayMode;
//2.6.8 开启前进后退手势
@property (nonatomic, strong) NSNumber *enableSwipeNavigation;
//2.6.8 开启手势震动反馈
@property (nonatomic, strong) NSNumber *enableGestureHaptics;
//2.7.1 显示搜索建议
@property (nonatomic, strong) NSNumber *isShowSearchSuggestion;
//2.7.1 显示搜索引擎切换条
//@property (nonatomic, strong) NSNumber *isShowSearchEngineBar;

@end
