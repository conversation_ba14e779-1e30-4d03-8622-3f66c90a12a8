//
//  PPEnums.h
//  PPBrowser
//
//  Created by qing<PERSON> on 2022/4/6.
//  Copyright © 2022 qingbin. All rights reserved.
//

#ifndef PPEnums_h
#define PPEnums_h

typedef enum ContentBlockItemType {
    ContentBlockItemTypeAd = 1,
    ContentBlockItemTypeImage,
    ContentBlockItemTypeTrackers,
    ContentBlockItemTypeHttp,
    ContentBlockItemTypeCookie
} ContentBlockItemType;

typedef NS_ENUM(NSInteger,HistorySearchType) {
    HistorySearchTypeUnknown = 0,
    HistorySearchTypeKeyword = 1,           //搜索引擎
    HistorySearchTypeUrl = 2,               //网址url
    HistorySearchTypePasteboard = 3,        //剪贴板,辅助
};

typedef NS_ENUM(NSInteger,BookMarkType) {
    BookMarkTypeUnknown = 0,
    BookMarkTypeFile   = 1,         //文件
    BookMarkTypeFolder = 2,         //文件夹
    //主要用来区分浏览器首页标签+浏览器书签的导入导出
    BookMarkTypeFocus_Homepage = 100,
};

typedef NS_ENUM(NSInteger,PreferenceItemType) {
    PreferenceItemTypeAddBookMark = 1,
    PreferenceItemTypeBookMark,          //书签
    PreferenceItemTypeHistory,           //历史记录
    PreferenceItemTypePrivacy,           //无痕模式
    PreferenceItemTypeJS ,               //JS模块
    PreferenceItemTypeUA ,               //UA
    PreferenceItemTypeNightMode ,        //暗黑模式
    PreferenceItemTypeSetting ,          //设置
    PreferenceItemTypeNoImage ,          //无图模式
    PreferenceItemTypeFindInPage ,       //页面内搜索
    PreferenceItemTypeImageMode ,        //看图模式
    PreferenceItemTypeTranslate ,        //翻译
    PreferenceItemTypeScreenshot ,       //截图
    PreferenceItemTypeRefresh ,          //刷新
    PreferenceItemTypeAddWebToHomePannel,      //添加到首页
    PreferenceItemTypeFullScreen,        //全屏模式
    PreferenceItemTypeFeedback,          //意见反馈
    PreferenceItemTypeTagit,             //标记模式
    PreferenceItemTypeShare,             //分享
    PreferenceItemTypeFont,              //字体大小
    PreferenceItemTypeToolBox,           //工具箱
    PreferenceItemTypeWebViewBlacklist,  //网页黑名单
    PreferenceItemTypeAutoPage,          //智能拼页
    PreferenceItemTypeDownload,          //下载
};

typedef NS_ENUM(NSInteger,MessageItemStatus) {
    MessageItemStatusDefault = 0,  //初始化,不显示
    MessageItemStatusVideo ,   //视频模式
    MessageItemStatusReader ,   //阅读模式
};

typedef NS_ENUM(NSInteger,PlayModelItemStatus) {
    PlayModelItemStatusFile = 0,  //文件url
    PlayModelItemStatusPlaylistDetector ,    //PlaylistDetector检测出来的链接
    PlayModelItemStatusContextMenuDetector , //ContextMenuHelper检测出来的链接
    PlayModelItemStatusBlobDetector , //BlobHelper检测出来的链接
};

typedef NS_ENUM(NSInteger,UserScriptCommand) {
    UserScriptCommand_getData = 0,
//    UserScriptCommand_listValues = 1, //这种情况不存在了
    UserScriptCommand_getValue = 2,
    UserScriptCommand_setValue = 3,
    UserScriptCommand_deleteValue = 4,
//    UserScriptCommand_addValueChangeListener = 5, //已废弃
//    UserScriptCommand_removeValueChangeListener = 6,
    UserScriptCommand_xmlhttpRequest = 7,
    UserScriptCommand_getResource = 8,
//    UserScriptCommand_getRequired = 9,//这种情况不存在了
    UserScriptCommand_setClipboard = 11,
    UserScriptCommand_openInTab = 12,
    UserScriptCommand_registerMenuCommand = 13,
    UserScriptCommand_unregisterMenuCommand = 14,
//    UserScriptCommand_isMatched = 15,
    UserScriptCommand_installScript = 20,
    UserScriptCommand_installViaScript = 21,
//    UserScriptCommand_saveTab = 22,
//    UserScriptCommand_getTab = 23,
//    UserScriptCommand_Reload = 24, //东方永页机有问题
    UserScriptCommand_listCookie = 25, //列举所有cookie
    UserScriptCommand_setCookie = 26, //添加cookie
    UserScriptCommand_deleteCookie = 27, //删除cookie
    UserScriptCommand_setFullscreen = 28,    // 设置全屏状态
    UserScriptCommand_getFullscreenStatus = 29,    // 获取全屏状态
};

typedef NS_ENUM(NSInteger,CustomTagType) {
    CustomTagTypeAdd = 0,           //添加
    CustomTagTypeBookMark = 1,  //书签
    CustomTagTypeFile = 2,   //文件
    CustomTagTypeWeb = 3 ,   //网页
    CustomTagTypeGuideline = 4,  //用户指南
    CustomTagTypeMetaso = 5 ,   //秘塔AI
};

typedef NS_ENUM(NSInteger,UserAgentType) {
    UserAgentTypePhone = 0, //移动端
    UserAgentTypePC = 1, //PC端
    UserAgentTypeCustom = 2, //自定义
};

//默认useragent的值不能写死，根据ppOrder来从代码中读取真正的值
typedef NS_ENUM(NSInteger,UserAgentDefaultType) {
    UserAgentDefaultTypeiPhone = 0, //iPhone
    UserAgentDefaultTypeiPad = 1, //iPad
    UserAgentDefaultTypeAndriod = 2, //安卓
    UserAgentDefaultTypeSafari = 3, //safari
    UserAgentDefaultTypeEdge = 4,
    UserAgentDefaultTypeChrome = 5,
    UserAgentDefaultTypeFirefox = 6,
};

typedef enum SettingType {
    SettingTypeVIP,         //vip
    SettingTypeGeneralSetting, //通用设置
    SettingTypeHomeSetting, //主页设置
    SettingTypeWebSetting,  //网页设置
    SettingTypeSearchSetting, //搜索设置
    SettingTypeRate ,       //评价应用
    SettingTypeFeedback ,   //意见反馈
    SettingTypeShare ,      //分享给朋友
    SettingTypeAboutUs ,    //关于我们
    SettingTypeProtocol ,   //隐私协议
    SettingTypeGuideline, //用户指南
    SettingTypeTagit,       //标记模式
    SettingTypeDataClear,   //数据清理
    SettingTypeAdBlock,    //广告过滤
    SettingTypeAlertJump,    //屏蔽APP跳转
    SettingTypeAutoPage,    //自动翻页
    SettingTypeiCloud,     //iCloud同步
    SettingTypeRecommend,  //Addons/轻阅
    SettingTypeTranslate, //翻译
    SettingTypeTabSetting, //标签页设置
    SettingTypeToolbarConfig, //工具栏设置
    SettingTypePhotoBrowser, //看图模式
    
    SettingTypeExport, //一键导出
} SettingType;

typedef enum SearchEngineType {
    SearchEngineTypeBaidu,
    SearchEngineTypeGoogle,
    SearchEngineTypeTouTiao,
    SearchEngineTypeBing,
    SearchEngineType360,
    SearchEngineTypeSougou,
    SearchEngineTypeDuckduckgo,
    SearchEngineTypeYahoo,
    SearchEngineTypeAol,
    SearchEngineTypeEcosia,
    //APP内搜索
    SearchEngineTypeYoutube,
    SearchEngineTypeReddit,
    SearchEngineTypeGreasyfork,
    SearchEngineTypeGoogleTranslate,
    SearchEngineTypeGithub,
    SearchEngineTypeTwitter,
    
    SearchEngineTypeYandex,
    SearchEngineTypeMetaso, //秘塔AI
} SearchEngineType;

typedef enum ToolbarOption {
    ToolbarOptionDefault,       //默认样式, < > + 口 三，后退/前进/新增/标签页/功能页
    ToolbarOption1   = 1,       //样式1, < > 三 口 主，后退/前进/功能页/标签页/首页
    ToolbarOption2   = 2,       //样式2, < > 主 口 三，后退/前进/首页/标签页/功能页
    ToolbarOption3   = 3,       //样式3, < > 口 三 主，后退/前进/标签页/功能页/首页
    ToolbarOption4   = 4,       //样式4, < > + 三 口，后退/前进/新增/功能页/标签页
} ToolbarOption;

typedef enum LocalizableOption {
    LocalizableOptionZh_Hans = 0,       //简体
    LocalizableOptionZh_Hant = 1,       //繁体
    LocalizableOption_en   = 2,       //英语
} LocalizableOption;

//弹出式窗口选项, 1每次都询问,2总是当前窗口打开,3总是新窗口打开,4总是后台窗口打开,5默认行为
typedef NS_ENUM(NSInteger,PopupWindowOption) {
    PopupWindowOptionAlwaysAsk = 1,
    PopupWindowOptionOpenInCurrent = 2,
    PopupWindowOptionOpenInNewWindow = 3,
    PopupWindowOptionOpenInBackWindow = 4,
    PopupWindowOptionDefault = 5,
};

typedef NS_ENUM(NSInteger, SnifferPolicy) {
    SnifferPolicy0 = 0,     /// referer: originUrl的domain
    SnifferPolicy1,         /// referer不传
    SnifferPolicy2,         /// referer: originUrl的全部
    SnifferPolicy3,         /// referer: http(s)://domain, 即origin
    SnifferPolicy4,         /// 什么都不传
    SnifferPolicy5,         /// 使用了透传，decidePolicyForNavigationAction
};

typedef NS_ENUM(NSInteger, DataClearType) {
    DataClearTypeTmp = 0,
    DataClearTypeInbox = 1,
    DataClearTypeWebView = 2,
    DataClearTypeImages = 4, //sdwebimage
};

typedef NS_ENUM(NSInteger, FaceIDStatus) {
    FaceIDStatusDefault = 0,  //默认
    FaceIDStatusHandling = 1,  //验证中
    FaceIDStatusFinish = 2,   //验证成功
};

typedef NS_ENUM(NSInteger, FontSize) {
    FontSize50,    //50%
    FontSize75,    //75%
    FontSize85,    //85%
    FontSize100,    //100%
    FontSize115,    //115%
    FontSize125,    //125%
    FontSize150,    //150%
    FontSize175,    //175%
    FontSize200,    //200%
};

typedef NS_ENUM(NSInteger,CustomTitleViewStatus) {
    CustomTitleViewStatusDefault = 0, //默认不显示状态,隐藏标签
    CustomTitleViewStatusWorking, //正常运行
};

//导航栏样式，2种
//1、默认，右上角关闭按钮
//2、UA，左上角返回，右上角添加按钮
typedef NS_ENUM(NSInteger,SheetModalType) {
    SheetModalTypeDefault = 0,
    SheetModalTypeUA = 1,
};

//长按排序的类型
typedef NS_ENUM(NSInteger,LongPressElementType) {
    LongPressElementTypeDefault = 0, //普通点击
    LongPressElementTypeLink = 1,    //网页链接
    LongPressElementTypeVideo = 2,   //视频
    LongPressElementTypeAudio = 3,   //音频
    LongPressElementTypeImage = 4,   //图片
};

typedef enum DarkModeStatus {
    DarkModeStatusAuto = 0,   //自动
    DarkModeStatusLight = 1,  //浅色
    DarkModeStatusDark = 2,   //深色
} DarkModeStatus;

typedef enum UserScriptUpdateStatus {
    UserScriptUpdateStatusDefault = 0,   //默认值
    UserScriptUpdateStatusLoading,       //更新中
    UserScriptUpdateStatusFinishRequest, //已更新,更新请求完毕
    UserScriptUpdateStatusFailed,        //更新失败
} UserScriptUpdateStatus;

typedef enum ContextMenuType {
    ContextMenuTypeTitle = 0,   //标题
    ContextMenuTypeItem = 1,  //选项
} ContextMenuType;

typedef enum PlayModeStatus {
    PlayModeStatusDefautl = 0,      //默认(只播放当前视频)
    PlayModeStatusRepeatList = 1,   //列表循环
    PlayModeStatusRepeatOne = 2,    //单曲循环
} PlayModeStatus;

//Task相关枚举

typedef NS_ENUM(NSInteger,TaskFileType) {
    TaskFileTypeUnknown = 0,
    TaskFileTypeFolder = 1,         //文件夹
    TaskFileTypeFile   = 2,         //文件
};

typedef enum CloudModelType {
    CloudModelTypeDefault = 0,   //默认
    CloudModelTypeUserScript = 1,  //脚本
    CloudModelTypeBookMark = 2, //书签
    CloudModelTypeTagit = 3, //标记模式
    CloudModelTypeCustomTag = 4, //首页标签
    CloudModelTypeWebBlacklist = 5, //网页黑名单
} CloudModelType;

//翻译的目标语言
typedef NS_ENUM(NSInteger, Language) {
    LanguageSimplifiedChinese = 0,
    LanguageTraditionalChinese,
    LanguageJapanese,
    LanguageKorean,
    LanguageEnglish,
    LanguageThai,
    LanguageVietnamese,
    LanguageArabic,
    LanguageBulgarian,
    LanguageCatalan,
    LanguageCroatian,
    LanguageCzech,
    LanguageDanish,
    LanguageDutch,
    LanguageFinnish,
    LanguageFrench,
    LanguageGerman,
    LanguageGreek,
    LanguageHindi,
    LanguageHungarian,
    LanguageIndonesian,
    LanguageItalian,
    LanguageMalay,
    LanguageMaltese,
    LanguageNorwegian,
    LanguagePolish,
    LanguagePortuguese,
    LanguageRomanian,
    LanguageRussian,
    LanguageSlovak,
    LanguageSlovenian,
    LanguageSpanish,
    LanguageSwedish,
    LanguageTamil,
    LanguageTelugu,
    LanguageTurkish,
    LanguageUkrainian,
};

//翻译引擎
typedef NS_ENUM(NSInteger, TranslateEngine) {
    TranslateEngineBing = 0, //默认,bing
    TranslateEngineGoogle = 1, //谷歌
};

//当前翻译状态
typedef NS_ENUM(NSInteger, TranslateStatus) {
    TranslateStatusDefault = 0, //"翻译"
    TranslateStatusLoading, //翻译中
    TranslateStatusRestore, //"显示原文"
};

typedef NS_ENUM(NSInteger, GuideLineType) {
    GuideLineTypeVideo = 0, //视频模式
    GuideLineTypeScript, //油猴脚本
    GuideLineTypeFullscreen, //全屏模式
    GuideLineTypeTranslate, //翻译模式
    GuideLineTypeCustomUrl, //自定义主页
    GuideLineTypeBookmark, //书签导入导出
    GuideLineTypeTagit, //标记模式
    GuideLineTypeAutoPage, //自动翻页
};

//油猴脚本执行范围
typedef NS_ENUM(NSInteger, UserScriptFrameOption) {
    UserScriptFrameOptionDefault = 0, //defautl，取决于selectFrame
    UserScriptFrameOptionMain = 1, //mainframe
    UserScriptFrameOptionAll = 2, //allframe
};

//关闭网页标签方式，扇形滑动，水平滑动，无效果
typedef NS_ENUM(NSInteger, SwipeCloseTabTrayType) {
    SwipeCloseTabTrayTypeFan = 0,
    SwipeCloseTabTrayTypeHorizontal = 1,
    SwipeCloseTabTrayTypeNoEffect = 2,
};

//2.6.3智能拼页三种模式
typedef NS_ENUM(NSInteger, AutoPageMarkMode) {
    AutoPageMarkModeAppend = 0, //append模式
    AutoPageMarkModeAjax = 1, //ajax模式
    AutoPageMarkModeClick = 2, //click模式
};

//2.6.3标签栏排列方式
typedef NS_ENUM(NSInteger, TabTrayArrangeType) {
    TabTrayArrangeTypeTopToBottom = 0, // 从上到下
    TabTrayArrangeTypeBottomToTop = 1,  // 从下到上
};

//首页批量操作
typedef NS_ENUM(NSInteger, BatchType) {
    BatchTypeDefault = 0, //默认
    BatchTypeMove, //批量移动
    BatchTypeDelete ,  //批量删除
    BatchTypeShare,    //批量分享
};

//v2.6.8,书签排列方法
typedef NS_ENUM(NSInteger, FileSortType) {
    FileSortTypeTimeDescend = 0, //时间降序
    FileSortTypeTimeAscend = 1, //时间升序
    FileSortTypeNameAscend = 2, //文件名升序
    FileSortTypeNameDescend = 3, //文件名降序
};

//2.6.8,地址栏显示内容
typedef NS_ENUM(NSInteger, AddressBarDisplayMode) {
    AddressBarDisplayModeURL = 0,      // 显示网址
    AddressBarDisplayModeTitle = 1,    // 显示标题
    AddressBarDisplayModeDomain = 2,    // 显示域名
};

//2.6.8,工具栏枚举类型
typedef NS_ENUM(NSInteger, ToolbarGroup) {
    ToolbarGroupBack,       // 后退
    ToolbarGroupForward,    // 前进
    ToolbarGroupNew,        // 新建
    ToolbarGroupTabs,       // 标签页
    ToolbarGroupFeatures,   // 功能页
    ToolbarGroupHome        // 首页
};

//2.6.8,工具栏长按事件类型
typedef NS_ENUM(NSInteger, ToolbarLongPressAction) {
    ToolbarLongPressActionGoHome = 1,          // 回到首页
    ToolbarLongPressActionNewTab = 2,          // 新建标签
    ToolbarLongPressActionShare = 3,           // 分享页面
    ToolbarLongPressActionAddBookmark = 4,     // 添加书签
    ToolbarLongPressActionAddHome = 5,         // 添加首页
    ToolbarLongPressActionOpenSettings = 6,    // 打开设置
    ToolbarLongPressActionFindInPage = 7,      // 页内查找
    ToolbarLongPressActionImageMode = 8,       // 看图模式
    ToolbarLongPressActionRefresh = 9,         // 刷新
    ToolbarLongPressActionScrollToTop = 10,     // 返回顶部
    ToolbarLongPressActionScrollToBottom = 11,  // 返回底部
    ToolbarLongPressActionOpenHistory = 12,     // 打开历史
    ToolbarLongPressActionOpenBookmarks = 13,   // 打开书签
    ToolbarLongPressActionCloseCurrentTab = 14, // 关闭当前标签
    ToolbarLongPressActionCloseOtherTabs = 15,  // 关闭其他标签
    ToolbarLongPressActionCloseAllTabs = 16,    // 关闭全部标签
    ToolbarLongPressActionOpenTranslation = 17, // 打开翻译
    ToolbarLongPressActionLockFullscreen = 18,   // 锁定全屏
    ToolbarLongPressActionOpenUserScript = 19,   // 打开脚本
    ToolbarLongPressActionOpenTagit = 20,   // 打开标记模式
    ToolbarLongPressActionOpenUseragent = 21,   // 打开浏览器标识
    
};

//v2.7.1, 搜索联想页的滑动列表类型
typedef NS_ENUM(NSInteger, SearchSuggestListType) {
    SearchSuggestListTypeCommonInputWords,       // 常用输入词
    SearchSuggestListTypeSearchEngine,           // 搜索引擎（百度、秘塔AI、必应等）,SearchEngineType
    SearchSuggestListTypeSearchSlider,           // 搜索滑动条
    SearchSuggestListTypeSearchSettings          // 搜索设置
};

//v2.7.8, 阅读模式背景主题类型枚举
typedef NS_ENUM(NSInteger, ReadingModeThemeType) {
    ReadingModeThemeTypeWhite = 0,      // 白色主题
    ReadingModeThemeTypeBeige = 1,          // 米色主题
    ReadingModeThemeTypeDark = 2,           // 深色主题
    ReadingModeThemeTypeGreen = 3,          // 护眼绿主题
    ReadingModeThemeTypeBlue = 4,           // 护眼蓝主题
    ReadingModeThemeTypePink = 5,           // 粉色主题
    ReadingModeThemeTypeYellow = 6,         // 暖黄主题
    ReadingModeThemeTypeGray = 7,           // 灰色主题
    ReadingModeThemeTypePurple = 8,         // 紫色主题
    ReadingModeThemeTypeCount           // 主题总数（用于遍历）
};

#endif /* PPEnums_h */
