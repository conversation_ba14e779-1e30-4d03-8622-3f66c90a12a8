//
//  TabModel.h
//  PandaBrowser
//
//  Created by q<PERSON><PERSON> on 2022/3/4.
//

#import "BaseModel.h"
@class Tab;
@class BrowserViewController;

@interface TabModel : BaseModel

@property(nonatomic,strong) NSString *title;

@property(nonatomic,strong) NSString *url;

@property(nonatomic,strong) NSString *tabId;

@property(nonatomic,assign) int ppOrder;

@property(nonatomic,strong) NSString* urlHistorySnapshot;

@property(nonatomic,assign) int urlHistoryCurrentIndex;

@property(nonatomic,assign) BOOL isSelected;

@property(nonatomic,strong) NSString* mimeType;

@property(nonatomic,strong) NSString *ctime;

// 根据webView更新model的标题
- (void)updateTitleWithWebViewTitle:(NSString*)webViewTitle
                                url:(NSURL*)url
                             toDisk:(BOOL)toDisk;

//新开一页
+ (instancetype)tabModelForNewHomePanel;

//根据指定url生产tab
+ (instancetype)tabModelWithUrl:(NSString *)url;

//当前index下是不是首页，需要判断历史url
- (BOOL)isCurrentTabAtHome;

@end


@interface TabSessionRestoreModel : BaseModel
@property(nonatomic,strong) NSArray *history;
@property(nonatomic,assign) int currentPage;
@end
