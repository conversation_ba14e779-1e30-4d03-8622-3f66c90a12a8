//
//  PPNotifications.h
//  PPBrowser
//
//  Created by q<PERSON><PERSON> on 2022/4/6.
//  Copyright © 2022 qingbin. All rights reserved.
//

#ifndef PPNotifications_h
#define PPNotifications_h

#define kFindInPageHelperNotification @"kFindInPageHelperNotification"

#define kPlaylistDetectorNotification @"kPlaylistDetectorNotification"

#define kGetAllImageHelperNotification @"kGetAllImageHelperNotification"

// 添加一个脚本
#define kAddUserScriptNotification @"kAddUserScriptNotification"

// 编辑了脚本信息,重新加载脚本
#define kReloadUserScriptNotification @"kReloadUserScriptNotification"

// 脚本命令打开新的标签页
#define kOpenInTabByUserScriptNotification @"kOpenInTabByUserScriptNotification"

// 打开书签页
#define kOpenBookMarkNotification @"kOpenBookMarkNotification"

// 排序改变，刷新所有书签
#define kReloadBookMarkNotification @"kReloadBookMarkNotification"

// 打开网页
#define kOpenWebViewNotification @"kOpenWebViewNotification"

// 切换搜索引擎
#define kUpdateSearchEngineNotification @"kUpdateSearchEngineNotification"

// 添加了一个主页标签
#define kAddHomeCustomTagNotification @"kAddHomeCustomTagNotification"

// 刷新当前网页
#define kReloadCurrentWebViewNotification @"kReloadAllWebViewNotification"

// 获取网页的metadata
#define kGetMetadataNotification @"kGetMetadataNotification"
 
// 更新壁纸
#define kUpdateWallpaperNotification @"kUpdateWallpaperNotification"

// 切换夜间模式
#define kDarkThemeDidChangeNotification @"kDarkThemeDidChangeNotification"
 
// 长按事件
#define kContextMenuNotification @"kContextMenuNotification"

// 更换了首页
#define kChangeCustomUrlNotification @"kChangeCustomUrlNotification"

// 底部工具栏切换
#define kToolbarDidChangeNotification @"kToolbarDidChangeNotification"

// iCloud更新
#define kBookMarkDidChangeNotification @"kBookMarkDidChangeNotification"

// 屏幕旋转
#define kOrientationDidChangeNotification @"kOrientationDidChangeNotification"

// 更新了菜单栏
#define kupdateCommandNotification @"kupdateCommandNotification"

// 开启或者关闭下拉刷新
#define kupdatePullToRefreshNotification @"kupdatePullToRefreshNotification"

// 成为会员/恢复会员
#define kReloadVipNotification @"kReloadVipNotification"

// 切换了播放循环方式
#define kChangePlayStatusNotification @"kChangePlayStatusNotification"

//
#define kTaskReloadNotification @"kTaskReloadNotification"

//
#define kTaskFailureNotification @"kTaskFailureNotification"

//
#define kTaskProgressNotification @"kTaskProgressNotification"

//
#define kTaskSuccessNotification @"kTaskSuccessNotification"

// 记录seekTime
#define kUpdateSeekTimeNotification @"kUpdateSeekTimeNotification"

// safearea发生变化
#define kViewSafeAreaInsetsDidChangeNotification @"kViewSafeAreaInsetsDidChangeNotification"

// CloudKit接收到订阅信息
#define kCloudKitDataSubscriptionNotification @"kCloudKitDataSubscriptionNotification"

// CloudKit接收到Record更新的通知
#define kCloudKitDataDidChangeNotification @"kCloudKitDataDidChangeNotification"

// 监听首页标签发生改变
#define kCustomTagDidChangedNotification @"kCustomTagDidChangedNotification"

// 切换了翻译状态(加载中-"显示原文")
#define kTranslateStatusDidChangedNotification @"kTranslateStatusDidChangedNotification"

// 更新了脚本
#define kUpdateUserScriptNotification @"kUpdateUserScriptNotification"

// 更新自动更新的URL
#define kUpdateAutoPageUrlNotification @"kUpdateAutoPageUrlNotification"

// 启用或者禁用滑动手势
#define kUpdateSwipeGesutreNotification @"kUpdateSwipeGesutreNotification"

// v2.7.4，无痕模式状态变更
#define kIncognitoStatusChangedNotification @"kIncognitoStatusChangedNotification"

// v2.7.6，阅读模式
#define kReadabilityMessageHelperNotification @"kReadabilityMessageHelperNotification"

// v2.7.6 进入阅读模式
#define kEnterReaderNotification @"kEnterReaderNotification"

// v2.7.6 退出阅读模式
#define kExitReaderNotification @"kExitReaderNotification"

#endif /* PPNotifications_h */
