// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXAggregateTarget section */
		A7F4DFED147FBAC600F4059A /* Documentation */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = A7F4DFEE147FBAC600F4059A /* Build configuration list for PBXAggregateTarget "Documentation" */;
			buildPhases = (
				A7F4DFF1147FBB0B00F4059A /* ShellScript */,
			);
			dependencies = (
			);
			name = Documentation;
			productName = Documentation;
		};
/* End PBXAggregateTarget section */

/* Begin PBXBuildFile section */
		29DA9D7D1C6DC22800F5F22A /* DTReachability.m in Sources */ = {isa = PBXBuildFile; fileRef = A72121D5173BFF89003C6F0A /* DTReachability.m */; };
		29DA9D7E1C6DC22800F5F22A /* DTReachability.m in Sources */ = {isa = PBXBuildFile; fileRef = A72121D5173BFF89003C6F0A /* DTReachability.m */; };
		29DA9D7F1C6DC22900F5F22A /* DTReachability.m in Sources */ = {isa = PBXBuildFile; fileRef = A72121D5173BFF89003C6F0A /* DTReachability.m */; };
		29DA9D811C6DC25300F5F22A /* SystemConfiguration.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 29DA9D801C6DC25300F5F22A /* SystemConfiguration.framework */; };
		29DA9D821C6DC25A00F5F22A /* SystemConfiguration.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A7D9D6631758CA6300C29B6F /* SystemConfiguration.framework */; };
		29DA9D841C6DC26000F5F22A /* SystemConfiguration.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 29DA9D831C6DC26000F5F22A /* SystemConfiguration.framework */; };
		29DA9D851C6DC27500F5F22A /* DTReachability.h in Headers */ = {isa = PBXBuildFile; fileRef = A72121D4173BFF89003C6F0A /* DTReachability.h */; settings = {ATTRIBUTES = (Public, ); }; };
		29DA9D861C6DC27600F5F22A /* DTReachability.h in Headers */ = {isa = PBXBuildFile; fileRef = A72121D4173BFF89003C6F0A /* DTReachability.h */; settings = {ATTRIBUTES = (Public, ); }; };
		29DA9D871C6DC27600F5F22A /* DTReachability.h in Headers */ = {isa = PBXBuildFile; fileRef = A72121D4173BFF89003C6F0A /* DTReachability.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384470921BA3330F0037C68D /* DTVersion.m in Sources */ = {isa = PBXBuildFile; fileRef = A70B4CC81486621B00873A4A /* DTVersion.m */; };
		384470931BA3330F0037C68D /* DTFolderMonitor.m in Sources */ = {isa = PBXBuildFile; fileRef = A78D661C17AFFCCC0039F5E6 /* DTFolderMonitor.m */; };
		384470941BA3330F0037C68D /* NSString+DTFormatNumbers.m in Sources */ = {isa = PBXBuildFile; fileRef = A70B4CCB1486621B00873A4A /* NSString+DTFormatNumbers.m */; };
		384470951BA3330F0037C68D /* NSString+DTPaths.m in Sources */ = {isa = PBXBuildFile; fileRef = A7D8628014EBF65C001436AF /* NSString+DTPaths.m */; };
		384470961BA3330F0037C68D /* NSDictionary+DTError.m in Sources */ = {isa = PBXBuildFile; fileRef = A7D0AA24153C1B160020F18B /* NSDictionary+DTError.m */; };
		384470971BA3330F0037C68D /* NSString+DTURLEncoding.m in Sources */ = {isa = PBXBuildFile; fileRef = A7D0AA2A153C1FE40020F18B /* NSString+DTURLEncoding.m */; };
		384470981BA3330F0037C68D /* NSString+DTUtilities.m in Sources */ = {isa = PBXBuildFile; fileRef = A7D0AA49153C233E0020F18B /* NSString+DTUtilities.m */; };
		384470991BA3330F0037C68D /* DTCoreGraphicsUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = A7D0AA6C153C39920020F18B /* DTCoreGraphicsUtils.m */; };
		3844709A1BA3330F0037C68D /* NSArray+DTError.m in Sources */ = {isa = PBXBuildFile; fileRef = A73D5BAA155271FD0024BDB7 /* NSArray+DTError.m */; };
		3844709B1BA3330F0037C68D /* NSURL+DTUnshorten.m in Sources */ = {isa = PBXBuildFile; fileRef = A79231CD157A0B9400C3ACBB /* NSURL+DTUnshorten.m */; };
		3844709C1BA3330F0037C68D /* NSMutableArray+DTMoving.m in Sources */ = {isa = PBXBuildFile; fileRef = A766135F16143F8A00DF6C2B /* NSMutableArray+DTMoving.m */; };
		3844709D1BA3330F0037C68D /* DTSidePanelControllerSegue.m in Sources */ = {isa = PBXBuildFile; fileRef = C9826822196DAD7800A84677 /* DTSidePanelControllerSegue.m */; };
		3844709E1BA3330F0037C68D /* NSData+DTCrypto.m in Sources */ = {isa = PBXBuildFile; fileRef = A79500F6161D680000358BC3 /* NSData+DTCrypto.m */; };
		3844709F1BA3330F0037C68D /* DTBlockFunctions.m in Sources */ = {isa = PBXBuildFile; fileRef = A768BE3B17FC3C91008834C6 /* DTBlockFunctions.m */; };
		384470A01BA3330F0037C68D /* UIView+DTActionHandlers.m in Sources */ = {isa = PBXBuildFile; fileRef = F88FF6D91920F1AF00120808 /* UIView+DTActionHandlers.m */; };
		384470A11BA3330F0037C68D /* DTAsyncFileDeleter.m in Sources */ = {isa = PBXBuildFile; fileRef = A7FA5FAC17B127E0000FC61F /* DTAsyncFileDeleter.m */; };
		384470A21BA3330F0037C68D /* NSFileWrapper+DTCopying.m in Sources */ = {isa = PBXBuildFile; fileRef = A73B6F8A163169BB002CCCA7 /* NSFileWrapper+DTCopying.m */; };
		384470A31BA3330F0037C68D /* NSURL+DTComparing.m in Sources */ = {isa = PBXBuildFile; fileRef = A7FAA3881652291D006ED151 /* NSURL+DTComparing.m */; };
		384470A41BA3330F0037C68D /* DTAlertView.m in Sources */ = {isa = PBXBuildFile; fileRef = F88FF6D71920F1AF00120808 /* DTAlertView.m */; };
		384470A51BA3330F0037C68D /* DTActivityTitleView.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB49216A5E37F0010CD85 /* DTActivityTitleView.m */; };
		384470A61BA3330F0037C68D /* DTLog.m in Sources */ = {isa = PBXBuildFile; fileRef = A7FA5FA317B0F7B4000FC61F /* DTLog.m */; };
		384470A71BA3330F0037C68D /* DTPieProgressIndicator.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB49616A5E37F0010CD85 /* DTPieProgressIndicator.m */; };
		384470A81BA3330F0037C68D /* DTSmartPagingScrollView.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB49816A5E37F0010CD85 /* DTSmartPagingScrollView.m */; };
		384470A91BA3330F0037C68D /* NSURL+DTAppLinks.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB4C116A5E50D0010CD85 /* NSURL+DTAppLinks.m */; };
		384470AA1BA3330F0037C68D /* DTFoundationConstants.m in Sources */ = {isa = PBXBuildFile; fileRef = FAB1726A16301F0D00B44EDC /* DTFoundationConstants.m */; };
		384470AB1BA3330F0037C68D /* UIApplication+DTNetworkActivity.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB4C316A5E50E0010CD85 /* UIApplication+DTNetworkActivity.m */; };
		384470AC1BA3330F0037C68D /* DTActionSheet.m in Sources */ = {isa = PBXBuildFile; fileRef = F88FF6D51920F1AF00120808 /* DTActionSheet.m */; };
		384470AD1BA3330F0037C68D /* UIImage+DTFoundation.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB4C516A5E50E0010CD85 /* UIImage+DTFoundation.m */; };
		384470AE1BA3330F0037C68D /* UIView+DTFoundation.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB4C916A5E50F0010CD85 /* UIView+DTFoundation.m */; };
		384470B01BA3330F0037C68D /* DTSidePanelPanGestureRecognizer.m in Sources */ = {isa = PBXBuildFile; fileRef = 355B210F4174F2B22B0CD6A0 /* DTSidePanelPanGestureRecognizer.m */; };
		384470B11BA3330F0037C68D /* DTCustomColoredAccessory.m in Sources */ = {isa = PBXBuildFile; fileRef = A7E88ED116BC0278008CBA9C /* DTCustomColoredAccessory.m */; };
		384470B21BA3330F0037C68D /* DTTiledLayerWithoutFade.m in Sources */ = {isa = PBXBuildFile; fileRef = A730BCCD16D2892E003B849F /* DTTiledLayerWithoutFade.m */; };
		384470B31BA3330F0037C68D /* UIViewController+DTSidePanelController.m in Sources */ = {isa = PBXBuildFile; fileRef = A7C92B68174F9AEC0019D70A /* UIViewController+DTSidePanelController.m */; };
		384470B41BA3330F0037C68D /* UIColor+DTDebug.m in Sources */ = {isa = PBXBuildFile; fileRef = A78EDD2B17A65D4B00480205 /* UIColor+DTDebug.m */; };
		384470B51BA3330F0037C68D /* DTBase64Coding.m in Sources */ = {isa = PBXBuildFile; fileRef = A77D5BF916E4961A00A45C28 /* DTBase64Coding.m */; };
		384470B61BA3330F0037C68D /* UIView+DTDebug.m in Sources */ = {isa = PBXBuildFile; fileRef = A78EDD2D17A65D4B00480205 /* UIView+DTDebug.m */; };
		384470B71BA3330F0037C68D /* DTScriptExpression.m in Sources */ = {isa = PBXBuildFile; fileRef = A78EDD1817A65AD700480205 /* DTScriptExpression.m */; };
		384470B81BA3330F0037C68D /* DTScriptVariable.m in Sources */ = {isa = PBXBuildFile; fileRef = A78EDD1A17A65AD700480205 /* DTScriptVariable.m */; };
		384470B91BA3330F0037C68D /* DTExtendedFileAttributes.m in Sources */ = {isa = PBXBuildFile; fileRef = A7D6F2E415063448001CACDD /* DTExtendedFileAttributes.m */; };
		384470BA1BA3330F0037C68D /* NSScanner+DTScripting.m in Sources */ = {isa = PBXBuildFile; fileRef = A78EDD1C17A65AD800480205 /* NSScanner+DTScripting.m */; };
		384470BB1BA3330F0037C68D /* DTObjectBlockExecutor.m in Sources */ = {isa = PBXBuildFile; fileRef = A78EDD2617A65B8600480205 /* DTObjectBlockExecutor.m */; };
		384470BC1BA3330F0037C68D /* NSObject+DTRuntime.m in Sources */ = {isa = PBXBuildFile; fileRef = A78EDD2817A65B8600480205 /* NSObject+DTRuntime.m */; };
		384470BD1BA3330F0037C68D /* DTSidePanelController.m in Sources */ = {isa = PBXBuildFile; fileRef = A74663B31743D43600D4D7D5 /* DTSidePanelController.m */; };
		384470C11BA3330F0037C68D /* NSString+DTPaths.h in Headers */ = {isa = PBXBuildFile; fileRef = A7D8627F14EBF65C001436AF /* NSString+DTPaths.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384470C21BA3330F0037C68D /* DTCompatibility.h in Headers */ = {isa = PBXBuildFile; fileRef = A74549681B6A3F26004B0CA7 /* DTCompatibility.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384470C31BA3330F0037C68D /* DTBase64Coding.h in Headers */ = {isa = PBXBuildFile; fileRef = A77D5BF816E4961A00A45C28 /* DTBase64Coding.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384470C41BA3330F0037C68D /* NSMutableArray+DTMoving.h in Headers */ = {isa = PBXBuildFile; fileRef = A766135E16143F8A00DF6C2B /* NSMutableArray+DTMoving.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384470C51BA3330F0037C68D /* NSArray+DTError.h in Headers */ = {isa = PBXBuildFile; fileRef = A73D5BA9155271FD0024BDB7 /* NSArray+DTError.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384470C61BA3330F0037C68D /* DTFoundation.h in Headers */ = {isa = PBXBuildFile; fileRef = A70B4CC51486621B00873A4A /* DTFoundation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384470C71BA3330F0037C68D /* DTAnimatedGIF.h in Headers */ = {isa = PBXBuildFile; fileRef = A7AC21A7196409560009E1B9 /* DTAnimatedGIF.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384470C81BA3330F0037C68D /* crypt.h in Headers */ = {isa = PBXBuildFile; fileRef = A77DD40D14E825FC00F34B03 /* crypt.h */; };
		384470C91BA3330F0037C68D /* ioapi.h in Headers */ = {isa = PBXBuildFile; fileRef = A77DD40F14E825FC00F34B03 /* ioapi.h */; };
		384470CA1BA3330F0037C68D /* mztools.h in Headers */ = {isa = PBXBuildFile; fileRef = A77DD41114E825FC00F34B03 /* mztools.h */; };
		384470CB1BA3330F0037C68D /* unzip.h in Headers */ = {isa = PBXBuildFile; fileRef = A77DD41314E825FC00F34B03 /* unzip.h */; };
		384470CC1BA3330F0037C68D /* zip.h in Headers */ = {isa = PBXBuildFile; fileRef = A77DD41514E825FC00F34B03 /* zip.h */; };
		384470CD1BA3330F0037C68D /* DTActivityTitleView.h in Headers */ = {isa = PBXBuildFile; fileRef = A76DB49116A5E37F0010CD85 /* DTActivityTitleView.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384470CE1BA3330F0037C68D /* DTPieProgressIndicator.h in Headers */ = {isa = PBXBuildFile; fileRef = A76DB49516A5E37F0010CD85 /* DTPieProgressIndicator.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384470CF1BA3330F0037C68D /* NSURL+DTComparing.h in Headers */ = {isa = PBXBuildFile; fileRef = A7FAA3871652291D006ED151 /* NSURL+DTComparing.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384470D01BA3330F0037C68D /* DTSmartPagingScrollView.h in Headers */ = {isa = PBXBuildFile; fileRef = A76DB49716A5E37F0010CD85 /* DTSmartPagingScrollView.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384470D11BA3330F0037C68D /* NSURL+DTAppLinks.h in Headers */ = {isa = PBXBuildFile; fileRef = A76DB4C016A5E50D0010CD85 /* NSURL+DTAppLinks.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384470D21BA3330F0037C68D /* NSDictionary+DTError.h in Headers */ = {isa = PBXBuildFile; fileRef = A7D0AA23153C1B160020F18B /* NSDictionary+DTError.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384470D31BA3330F0037C68D /* DTProgressHUD.h in Headers */ = {isa = PBXBuildFile; fileRef = F88FF6831920A9DF00120808 /* DTProgressHUD.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384470D41BA3330F0037C68D /* NSString+DTFormatNumbers.h in Headers */ = {isa = PBXBuildFile; fileRef = A70B4CCA1486621B00873A4A /* NSString+DTFormatNumbers.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384470D51BA3330F0037C68D /* NSData+DTCrypto.h in Headers */ = {isa = PBXBuildFile; fileRef = A79500F5161D680000358BC3 /* NSData+DTCrypto.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384470D61BA3330F0037C68D /* UIApplication+DTNetworkActivity.h in Headers */ = {isa = PBXBuildFile; fileRef = A76DB4C216A5E50D0010CD85 /* UIApplication+DTNetworkActivity.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384470D71BA3330F0037C68D /* DTBlockFunctions.h in Headers */ = {isa = PBXBuildFile; fileRef = A768BE3A17FC3C91008834C6 /* DTBlockFunctions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384470D81BA3330F0037C68D /* UIImage+DTFoundation.h in Headers */ = {isa = PBXBuildFile; fileRef = A76DB4C416A5E50E0010CD85 /* UIImage+DTFoundation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384470D91BA3330F0037C68D /* UIView+DTFoundation.h in Headers */ = {isa = PBXBuildFile; fileRef = A76DB4C816A5E50F0010CD85 /* UIView+DTFoundation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384470DB1BA3330F0037C68D /* NSString+DTURLEncoding.h in Headers */ = {isa = PBXBuildFile; fileRef = A7D0AA29153C1FE40020F18B /* NSString+DTURLEncoding.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384470DC1BA3330F0037C68D /* DTCustomColoredAccessory.h in Headers */ = {isa = PBXBuildFile; fileRef = A7E88ED016BC0278008CBA9C /* DTCustomColoredAccessory.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384470DD1BA3330F0037C68D /* UIViewController+DTSidePanelController.h in Headers */ = {isa = PBXBuildFile; fileRef = A7C92B67174F9AEC0019D70A /* UIViewController+DTSidePanelController.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384470DE1BA3330F0037C68D /* DTSidePanelController.h in Headers */ = {isa = PBXBuildFile; fileRef = A74663B21743D43600D4D7D5 /* DTSidePanelController.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384470DF1BA3330F0037C68D /* NSString+DTUtilities.h in Headers */ = {isa = PBXBuildFile; fileRef = A7D0AA48153C233E0020F18B /* NSString+DTUtilities.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384470E01BA3330F0037C68D /* DTHTMLParser.h in Headers */ = {isa = PBXBuildFile; fileRef = A76DB48916A5E1D20010CD85 /* DTHTMLParser.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384470E11BA3330F0037C68D /* DTCoreGraphicsUtils.h in Headers */ = {isa = PBXBuildFile; fileRef = A7D0AA6B153C39920020F18B /* DTCoreGraphicsUtils.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384470E21BA3330F0037C68D /* DTWeakSupport.h in Headers */ = {isa = PBXBuildFile; fileRef = A7FB1218175C9D5000D4B7F0 /* DTWeakSupport.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384470E31BA3330F0037C68D /* DTTiledLayerWithoutFade.h in Headers */ = {isa = PBXBuildFile; fileRef = A730BCCC16D2892E003B849F /* DTTiledLayerWithoutFade.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384470E41BA3330F0037C68D /* NSURL+DTUnshorten.h in Headers */ = {isa = PBXBuildFile; fileRef = A79231CC157A0B9400C3ACBB /* NSURL+DTUnshorten.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384470E51BA3330F0037C68D /* DTLog.h in Headers */ = {isa = PBXBuildFile; fileRef = A7FA5FA217B0F7B4000FC61F /* DTLog.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384470E61BA3330F0037C68D /* NSFileWrapper+DTCopying.h in Headers */ = {isa = PBXBuildFile; fileRef = A73B6F89163169BB002CCCA7 /* NSFileWrapper+DTCopying.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384470EE1BA33ED80037C68D /* DTActivityTitleView.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB49216A5E37F0010CD85 /* DTActivityTitleView.m */; };
		384470EF1BA33ED80037C68D /* DTHTMLParser.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB48A16A5E1D20010CD85 /* DTHTMLParser.m */; };
		384470F01BA33ED80037C68D /* NSURL+DTComparing.m in Sources */ = {isa = PBXBuildFile; fileRef = A7FAA3881652291D006ED151 /* NSURL+DTComparing.m */; };
		384470F11BA33ED80037C68D /* NSFileWrapper+DTCopying.m in Sources */ = {isa = PBXBuildFile; fileRef = A73B6F8A163169BB002CCCA7 /* NSFileWrapper+DTCopying.m */; };
		384470F21BA33ED80037C68D /* NSArray+DTError.m in Sources */ = {isa = PBXBuildFile; fileRef = A73D5BAA155271FD0024BDB7 /* NSArray+DTError.m */; };
		384470F31BA33ED80037C68D /* DTFoundationConstants.m in Sources */ = {isa = PBXBuildFile; fileRef = FAB1726A16301F0D00B44EDC /* DTFoundationConstants.m */; };
		384470F41BA33ED80037C68D /* NSURL+DTUnshorten.m in Sources */ = {isa = PBXBuildFile; fileRef = A79231CD157A0B9400C3ACBB /* NSURL+DTUnshorten.m */; };
		384470F51BA33ED80037C68D /* DTCustomColoredAccessory.m in Sources */ = {isa = PBXBuildFile; fileRef = A7E88ED116BC0278008CBA9C /* DTCustomColoredAccessory.m */; };
		384470F61BA33ED80037C68D /* DTTiledLayerWithoutFade.m in Sources */ = {isa = PBXBuildFile; fileRef = A730BCCD16D2892E003B849F /* DTTiledLayerWithoutFade.m */; };
		384470F71BA33ED80037C68D /* NSData+DTCrypto.m in Sources */ = {isa = PBXBuildFile; fileRef = A79500F6161D680000358BC3 /* NSData+DTCrypto.m */; };
		384470F81BA33ED80037C68D /* DTAlertView.m in Sources */ = {isa = PBXBuildFile; fileRef = F88FF6D71920F1AF00120808 /* DTAlertView.m */; };
		384470F91BA33ED80037C68D /* UIView+DTActionHandlers.m in Sources */ = {isa = PBXBuildFile; fileRef = F88FF6D91920F1AF00120808 /* UIView+DTActionHandlers.m */; };
		384470FA1BA33ED80037C68D /* DTSidePanelController.m in Sources */ = {isa = PBXBuildFile; fileRef = A74663B31743D43600D4D7D5 /* DTSidePanelController.m */; };
		384470FB1BA33ED80037C68D /* DTAnimatedGIF.m in Sources */ = {isa = PBXBuildFile; fileRef = A7AC21A8196409560009E1B9 /* DTAnimatedGIF.m */; };
		384470FC1BA33ED80037C68D /* NSString+DTFormatNumbers.m in Sources */ = {isa = PBXBuildFile; fileRef = A70B4CCB1486621B00873A4A /* NSString+DTFormatNumbers.m */; };
		384470FD1BA33ED80037C68D /* UIView+DTFoundation.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB4C916A5E50F0010CD85 /* UIView+DTFoundation.m */; };
		384470FE1BA33ED80037C68D /* DTBase64Coding.m in Sources */ = {isa = PBXBuildFile; fileRef = A77D5BF916E4961A00A45C28 /* DTBase64Coding.m */; };
		384470FF1BA33ED80037C68D /* DTSmartPagingScrollView.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB49816A5E37F0010CD85 /* DTSmartPagingScrollView.m */; };
		384471001BA33ED80037C68D /* NSString+DTUtilities.m in Sources */ = {isa = PBXBuildFile; fileRef = A7D0AA49153C233E0020F18B /* NSString+DTUtilities.m */; };
		384471011BA33ED80037C68D /* DTSidePanelPanGestureRecognizer.m in Sources */ = {isa = PBXBuildFile; fileRef = 355B210F4174F2B22B0CD6A0 /* DTSidePanelPanGestureRecognizer.m */; };
		384471021BA33ED80037C68D /* DTAsyncFileDeleter.m in Sources */ = {isa = PBXBuildFile; fileRef = A7FA5FAC17B127E0000FC61F /* DTAsyncFileDeleter.m */; };
		384471031BA33ED80037C68D /* UIApplication+DTNetworkActivity.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB4C316A5E50E0010CD85 /* UIApplication+DTNetworkActivity.m */; };
		384471041BA33ED80037C68D /* DTVersion.m in Sources */ = {isa = PBXBuildFile; fileRef = A70B4CC81486621B00873A4A /* DTVersion.m */; };
		384471061BA33ED80037C68D /* DTLog.m in Sources */ = {isa = PBXBuildFile; fileRef = A7FA5FA317B0F7B4000FC61F /* DTLog.m */; };
		384471071BA33ED80037C68D /* NSString+DTURLEncoding.m in Sources */ = {isa = PBXBuildFile; fileRef = A7D0AA2A153C1FE40020F18B /* NSString+DTURLEncoding.m */; };
		384471081BA33ED80037C68D /* UIViewController+DTSidePanelController.m in Sources */ = {isa = PBXBuildFile; fileRef = A7C92B68174F9AEC0019D70A /* UIViewController+DTSidePanelController.m */; };
		384471091BA33ED80037C68D /* DTPieProgressIndicator.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB49616A5E37F0010CD85 /* DTPieProgressIndicator.m */; };
		3844710A1BA33ED80037C68D /* NSDictionary+DTError.m in Sources */ = {isa = PBXBuildFile; fileRef = A7D0AA24153C1B160020F18B /* NSDictionary+DTError.m */; };
		3844710B1BA33ED80037C68D /* NSMutableArray+DTMoving.m in Sources */ = {isa = PBXBuildFile; fileRef = A766135F16143F8A00DF6C2B /* NSMutableArray+DTMoving.m */; };
		3844710C1BA33ED80037C68D /* DTExtendedFileAttributes.m in Sources */ = {isa = PBXBuildFile; fileRef = A7D6F2E415063448001CACDD /* DTExtendedFileAttributes.m */; };
		3844710D1BA33ED80037C68D /* NSObject+DTRuntime.m in Sources */ = {isa = PBXBuildFile; fileRef = A78EDD2817A65B8600480205 /* NSObject+DTRuntime.m */; };
		3844710E1BA33ED80037C68D /* NSURL+DTAppLinks.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB4C116A5E50D0010CD85 /* NSURL+DTAppLinks.m */; };
		3844710F1BA33ED80037C68D /* UIImage+DTFoundation.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB4C516A5E50E0010CD85 /* UIImage+DTFoundation.m */; };
		384471101BA33ED80037C68D /* DTSidePanelControllerSegue.m in Sources */ = {isa = PBXBuildFile; fileRef = C9826822196DAD7800A84677 /* DTSidePanelControllerSegue.m */; };
		384471111BA33ED80037C68D /* DTFolderMonitor.m in Sources */ = {isa = PBXBuildFile; fileRef = A78D661C17AFFCCC0039F5E6 /* DTFolderMonitor.m */; };
		384471121BA33ED80037C68D /* UIView+DTDebug.m in Sources */ = {isa = PBXBuildFile; fileRef = A78EDD2D17A65D4B00480205 /* UIView+DTDebug.m */; };
		384471131BA33ED80037C68D /* DTObjectBlockExecutor.m in Sources */ = {isa = PBXBuildFile; fileRef = A78EDD2617A65B8600480205 /* DTObjectBlockExecutor.m */; };
		384471141BA33ED80037C68D /* DTActionSheet.m in Sources */ = {isa = PBXBuildFile; fileRef = F88FF6D51920F1AF00120808 /* DTActionSheet.m */; };
		384471151BA33ED80037C68D /* DTBlockFunctions.m in Sources */ = {isa = PBXBuildFile; fileRef = A768BE3B17FC3C91008834C6 /* DTBlockFunctions.m */; };
		384471161BA33ED80037C68D /* NSString+DTPaths.m in Sources */ = {isa = PBXBuildFile; fileRef = A7D8628014EBF65C001436AF /* NSString+DTPaths.m */; };
		384471171BA33ED80037C68D /* DTCoreGraphicsUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = A7D0AA6C153C39920020F18B /* DTCoreGraphicsUtils.m */; };
		384471181BA33ED80037C68D /* UIColor+DTDebug.m in Sources */ = {isa = PBXBuildFile; fileRef = A78EDD2B17A65D4B00480205 /* UIColor+DTDebug.m */; };
		3844711A1BA33ED80037C68D /* ImageIO.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A74A7A8D1B617186004163BE /* ImageIO.framework */; };
		3844711B1BA33ED80037C68D /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A74A7A8B1B61716E004163BE /* CoreGraphics.framework */; };
		3844711C1BA33ED80037C68D /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A74A7A891B617158004163BE /* Foundation.framework */; };
		3844711D1BA33ED80037C68D /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A74A7A871B61713A004163BE /* QuartzCore.framework */; };
		3844711E1BA33ED80037C68D /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A74A7A841B61712B004163BE /* UIKit.framework */; };
		3844711F1BA33ED80037C68D /* libxml2.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = A79BA6C61B46A3CA0086C2F6 /* libxml2.dylib */; };
		384471211BA33ED80037C68D /* DTWeakSupport.h in Headers */ = {isa = PBXBuildFile; fileRef = A7FB1218175C9D5000D4B7F0 /* DTWeakSupport.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384471221BA33ED80037C68D /* UIView+DTFoundation.h in Headers */ = {isa = PBXBuildFile; fileRef = A76DB4C816A5E50F0010CD85 /* UIView+DTFoundation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384471231BA33ED80037C68D /* NSArray+DTError.h in Headers */ = {isa = PBXBuildFile; fileRef = A73D5BA9155271FD0024BDB7 /* NSArray+DTError.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384471241BA33ED80037C68D /* NSString+DTURLEncoding.h in Headers */ = {isa = PBXBuildFile; fileRef = A7D0AA29153C1FE40020F18B /* NSString+DTURLEncoding.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384471251BA33ED80037C68D /* DTAnimatedGIF.h in Headers */ = {isa = PBXBuildFile; fileRef = A7AC21A7196409560009E1B9 /* DTAnimatedGIF.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384471261BA33ED80037C68D /* DTSmartPagingScrollView.h in Headers */ = {isa = PBXBuildFile; fileRef = A76DB49716A5E37F0010CD85 /* DTSmartPagingScrollView.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384471271BA33ED80037C68D /* DTActionSheet.h in Headers */ = {isa = PBXBuildFile; fileRef = F88FF6D41920F1AF00120808 /* DTActionSheet.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384471281BA33ED80037C68D /* DTSidePanelController.h in Headers */ = {isa = PBXBuildFile; fileRef = A74663B21743D43600D4D7D5 /* DTSidePanelController.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384471291BA33ED80037C68D /* NSFileWrapper+DTCopying.h in Headers */ = {isa = PBXBuildFile; fileRef = A73B6F89163169BB002CCCA7 /* NSFileWrapper+DTCopying.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3844712A1BA33ED80037C68D /* DTExtendedFileAttributes.h in Headers */ = {isa = PBXBuildFile; fileRef = A7D6F2E315063448001CACDD /* DTExtendedFileAttributes.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3844712C1BA33ED80037C68D /* DTHTMLParser.h in Headers */ = {isa = PBXBuildFile; fileRef = A76DB48916A5E1D20010CD85 /* DTHTMLParser.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3844712D1BA33ED80037C68D /* NSString+DTPaths.h in Headers */ = {isa = PBXBuildFile; fileRef = A7D8627F14EBF65C001436AF /* NSString+DTPaths.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3844712E1BA33ED80037C68D /* DTTiledLayerWithoutFade.h in Headers */ = {isa = PBXBuildFile; fileRef = A730BCCC16D2892E003B849F /* DTTiledLayerWithoutFade.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3844712F1BA33ED80037C68D /* DTVersion.h in Headers */ = {isa = PBXBuildFile; fileRef = A70B4CC71486621B00873A4A /* DTVersion.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384471301BA33ED80037C68D /* DTFoundationConstants.h in Headers */ = {isa = PBXBuildFile; fileRef = FAB1726916301F0D00B44EDC /* DTFoundationConstants.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384471311BA33ED80037C68D /* UIViewController+DTSidePanelController.h in Headers */ = {isa = PBXBuildFile; fileRef = A7C92B67174F9AEC0019D70A /* UIViewController+DTSidePanelController.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384471321BA33ED80037C68D /* NSData+DTCrypto.h in Headers */ = {isa = PBXBuildFile; fileRef = A79500F5161D680000358BC3 /* NSData+DTCrypto.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384471331BA33ED80037C68D /* NSURL+DTUnshorten.h in Headers */ = {isa = PBXBuildFile; fileRef = A79231CC157A0B9400C3ACBB /* NSURL+DTUnshorten.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384471341BA33ED80037C68D /* UIApplication+DTNetworkActivity.h in Headers */ = {isa = PBXBuildFile; fileRef = A76DB4C216A5E50D0010CD85 /* UIApplication+DTNetworkActivity.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384471351BA33ED80037C68D /* DTCoreGraphicsUtils.h in Headers */ = {isa = PBXBuildFile; fileRef = A7D0AA6B153C39920020F18B /* DTCoreGraphicsUtils.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384471361BA33ED80037C68D /* NSMutableArray+DTMoving.h in Headers */ = {isa = PBXBuildFile; fileRef = A766135E16143F8A00DF6C2B /* NSMutableArray+DTMoving.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384471371BA33ED80037C68D /* DTSidePanelControllerSegue.h in Headers */ = {isa = PBXBuildFile; fileRef = C9826821196DAD7800A84677 /* DTSidePanelControllerSegue.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384471381BA33ED80037C68D /* DTObjectBlockExecutor.h in Headers */ = {isa = PBXBuildFile; fileRef = A78EDD2517A65B8600480205 /* DTObjectBlockExecutor.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384471391BA33ED80037C68D /* DTActivityTitleView.h in Headers */ = {isa = PBXBuildFile; fileRef = A76DB49116A5E37F0010CD85 /* DTActivityTitleView.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3844713A1BA33ED80037C68D /* NSObject+DTRuntime.h in Headers */ = {isa = PBXBuildFile; fileRef = A78EDD2717A65B8600480205 /* NSObject+DTRuntime.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3844713B1BA33ED80037C68D /* DTSidePanelPanGestureRecognizer.h in Headers */ = {isa = PBXBuildFile; fileRef = 355B257CC69FC64D78ABE0BE /* DTSidePanelPanGestureRecognizer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3844713C1BA33ED80037C68D /* DTLog.h in Headers */ = {isa = PBXBuildFile; fileRef = A7FA5FA217B0F7B4000FC61F /* DTLog.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3844713D1BA33ED80037C68D /* DTFolderMonitor.h in Headers */ = {isa = PBXBuildFile; fileRef = A78D661B17AFFCCC0039F5E6 /* DTFolderMonitor.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3844713E1BA33ED80037C68D /* DTPieProgressIndicator.h in Headers */ = {isa = PBXBuildFile; fileRef = A76DB49516A5E37F0010CD85 /* DTPieProgressIndicator.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3844713F1BA33ED80037C68D /* NSURL+DTAppLinks.h in Headers */ = {isa = PBXBuildFile; fileRef = A76DB4C016A5E50D0010CD85 /* NSURL+DTAppLinks.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384471401BA33ED80037C68D /* NSString+DTFormatNumbers.h in Headers */ = {isa = PBXBuildFile; fileRef = A70B4CCA1486621B00873A4A /* NSString+DTFormatNumbers.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384471411BA33ED80037C68D /* DTFoundation.h in Headers */ = {isa = PBXBuildFile; fileRef = A70B4CC51486621B00873A4A /* DTFoundation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384471421BA33ED80037C68D /* UIView+DTActionHandlers.h in Headers */ = {isa = PBXBuildFile; fileRef = F88FF6D81920F1AF00120808 /* UIView+DTActionHandlers.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384471431BA33ED80037C68D /* DTCustomColoredAccessory.h in Headers */ = {isa = PBXBuildFile; fileRef = A7E88ED016BC0278008CBA9C /* DTCustomColoredAccessory.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384471441BA33ED80037C68D /* DTBlockFunctions.h in Headers */ = {isa = PBXBuildFile; fileRef = A768BE3A17FC3C91008834C6 /* DTBlockFunctions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384471451BA33ED80037C68D /* DTBase64Coding.h in Headers */ = {isa = PBXBuildFile; fileRef = A77D5BF816E4961A00A45C28 /* DTBase64Coding.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384471461BA33ED80037C68D /* UIView+DTDebug.h in Headers */ = {isa = PBXBuildFile; fileRef = A78EDD2C17A65D4B00480205 /* UIView+DTDebug.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384471471BA33ED80037C68D /* NSString+DTUtilities.h in Headers */ = {isa = PBXBuildFile; fileRef = A7D0AA48153C233E0020F18B /* NSString+DTUtilities.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384471481BA33ED80037C68D /* UIColor+DTDebug.h in Headers */ = {isa = PBXBuildFile; fileRef = A78EDD2A17A65D4B00480205 /* UIColor+DTDebug.h */; settings = {ATTRIBUTES = (Public, ); }; };
		384471491BA33ED80037C68D /* NSURL+DTComparing.h in Headers */ = {isa = PBXBuildFile; fileRef = A7FAA3871652291D006ED151 /* NSURL+DTComparing.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3844714A1BA33ED80037C68D /* DTAlertView.h in Headers */ = {isa = PBXBuildFile; fileRef = F88FF6D61920F1AF00120808 /* DTAlertView.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3844714B1BA33ED80037C68D /* DTProgressHUD.h in Headers */ = {isa = PBXBuildFile; fileRef = F88FF6831920A9DF00120808 /* DTProgressHUD.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3844714C1BA33ED80037C68D /* DTAsyncFileDeleter.h in Headers */ = {isa = PBXBuildFile; fileRef = A7FA5FAD17B127E0000FC61F /* DTAsyncFileDeleter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3844714D1BA33ED80037C68D /* UIImage+DTFoundation.h in Headers */ = {isa = PBXBuildFile; fileRef = A76DB4C416A5E50E0010CD85 /* UIImage+DTFoundation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3844714E1BA33ED80037C68D /* NSDictionary+DTError.h in Headers */ = {isa = PBXBuildFile; fileRef = A7D0AA23153C1B160020F18B /* NSDictionary+DTError.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3844714F1BA33ED80037C68D /* DTCompatibility.h in Headers */ = {isa = PBXBuildFile; fileRef = A74549681B6A3F26004B0CA7 /* DTCompatibility.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7DBE5BE11859D2E300679AF8 /* DTASN1BitString.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = A78EDD1E17A65AD800480205 /* DTASN1BitString.h */; };
		7DBE5BE21859D2E500679AF8 /* DTASN1Parser.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = A78EDD2017A65AD800480205 /* DTASN1Parser.h */; };
		7DBE5BE31859D2E800679AF8 /* DTASN1Serialization.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = A78EDD2217A65AD800480205 /* DTASN1Serialization.h */; };
		A704BEBA1B6176D10067BF7E /* AppKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A704BEB91B6176D10067BF7E /* AppKit.framework */; };
		A704BEBB1B6176D70067BF7E /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A7F4DFFA147FD08F00F4059A /* Foundation.framework */; };
		A70B4CE81486637E00873A4A /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A7F4DFFA147FD08F00F4059A /* Foundation.framework */; };
		A70B4CF7148663AF00873A4A /* DTFoundation.h in Headers */ = {isa = PBXBuildFile; fileRef = A70B4CC51486621B00873A4A /* DTFoundation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A70BCEC41BF4A5EF000CD60D /* DTObjectBlockExecutor.h in Headers */ = {isa = PBXBuildFile; fileRef = A78EDD2517A65B8600480205 /* DTObjectBlockExecutor.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A70BCEC51BF4A5F0000CD60D /* DTObjectBlockExecutor.h in Headers */ = {isa = PBXBuildFile; fileRef = A78EDD2517A65B8600480205 /* DTObjectBlockExecutor.h */; };
		A70BCEC61BF4A5F0000CD60D /* DTObjectBlockExecutor.h in Headers */ = {isa = PBXBuildFile; fileRef = A78EDD2517A65B8600480205 /* DTObjectBlockExecutor.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A70BCEC71BF4A61A000CD60D /* NSObject+DTRuntime.h in Headers */ = {isa = PBXBuildFile; fileRef = A78EDD2717A65B8600480205 /* NSObject+DTRuntime.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A70BCEC81BF4A61B000CD60D /* NSObject+DTRuntime.h in Headers */ = {isa = PBXBuildFile; fileRef = A78EDD2717A65B8600480205 /* NSObject+DTRuntime.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A70BCEC91BF4A61C000CD60D /* NSObject+DTRuntime.h in Headers */ = {isa = PBXBuildFile; fileRef = A78EDD2717A65B8600480205 /* NSObject+DTRuntime.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A70C4FCC17AA7CEC00000DF5 /* UIColor+DTDebug.m in Sources */ = {isa = PBXBuildFile; fileRef = A78EDD2B17A65D4B00480205 /* UIColor+DTDebug.m */; };
		A70C4FCD17AA7CEC00000DF5 /* UIView+DTDebug.m in Sources */ = {isa = PBXBuildFile; fileRef = A78EDD2D17A65D4B00480205 /* UIView+DTDebug.m */; };
		A70C4FE817AA7F0200000DF5 /* DTScriptExpression.m in Sources */ = {isa = PBXBuildFile; fileRef = A78EDD1817A65AD700480205 /* DTScriptExpression.m */; };
		A70C4FE917AA7F0200000DF5 /* DTScriptVariable.m in Sources */ = {isa = PBXBuildFile; fileRef = A78EDD1A17A65AD700480205 /* DTScriptVariable.m */; };
		A70C4FEA17AA7F0200000DF5 /* NSScanner+DTScripting.m in Sources */ = {isa = PBXBuildFile; fileRef = A78EDD1C17A65AD800480205 /* NSScanner+DTScripting.m */; };
		A70C4FEE17AA7F0700000DF5 /* DTScriptExpression.m in Sources */ = {isa = PBXBuildFile; fileRef = A78EDD1817A65AD700480205 /* DTScriptExpression.m */; };
		A70C4FEF17AA7F0700000DF5 /* DTScriptVariable.m in Sources */ = {isa = PBXBuildFile; fileRef = A78EDD1A17A65AD700480205 /* DTScriptVariable.m */; };
		A70C4FF017AA7F0700000DF5 /* NSScanner+DTScripting.m in Sources */ = {isa = PBXBuildFile; fileRef = A78EDD1C17A65AD800480205 /* NSScanner+DTScripting.m */; };
		A70C4FF117AA7F2000000DF5 /* DTObjectBlockExecutor.m in Sources */ = {isa = PBXBuildFile; fileRef = A78EDD2617A65B8600480205 /* DTObjectBlockExecutor.m */; };
		A70C4FF217AA7F2000000DF5 /* NSObject+DTRuntime.m in Sources */ = {isa = PBXBuildFile; fileRef = A78EDD2817A65B8600480205 /* NSObject+DTRuntime.m */; };
		A70C4FF517AA7F2300000DF5 /* DTObjectBlockExecutor.m in Sources */ = {isa = PBXBuildFile; fileRef = A78EDD2617A65B8600480205 /* DTObjectBlockExecutor.m */; };
		A70C4FF617AA7F2300000DF5 /* NSObject+DTRuntime.m in Sources */ = {isa = PBXBuildFile; fileRef = A78EDD2817A65B8600480205 /* NSObject+DTRuntime.m */; };
		A70D74D11743AFBC00E6E626 /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A7F4DFF8147FD08900F4059A /* UIKit.framework */; };
		A70D74D21743AFBC00E6E626 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A7F4DFFA147FD08F00F4059A /* Foundation.framework */; };
		A70D74D31743AFBC00E6E626 /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FAE5B9311610A38200CA0D99 /* CoreGraphics.framework */; };
		A70D74F11743AFDF00E6E626 /* libDTFoundation.a in Frameworks */ = {isa = PBXBuildFile; fileRef = A70B4CE71486637E00873A4A /* libDTFoundation.a */; };
		A70D74F21743B01E00E6E626 /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FAB1725D163004FF00B44EDC /* QuartzCore.framework */; };
		A710A50B160755A500437D36 /* NSDictionary+DTError.m in Sources */ = {isa = PBXBuildFile; fileRef = A7D0AA24153C1B160020F18B /* NSDictionary+DTError.m */; };
		A72121F5173C001B003C6F0A /* DTReachability.m in Sources */ = {isa = PBXBuildFile; fileRef = A72121D5173BFF89003C6F0A /* DTReachability.m */; };
		A72121F6173C001B003C6F0A /* DTReachability.m in Sources */ = {isa = PBXBuildFile; fileRef = A72121D5173BFF89003C6F0A /* DTReachability.m */; };
		A730BCCF16D2892E003B849F /* DTTiledLayerWithoutFade.h in Headers */ = {isa = PBXBuildFile; fileRef = A730BCCC16D2892E003B849F /* DTTiledLayerWithoutFade.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A730BCD116D2892E003B849F /* DTTiledLayerWithoutFade.m in Sources */ = {isa = PBXBuildFile; fileRef = A730BCCD16D2892E003B849F /* DTTiledLayerWithoutFade.m */; };
		A73B6F8F163169BB002CCCA7 /* NSFileWrapper+DTCopying.m in Sources */ = {isa = PBXBuildFile; fileRef = A73B6F8A163169BB002CCCA7 /* NSFileWrapper+DTCopying.m */; };
		A73B6F90163169BB002CCCA7 /* NSFileWrapper+DTCopying.m in Sources */ = {isa = PBXBuildFile; fileRef = A73B6F8A163169BB002CCCA7 /* NSFileWrapper+DTCopying.m */; };
		A73B6FA716318EBC002CCCA7 /* NSString+DTFormatNumbers.m in Sources */ = {isa = PBXBuildFile; fileRef = A70B4CCB1486621B00873A4A /* NSString+DTFormatNumbers.m */; };
		A73D5BAE155271FD0024BDB7 /* NSArray+DTError.m in Sources */ = {isa = PBXBuildFile; fileRef = A73D5BAA155271FD0024BDB7 /* NSArray+DTError.m */; };
		A741709E1CEA10E600F15DA9 /* DTCompatibility.h in Headers */ = {isa = PBXBuildFile; fileRef = A74549681B6A3F26004B0CA7 /* DTCompatibility.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7437DE216147A450091C1A2 /* NSString+DTPaths.m in Sources */ = {isa = PBXBuildFile; fileRef = A7D8628014EBF65C001436AF /* NSString+DTPaths.m */; };
		A7444D2C162C011C00DD3692 /* NSArray+DTError.m in Sources */ = {isa = PBXBuildFile; fileRef = A73D5BAA155271FD0024BDB7 /* NSArray+DTError.m */; };
		A74988811B6EA91C00A66234 /* DTProgressHUD.h in Headers */ = {isa = PBXBuildFile; fileRef = F88FF6831920A9DF00120808 /* DTProgressHUD.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A74988821B6EA91F00A66234 /* DTProgressHUD.h in Headers */ = {isa = PBXBuildFile; fileRef = F88FF6831920A9DF00120808 /* DTProgressHUD.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A74988871B6EAB0900A66234 /* DTBlockFunctions.h in Headers */ = {isa = PBXBuildFile; fileRef = A768BE3A17FC3C91008834C6 /* DTBlockFunctions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A74A765017BBB163007073C8 /* libDTFoundation.a in Frameworks */ = {isa = PBXBuildFile; fileRef = A70B4CE71486637E00873A4A /* libDTFoundation.a */; };
		A74A7A851B61712B004163BE /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A74A7A841B61712B004163BE /* UIKit.framework */; };
		A74A7A881B61713A004163BE /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A74A7A871B61713A004163BE /* QuartzCore.framework */; };
		A74A7A8A1B617158004163BE /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A74A7A891B617158004163BE /* Foundation.framework */; };
		A74A7A8C1B61716E004163BE /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A74A7A8B1B61716E004163BE /* CoreGraphics.framework */; };
		A74A7A8E1B617186004163BE /* ImageIO.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A74A7A8D1B617186004163BE /* ImageIO.framework */; };
		A766136416143F8A00DF6C2B /* NSMutableArray+DTMoving.m in Sources */ = {isa = PBXBuildFile; fileRef = A766135F16143F8A00DF6C2B /* NSMutableArray+DTMoving.m */; };
		A766136616143F8A00DF6C2B /* NSMutableArray+DTMoving.m in Sources */ = {isa = PBXBuildFile; fileRef = A766135F16143F8A00DF6C2B /* NSMutableArray+DTMoving.m */; };
		A76613E5161444FD00DF6C2B /* NSString+DTUtilities.m in Sources */ = {isa = PBXBuildFile; fileRef = A7D0AA49153C233E0020F18B /* NSString+DTUtilities.m */; };
		A768BE4117FC3C91008834C6 /* DTBlockFunctions.m in Sources */ = {isa = PBXBuildFile; fileRef = A768BE3B17FC3C91008834C6 /* DTBlockFunctions.m */; };
		A76DB48B16A5E1D20010CD85 /* DTHTMLParser.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB48A16A5E1D20010CD85 /* DTHTMLParser.m */; };
		A76DB48C16A5E1D20010CD85 /* DTHTMLParser.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB48A16A5E1D20010CD85 /* DTHTMLParser.m */; };
		A76DB4A016A5E37F0010CD85 /* DTActivityTitleView.h in Headers */ = {isa = PBXBuildFile; fileRef = A76DB49116A5E37F0010CD85 /* DTActivityTitleView.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A76DB4A216A5E37F0010CD85 /* DTActivityTitleView.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB49216A5E37F0010CD85 /* DTActivityTitleView.m */; };
		A76DB4AC16A5E37F0010CD85 /* DTPieProgressIndicator.h in Headers */ = {isa = PBXBuildFile; fileRef = A76DB49516A5E37F0010CD85 /* DTPieProgressIndicator.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A76DB4AE16A5E37F0010CD85 /* DTPieProgressIndicator.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB49616A5E37F0010CD85 /* DTPieProgressIndicator.m */; };
		A76DB4B216A5E37F0010CD85 /* DTSmartPagingScrollView.h in Headers */ = {isa = PBXBuildFile; fileRef = A76DB49716A5E37F0010CD85 /* DTSmartPagingScrollView.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A76DB4B416A5E37F0010CD85 /* DTSmartPagingScrollView.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB49816A5E37F0010CD85 /* DTSmartPagingScrollView.m */; };
		A76DB4BB16A5E3B20010CD85 /* DTScrollView.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB4B916A5E3B20010CD85 /* DTScrollView.m */; };
		A76DB4BF16A5E4770010CD85 /* DTZipArchive.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB4BE16A5E4770010CD85 /* DTZipArchive.m */; };
		A76DB4CD16A5E5100010CD85 /* NSURL+DTAppLinks.h in Headers */ = {isa = PBXBuildFile; fileRef = A76DB4C016A5E50D0010CD85 /* NSURL+DTAppLinks.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A76DB4CF16A5E5100010CD85 /* NSURL+DTAppLinks.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB4C116A5E50D0010CD85 /* NSURL+DTAppLinks.m */; };
		A76DB4D116A5E5100010CD85 /* UIApplication+DTNetworkActivity.h in Headers */ = {isa = PBXBuildFile; fileRef = A76DB4C216A5E50D0010CD85 /* UIApplication+DTNetworkActivity.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A76DB4D316A5E5100010CD85 /* UIApplication+DTNetworkActivity.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB4C316A5E50E0010CD85 /* UIApplication+DTNetworkActivity.m */; };
		A76DB4D516A5E5100010CD85 /* UIImage+DTFoundation.h in Headers */ = {isa = PBXBuildFile; fileRef = A76DB4C416A5E50E0010CD85 /* UIImage+DTFoundation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A76DB4D716A5E5100010CD85 /* UIImage+DTFoundation.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB4C516A5E50E0010CD85 /* UIImage+DTFoundation.m */; };
		A76DB4DD16A5E5100010CD85 /* UIView+DTFoundation.h in Headers */ = {isa = PBXBuildFile; fileRef = A76DB4C816A5E50F0010CD85 /* UIView+DTFoundation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A76DB4DF16A5E5100010CD85 /* UIView+DTFoundation.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB4C916A5E50F0010CD85 /* UIView+DTFoundation.m */; };
		A76DB4EF16A5E5590010CD85 /* NSDocument+DTFoundation.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB4E516A5E5590010CD85 /* NSDocument+DTFoundation.m */; };
		A76DB4F116A5E5590010CD85 /* NSImage+DTUtilities.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB4E716A5E5590010CD85 /* NSImage+DTUtilities.m */; };
		A76DB4F316A5E5590010CD85 /* NSValue+DTConversion.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB4E916A5E5590010CD85 /* NSValue+DTConversion.m */; };
		A76DB4F516A5E5590010CD85 /* NSView+DTAutoLayout.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB4EB16A5E5590010CD85 /* NSView+DTAutoLayout.m */; };
		A76DB4F716A5E5590010CD85 /* NSWindowController+DTPanelControllerPresenting.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB4ED16A5E5590010CD85 /* NSWindowController+DTPanelControllerPresenting.m */; };
		A76DB4FB16A5E5950010CD85 /* NSString+DTUTI.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB4FA16A5E5950010CD85 /* NSString+DTUTI.m */; };
		A77D5BFF16E4961A00A45C28 /* DTBase64Coding.m in Sources */ = {isa = PBXBuildFile; fileRef = A77D5BF916E4961A00A45C28 /* DTBase64Coding.m */; };
		A77DD41714E825FC00F34B03 /* crypt.h in Headers */ = {isa = PBXBuildFile; fileRef = A77DD40D14E825FC00F34B03 /* crypt.h */; };
		A77DD41B14E825FC00F34B03 /* ioapi.h in Headers */ = {isa = PBXBuildFile; fileRef = A77DD40F14E825FC00F34B03 /* ioapi.h */; };
		A77DD41F14E825FC00F34B03 /* mztools.h in Headers */ = {isa = PBXBuildFile; fileRef = A77DD41114E825FC00F34B03 /* mztools.h */; };
		A77DD42314E825FC00F34B03 /* unzip.h in Headers */ = {isa = PBXBuildFile; fileRef = A77DD41314E825FC00F34B03 /* unzip.h */; };
		A77DD42714E825FC00F34B03 /* zip.h in Headers */ = {isa = PBXBuildFile; fileRef = A77DD41514E825FC00F34B03 /* zip.h */; };
		A78D662217AFFCCC0039F5E6 /* DTFolderMonitor.m in Sources */ = {isa = PBXBuildFile; fileRef = A78D661C17AFFCCC0039F5E6 /* DTFolderMonitor.m */; };
		A7917C78191A312700964D63 /* XCTest.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A79D99AA1792D1F50082BC06 /* XCTest.framework */; };
		A7917C79191A312700964D63 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A7F4DFFA147FD08F00F4059A /* Foundation.framework */; };
		A7917C9E191A323000964D63 /* libz.1.2.5.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = A7917C9D191A322F00964D63 /* libz.1.2.5.dylib */; };
		A7917CA3191A327A00964D63 /* libxml2.2.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = A7917CA2191A327A00964D63 /* libxml2.2.dylib */; };
		A7917CB6191A3CD500964D63 /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A7917CB5191A3CD500964D63 /* UIKit.framework */; };
		A79231D1157A0B9400C3ACBB /* NSURL+DTUnshorten.m in Sources */ = {isa = PBXBuildFile; fileRef = A79231CD157A0B9400C3ACBB /* NSURL+DTUnshorten.m */; };
		A792968F1619F0FA00D5C979 /* DTCoreGraphicsUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = A7D0AA6C153C39920020F18B /* DTCoreGraphicsUtils.m */; };
		A7950101161D680000358BC3 /* NSData+DTCrypto.m in Sources */ = {isa = PBXBuildFile; fileRef = A79500F6161D680000358BC3 /* NSData+DTCrypto.m */; };
		A7950102161D680000358BC3 /* NSData+DTCrypto.m in Sources */ = {isa = PBXBuildFile; fileRef = A79500F6161D680000358BC3 /* NSData+DTCrypto.m */; };
		A79BA5D81B46A0D50086C2F6 /* DTHTMLParser.h in Headers */ = {isa = PBXBuildFile; fileRef = A76DB48916A5E1D20010CD85 /* DTHTMLParser.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A79BA5D91B46A0D50086C2F6 /* DTHTMLParser.h in Headers */ = {isa = PBXBuildFile; fileRef = A76DB48916A5E1D20010CD85 /* DTHTMLParser.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A79BA6C31B46A38A0086C2F6 /* DTHTMLParser.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB48A16A5E1D20010CD85 /* DTHTMLParser.m */; };
		A79BA6C71B46A3CA0086C2F6 /* libxml2.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = A79BA6C61B46A3CA0086C2F6 /* libxml2.dylib */; };
		A79BA6CA1B46A3E40086C2F6 /* DTAnimatedGIF.h in Headers */ = {isa = PBXBuildFile; fileRef = A7AC21A7196409560009E1B9 /* DTAnimatedGIF.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A79BA6CB1B46A3E40086C2F6 /* DTAnimatedGIF.m in Sources */ = {isa = PBXBuildFile; fileRef = A7AC21A8196409560009E1B9 /* DTAnimatedGIF.m */; };
		A79BA7CF1B46C3EF0086C2F6 /* DTLog.h in Headers */ = {isa = PBXBuildFile; fileRef = A7FA5FA217B0F7B4000FC61F /* DTLog.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A79BA7D01B46C3EF0086C2F6 /* DTLog.h in Headers */ = {isa = PBXBuildFile; fileRef = A7FA5FA217B0F7B4000FC61F /* DTLog.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A79BA7E61B46C4260086C2F6 /* DTWeakSupport.h in Headers */ = {isa = PBXBuildFile; fileRef = A7FB1218175C9D5000D4B7F0 /* DTWeakSupport.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A79BA7E71B46C4270086C2F6 /* DTWeakSupport.h in Headers */ = {isa = PBXBuildFile; fileRef = A7FB1218175C9D5000D4B7F0 /* DTWeakSupport.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A79BA7E81B46C5050086C2F6 /* NSString+DTUtilities.h in Headers */ = {isa = PBXBuildFile; fileRef = A7D0AA48153C233E0020F18B /* NSString+DTUtilities.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A79BA7E91B46C5060086C2F6 /* NSString+DTUtilities.h in Headers */ = {isa = PBXBuildFile; fileRef = A7D0AA48153C233E0020F18B /* NSString+DTUtilities.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A79BA8481B46E5840086C2F6 /* DTHTMLParser.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB48A16A5E1D20010CD85 /* DTHTMLParser.m */; };
		A79BA84A1B46E5DF0086C2F6 /* libxml2.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = A7D60FF715D3B28B00AEDD1B /* libxml2.dylib */; };
		A79BA89A1B46ED880086C2F6 /* DTHTMLParser.h in Headers */ = {isa = PBXBuildFile; fileRef = A76DB48916A5E1D20010CD85 /* DTHTMLParser.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A79BA89B1B46ED880086C2F6 /* DTHTMLParser.h in Headers */ = {isa = PBXBuildFile; fileRef = A76DB48916A5E1D20010CD85 /* DTHTMLParser.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A79BA89C1B46EDDD0086C2F6 /* DTAnimatedGIF.h in Headers */ = {isa = PBXBuildFile; fileRef = A7AC21A7196409560009E1B9 /* DTAnimatedGIF.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A79BA89D1B46EDE90086C2F6 /* DTBase64Coding.h in Headers */ = {isa = PBXBuildFile; fileRef = A77D5BF816E4961A00A45C28 /* DTBase64Coding.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A79D99C21792D2490082BC06 /* NSURL+DTAWS.m in Sources */ = {isa = PBXBuildFile; fileRef = A79D99C11792D2490082BC06 /* NSURL+DTAWS.m */; };
		A7A5F0AB1A2FA92400DA3B62 /* Security.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A7A5F0AA1A2FA92400DA3B62 /* Security.framework */; };
		A7A7CC7A14866CAF00EC2EE4 /* NSString+DTFormatNumbers.m in Sources */ = {isa = PBXBuildFile; fileRef = A70B4CCB1486621B00873A4A /* NSString+DTFormatNumbers.m */; };
		A7AC21C9196417BF0009E1B9 /* DTAnimatedGIF.m in Sources */ = {isa = PBXBuildFile; fileRef = A7AC21A8196409560009E1B9 /* DTAnimatedGIF.m */; };
		A7AC21CC196417D00009E1B9 /* libDTAnimatedGIF.a in Frameworks */ = {isa = PBXBuildFile; fileRef = A7AC21AF196417830009E1B9 /* libDTAnimatedGIF.a */; };
		A7AC21CE1964184F0009E1B9 /* ImageIO.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A7AC21CD1964184F0009E1B9 /* ImageIO.framework */; };
		A7B505351B6A594F00BC743A /* DTCompatibility.h in Headers */ = {isa = PBXBuildFile; fileRef = A74549681B6A3F26004B0CA7 /* DTCompatibility.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7B505361B6A595700BC743A /* DTCompatibility.h in Headers */ = {isa = PBXBuildFile; fileRef = A74549681B6A3F26004B0CA7 /* DTCompatibility.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7BDB4221C64D6FE00E8CD84 /* DTProgressHUD.m in Sources */ = {isa = PBXBuildFile; fileRef = F88FF6841920A9DF00120808 /* DTProgressHUD.m */; };
		A7BDB4241C64D71900E8CD84 /* DTProgressHUDWindow.m in Sources */ = {isa = PBXBuildFile; fileRef = F88FF6D11920BF5200120808 /* DTProgressHUDWindow.m */; };
		A7C92B6A174F9AEC0019D70A /* UIViewController+DTSidePanelController.h in Headers */ = {isa = PBXBuildFile; fileRef = A7C92B67174F9AEC0019D70A /* UIViewController+DTSidePanelController.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7C92B6C174F9AEC0019D70A /* UIViewController+DTSidePanelController.m in Sources */ = {isa = PBXBuildFile; fileRef = A7C92B68174F9AEC0019D70A /* UIViewController+DTSidePanelController.m */; };
		A7C92B86174FB8C20019D70A /* DTSQLiteDatabase.m in Sources */ = {isa = PBXBuildFile; fileRef = A7C92B73174FB81C0019D70A /* DTSQLiteDatabase.m */; };
		A7C92B87174FB8C20019D70A /* DTSQLiteFunctions.m in Sources */ = {isa = PBXBuildFile; fileRef = A7C92B75174FB81C0019D70A /* DTSQLiteFunctions.m */; };
		A7C92B97174FB9AB0019D70A /* DTSQLiteDatabase.m in Sources */ = {isa = PBXBuildFile; fileRef = A7C92B73174FB81C0019D70A /* DTSQLiteDatabase.m */; };
		A7C92B98174FB9AB0019D70A /* DTSQLiteFunctions.m in Sources */ = {isa = PBXBuildFile; fileRef = A7C92B75174FB81C0019D70A /* DTSQLiteFunctions.m */; };
		A7D0AA28153C1B160020F18B /* NSDictionary+DTError.m in Sources */ = {isa = PBXBuildFile; fileRef = A7D0AA24153C1B160020F18B /* NSDictionary+DTError.m */; };
		A7D0AA2E153C1FE40020F18B /* NSString+DTURLEncoding.m in Sources */ = {isa = PBXBuildFile; fileRef = A7D0AA2A153C1FE40020F18B /* NSString+DTURLEncoding.m */; };
		A7D0AA4D153C233E0020F18B /* NSString+DTUtilities.m in Sources */ = {isa = PBXBuildFile; fileRef = A7D0AA49153C233E0020F18B /* NSString+DTUtilities.m */; };
		A7D0AA70153C39920020F18B /* DTCoreGraphicsUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = A7D0AA6C153C39920020F18B /* DTCoreGraphicsUtils.m */; };
		A7D8628414EBF65C001436AF /* NSString+DTPaths.m in Sources */ = {isa = PBXBuildFile; fileRef = A7D8628014EBF65C001436AF /* NSString+DTPaths.m */; };
		A7D8C7B11723174600025675 /* ioapi.c in Sources */ = {isa = PBXBuildFile; fileRef = A77DD40E14E825FC00F34B03 /* ioapi.c */; };
		A7D8C7B21723174600025675 /* mztools.c in Sources */ = {isa = PBXBuildFile; fileRef = A77DD41014E825FC00F34B03 /* mztools.c */; };
		A7D8C7B31723174600025675 /* unzip.c in Sources */ = {isa = PBXBuildFile; fileRef = A77DD41214E825FC00F34B03 /* unzip.c */; };
		A7D8C7B41723174600025675 /* zip.c in Sources */ = {isa = PBXBuildFile; fileRef = A77DD41414E825FC00F34B03 /* zip.c */; };
		A7D8C7B51723174600025675 /* DTZipArchive.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB4BE16A5E4770010CD85 /* DTZipArchive.m */; };
		A7D8C7B61723174600025675 /* DTZipArchiveNode.m in Sources */ = {isa = PBXBuildFile; fileRef = C0494FCC2C675F2CDF315027 /* DTZipArchiveNode.m */; };
		A7D8C7B71723174600025675 /* DTZipArchivePKZip.m in Sources */ = {isa = PBXBuildFile; fileRef = C0494776E02407791E96C641 /* DTZipArchivePKZip.m */; };
		A7D8C7B81723174600025675 /* DTZipArchiveGZip.m in Sources */ = {isa = PBXBuildFile; fileRef = C0494E1FBE9D6D4FB042F2A3 /* DTZipArchiveGZip.m */; };
		A7D8C7BC1723174600025675 /* DTZipArchiveNode.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = C0494CD06FFAB6AEAC8ADAD4 /* DTZipArchiveNode.h */; };
		A7D8C7BD1723174600025675 /* DTZipArchivePKZip.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = C0494E7390203BE4105C5070 /* DTZipArchivePKZip.h */; };
		A7D8C7BE1723174600025675 /* DTZipArchiveGZip.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = C04943358446F1A9BA86F458 /* DTZipArchiveGZip.h */; };
		A7D95AD81BC40DAB00AB8A21 /* DTASN1BitString.h in Headers */ = {isa = PBXBuildFile; fileRef = A78EDD1E17A65AD800480205 /* DTASN1BitString.h */; };
		A7D95AD91BC40DAB00AB8A21 /* DTASN1BitString.m in Sources */ = {isa = PBXBuildFile; fileRef = A78EDD1F17A65AD800480205 /* DTASN1BitString.m */; };
		A7D95ADA1BC40DAB00AB8A21 /* DTASN1Parser.h in Headers */ = {isa = PBXBuildFile; fileRef = A78EDD2017A65AD800480205 /* DTASN1Parser.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7D95ADB1BC40DAB00AB8A21 /* DTASN1Parser.m in Sources */ = {isa = PBXBuildFile; fileRef = A78EDD2117A65AD800480205 /* DTASN1Parser.m */; };
		A7D95ADC1BC40DAB00AB8A21 /* DTASN1Serialization.h in Headers */ = {isa = PBXBuildFile; fileRef = A78EDD2217A65AD800480205 /* DTASN1Serialization.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7D95ADD1BC40DAB00AB8A21 /* DTASN1Serialization.m in Sources */ = {isa = PBXBuildFile; fileRef = A78EDD2317A65AD800480205 /* DTASN1Serialization.m */; };
		A7D95ADE1BC40DAB00AB8A21 /* DTASN1BitString.h in Headers */ = {isa = PBXBuildFile; fileRef = A78EDD1E17A65AD800480205 /* DTASN1BitString.h */; };
		A7D95ADF1BC40DAB00AB8A21 /* DTASN1BitString.m in Sources */ = {isa = PBXBuildFile; fileRef = A78EDD1F17A65AD800480205 /* DTASN1BitString.m */; };
		A7D95AE01BC40DAB00AB8A21 /* DTASN1Parser.h in Headers */ = {isa = PBXBuildFile; fileRef = A78EDD2017A65AD800480205 /* DTASN1Parser.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7D95AE11BC40DAB00AB8A21 /* DTASN1Parser.m in Sources */ = {isa = PBXBuildFile; fileRef = A78EDD2117A65AD800480205 /* DTASN1Parser.m */; };
		A7D95AE21BC40DAB00AB8A21 /* DTASN1Serialization.h in Headers */ = {isa = PBXBuildFile; fileRef = A78EDD2217A65AD800480205 /* DTASN1Serialization.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7D95AE31BC40DAB00AB8A21 /* DTASN1Serialization.m in Sources */ = {isa = PBXBuildFile; fileRef = A78EDD2317A65AD800480205 /* DTASN1Serialization.m */; };
		A7D95AE41BC40DAC00AB8A21 /* DTASN1BitString.h in Headers */ = {isa = PBXBuildFile; fileRef = A78EDD1E17A65AD800480205 /* DTASN1BitString.h */; };
		A7D95AE51BC40DAC00AB8A21 /* DTASN1BitString.m in Sources */ = {isa = PBXBuildFile; fileRef = A78EDD1F17A65AD800480205 /* DTASN1BitString.m */; };
		A7D95AE61BC40DAC00AB8A21 /* DTASN1Parser.h in Headers */ = {isa = PBXBuildFile; fileRef = A78EDD2017A65AD800480205 /* DTASN1Parser.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7D95AE71BC40DAC00AB8A21 /* DTASN1Parser.m in Sources */ = {isa = PBXBuildFile; fileRef = A78EDD2117A65AD800480205 /* DTASN1Parser.m */; };
		A7D95AE81BC40DAC00AB8A21 /* DTASN1Serialization.h in Headers */ = {isa = PBXBuildFile; fileRef = A78EDD2217A65AD800480205 /* DTASN1Serialization.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7D95AE91BC40DAC00AB8A21 /* DTASN1Serialization.m in Sources */ = {isa = PBXBuildFile; fileRef = A78EDD2317A65AD800480205 /* DTASN1Serialization.m */; };
		A7D9D62E1758C89800C29B6F /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A7F4DFF8147FD08900F4059A /* UIKit.framework */; };
		A7D9D62F1758C89800C29B6F /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A7F4DFFA147FD08F00F4059A /* Foundation.framework */; };
		A7D9D6301758C89800C29B6F /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FAE5B9311610A38200CA0D99 /* CoreGraphics.framework */; };
		A7D9D6621758CA5A00C29B6F /* libDTReachability_iOS.a in Frameworks */ = {isa = PBXBuildFile; fileRef = A72121DA173BFFB1003C6F0A /* libDTReachability_iOS.a */; };
		A7D9D6641758CA6300C29B6F /* SystemConfiguration.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A7D9D6631758CA6300C29B6F /* SystemConfiguration.framework */; };
		A7E38406160E039D00CF72D6 /* ioapi.c in Sources */ = {isa = PBXBuildFile; fileRef = A77DD40E14E825FC00F34B03 /* ioapi.c */; };
		A7E38407160E03A600CF72D6 /* mztools.c in Sources */ = {isa = PBXBuildFile; fileRef = A77DD41014E825FC00F34B03 /* mztools.c */; };
		A7E38408160E03A600CF72D6 /* unzip.c in Sources */ = {isa = PBXBuildFile; fileRef = A77DD41214E825FC00F34B03 /* unzip.c */; };
		A7E38409160E03A600CF72D6 /* zip.c in Sources */ = {isa = PBXBuildFile; fileRef = A77DD41414E825FC00F34B03 /* zip.c */; };
		A7E799791AB9AF3B008C2393 /* DTFoundation.h in Headers */ = {isa = PBXBuildFile; fileRef = A70B4CC51486621B00873A4A /* DTFoundation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7E7997A1AB9AFA9008C2393 /* DTFoundation.h in Headers */ = {isa = PBXBuildFile; fileRef = A70B4CC51486621B00873A4A /* DTFoundation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7E889B416A9B1C4009EF0DF /* NSString+DTUTI.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB4FA16A5E5950010CD85 /* NSString+DTUTI.m */; };
		A7E88ED316BC0278008CBA9C /* DTCustomColoredAccessory.h in Headers */ = {isa = PBXBuildFile; fileRef = A7E88ED016BC0278008CBA9C /* DTCustomColoredAccessory.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7E88ED516BC0278008CBA9C /* DTCustomColoredAccessory.m in Sources */ = {isa = PBXBuildFile; fileRef = A7E88ED116BC0278008CBA9C /* DTCustomColoredAccessory.m */; };
		A7EA07591A2B220100B61CCE /* DTScrollView.h in Headers */ = {isa = PBXBuildFile; fileRef = A76DB4B816A5E3B20010CD85 /* DTScrollView.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA075A1A2B220100B61CCE /* DTScrollView.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB4B916A5E3B20010CD85 /* DTScrollView.m */; };
		A7EA075B1A2B220100B61CCE /* NSDocument+DTFoundation.h in Headers */ = {isa = PBXBuildFile; fileRef = A76DB4E416A5E5590010CD85 /* NSDocument+DTFoundation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA075C1A2B220100B61CCE /* NSDocument+DTFoundation.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB4E516A5E5590010CD85 /* NSDocument+DTFoundation.m */; };
		A7EA075D1A2B220100B61CCE /* NSImage+DTUtilities.h in Headers */ = {isa = PBXBuildFile; fileRef = A76DB4E616A5E5590010CD85 /* NSImage+DTUtilities.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA075E1A2B220100B61CCE /* NSImage+DTUtilities.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB4E716A5E5590010CD85 /* NSImage+DTUtilities.m */; };
		A7EA075F1A2B220100B61CCE /* NSValue+DTConversion.h in Headers */ = {isa = PBXBuildFile; fileRef = A76DB4E816A5E5590010CD85 /* NSValue+DTConversion.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA07601A2B220100B61CCE /* NSValue+DTConversion.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB4E916A5E5590010CD85 /* NSValue+DTConversion.m */; };
		A7EA07611A2B220100B61CCE /* NSView+DTAutoLayout.h in Headers */ = {isa = PBXBuildFile; fileRef = A76DB4EA16A5E5590010CD85 /* NSView+DTAutoLayout.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA07621A2B220100B61CCE /* NSView+DTAutoLayout.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB4EB16A5E5590010CD85 /* NSView+DTAutoLayout.m */; };
		A7EA07631A2B220100B61CCE /* NSWindowController+DTPanelControllerPresenting.h in Headers */ = {isa = PBXBuildFile; fileRef = A76DB4EC16A5E5590010CD85 /* NSWindowController+DTPanelControllerPresenting.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA07641A2B220100B61CCE /* NSWindowController+DTPanelControllerPresenting.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB4ED16A5E5590010CD85 /* NSWindowController+DTPanelControllerPresenting.m */; };
		A7EA07651A2B221300B61CCE /* NSArray+DTError.h in Headers */ = {isa = PBXBuildFile; fileRef = A73D5BA9155271FD0024BDB7 /* NSArray+DTError.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA07661A2B221300B61CCE /* NSArray+DTError.m in Sources */ = {isa = PBXBuildFile; fileRef = A73D5BAA155271FD0024BDB7 /* NSArray+DTError.m */; };
		A7EA07671A2B221300B61CCE /* NSData+DTCrypto.h in Headers */ = {isa = PBXBuildFile; fileRef = A79500F5161D680000358BC3 /* NSData+DTCrypto.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA07681A2B221300B61CCE /* NSData+DTCrypto.m in Sources */ = {isa = PBXBuildFile; fileRef = A79500F6161D680000358BC3 /* NSData+DTCrypto.m */; };
		A7EA07691A2B221300B61CCE /* NSDictionary+DTError.h in Headers */ = {isa = PBXBuildFile; fileRef = A7D0AA23153C1B160020F18B /* NSDictionary+DTError.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA076A1A2B221300B61CCE /* NSDictionary+DTError.m in Sources */ = {isa = PBXBuildFile; fileRef = A7D0AA24153C1B160020F18B /* NSDictionary+DTError.m */; };
		A7EA076B1A2B221300B61CCE /* NSFileWrapper+DTCopying.h in Headers */ = {isa = PBXBuildFile; fileRef = A73B6F89163169BB002CCCA7 /* NSFileWrapper+DTCopying.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA076C1A2B221300B61CCE /* NSFileWrapper+DTCopying.m in Sources */ = {isa = PBXBuildFile; fileRef = A73B6F8A163169BB002CCCA7 /* NSFileWrapper+DTCopying.m */; };
		A7EA076D1A2B221300B61CCE /* NSMutableArray+DTMoving.h in Headers */ = {isa = PBXBuildFile; fileRef = A766135E16143F8A00DF6C2B /* NSMutableArray+DTMoving.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA076E1A2B221300B61CCE /* NSMutableArray+DTMoving.m in Sources */ = {isa = PBXBuildFile; fileRef = A766135F16143F8A00DF6C2B /* NSMutableArray+DTMoving.m */; };
		A7EA076F1A2B221300B61CCE /* NSString+DTFormatNumbers.h in Headers */ = {isa = PBXBuildFile; fileRef = A70B4CCA1486621B00873A4A /* NSString+DTFormatNumbers.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA07701A2B221300B61CCE /* NSString+DTFormatNumbers.m in Sources */ = {isa = PBXBuildFile; fileRef = A70B4CCB1486621B00873A4A /* NSString+DTFormatNumbers.m */; };
		A7EA07711A2B221300B61CCE /* NSString+DTUtilities.h in Headers */ = {isa = PBXBuildFile; fileRef = A7D0AA48153C233E0020F18B /* NSString+DTUtilities.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA07721A2B221300B61CCE /* NSString+DTUtilities.m in Sources */ = {isa = PBXBuildFile; fileRef = A7D0AA49153C233E0020F18B /* NSString+DTUtilities.m */; };
		A7EA07731A2B221300B61CCE /* NSString+DTPaths.h in Headers */ = {isa = PBXBuildFile; fileRef = A7D8627F14EBF65C001436AF /* NSString+DTPaths.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA07741A2B221300B61CCE /* NSString+DTPaths.m in Sources */ = {isa = PBXBuildFile; fileRef = A7D8628014EBF65C001436AF /* NSString+DTPaths.m */; };
		A7EA07751A2B221300B61CCE /* NSString+DTURLEncoding.h in Headers */ = {isa = PBXBuildFile; fileRef = A7D0AA29153C1FE40020F18B /* NSString+DTURLEncoding.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA07761A2B221300B61CCE /* NSString+DTURLEncoding.m in Sources */ = {isa = PBXBuildFile; fileRef = A7D0AA2A153C1FE40020F18B /* NSString+DTURLEncoding.m */; };
		A7EA07771A2B221300B61CCE /* NSURL+DTUnshorten.h in Headers */ = {isa = PBXBuildFile; fileRef = A79231CC157A0B9400C3ACBB /* NSURL+DTUnshorten.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA07781A2B221300B61CCE /* NSURL+DTUnshorten.m in Sources */ = {isa = PBXBuildFile; fileRef = A79231CD157A0B9400C3ACBB /* NSURL+DTUnshorten.m */; };
		A7EA07791A2B221300B61CCE /* NSURL+DTComparing.h in Headers */ = {isa = PBXBuildFile; fileRef = A7FAA3871652291D006ED151 /* NSURL+DTComparing.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA077A1A2B221300B61CCE /* NSURL+DTComparing.m in Sources */ = {isa = PBXBuildFile; fileRef = A7FAA3881652291D006ED151 /* NSURL+DTComparing.m */; };
		A7EA077F1A2B230B00B61CCE /* DTExtendedFileAttributes.m in Sources */ = {isa = PBXBuildFile; fileRef = A7D6F2E415063448001CACDD /* DTExtendedFileAttributes.m */; };
		A7EA07821A2B230F00B61CCE /* DTAsyncFileDeleter.m in Sources */ = {isa = PBXBuildFile; fileRef = A7FA5FAC17B127E0000FC61F /* DTAsyncFileDeleter.m */; };
		A7EA07841A2B230F00B61CCE /* DTBase64Coding.m in Sources */ = {isa = PBXBuildFile; fileRef = A77D5BF916E4961A00A45C28 /* DTBase64Coding.m */; };
		A7EA07861A2B230F00B61CCE /* DTExtendedFileAttributes.m in Sources */ = {isa = PBXBuildFile; fileRef = A7D6F2E415063448001CACDD /* DTExtendedFileAttributes.m */; };
		A7EA07881A2B230F00B61CCE /* DTFolderMonitor.m in Sources */ = {isa = PBXBuildFile; fileRef = A78D661C17AFFCCC0039F5E6 /* DTFolderMonitor.m */; };
		A7EA078A1A2B230F00B61CCE /* DTFoundationConstants.m in Sources */ = {isa = PBXBuildFile; fileRef = FAB1726A16301F0D00B44EDC /* DTFoundationConstants.m */; };
		A7EA078C1A2B230F00B61CCE /* DTLog.m in Sources */ = {isa = PBXBuildFile; fileRef = A7FA5FA317B0F7B4000FC61F /* DTLog.m */; };
		A7EA078E1A2B230F00B61CCE /* DTVersion.m in Sources */ = {isa = PBXBuildFile; fileRef = A70B4CC81486621B00873A4A /* DTVersion.m */; };
		A7EA07911A2B230F00B61CCE /* DTBlockFunctions.m in Sources */ = {isa = PBXBuildFile; fileRef = A768BE3B17FC3C91008834C6 /* DTBlockFunctions.m */; };
		A7EA07921A2B232500B61CCE /* DTAsyncFileDeleter.h in Headers */ = {isa = PBXBuildFile; fileRef = A7FA5FAD17B127E0000FC61F /* DTAsyncFileDeleter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA07931A2B232500B61CCE /* DTBase64Coding.h in Headers */ = {isa = PBXBuildFile; fileRef = A77D5BF816E4961A00A45C28 /* DTBase64Coding.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA07941A2B232500B61CCE /* DTExtendedFileAttributes.h in Headers */ = {isa = PBXBuildFile; fileRef = A7D6F2E315063448001CACDD /* DTExtendedFileAttributes.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA07951A2B232500B61CCE /* DTFolderMonitor.h in Headers */ = {isa = PBXBuildFile; fileRef = A78D661B17AFFCCC0039F5E6 /* DTFolderMonitor.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA07961A2B232500B61CCE /* DTFoundationConstants.h in Headers */ = {isa = PBXBuildFile; fileRef = FAB1726916301F0D00B44EDC /* DTFoundationConstants.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA07971A2B232500B61CCE /* DTLog.h in Headers */ = {isa = PBXBuildFile; fileRef = A7FA5FA217B0F7B4000FC61F /* DTLog.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA07981A2B232500B61CCE /* DTVersion.h in Headers */ = {isa = PBXBuildFile; fileRef = A70B4CC71486621B00873A4A /* DTVersion.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA07991A2B232500B61CCE /* DTWeakSupport.h in Headers */ = {isa = PBXBuildFile; fileRef = A7FB1218175C9D5000D4B7F0 /* DTWeakSupport.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA079A1A2B232500B61CCE /* DTBlockFunctions.h in Headers */ = {isa = PBXBuildFile; fileRef = A768BE3A17FC3C91008834C6 /* DTBlockFunctions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA079B1A2B233E00B61CCE /* DTAsyncFileDeleter.m in Sources */ = {isa = PBXBuildFile; fileRef = A7FA5FAC17B127E0000FC61F /* DTAsyncFileDeleter.m */; };
		A7EA079C1A2B233E00B61CCE /* DTBase64Coding.m in Sources */ = {isa = PBXBuildFile; fileRef = A77D5BF916E4961A00A45C28 /* DTBase64Coding.m */; };
		A7EA079D1A2B233E00B61CCE /* DTExtendedFileAttributes.m in Sources */ = {isa = PBXBuildFile; fileRef = A7D6F2E415063448001CACDD /* DTExtendedFileAttributes.m */; };
		A7EA079E1A2B233E00B61CCE /* DTFolderMonitor.m in Sources */ = {isa = PBXBuildFile; fileRef = A78D661C17AFFCCC0039F5E6 /* DTFolderMonitor.m */; };
		A7EA079F1A2B233E00B61CCE /* DTFoundationConstants.m in Sources */ = {isa = PBXBuildFile; fileRef = FAB1726A16301F0D00B44EDC /* DTFoundationConstants.m */; };
		A7EA07A01A2B233E00B61CCE /* DTLog.m in Sources */ = {isa = PBXBuildFile; fileRef = A7FA5FA317B0F7B4000FC61F /* DTLog.m */; };
		A7EA07A11A2B233E00B61CCE /* DTVersion.m in Sources */ = {isa = PBXBuildFile; fileRef = A70B4CC81486621B00873A4A /* DTVersion.m */; };
		A7EA07A21A2B233E00B61CCE /* DTBlockFunctions.m in Sources */ = {isa = PBXBuildFile; fileRef = A768BE3B17FC3C91008834C6 /* DTBlockFunctions.m */; };
		A7EA07AB1A2B259300B61CCE /* DTCoreGraphicsUtils.h in Headers */ = {isa = PBXBuildFile; fileRef = A7D0AA6B153C39920020F18B /* DTCoreGraphicsUtils.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA07AC1A2B259300B61CCE /* DTCoreGraphicsUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = A7D0AA6C153C39920020F18B /* DTCoreGraphicsUtils.m */; };
		A7EA07AD1A2B25B100B61CCE /* NSObject+DTRuntime.h in Headers */ = {isa = PBXBuildFile; fileRef = A78EDD2717A65B8600480205 /* NSObject+DTRuntime.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA07AE1A2B25B900B61CCE /* NSObject+DTRuntime.m in Sources */ = {isa = PBXBuildFile; fileRef = A78EDD2817A65B8600480205 /* NSObject+DTRuntime.m */; };
		A7EA07B01A2B299D00B61CCE /* DTObjectBlockExecutor.h in Headers */ = {isa = PBXBuildFile; fileRef = A78EDD2517A65B8600480205 /* DTObjectBlockExecutor.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA07B11A2B299D00B61CCE /* DTObjectBlockExecutor.m in Sources */ = {isa = PBXBuildFile; fileRef = A78EDD2617A65B8600480205 /* DTObjectBlockExecutor.m */; };
		A7EA07CF1A2B2F6B00B61CCE /* DTFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A7EA07B81A2B2F6A00B61CCE /* DTFoundation.framework */; };
		A7EA07D01A2B2F6B00B61CCE /* DTFoundation.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = A7EA07B81A2B2F6A00B61CCE /* DTFoundation.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		A7EA07DA1A2B30F000B61CCE /* DTTiledLayerWithoutFade.h in Headers */ = {isa = PBXBuildFile; fileRef = A730BCCC16D2892E003B849F /* DTTiledLayerWithoutFade.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA07DB1A2B30F000B61CCE /* DTTiledLayerWithoutFade.m in Sources */ = {isa = PBXBuildFile; fileRef = A730BCCD16D2892E003B849F /* DTTiledLayerWithoutFade.m */; };
		A7EA07DC1A2B30F000B61CCE /* DTActivityTitleView.h in Headers */ = {isa = PBXBuildFile; fileRef = A76DB49116A5E37F0010CD85 /* DTActivityTitleView.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA07DD1A2B30F000B61CCE /* DTActivityTitleView.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB49216A5E37F0010CD85 /* DTActivityTitleView.m */; };
		A7EA07DE1A2B30F000B61CCE /* DTCustomColoredAccessory.h in Headers */ = {isa = PBXBuildFile; fileRef = A7E88ED016BC0278008CBA9C /* DTCustomColoredAccessory.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA07DF1A2B30F000B61CCE /* DTCustomColoredAccessory.m in Sources */ = {isa = PBXBuildFile; fileRef = A7E88ED116BC0278008CBA9C /* DTCustomColoredAccessory.m */; };
		A7EA07E01A2B30F000B61CCE /* DTPieProgressIndicator.h in Headers */ = {isa = PBXBuildFile; fileRef = A76DB49516A5E37F0010CD85 /* DTPieProgressIndicator.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA07E11A2B30F000B61CCE /* DTPieProgressIndicator.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB49616A5E37F0010CD85 /* DTPieProgressIndicator.m */; };
		A7EA07E21A2B30F000B61CCE /* DTSmartPagingScrollView.h in Headers */ = {isa = PBXBuildFile; fileRef = A76DB49716A5E37F0010CD85 /* DTSmartPagingScrollView.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA07E31A2B30F000B61CCE /* DTSmartPagingScrollView.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB49816A5E37F0010CD85 /* DTSmartPagingScrollView.m */; };
		A7EA07E41A2B30F000B61CCE /* UIApplication+DTNetworkActivity.h in Headers */ = {isa = PBXBuildFile; fileRef = A76DB4C216A5E50D0010CD85 /* UIApplication+DTNetworkActivity.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA07E51A2B30F000B61CCE /* UIApplication+DTNetworkActivity.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB4C316A5E50E0010CD85 /* UIApplication+DTNetworkActivity.m */; };
		A7EA07E61A2B30F000B61CCE /* UIImage+DTFoundation.h in Headers */ = {isa = PBXBuildFile; fileRef = A76DB4C416A5E50E0010CD85 /* UIImage+DTFoundation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA07E71A2B30F000B61CCE /* UIImage+DTFoundation.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB4C516A5E50E0010CD85 /* UIImage+DTFoundation.m */; };
		A7EA07E81A2B30F000B61CCE /* NSURL+DTAppLinks.h in Headers */ = {isa = PBXBuildFile; fileRef = A76DB4C016A5E50D0010CD85 /* NSURL+DTAppLinks.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA07E91A2B30F000B61CCE /* NSURL+DTAppLinks.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB4C116A5E50D0010CD85 /* NSURL+DTAppLinks.m */; };
		A7EA07EA1A2B30F000B61CCE /* UIView+DTFoundation.h in Headers */ = {isa = PBXBuildFile; fileRef = A76DB4C816A5E50F0010CD85 /* UIView+DTFoundation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA07EB1A2B30F000B61CCE /* UIView+DTFoundation.m in Sources */ = {isa = PBXBuildFile; fileRef = A76DB4C916A5E50F0010CD85 /* UIView+DTFoundation.m */; };
		A7EA07EE1A2B310600B61CCE /* DTSidePanelController.h in Headers */ = {isa = PBXBuildFile; fileRef = A74663B21743D43600D4D7D5 /* DTSidePanelController.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA07EF1A2B310600B61CCE /* DTSidePanelController.m in Sources */ = {isa = PBXBuildFile; fileRef = A74663B31743D43600D4D7D5 /* DTSidePanelController.m */; };
		A7EA07F01A2B310600B61CCE /* UIViewController+DTSidePanelController.h in Headers */ = {isa = PBXBuildFile; fileRef = A7C92B67174F9AEC0019D70A /* UIViewController+DTSidePanelController.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA07F11A2B310600B61CCE /* UIViewController+DTSidePanelController.m in Sources */ = {isa = PBXBuildFile; fileRef = A7C92B68174F9AEC0019D70A /* UIViewController+DTSidePanelController.m */; };
		A7EA07F21A2B310600B61CCE /* DTSidePanelPanGestureRecognizer.m in Sources */ = {isa = PBXBuildFile; fileRef = 355B210F4174F2B22B0CD6A0 /* DTSidePanelPanGestureRecognizer.m */; };
		A7EA07F31A2B310600B61CCE /* DTSidePanelPanGestureRecognizer.h in Headers */ = {isa = PBXBuildFile; fileRef = 355B257CC69FC64D78ABE0BE /* DTSidePanelPanGestureRecognizer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA07F41A2B310600B61CCE /* DTSidePanelControllerSegue.h in Headers */ = {isa = PBXBuildFile; fileRef = C9826821196DAD7800A84677 /* DTSidePanelControllerSegue.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA07F51A2B310600B61CCE /* DTSidePanelControllerSegue.m in Sources */ = {isa = PBXBuildFile; fileRef = C9826822196DAD7800A84677 /* DTSidePanelControllerSegue.m */; };
		A7EA07F61A2B311700B61CCE /* UIColor+DTDebug.h in Headers */ = {isa = PBXBuildFile; fileRef = A78EDD2A17A65D4B00480205 /* UIColor+DTDebug.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA07F71A2B311700B61CCE /* UIColor+DTDebug.m in Sources */ = {isa = PBXBuildFile; fileRef = A78EDD2B17A65D4B00480205 /* UIColor+DTDebug.m */; };
		A7EA07F81A2B311700B61CCE /* UIView+DTDebug.h in Headers */ = {isa = PBXBuildFile; fileRef = A78EDD2C17A65D4B00480205 /* UIView+DTDebug.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA07F91A2B311700B61CCE /* UIView+DTDebug.m in Sources */ = {isa = PBXBuildFile; fileRef = A78EDD2D17A65D4B00480205 /* UIView+DTDebug.m */; };
		A7EA07FA1A2B312300B61CCE /* DTActionSheet.h in Headers */ = {isa = PBXBuildFile; fileRef = F88FF6D41920F1AF00120808 /* DTActionSheet.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA07FB1A2B312300B61CCE /* DTActionSheet.m in Sources */ = {isa = PBXBuildFile; fileRef = F88FF6D51920F1AF00120808 /* DTActionSheet.m */; };
		A7EA07FC1A2B312300B61CCE /* DTAlertView.h in Headers */ = {isa = PBXBuildFile; fileRef = F88FF6D61920F1AF00120808 /* DTAlertView.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA07FD1A2B312300B61CCE /* DTAlertView.m in Sources */ = {isa = PBXBuildFile; fileRef = F88FF6D71920F1AF00120808 /* DTAlertView.m */; };
		A7EA07FE1A2B312300B61CCE /* UIView+DTActionHandlers.h in Headers */ = {isa = PBXBuildFile; fileRef = F88FF6D81920F1AF00120808 /* UIView+DTActionHandlers.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA07FF1A2B312300B61CCE /* UIView+DTActionHandlers.m in Sources */ = {isa = PBXBuildFile; fileRef = F88FF6D91920F1AF00120808 /* UIView+DTActionHandlers.m */; };
		A7EA08061A2B316100B61CCE /* NSArray+DTError.h in Headers */ = {isa = PBXBuildFile; fileRef = A73D5BA9155271FD0024BDB7 /* NSArray+DTError.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA08071A2B316100B61CCE /* NSArray+DTError.m in Sources */ = {isa = PBXBuildFile; fileRef = A73D5BAA155271FD0024BDB7 /* NSArray+DTError.m */; };
		A7EA08081A2B316100B61CCE /* NSData+DTCrypto.h in Headers */ = {isa = PBXBuildFile; fileRef = A79500F5161D680000358BC3 /* NSData+DTCrypto.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA08091A2B316100B61CCE /* NSData+DTCrypto.m in Sources */ = {isa = PBXBuildFile; fileRef = A79500F6161D680000358BC3 /* NSData+DTCrypto.m */; };
		A7EA080A1A2B316100B61CCE /* NSDictionary+DTError.h in Headers */ = {isa = PBXBuildFile; fileRef = A7D0AA23153C1B160020F18B /* NSDictionary+DTError.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA080B1A2B316100B61CCE /* NSDictionary+DTError.m in Sources */ = {isa = PBXBuildFile; fileRef = A7D0AA24153C1B160020F18B /* NSDictionary+DTError.m */; };
		A7EA080C1A2B316100B61CCE /* NSFileWrapper+DTCopying.h in Headers */ = {isa = PBXBuildFile; fileRef = A73B6F89163169BB002CCCA7 /* NSFileWrapper+DTCopying.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA080D1A2B316100B61CCE /* NSFileWrapper+DTCopying.m in Sources */ = {isa = PBXBuildFile; fileRef = A73B6F8A163169BB002CCCA7 /* NSFileWrapper+DTCopying.m */; };
		A7EA080E1A2B316100B61CCE /* NSMutableArray+DTMoving.h in Headers */ = {isa = PBXBuildFile; fileRef = A766135E16143F8A00DF6C2B /* NSMutableArray+DTMoving.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA080F1A2B316100B61CCE /* NSMutableArray+DTMoving.m in Sources */ = {isa = PBXBuildFile; fileRef = A766135F16143F8A00DF6C2B /* NSMutableArray+DTMoving.m */; };
		A7EA08101A2B316100B61CCE /* NSString+DTFormatNumbers.h in Headers */ = {isa = PBXBuildFile; fileRef = A70B4CCA1486621B00873A4A /* NSString+DTFormatNumbers.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA08111A2B316100B61CCE /* NSString+DTFormatNumbers.m in Sources */ = {isa = PBXBuildFile; fileRef = A70B4CCB1486621B00873A4A /* NSString+DTFormatNumbers.m */; };
		A7EA08121A2B316100B61CCE /* NSString+DTUtilities.h in Headers */ = {isa = PBXBuildFile; fileRef = A7D0AA48153C233E0020F18B /* NSString+DTUtilities.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA08131A2B316100B61CCE /* NSString+DTUtilities.m in Sources */ = {isa = PBXBuildFile; fileRef = A7D0AA49153C233E0020F18B /* NSString+DTUtilities.m */; };
		A7EA08141A2B316100B61CCE /* NSString+DTPaths.h in Headers */ = {isa = PBXBuildFile; fileRef = A7D8627F14EBF65C001436AF /* NSString+DTPaths.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA08151A2B316100B61CCE /* NSString+DTPaths.m in Sources */ = {isa = PBXBuildFile; fileRef = A7D8628014EBF65C001436AF /* NSString+DTPaths.m */; };
		A7EA08161A2B316100B61CCE /* NSString+DTURLEncoding.h in Headers */ = {isa = PBXBuildFile; fileRef = A7D0AA29153C1FE40020F18B /* NSString+DTURLEncoding.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA08171A2B316100B61CCE /* NSString+DTURLEncoding.m in Sources */ = {isa = PBXBuildFile; fileRef = A7D0AA2A153C1FE40020F18B /* NSString+DTURLEncoding.m */; };
		A7EA08181A2B316100B61CCE /* NSURL+DTUnshorten.h in Headers */ = {isa = PBXBuildFile; fileRef = A79231CC157A0B9400C3ACBB /* NSURL+DTUnshorten.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA08191A2B316100B61CCE /* NSURL+DTUnshorten.m in Sources */ = {isa = PBXBuildFile; fileRef = A79231CD157A0B9400C3ACBB /* NSURL+DTUnshorten.m */; };
		A7EA081A1A2B316100B61CCE /* NSURL+DTComparing.h in Headers */ = {isa = PBXBuildFile; fileRef = A7FAA3871652291D006ED151 /* NSURL+DTComparing.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA081B1A2B316100B61CCE /* NSURL+DTComparing.m in Sources */ = {isa = PBXBuildFile; fileRef = A7FAA3881652291D006ED151 /* NSURL+DTComparing.m */; };
		A7EA081C1A2B317300B61CCE /* DTCoreGraphicsUtils.h in Headers */ = {isa = PBXBuildFile; fileRef = A7D0AA6B153C39920020F18B /* DTCoreGraphicsUtils.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA081D1A2B317300B61CCE /* DTCoreGraphicsUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = A7D0AA6C153C39920020F18B /* DTCoreGraphicsUtils.m */; };
		A7EA081E1A2B318500B61CCE /* DTObjectBlockExecutor.h in Headers */ = {isa = PBXBuildFile; fileRef = A78EDD2517A65B8600480205 /* DTObjectBlockExecutor.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA081F1A2B318500B61CCE /* DTObjectBlockExecutor.m in Sources */ = {isa = PBXBuildFile; fileRef = A78EDD2617A65B8600480205 /* DTObjectBlockExecutor.m */; };
		A7EA08201A2B318500B61CCE /* NSObject+DTRuntime.h in Headers */ = {isa = PBXBuildFile; fileRef = A78EDD2717A65B8600480205 /* NSObject+DTRuntime.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA08211A2B318500B61CCE /* NSObject+DTRuntime.m in Sources */ = {isa = PBXBuildFile; fileRef = A78EDD2817A65B8600480205 /* NSObject+DTRuntime.m */; };
		A7EA08221A2B318E00B61CCE /* DTAsyncFileDeleter.h in Headers */ = {isa = PBXBuildFile; fileRef = A7FA5FAD17B127E0000FC61F /* DTAsyncFileDeleter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA08231A2B318E00B61CCE /* DTAsyncFileDeleter.m in Sources */ = {isa = PBXBuildFile; fileRef = A7FA5FAC17B127E0000FC61F /* DTAsyncFileDeleter.m */; };
		A7EA08241A2B318E00B61CCE /* DTBase64Coding.h in Headers */ = {isa = PBXBuildFile; fileRef = A77D5BF816E4961A00A45C28 /* DTBase64Coding.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA08251A2B318E00B61CCE /* DTBase64Coding.m in Sources */ = {isa = PBXBuildFile; fileRef = A77D5BF916E4961A00A45C28 /* DTBase64Coding.m */; };
		A7EA08261A2B318E00B61CCE /* DTExtendedFileAttributes.h in Headers */ = {isa = PBXBuildFile; fileRef = A7D6F2E315063448001CACDD /* DTExtendedFileAttributes.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA08271A2B318E00B61CCE /* DTExtendedFileAttributes.m in Sources */ = {isa = PBXBuildFile; fileRef = A7D6F2E415063448001CACDD /* DTExtendedFileAttributes.m */; };
		A7EA08281A2B318E00B61CCE /* DTFolderMonitor.h in Headers */ = {isa = PBXBuildFile; fileRef = A78D661B17AFFCCC0039F5E6 /* DTFolderMonitor.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA08291A2B318E00B61CCE /* DTFolderMonitor.m in Sources */ = {isa = PBXBuildFile; fileRef = A78D661C17AFFCCC0039F5E6 /* DTFolderMonitor.m */; };
		A7EA082A1A2B318E00B61CCE /* DTFoundationConstants.h in Headers */ = {isa = PBXBuildFile; fileRef = FAB1726916301F0D00B44EDC /* DTFoundationConstants.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA082B1A2B318E00B61CCE /* DTFoundationConstants.m in Sources */ = {isa = PBXBuildFile; fileRef = FAB1726A16301F0D00B44EDC /* DTFoundationConstants.m */; };
		A7EA082C1A2B318E00B61CCE /* DTLog.h in Headers */ = {isa = PBXBuildFile; fileRef = A7FA5FA217B0F7B4000FC61F /* DTLog.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA082D1A2B318E00B61CCE /* DTLog.m in Sources */ = {isa = PBXBuildFile; fileRef = A7FA5FA317B0F7B4000FC61F /* DTLog.m */; };
		A7EA082E1A2B318E00B61CCE /* DTVersion.h in Headers */ = {isa = PBXBuildFile; fileRef = A70B4CC71486621B00873A4A /* DTVersion.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA082F1A2B318E00B61CCE /* DTVersion.m in Sources */ = {isa = PBXBuildFile; fileRef = A70B4CC81486621B00873A4A /* DTVersion.m */; };
		A7EA08301A2B318E00B61CCE /* DTWeakSupport.h in Headers */ = {isa = PBXBuildFile; fileRef = A7FB1218175C9D5000D4B7F0 /* DTWeakSupport.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA08311A2B318E00B61CCE /* DTBlockFunctions.h in Headers */ = {isa = PBXBuildFile; fileRef = A768BE3A17FC3C91008834C6 /* DTBlockFunctions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7EA08321A2B318E00B61CCE /* DTBlockFunctions.m in Sources */ = {isa = PBXBuildFile; fileRef = A768BE3B17FC3C91008834C6 /* DTBlockFunctions.m */; };
		A7F3BD2C18436A0F00DB7854 /* DTSidePanelPanGestureRecognizer.m in Sources */ = {isa = PBXBuildFile; fileRef = 355B210F4174F2B22B0CD6A0 /* DTSidePanelPanGestureRecognizer.m */; };
		A7F3BD2D18436A1200DB7854 /* DTSidePanelController.m in Sources */ = {isa = PBXBuildFile; fileRef = A74663B31743D43600D4D7D5 /* DTSidePanelController.m */; };
		A7F8CD9C1B4BD8F7007DAD63 /* DTCoreGraphicsUtils.h in Headers */ = {isa = PBXBuildFile; fileRef = A7D0AA6B153C39920020F18B /* DTCoreGraphicsUtils.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7F8CD9D1B4BD8F7007DAD63 /* DTCoreGraphicsUtils.h in Headers */ = {isa = PBXBuildFile; fileRef = A7D0AA6B153C39920020F18B /* DTCoreGraphicsUtils.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7F8CDA41B4BE671007DAD63 /* NSURL+DTComparing.h in Headers */ = {isa = PBXBuildFile; fileRef = A7FAA3871652291D006ED151 /* NSURL+DTComparing.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7F8CDA51B4BE671007DAD63 /* NSURL+DTComparing.h in Headers */ = {isa = PBXBuildFile; fileRef = A7FAA3871652291D006ED151 /* NSURL+DTComparing.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7F8CDA61B4BE67D007DAD63 /* NSArray+DTError.h in Headers */ = {isa = PBXBuildFile; fileRef = A73D5BA9155271FD0024BDB7 /* NSArray+DTError.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7F8CDA71B4BE67D007DAD63 /* NSData+DTCrypto.h in Headers */ = {isa = PBXBuildFile; fileRef = A79500F5161D680000358BC3 /* NSData+DTCrypto.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7F8CDA81B4BE67D007DAD63 /* NSDictionary+DTError.h in Headers */ = {isa = PBXBuildFile; fileRef = A7D0AA23153C1B160020F18B /* NSDictionary+DTError.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7F8CDA91B4BE67D007DAD63 /* NSFileWrapper+DTCopying.h in Headers */ = {isa = PBXBuildFile; fileRef = A73B6F89163169BB002CCCA7 /* NSFileWrapper+DTCopying.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7F8CDAA1B4BE67D007DAD63 /* NSMutableArray+DTMoving.h in Headers */ = {isa = PBXBuildFile; fileRef = A766135E16143F8A00DF6C2B /* NSMutableArray+DTMoving.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7F8CDAB1B4BE67D007DAD63 /* NSString+DTFormatNumbers.h in Headers */ = {isa = PBXBuildFile; fileRef = A70B4CCA1486621B00873A4A /* NSString+DTFormatNumbers.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7F8CDAC1B4BE67D007DAD63 /* NSString+DTPaths.h in Headers */ = {isa = PBXBuildFile; fileRef = A7D8627F14EBF65C001436AF /* NSString+DTPaths.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7F8CDAD1B4BE67D007DAD63 /* NSString+DTURLEncoding.h in Headers */ = {isa = PBXBuildFile; fileRef = A7D0AA29153C1FE40020F18B /* NSString+DTURLEncoding.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7F8CDAE1B4BE67D007DAD63 /* NSString+DTURLEncoding.m in Sources */ = {isa = PBXBuildFile; fileRef = A7D0AA2A153C1FE40020F18B /* NSString+DTURLEncoding.m */; };
		A7F8CDAF1B4BE67D007DAD63 /* NSURL+DTUnshorten.h in Headers */ = {isa = PBXBuildFile; fileRef = A79231CC157A0B9400C3ACBB /* NSURL+DTUnshorten.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7F8CDB01B4BE67D007DAD63 /* NSURL+DTUnshorten.m in Sources */ = {isa = PBXBuildFile; fileRef = A79231CD157A0B9400C3ACBB /* NSURL+DTUnshorten.m */; };
		A7F8CDB11B4BE67E007DAD63 /* NSArray+DTError.h in Headers */ = {isa = PBXBuildFile; fileRef = A73D5BA9155271FD0024BDB7 /* NSArray+DTError.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7F8CDB21B4BE67E007DAD63 /* NSData+DTCrypto.h in Headers */ = {isa = PBXBuildFile; fileRef = A79500F5161D680000358BC3 /* NSData+DTCrypto.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7F8CDB31B4BE67E007DAD63 /* NSDictionary+DTError.h in Headers */ = {isa = PBXBuildFile; fileRef = A7D0AA23153C1B160020F18B /* NSDictionary+DTError.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7F8CDB41B4BE67E007DAD63 /* NSFileWrapper+DTCopying.h in Headers */ = {isa = PBXBuildFile; fileRef = A73B6F89163169BB002CCCA7 /* NSFileWrapper+DTCopying.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7F8CDB51B4BE67E007DAD63 /* NSMutableArray+DTMoving.h in Headers */ = {isa = PBXBuildFile; fileRef = A766135E16143F8A00DF6C2B /* NSMutableArray+DTMoving.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7F8CDB61B4BE67E007DAD63 /* NSString+DTFormatNumbers.h in Headers */ = {isa = PBXBuildFile; fileRef = A70B4CCA1486621B00873A4A /* NSString+DTFormatNumbers.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7F8CDB71B4BE67E007DAD63 /* NSString+DTPaths.h in Headers */ = {isa = PBXBuildFile; fileRef = A7D8627F14EBF65C001436AF /* NSString+DTPaths.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7F8CDB81B4BE67E007DAD63 /* NSString+DTURLEncoding.h in Headers */ = {isa = PBXBuildFile; fileRef = A7D0AA29153C1FE40020F18B /* NSString+DTURLEncoding.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7F8CDB91B4BE67E007DAD63 /* NSURL+DTUnshorten.h in Headers */ = {isa = PBXBuildFile; fileRef = A79231CC157A0B9400C3ACBB /* NSURL+DTUnshorten.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7F8CDBA1B4BE694007DAD63 /* DTScrollView.h in Headers */ = {isa = PBXBuildFile; fileRef = A76DB4B816A5E3B20010CD85 /* DTScrollView.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7F8CDBB1B4BE694007DAD63 /* NSDocument+DTFoundation.h in Headers */ = {isa = PBXBuildFile; fileRef = A76DB4E416A5E5590010CD85 /* NSDocument+DTFoundation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7F8CDBC1B4BE694007DAD63 /* NSImage+DTUtilities.h in Headers */ = {isa = PBXBuildFile; fileRef = A76DB4E616A5E5590010CD85 /* NSImage+DTUtilities.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7F8CDBD1B4BE694007DAD63 /* NSValue+DTConversion.h in Headers */ = {isa = PBXBuildFile; fileRef = A76DB4E816A5E5590010CD85 /* NSValue+DTConversion.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7F8CDBE1B4BE694007DAD63 /* NSView+DTAutoLayout.h in Headers */ = {isa = PBXBuildFile; fileRef = A76DB4EA16A5E5590010CD85 /* NSView+DTAutoLayout.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7F8CDBF1B4BE694007DAD63 /* NSWindowController+DTPanelControllerPresenting.h in Headers */ = {isa = PBXBuildFile; fileRef = A76DB4EC16A5E5590010CD85 /* NSWindowController+DTPanelControllerPresenting.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7FA5FA917B0F7B4000FC61F /* DTLog.m in Sources */ = {isa = PBXBuildFile; fileRef = A7FA5FA317B0F7B4000FC61F /* DTLog.m */; };
		A7FA5FB017B127E0000FC61F /* DTAsyncFileDeleter.m in Sources */ = {isa = PBXBuildFile; fileRef = A7FA5FAC17B127E0000FC61F /* DTAsyncFileDeleter.m */; };
		A7FAA38B1652291D006ED151 /* NSURL+DTComparing.m in Sources */ = {isa = PBXBuildFile; fileRef = A7FAA3881652291D006ED151 /* NSURL+DTComparing.m */; };
		A7FAA38C1652291D006ED151 /* NSURL+DTComparing.m in Sources */ = {isa = PBXBuildFile; fileRef = A7FAA3881652291D006ED151 /* NSURL+DTComparing.m */; };
		A7FB1216175C9C8B00D4B7F0 /* DTSidePanelController.h in Headers */ = {isa = PBXBuildFile; fileRef = A74663B21743D43600D4D7D5 /* DTSidePanelController.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A8C4A7791F949E5100FC611D /* UIScreen+DTFoundation.m in Sources */ = {isa = PBXBuildFile; fileRef = A8C4A7781F949D5500FC611D /* UIScreen+DTFoundation.m */; };
		A8C4A77D1F949F6900FC611D /* UIScreen+DTFoundation.m in Sources */ = {isa = PBXBuildFile; fileRef = A8C4A7781F949D5500FC611D /* UIScreen+DTFoundation.m */; };
		C0494033163C863A470098D8 /* DTZipArchiveNode.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = C0494CD06FFAB6AEAC8ADAD4 /* DTZipArchiveNode.h */; };
		C04941BC829C1022DF240F4C /* DTZipArchiveNode.m in Sources */ = {isa = PBXBuildFile; fileRef = C0494FCC2C675F2CDF315027 /* DTZipArchiveNode.m */; };
		C049420F39CD4294FCE0F549 /* DTZipArchiveGZip.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = C04943358446F1A9BA86F458 /* DTZipArchiveGZip.h */; };
		C04946681218C94801D2AB78 /* DTZipArchivePKZip.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = C0494E7390203BE4105C5070 /* DTZipArchivePKZip.h */; };
		C04948B5DABE59E8CC2340AF /* DTZipArchiveGZip.m in Sources */ = {isa = PBXBuildFile; fileRef = C0494E1FBE9D6D4FB042F2A3 /* DTZipArchiveGZip.m */; };
		C0494DA5BB16F9A0E8533152 /* DTZipArchivePKZip.m in Sources */ = {isa = PBXBuildFile; fileRef = C0494776E02407791E96C641 /* DTZipArchivePKZip.m */; };
		C9826825196DAD8600A84677 /* DTSidePanelControllerSegue.m in Sources */ = {isa = PBXBuildFile; fileRef = C9826822196DAD7800A84677 /* DTSidePanelControllerSegue.m */; };
		E2262E82163846AB00BFDAD7 /* DTFoundation.h in Headers */ = {isa = PBXBuildFile; fileRef = A70B4CC51486621B00873A4A /* DTFoundation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F88FF6851920A9DF00120808 /* DTProgressHUD.m in Sources */ = {isa = PBXBuildFile; fileRef = F88FF6841920A9DF00120808 /* DTProgressHUD.m */; };
		F88FF6C61920AC1900120808 /* libDTProgressHUD_iOS.a in Frameworks */ = {isa = PBXBuildFile; fileRef = F88FF6801920A83300120808 /* libDTProgressHUD_iOS.a */; };
		F88FF6C71920AE7300120808 /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A7917CB5191A3CD500964D63 /* UIKit.framework */; };
		F88FF6C91920AE9700120808 /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F88FF6C81920AE9700120808 /* CoreGraphics.framework */; };
		F88FF6CC1920AF2900120808 /* libDTFoundation.a in Frameworks */ = {isa = PBXBuildFile; fileRef = A70B4CE71486637E00873A4A /* libDTFoundation.a */; };
		F88FF6CF1920B0CC00120808 /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F88FF6CE1920B0CC00120808 /* QuartzCore.framework */; };
		F88FF6D21920BF5200120808 /* DTProgressHUDWindow.m in Sources */ = {isa = PBXBuildFile; fileRef = F88FF6D11920BF5200120808 /* DTProgressHUDWindow.m */; };
		F88FF6DB1920F1AF00120808 /* DTActionSheet.m in Sources */ = {isa = PBXBuildFile; fileRef = F88FF6D51920F1AF00120808 /* DTActionSheet.m */; };
		F88FF6DD1920F1AF00120808 /* DTAlertView.m in Sources */ = {isa = PBXBuildFile; fileRef = F88FF6D71920F1AF00120808 /* DTAlertView.m */; };
		F88FF6DF1920F1AF00120808 /* UIView+DTActionHandlers.m in Sources */ = {isa = PBXBuildFile; fileRef = F88FF6D91920F1AF00120808 /* UIView+DTActionHandlers.m */; };
		FA2A29291609F7330023CDD7 /* DTVersion.m in Sources */ = {isa = PBXBuildFile; fileRef = A70B4CC81486621B00873A4A /* DTVersion.m */; };
		FA9CB81817ABDF1500A596C5 /* DTASN1BitString.m in Sources */ = {isa = PBXBuildFile; fileRef = A78EDD1F17A65AD800480205 /* DTASN1BitString.m */; };
		FA9CB81917ABDF1500A596C5 /* DTASN1Parser.m in Sources */ = {isa = PBXBuildFile; fileRef = A78EDD2117A65AD800480205 /* DTASN1Parser.m */; };
		FA9CB81A17ABDF1500A596C5 /* DTASN1Serialization.m in Sources */ = {isa = PBXBuildFile; fileRef = A78EDD2317A65AD800480205 /* DTASN1Serialization.m */; };
		FAB1727116301F0D00B44EDC /* DTFoundationConstants.m in Sources */ = {isa = PBXBuildFile; fileRef = FAB1726A16301F0D00B44EDC /* DTFoundationConstants.m */; };
		FAC284D519B84B5F0010C385 /* libDTFoundation.a in Frameworks */ = {isa = PBXBuildFile; fileRef = A70B4CE71486637E00873A4A /* libDTFoundation.a */; };
		FAC284D619B84B5F0010C385 /* QuickLook.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FAF37A6F17AFE098009AC27C /* QuickLook.framework */; };
		FAC284D919B84B5F0010C385 /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A7F4DFF8147FD08900F4059A /* UIKit.framework */; };
		FAC284DA19B84B5F0010C385 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A7F4DFFA147FD08F00F4059A /* Foundation.framework */; };
		FAC284DB19B84B5F0010C385 /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FAE5B9311610A38200CA0D99 /* CoreGraphics.framework */; };
		FAC284DC19B84B5F0010C385 /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FAB1725D163004FF00B44EDC /* QuartzCore.framework */; };
		FAF37A2117AFDD93009AC27C /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A7F4DFF8147FD08900F4059A /* UIKit.framework */; };
		FAF37A2217AFDD93009AC27C /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A7F4DFFA147FD08F00F4059A /* Foundation.framework */; };
		FAF37A2317AFDD93009AC27C /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FAE5B9311610A38200CA0D99 /* CoreGraphics.framework */; };
		FAF37A5A17AFDF63009AC27C /* libDTZipArchive_iOS.a in Frameworks */ = {isa = PBXBuildFile; fileRef = A7E383DA160DFF8600CF72D6 /* libDTZipArchive_iOS.a */; };
		FAF37A6D17AFE06B009AC27C /* libz.1.2.5.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = FAF37A6C17AFE06B009AC27C /* libz.1.2.5.dylib */; };
		FAF37A7017AFE098009AC27C /* QuickLook.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FAF37A6F17AFE098009AC27C /* QuickLook.framework */; };
		FAF37A7317AFE0D1009AC27C /* libDTFoundation.a in Frameworks */ = {isa = PBXBuildFile; fileRef = A70B4CE71486637E00873A4A /* libDTFoundation.a */; };
		FAF37A7417AFE11D009AC27C /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FAB1725D163004FF00B44EDC /* QuartzCore.framework */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		A70D74EF1743AFD700E6E626 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = A7F4DF9A147FB61500F4059A /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = A70B4CE61486637E00873A4A;
			remoteInfo = "Static Library";
		};
		A74A764E17BBB15D007073C8 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = A7F4DF9A147FB61500F4059A /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = A70B4CE61486637E00873A4A;
			remoteInfo = "Static Library";
		};
		A7AC21CA196417CC0009E1B9 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = A7F4DF9A147FB61500F4059A /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = A7AC21AE196417830009E1B9;
			remoteInfo = "DTAnimatedGIF (iOS)";
		};
		A7D9D6601758CA5400C29B6F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = A7F4DF9A147FB61500F4059A /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = A72121D9173BFFB1003C6F0A;
			remoteInfo = "DTReachability (iOS)";
		};
		A7EA07CD1A2B2F6B00B61CCE /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = A7F4DF9A147FB61500F4059A /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = A7EA07B71A2B2F6A00B61CCE;
			remoteInfo = DTFoundation_;
		};
		F88FF6C41920ABD900120808 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = A7F4DF9A147FB61500F4059A /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = F88FF6771920A83300120808;
			remoteInfo = "DTProgressHUD (iOS)";
		};
		F88FF6CA1920AF1F00120808 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = A7F4DF9A147FB61500F4059A /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = A70B4CE61486637E00873A4A;
			remoteInfo = "Static Library";
		};
		FAC284C919B84B5F0010C385 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = A7F4DF9A147FB61500F4059A /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = A70B4CE61486637E00873A4A;
			remoteInfo = "Static Library";
		};
		FAC284CB19B84B5F0010C385 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = A7F4DF9A147FB61500F4059A /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = A7E383D9160DFF8600CF72D6;
			remoteInfo = "DTZipArchive (iOS)";
		};
		FAF37A5817AFDF5D009AC27C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = A7F4DF9A147FB61500F4059A /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = A7E383D9160DFF8600CF72D6;
			remoteInfo = "DTZipArchive (iOS)";
		};
		FAF37A7117AFE0CC009AC27C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = A7F4DF9A147FB61500F4059A /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = A70B4CE61486637E00873A4A;
			remoteInfo = "Static Library";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		3D7CE543166613B60028D339 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/${PRODUCT_NAME}";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A72121D8173BFFB1003C6F0A /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/${PRODUCT_NAME}";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A78220B8168060CA005B602D /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/${PRODUCT_NAME}";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A79D999A1792D1F50082BC06 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A7AC21AD196417830009E1B9 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A7C92B78174FB8900019D70A /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/${PRODUCT_NAME}";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A7D8C7BB1723174600025675 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/${PRODUCT_NAME}";
			dstSubfolderSpec = 16;
			files = (
				A7D8C7BC1723174600025675 /* DTZipArchiveNode.h in CopyFiles */,
				A7D8C7BD1723174600025675 /* DTZipArchivePKZip.h in CopyFiles */,
				A7D8C7BE1723174600025675 /* DTZipArchiveGZip.h in CopyFiles */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A7E383C0160DFEDB00CF72D6 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/${PRODUCT_NAME}";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A7E383D8160DFF8600CF72D6 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/${PRODUCT_NAME}";
			dstSubfolderSpec = 16;
			files = (
				C0494033163C863A470098D8 /* DTZipArchiveNode.h in CopyFiles */,
				C04946681218C94801D2AB78 /* DTZipArchivePKZip.h in CopyFiles */,
				C049420F39CD4294FCE0F549 /* DTZipArchiveGZip.h in CopyFiles */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A7EA07D51A2B2F6B00B61CCE /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				A7EA07D01A2B2F6B00B61CCE /* DTFoundation.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F88FF67B1920A83300120808 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/${PRODUCT_NAME}";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FA9CB80A17ABDEF200A596C5 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/${PRODUCT_NAME}";
			dstSubfolderSpec = 16;
			files = (
				7DBE5BE11859D2E300679AF8 /* DTASN1BitString.h in CopyFiles */,
				7DBE5BE21859D2E500679AF8 /* DTASN1Parser.h in CopyFiles */,
				7DBE5BE31859D2E800679AF8 /* DTASN1Serialization.h in CopyFiles */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		29DA9D801C6DC25300F5F22A /* SystemConfiguration.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SystemConfiguration.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS9.2.sdk/System/Library/Frameworks/SystemConfiguration.framework; sourceTree = DEVELOPER_DIR; };
		29DA9D831C6DC26000F5F22A /* SystemConfiguration.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SystemConfiguration.framework; path = Platforms/AppleTVOS.platform/Developer/SDKs/AppleTVOS9.1.sdk/System/Library/Frameworks/SystemConfiguration.framework; sourceTree = DEVELOPER_DIR; };
		355B210F4174F2B22B0CD6A0 /* DTSidePanelPanGestureRecognizer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DTSidePanelPanGestureRecognizer.m; sourceTree = "<group>"; };
		355B257CC69FC64D78ABE0BE /* DTSidePanelPanGestureRecognizer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DTSidePanelPanGestureRecognizer.h; sourceTree = "<group>"; };
		384470EB1BA3330F0037C68D /* libStatic Library (tvOS).a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libStatic Library (tvOS).a"; sourceTree = BUILT_PRODUCTS_DIR; };
		384471551BA33ED80037C68D /* DTFoundation.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = DTFoundation.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		3D7CE547166613B60028D339 /* libDTHTMLParser_Mac.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libDTHTMLParser_Mac.a; sourceTree = BUILT_PRODUCTS_DIR; };
		A704BEB91B6176D10067BF7E /* AppKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AppKit.framework; path = System/Library/Frameworks/AppKit.framework; sourceTree = SDKROOT; };
		A70B4CC51486621B00873A4A /* DTFoundation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DTFoundation.h; sourceTree = "<group>"; };
		A70B4CC71486621B00873A4A /* DTVersion.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DTVersion.h; sourceTree = "<group>"; };
		A70B4CC81486621B00873A4A /* DTVersion.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DTVersion.m; sourceTree = "<group>"; };
		A70B4CCA1486621B00873A4A /* NSString+DTFormatNumbers.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSString+DTFormatNumbers.h"; sourceTree = "<group>"; };
		A70B4CCB1486621B00873A4A /* NSString+DTFormatNumbers.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSString+DTFormatNumbers.m"; sourceTree = "<group>"; };
		A70B4CDF148662B000873A4A /* DTFoundation-Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = "DTFoundation-Info.plist"; sourceTree = "<group>"; };
		A70B4CE0148662B000873A4A /* DTFoundation-Prefix.pch */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "DTFoundation-Prefix.pch"; sourceTree = "<group>"; };
		A70B4CE71486637E00873A4A /* libDTFoundation.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libDTFoundation.a; sourceTree = BUILT_PRODUCTS_DIR; };
		A70D74D01743AFBC00E6E626 /* DTSidePanels Demo.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "DTSidePanels Demo.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		A710A5001607556000437D36 /* libDTFoundation_Mac.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libDTFoundation_Mac.a; sourceTree = BUILT_PRODUCTS_DIR; };
		A72121D4173BFF89003C6F0A /* DTReachability.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DTReachability.h; sourceTree = "<group>"; };
		A72121D5173BFF89003C6F0A /* DTReachability.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DTReachability.m; sourceTree = "<group>"; };
		A72121DA173BFFB1003C6F0A /* libDTReachability_iOS.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libDTReachability_iOS.a; sourceTree = BUILT_PRODUCTS_DIR; };
		A72121EA173BFFED003C6F0A /* libDTReachability_Mac.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libDTReachability_Mac.a; sourceTree = BUILT_PRODUCTS_DIR; };
		A7239FFC16A6CF0B0071E902 /* CoreData.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreData.framework; path = System/Library/Frameworks/CoreData.framework; sourceTree = SDKROOT; };
		A730BCCC16D2892E003B849F /* DTTiledLayerWithoutFade.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DTTiledLayerWithoutFade.h; sourceTree = "<group>"; };
		A730BCCD16D2892E003B849F /* DTTiledLayerWithoutFade.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DTTiledLayerWithoutFade.m; sourceTree = "<group>"; };
		A73B6F89163169BB002CCCA7 /* NSFileWrapper+DTCopying.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSFileWrapper+DTCopying.h"; sourceTree = "<group>"; };
		A73B6F8A163169BB002CCCA7 /* NSFileWrapper+DTCopying.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSFileWrapper+DTCopying.m"; sourceTree = "<group>"; };
		A73D5BA9155271FD0024BDB7 /* NSArray+DTError.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSArray+DTError.h"; sourceTree = "<group>"; };
		A73D5BAA155271FD0024BDB7 /* NSArray+DTError.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSArray+DTError.m"; sourceTree = "<group>"; };
		A74549681B6A3F26004B0CA7 /* DTCompatibility.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DTCompatibility.h; sourceTree = "<group>"; };
		A74663B21743D43600D4D7D5 /* DTSidePanelController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DTSidePanelController.h; sourceTree = "<group>"; };
		A74663B31743D43600D4D7D5 /* DTSidePanelController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DTSidePanelController.m; sourceTree = "<group>"; };
		A74A7A841B61712B004163BE /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS8.4.sdk/System/Library/Frameworks/UIKit.framework; sourceTree = DEVELOPER_DIR; };
		A74A7A871B61713A004163BE /* QuartzCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = QuartzCore.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS8.4.sdk/System/Library/Frameworks/QuartzCore.framework; sourceTree = DEVELOPER_DIR; };
		A74A7A891B617158004163BE /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS8.4.sdk/System/Library/Frameworks/Foundation.framework; sourceTree = DEVELOPER_DIR; };
		A74A7A8B1B61716E004163BE /* CoreGraphics.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreGraphics.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS8.4.sdk/System/Library/Frameworks/CoreGraphics.framework; sourceTree = DEVELOPER_DIR; };
		A74A7A8D1B617186004163BE /* ImageIO.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = ImageIO.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS8.4.sdk/System/Library/Frameworks/ImageIO.framework; sourceTree = DEVELOPER_DIR; };
		A766135E16143F8A00DF6C2B /* NSMutableArray+DTMoving.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSMutableArray+DTMoving.h"; sourceTree = "<group>"; };
		A766135F16143F8A00DF6C2B /* NSMutableArray+DTMoving.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSMutableArray+DTMoving.m"; sourceTree = "<group>"; };
		A768BE3A17FC3C91008834C6 /* DTBlockFunctions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DTBlockFunctions.h; sourceTree = "<group>"; };
		A768BE3B17FC3C91008834C6 /* DTBlockFunctions.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DTBlockFunctions.m; sourceTree = "<group>"; };
		A76DB48916A5E1D20010CD85 /* DTHTMLParser.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DTHTMLParser.h; sourceTree = "<group>"; };
		A76DB48A16A5E1D20010CD85 /* DTHTMLParser.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DTHTMLParser.m; sourceTree = "<group>"; };
		A76DB49116A5E37F0010CD85 /* DTActivityTitleView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DTActivityTitleView.h; sourceTree = "<group>"; };
		A76DB49216A5E37F0010CD85 /* DTActivityTitleView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DTActivityTitleView.m; sourceTree = "<group>"; };
		A76DB49516A5E37F0010CD85 /* DTPieProgressIndicator.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DTPieProgressIndicator.h; sourceTree = "<group>"; };
		A76DB49616A5E37F0010CD85 /* DTPieProgressIndicator.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DTPieProgressIndicator.m; sourceTree = "<group>"; };
		A76DB49716A5E37F0010CD85 /* DTSmartPagingScrollView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DTSmartPagingScrollView.h; sourceTree = "<group>"; };
		A76DB49816A5E37F0010CD85 /* DTSmartPagingScrollView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DTSmartPagingScrollView.m; sourceTree = "<group>"; };
		A76DB4B816A5E3B20010CD85 /* DTScrollView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DTScrollView.h; sourceTree = "<group>"; };
		A76DB4B916A5E3B20010CD85 /* DTScrollView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DTScrollView.m; sourceTree = "<group>"; };
		A76DB4BD16A5E4770010CD85 /* DTZipArchive.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DTZipArchive.h; sourceTree = "<group>"; };
		A76DB4BE16A5E4770010CD85 /* DTZipArchive.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DTZipArchive.m; sourceTree = "<group>"; };
		A76DB4C016A5E50D0010CD85 /* NSURL+DTAppLinks.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSURL+DTAppLinks.h"; sourceTree = "<group>"; };
		A76DB4C116A5E50D0010CD85 /* NSURL+DTAppLinks.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSURL+DTAppLinks.m"; sourceTree = "<group>"; };
		A76DB4C216A5E50D0010CD85 /* UIApplication+DTNetworkActivity.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIApplication+DTNetworkActivity.h"; sourceTree = "<group>"; };
		A76DB4C316A5E50E0010CD85 /* UIApplication+DTNetworkActivity.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIApplication+DTNetworkActivity.m"; sourceTree = "<group>"; };
		A76DB4C416A5E50E0010CD85 /* UIImage+DTFoundation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIImage+DTFoundation.h"; sourceTree = "<group>"; };
		A76DB4C516A5E50E0010CD85 /* UIImage+DTFoundation.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIImage+DTFoundation.m"; sourceTree = "<group>"; };
		A76DB4C816A5E50F0010CD85 /* UIView+DTFoundation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIView+DTFoundation.h"; sourceTree = "<group>"; };
		A76DB4C916A5E50F0010CD85 /* UIView+DTFoundation.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIView+DTFoundation.m"; sourceTree = "<group>"; };
		A76DB4E416A5E5590010CD85 /* NSDocument+DTFoundation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSDocument+DTFoundation.h"; sourceTree = "<group>"; };
		A76DB4E516A5E5590010CD85 /* NSDocument+DTFoundation.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSDocument+DTFoundation.m"; sourceTree = "<group>"; };
		A76DB4E616A5E5590010CD85 /* NSImage+DTUtilities.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSImage+DTUtilities.h"; sourceTree = "<group>"; };
		A76DB4E716A5E5590010CD85 /* NSImage+DTUtilities.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSImage+DTUtilities.m"; sourceTree = "<group>"; };
		A76DB4E816A5E5590010CD85 /* NSValue+DTConversion.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSValue+DTConversion.h"; sourceTree = "<group>"; };
		A76DB4E916A5E5590010CD85 /* NSValue+DTConversion.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSValue+DTConversion.m"; sourceTree = "<group>"; };
		A76DB4EA16A5E5590010CD85 /* NSView+DTAutoLayout.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSView+DTAutoLayout.h"; sourceTree = "<group>"; };
		A76DB4EB16A5E5590010CD85 /* NSView+DTAutoLayout.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSView+DTAutoLayout.m"; sourceTree = "<group>"; };
		A76DB4EC16A5E5590010CD85 /* NSWindowController+DTPanelControllerPresenting.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSWindowController+DTPanelControllerPresenting.h"; sourceTree = "<group>"; };
		A76DB4ED16A5E5590010CD85 /* NSWindowController+DTPanelControllerPresenting.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSWindowController+DTPanelControllerPresenting.m"; sourceTree = "<group>"; };
		A76DB4F916A5E5950010CD85 /* NSString+DTUTI.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSString+DTUTI.h"; sourceTree = "<group>"; };
		A76DB4FA16A5E5950010CD85 /* NSString+DTUTI.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSString+DTUTI.m"; sourceTree = "<group>"; };
		A77D5BF816E4961A00A45C28 /* DTBase64Coding.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DTBase64Coding.h; sourceTree = "<group>"; };
		A77D5BF916E4961A00A45C28 /* DTBase64Coding.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DTBase64Coding.m; sourceTree = "<group>"; };
		A77DD40D14E825FC00F34B03 /* crypt.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = crypt.h; sourceTree = "<group>"; };
		A77DD40E14E825FC00F34B03 /* ioapi.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = ioapi.c; sourceTree = "<group>"; };
		A77DD40F14E825FC00F34B03 /* ioapi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ioapi.h; sourceTree = "<group>"; };
		A77DD41014E825FC00F34B03 /* mztools.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = mztools.c; sourceTree = "<group>"; };
		A77DD41114E825FC00F34B03 /* mztools.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = mztools.h; sourceTree = "<group>"; };
		A77DD41214E825FC00F34B03 /* unzip.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = unzip.c; sourceTree = "<group>"; };
		A77DD41314E825FC00F34B03 /* unzip.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = unzip.h; sourceTree = "<group>"; };
		A77DD41414E825FC00F34B03 /* zip.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = zip.c; sourceTree = "<group>"; };
		A77DD41514E825FC00F34B03 /* zip.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = zip.h; sourceTree = "<group>"; };
		A78220BA168060CA005B602D /* libDTUTI_iOS.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libDTUTI_iOS.a; sourceTree = BUILT_PRODUCTS_DIR; };
		A78381C31963F8D700AF09D3 /* DTAnimatedGIF Demo.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "DTAnimatedGIF Demo.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		A78D661B17AFFCCC0039F5E6 /* DTFolderMonitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DTFolderMonitor.h; sourceTree = "<group>"; };
		A78D661C17AFFCCC0039F5E6 /* DTFolderMonitor.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DTFolderMonitor.m; sourceTree = "<group>"; };
		A78EDD1717A65AD700480205 /* DTScriptExpression.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DTScriptExpression.h; sourceTree = "<group>"; };
		A78EDD1817A65AD700480205 /* DTScriptExpression.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DTScriptExpression.m; sourceTree = "<group>"; };
		A78EDD1917A65AD700480205 /* DTScriptVariable.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DTScriptVariable.h; sourceTree = "<group>"; };
		A78EDD1A17A65AD700480205 /* DTScriptVariable.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DTScriptVariable.m; sourceTree = "<group>"; };
		A78EDD1B17A65AD800480205 /* NSScanner+DTScripting.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSScanner+DTScripting.h"; sourceTree = "<group>"; };
		A78EDD1C17A65AD800480205 /* NSScanner+DTScripting.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSScanner+DTScripting.m"; sourceTree = "<group>"; };
		A78EDD1E17A65AD800480205 /* DTASN1BitString.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DTASN1BitString.h; sourceTree = "<group>"; };
		A78EDD1F17A65AD800480205 /* DTASN1BitString.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DTASN1BitString.m; sourceTree = "<group>"; };
		A78EDD2017A65AD800480205 /* DTASN1Parser.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DTASN1Parser.h; sourceTree = "<group>"; };
		A78EDD2117A65AD800480205 /* DTASN1Parser.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DTASN1Parser.m; sourceTree = "<group>"; };
		A78EDD2217A65AD800480205 /* DTASN1Serialization.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DTASN1Serialization.h; sourceTree = "<group>"; };
		A78EDD2317A65AD800480205 /* DTASN1Serialization.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DTASN1Serialization.m; sourceTree = "<group>"; };
		A78EDD2517A65B8600480205 /* DTObjectBlockExecutor.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DTObjectBlockExecutor.h; sourceTree = "<group>"; };
		A78EDD2617A65B8600480205 /* DTObjectBlockExecutor.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DTObjectBlockExecutor.m; sourceTree = "<group>"; };
		A78EDD2717A65B8600480205 /* NSObject+DTRuntime.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSObject+DTRuntime.h"; sourceTree = "<group>"; };
		A78EDD2817A65B8600480205 /* NSObject+DTRuntime.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSObject+DTRuntime.m"; sourceTree = "<group>"; };
		A78EDD2A17A65D4B00480205 /* UIColor+DTDebug.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIColor+DTDebug.h"; sourceTree = "<group>"; };
		A78EDD2B17A65D4B00480205 /* UIColor+DTDebug.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIColor+DTDebug.m"; sourceTree = "<group>"; };
		A78EDD2C17A65D4B00480205 /* UIView+DTDebug.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIView+DTDebug.h"; sourceTree = "<group>"; };
		A78EDD2D17A65D4B00480205 /* UIView+DTDebug.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIView+DTDebug.m"; sourceTree = "<group>"; };
		A7917C77191A312600964D63 /* Unit Tests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "Unit Tests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
		A7917C9D191A322F00964D63 /* libz.1.2.5.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libz.1.2.5.dylib; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS7.1.sdk/usr/lib/libz.1.2.5.dylib; sourceTree = DEVELOPER_DIR; };
		A7917CA2191A327A00964D63 /* libxml2.2.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libxml2.2.dylib; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS7.1.sdk/usr/lib/libxml2.2.dylib; sourceTree = DEVELOPER_DIR; };
		A7917CB5191A3CD500964D63 /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS7.1.sdk/System/Library/Frameworks/UIKit.framework; sourceTree = DEVELOPER_DIR; };
		A79231CC157A0B9400C3ACBB /* NSURL+DTUnshorten.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSURL+DTUnshorten.h"; sourceTree = "<group>"; };
		A79231CD157A0B9400C3ACBB /* NSURL+DTUnshorten.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSURL+DTUnshorten.m"; sourceTree = "<group>"; };
		A79500F5161D680000358BC3 /* NSData+DTCrypto.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSData+DTCrypto.h"; sourceTree = "<group>"; };
		A79500F6161D680000358BC3 /* NSData+DTCrypto.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSData+DTCrypto.m"; sourceTree = "<group>"; };
		A79BA6C61B46A3CA0086C2F6 /* libxml2.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libxml2.dylib; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS8.4.sdk/usr/lib/libxml2.dylib; sourceTree = DEVELOPER_DIR; };
		A79D999C1792D1F50082BC06 /* libDTAWS.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libDTAWS.a; sourceTree = BUILT_PRODUCTS_DIR; };
		A79D99AA1792D1F50082BC06 /* XCTest.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = XCTest.framework; path = Library/Frameworks/XCTest.framework; sourceTree = DEVELOPER_DIR; };
		A79D99C01792D2490082BC06 /* NSURL+DTAWS.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSURL+DTAWS.h"; sourceTree = "<group>"; };
		A79D99C11792D2490082BC06 /* NSURL+DTAWS.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSURL+DTAWS.m"; sourceTree = "<group>"; };
		A7A5F0AA1A2FA92400DA3B62 /* Security.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Security.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS8.1.sdk/System/Library/Frameworks/Security.framework; sourceTree = DEVELOPER_DIR; };
		A7AC21A7196409560009E1B9 /* DTAnimatedGIF.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DTAnimatedGIF.h; sourceTree = "<group>"; };
		A7AC21A8196409560009E1B9 /* DTAnimatedGIF.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DTAnimatedGIF.m; sourceTree = "<group>"; };
		A7AC21AF196417830009E1B9 /* libDTAnimatedGIF.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libDTAnimatedGIF.a; sourceTree = BUILT_PRODUCTS_DIR; };
		A7AC21CD1964184F0009E1B9 /* ImageIO.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = ImageIO.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS8.0.sdk/System/Library/Frameworks/ImageIO.framework; sourceTree = DEVELOPER_DIR; };
		A7C619EF162D8D84002C0C1A /* MobileCoreServices.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MobileCoreServices.framework; path = System/Library/Frameworks/MobileCoreServices.framework; sourceTree = SDKROOT; };
		A7C92B67174F9AEC0019D70A /* UIViewController+DTSidePanelController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIViewController+DTSidePanelController.h"; sourceTree = "<group>"; };
		A7C92B68174F9AEC0019D70A /* UIViewController+DTSidePanelController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIViewController+DTSidePanelController.m"; sourceTree = "<group>"; };
		A7C92B72174FB81C0019D70A /* DTSQLiteDatabase.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DTSQLiteDatabase.h; sourceTree = "<group>"; };
		A7C92B73174FB81C0019D70A /* DTSQLiteDatabase.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DTSQLiteDatabase.m; sourceTree = "<group>"; };
		A7C92B74174FB81C0019D70A /* DTSQLiteFunctions.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DTSQLiteFunctions.h; sourceTree = "<group>"; };
		A7C92B75174FB81C0019D70A /* DTSQLiteFunctions.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DTSQLiteFunctions.m; sourceTree = "<group>"; };
		A7C92B7A174FB8900019D70A /* libDTSQLite_iOS.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libDTSQLite_iOS.a; sourceTree = BUILT_PRODUCTS_DIR; };
		A7C92B8C174FB9720019D70A /* libDTSQLite_Mac.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libDTSQLite_Mac.a; sourceTree = BUILT_PRODUCTS_DIR; };
		A7D0AA23153C1B160020F18B /* NSDictionary+DTError.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSDictionary+DTError.h"; sourceTree = "<group>"; };
		A7D0AA24153C1B160020F18B /* NSDictionary+DTError.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSDictionary+DTError.m"; sourceTree = "<group>"; };
		A7D0AA29153C1FE40020F18B /* NSString+DTURLEncoding.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSString+DTURLEncoding.h"; sourceTree = "<group>"; };
		A7D0AA2A153C1FE40020F18B /* NSString+DTURLEncoding.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSString+DTURLEncoding.m"; sourceTree = "<group>"; };
		A7D0AA48153C233E0020F18B /* NSString+DTUtilities.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSString+DTUtilities.h"; sourceTree = "<group>"; };
		A7D0AA49153C233E0020F18B /* NSString+DTUtilities.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSString+DTUtilities.m"; sourceTree = "<group>"; };
		A7D0AA6B153C39920020F18B /* DTCoreGraphicsUtils.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DTCoreGraphicsUtils.h; sourceTree = "<group>"; };
		A7D0AA6C153C39920020F18B /* DTCoreGraphicsUtils.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DTCoreGraphicsUtils.m; sourceTree = "<group>"; };
		A7D60FD015D3B0BC00AEDD1B /* SenTestingKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SenTestingKit.framework; path = Library/Frameworks/SenTestingKit.framework; sourceTree = DEVELOPER_DIR; };
		A7D60FF715D3B28B00AEDD1B /* libxml2.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libxml2.dylib; path = usr/lib/libxml2.dylib; sourceTree = SDKROOT; };
		A7D6F2E315063448001CACDD /* DTExtendedFileAttributes.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DTExtendedFileAttributes.h; sourceTree = "<group>"; };
		A7D6F2E415063448001CACDD /* DTExtendedFileAttributes.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DTExtendedFileAttributes.m; sourceTree = "<group>"; };
		A7D8627F14EBF65C001436AF /* NSString+DTPaths.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSString+DTPaths.h"; sourceTree = "<group>"; };
		A7D8628014EBF65C001436AF /* NSString+DTPaths.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSString+DTPaths.m"; sourceTree = "<group>"; };
		A7D8C7C21723174600025675 /* libDTZipArchive_Mac.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libDTZipArchive_Mac.a; sourceTree = BUILT_PRODUCTS_DIR; };
		A7D9D62D1758C89800C29B6F /* DTReachability Demo.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "DTReachability Demo.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		A7D9D6631758CA6300C29B6F /* SystemConfiguration.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SystemConfiguration.framework; path = System/Library/Frameworks/SystemConfiguration.framework; sourceTree = SDKROOT; };
		A7E383C2160DFEDB00CF72D6 /* libDTHTMLParser_iOS.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libDTHTMLParser_iOS.a; sourceTree = BUILT_PRODUCTS_DIR; };
		A7E383DA160DFF8600CF72D6 /* libDTZipArchive_iOS.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libDTZipArchive_iOS.a; sourceTree = BUILT_PRODUCTS_DIR; };
		A7E889A416A9B190009EF0DF /* libDTUTI_Mac.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libDTUTI_Mac.a; sourceTree = BUILT_PRODUCTS_DIR; };
		A7E889A516A9B190009EF0DF /* Cocoa.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Cocoa.framework; path = Library/Frameworks/Cocoa.framework; sourceTree = DEVELOPER_DIR; };
		A7E889A816A9B190009EF0DF /* AppKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AppKit.framework; path = Library/Frameworks/AppKit.framework; sourceTree = SDKROOT; };
		A7E889A916A9B190009EF0DF /* CoreData.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreData.framework; path = Library/Frameworks/CoreData.framework; sourceTree = SDKROOT; };
		A7E889AA16A9B190009EF0DF /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = Library/Frameworks/Foundation.framework; sourceTree = SDKROOT; };
		A7E88ED016BC0278008CBA9C /* DTCustomColoredAccessory.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DTCustomColoredAccessory.h; sourceTree = "<group>"; };
		A7E88ED116BC0278008CBA9C /* DTCustomColoredAccessory.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DTCustomColoredAccessory.m; sourceTree = "<group>"; };
		A7EA07391A2B215200B61CCE /* DTFoundation.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = DTFoundation.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		A7EA07B81A2B2F6A00B61CCE /* DTFoundation.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = DTFoundation.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		A7F4DFF8147FD08900F4059A /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = System/Library/Frameworks/UIKit.framework; sourceTree = SDKROOT; };
		A7F4DFFA147FD08F00F4059A /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = System/Library/Frameworks/Foundation.framework; sourceTree = SDKROOT; };
		A7FA5FA217B0F7B4000FC61F /* DTLog.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DTLog.h; sourceTree = "<group>"; };
		A7FA5FA317B0F7B4000FC61F /* DTLog.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DTLog.m; sourceTree = "<group>"; };
		A7FA5FAC17B127E0000FC61F /* DTAsyncFileDeleter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = DTAsyncFileDeleter.m; path = DTAsyncFileDeleter/DTAsyncFileDeleter.m; sourceTree = "<group>"; };
		A7FA5FAD17B127E0000FC61F /* DTAsyncFileDeleter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = DTAsyncFileDeleter.h; path = DTAsyncFileDeleter/DTAsyncFileDeleter.h; sourceTree = "<group>"; };
		A7FAA3871652291D006ED151 /* NSURL+DTComparing.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSURL+DTComparing.h"; sourceTree = "<group>"; };
		A7FAA3881652291D006ED151 /* NSURL+DTComparing.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSURL+DTComparing.m"; sourceTree = "<group>"; };
		A7FB1218175C9D5000D4B7F0 /* DTWeakSupport.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DTWeakSupport.h; sourceTree = "<group>"; };
		A8C4A7771F949D4500FC611D /* UIScreen+DTFoundation.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIScreen+DTFoundation.h"; sourceTree = "<group>"; };
		A8C4A7781F949D5500FC611D /* UIScreen+DTFoundation.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIScreen+DTFoundation.m"; sourceTree = "<group>"; };
		C04943358446F1A9BA86F458 /* DTZipArchiveGZip.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DTZipArchiveGZip.h; sourceTree = "<group>"; };
		C0494776E02407791E96C641 /* DTZipArchivePKZip.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DTZipArchivePKZip.m; sourceTree = "<group>"; };
		C0494CD06FFAB6AEAC8ADAD4 /* DTZipArchiveNode.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DTZipArchiveNode.h; sourceTree = "<group>"; };
		C0494E1FBE9D6D4FB042F2A3 /* DTZipArchiveGZip.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DTZipArchiveGZip.m; sourceTree = "<group>"; };
		C0494E7390203BE4105C5070 /* DTZipArchivePKZip.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DTZipArchivePKZip.h; sourceTree = "<group>"; };
		C0494FCC2C675F2CDF315027 /* DTZipArchiveNode.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DTZipArchiveNode.m; sourceTree = "<group>"; };
		C9826821196DAD7800A84677 /* DTSidePanelControllerSegue.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DTSidePanelControllerSegue.h; sourceTree = "<group>"; };
		C9826822196DAD7800A84677 /* DTSidePanelControllerSegue.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DTSidePanelControllerSegue.m; sourceTree = "<group>"; };
		F88FF6801920A83300120808 /* libDTProgressHUD_iOS.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libDTProgressHUD_iOS.a; sourceTree = BUILT_PRODUCTS_DIR; };
		F88FF6831920A9DF00120808 /* DTProgressHUD.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DTProgressHUD.h; sourceTree = "<group>"; };
		F88FF6841920A9DF00120808 /* DTProgressHUD.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DTProgressHUD.m; sourceTree = "<group>"; };
		F88FF6A51920AAB600120808 /* DTProgressHUD Demo.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "DTProgressHUD Demo.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		F88FF6C81920AE9700120808 /* CoreGraphics.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreGraphics.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS7.1.sdk/System/Library/Frameworks/CoreGraphics.framework; sourceTree = DEVELOPER_DIR; };
		F88FF6CE1920B0CC00120808 /* QuartzCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = QuartzCore.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS7.1.sdk/System/Library/Frameworks/QuartzCore.framework; sourceTree = DEVELOPER_DIR; };
		F88FF6D01920BF5200120808 /* DTProgressHUDWindow.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DTProgressHUDWindow.h; sourceTree = "<group>"; };
		F88FF6D11920BF5200120808 /* DTProgressHUDWindow.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DTProgressHUDWindow.m; sourceTree = "<group>"; };
		F88FF6D41920F1AF00120808 /* DTActionSheet.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DTActionSheet.h; sourceTree = "<group>"; };
		F88FF6D51920F1AF00120808 /* DTActionSheet.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DTActionSheet.m; sourceTree = "<group>"; };
		F88FF6D61920F1AF00120808 /* DTAlertView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DTAlertView.h; sourceTree = "<group>"; };
		F88FF6D71920F1AF00120808 /* DTAlertView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DTAlertView.m; sourceTree = "<group>"; };
		F88FF6D81920F1AF00120808 /* UIView+DTActionHandlers.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIView+DTActionHandlers.h"; sourceTree = "<group>"; };
		F88FF6D91920F1AF00120808 /* UIView+DTActionHandlers.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIView+DTActionHandlers.m"; sourceTree = "<group>"; };
		FA9CB80C17ABDEF200A596C5 /* libDTASN1.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libDTASN1.a; sourceTree = BUILT_PRODUCTS_DIR; };
		FAB1725D163004FF00B44EDC /* QuartzCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = QuartzCore.framework; path = System/Library/Frameworks/QuartzCore.framework; sourceTree = SDKROOT; };
		FAB1726916301F0D00B44EDC /* DTFoundationConstants.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DTFoundationConstants.h; sourceTree = "<group>"; };
		FAB1726A16301F0D00B44EDC /* DTFoundationConstants.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DTFoundationConstants.m; sourceTree = "<group>"; };
		FAC284E619B84B5F0010C385 /* DTActionSheet Demo.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "DTActionSheet Demo.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		FAE5B9311610A38200CA0D99 /* CoreGraphics.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreGraphics.framework; path = System/Library/Frameworks/CoreGraphics.framework; sourceTree = SDKROOT; };
		FAF37A2017AFDD93009AC27C /* DTZipArchive.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = DTZipArchive.app; sourceTree = BUILT_PRODUCTS_DIR; };
		FAF37A6C17AFE06B009AC27C /* libz.1.2.5.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libz.1.2.5.dylib; path = usr/lib/libz.1.2.5.dylib; sourceTree = SDKROOT; };
		FAF37A6F17AFE098009AC27C /* QuickLook.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = QuickLook.framework; path = System/Library/Frameworks/QuickLook.framework; sourceTree = SDKROOT; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		384470BE1BA3330F0037C68D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		384471191BA33ED80037C68D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				29DA9D841C6DC26000F5F22A /* SystemConfiguration.framework in Frameworks */,
				3844711A1BA33ED80037C68D /* ImageIO.framework in Frameworks */,
				3844711B1BA33ED80037C68D /* CoreGraphics.framework in Frameworks */,
				3844711C1BA33ED80037C68D /* Foundation.framework in Frameworks */,
				3844711D1BA33ED80037C68D /* QuartzCore.framework in Frameworks */,
				3844711E1BA33ED80037C68D /* UIKit.framework in Frameworks */,
				3844711F1BA33ED80037C68D /* libxml2.dylib in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3D7CE540166613B60028D339 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A70B4CE41486637E00873A4A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A70B4CE81486637E00873A4A /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A70D74CD1743AFBC00E6E626 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A70D74F21743B01E00E6E626 /* QuartzCore.framework in Frameworks */,
				A70D74F11743AFDF00E6E626 /* libDTFoundation.a in Frameworks */,
				A70D74D11743AFBC00E6E626 /* UIKit.framework in Frameworks */,
				A70D74D21743AFBC00E6E626 /* Foundation.framework in Frameworks */,
				A70D74D31743AFBC00E6E626 /* CoreGraphics.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A710A4FD1607556000437D36 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A72121D7173BFFB1003C6F0A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A72121E7173BFFED003C6F0A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A78220B7168060CA005B602D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A78381C01963F8D700AF09D3 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A7EA07CF1A2B2F6B00B61CCE /* DTFoundation.framework in Frameworks */,
				A7AC21CE1964184F0009E1B9 /* ImageIO.framework in Frameworks */,
				A7AC21CC196417D00009E1B9 /* libDTAnimatedGIF.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A7917C74191A312600964D63 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A7A5F0AB1A2FA92400DA3B62 /* Security.framework in Frameworks */,
				A7917CB6191A3CD500964D63 /* UIKit.framework in Frameworks */,
				A7917CA3191A327A00964D63 /* libxml2.2.dylib in Frameworks */,
				A7917C9E191A323000964D63 /* libz.1.2.5.dylib in Frameworks */,
				A7917C78191A312700964D63 /* XCTest.framework in Frameworks */,
				A7917C79191A312700964D63 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A79D99991792D1F50082BC06 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A7AC21AC196417830009E1B9 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A7C92B77174FB8900019D70A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A7C92B89174FB9720019D70A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A7D8C7B91723174600025675 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A7D9D62A1758C89800C29B6F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A74A765017BBB163007073C8 /* libDTFoundation.a in Frameworks */,
				A7D9D6641758CA6300C29B6F /* SystemConfiguration.framework in Frameworks */,
				A7D9D6621758CA5A00C29B6F /* libDTReachability_iOS.a in Frameworks */,
				A7D9D62E1758C89800C29B6F /* UIKit.framework in Frameworks */,
				A7D9D62F1758C89800C29B6F /* Foundation.framework in Frameworks */,
				A7D9D6301758C89800C29B6F /* CoreGraphics.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A7E383BF160DFEDB00CF72D6 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A7E383D7160DFF8600CF72D6 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A7E889A116A9B190009EF0DF /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A7EA07351A2B215200B61CCE /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				29DA9D821C6DC25A00F5F22A /* SystemConfiguration.framework in Frameworks */,
				A704BEBB1B6176D70067BF7E /* Foundation.framework in Frameworks */,
				A704BEBA1B6176D10067BF7E /* AppKit.framework in Frameworks */,
				A79BA84A1B46E5DF0086C2F6 /* libxml2.dylib in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A7EA07B41A2B2F6A00B61CCE /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				29DA9D811C6DC25300F5F22A /* SystemConfiguration.framework in Frameworks */,
				A74A7A8E1B617186004163BE /* ImageIO.framework in Frameworks */,
				A74A7A8C1B61716E004163BE /* CoreGraphics.framework in Frameworks */,
				A74A7A8A1B617158004163BE /* Foundation.framework in Frameworks */,
				A74A7A881B61713A004163BE /* QuartzCore.framework in Frameworks */,
				A74A7A851B61712B004163BE /* UIKit.framework in Frameworks */,
				A79BA6C71B46A3CA0086C2F6 /* libxml2.dylib in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F88FF67A1920A83300120808 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F88FF6931920AAB600120808 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F88FF6CF1920B0CC00120808 /* QuartzCore.framework in Frameworks */,
				F88FF6CC1920AF2900120808 /* libDTFoundation.a in Frameworks */,
				F88FF6C91920AE9700120808 /* CoreGraphics.framework in Frameworks */,
				F88FF6C71920AE7300120808 /* UIKit.framework in Frameworks */,
				F88FF6C61920AC1900120808 /* libDTProgressHUD_iOS.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FA9CB80917ABDEF200A596C5 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FAC284D419B84B5F0010C385 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FAC284D519B84B5F0010C385 /* libDTFoundation.a in Frameworks */,
				FAC284D619B84B5F0010C385 /* QuickLook.framework in Frameworks */,
				FAC284D919B84B5F0010C385 /* UIKit.framework in Frameworks */,
				FAC284DA19B84B5F0010C385 /* Foundation.framework in Frameworks */,
				FAC284DB19B84B5F0010C385 /* CoreGraphics.framework in Frameworks */,
				FAC284DC19B84B5F0010C385 /* QuartzCore.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FAF37A1D17AFDD93009AC27C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FAF37A7317AFE0D1009AC27C /* libDTFoundation.a in Frameworks */,
				FAF37A7017AFE098009AC27C /* QuickLook.framework in Frameworks */,
				FAF37A6D17AFE06B009AC27C /* libz.1.2.5.dylib in Frameworks */,
				FAF37A5A17AFDF63009AC27C /* libDTZipArchive_iOS.a in Frameworks */,
				FAF37A2117AFDD93009AC27C /* UIKit.framework in Frameworks */,
				FAF37A2217AFDD93009AC27C /* Foundation.framework in Frameworks */,
				FAF37A2317AFDD93009AC27C /* CoreGraphics.framework in Frameworks */,
				FAF37A7417AFE11D009AC27C /* QuartzCore.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		A70B4CC41486621B00873A4A /* Core */ = {
			isa = PBXGroup;
			children = (
				A70B4CC51486621B00873A4A /* DTFoundation.h */,
				A70B4CDF148662B000873A4A /* DTFoundation-Info.plist */,
				A70B4CE0148662B000873A4A /* DTFoundation-Prefix.pch */,
				A70B4CC61486621B00873A4A /* Source */,
			);
			path = Core;
			sourceTree = "<group>";
		};
		A70B4CC61486621B00873A4A /* Source */ = {
			isa = PBXGroup;
			children = (
				A76DB48E16A5E37F0010CD85 /* iOS */,
				A76DB4B716A5E3B20010CD85 /* OSX */,
				A7D0AA6A153C395C0020F18B /* Universal */,
				A77DD3D014E81A0B00F34B03 /* Externals */,
			);
			path = Source;
			sourceTree = "<group>";
		};
		A72121D3173BFF89003C6F0A /* DTReachability */ = {
			isa = PBXGroup;
			children = (
				A72121D4173BFF89003C6F0A /* DTReachability.h */,
				A72121D5173BFF89003C6F0A /* DTReachability.m */,
			);
			path = DTReachability;
			sourceTree = "<group>";
		};
		A74663B11743D00500D4D7D5 /* DTSidePanel */ = {
			isa = PBXGroup;
			children = (
				A74663B21743D43600D4D7D5 /* DTSidePanelController.h */,
				A74663B31743D43600D4D7D5 /* DTSidePanelController.m */,
				A7C92B67174F9AEC0019D70A /* UIViewController+DTSidePanelController.h */,
				A7C92B68174F9AEC0019D70A /* UIViewController+DTSidePanelController.m */,
				355B257CC69FC64D78ABE0BE /* DTSidePanelPanGestureRecognizer.h */,
				355B210F4174F2B22B0CD6A0 /* DTSidePanelPanGestureRecognizer.m */,
				C9826821196DAD7800A84677 /* DTSidePanelControllerSegue.h */,
				C9826822196DAD7800A84677 /* DTSidePanelControllerSegue.m */,
			);
			path = DTSidePanel;
			sourceTree = "<group>";
		};
		A76DB48816A5E1D20010CD85 /* DTHTMLParser */ = {
			isa = PBXGroup;
			children = (
				A76DB48916A5E1D20010CD85 /* DTHTMLParser.h */,
				A76DB48A16A5E1D20010CD85 /* DTHTMLParser.m */,
			);
			path = DTHTMLParser;
			sourceTree = "<group>";
		};
		A76DB48E16A5E37F0010CD85 /* iOS */ = {
			isa = PBXGroup;
			children = (
				F88FF6D31920F1AF00120808 /* BlocksAdditions */,
				A7AC21A6196409270009E1B9 /* DTAnimatedGIF */,
				F88FF6821920A9DF00120808 /* DTProgressHUD */,
				A78EDD2917A65D4A00480205 /* Debug */,
				A74663B11743D00500D4D7D5 /* DTSidePanel */,
				A730BCCC16D2892E003B849F /* DTTiledLayerWithoutFade.h */,
				A730BCCD16D2892E003B849F /* DTTiledLayerWithoutFade.m */,
				A76DB49116A5E37F0010CD85 /* DTActivityTitleView.h */,
				A76DB49216A5E37F0010CD85 /* DTActivityTitleView.m */,
				A7E88ED016BC0278008CBA9C /* DTCustomColoredAccessory.h */,
				A7E88ED116BC0278008CBA9C /* DTCustomColoredAccessory.m */,
				A76DB49516A5E37F0010CD85 /* DTPieProgressIndicator.h */,
				A76DB49616A5E37F0010CD85 /* DTPieProgressIndicator.m */,
				A76DB49716A5E37F0010CD85 /* DTSmartPagingScrollView.h */,
				A76DB49816A5E37F0010CD85 /* DTSmartPagingScrollView.m */,
				A76DB4C216A5E50D0010CD85 /* UIApplication+DTNetworkActivity.h */,
				A76DB4C316A5E50E0010CD85 /* UIApplication+DTNetworkActivity.m */,
				A76DB4C416A5E50E0010CD85 /* UIImage+DTFoundation.h */,
				A76DB4C516A5E50E0010CD85 /* UIImage+DTFoundation.m */,
				A76DB4C016A5E50D0010CD85 /* NSURL+DTAppLinks.h */,
				A76DB4C116A5E50D0010CD85 /* NSURL+DTAppLinks.m */,
				A8C4A7771F949D4500FC611D /* UIScreen+DTFoundation.h */,
				A8C4A7781F949D5500FC611D /* UIScreen+DTFoundation.m */,
				A76DB4C816A5E50F0010CD85 /* UIView+DTFoundation.h */,
				A76DB4C916A5E50F0010CD85 /* UIView+DTFoundation.m */,
			);
			path = iOS;
			sourceTree = "<group>";
		};
		A76DB4B716A5E3B20010CD85 /* OSX */ = {
			isa = PBXGroup;
			children = (
				A76DB4B816A5E3B20010CD85 /* DTScrollView.h */,
				A76DB4B916A5E3B20010CD85 /* DTScrollView.m */,
				A76DB4E416A5E5590010CD85 /* NSDocument+DTFoundation.h */,
				A76DB4E516A5E5590010CD85 /* NSDocument+DTFoundation.m */,
				A76DB4E616A5E5590010CD85 /* NSImage+DTUtilities.h */,
				A76DB4E716A5E5590010CD85 /* NSImage+DTUtilities.m */,
				A76DB4E816A5E5590010CD85 /* NSValue+DTConversion.h */,
				A76DB4E916A5E5590010CD85 /* NSValue+DTConversion.m */,
				A76DB4EA16A5E5590010CD85 /* NSView+DTAutoLayout.h */,
				A76DB4EB16A5E5590010CD85 /* NSView+DTAutoLayout.m */,
				A76DB4EC16A5E5590010CD85 /* NSWindowController+DTPanelControllerPresenting.h */,
				A76DB4ED16A5E5590010CD85 /* NSWindowController+DTPanelControllerPresenting.m */,
			);
			path = OSX;
			sourceTree = "<group>";
		};
		A76DB4BC16A5E4760010CD85 /* DTZipArchive */ = {
			isa = PBXGroup;
			children = (
				A76DB4BD16A5E4770010CD85 /* DTZipArchive.h */,
				A76DB4BE16A5E4770010CD85 /* DTZipArchive.m */,
				C0494CD06FFAB6AEAC8ADAD4 /* DTZipArchiveNode.h */,
				C0494FCC2C675F2CDF315027 /* DTZipArchiveNode.m */,
				C0494E7390203BE4105C5070 /* DTZipArchivePKZip.h */,
				C0494776E02407791E96C641 /* DTZipArchivePKZip.m */,
				C04943358446F1A9BA86F458 /* DTZipArchiveGZip.h */,
				C0494E1FBE9D6D4FB042F2A3 /* DTZipArchiveGZip.m */,
			);
			path = DTZipArchive;
			sourceTree = "<group>";
		};
		A76DB4F816A5E5940010CD85 /* DTUTI */ = {
			isa = PBXGroup;
			children = (
				A76DB4F916A5E5950010CD85 /* NSString+DTUTI.h */,
				A76DB4FA16A5E5950010CD85 /* NSString+DTUTI.m */,
			);
			path = DTUTI;
			sourceTree = "<group>";
		};
		A77DD3D014E81A0B00F34B03 /* Externals */ = {
			isa = PBXGroup;
			children = (
				A77DD40C14E825FC00F34B03 /* minizip */,
			);
			path = Externals;
			sourceTree = "<group>";
		};
		A77DD40C14E825FC00F34B03 /* minizip */ = {
			isa = PBXGroup;
			children = (
				A77DD40D14E825FC00F34B03 /* crypt.h */,
				A77DD40E14E825FC00F34B03 /* ioapi.c */,
				A77DD40F14E825FC00F34B03 /* ioapi.h */,
				A77DD41014E825FC00F34B03 /* mztools.c */,
				A77DD41114E825FC00F34B03 /* mztools.h */,
				A77DD41214E825FC00F34B03 /* unzip.c */,
				A77DD41314E825FC00F34B03 /* unzip.h */,
				A77DD41414E825FC00F34B03 /* zip.c */,
				A77DD41514E825FC00F34B03 /* zip.h */,
			);
			path = minizip;
			sourceTree = "<group>";
		};
		A78EDD1617A65AD700480205 /* DTScripting */ = {
			isa = PBXGroup;
			children = (
				A78EDD1717A65AD700480205 /* DTScriptExpression.h */,
				A78EDD1817A65AD700480205 /* DTScriptExpression.m */,
				A78EDD1917A65AD700480205 /* DTScriptVariable.h */,
				A78EDD1A17A65AD700480205 /* DTScriptVariable.m */,
				A78EDD1B17A65AD800480205 /* NSScanner+DTScripting.h */,
				A78EDD1C17A65AD800480205 /* NSScanner+DTScripting.m */,
			);
			path = DTScripting;
			sourceTree = "<group>";
		};
		A78EDD1D17A65AD800480205 /* DTASN1 */ = {
			isa = PBXGroup;
			children = (
				A78EDD1E17A65AD800480205 /* DTASN1BitString.h */,
				A78EDD1F17A65AD800480205 /* DTASN1BitString.m */,
				A78EDD2017A65AD800480205 /* DTASN1Parser.h */,
				A78EDD2117A65AD800480205 /* DTASN1Parser.m */,
				A78EDD2217A65AD800480205 /* DTASN1Serialization.h */,
				A78EDD2317A65AD800480205 /* DTASN1Serialization.m */,
			);
			path = DTASN1;
			sourceTree = "<group>";
		};
		A78EDD2417A65B8600480205 /* Runtime */ = {
			isa = PBXGroup;
			children = (
				A78EDD2517A65B8600480205 /* DTObjectBlockExecutor.h */,
				A78EDD2617A65B8600480205 /* DTObjectBlockExecutor.m */,
				A78EDD2717A65B8600480205 /* NSObject+DTRuntime.h */,
				A78EDD2817A65B8600480205 /* NSObject+DTRuntime.m */,
			);
			path = Runtime;
			sourceTree = "<group>";
		};
		A78EDD2917A65D4A00480205 /* Debug */ = {
			isa = PBXGroup;
			children = (
				A78EDD2A17A65D4B00480205 /* UIColor+DTDebug.h */,
				A78EDD2B17A65D4B00480205 /* UIColor+DTDebug.m */,
				A78EDD2C17A65D4B00480205 /* UIView+DTDebug.h */,
				A78EDD2D17A65D4B00480205 /* UIView+DTDebug.m */,
			);
			path = Debug;
			sourceTree = "<group>";
		};
		A79D99BF1792D2380082BC06 /* DTAWS */ = {
			isa = PBXGroup;
			children = (
				A79D99C01792D2490082BC06 /* NSURL+DTAWS.h */,
				A79D99C11792D2490082BC06 /* NSURL+DTAWS.m */,
			);
			path = DTAWS;
			sourceTree = "<group>";
		};
		A7AB49DB1483C47F00028FE8 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				29DA9D831C6DC26000F5F22A /* SystemConfiguration.framework */,
				29DA9D801C6DC25300F5F22A /* SystemConfiguration.framework */,
				A704BEB91B6176D10067BF7E /* AppKit.framework */,
				A74A7A8D1B617186004163BE /* ImageIO.framework */,
				A74A7A8B1B61716E004163BE /* CoreGraphics.framework */,
				A74A7A891B617158004163BE /* Foundation.framework */,
				A74A7A871B61713A004163BE /* QuartzCore.framework */,
				A74A7A841B61712B004163BE /* UIKit.framework */,
				A79BA6C61B46A3CA0086C2F6 /* libxml2.dylib */,
				A7A5F0AA1A2FA92400DA3B62 /* Security.framework */,
				A7AC21CD1964184F0009E1B9 /* ImageIO.framework */,
				F88FF6CE1920B0CC00120808 /* QuartzCore.framework */,
				F88FF6C81920AE9700120808 /* CoreGraphics.framework */,
				A7917CB5191A3CD500964D63 /* UIKit.framework */,
				A7917CA2191A327A00964D63 /* libxml2.2.dylib */,
				A7917C9D191A322F00964D63 /* libz.1.2.5.dylib */,
				A7D60FF715D3B28B00AEDD1B /* libxml2.dylib */,
				FAF37A6C17AFE06B009AC27C /* libz.1.2.5.dylib */,
				A7239FFC16A6CF0B0071E902 /* CoreData.framework */,
				FAE5B9311610A38200CA0D99 /* CoreGraphics.framework */,
				A7F4DFFA147FD08F00F4059A /* Foundation.framework */,
				A7C619EF162D8D84002C0C1A /* MobileCoreServices.framework */,
				FAB1725D163004FF00B44EDC /* QuartzCore.framework */,
				FAF37A6F17AFE098009AC27C /* QuickLook.framework */,
				A7D60FD015D3B0BC00AEDD1B /* SenTestingKit.framework */,
				A7D9D6631758CA6300C29B6F /* SystemConfiguration.framework */,
				A7F4DFF8147FD08900F4059A /* UIKit.framework */,
				A7E889A516A9B190009EF0DF /* Cocoa.framework */,
				A79D99AA1792D1F50082BC06 /* XCTest.framework */,
				A7E889A716A9B190009EF0DF /* Other Frameworks */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		A7AC21A6196409270009E1B9 /* DTAnimatedGIF */ = {
			isa = PBXGroup;
			children = (
				A7AC21A7196409560009E1B9 /* DTAnimatedGIF.h */,
				A7AC21A8196409560009E1B9 /* DTAnimatedGIF.m */,
			);
			path = DTAnimatedGIF;
			sourceTree = "<group>";
		};
		A7C92B71174FB81C0019D70A /* DTSQLite */ = {
			isa = PBXGroup;
			children = (
				A7C92B72174FB81C0019D70A /* DTSQLiteDatabase.h */,
				A7C92B73174FB81C0019D70A /* DTSQLiteDatabase.m */,
				A7C92B74174FB81C0019D70A /* DTSQLiteFunctions.h */,
				A7C92B75174FB81C0019D70A /* DTSQLiteFunctions.m */,
			);
			path = DTSQLite;
			sourceTree = "<group>";
		};
		A7D0AA68153C39490020F18B /* Core Graphics */ = {
			isa = PBXGroup;
			children = (
				A7D0AA6B153C39920020F18B /* DTCoreGraphicsUtils.h */,
				A7D0AA6C153C39920020F18B /* DTCoreGraphicsUtils.m */,
			);
			name = "Core Graphics";
			sourceTree = "<group>";
		};
		A7D0AA69153C39540020F18B /* Categories */ = {
			isa = PBXGroup;
			children = (
				A73D5BA9155271FD0024BDB7 /* NSArray+DTError.h */,
				A73D5BAA155271FD0024BDB7 /* NSArray+DTError.m */,
				A79500F5161D680000358BC3 /* NSData+DTCrypto.h */,
				A79500F6161D680000358BC3 /* NSData+DTCrypto.m */,
				A7D0AA23153C1B160020F18B /* NSDictionary+DTError.h */,
				A7D0AA24153C1B160020F18B /* NSDictionary+DTError.m */,
				A73B6F89163169BB002CCCA7 /* NSFileWrapper+DTCopying.h */,
				A73B6F8A163169BB002CCCA7 /* NSFileWrapper+DTCopying.m */,
				A766135E16143F8A00DF6C2B /* NSMutableArray+DTMoving.h */,
				A766135F16143F8A00DF6C2B /* NSMutableArray+DTMoving.m */,
				A70B4CCA1486621B00873A4A /* NSString+DTFormatNumbers.h */,
				A70B4CCB1486621B00873A4A /* NSString+DTFormatNumbers.m */,
				A7D0AA48153C233E0020F18B /* NSString+DTUtilities.h */,
				A7D0AA49153C233E0020F18B /* NSString+DTUtilities.m */,
				A7D8627F14EBF65C001436AF /* NSString+DTPaths.h */,
				A7D8628014EBF65C001436AF /* NSString+DTPaths.m */,
				A7D0AA29153C1FE40020F18B /* NSString+DTURLEncoding.h */,
				A7D0AA2A153C1FE40020F18B /* NSString+DTURLEncoding.m */,
				A79231CC157A0B9400C3ACBB /* NSURL+DTUnshorten.h */,
				A79231CD157A0B9400C3ACBB /* NSURL+DTUnshorten.m */,
				A7FAA3871652291D006ED151 /* NSURL+DTComparing.h */,
				A7FAA3881652291D006ED151 /* NSURL+DTComparing.m */,
			);
			name = Categories;
			sourceTree = "<group>";
		};
		A7D0AA6A153C395C0020F18B /* Universal */ = {
			isa = PBXGroup;
			children = (
				A7D0AA69153C39540020F18B /* Categories */,
				A7D0AA68153C39490020F18B /* Core Graphics */,
				A78EDD1D17A65AD800480205 /* DTASN1 */,
				A79D99BF1792D2380082BC06 /* DTAWS */,
				A78EDD1617A65AD700480205 /* DTScripting */,
				A76DB48816A5E1D20010CD85 /* DTHTMLParser */,
				A72121D3173BFF89003C6F0A /* DTReachability */,
				A7C92B71174FB81C0019D70A /* DTSQLite */,
				A76DB4F816A5E5940010CD85 /* DTUTI */,
				A76DB4BC16A5E4760010CD85 /* DTZipArchive */,
				A78EDD2417A65B8600480205 /* Runtime */,
				A7FA5FAD17B127E0000FC61F /* DTAsyncFileDeleter.h */,
				A7FA5FAC17B127E0000FC61F /* DTAsyncFileDeleter.m */,
				A77D5BF816E4961A00A45C28 /* DTBase64Coding.h */,
				A77D5BF916E4961A00A45C28 /* DTBase64Coding.m */,
				A7D6F2E315063448001CACDD /* DTExtendedFileAttributes.h */,
				A7D6F2E415063448001CACDD /* DTExtendedFileAttributes.m */,
				A78D661B17AFFCCC0039F5E6 /* DTFolderMonitor.h */,
				A78D661C17AFFCCC0039F5E6 /* DTFolderMonitor.m */,
				FAB1726916301F0D00B44EDC /* DTFoundationConstants.h */,
				FAB1726A16301F0D00B44EDC /* DTFoundationConstants.m */,
				A7FA5FA217B0F7B4000FC61F /* DTLog.h */,
				A7FA5FA317B0F7B4000FC61F /* DTLog.m */,
				A70B4CC71486621B00873A4A /* DTVersion.h */,
				A70B4CC81486621B00873A4A /* DTVersion.m */,
				A7FB1218175C9D5000D4B7F0 /* DTWeakSupport.h */,
				A768BE3A17FC3C91008834C6 /* DTBlockFunctions.h */,
				A768BE3B17FC3C91008834C6 /* DTBlockFunctions.m */,
				A74549681B6A3F26004B0CA7 /* DTCompatibility.h */,
			);
			name = Universal;
			sourceTree = "<group>";
		};
		A7E889A716A9B190009EF0DF /* Other Frameworks */ = {
			isa = PBXGroup;
			children = (
				A7E889A816A9B190009EF0DF /* AppKit.framework */,
				A7E889A916A9B190009EF0DF /* CoreData.framework */,
				A7E889AA16A9B190009EF0DF /* Foundation.framework */,
			);
			name = "Other Frameworks";
			sourceTree = "<group>";
		};
		A7F4DF98147FB61500F4059A = {
			isa = PBXGroup;
			children = (
				A70B4CC41486621B00873A4A /* Core */,
				A7F4DFA6147FB61500F4059A /* Products */,
				A7AB49DB1483C47F00028FE8 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		A7F4DFA6147FB61500F4059A /* Products */ = {
			isa = PBXGroup;
			children = (
				A70B4CE71486637E00873A4A /* libDTFoundation.a */,
				A710A5001607556000437D36 /* libDTFoundation_Mac.a */,
				A7E383C2160DFEDB00CF72D6 /* libDTHTMLParser_iOS.a */,
				A7E383DA160DFF8600CF72D6 /* libDTZipArchive_iOS.a */,
				3D7CE547166613B60028D339 /* libDTHTMLParser_Mac.a */,
				A78220BA168060CA005B602D /* libDTUTI_iOS.a */,
				A7E889A416A9B190009EF0DF /* libDTUTI_Mac.a */,
				A7D8C7C21723174600025675 /* libDTZipArchive_Mac.a */,
				A72121DA173BFFB1003C6F0A /* libDTReachability_iOS.a */,
				A72121EA173BFFED003C6F0A /* libDTReachability_Mac.a */,
				A70D74D01743AFBC00E6E626 /* DTSidePanels Demo.app */,
				A7C92B7A174FB8900019D70A /* libDTSQLite_iOS.a */,
				A7C92B8C174FB9720019D70A /* libDTSQLite_Mac.a */,
				A7D9D62D1758C89800C29B6F /* DTReachability Demo.app */,
				A79D999C1792D1F50082BC06 /* libDTAWS.a */,
				FA9CB80C17ABDEF200A596C5 /* libDTASN1.a */,
				FAF37A2017AFDD93009AC27C /* DTZipArchive.app */,
				A7917C77191A312600964D63 /* Unit Tests.xctest */,
				F88FF6801920A83300120808 /* libDTProgressHUD_iOS.a */,
				F88FF6A51920AAB600120808 /* DTProgressHUD Demo.app */,
				A78381C31963F8D700AF09D3 /* DTAnimatedGIF Demo.app */,
				A7AC21AF196417830009E1B9 /* libDTAnimatedGIF.a */,
				FAC284E619B84B5F0010C385 /* DTActionSheet Demo.app */,
				A7EA07391A2B215200B61CCE /* DTFoundation.framework */,
				A7EA07B81A2B2F6A00B61CCE /* DTFoundation.framework */,
				384470EB1BA3330F0037C68D /* libStatic Library (tvOS).a */,
				384471551BA33ED80037C68D /* DTFoundation.framework */,
			);
			name = Products;
			path = ../..;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		F88FF6821920A9DF00120808 /* DTProgressHUD */ = {
			isa = PBXGroup;
			children = (
				F88FF6831920A9DF00120808 /* DTProgressHUD.h */,
				F88FF6841920A9DF00120808 /* DTProgressHUD.m */,
				F88FF6D01920BF5200120808 /* DTProgressHUDWindow.h */,
				F88FF6D11920BF5200120808 /* DTProgressHUDWindow.m */,
			);
			path = DTProgressHUD;
			sourceTree = "<group>";
		};
		F88FF6D31920F1AF00120808 /* BlocksAdditions */ = {
			isa = PBXGroup;
			children = (
				F88FF6D41920F1AF00120808 /* DTActionSheet.h */,
				F88FF6D51920F1AF00120808 /* DTActionSheet.m */,
				F88FF6D61920F1AF00120808 /* DTAlertView.h */,
				F88FF6D71920F1AF00120808 /* DTAlertView.m */,
				F88FF6D81920F1AF00120808 /* UIView+DTActionHandlers.h */,
				F88FF6D91920F1AF00120808 /* UIView+DTActionHandlers.m */,
			);
			path = BlocksAdditions;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		384470C01BA3330F0037C68D /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				384470C11BA3330F0037C68D /* NSString+DTPaths.h in Headers */,
				384470C21BA3330F0037C68D /* DTCompatibility.h in Headers */,
				384470C31BA3330F0037C68D /* DTBase64Coding.h in Headers */,
				384470C41BA3330F0037C68D /* NSMutableArray+DTMoving.h in Headers */,
				384470C51BA3330F0037C68D /* NSArray+DTError.h in Headers */,
				384470C61BA3330F0037C68D /* DTFoundation.h in Headers */,
				384470C71BA3330F0037C68D /* DTAnimatedGIF.h in Headers */,
				384470C81BA3330F0037C68D /* crypt.h in Headers */,
				384470C91BA3330F0037C68D /* ioapi.h in Headers */,
				A70BCEC81BF4A61B000CD60D /* NSObject+DTRuntime.h in Headers */,
				384470CA1BA3330F0037C68D /* mztools.h in Headers */,
				384470CB1BA3330F0037C68D /* unzip.h in Headers */,
				384470CC1BA3330F0037C68D /* zip.h in Headers */,
				384470CD1BA3330F0037C68D /* DTActivityTitleView.h in Headers */,
				384470CE1BA3330F0037C68D /* DTPieProgressIndicator.h in Headers */,
				A70BCEC51BF4A5F0000CD60D /* DTObjectBlockExecutor.h in Headers */,
				384470CF1BA3330F0037C68D /* NSURL+DTComparing.h in Headers */,
				384470D01BA3330F0037C68D /* DTSmartPagingScrollView.h in Headers */,
				384470D11BA3330F0037C68D /* NSURL+DTAppLinks.h in Headers */,
				384470D21BA3330F0037C68D /* NSDictionary+DTError.h in Headers */,
				384470D31BA3330F0037C68D /* DTProgressHUD.h in Headers */,
				384470D41BA3330F0037C68D /* NSString+DTFormatNumbers.h in Headers */,
				384470D51BA3330F0037C68D /* NSData+DTCrypto.h in Headers */,
				384470D61BA3330F0037C68D /* UIApplication+DTNetworkActivity.h in Headers */,
				384470D71BA3330F0037C68D /* DTBlockFunctions.h in Headers */,
				384470D81BA3330F0037C68D /* UIImage+DTFoundation.h in Headers */,
				384470D91BA3330F0037C68D /* UIView+DTFoundation.h in Headers */,
				384470DB1BA3330F0037C68D /* NSString+DTURLEncoding.h in Headers */,
				384470DC1BA3330F0037C68D /* DTCustomColoredAccessory.h in Headers */,
				384470DD1BA3330F0037C68D /* UIViewController+DTSidePanelController.h in Headers */,
				384470DE1BA3330F0037C68D /* DTSidePanelController.h in Headers */,
				384470DF1BA3330F0037C68D /* NSString+DTUtilities.h in Headers */,
				384470E01BA3330F0037C68D /* DTHTMLParser.h in Headers */,
				384470E11BA3330F0037C68D /* DTCoreGraphicsUtils.h in Headers */,
				384470E21BA3330F0037C68D /* DTWeakSupport.h in Headers */,
				384470E31BA3330F0037C68D /* DTTiledLayerWithoutFade.h in Headers */,
				384470E41BA3330F0037C68D /* NSURL+DTUnshorten.h in Headers */,
				384470E51BA3330F0037C68D /* DTLog.h in Headers */,
				384470E61BA3330F0037C68D /* NSFileWrapper+DTCopying.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		384471201BA33ED80037C68D /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				384471211BA33ED80037C68D /* DTWeakSupport.h in Headers */,
				384471221BA33ED80037C68D /* UIView+DTFoundation.h in Headers */,
				384471231BA33ED80037C68D /* NSArray+DTError.h in Headers */,
				384471241BA33ED80037C68D /* NSString+DTURLEncoding.h in Headers */,
				A7D95ADC1BC40DAB00AB8A21 /* DTASN1Serialization.h in Headers */,
				384471251BA33ED80037C68D /* DTAnimatedGIF.h in Headers */,
				384471261BA33ED80037C68D /* DTSmartPagingScrollView.h in Headers */,
				29DA9D871C6DC27600F5F22A /* DTReachability.h in Headers */,
				384471271BA33ED80037C68D /* DTActionSheet.h in Headers */,
				384471281BA33ED80037C68D /* DTSidePanelController.h in Headers */,
				384471291BA33ED80037C68D /* NSFileWrapper+DTCopying.h in Headers */,
				3844712A1BA33ED80037C68D /* DTExtendedFileAttributes.h in Headers */,
				3844712C1BA33ED80037C68D /* DTHTMLParser.h in Headers */,
				3844712D1BA33ED80037C68D /* NSString+DTPaths.h in Headers */,
				3844712E1BA33ED80037C68D /* DTTiledLayerWithoutFade.h in Headers */,
				3844712F1BA33ED80037C68D /* DTVersion.h in Headers */,
				384471301BA33ED80037C68D /* DTFoundationConstants.h in Headers */,
				384471311BA33ED80037C68D /* UIViewController+DTSidePanelController.h in Headers */,
				384471321BA33ED80037C68D /* NSData+DTCrypto.h in Headers */,
				384471331BA33ED80037C68D /* NSURL+DTUnshorten.h in Headers */,
				384471341BA33ED80037C68D /* UIApplication+DTNetworkActivity.h in Headers */,
				384471351BA33ED80037C68D /* DTCoreGraphicsUtils.h in Headers */,
				384471361BA33ED80037C68D /* NSMutableArray+DTMoving.h in Headers */,
				384471371BA33ED80037C68D /* DTSidePanelControllerSegue.h in Headers */,
				384471381BA33ED80037C68D /* DTObjectBlockExecutor.h in Headers */,
				384471391BA33ED80037C68D /* DTActivityTitleView.h in Headers */,
				3844713A1BA33ED80037C68D /* NSObject+DTRuntime.h in Headers */,
				3844713B1BA33ED80037C68D /* DTSidePanelPanGestureRecognizer.h in Headers */,
				3844713C1BA33ED80037C68D /* DTLog.h in Headers */,
				3844713D1BA33ED80037C68D /* DTFolderMonitor.h in Headers */,
				3844713E1BA33ED80037C68D /* DTPieProgressIndicator.h in Headers */,
				3844713F1BA33ED80037C68D /* NSURL+DTAppLinks.h in Headers */,
				384471401BA33ED80037C68D /* NSString+DTFormatNumbers.h in Headers */,
				384471411BA33ED80037C68D /* DTFoundation.h in Headers */,
				384471421BA33ED80037C68D /* UIView+DTActionHandlers.h in Headers */,
				384471431BA33ED80037C68D /* DTCustomColoredAccessory.h in Headers */,
				384471441BA33ED80037C68D /* DTBlockFunctions.h in Headers */,
				384471451BA33ED80037C68D /* DTBase64Coding.h in Headers */,
				384471461BA33ED80037C68D /* UIView+DTDebug.h in Headers */,
				384471471BA33ED80037C68D /* NSString+DTUtilities.h in Headers */,
				384471481BA33ED80037C68D /* UIColor+DTDebug.h in Headers */,
				384471491BA33ED80037C68D /* NSURL+DTComparing.h in Headers */,
				3844714A1BA33ED80037C68D /* DTAlertView.h in Headers */,
				3844714B1BA33ED80037C68D /* DTProgressHUD.h in Headers */,
				3844714C1BA33ED80037C68D /* DTAsyncFileDeleter.h in Headers */,
				A7D95ADA1BC40DAB00AB8A21 /* DTASN1Parser.h in Headers */,
				3844714D1BA33ED80037C68D /* UIImage+DTFoundation.h in Headers */,
				3844714E1BA33ED80037C68D /* NSDictionary+DTError.h in Headers */,
				3844714F1BA33ED80037C68D /* DTCompatibility.h in Headers */,
				A7D95AD81BC40DAB00AB8A21 /* DTASN1BitString.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A70B4CE51486637E00873A4A /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A7F8CDB71B4BE67E007DAD63 /* NSString+DTPaths.h in Headers */,
				A7B505351B6A594F00BC743A /* DTCompatibility.h in Headers */,
				A79BA89D1B46EDE90086C2F6 /* DTBase64Coding.h in Headers */,
				A7F8CDB51B4BE67E007DAD63 /* NSMutableArray+DTMoving.h in Headers */,
				A7F8CDB11B4BE67E007DAD63 /* NSArray+DTError.h in Headers */,
				A70B4CF7148663AF00873A4A /* DTFoundation.h in Headers */,
				A79BA89C1B46EDDD0086C2F6 /* DTAnimatedGIF.h in Headers */,
				A77DD41714E825FC00F34B03 /* crypt.h in Headers */,
				A77DD41B14E825FC00F34B03 /* ioapi.h in Headers */,
				A70BCEC71BF4A61A000CD60D /* NSObject+DTRuntime.h in Headers */,
				A77DD41F14E825FC00F34B03 /* mztools.h in Headers */,
				A77DD42314E825FC00F34B03 /* unzip.h in Headers */,
				A77DD42714E825FC00F34B03 /* zip.h in Headers */,
				A76DB4A016A5E37F0010CD85 /* DTActivityTitleView.h in Headers */,
				A76DB4AC16A5E37F0010CD85 /* DTPieProgressIndicator.h in Headers */,
				A70BCEC61BF4A5F0000CD60D /* DTObjectBlockExecutor.h in Headers */,
				A7F8CDA51B4BE671007DAD63 /* NSURL+DTComparing.h in Headers */,
				A76DB4B216A5E37F0010CD85 /* DTSmartPagingScrollView.h in Headers */,
				A76DB4CD16A5E5100010CD85 /* NSURL+DTAppLinks.h in Headers */,
				A7F8CDB31B4BE67E007DAD63 /* NSDictionary+DTError.h in Headers */,
				A74988811B6EA91C00A66234 /* DTProgressHUD.h in Headers */,
				A7F8CDB61B4BE67E007DAD63 /* NSString+DTFormatNumbers.h in Headers */,
				A7F8CDB21B4BE67E007DAD63 /* NSData+DTCrypto.h in Headers */,
				A76DB4D116A5E5100010CD85 /* UIApplication+DTNetworkActivity.h in Headers */,
				A74988871B6EAB0900A66234 /* DTBlockFunctions.h in Headers */,
				A76DB4D516A5E5100010CD85 /* UIImage+DTFoundation.h in Headers */,
				A76DB4DD16A5E5100010CD85 /* UIView+DTFoundation.h in Headers */,
				A7F8CDB81B4BE67E007DAD63 /* NSString+DTURLEncoding.h in Headers */,
				A7E88ED316BC0278008CBA9C /* DTCustomColoredAccessory.h in Headers */,
				A7C92B6A174F9AEC0019D70A /* UIViewController+DTSidePanelController.h in Headers */,
				A7FB1216175C9C8B00D4B7F0 /* DTSidePanelController.h in Headers */,
				A79BA7E91B46C5060086C2F6 /* NSString+DTUtilities.h in Headers */,
				A79BA89A1B46ED880086C2F6 /* DTHTMLParser.h in Headers */,
				A7F8CD9D1B4BD8F7007DAD63 /* DTCoreGraphicsUtils.h in Headers */,
				A79BA7E71B46C4270086C2F6 /* DTWeakSupport.h in Headers */,
				A730BCCF16D2892E003B849F /* DTTiledLayerWithoutFade.h in Headers */,
				A7F8CDB91B4BE67E007DAD63 /* NSURL+DTUnshorten.h in Headers */,
				A79BA7D01B46C3EF0086C2F6 /* DTLog.h in Headers */,
				A7F8CDB41B4BE67E007DAD63 /* NSFileWrapper+DTCopying.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A710A4FE1607556000437D36 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A7F8CDBE1B4BE694007DAD63 /* NSView+DTAutoLayout.h in Headers */,
				A70BCEC91BF4A61C000CD60D /* NSObject+DTRuntime.h in Headers */,
				A79BA89B1B46ED880086C2F6 /* DTHTMLParser.h in Headers */,
				A7F8CDA71B4BE67D007DAD63 /* NSData+DTCrypto.h in Headers */,
				A7F8CDAD1B4BE67D007DAD63 /* NSString+DTURLEncoding.h in Headers */,
				A7F8CDBD1B4BE694007DAD63 /* NSValue+DTConversion.h in Headers */,
				A7F8CDBF1B4BE694007DAD63 /* NSWindowController+DTPanelControllerPresenting.h in Headers */,
				A7F8CDA81B4BE67D007DAD63 /* NSDictionary+DTError.h in Headers */,
				A7F8CDBA1B4BE694007DAD63 /* DTScrollView.h in Headers */,
				A79BA7E61B46C4260086C2F6 /* DTWeakSupport.h in Headers */,
				A7F8CD9C1B4BD8F7007DAD63 /* DTCoreGraphicsUtils.h in Headers */,
				A7F8CDAF1B4BE67D007DAD63 /* NSURL+DTUnshorten.h in Headers */,
				A7F8CDAB1B4BE67D007DAD63 /* NSString+DTFormatNumbers.h in Headers */,
				E2262E82163846AB00BFDAD7 /* DTFoundation.h in Headers */,
				A7F8CDAC1B4BE67D007DAD63 /* NSString+DTPaths.h in Headers */,
				A7F8CDA61B4BE67D007DAD63 /* NSArray+DTError.h in Headers */,
				A70BCEC41BF4A5EF000CD60D /* DTObjectBlockExecutor.h in Headers */,
				A7F8CDA41B4BE671007DAD63 /* NSURL+DTComparing.h in Headers */,
				A7F8CDBB1B4BE694007DAD63 /* NSDocument+DTFoundation.h in Headers */,
				A7F8CDAA1B4BE67D007DAD63 /* NSMutableArray+DTMoving.h in Headers */,
				A79BA7CF1B46C3EF0086C2F6 /* DTLog.h in Headers */,
				A7F8CDBC1B4BE694007DAD63 /* NSImage+DTUtilities.h in Headers */,
				A7F8CDA91B4BE67D007DAD63 /* NSFileWrapper+DTCopying.h in Headers */,
				A79BA7E81B46C5050086C2F6 /* NSString+DTUtilities.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A72121E8173BFFED003C6F0A /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A7C92B8A174FB9720019D70A /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A7E889A216A9B190009EF0DF /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A7EA07361A2B215200B61CCE /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A7D95AE01BC40DAB00AB8A21 /* DTASN1Parser.h in Headers */,
				A741709E1CEA10E600F15DA9 /* DTCompatibility.h in Headers */,
				A7EA07941A2B232500B61CCE /* DTExtendedFileAttributes.h in Headers */,
				A7E7997A1AB9AFA9008C2393 /* DTFoundation.h in Headers */,
				A7EA07591A2B220100B61CCE /* DTScrollView.h in Headers */,
				29DA9D861C6DC27600F5F22A /* DTReachability.h in Headers */,
				A7EA07AB1A2B259300B61CCE /* DTCoreGraphicsUtils.h in Headers */,
				A7EA07671A2B221300B61CCE /* NSData+DTCrypto.h in Headers */,
				A7EA07691A2B221300B61CCE /* NSDictionary+DTError.h in Headers */,
				A7EA079A1A2B232500B61CCE /* DTBlockFunctions.h in Headers */,
				A79BA5D81B46A0D50086C2F6 /* DTHTMLParser.h in Headers */,
				A7EA07931A2B232500B61CCE /* DTBase64Coding.h in Headers */,
				A7EA07981A2B232500B61CCE /* DTVersion.h in Headers */,
				A7EA07751A2B221300B61CCE /* NSString+DTURLEncoding.h in Headers */,
				A7EA07791A2B221300B61CCE /* NSURL+DTComparing.h in Headers */,
				A7EA07731A2B221300B61CCE /* NSString+DTPaths.h in Headers */,
				A7EA075B1A2B220100B61CCE /* NSDocument+DTFoundation.h in Headers */,
				A7EA07991A2B232500B61CCE /* DTWeakSupport.h in Headers */,
				A7EA075D1A2B220100B61CCE /* NSImage+DTUtilities.h in Headers */,
				A7D95AE21BC40DAB00AB8A21 /* DTASN1Serialization.h in Headers */,
				A7EA07B01A2B299D00B61CCE /* DTObjectBlockExecutor.h in Headers */,
				A7EA076F1A2B221300B61CCE /* NSString+DTFormatNumbers.h in Headers */,
				A7EA07631A2B220100B61CCE /* NSWindowController+DTPanelControllerPresenting.h in Headers */,
				A7EA07AD1A2B25B100B61CCE /* NSObject+DTRuntime.h in Headers */,
				A7EA07711A2B221300B61CCE /* NSString+DTUtilities.h in Headers */,
				A7EA07771A2B221300B61CCE /* NSURL+DTUnshorten.h in Headers */,
				A7EA07651A2B221300B61CCE /* NSArray+DTError.h in Headers */,
				A7EA07971A2B232500B61CCE /* DTLog.h in Headers */,
				A7EA07611A2B220100B61CCE /* NSView+DTAutoLayout.h in Headers */,
				A7EA076D1A2B221300B61CCE /* NSMutableArray+DTMoving.h in Headers */,
				A7EA075F1A2B220100B61CCE /* NSValue+DTConversion.h in Headers */,
				A7EA076B1A2B221300B61CCE /* NSFileWrapper+DTCopying.h in Headers */,
				A7EA07961A2B232500B61CCE /* DTFoundationConstants.h in Headers */,
				A7EA07921A2B232500B61CCE /* DTAsyncFileDeleter.h in Headers */,
				A7EA07951A2B232500B61CCE /* DTFolderMonitor.h in Headers */,
				A7D95ADE1BC40DAB00AB8A21 /* DTASN1BitString.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A7EA07B51A2B2F6A00B61CCE /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A7EA08301A2B318E00B61CCE /* DTWeakSupport.h in Headers */,
				A7EA07EA1A2B30F000B61CCE /* UIView+DTFoundation.h in Headers */,
				A7EA08061A2B316100B61CCE /* NSArray+DTError.h in Headers */,
				A7EA08161A2B316100B61CCE /* NSString+DTURLEncoding.h in Headers */,
				A7D95AE81BC40DAC00AB8A21 /* DTASN1Serialization.h in Headers */,
				A79BA6CA1B46A3E40086C2F6 /* DTAnimatedGIF.h in Headers */,
				A7EA07E21A2B30F000B61CCE /* DTSmartPagingScrollView.h in Headers */,
				29DA9D851C6DC27500F5F22A /* DTReachability.h in Headers */,
				A7EA07FA1A2B312300B61CCE /* DTActionSheet.h in Headers */,
				A7EA07EE1A2B310600B61CCE /* DTSidePanelController.h in Headers */,
				A7EA080C1A2B316100B61CCE /* NSFileWrapper+DTCopying.h in Headers */,
				A7EA08261A2B318E00B61CCE /* DTExtendedFileAttributes.h in Headers */,
				A79BA5D91B46A0D50086C2F6 /* DTHTMLParser.h in Headers */,
				A7EA08141A2B316100B61CCE /* NSString+DTPaths.h in Headers */,
				A7EA07DA1A2B30F000B61CCE /* DTTiledLayerWithoutFade.h in Headers */,
				A7EA082E1A2B318E00B61CCE /* DTVersion.h in Headers */,
				A7EA082A1A2B318E00B61CCE /* DTFoundationConstants.h in Headers */,
				A7EA07F01A2B310600B61CCE /* UIViewController+DTSidePanelController.h in Headers */,
				A7EA08081A2B316100B61CCE /* NSData+DTCrypto.h in Headers */,
				A7EA08181A2B316100B61CCE /* NSURL+DTUnshorten.h in Headers */,
				A7EA07E41A2B30F000B61CCE /* UIApplication+DTNetworkActivity.h in Headers */,
				A7EA081C1A2B317300B61CCE /* DTCoreGraphicsUtils.h in Headers */,
				A7EA080E1A2B316100B61CCE /* NSMutableArray+DTMoving.h in Headers */,
				A7D95AE61BC40DAC00AB8A21 /* DTASN1Parser.h in Headers */,
				A7EA07F41A2B310600B61CCE /* DTSidePanelControllerSegue.h in Headers */,
				A7EA081E1A2B318500B61CCE /* DTObjectBlockExecutor.h in Headers */,
				A7EA07DC1A2B30F000B61CCE /* DTActivityTitleView.h in Headers */,
				A7EA08201A2B318500B61CCE /* NSObject+DTRuntime.h in Headers */,
				A7EA07F31A2B310600B61CCE /* DTSidePanelPanGestureRecognizer.h in Headers */,
				A7EA082C1A2B318E00B61CCE /* DTLog.h in Headers */,
				A7EA08281A2B318E00B61CCE /* DTFolderMonitor.h in Headers */,
				A7EA07E01A2B30F000B61CCE /* DTPieProgressIndicator.h in Headers */,
				A7EA07E81A2B30F000B61CCE /* NSURL+DTAppLinks.h in Headers */,
				A7EA08101A2B316100B61CCE /* NSString+DTFormatNumbers.h in Headers */,
				A7E799791AB9AF3B008C2393 /* DTFoundation.h in Headers */,
				A7EA07FE1A2B312300B61CCE /* UIView+DTActionHandlers.h in Headers */,
				A7EA07DE1A2B30F000B61CCE /* DTCustomColoredAccessory.h in Headers */,
				A7EA08311A2B318E00B61CCE /* DTBlockFunctions.h in Headers */,
				A7EA08241A2B318E00B61CCE /* DTBase64Coding.h in Headers */,
				A7EA07F81A2B311700B61CCE /* UIView+DTDebug.h in Headers */,
				A7EA08121A2B316100B61CCE /* NSString+DTUtilities.h in Headers */,
				A7EA07F61A2B311700B61CCE /* UIColor+DTDebug.h in Headers */,
				A7EA081A1A2B316100B61CCE /* NSURL+DTComparing.h in Headers */,
				A7EA07FC1A2B312300B61CCE /* DTAlertView.h in Headers */,
				A74988821B6EA91F00A66234 /* DTProgressHUD.h in Headers */,
				A7EA08221A2B318E00B61CCE /* DTAsyncFileDeleter.h in Headers */,
				A7EA07E61A2B30F000B61CCE /* UIImage+DTFoundation.h in Headers */,
				A7EA080A1A2B316100B61CCE /* NSDictionary+DTError.h in Headers */,
				A7B505361B6A595700BC743A /* DTCompatibility.h in Headers */,
				A7D95AE41BC40DAC00AB8A21 /* DTASN1BitString.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		384470901BA3330F0037C68D /* Static Library (tvOS) */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 384470E71BA3330F0037C68D /* Build configuration list for PBXNativeTarget "Static Library (tvOS)" */;
			buildPhases = (
				384470911BA3330F0037C68D /* Sources */,
				384470BE1BA3330F0037C68D /* Frameworks */,
				384470C01BA3330F0037C68D /* Headers */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "Static Library (tvOS)";
			productName = DTFoundation;
			productReference = 384470EB1BA3330F0037C68D /* libStatic Library (tvOS).a */;
			productType = "com.apple.product-type.library.static";
		};
		384470EC1BA33ED80037C68D /* DTFoundation (tvOS) */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 384471511BA33ED80037C68D /* Build configuration list for PBXNativeTarget "DTFoundation (tvOS)" */;
			buildPhases = (
				384470ED1BA33ED80037C68D /* Sources */,
				384471191BA33ED80037C68D /* Frameworks */,
				384471201BA33ED80037C68D /* Headers */,
				384471501BA33ED80037C68D /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "DTFoundation (tvOS)";
			productName = DTFoundation_;
			productReference = 384471551BA33ED80037C68D /* DTFoundation.framework */;
			productType = "com.apple.product-type.framework";
		};
		3D7CE53D166613B60028D339 /* DTHTMLParser (Mac) */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 3D7CE544166613B60028D339 /* Build configuration list for PBXNativeTarget "DTHTMLParser (Mac)" */;
			buildPhases = (
				3D7CE53E166613B60028D339 /* Sources */,
				3D7CE540166613B60028D339 /* Frameworks */,
				3D7CE543166613B60028D339 /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "DTHTMLParser (Mac)";
			productName = DTHTMLParser;
			productReference = 3D7CE547166613B60028D339 /* libDTHTMLParser_Mac.a */;
			productType = "com.apple.product-type.library.static";
		};
		A70B4CE61486637E00873A4A /* Static Library */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A70B4CEF1486637E00873A4A /* Build configuration list for PBXNativeTarget "Static Library" */;
			buildPhases = (
				A70B4CE31486637E00873A4A /* Sources */,
				A70B4CE41486637E00873A4A /* Frameworks */,
				A70B4CE51486637E00873A4A /* Headers */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "Static Library";
			productName = DTFoundation;
			productReference = A70B4CE71486637E00873A4A /* libDTFoundation.a */;
			productType = "com.apple.product-type.library.static";
		};
		A70D74CF1743AFBC00E6E626 /* DTSidePanels Demo */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A70D74E61743AFBC00E6E626 /* Build configuration list for PBXNativeTarget "DTSidePanels Demo" */;
			buildPhases = (
				A70D74CC1743AFBC00E6E626 /* Sources */,
				A70D74CD1743AFBC00E6E626 /* Frameworks */,
				A70D74CE1743AFBC00E6E626 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				A70D74F01743AFD700E6E626 /* PBXTargetDependency */,
			);
			name = "DTSidePanels Demo";
			productName = "DTSidePanels Demo";
			productReference = A70D74D01743AFBC00E6E626 /* DTSidePanels Demo.app */;
			productType = "com.apple.product-type.application";
		};
		A710A4FF1607556000437D36 /* Static Library (Mac) */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A710A5081607556000437D36 /* Build configuration list for PBXNativeTarget "Static Library (Mac)" */;
			buildPhases = (
				A710A4FC1607556000437D36 /* Sources */,
				A710A4FD1607556000437D36 /* Frameworks */,
				A710A4FE1607556000437D36 /* Headers */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "Static Library (Mac)";
			productName = DTFoundation_Mac;
			productReference = A710A5001607556000437D36 /* libDTFoundation_Mac.a */;
			productType = "com.apple.product-type.library.static";
		};
		A72121D9173BFFB1003C6F0A /* DTReachability (iOS) */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A72121E3173BFFB1003C6F0A /* Build configuration list for PBXNativeTarget "DTReachability (iOS)" */;
			buildPhases = (
				A72121D6173BFFB1003C6F0A /* Sources */,
				A72121D7173BFFB1003C6F0A /* Frameworks */,
				A72121D8173BFFB1003C6F0A /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "DTReachability (iOS)";
			productName = DTReachability;
			productReference = A72121DA173BFFB1003C6F0A /* libDTReachability_iOS.a */;
			productType = "com.apple.product-type.library.static";
		};
		A72121E9173BFFED003C6F0A /* DTReachability (Mac) */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A72121F2173BFFED003C6F0A /* Build configuration list for PBXNativeTarget "DTReachability (Mac)" */;
			buildPhases = (
				A72121E6173BFFED003C6F0A /* Sources */,
				A72121E7173BFFED003C6F0A /* Frameworks */,
				A72121E8173BFFED003C6F0A /* Headers */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "DTReachability (Mac)";
			productName = DTReachability;
			productReference = A72121EA173BFFED003C6F0A /* libDTReachability_Mac.a */;
			productType = "com.apple.product-type.library.static";
		};
		A78220B9168060CA005B602D /* DTUTI (iOS) */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A78220C5168060CA005B602D /* Build configuration list for PBXNativeTarget "DTUTI (iOS)" */;
			buildPhases = (
				A78220B6168060CA005B602D /* Sources */,
				A78220B7168060CA005B602D /* Frameworks */,
				A78220B8168060CA005B602D /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "DTUTI (iOS)";
			productName = DTUTI;
			productReference = A78220BA168060CA005B602D /* libDTUTI_iOS.a */;
			productType = "com.apple.product-type.library.static";
		};
		A78381C21963F8D700AF09D3 /* DTAnimatedGIF Demo */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A78381E31963F8D700AF09D3 /* Build configuration list for PBXNativeTarget "DTAnimatedGIF Demo" */;
			buildPhases = (
				A78381BF1963F8D700AF09D3 /* Sources */,
				A78381C01963F8D700AF09D3 /* Frameworks */,
				A78381C11963F8D700AF09D3 /* Resources */,
				A7EA07D51A2B2F6B00B61CCE /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				A7AC21CB196417CC0009E1B9 /* PBXTargetDependency */,
				A7EA07CE1A2B2F6B00B61CCE /* PBXTargetDependency */,
			);
			name = "DTAnimatedGIF Demo";
			productName = "DTAnimatedGIF Demo";
			productReference = A78381C31963F8D700AF09D3 /* DTAnimatedGIF Demo.app */;
			productType = "com.apple.product-type.application";
		};
		A7917C76191A312600964D63 /* Unit Tests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A7917C86191A312700964D63 /* Build configuration list for PBXNativeTarget "Unit Tests" */;
			buildPhases = (
				A7917C73191A312600964D63 /* Sources */,
				A7917C74191A312600964D63 /* Frameworks */,
				A7917C75191A312600964D63 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				A7E77947253D9E5600E011D3 /* PBXTargetDependency */,
			);
			name = "Unit Tests";
			packageProductDependencies = (
			);
			productName = "Unit Tests";
			productReference = A7917C77191A312600964D63 /* Unit Tests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		A79D999B1792D1F50082BC06 /* DTAWS (iOS) */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A79D99BD1792D1F60082BC06 /* Build configuration list for PBXNativeTarget "DTAWS (iOS)" */;
			buildPhases = (
				A79D99981792D1F50082BC06 /* Sources */,
				A79D99991792D1F50082BC06 /* Frameworks */,
				A79D999A1792D1F50082BC06 /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "DTAWS (iOS)";
			productName = DTAWS;
			productReference = A79D999C1792D1F50082BC06 /* libDTAWS.a */;
			productType = "com.apple.product-type.library.static";
		};
		A7AC21AE196417830009E1B9 /* DTAnimatedGIF (iOS) */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A7AC21C0196417830009E1B9 /* Build configuration list for PBXNativeTarget "DTAnimatedGIF (iOS)" */;
			buildPhases = (
				A7AC21AB196417830009E1B9 /* Sources */,
				A7AC21AC196417830009E1B9 /* Frameworks */,
				A7AC21AD196417830009E1B9 /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "DTAnimatedGIF (iOS)";
			productName = DTAnimatedGIF;
			productReference = A7AC21AF196417830009E1B9 /* libDTAnimatedGIF.a */;
			productType = "com.apple.product-type.library.static";
		};
		A7C92B79174FB8900019D70A /* DTSQLite (iOS) */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A7C92B83174FB8900019D70A /* Build configuration list for PBXNativeTarget "DTSQLite (iOS)" */;
			buildPhases = (
				A7C92B76174FB8900019D70A /* Sources */,
				A7C92B77174FB8900019D70A /* Frameworks */,
				A7C92B78174FB8900019D70A /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "DTSQLite (iOS)";
			productName = DTSQLite;
			productReference = A7C92B7A174FB8900019D70A /* libDTSQLite_iOS.a */;
			productType = "com.apple.product-type.library.static";
		};
		A7C92B8B174FB9720019D70A /* DTSQLite (Mac) */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A7C92B94174FB9720019D70A /* Build configuration list for PBXNativeTarget "DTSQLite (Mac)" */;
			buildPhases = (
				A7C92B88174FB9720019D70A /* Sources */,
				A7C92B89174FB9720019D70A /* Frameworks */,
				A7C92B8A174FB9720019D70A /* Headers */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "DTSQLite (Mac)";
			productName = DTSQLite;
			productReference = A7C92B8C174FB9720019D70A /* libDTSQLite_Mac.a */;
			productType = "com.apple.product-type.library.static";
		};
		A7D8C7AF1723174600025675 /* DTZipArchive (Mac) */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A7D8C7BF1723174600025675 /* Build configuration list for PBXNativeTarget "DTZipArchive (Mac)" */;
			buildPhases = (
				A7D8C7B01723174600025675 /* Sources */,
				A7D8C7B91723174600025675 /* Frameworks */,
				A7D8C7BB1723174600025675 /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "DTZipArchive (Mac)";
			productName = DTZipArchive;
			productReference = A7D8C7C21723174600025675 /* libDTZipArchive_Mac.a */;
			productType = "com.apple.product-type.library.static";
		};
		A7D9D62C1758C89800C29B6F /* DTReachability Demo */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A7D9D6491758C89900C29B6F /* Build configuration list for PBXNativeTarget "DTReachability Demo" */;
			buildPhases = (
				A7D9D6291758C89800C29B6F /* Sources */,
				A7D9D62A1758C89800C29B6F /* Frameworks */,
				A7D9D62B1758C89800C29B6F /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				A74A764F17BBB15D007073C8 /* PBXTargetDependency */,
				A7D9D6611758CA5400C29B6F /* PBXTargetDependency */,
			);
			name = "DTReachability Demo";
			productName = "DTReachability Demo";
			productReference = A7D9D62D1758C89800C29B6F /* DTReachability Demo.app */;
			productType = "com.apple.product-type.application";
		};
		A7E383C1160DFEDB00CF72D6 /* DTHTMLParser (iOS) */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A7E383CD160DFEDB00CF72D6 /* Build configuration list for PBXNativeTarget "DTHTMLParser (iOS)" */;
			buildPhases = (
				A7E383BE160DFEDB00CF72D6 /* Sources */,
				A7E383BF160DFEDB00CF72D6 /* Frameworks */,
				A7E383C0160DFEDB00CF72D6 /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "DTHTMLParser (iOS)";
			productName = DTHTMLParser;
			productReference = A7E383C2160DFEDB00CF72D6 /* libDTHTMLParser_iOS.a */;
			productType = "com.apple.product-type.library.static";
		};
		A7E383D9160DFF8600CF72D6 /* DTZipArchive (iOS) */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A7E383E3160DFF8600CF72D6 /* Build configuration list for PBXNativeTarget "DTZipArchive (iOS)" */;
			buildPhases = (
				A7E383D6160DFF8600CF72D6 /* Sources */,
				A7E383D7160DFF8600CF72D6 /* Frameworks */,
				A7E383D8160DFF8600CF72D6 /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "DTZipArchive (iOS)";
			productName = DTZipArchive;
			productReference = A7E383DA160DFF8600CF72D6 /* libDTZipArchive_iOS.a */;
			productType = "com.apple.product-type.library.static";
		};
		A7E889A316A9B190009EF0DF /* DTUTI (Mac) */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A7E889B116A9B190009EF0DF /* Build configuration list for PBXNativeTarget "DTUTI (Mac)" */;
			buildPhases = (
				A7E889A016A9B190009EF0DF /* Sources */,
				A7E889A116A9B190009EF0DF /* Frameworks */,
				A7E889A216A9B190009EF0DF /* Headers */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "DTUTI (Mac)";
			productName = DTUTI;
			productReference = A7E889A416A9B190009EF0DF /* libDTUTI_Mac.a */;
			productType = "com.apple.product-type.library.static";
		};
		A7EA07381A2B215200B61CCE /* DTFoundation (OSX) */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A7EA07521A2B215300B61CCE /* Build configuration list for PBXNativeTarget "DTFoundation (OSX)" */;
			buildPhases = (
				A7EA07341A2B215200B61CCE /* Sources */,
				A7EA07351A2B215200B61CCE /* Frameworks */,
				A7EA07361A2B215200B61CCE /* Headers */,
				A7EA07371A2B215200B61CCE /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "DTFoundation (OSX)";
			productName = "DTFoundation (Mac)";
			productReference = A7EA07391A2B215200B61CCE /* DTFoundation.framework */;
			productType = "com.apple.product-type.framework";
		};
		A7EA07B71A2B2F6A00B61CCE /* DTFoundation (iOS) */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A7EA07D11A2B2F6B00B61CCE /* Build configuration list for PBXNativeTarget "DTFoundation (iOS)" */;
			buildPhases = (
				A7EA07B31A2B2F6A00B61CCE /* Sources */,
				A7EA07B41A2B2F6A00B61CCE /* Frameworks */,
				A7EA07B51A2B2F6A00B61CCE /* Headers */,
				A7EA07B61A2B2F6A00B61CCE /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "DTFoundation (iOS)";
			productName = DTFoundation_;
			productReference = A7EA07B81A2B2F6A00B61CCE /* DTFoundation.framework */;
			productType = "com.apple.product-type.framework";
		};
		F88FF6771920A83300120808 /* DTProgressHUD (iOS) */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F88FF67C1920A83300120808 /* Build configuration list for PBXNativeTarget "DTProgressHUD (iOS)" */;
			buildPhases = (
				F88FF6781920A83300120808 /* Sources */,
				F88FF67A1920A83300120808 /* Frameworks */,
				F88FF67B1920A83300120808 /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "DTProgressHUD (iOS)";
			productName = DTReachability;
			productReference = F88FF6801920A83300120808 /* libDTProgressHUD_iOS.a */;
			productType = "com.apple.product-type.library.static";
		};
		F88FF6861920AAB600120808 /* DTProgressHUD Demo */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F88FF6A11920AAB600120808 /* Build configuration list for PBXNativeTarget "DTProgressHUD Demo" */;
			buildPhases = (
				F88FF68B1920AAB600120808 /* Sources */,
				F88FF6931920AAB600120808 /* Frameworks */,
				F88FF69C1920AAB600120808 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				F88FF6CB1920AF1F00120808 /* PBXTargetDependency */,
				F88FF6C51920ABD900120808 /* PBXTargetDependency */,
			);
			name = "DTProgressHUD Demo";
			productName = DTZipArchiveDemo;
			productReference = F88FF6A51920AAB600120808 /* DTProgressHUD Demo.app */;
			productType = "com.apple.product-type.application";
		};
		FA9CB80B17ABDEF200A596C5 /* DTASN1 (iOS) */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = FA9CB81717ABDEF200A596C5 /* Build configuration list for PBXNativeTarget "DTASN1 (iOS)" */;
			buildPhases = (
				FA9CB80817ABDEF200A596C5 /* Sources */,
				FA9CB80917ABDEF200A596C5 /* Frameworks */,
				FA9CB80A17ABDEF200A596C5 /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "DTASN1 (iOS)";
			productName = DTASN1;
			productReference = FA9CB80C17ABDEF200A596C5 /* libDTASN1.a */;
			productType = "com.apple.product-type.library.static";
		};
		FAC284C719B84B5F0010C385 /* DTActionSheet Demo */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = FAC284E219B84B5F0010C385 /* Build configuration list for PBXNativeTarget "DTActionSheet Demo" */;
			buildPhases = (
				FAC284CC19B84B5F0010C385 /* Sources */,
				FAC284D419B84B5F0010C385 /* Frameworks */,
				FAC284DD19B84B5F0010C385 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				FAC284C819B84B5F0010C385 /* PBXTargetDependency */,
				FAC284CA19B84B5F0010C385 /* PBXTargetDependency */,
			);
			name = "DTActionSheet Demo";
			productName = DTZipArchiveDemo;
			productReference = FAC284E619B84B5F0010C385 /* DTActionSheet Demo.app */;
			productType = "com.apple.product-type.application";
		};
		FAF37A1F17AFDD93009AC27C /* DTZipArchive Demo */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = FAF37A3817AFDD93009AC27C /* Build configuration list for PBXNativeTarget "DTZipArchive Demo" */;
			buildPhases = (
				FAF37A1C17AFDD93009AC27C /* Sources */,
				FAF37A1D17AFDD93009AC27C /* Frameworks */,
				FAF37A1E17AFDD93009AC27C /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				FAF37A7217AFE0CC009AC27C /* PBXTargetDependency */,
				FAF37A5917AFDF5D009AC27C /* PBXTargetDependency */,
			);
			name = "DTZipArchive Demo";
			productName = DTZipArchiveDemo;
			productReference = FAF37A2017AFDD93009AC27C /* DTZipArchive.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		A7F4DF9A147FB61500F4059A /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastTestingUpgradeCheck = 0510;
				LastUpgradeCheck = 1300;
				ORGANIZATIONNAME = Cocoanetics;
				TargetAttributes = {
					A78381C21963F8D700AF09D3 = {
						CreatedOnToolsVersion = 6.0;
					};
					A7917C76191A312600964D63 = {
						TestTargetID = A7F4DFED147FBAC600F4059A;
					};
					A7AC21AE196417830009E1B9 = {
						CreatedOnToolsVersion = 6.0;
					};
					A7EA07381A2B215200B61CCE = {
						CreatedOnToolsVersion = 6.1;
					};
					A7EA07B71A2B2F6A00B61CCE = {
						CreatedOnToolsVersion = 6.1;
						DevelopmentTeam = Z7L2YCUH45;
						ProvisioningStyle = Automatic;
					};
				};
			};
			buildConfigurationList = A7F4DF9D147FB61500F4059A /* Build configuration list for PBXProject "DTFoundation" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = A7F4DF98147FB61500F4059A;
			productRefGroup = A7F4DFA6147FB61500F4059A /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				A7F4DFED147FBAC600F4059A /* Documentation */,
				A70B4CE61486637E00873A4A /* Static Library */,
				384470901BA3330F0037C68D /* Static Library (tvOS) */,
				A710A4FF1607556000437D36 /* Static Library (Mac) */,
				A7AC21AE196417830009E1B9 /* DTAnimatedGIF (iOS) */,
				A79D999B1792D1F50082BC06 /* DTAWS (iOS) */,
				FA9CB80B17ABDEF200A596C5 /* DTASN1 (iOS) */,
				A7E383C1160DFEDB00CF72D6 /* DTHTMLParser (iOS) */,
				3D7CE53D166613B60028D339 /* DTHTMLParser (Mac) */,
				F88FF6771920A83300120808 /* DTProgressHUD (iOS) */,
				A72121D9173BFFB1003C6F0A /* DTReachability (iOS) */,
				A72121E9173BFFED003C6F0A /* DTReachability (Mac) */,
				A7C92B79174FB8900019D70A /* DTSQLite (iOS) */,
				A7C92B8B174FB9720019D70A /* DTSQLite (Mac) */,
				A78220B9168060CA005B602D /* DTUTI (iOS) */,
				A7E889A316A9B190009EF0DF /* DTUTI (Mac) */,
				A7E383D9160DFF8600CF72D6 /* DTZipArchive (iOS) */,
				A7D8C7AF1723174600025675 /* DTZipArchive (Mac) */,
				A78381C21963F8D700AF09D3 /* DTAnimatedGIF Demo */,
				F88FF6861920AAB600120808 /* DTProgressHUD Demo */,
				A7D9D62C1758C89800C29B6F /* DTReachability Demo */,
				A70D74CF1743AFBC00E6E626 /* DTSidePanels Demo */,
				FAF37A1F17AFDD93009AC27C /* DTZipArchive Demo */,
				FAC284C719B84B5F0010C385 /* DTActionSheet Demo */,
				A7917C76191A312600964D63 /* Unit Tests */,
				A7EA07B71A2B2F6A00B61CCE /* DTFoundation (iOS) */,
				A7EA07381A2B215200B61CCE /* DTFoundation (OSX) */,
				384470EC1BA33ED80037C68D /* DTFoundation (tvOS) */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		384471501BA33ED80037C68D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A70D74CE1743AFBC00E6E626 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A78381C11963F8D700AF09D3 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A7917C75191A312600964D63 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A7D9D62B1758C89800C29B6F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A7EA07371A2B215200B61CCE /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A7EA07B61A2B2F6A00B61CCE /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F88FF69C1920AAB600120808 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FAC284DD19B84B5F0010C385 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FAF37A1E17AFDD93009AC27C /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		A7F4DFF1147FBB0B00F4059A /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "/usr/local/bin/appledoc --print-settings --output \"${BUILD_DIR}/Documentation/\" \"${PROJECT_DIR}\"\necho \"Documentation Output directory: ${BUILD_DIR}/Documentation/\"";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		384470911BA3330F0037C68D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				384470921BA3330F0037C68D /* DTVersion.m in Sources */,
				384470931BA3330F0037C68D /* DTFolderMonitor.m in Sources */,
				384470941BA3330F0037C68D /* NSString+DTFormatNumbers.m in Sources */,
				384470951BA3330F0037C68D /* NSString+DTPaths.m in Sources */,
				384470961BA3330F0037C68D /* NSDictionary+DTError.m in Sources */,
				384470971BA3330F0037C68D /* NSString+DTURLEncoding.m in Sources */,
				384470981BA3330F0037C68D /* NSString+DTUtilities.m in Sources */,
				384470991BA3330F0037C68D /* DTCoreGraphicsUtils.m in Sources */,
				3844709A1BA3330F0037C68D /* NSArray+DTError.m in Sources */,
				3844709B1BA3330F0037C68D /* NSURL+DTUnshorten.m in Sources */,
				3844709C1BA3330F0037C68D /* NSMutableArray+DTMoving.m in Sources */,
				3844709D1BA3330F0037C68D /* DTSidePanelControllerSegue.m in Sources */,
				3844709E1BA3330F0037C68D /* NSData+DTCrypto.m in Sources */,
				3844709F1BA3330F0037C68D /* DTBlockFunctions.m in Sources */,
				384470A01BA3330F0037C68D /* UIView+DTActionHandlers.m in Sources */,
				384470A11BA3330F0037C68D /* DTAsyncFileDeleter.m in Sources */,
				384470A21BA3330F0037C68D /* NSFileWrapper+DTCopying.m in Sources */,
				384470A31BA3330F0037C68D /* NSURL+DTComparing.m in Sources */,
				384470A41BA3330F0037C68D /* DTAlertView.m in Sources */,
				384470A51BA3330F0037C68D /* DTActivityTitleView.m in Sources */,
				384470A61BA3330F0037C68D /* DTLog.m in Sources */,
				384470A71BA3330F0037C68D /* DTPieProgressIndicator.m in Sources */,
				384470A81BA3330F0037C68D /* DTSmartPagingScrollView.m in Sources */,
				384470A91BA3330F0037C68D /* NSURL+DTAppLinks.m in Sources */,
				384470AA1BA3330F0037C68D /* DTFoundationConstants.m in Sources */,
				384470AB1BA3330F0037C68D /* UIApplication+DTNetworkActivity.m in Sources */,
				384470AC1BA3330F0037C68D /* DTActionSheet.m in Sources */,
				384470AD1BA3330F0037C68D /* UIImage+DTFoundation.m in Sources */,
				384470AE1BA3330F0037C68D /* UIView+DTFoundation.m in Sources */,
				384470B01BA3330F0037C68D /* DTSidePanelPanGestureRecognizer.m in Sources */,
				384470B11BA3330F0037C68D /* DTCustomColoredAccessory.m in Sources */,
				384470B21BA3330F0037C68D /* DTTiledLayerWithoutFade.m in Sources */,
				384470B31BA3330F0037C68D /* UIViewController+DTSidePanelController.m in Sources */,
				384470B41BA3330F0037C68D /* UIColor+DTDebug.m in Sources */,
				384470B51BA3330F0037C68D /* DTBase64Coding.m in Sources */,
				384470B61BA3330F0037C68D /* UIView+DTDebug.m in Sources */,
				384470B71BA3330F0037C68D /* DTScriptExpression.m in Sources */,
				384470B81BA3330F0037C68D /* DTScriptVariable.m in Sources */,
				384470B91BA3330F0037C68D /* DTExtendedFileAttributes.m in Sources */,
				384470BA1BA3330F0037C68D /* NSScanner+DTScripting.m in Sources */,
				384470BB1BA3330F0037C68D /* DTObjectBlockExecutor.m in Sources */,
				384470BC1BA3330F0037C68D /* NSObject+DTRuntime.m in Sources */,
				384470BD1BA3330F0037C68D /* DTSidePanelController.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		384470ED1BA33ED80037C68D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				384470EE1BA33ED80037C68D /* DTActivityTitleView.m in Sources */,
				384470EF1BA33ED80037C68D /* DTHTMLParser.m in Sources */,
				384470F01BA33ED80037C68D /* NSURL+DTComparing.m in Sources */,
				384470F11BA33ED80037C68D /* NSFileWrapper+DTCopying.m in Sources */,
				384470F21BA33ED80037C68D /* NSArray+DTError.m in Sources */,
				384470F31BA33ED80037C68D /* DTFoundationConstants.m in Sources */,
				384470F41BA33ED80037C68D /* NSURL+DTUnshorten.m in Sources */,
				384470F51BA33ED80037C68D /* DTCustomColoredAccessory.m in Sources */,
				384470F61BA33ED80037C68D /* DTTiledLayerWithoutFade.m in Sources */,
				384470F71BA33ED80037C68D /* NSData+DTCrypto.m in Sources */,
				384470F81BA33ED80037C68D /* DTAlertView.m in Sources */,
				384470F91BA33ED80037C68D /* UIView+DTActionHandlers.m in Sources */,
				384470FA1BA33ED80037C68D /* DTSidePanelController.m in Sources */,
				384470FB1BA33ED80037C68D /* DTAnimatedGIF.m in Sources */,
				384470FC1BA33ED80037C68D /* NSString+DTFormatNumbers.m in Sources */,
				384470FD1BA33ED80037C68D /* UIView+DTFoundation.m in Sources */,
				384470FE1BA33ED80037C68D /* DTBase64Coding.m in Sources */,
				384470FF1BA33ED80037C68D /* DTSmartPagingScrollView.m in Sources */,
				384471001BA33ED80037C68D /* NSString+DTUtilities.m in Sources */,
				384471011BA33ED80037C68D /* DTSidePanelPanGestureRecognizer.m in Sources */,
				384471021BA33ED80037C68D /* DTAsyncFileDeleter.m in Sources */,
				384471031BA33ED80037C68D /* UIApplication+DTNetworkActivity.m in Sources */,
				384471041BA33ED80037C68D /* DTVersion.m in Sources */,
				384471061BA33ED80037C68D /* DTLog.m in Sources */,
				384471071BA33ED80037C68D /* NSString+DTURLEncoding.m in Sources */,
				384471081BA33ED80037C68D /* UIViewController+DTSidePanelController.m in Sources */,
				384471091BA33ED80037C68D /* DTPieProgressIndicator.m in Sources */,
				3844710A1BA33ED80037C68D /* NSDictionary+DTError.m in Sources */,
				3844710B1BA33ED80037C68D /* NSMutableArray+DTMoving.m in Sources */,
				3844710C1BA33ED80037C68D /* DTExtendedFileAttributes.m in Sources */,
				A7D95ADB1BC40DAB00AB8A21 /* DTASN1Parser.m in Sources */,
				29DA9D7D1C6DC22800F5F22A /* DTReachability.m in Sources */,
				3844710D1BA33ED80037C68D /* NSObject+DTRuntime.m in Sources */,
				3844710E1BA33ED80037C68D /* NSURL+DTAppLinks.m in Sources */,
				3844710F1BA33ED80037C68D /* UIImage+DTFoundation.m in Sources */,
				384471101BA33ED80037C68D /* DTSidePanelControllerSegue.m in Sources */,
				384471111BA33ED80037C68D /* DTFolderMonitor.m in Sources */,
				384471121BA33ED80037C68D /* UIView+DTDebug.m in Sources */,
				384471131BA33ED80037C68D /* DTObjectBlockExecutor.m in Sources */,
				384471141BA33ED80037C68D /* DTActionSheet.m in Sources */,
				384471151BA33ED80037C68D /* DTBlockFunctions.m in Sources */,
				A7D95ADD1BC40DAB00AB8A21 /* DTASN1Serialization.m in Sources */,
				384471161BA33ED80037C68D /* NSString+DTPaths.m in Sources */,
				A7D95AD91BC40DAB00AB8A21 /* DTASN1BitString.m in Sources */,
				384471171BA33ED80037C68D /* DTCoreGraphicsUtils.m in Sources */,
				384471181BA33ED80037C68D /* UIColor+DTDebug.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3D7CE53E166613B60028D339 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A76DB48C16A5E1D20010CD85 /* DTHTMLParser.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A70B4CE31486637E00873A4A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A7EA078E1A2B230F00B61CCE /* DTVersion.m in Sources */,
				A7EA07881A2B230F00B61CCE /* DTFolderMonitor.m in Sources */,
				A7A7CC7A14866CAF00EC2EE4 /* NSString+DTFormatNumbers.m in Sources */,
				A7D8628414EBF65C001436AF /* NSString+DTPaths.m in Sources */,
				A7D0AA28153C1B160020F18B /* NSDictionary+DTError.m in Sources */,
				A7D0AA2E153C1FE40020F18B /* NSString+DTURLEncoding.m in Sources */,
				A7D0AA4D153C233E0020F18B /* NSString+DTUtilities.m in Sources */,
				A7D0AA70153C39920020F18B /* DTCoreGraphicsUtils.m in Sources */,
				A73D5BAE155271FD0024BDB7 /* NSArray+DTError.m in Sources */,
				A79231D1157A0B9400C3ACBB /* NSURL+DTUnshorten.m in Sources */,
				A766136416143F8A00DF6C2B /* NSMutableArray+DTMoving.m in Sources */,
				C9826825196DAD8600A84677 /* DTSidePanelControllerSegue.m in Sources */,
				A7950101161D680000358BC3 /* NSData+DTCrypto.m in Sources */,
				A7EA07911A2B230F00B61CCE /* DTBlockFunctions.m in Sources */,
				F88FF6DF1920F1AF00120808 /* UIView+DTActionHandlers.m in Sources */,
				A7EA07821A2B230F00B61CCE /* DTAsyncFileDeleter.m in Sources */,
				A73B6F8F163169BB002CCCA7 /* NSFileWrapper+DTCopying.m in Sources */,
				A7FAA38B1652291D006ED151 /* NSURL+DTComparing.m in Sources */,
				F88FF6DD1920F1AF00120808 /* DTAlertView.m in Sources */,
				A76DB4A216A5E37F0010CD85 /* DTActivityTitleView.m in Sources */,
				A7EA078C1A2B230F00B61CCE /* DTLog.m in Sources */,
				A76DB4AE16A5E37F0010CD85 /* DTPieProgressIndicator.m in Sources */,
				A76DB4B416A5E37F0010CD85 /* DTSmartPagingScrollView.m in Sources */,
				A76DB4CF16A5E5100010CD85 /* NSURL+DTAppLinks.m in Sources */,
				A7EA078A1A2B230F00B61CCE /* DTFoundationConstants.m in Sources */,
				A76DB4D316A5E5100010CD85 /* UIApplication+DTNetworkActivity.m in Sources */,
				F88FF6DB1920F1AF00120808 /* DTActionSheet.m in Sources */,
				A76DB4D716A5E5100010CD85 /* UIImage+DTFoundation.m in Sources */,
				A76DB4DF16A5E5100010CD85 /* UIView+DTFoundation.m in Sources */,
				A7F3BD2C18436A0F00DB7854 /* DTSidePanelPanGestureRecognizer.m in Sources */,
				A7E88ED516BC0278008CBA9C /* DTCustomColoredAccessory.m in Sources */,
				A730BCD116D2892E003B849F /* DTTiledLayerWithoutFade.m in Sources */,
				A7C92B6C174F9AEC0019D70A /* UIViewController+DTSidePanelController.m in Sources */,
				A70C4FCC17AA7CEC00000DF5 /* UIColor+DTDebug.m in Sources */,
				A7EA07841A2B230F00B61CCE /* DTBase64Coding.m in Sources */,
				A70C4FCD17AA7CEC00000DF5 /* UIView+DTDebug.m in Sources */,
				A70C4FE817AA7F0200000DF5 /* DTScriptExpression.m in Sources */,
				A70C4FE917AA7F0200000DF5 /* DTScriptVariable.m in Sources */,
				A7EA07861A2B230F00B61CCE /* DTExtendedFileAttributes.m in Sources */,
				A70C4FEA17AA7F0200000DF5 /* NSScanner+DTScripting.m in Sources */,
				A70C4FF117AA7F2000000DF5 /* DTObjectBlockExecutor.m in Sources */,
				A70C4FF217AA7F2000000DF5 /* NSObject+DTRuntime.m in Sources */,
				A7F3BD2D18436A1200DB7854 /* DTSidePanelController.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A70D74CC1743AFBC00E6E626 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A710A4FC1607556000437D36 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A710A50B160755A500437D36 /* NSDictionary+DTError.m in Sources */,
				FA2A29291609F7330023CDD7 /* DTVersion.m in Sources */,
				A766136616143F8A00DF6C2B /* NSMutableArray+DTMoving.m in Sources */,
				A76613E5161444FD00DF6C2B /* NSString+DTUtilities.m in Sources */,
				A7437DE216147A450091C1A2 /* NSString+DTPaths.m in Sources */,
				A792968F1619F0FA00D5C979 /* DTCoreGraphicsUtils.m in Sources */,
				A7F8CDB01B4BE67D007DAD63 /* NSURL+DTUnshorten.m in Sources */,
				A7950102161D680000358BC3 /* NSData+DTCrypto.m in Sources */,
				A7444D2C162C011C00DD3692 /* NSArray+DTError.m in Sources */,
				FAB1727116301F0D00B44EDC /* DTFoundationConstants.m in Sources */,
				A73B6F90163169BB002CCCA7 /* NSFileWrapper+DTCopying.m in Sources */,
				A73B6FA716318EBC002CCCA7 /* NSString+DTFormatNumbers.m in Sources */,
				A7FAA38C1652291D006ED151 /* NSURL+DTComparing.m in Sources */,
				A7F8CDAE1B4BE67D007DAD63 /* NSString+DTURLEncoding.m in Sources */,
				A76DB4BB16A5E3B20010CD85 /* DTScrollView.m in Sources */,
				A768BE4117FC3C91008834C6 /* DTBlockFunctions.m in Sources */,
				A76DB4EF16A5E5590010CD85 /* NSDocument+DTFoundation.m in Sources */,
				A76DB4F116A5E5590010CD85 /* NSImage+DTUtilities.m in Sources */,
				A76DB4F316A5E5590010CD85 /* NSValue+DTConversion.m in Sources */,
				A76DB4F516A5E5590010CD85 /* NSView+DTAutoLayout.m in Sources */,
				A7EA077F1A2B230B00B61CCE /* DTExtendedFileAttributes.m in Sources */,
				A76DB4F716A5E5590010CD85 /* NSWindowController+DTPanelControllerPresenting.m in Sources */,
				A77D5BFF16E4961A00A45C28 /* DTBase64Coding.m in Sources */,
				A70C4FEE17AA7F0700000DF5 /* DTScriptExpression.m in Sources */,
				A70C4FEF17AA7F0700000DF5 /* DTScriptVariable.m in Sources */,
				A70C4FF017AA7F0700000DF5 /* NSScanner+DTScripting.m in Sources */,
				A70C4FF517AA7F2300000DF5 /* DTObjectBlockExecutor.m in Sources */,
				A70C4FF617AA7F2300000DF5 /* NSObject+DTRuntime.m in Sources */,
				A78D662217AFFCCC0039F5E6 /* DTFolderMonitor.m in Sources */,
				A7FA5FA917B0F7B4000FC61F /* DTLog.m in Sources */,
				A7FA5FB017B127E0000FC61F /* DTAsyncFileDeleter.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A72121D6173BFFB1003C6F0A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A72121F6173C001B003C6F0A /* DTReachability.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A72121E6173BFFED003C6F0A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A72121F5173C001B003C6F0A /* DTReachability.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A78220B6168060CA005B602D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A76DB4FB16A5E5950010CD85 /* NSString+DTUTI.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A78381BF1963F8D700AF09D3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A7917C73191A312600964D63 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A79D99981792D1F50082BC06 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A79D99C21792D2490082BC06 /* NSURL+DTAWS.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A7AC21AB196417830009E1B9 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A7AC21C9196417BF0009E1B9 /* DTAnimatedGIF.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A7C92B76174FB8900019D70A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A7C92B86174FB8C20019D70A /* DTSQLiteDatabase.m in Sources */,
				A7C92B87174FB8C20019D70A /* DTSQLiteFunctions.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A7C92B88174FB9720019D70A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A7C92B97174FB9AB0019D70A /* DTSQLiteDatabase.m in Sources */,
				A7C92B98174FB9AB0019D70A /* DTSQLiteFunctions.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A7D8C7B01723174600025675 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A7D8C7B11723174600025675 /* ioapi.c in Sources */,
				A7D8C7B21723174600025675 /* mztools.c in Sources */,
				A7D8C7B31723174600025675 /* unzip.c in Sources */,
				A7D8C7B41723174600025675 /* zip.c in Sources */,
				A7D8C7B51723174600025675 /* DTZipArchive.m in Sources */,
				A7D8C7B61723174600025675 /* DTZipArchiveNode.m in Sources */,
				A7D8C7B71723174600025675 /* DTZipArchivePKZip.m in Sources */,
				A7D8C7B81723174600025675 /* DTZipArchiveGZip.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A7D9D6291758C89800C29B6F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A7E383BE160DFEDB00CF72D6 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A76DB48B16A5E1D20010CD85 /* DTHTMLParser.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A7E383D6160DFF8600CF72D6 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A7E38406160E039D00CF72D6 /* ioapi.c in Sources */,
				A7E38407160E03A600CF72D6 /* mztools.c in Sources */,
				A7E38408160E03A600CF72D6 /* unzip.c in Sources */,
				A7E38409160E03A600CF72D6 /* zip.c in Sources */,
				A76DB4BF16A5E4770010CD85 /* DTZipArchive.m in Sources */,
				C04941BC829C1022DF240F4C /* DTZipArchiveNode.m in Sources */,
				C0494DA5BB16F9A0E8533152 /* DTZipArchivePKZip.m in Sources */,
				C04948B5DABE59E8CC2340AF /* DTZipArchiveGZip.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A7E889A016A9B190009EF0DF /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A7E889B416A9B1C4009EF0DF /* NSString+DTUTI.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A7EA07341A2B215200B61CCE /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A7EA07601A2B220100B61CCE /* NSValue+DTConversion.m in Sources */,
				A7EA076A1A2B221300B61CCE /* NSDictionary+DTError.m in Sources */,
				29DA9D7E1C6DC22800F5F22A /* DTReachability.m in Sources */,
				A7EA075C1A2B220100B61CCE /* NSDocument+DTFoundation.m in Sources */,
				A7D95AE11BC40DAB00AB8A21 /* DTASN1Parser.m in Sources */,
				A7EA077A1A2B221300B61CCE /* NSURL+DTComparing.m in Sources */,
				A7EA079B1A2B233E00B61CCE /* DTAsyncFileDeleter.m in Sources */,
				A7EA07641A2B220100B61CCE /* NSWindowController+DTPanelControllerPresenting.m in Sources */,
				A7EA07A21A2B233E00B61CCE /* DTBlockFunctions.m in Sources */,
				A7EA079F1A2B233E00B61CCE /* DTFoundationConstants.m in Sources */,
				A7EA076C1A2B221300B61CCE /* NSFileWrapper+DTCopying.m in Sources */,
				A7EA07701A2B221300B61CCE /* NSString+DTFormatNumbers.m in Sources */,
				A79BA8481B46E5840086C2F6 /* DTHTMLParser.m in Sources */,
				A7EA07681A2B221300B61CCE /* NSData+DTCrypto.m in Sources */,
				A7EA075E1A2B220100B61CCE /* NSImage+DTUtilities.m in Sources */,
				A7D95ADF1BC40DAB00AB8A21 /* DTASN1BitString.m in Sources */,
				A7EA07621A2B220100B61CCE /* NSView+DTAutoLayout.m in Sources */,
				A7EA07A11A2B233E00B61CCE /* DTVersion.m in Sources */,
				A7EA07A01A2B233E00B61CCE /* DTLog.m in Sources */,
				A7EA079E1A2B233E00B61CCE /* DTFolderMonitor.m in Sources */,
				A7EA079C1A2B233E00B61CCE /* DTBase64Coding.m in Sources */,
				A7EA07661A2B221300B61CCE /* NSArray+DTError.m in Sources */,
				A7EA07741A2B221300B61CCE /* NSString+DTPaths.m in Sources */,
				A7EA07781A2B221300B61CCE /* NSURL+DTUnshorten.m in Sources */,
				A7EA07AE1A2B25B900B61CCE /* NSObject+DTRuntime.m in Sources */,
				A7EA076E1A2B221300B61CCE /* NSMutableArray+DTMoving.m in Sources */,
				A7D95AE31BC40DAB00AB8A21 /* DTASN1Serialization.m in Sources */,
				A7EA07761A2B221300B61CCE /* NSString+DTURLEncoding.m in Sources */,
				A7EA079D1A2B233E00B61CCE /* DTExtendedFileAttributes.m in Sources */,
				A7EA07AC1A2B259300B61CCE /* DTCoreGraphicsUtils.m in Sources */,
				A7EA075A1A2B220100B61CCE /* DTScrollView.m in Sources */,
				A7EA07721A2B221300B61CCE /* NSString+DTUtilities.m in Sources */,
				A7EA07B11A2B299D00B61CCE /* DTObjectBlockExecutor.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A7EA07B31A2B2F6A00B61CCE /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A7EA07DD1A2B30F000B61CCE /* DTActivityTitleView.m in Sources */,
				A79BA6C31B46A38A0086C2F6 /* DTHTMLParser.m in Sources */,
				A7EA081B1A2B316100B61CCE /* NSURL+DTComparing.m in Sources */,
				A7EA080D1A2B316100B61CCE /* NSFileWrapper+DTCopying.m in Sources */,
				A7EA08071A2B316100B61CCE /* NSArray+DTError.m in Sources */,
				A7EA082B1A2B318E00B61CCE /* DTFoundationConstants.m in Sources */,
				A7BDB4241C64D71900E8CD84 /* DTProgressHUDWindow.m in Sources */,
				A7EA08191A2B316100B61CCE /* NSURL+DTUnshorten.m in Sources */,
				A7EA07DF1A2B30F000B61CCE /* DTCustomColoredAccessory.m in Sources */,
				A7EA07DB1A2B30F000B61CCE /* DTTiledLayerWithoutFade.m in Sources */,
				A7EA08091A2B316100B61CCE /* NSData+DTCrypto.m in Sources */,
				A7EA07FD1A2B312300B61CCE /* DTAlertView.m in Sources */,
				A7EA07FF1A2B312300B61CCE /* UIView+DTActionHandlers.m in Sources */,
				A7EA07EF1A2B310600B61CCE /* DTSidePanelController.m in Sources */,
				A79BA6CB1B46A3E40086C2F6 /* DTAnimatedGIF.m in Sources */,
				A7EA08111A2B316100B61CCE /* NSString+DTFormatNumbers.m in Sources */,
				A7EA07EB1A2B30F000B61CCE /* UIView+DTFoundation.m in Sources */,
				A7EA08251A2B318E00B61CCE /* DTBase64Coding.m in Sources */,
				A7EA07E31A2B30F000B61CCE /* DTSmartPagingScrollView.m in Sources */,
				A7EA08131A2B316100B61CCE /* NSString+DTUtilities.m in Sources */,
				A7EA07F21A2B310600B61CCE /* DTSidePanelPanGestureRecognizer.m in Sources */,
				A7EA08231A2B318E00B61CCE /* DTAsyncFileDeleter.m in Sources */,
				A7EA07E51A2B30F000B61CCE /* UIApplication+DTNetworkActivity.m in Sources */,
				A7EA082F1A2B318E00B61CCE /* DTVersion.m in Sources */,
				A7EA082D1A2B318E00B61CCE /* DTLog.m in Sources */,
				A7EA08171A2B316100B61CCE /* NSString+DTURLEncoding.m in Sources */,
				A7EA07F11A2B310600B61CCE /* UIViewController+DTSidePanelController.m in Sources */,
				A7EA07E11A2B30F000B61CCE /* DTPieProgressIndicator.m in Sources */,
				A7EA080B1A2B316100B61CCE /* NSDictionary+DTError.m in Sources */,
				A7EA080F1A2B316100B61CCE /* NSMutableArray+DTMoving.m in Sources */,
				A7EA08271A2B318E00B61CCE /* DTExtendedFileAttributes.m in Sources */,
				A8C4A7791F949E5100FC611D /* UIScreen+DTFoundation.m in Sources */,
				A7D95AE71BC40DAC00AB8A21 /* DTASN1Parser.m in Sources */,
				29DA9D7F1C6DC22900F5F22A /* DTReachability.m in Sources */,
				A7EA08211A2B318500B61CCE /* NSObject+DTRuntime.m in Sources */,
				A7EA07E91A2B30F000B61CCE /* NSURL+DTAppLinks.m in Sources */,
				A7EA07E71A2B30F000B61CCE /* UIImage+DTFoundation.m in Sources */,
				A7EA07F51A2B310600B61CCE /* DTSidePanelControllerSegue.m in Sources */,
				A7EA08291A2B318E00B61CCE /* DTFolderMonitor.m in Sources */,
				A7EA07F91A2B311700B61CCE /* UIView+DTDebug.m in Sources */,
				A7EA081F1A2B318500B61CCE /* DTObjectBlockExecutor.m in Sources */,
				A7EA07FB1A2B312300B61CCE /* DTActionSheet.m in Sources */,
				A7EA08321A2B318E00B61CCE /* DTBlockFunctions.m in Sources */,
				A7D95AE91BC40DAC00AB8A21 /* DTASN1Serialization.m in Sources */,
				A7EA08151A2B316100B61CCE /* NSString+DTPaths.m in Sources */,
				A7D95AE51BC40DAC00AB8A21 /* DTASN1BitString.m in Sources */,
				A7EA081D1A2B317300B61CCE /* DTCoreGraphicsUtils.m in Sources */,
				A7BDB4221C64D6FE00E8CD84 /* DTProgressHUD.m in Sources */,
				A7EA07F71A2B311700B61CCE /* UIColor+DTDebug.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F88FF6781920A83300120808 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A8C4A77D1F949F6900FC611D /* UIScreen+DTFoundation.m in Sources */,
				F88FF6851920A9DF00120808 /* DTProgressHUD.m in Sources */,
				F88FF6D21920BF5200120808 /* DTProgressHUDWindow.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F88FF68B1920AAB600120808 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FA9CB80817ABDEF200A596C5 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FA9CB81817ABDF1500A596C5 /* DTASN1BitString.m in Sources */,
				FA9CB81917ABDF1500A596C5 /* DTASN1Parser.m in Sources */,
				FA9CB81A17ABDF1500A596C5 /* DTASN1Serialization.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FAC284CC19B84B5F0010C385 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FAF37A1C17AFDD93009AC27C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		A70D74F01743AFD700E6E626 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = A70B4CE61486637E00873A4A /* Static Library */;
			targetProxy = A70D74EF1743AFD700E6E626 /* PBXContainerItemProxy */;
		};
		A74A764F17BBB15D007073C8 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = A70B4CE61486637E00873A4A /* Static Library */;
			targetProxy = A74A764E17BBB15D007073C8 /* PBXContainerItemProxy */;
		};
		A7AC21CB196417CC0009E1B9 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = A7AC21AE196417830009E1B9 /* DTAnimatedGIF (iOS) */;
			targetProxy = A7AC21CA196417CC0009E1B9 /* PBXContainerItemProxy */;
		};
		A7D9D6611758CA5400C29B6F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = A72121D9173BFFB1003C6F0A /* DTReachability (iOS) */;
			targetProxy = A7D9D6601758CA5400C29B6F /* PBXContainerItemProxy */;
		};
		A7E77947253D9E5600E011D3 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			productRef = A7E77946253D9E5600E011D3 /* DTFoundation */;
		};
		A7EA07CE1A2B2F6B00B61CCE /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = A7EA07B71A2B2F6A00B61CCE /* DTFoundation (iOS) */;
			targetProxy = A7EA07CD1A2B2F6B00B61CCE /* PBXContainerItemProxy */;
		};
		F88FF6C51920ABD900120808 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = F88FF6771920A83300120808 /* DTProgressHUD (iOS) */;
			targetProxy = F88FF6C41920ABD900120808 /* PBXContainerItemProxy */;
		};
		F88FF6CB1920AF1F00120808 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = A70B4CE61486637E00873A4A /* Static Library */;
			targetProxy = F88FF6CA1920AF1F00120808 /* PBXContainerItemProxy */;
		};
		FAC284C819B84B5F0010C385 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = A70B4CE61486637E00873A4A /* Static Library */;
			targetProxy = FAC284C919B84B5F0010C385 /* PBXContainerItemProxy */;
		};
		FAC284CA19B84B5F0010C385 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = A7E383D9160DFF8600CF72D6 /* DTZipArchive (iOS) */;
			targetProxy = FAC284CB19B84B5F0010C385 /* PBXContainerItemProxy */;
		};
		FAF37A5917AFDF5D009AC27C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = A7E383D9160DFF8600CF72D6 /* DTZipArchive (iOS) */;
			targetProxy = FAF37A5817AFDF5D009AC27C /* PBXContainerItemProxy */;
		};
		FAF37A7217AFE0CC009AC27C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = A70B4CE61486637E00873A4A /* Static Library */;
			targetProxy = FAF37A7117AFE0CC009AC27C /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		384470E81BA3330F0037C68D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				DSTROOT = /tmp/DTFoundation.dst;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_THUMB_SUPPORT = NO;
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "appletvsimulator appletvos";
			};
			name = Debug;
		};
		384470E91BA3330F0037C68D /* Coverage */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				DSTROOT = /tmp/DTFoundation.dst;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_THUMB_SUPPORT = NO;
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "appletvsimulator appletvos";
			};
			name = Coverage;
		};
		384470EA1BA3330F0037C68D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				DSTROOT = /tmp/DTFoundation.dst;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_THUMB_SUPPORT = NO;
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "appletvsimulator appletvos";
			};
			name = Release;
		};
		384471521BA33ED80037C68D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				APPLICATION_EXTENSION_API_ONLY = NO;
				BITCODE_GENERATION_MODE = bitcode;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COMBINE_HIDPI_IMAGES = YES;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				INFOPLIST_FILE = "Core/DTFoundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_CFLAGS = "-fembed-bitcode";
				PRODUCT_BUNDLE_IDENTIFIER = "com.drobnik.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = DTFoundation;
				PUBLIC_HEADERS_FOLDER_PATH = "$(CONTENTS_FOLDER_PATH)/Headers";
				SDKROOT = appletvos;
				SKIP_INSTALL = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		384471531BA33ED80037C68D /* Coverage */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				APPLICATION_EXTENSION_API_ONLY = NO;
				BITCODE_GENERATION_MODE = bitcode;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COMBINE_HIDPI_IMAGES = YES;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_NS_ASSERTIONS = NO;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				INFOPLIST_FILE = "Core/DTFoundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_CFLAGS = "-fembed-bitcode";
				PRODUCT_BUNDLE_IDENTIFIER = "com.drobnik.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = DTFoundation;
				PUBLIC_HEADERS_FOLDER_PATH = "$(CONTENTS_FOLDER_PATH)/Headers";
				SDKROOT = appletvos;
				SKIP_INSTALL = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Coverage;
		};
		384471541BA33ED80037C68D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				APPLICATION_EXTENSION_API_ONLY = NO;
				BITCODE_GENERATION_MODE = bitcode;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COMBINE_HIDPI_IMAGES = YES;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_NS_ASSERTIONS = NO;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				INFOPLIST_FILE = "Core/DTFoundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_CFLAGS = "-fembed-bitcode";
				PRODUCT_BUNDLE_IDENTIFIER = "com.drobnik.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = DTFoundation;
				PUBLIC_HEADERS_FOLDER_PATH = "$(CONTENTS_FOLDER_PATH)/Headers";
				SDKROOT = appletvos;
				SKIP_INSTALL = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		3D7CE545166613B60028D339 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COMBINE_HIDPI_IMAGES = YES;
				DSTROOT = /tmp/DTHTMLParser.dst;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				PRODUCT_NAME = DTHTMLParser_Mac;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		3D7CE546166613B60028D339 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COMBINE_HIDPI_IMAGES = YES;
				DSTROOT = /tmp/DTHTMLParser.dst;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				PRODUCT_NAME = DTHTMLParser_Mac;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		A70B4CF01486637E00873A4A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				DSTROOT = /tmp/DTFoundation.dst;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_THUMB_SUPPORT = NO;
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_NAME = DTFoundation;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphonesimulator iphoneos appletvsimulator";
			};
			name = Debug;
		};
		A70B4CF11486637E00873A4A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				DSTROOT = /tmp/DTFoundation.dst;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_THUMB_SUPPORT = NO;
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_NAME = DTFoundation;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphonesimulator iphoneos appletvsimulator";
			};
			name = Release;
		};
		A70D74E71743AFBC00E6E626 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				GCC_PREFIX_HEADER = "Demo/DTSidePanels/DTSidePanels-Prefix.pch";
				INFOPLIST_FILE = "Demo/DTSidePanels/DTSidePanels-Info.plist";
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_BUNDLE_IDENTIFIER = "cocoanetics.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = app;
			};
			name = Debug;
		};
		A70D74E81743AFBC00E6E626 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				GCC_PREFIX_HEADER = "Demo/DTSidePanels/DTSidePanels-Prefix.pch";
				INFOPLIST_FILE = "Demo/DTSidePanels/DTSidePanels-Info.plist";
				OTHER_CFLAGS = "-DNS_BLOCK_ASSERTIONS=1";
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_BUNDLE_IDENTIFIER = "cocoanetics.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = app;
			};
			name = Release;
		};
		A70ED6B617F98468008A801A /* Coverage */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				BITCODE_GENERATION_MODE = bitcode;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = NO;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1.7.18;
				ENABLE_BITCODE = YES;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_GENERATE_TEST_COVERAGE_FILES = YES;
				GCC_INSTRUMENT_PROGRAM_FLOW_ARCS = YES;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = "COVERAGE=1";
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_THUMB_SUPPORT = NO;
				GCC_VERSION = com.apple.compilers.llvm.clang.1_0;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_MISSING_PROTOTYPES = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = /usr/include/libxml2;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				MACOSX_DEPLOYMENT_TARGET = 13.0;
				OTHER_CFLAGS = "-fembed-bitcode";
				PUBLIC_HEADERS_FOLDER_PATH = DTFoundation;
				TVOS_DEPLOYMENT_TARGET = 13.0;
				WATCHOS_DEPLOYMENT_TARGET = 2.0;
			};
			name = Coverage;
		};
		A70ED6B717F98468008A801A /* Coverage */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				GCC_THUMB_SUPPORT = NO;
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Coverage;
		};
		A70ED6B917F98468008A801A /* Coverage */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				DSTROOT = /tmp/DTFoundation.dst;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_THUMB_SUPPORT = NO;
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_NAME = DTFoundation;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphonesimulator iphoneos appletvsimulator";
			};
			name = Coverage;
		};
		A70ED6BB17F98468008A801A /* Coverage */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COMBINE_HIDPI_IMAGES = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\"$(SYSTEM_APPS_DIR)/Xcode.app/Contents/Developer/Library/Frameworks\"",
				);
				GCC_ENABLE_OBJC_EXCEPTIONS = YES;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				PRODUCT_NAME = DTFoundation_Mac;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
			};
			name = Coverage;
		};
		A70ED6BC17F98468008A801A /* Coverage */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				DSTROOT = /tmp/DTAWS.dst;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				PRODUCT_NAME = DTAWS;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphonesimulator iphoneos appletvsimulator";
			};
			name = Coverage;
		};
		A70ED6BD17F98468008A801A /* Coverage */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				DSTROOT = /tmp/DTASN1.dst;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				PRODUCT_NAME = DTASN1;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphonesimulator iphoneos appletvsimulator";
			};
			name = Coverage;
		};
		A70ED6BE17F98468008A801A /* Coverage */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				DSTROOT = /tmp/DTHTMLParser.dst;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				PRODUCT_NAME = DTHTMLParser_iOS;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphonesimulator iphoneos appletvsimulator";
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Coverage;
		};
		A70ED6BF17F98468008A801A /* Coverage */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COMBINE_HIDPI_IMAGES = YES;
				DSTROOT = /tmp/DTHTMLParser.dst;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				PRODUCT_NAME = DTHTMLParser_Mac;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Coverage;
		};
		A70ED6C017F98468008A801A /* Coverage */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				DSTROOT = /tmp/DTZipArchive.dst;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				PRODUCT_NAME = DTZipArchive_iOS;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphonesimulator iphoneos appletvsimulator";
			};
			name = Coverage;
		};
		A70ED6C117F98468008A801A /* Coverage */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COMBINE_HIDPI_IMAGES = YES;
				DSTROOT = /tmp/DTZipArchive.dst;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				PRODUCT_NAME = DTZipArchive_Mac;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = macosx;
			};
			name = Coverage;
		};
		A70ED6C217F98468008A801A /* Coverage */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				DSTROOT = /tmp/DTUTI.dst;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				PRODUCT_NAME = DTUTI_iOS;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphonesimulator iphoneos appletvsimulator";
			};
			name = Coverage;
		};
		A70ED6C317F98468008A801A /* Coverage */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				COMBINE_HIDPI_IMAGES = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\"$(SYSTEM_APPS_DIR)/Xcode46-DP4.app/Contents/Developer/Library/Frameworks\"",
				);
				GCC_ENABLE_OBJC_EXCEPTIONS = YES;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				PRODUCT_NAME = DTUTI_Mac;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
			};
			name = Coverage;
		};
		A70ED6C417F98468008A801A /* Coverage */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				DSTROOT = /tmp/DTReachability.dst;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				PRODUCT_NAME = DTReachability_iOS;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
			};
			name = Coverage;
		};
		A70ED6C517F98468008A801A /* Coverage */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				COMBINE_HIDPI_IMAGES = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\"$(SYSTEM_APPS_DIR)/Xcode.app/Contents/Developer/Library/Frameworks\"",
				);
				GCC_ENABLE_OBJC_EXCEPTIONS = YES;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				PRODUCT_NAME = DTReachability_Mac;
				SDKROOT = macosx;
			};
			name = Coverage;
		};
		A70ED6C617F98468008A801A /* Coverage */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				DSTROOT = /tmp/DTSQLite.dst;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				PRODUCT_NAME = DTSQLite_iOS;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphonesimulator iphoneos appletvsimulator";
			};
			name = Coverage;
		};
		A70ED6C717F98468008A801A /* Coverage */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				COMBINE_HIDPI_IMAGES = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\"$(SYSTEM_APPS_DIR)/Xcode.app/Contents/Developer/Library/Frameworks\"",
				);
				GCC_ENABLE_OBJC_EXCEPTIONS = YES;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				PRODUCT_NAME = DTSQLite_Mac;
				SDKROOT = macosx;
			};
			name = Coverage;
		};
		A70ED6C817F98468008A801A /* Coverage */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				GCC_PREFIX_HEADER = "Demo/DTSidePanels/DTSidePanels-Prefix.pch";
				INFOPLIST_FILE = "Demo/DTSidePanels/DTSidePanels-Info.plist";
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_BUNDLE_IDENTIFIER = "cocoanetics.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = app;
			};
			name = Coverage;
		};
		A70ED6C917F98468008A801A /* Coverage */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				GCC_PREFIX_HEADER = "Demo/DTReachability/DTReachability-Prefix.pch";
				INFOPLIST_FILE = "Demo/DTReachability/DTReachability-Info.plist";
				PRODUCT_BUNDLE_IDENTIFIER = "com.drobnik.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = app;
			};
			name = Coverage;
		};
		A70ED6CA17F98468008A801A /* Coverage */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				GCC_PREFIX_HEADER = "Demo/DTZipArchiveDemo/DTZipArchiveDemo-Prefix.pch";
				INFOPLIST_FILE = "Demo/DTZipArchiveDemo/DTZipArchiveDemo-Info.plist";
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_BUNDLE_IDENTIFIER = "com.drobnik.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = DTZipArchive;
				PROVISIONING_PROFILE = "";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = app;
			};
			name = Coverage;
		};
		A710A5091607556000437D36 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COMBINE_HIDPI_IMAGES = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\"$(SYSTEM_APPS_DIR)/Xcode.app/Contents/Developer/Library/Frameworks\"",
				);
				GCC_ENABLE_OBJC_EXCEPTIONS = YES;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				PRODUCT_NAME = DTFoundation_Mac;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
			};
			name = Debug;
		};
		A710A50A1607556000437D36 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COMBINE_HIDPI_IMAGES = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\"$(SYSTEM_APPS_DIR)/Xcode.app/Contents/Developer/Library/Frameworks\"",
				);
				GCC_ENABLE_OBJC_EXCEPTIONS = YES;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				PRODUCT_NAME = DTFoundation_Mac;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
			};
			name = Release;
		};
		A72121E4173BFFB1003C6F0A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				DSTROOT = /tmp/DTReachability.dst;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				PRODUCT_NAME = DTReachability_iOS;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
			};
			name = Debug;
		};
		A72121E5173BFFB1003C6F0A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				DSTROOT = /tmp/DTReachability.dst;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				PRODUCT_NAME = DTReachability_iOS;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
			};
			name = Release;
		};
		A72121F3173BFFED003C6F0A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				COMBINE_HIDPI_IMAGES = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\"$(SYSTEM_APPS_DIR)/Xcode.app/Contents/Developer/Library/Frameworks\"",
				);
				GCC_ENABLE_OBJC_EXCEPTIONS = YES;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				PRODUCT_NAME = DTReachability_Mac;
				SDKROOT = macosx;
			};
			name = Debug;
		};
		A72121F4173BFFED003C6F0A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				COMBINE_HIDPI_IMAGES = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\"$(SYSTEM_APPS_DIR)/Xcode.app/Contents/Developer/Library/Frameworks\"",
				);
				GCC_ENABLE_OBJC_EXCEPTIONS = YES;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				PRODUCT_NAME = DTReachability_Mac;
				SDKROOT = macosx;
			};
			name = Release;
		};
		A78220C3168060CA005B602D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				DSTROOT = /tmp/DTUTI.dst;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				PRODUCT_NAME = DTUTI_iOS;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphonesimulator iphoneos appletvsimulator";
			};
			name = Debug;
		};
		A78220C4168060CA005B602D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				DSTROOT = /tmp/DTUTI.dst;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				PRODUCT_NAME = DTUTI_iOS;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphonesimulator iphoneos appletvsimulator";
			};
			name = Release;
		};
		A78381E41963F8D700AF09D3 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = LaunchImage;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_PREFIX_HEADER = "Demo/DTAnimatedGIF/DTAnimatedGIFDemo-Prefix.pch";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				INFOPLIST_FILE = "Demo/DTAnimatedGIF/DTAnimatedGIFDemo-Info.plist";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				METAL_ENABLE_DEBUG_INFO = YES;
				PRODUCT_BUNDLE_IDENTIFIER = "com.cocoanetics.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		A78381E51963F8D700AF09D3 /* Coverage */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = LaunchImage;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_PREFIX_HEADER = "Demo/DTAnimatedGIF/DTAnimatedGIFDemo-Prefix.pch";
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				INFOPLIST_FILE = "Demo/DTAnimatedGIF/DTAnimatedGIFDemo-Info.plist";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				METAL_ENABLE_DEBUG_INFO = NO;
				PRODUCT_BUNDLE_IDENTIFIER = "com.cocoanetics.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
			};
			name = Coverage;
		};
		A78381E61963F8D700AF09D3 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = LaunchImage;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_PREFIX_HEADER = "Demo/DTAnimatedGIF/DTAnimatedGIFDemo-Prefix.pch";
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				INFOPLIST_FILE = "Demo/DTAnimatedGIF/DTAnimatedGIFDemo-Info.plist";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				METAL_ENABLE_DEBUG_INFO = NO;
				PRODUCT_BUNDLE_IDENTIFIER = "com.cocoanetics.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
			};
			name = Release;
		};
		A7917C87191A312700964D63 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "Test/UnitTests-Prefix.pch";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				INFOPLIST_FILE = "Test/UnitTests-Info.plist";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.drobnik.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				WRAPPER_EXTENSION = xctest;
			};
			name = Debug;
		};
		A7917C88191A312700964D63 /* Coverage */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				ENABLE_NS_ASSERTIONS = NO;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "Test/UnitTests-Prefix.pch";
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				INFOPLIST_FILE = "Test/UnitTests-Info.plist";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.drobnik.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				WRAPPER_EXTENSION = xctest;
			};
			name = Coverage;
		};
		A7917C89191A312700964D63 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				ENABLE_NS_ASSERTIONS = NO;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "Test/UnitTests-Prefix.pch";
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				INFOPLIST_FILE = "Test/UnitTests-Info.plist";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.drobnik.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				WRAPPER_EXTENSION = xctest;
			};
			name = Release;
		};
		A79D99B91792D1F60082BC06 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				DSTROOT = /tmp/DTAWS.dst;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				PRODUCT_NAME = DTAWS;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphonesimulator iphoneos appletvsimulator";
			};
			name = Debug;
		};
		A79D99BA1792D1F60082BC06 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				DSTROOT = /tmp/DTAWS.dst;
				ENABLE_NS_ASSERTIONS = NO;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				PRODUCT_NAME = DTAWS;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphonesimulator iphoneos appletvsimulator";
			};
			name = Release;
		};
		A7AC21C1196417830009E1B9 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				METAL_ENABLE_DEBUG_INFO = YES;
				PRODUCT_NAME = DTAnimatedGIF;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
			};
			name = Debug;
		};
		A7AC21C2196417830009E1B9 /* Coverage */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				METAL_ENABLE_DEBUG_INFO = NO;
				PRODUCT_NAME = DTAnimatedGIF;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
			};
			name = Coverage;
		};
		A7AC21C3196417830009E1B9 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				METAL_ENABLE_DEBUG_INFO = NO;
				PRODUCT_NAME = DTAnimatedGIF;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
			};
			name = Release;
		};
		A7C92B84174FB8900019D70A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				DSTROOT = /tmp/DTSQLite.dst;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				PRODUCT_NAME = DTSQLite_iOS;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphonesimulator iphoneos appletvsimulator";
			};
			name = Debug;
		};
		A7C92B85174FB8900019D70A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				DSTROOT = /tmp/DTSQLite.dst;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				PRODUCT_NAME = DTSQLite_iOS;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphonesimulator iphoneos appletvsimulator";
			};
			name = Release;
		};
		A7C92B95174FB9720019D70A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				COMBINE_HIDPI_IMAGES = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\"$(SYSTEM_APPS_DIR)/Xcode.app/Contents/Developer/Library/Frameworks\"",
				);
				GCC_ENABLE_OBJC_EXCEPTIONS = YES;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				PRODUCT_NAME = DTSQLite_Mac;
				SDKROOT = macosx;
			};
			name = Debug;
		};
		A7C92B96174FB9720019D70A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				COMBINE_HIDPI_IMAGES = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\"$(SYSTEM_APPS_DIR)/Xcode.app/Contents/Developer/Library/Frameworks\"",
				);
				GCC_ENABLE_OBJC_EXCEPTIONS = YES;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				PRODUCT_NAME = DTSQLite_Mac;
				SDKROOT = macosx;
			};
			name = Release;
		};
		A7D8C7C01723174600025675 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COMBINE_HIDPI_IMAGES = YES;
				DSTROOT = /tmp/DTZipArchive.dst;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				PRODUCT_NAME = DTZipArchive_Mac;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = macosx;
			};
			name = Debug;
		};
		A7D8C7C11723174600025675 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COMBINE_HIDPI_IMAGES = YES;
				DSTROOT = /tmp/DTZipArchive.dst;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				PRODUCT_NAME = DTZipArchive_Mac;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = macosx;
			};
			name = Release;
		};
		A7D9D64A1758C89900C29B6F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				GCC_PREFIX_HEADER = "Demo/DTReachability/DTReachability-Prefix.pch";
				INFOPLIST_FILE = "Demo/DTReachability/DTReachability-Info.plist";
				PRODUCT_BUNDLE_IDENTIFIER = "com.drobnik.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = app;
			};
			name = Debug;
		};
		A7D9D64B1758C89900C29B6F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				GCC_PREFIX_HEADER = "Demo/DTReachability/DTReachability-Prefix.pch";
				INFOPLIST_FILE = "Demo/DTReachability/DTReachability-Info.plist";
				OTHER_CFLAGS = "-DNS_BLOCK_ASSERTIONS=1";
				PRODUCT_BUNDLE_IDENTIFIER = "com.drobnik.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = app;
			};
			name = Release;
		};
		A7E383CB160DFEDB00CF72D6 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				DSTROOT = /tmp/DTHTMLParser.dst;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				PRODUCT_NAME = DTHTMLParser_iOS;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphonesimulator iphoneos appletvsimulator";
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		A7E383CC160DFEDB00CF72D6 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				DSTROOT = /tmp/DTHTMLParser.dst;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				PRODUCT_NAME = DTHTMLParser_iOS;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphonesimulator iphoneos appletvsimulator";
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		A7E383E4160DFF8600CF72D6 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				DSTROOT = /tmp/DTZipArchive.dst;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				PRODUCT_NAME = DTZipArchive_iOS;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphonesimulator iphoneos appletvsimulator";
			};
			name = Debug;
		};
		A7E383E5160DFF8600CF72D6 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				DSTROOT = /tmp/DTZipArchive.dst;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				PRODUCT_NAME = DTZipArchive_iOS;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphonesimulator iphoneos appletvsimulator";
			};
			name = Release;
		};
		A7E889B216A9B190009EF0DF /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				COMBINE_HIDPI_IMAGES = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\"$(SYSTEM_APPS_DIR)/Xcode46-DP4.app/Contents/Developer/Library/Frameworks\"",
				);
				GCC_ENABLE_OBJC_EXCEPTIONS = YES;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				PRODUCT_NAME = DTUTI_Mac;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
			};
			name = Debug;
		};
		A7E889B316A9B190009EF0DF /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				COMBINE_HIDPI_IMAGES = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\"$(SYSTEM_APPS_DIR)/Xcode46-DP4.app/Contents/Developer/Library/Frameworks\"",
				);
				GCC_ENABLE_OBJC_EXCEPTIONS = YES;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				PRODUCT_NAME = DTUTI_Mac;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
			};
			name = Release;
		};
		A7EA074C1A2B215300B61CCE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				APPLICATION_EXTENSION_API_ONLY = NO;
				BITCODE_GENERATION_MODE = bitcode;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				COMBINE_HIDPI_IMAGES = YES;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				FRAMEWORK_VERSION = A;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				INFOPLIST_FILE = "Core/DTFoundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/../Frameworks @loader_path/Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 10.7;
				OTHER_CFLAGS = "-fembed-bitcode";
				PRODUCT_BUNDLE_IDENTIFIER = "com.drobnik.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = DTFoundation;
				PUBLIC_HEADERS_FOLDER_PATH = "$(CONTENTS_FOLDER_PATH)/Headers";
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		A7EA074D1A2B215300B61CCE /* Coverage */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				APPLICATION_EXTENSION_API_ONLY = NO;
				BITCODE_GENERATION_MODE = bitcode;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				COMBINE_HIDPI_IMAGES = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_NS_ASSERTIONS = NO;
				FRAMEWORK_VERSION = A;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				INFOPLIST_FILE = "Core/DTFoundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/../Frameworks @loader_path/Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 10.7;
				OTHER_CFLAGS = "-fembed-bitcode";
				PRODUCT_BUNDLE_IDENTIFIER = "com.drobnik.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = DTFoundation;
				PUBLIC_HEADERS_FOLDER_PATH = "$(CONTENTS_FOLDER_PATH)/Headers";
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Coverage;
		};
		A7EA074E1A2B215300B61CCE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				APPLICATION_EXTENSION_API_ONLY = NO;
				BITCODE_GENERATION_MODE = bitcode;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				COMBINE_HIDPI_IMAGES = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_NS_ASSERTIONS = NO;
				FRAMEWORK_VERSION = A;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				INFOPLIST_FILE = "Core/DTFoundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/../Frameworks @loader_path/Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 10.7;
				OTHER_CFLAGS = "-fembed-bitcode";
				PRODUCT_BUNDLE_IDENTIFIER = "com.drobnik.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = DTFoundation;
				PUBLIC_HEADERS_FOLDER_PATH = "$(CONTENTS_FOLDER_PATH)/Headers";
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		A7EA07D21A2B2F6B00B61CCE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				APPLICATION_EXTENSION_API_ONLY = NO;
				BITCODE_GENERATION_MODE = bitcode;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CODE_SIGN_IDENTITY = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = Z7L2YCUH45;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				INFOPLIST_FILE = "Core/DTFoundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_CFLAGS = "-fembed-bitcode";
				PRODUCT_BUNDLE_IDENTIFIER = "com.drobnik.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = DTFoundation;
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=macosx*]" = "";
				PUBLIC_HEADERS_FOLDER_PATH = "$(CONTENTS_FOLDER_PATH)/Headers";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		A7EA07D31A2B2F6B00B61CCE /* Coverage */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				APPLICATION_EXTENSION_API_ONLY = NO;
				BITCODE_GENERATION_MODE = bitcode;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CODE_SIGN_IDENTITY = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = Z7L2YCUH45;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_NS_ASSERTIONS = NO;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				INFOPLIST_FILE = "Core/DTFoundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_CFLAGS = "-fembed-bitcode";
				PRODUCT_BUNDLE_IDENTIFIER = "com.drobnik.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = DTFoundation;
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=macosx*]" = "";
				PUBLIC_HEADERS_FOLDER_PATH = "$(CONTENTS_FOLDER_PATH)/Headers";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Coverage;
		};
		A7EA07D41A2B2F6B00B61CCE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				APPLICATION_EXTENSION_API_ONLY = NO;
				BITCODE_GENERATION_MODE = bitcode;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CODE_SIGN_IDENTITY = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = Z7L2YCUH45;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_NS_ASSERTIONS = NO;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				INFOPLIST_FILE = "Core/DTFoundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_CFLAGS = "-fembed-bitcode";
				PRODUCT_BUNDLE_IDENTIFIER = "com.drobnik.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = DTFoundation;
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=macosx*]" = "";
				PUBLIC_HEADERS_FOLDER_PATH = "$(CONTENTS_FOLDER_PATH)/Headers";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		A7F4DFB0147FB61500F4059A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				BITCODE_GENERATION_MODE = bitcode;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = NO;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1.7.18;
				ENABLE_BITCODE = YES;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_THUMB_SUPPORT = NO;
				GCC_VERSION = com.apple.compilers.llvm.clang.1_0;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_MISSING_PROTOTYPES = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = /usr/include/libxml2;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				MACOSX_DEPLOYMENT_TARGET = 13.0;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CFLAGS = "-fembed-bitcode";
				PUBLIC_HEADERS_FOLDER_PATH = DTFoundation;
				TVOS_DEPLOYMENT_TARGET = 13.0;
				WATCHOS_DEPLOYMENT_TARGET = 2.0;
			};
			name = Debug;
		};
		A7F4DFB1147FB61500F4059A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				BITCODE_GENERATION_MODE = bitcode;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = NO;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = YES;
				CURRENT_PROJECT_VERSION = 1.7.18;
				ENABLE_BITCODE = YES;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_THUMB_SUPPORT = NO;
				GCC_VERSION = com.apple.compilers.llvm.clang.1_0;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_MISSING_PROTOTYPES = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = /usr/include/libxml2;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				MACOSX_DEPLOYMENT_TARGET = 13.0;
				OTHER_CFLAGS = "-fembed-bitcode";
				PUBLIC_HEADERS_FOLDER_PATH = DTFoundation;
				TVOS_DEPLOYMENT_TARGET = 13.0;
				VALIDATE_PRODUCT = YES;
				WATCHOS_DEPLOYMENT_TARGET = 2.0;
			};
			name = Release;
		};
		A7F4DFEF147FBAC600F4059A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				GCC_THUMB_SUPPORT = NO;
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Debug;
		};
		A7F4DFF0147FBAC600F4059A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				GCC_THUMB_SUPPORT = NO;
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Release;
		};
		F88FF67D1920A83300120808 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				DSTROOT = /tmp/DTReachability.dst;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				PRODUCT_NAME = DTProgressHUD_iOS;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphonesimulator iphoneos appletvsimulator";
			};
			name = Debug;
		};
		F88FF67E1920A83300120808 /* Coverage */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				DSTROOT = /tmp/DTReachability.dst;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				PRODUCT_NAME = DTProgressHUD_iOS;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphonesimulator iphoneos appletvsimulator";
			};
			name = Coverage;
		};
		F88FF67F1920A83300120808 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				DSTROOT = /tmp/DTReachability.dst;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				PRODUCT_NAME = DTProgressHUD_iOS;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphonesimulator iphoneos appletvsimulator";
			};
			name = Release;
		};
		F88FF6A21920AAB600120808 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = LaunchImage;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				GCC_PREFIX_HEADER = "Demo/DTProgressHUDDemo/DTProgressHUDDemo-Prefix.pch";
				INFOPLIST_FILE = "Demo/DTProgressHUDDemo/DTProgressHUDDemo-Info.plist";
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_BUNDLE_IDENTIFIER = "com.drobnik.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "DTProgressHUD Demo";
				PROVISIONING_PROFILE = "";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = app;
			};
			name = Debug;
		};
		F88FF6A31920AAB600120808 /* Coverage */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = LaunchImage;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				GCC_PREFIX_HEADER = "Demo/DTProgressHUDDemo/DTProgressHUDDemo-Prefix.pch";
				INFOPLIST_FILE = "Demo/DTProgressHUDDemo/DTProgressHUDDemo-Info.plist";
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_BUNDLE_IDENTIFIER = "com.drobnik.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "DTProgressHUD Demo";
				PROVISIONING_PROFILE = "";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = app;
			};
			name = Coverage;
		};
		F88FF6A41920AAB600120808 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = LaunchImage;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				GCC_PREFIX_HEADER = "Demo/DTProgressHUDDemo/DTProgressHUDDemo-Prefix.pch";
				INFOPLIST_FILE = "Demo/DTProgressHUDDemo/DTProgressHUDDemo-Info.plist";
				OTHER_CFLAGS = "-DNS_BLOCK_ASSERTIONS=1";
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_BUNDLE_IDENTIFIER = "com.drobnik.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "DTProgressHUD Demo";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = app;
			};
			name = Release;
		};
		FA9CB81517ABDEF200A596C5 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				DSTROOT = /tmp/DTASN1.dst;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				PRODUCT_NAME = DTASN1;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphonesimulator iphoneos appletvsimulator";
			};
			name = Debug;
		};
		FA9CB81617ABDEF200A596C5 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				DSTROOT = /tmp/DTASN1.dst;
				GCC_PREFIX_HEADER = "Core/DTFoundation-Prefix.pch";
				PRODUCT_NAME = DTASN1;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphonesimulator iphoneos appletvsimulator";
			};
			name = Release;
		};
		FAC284E319B84B5F0010C385 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				GCC_PREFIX_HEADER = "Demo/DTActionSheetDemo/DTActionSheetDemo-Prefix.pch";
				INFOPLIST_FILE = "Demo/DTActionSheetDemo/DTActionSheetDemo-Info.plist";
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_BUNDLE_IDENTIFIER = "com.drobnik.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "DTActionSheet Demo";
				PROVISIONING_PROFILE = "";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = app;
			};
			name = Debug;
		};
		FAC284E419B84B5F0010C385 /* Coverage */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				GCC_PREFIX_HEADER = "Demo/DTActionSheetDemo/DTActionSheetDemo-Prefix.pch";
				INFOPLIST_FILE = "Demo/DTActionSheetDemo/DTActionSheetDemo-Info.plist";
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_BUNDLE_IDENTIFIER = "com.drobnik.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "DTActionSheet Demo";
				PROVISIONING_PROFILE = "";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = app;
			};
			name = Coverage;
		};
		FAC284E519B84B5F0010C385 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				GCC_PREFIX_HEADER = "Demo/DTActionSheetDemo/DTActionSheetDemo-Prefix.pch";
				INFOPLIST_FILE = "Demo/DTActionSheetDemo/DTActionSheetDemo-Info.plist";
				OTHER_CFLAGS = "-DNS_BLOCK_ASSERTIONS=1";
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_BUNDLE_IDENTIFIER = "com.drobnik.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "DTActionSheet Demo";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = app;
			};
			name = Release;
		};
		FAF37A3617AFDD93009AC27C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				GCC_PREFIX_HEADER = "Demo/DTZipArchiveDemo/DTZipArchiveDemo-Prefix.pch";
				INFOPLIST_FILE = "Demo/DTZipArchiveDemo/DTZipArchiveDemo-Info.plist";
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_BUNDLE_IDENTIFIER = "com.drobnik.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = DTZipArchive;
				PROVISIONING_PROFILE = "";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = app;
			};
			name = Debug;
		};
		FAF37A3717AFDD93009AC27C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				GCC_PREFIX_HEADER = "Demo/DTZipArchiveDemo/DTZipArchiveDemo-Prefix.pch";
				INFOPLIST_FILE = "Demo/DTZipArchiveDemo/DTZipArchiveDemo-Info.plist";
				OTHER_CFLAGS = "-DNS_BLOCK_ASSERTIONS=1";
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_BUNDLE_IDENTIFIER = "com.drobnik.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = DTZipArchive;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = app;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		384470E71BA3330F0037C68D /* Build configuration list for PBXNativeTarget "Static Library (tvOS)" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				384470E81BA3330F0037C68D /* Debug */,
				384470E91BA3330F0037C68D /* Coverage */,
				384470EA1BA3330F0037C68D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		384471511BA33ED80037C68D /* Build configuration list for PBXNativeTarget "DTFoundation (tvOS)" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				384471521BA33ED80037C68D /* Debug */,
				384471531BA33ED80037C68D /* Coverage */,
				384471541BA33ED80037C68D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		3D7CE544166613B60028D339 /* Build configuration list for PBXNativeTarget "DTHTMLParser (Mac)" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				3D7CE545166613B60028D339 /* Debug */,
				A70ED6BF17F98468008A801A /* Coverage */,
				3D7CE546166613B60028D339 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A70B4CEF1486637E00873A4A /* Build configuration list for PBXNativeTarget "Static Library" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A70B4CF01486637E00873A4A /* Debug */,
				A70ED6B917F98468008A801A /* Coverage */,
				A70B4CF11486637E00873A4A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A70D74E61743AFBC00E6E626 /* Build configuration list for PBXNativeTarget "DTSidePanels Demo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A70D74E71743AFBC00E6E626 /* Debug */,
				A70ED6C817F98468008A801A /* Coverage */,
				A70D74E81743AFBC00E6E626 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A710A5081607556000437D36 /* Build configuration list for PBXNativeTarget "Static Library (Mac)" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A710A5091607556000437D36 /* Debug */,
				A70ED6BB17F98468008A801A /* Coverage */,
				A710A50A1607556000437D36 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A72121E3173BFFB1003C6F0A /* Build configuration list for PBXNativeTarget "DTReachability (iOS)" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A72121E4173BFFB1003C6F0A /* Debug */,
				A70ED6C417F98468008A801A /* Coverage */,
				A72121E5173BFFB1003C6F0A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A72121F2173BFFED003C6F0A /* Build configuration list for PBXNativeTarget "DTReachability (Mac)" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A72121F3173BFFED003C6F0A /* Debug */,
				A70ED6C517F98468008A801A /* Coverage */,
				A72121F4173BFFED003C6F0A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A78220C5168060CA005B602D /* Build configuration list for PBXNativeTarget "DTUTI (iOS)" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A78220C3168060CA005B602D /* Debug */,
				A70ED6C217F98468008A801A /* Coverage */,
				A78220C4168060CA005B602D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A78381E31963F8D700AF09D3 /* Build configuration list for PBXNativeTarget "DTAnimatedGIF Demo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A78381E41963F8D700AF09D3 /* Debug */,
				A78381E51963F8D700AF09D3 /* Coverage */,
				A78381E61963F8D700AF09D3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A7917C86191A312700964D63 /* Build configuration list for PBXNativeTarget "Unit Tests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A7917C87191A312700964D63 /* Debug */,
				A7917C88191A312700964D63 /* Coverage */,
				A7917C89191A312700964D63 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A79D99BD1792D1F60082BC06 /* Build configuration list for PBXNativeTarget "DTAWS (iOS)" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A79D99B91792D1F60082BC06 /* Debug */,
				A70ED6BC17F98468008A801A /* Coverage */,
				A79D99BA1792D1F60082BC06 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A7AC21C0196417830009E1B9 /* Build configuration list for PBXNativeTarget "DTAnimatedGIF (iOS)" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A7AC21C1196417830009E1B9 /* Debug */,
				A7AC21C2196417830009E1B9 /* Coverage */,
				A7AC21C3196417830009E1B9 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A7C92B83174FB8900019D70A /* Build configuration list for PBXNativeTarget "DTSQLite (iOS)" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A7C92B84174FB8900019D70A /* Debug */,
				A70ED6C617F98468008A801A /* Coverage */,
				A7C92B85174FB8900019D70A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A7C92B94174FB9720019D70A /* Build configuration list for PBXNativeTarget "DTSQLite (Mac)" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A7C92B95174FB9720019D70A /* Debug */,
				A70ED6C717F98468008A801A /* Coverage */,
				A7C92B96174FB9720019D70A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A7D8C7BF1723174600025675 /* Build configuration list for PBXNativeTarget "DTZipArchive (Mac)" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A7D8C7C01723174600025675 /* Debug */,
				A70ED6C117F98468008A801A /* Coverage */,
				A7D8C7C11723174600025675 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A7D9D6491758C89900C29B6F /* Build configuration list for PBXNativeTarget "DTReachability Demo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A7D9D64A1758C89900C29B6F /* Debug */,
				A70ED6C917F98468008A801A /* Coverage */,
				A7D9D64B1758C89900C29B6F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A7E383CD160DFEDB00CF72D6 /* Build configuration list for PBXNativeTarget "DTHTMLParser (iOS)" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A7E383CB160DFEDB00CF72D6 /* Debug */,
				A70ED6BE17F98468008A801A /* Coverage */,
				A7E383CC160DFEDB00CF72D6 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A7E383E3160DFF8600CF72D6 /* Build configuration list for PBXNativeTarget "DTZipArchive (iOS)" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A7E383E4160DFF8600CF72D6 /* Debug */,
				A70ED6C017F98468008A801A /* Coverage */,
				A7E383E5160DFF8600CF72D6 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A7E889B116A9B190009EF0DF /* Build configuration list for PBXNativeTarget "DTUTI (Mac)" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A7E889B216A9B190009EF0DF /* Debug */,
				A70ED6C317F98468008A801A /* Coverage */,
				A7E889B316A9B190009EF0DF /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A7EA07521A2B215300B61CCE /* Build configuration list for PBXNativeTarget "DTFoundation (OSX)" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A7EA074C1A2B215300B61CCE /* Debug */,
				A7EA074D1A2B215300B61CCE /* Coverage */,
				A7EA074E1A2B215300B61CCE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A7EA07D11A2B2F6B00B61CCE /* Build configuration list for PBXNativeTarget "DTFoundation (iOS)" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A7EA07D21A2B2F6B00B61CCE /* Debug */,
				A7EA07D31A2B2F6B00B61CCE /* Coverage */,
				A7EA07D41A2B2F6B00B61CCE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A7F4DF9D147FB61500F4059A /* Build configuration list for PBXProject "DTFoundation" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A7F4DFB0147FB61500F4059A /* Debug */,
				A70ED6B617F98468008A801A /* Coverage */,
				A7F4DFB1147FB61500F4059A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A7F4DFEE147FBAC600F4059A /* Build configuration list for PBXAggregateTarget "Documentation" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A7F4DFEF147FBAC600F4059A /* Debug */,
				A70ED6B717F98468008A801A /* Coverage */,
				A7F4DFF0147FBAC600F4059A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F88FF67C1920A83300120808 /* Build configuration list for PBXNativeTarget "DTProgressHUD (iOS)" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F88FF67D1920A83300120808 /* Debug */,
				F88FF67E1920A83300120808 /* Coverage */,
				F88FF67F1920A83300120808 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F88FF6A11920AAB600120808 /* Build configuration list for PBXNativeTarget "DTProgressHUD Demo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F88FF6A21920AAB600120808 /* Debug */,
				F88FF6A31920AAB600120808 /* Coverage */,
				F88FF6A41920AAB600120808 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		FA9CB81717ABDEF200A596C5 /* Build configuration list for PBXNativeTarget "DTASN1 (iOS)" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				FA9CB81517ABDEF200A596C5 /* Debug */,
				A70ED6BD17F98468008A801A /* Coverage */,
				FA9CB81617ABDEF200A596C5 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		FAC284E219B84B5F0010C385 /* Build configuration list for PBXNativeTarget "DTActionSheet Demo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				FAC284E319B84B5F0010C385 /* Debug */,
				FAC284E419B84B5F0010C385 /* Coverage */,
				FAC284E519B84B5F0010C385 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		FAF37A3817AFDD93009AC27C /* Build configuration list for PBXNativeTarget "DTZipArchive Demo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				FAF37A3617AFDD93009AC27C /* Debug */,
				A70ED6CA17F98468008A801A /* Coverage */,
				FAF37A3717AFDD93009AC27C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCSwiftPackageProductDependency section */
		A7E77946253D9E5600E011D3 /* DTFoundation */ = {
			isa = XCSwiftPackageProductDependency;
			productName = DTFoundation;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = A7F4DF9A147FB61500F4059A /* Project object */;
}
