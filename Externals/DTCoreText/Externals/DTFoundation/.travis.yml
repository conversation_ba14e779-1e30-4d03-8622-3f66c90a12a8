---
language: objective-c
osx_image: xcode8.3

script: 
 - xcodebuild -project DTFoundation.xcodeproj -scheme "DTSidePanels Demo" build -sdk iphonesimulator -arch i386 ONLY_ACTIVE_ARCH=NO | xcpretty
 - xcodebuild -project DTFoundation.xcodeproj -scheme "DTReachability Demo" build -sdk iphonesimulator -arch i386 ONLY_ACTIVE_ARCH=NO | xcpretty
 - xcodebuild -project DTFoundation.xcodeproj -scheme "DTZipArchive Demo" build -sdk iphonesimulator -arch i386 ONLY_ACTIVE_ARCH=NO | xcpretty
 - xcodebuild -project DTFoundation.xcodeproj -scheme "DTProgressHUD Demo" build -sdk iphonesimulator -arch i386 ONLY_ACTIVE_ARCH=NO | xcpretty
 - xcodebuild -project DTFoundation.xcodeproj -scheme "DTFoundation (iOS)" test -sdk iphonesimulator -destination 'platform=iOS Simulator,name=iPhone 6 Plus' | xcpretty
 - xcodebuild -project DTFoundation.xcodeproj -scheme "DTFoundation (OSX)" clean build  | xcpretty
