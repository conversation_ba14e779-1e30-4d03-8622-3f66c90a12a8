//
//  UIScreen+DTFoundation.m
//  DTFoundation
//
//  Created by <PERSON> on 16.10.17.
//  Copyright © 2017 Cocoanetics. All rights reserved.
//

#import "UIScreen+DTFoundation.h"

#if TARGET_OS_IPHONE && !TARGET_OS_TV && !TARGET_OS_WATCH

#import <UIKit/UIKit.h>

@implementation UIScreen (DTFoundation)

- (UIInterfaceOrientation)orientation {
    CGPoint point = [self.coordinateSpace convertPoint:CGPointZero toCoordinateSpace:self.fixedCoordinateSpace];
    if (point.x == 0 && point.y == 0) {
        return UIInterfaceOrientationPortrait;
    } else if (point.x != 0 && point.y != 0) {
        return UIInterfaceOrientationPortraitUpsideDown;
    } else if (point.x == 0 && point.y != 0) {
        return UIInterfaceOrientationLandscapeLeft;
    } else if (point.x != 0 && point.y == 0) {
        return UIInterfaceOrientationLandscapeRight;
    } else {
        return UIInterfaceOrientationUnknown;
    }
}

@end

#endif
