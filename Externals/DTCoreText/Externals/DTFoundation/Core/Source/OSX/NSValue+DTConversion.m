//
//  NSValue+DTConversion.m
//  DTFoundation
//
//  Created by <PERSON> on 27.11.12.
//  Copyright (c) 2012 Cocoanetics. All rights reserved.
//

#import "NSValue+DTConversion.h"

#if TARGET_OS_OSX

@implementation NSValue (DTConversion)

+ (NSValue *)valueWithCGAffineTransform:(CGAffineTransform)transform
{
    return [NSValue valueWithBytes:&transform objCType:@encode(CGAffineTransform)];
}

+ (NSValue *)valueWithCGPoint:(CGPoint)point
{
    return [NSValue valueWithBytes:&point objCType:@encode(CGPoint)];
}

- (CGAffineTransform)CGAffineTransformValue
{
    CGAffineTransform transform;
    
    [self getValue:&transform];
    
    return transform;
}

- (CGPoint)CGPointValue
{
    CGPoint point;
    
    [self getValue:&point];
    
    return point;
}

@end

#endif
