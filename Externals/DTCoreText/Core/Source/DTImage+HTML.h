//
//  DTImage+HTML.h
//  DTCoreText
//
//  Created by <PERSON> on 1/9/11.
//  Copyright 2011 Drobnik.com. All rights reserved.
//

#import "DTCompatibility.h"

#if TARGET_OS_IPHONE

#import <UIKit/UIImage.h>

/**
 Category used to have the same method available for unit testing on Mac on iOS.
 */
@interface UIImage (HTML)

/** 
 Retrieve the NSData representation of a UIImage. Used to encode UIImages in DTTextAttachments.
 
 @returns The NSData representation of the UIImage instance receiving this message. Convenience method for UIImagePNGRepresentation(). 
 */
- (NSData *)dataForPNGRepresentation;

@end

#endif

#if TARGET_OS_OSX
#import <AppKit/NSImage.h>

/**
 Category used to have the same method available for unit testing on Mac on iOS.
 */
@interface NSImage (HTML)


/** 
 Retrieve the NSData representation of a NSImage.
 
 @returns The NSData representation of the NSImage instance receiving this message. 
 */
- (NSData *)dataForPNGRepresentation;

@end

#endif
