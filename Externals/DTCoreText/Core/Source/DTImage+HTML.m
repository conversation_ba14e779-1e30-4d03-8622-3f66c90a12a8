//
//  DTImage+HTML.m
//  DTCoreText
//
//  Created by <PERSON> on 31.01.12.
//  Copyright (c) 2012 Drobnik.com. All rights reserved.
//

#import "DTImage+HTML.h"

#if TARGET_OS_IPHONE

@implementation UIImage (HTML)

- (NSData *)dataForPNGRepresentation
{
	return UIImagePNGRepresentation(self);
}

@end

#endif

#if TARGET_OS_OSX

@implementation NSImage (HTML)

- (NSData *)dataForPNGRepresentation
{
	[self lockFocus];
	NSBitmapImageRep *bitmapRep = [[NSBitmapImageRep alloc] initWithFocusedViewRect:NSMakeRect(0, 0, self.size.width, self.size.height)];
	[self unlockFocus];
	
	return [bitmapRep representationUsingType:NSPNGFileType properties:[NSDictionary new]];
}

@end

#endif
