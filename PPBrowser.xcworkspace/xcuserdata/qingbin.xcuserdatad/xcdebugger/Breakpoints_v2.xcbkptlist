<?xml version="1.0" encoding="UTF-8"?>
<Bucket
   uuid = "3CAFFAFB-ED60-4B3F-81B7-0F3066CA4204"
   type = "0"
   version = "2.0">
   <Breakpoints>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.ExceptionBreakpoint">
         <BreakpointContent
            uuid = "841CD9A2-A9DB-4D2F-9E7F-4A660E447C4A"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            breakpointStackSelectionBehavior = "1"
            scope = "1"
            stopOnStyle = "0">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "8E521B88-0364-41C1-BBA8-20E0743B4BB6"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "PPBrowser/Sections/TabTray/Controller/TabTrayViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "268"
            endingLineNumber = "268"
            landmarkName = "-setupObservers"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "765BC38F-8A45-46B4-9AA7-3B74BA3F553E"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "PPBrowser/Sections/Browser/Category/BrowserViewController+WKNavigationDelegate.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "384"
            endingLineNumber = "384"
            landmarkName = "-webView:decidePolicyForNavigationResponse:decisionHandler:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "BC967A61-AB1A-41EE-AADB-6BE131435ED6"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "PPBrowser/Sections/AutoPage/Helper/AutoPageHelper.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "64"
            endingLineNumber = "64"
            landmarkName = "-userContentController:didReceiveScriptMessage:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "69720132-E620-4704-87F7-67508A96590F"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "PPBrowser/Sections/AutoPage/Helper/AutoPageHelper.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "77"
            endingLineNumber = "77"
            landmarkName = "-userContentController:didReceiveScriptMessage:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "461171D7-53FC-4C24-8E6A-1D3421CDC442"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "PPBrowser/SystemScript/Helper/TagitHelper.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "163"
            endingLineNumber = "163"
            landmarkName = "-userContentController:didReceiveScriptMessage:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "9559C1DB-F7A6-4487-A383-06789F0CD7A4"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "PPBrowser/Sections/NewTabPage/Next/FavorEdit/Controller/FavorEditController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "216"
            endingLineNumber = "216"
            landmarkName = "-finishAction"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "ADA341C1-F7BA-4036-B08F-6C4D2B000EE8"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "PPBrowser/Sections/Reader/Next/ReaderTheme/Adapter/ReadingThemeCell.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "100"
            endingLineNumber = "100"
            landmarkName = "-updateCornerRadius"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "B603250D-5EC4-4558-A144-BB946106B425"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "PPBrowser/Sections/Reader/Resource/Reader.js"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "565"
            endingLineNumber = "565"
            landmarkName = "_appendElements"
            landmarkType = "9">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "630A1F6B-774B-41BF-B0B8-5793E5A0E123"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "PPBrowser/Sections/Reader/Helper/ReaderHelper.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "74"
            endingLineNumber = "74"
            landmarkName = "-enterReader:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "D943621D-94E7-4A23-A425-28A1A29EBCDF"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "PPBrowser/Sections/Reader/Resource/Reader.js"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "592"
            endingLineNumber = "592"
            landmarkName = "_appendElements"
            landmarkType = "9">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "86E88299-B331-453A-8067-7EBE05F2660A"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "PPBrowser/Sections/Reader/Resource/Reader.js"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "4069"
            endingLineNumber = "4069"
            landmarkName = "applyReaderContentFilters"
            landmarkType = "9">
         </BreakpointContent>
      </BreakpointProxy>
   </Breakpoints>
</Bucket>
