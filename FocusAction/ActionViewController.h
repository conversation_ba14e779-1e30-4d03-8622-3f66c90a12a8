//
//  ActionViewController.h
//  FocusAction
//
//  Created by qingbin on 2023/4/28.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import <UIKit/UIKit.h>

/// 转换为灰度图片 https://blackandwhite.imageonline.co/cn/
/// 设置图标：在Action Extension的target里面的Build Settings，里面的Asset Catalog Compiler -- Primary Icon Set Name即可
/// iOS 18无法跳转，参考以下链接：
/// https://stackoverflow.com/questions/79077018/unable-to-open-main-app-from-action-extension-in-ios-18-previously-working-met
@interface ActionViewController : UIViewController

@end
